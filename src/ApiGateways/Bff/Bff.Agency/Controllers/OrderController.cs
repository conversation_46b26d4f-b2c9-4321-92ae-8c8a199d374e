using AutoMapper;
using Bff.Agency.Callers;
using Bff.Agency.Models.Order;
using Bff.Agency.Services.Interfaces;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Agency.Controllers;

/// <summary>
/// 订单
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class OrderController : ControllerBase
{
    private readonly IPaymentApiCaller _paymentApiCaller;
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IMapper _mapper;
    private readonly IOrderPaymentService _paymentService;
    private readonly ITenantApiCaller _tenantApiCaller;

    public OrderController(IPaymentApiCaller paymentApiCaller,
        IOrderApiCaller orderApiCaller,
        IMapper mapper,
        IOrderPaymentService paymentService,
        ITenantApiCaller tenantApiCaller)
    {
        _paymentApiCaller = paymentApiCaller;
        _orderApiCaller = orderApiCaller;
        _mapper = mapper;
        _paymentService = paymentService;
        _tenantApiCaller = tenantApiCaller;
    }

    /// <summary>
    /// 支付
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(OrderPayOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.OrderNotFind,
        ErrorTypes.Payment.OrderHasPaid,
        ErrorTypes.Payment.YeeMerNotConfig,
        ErrorTypes.Payment.CurrencyNonsupport)]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    public async Task<IActionResult> Pay([FromBody] OrderPaymentPayInput input)
    {
        var result = await _paymentApiCaller.Pay(input);
        return Ok(result);
    }

    /// <summary>
    /// 分销商额度支付
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(OrderPayOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.OrderNotFind,
    ErrorTypes.Payment.OrderHasPaid,
    ErrorTypes.Payment.YeeMerNotConfig,
    ErrorTypes.Payment.CurrencyNonsupport)]
    [Authorize]
    public async Task<IActionResult> CreditPay([FromBody] AgencyCreditOrderPayInput input)
    {
        var result = await _paymentApiCaller.AgencyCreditPay(input);
        return Ok(result);
    }

    /// <summary>
    /// 分销商预付款 支付
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(OrderPayOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.OrderNotFind,
        ErrorTypes.Payment.OrderHasPaid,
        ErrorTypes.Payment.ReceiptPrepaymentInsufficientBalance)]
    [Authorize]
    public async Task<IActionResult> ReceiptPrepaymentPay(ReceiptPrepaymentPayInput input)
    {
        var result = await _paymentApiCaller.ReceiptPrepaymentPay(input);
        return Ok(result);
    }

    /// <summary>
    /// Onerway 信用卡支付
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(OrderPayOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.OrderNotFind,
    ErrorTypes.Payment.OrderHasPaid)]
    [Authorize]
    public async Task<IActionResult> OnerwayTxnPay(OnerwayTxnPayInput input)
    {
        var txnOrderMsg = input.TxnPaymentInfo?.TxnOrderMsg;
        if (txnOrderMsg is not null)
        {
            if (string.IsNullOrWhiteSpace(txnOrderMsg.ContentLength))
            {
                txnOrderMsg.ContentLength = Request.ContentLength?.ToString() ?? "1024";
            }
        }
        var result = await _paymentApiCaller.OnerwayTxnPay(input);
        return Ok(result);
    }

    /// <summary>
    /// 汇智微信支付
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(OrderPayOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.OrderNotFind,
        ErrorTypes.Payment.OrderHasPaid,
        ErrorTypes.Payment.YeeMerNotConfig,
        ErrorTypes.Payment.CurrencyNonsupport)]
    [Authorize]
    public async Task<IActionResult> HuizhiWechatPay(HuizhiWechatPayInput input)
    {
        var result = await _paymentApiCaller.HuizhiWechatPay(input);
        return Ok(result);
    }

    /// <summary>
    /// 获取能开票的列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<GetListByCanBeInvoiceBffOutput>), (int)HttpStatusCode.OK)]
    [Authorize]
    public async Task<IActionResult> GetListByCanBeInvoice(GetListByCanBeInvoiceBffInput bffInput)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var input = _mapper.Map<GetListByCanBeInvoiceInput>(bffInput);
        input.AgencyId = currentUser.Provider;
        input.SourceChannel = Contracts.Common.Order.Enums.InvoiceSourceChannel.B2B;
        input.IsToBeInvoiced = bffInput.InvoiceStatus.Contains(InvoiceStatus.ToBeInvoiced);
        var invoiceStatusList = input.InvoiceStatus.ToList();
        var toBeInvoiced = invoiceStatusList.FirstOrDefault(x => x.Equals(InvoiceStatus.ToBeInvoiced));
        invoiceStatusList.Remove(toBeInvoiced);
        input.InvoiceStatus = invoiceStatusList.ToArray();
        var canBeInvoiceDtos = await _orderApiCaller.GetListByCanBeInvoice(input);
        var result = _mapper.Map<PagingModel<GetListByCanBeInvoiceBffOutput>>(canBeInvoiceDtos);

        await FinancialAmount(result.Data);

        return Ok(result);
    }

    /// <summary>
    /// 获取酒店订单能开票的子订单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<GetListByCanBeInvoiceBffOutput>), (int)HttpStatusCode.OK)]
    [Authorize]
    public async Task<IActionResult> CanIssueInvoiceList(BffCanIssueInvoiceListInput bffInput)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();

        var input = _mapper.Map<GetListByCanBeInvoiceInput>(bffInput);
        input.AgencyId = currentUser.Provider;
        input.SourceChannel = Contracts.Common.Order.Enums.InvoiceSourceChannel.B2B;
        input.OrderType = Contracts.Common.Order.Enums.OrderType.Hotel;
        var canBeInvoiceDtos = await _orderApiCaller.CanIssueInvoiceList(input);
        var result = _mapper.Map<List<GetListByCanBeInvoiceBffOutput>>(canBeInvoiceDtos);

        await FinancialAmount(result);

        return Ok(result);
    }

    /// <summary>
    /// 计算发票可开票订单的抵充后是否可开票与开票金额
    /// </summary>
    /// <param name="result"></param>
    /// <returns></returns>
    private async Task FinancialAmount(IEnumerable<GetListByCanBeInvoiceBffOutput> result)
    {
        // 计算订单金额
        foreach (var item in result)
        {
            var financialData = await _paymentService.GetFinancialData(new Models.OrderPayment.GetOrderFinancialDataInput
            {
                OrderId = item.BaseOrderId!.Value,
                OrderPaymentType = OrderPaymentType.OrderPay
            });
            if (financialData?.TotalSalesInfo is not null)
            {
                item.OrderAmount = financialData.TotalSalesInfo.Amount;
                if (item.OrderAmount <= 0)
                    item.IsCanInvoice = false;
            }
        }
    }

    /// <summary>
    /// 信用卡担保支付
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(OrderPayOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.OrderNotFind, ErrorTypes.Common.NotSupportedOperation,
        ErrorTypes.Payment.OrderHasPaid,
        ErrorTypes.Payment.YeeMerNotConfig,
        ErrorTypes.Payment.CurrencyNonsupport)]
    [Authorize]
    public async Task<IActionResult> CreditCardGuaranteePay(CreditCardGuaranteeInput input)
    {
        var carResult = await _orderApiCaller.AddOrderPaymentCard(input);
        var result = await _paymentApiCaller.CreditCardGuaranteePay(new CreditCardOrderPayInput()
        {
            OrderId = input.BaseOrderId,
            OrderPaymentType = OrderPaymentType.OrderPay
        });
        return Ok(result);
    }

    /// <summary>
    /// 易宝海外银行账户收款支付
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(YeepayCollectionPayOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation, ErrorTypes.Order.OrderNotFind, ErrorTypes.Payment.OrderHasPaid)]
    public async Task<IActionResult> YeepayCollectionPay(YeepayCollectionPayInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var config = await _tenantApiCaller.GetAgencyPaymentConfigByAgencyId(currentUser.Provider);
        // 检查是否启用海外银行账户支付
        if (config?.OverseasBankCardPaymentEnabled is not true)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var result = await _paymentApiCaller.YeepayCollectionPay(input);
        return Ok(result);
    }
}
