using AutoMapper;
using Bff.Agency.Callers;
using Bff.Agency.Models.Agency;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Product.DTOs.ProductSku;
using Contracts.Common.Tenant.DTOs.Agency;
using Contracts.Common.Tenant.DTOs.Agency.AgencyRegister;
using Contracts.Common.Tenant.DTOs.SignSubject;
using Contracts.Common.Tenant.Enums;
using Contracts.Common.User.DTOs;
using Contracts.Common.User.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Agency.Controllers;

/// <summary>
/// 分销商
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class AgencyController : ControllerBase
{
    private readonly ITenantApiCaller _tenantCaller;
    private readonly IPaymentApiCaller _paymentApiCaller;
    private readonly IUserApiCaller _userApiCaller;
    //private readonly ITenantIdentify _tenantIdentify;
    private readonly IMapper _mapper;

    public AgencyController(ITenantApiCaller tenantCaller,
        IPaymentApiCaller paymentApiCaller,
        IUserApiCaller userApiCaller,
        // ITenantIdentify tenantIdentify,
        IMapper mapper)
    {
        _tenantCaller = tenantCaller;
        _paymentApiCaller = paymentApiCaller;
        _mapper = mapper;
        _userApiCaller = userApiCaller;
        //_tenantIdentify = tenantIdentify;
    }

    /// <summary>
    /// 提交认证
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(bool), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Tenant.AgencyNotAudited)]
    public async Task<IActionResult> SaveCertified(SaveCertifiedInput input)
    {
        var agencyInput = _mapper.Map<UpdateCertifiedInput>(input);
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        agencyInput.TenantId = currentUser.Tenant;
        agencyInput.Id = currentUser.Provider;
        agencyInput.CreateName = currentUser.NickName;
        agencyInput.CreateUserId = currentUser.UserId;
        var res = await _tenantCaller.SaveCertified(agencyInput);
        return Ok(res);
    }

    /// <summary>
    /// 获取认证详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(GetCertifiedDetailOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetCertifiedDetail()
    {

        var agencyId = HttpContext.User.ParseUserInfo<CurrentUser>().Provider;
        var res = await _tenantCaller.GetCertifiedDetail(agencyId);
        return Ok(res);
    }

    /// <summary>
    /// 获取分销商签约主体信息 null为无签约主体
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(GetSignSubjectOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetSignSubject()
    {
        CurrentUser currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var agency = await _tenantCaller.GetAgencyFirstOrDefault(currentUser.Provider);
        GetSignSubjectOutput? output = agency?.SignSubjectId is > 0 ?
            (await _tenantCaller.GetSignSubject(new GetSignSubjectInput
            {
                Id = agency.SignSubjectId!.Value
            }))
            : null;
        return Ok(output);
    }

    /// <summary>
    /// 获取分销商额度信息 授信额度 预收款 延时支付额度
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(GetCreditDetailOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetCreditDetail()
    {
        CurrentUser currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var agencyId = currentUser.Provider;
        var agencyCreditTask = _tenantCaller.GetAgencyCredit(agencyId);
        var receiptPrepaymentsTask = _paymentApiCaller.GetReceiptPrepayments(agencyId);
        var agencyDelayedCreditTask = _tenantCaller.GetAgencyDelayedCredit(agencyId);

        var agencyCredit = await agencyCreditTask;
        var receiptPrepayment = (await receiptPrepaymentsTask).FirstOrDefault();
        var agencyDelayedCredit = await agencyDelayedCreditTask;
        GetCreditDetailOutput result = new()
        {
            AgencyCredit = agencyCredit,
            ReceiptPrepayment = receiptPrepayment,
            AgencyDelayedCredit = agencyDelayedCredit,
        };
        return Ok(result);
    }


    /// <summary>
    /// 分销商信息唯一性校验
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(bool), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> CheckAgency(AgencyCheckOnlyInput input)
    {
        var result = new BffCheckAgencyDto();

        if (string.IsNullOrEmpty(input.Email) && string.IsNullOrEmpty(input.ContactNumber) && string.IsNullOrEmpty(input.FullName))
        {
            return Ok(result);
        }

        if (string.IsNullOrEmpty(input.FullName) is false)
        {
            var agencys = await _tenantCaller.GetAgencyInfos(new GetSimpleInfoInput
            {
                FullName = input.FullName,
            });
            result.FullName = agencys is not null && agencys.Any(x => x.FullName == input.FullName);
        }

        var emailExist = string.IsNullOrEmpty(input.Email) is false;

        var phoneExist = string.IsNullOrEmpty(input.ContactNumber) is false;

        if (phoneExist || emailExist)
        {
            var agencyUsers = await _userApiCaller.GetAgencyUserInfo(new Contracts.Common.User.DTOs.AgencyUser.GetDetailsInput
            {
                Email = input.Email,
                PhoneNumber = input.ContactNumber,
            });
            result.Email = emailExist && agencyUsers is not null && agencyUsers.Any(x => x.Email == input.Email);
            result.ContactNumber = phoneExist && agencyUsers is not null && agencyUsers.Any(x => x.PhoneNumber == input.ContactNumber);
        }

        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(bool), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.User.VerifyCodeError)]
    public async Task VaildContact(AgencyRegisterVaildContactInput input)
    {
        var checkCodeValue = !string.IsNullOrEmpty(input.CodeValue) && !string.IsNullOrEmpty(input.CodeId);

        //核验验证码  发送手机短信必须验证，邮箱可选验证
        if (input.ContactType is TenantUserVaildContactType.Phone || checkCodeValue)
        {
            var isCorrect = await _userApiCaller.CheckImageCaptcha(new Contracts.Common.User.DTOs.ImageCaptchaDto
            {
                CodeId = input.CodeId,
                CodeValue = input.CodeValue,
            });
            if (isCorrect is false)
                throw new BusinessException(ErrorTypes.User.VerifyCodeError);
        }
        var fullNameExist = string.IsNullOrEmpty(input.FullName) is false;
        if (fullNameExist)
        {
            var agencys = await _tenantCaller.GetAgencyInfos(new GetSimpleInfoInput
            {
                FullName = input.FullName,
            });
            if (agencys?.Any(x => x.FullName == input.FullName) is true)
                throw new BusinessException(ErrorTypes.Tenant.AgencyFullNameExist);
        }


        var captchaInput = new CaptchaDTO
        {
            Key = input.ContactTypeValue,
            TenantId = HttpContext.GetTenantId()
        };
        switch (input.ContactType)
        {
            case TenantUserVaildContactType.Phone:
                captchaInput.CaptchaChannelType = CaptchaChannelType.Sms;
                captchaInput.CaptchaType = CaptchaType.AgencyUser_SMS_Register;
                break;
            case TenantUserVaildContactType.Email:
                captchaInput.CaptchaChannelType = CaptchaChannelType.Email;
                captchaInput.CaptchaType = CaptchaType.AgencyUser_Email_EmailBingding;
                captchaInput.Variables.Add(new(0, fullNameExist ? input.FullName : string.Empty));
                break;
        }
        await _userApiCaller.SendCaptcha(captchaInput);
    }

}
