using AutoMapper;
using Bff.Agency.Callers;
using Bff.Agency.Consts;
using Bff.Agency.Models.AgencyChannelPriceSettings;
using Bff.Agency.Models.B2BIndexPage;
using Bff.Agency.Models.FunProduct;
using Bff.Agency.Models.Hotel;
using Bff.Agency.Services.Interfaces;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Hotel.DTOs.ApiHotel;
using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Hotel.DTOs.HotelTag;
using Contracts.Common.Hotel.DTOs.PriceStrategy;
using Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Marketing.DTOs;
using Contracts.Common.Marketing.DTOs.UserCoupon;
using Contracts.Common.Marketing.Enums;
using Contracts.Common.Order.DTOs.HotelOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.DTOs.Currency;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Permission.Const;
using Contracts.Common.Product.DTOs.AgencyChannelCommissionSettings;
using Contracts.Common.Product.DTOs.AgencyChannelPriceSettings;
using Contracts.Common.Product.Enums;
using Contracts.Common.Resource.DTOs;
using Contracts.Common.Resource.DTOs.GDSHotel;
using Contracts.Common.Resource.DTOs.HotelExtend;
using Contracts.Common.Resource.DTOs.ThirdHotel;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Tenant.DTOs.Agency;
using Contracts.Common.Tenant.DTOs.TenantApiSupplierConfig;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Enum = System.Enum;

namespace Bff.Agency.Controllers;

/// <summary>
/// 酒店数据
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class HotelController : ControllerBase
{
    private readonly IMapper _mapper;
    private readonly IHotelApiCaller _hotelApiCaller;
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IMarketingApiCaller _marketingApiCaller;
    private readonly IHotelService _hotelService;
    private readonly IPaymentService _paymentService;
    private readonly IAgencyChannelPriceService _agencyChannelPriceService;
    private readonly IPermissionCaller _permissionCaller;
    private readonly IPaymentApiCaller _paymentApiCaller;
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IProductApiCaller _productApiCaller;
    private readonly IGDSHotelService _gdsHotelService;
    private readonly ILogger<HotelController> _logger;

    private const string _agencyUserAclKey_Main = AgencyUserAclKey.HotelLabelMain;
    private const string _agencyUserAclKey_Direct = AgencyUserAclKey.HotelLabelDirect;

    public HotelController(
        IMapper mapper,
        IHotelApiCaller hotelApiCaller,
        IResourceApiCaller resourceApiCaller,
        ITenantApiCaller tenantApiCaller,
        IMarketingApiCaller marketingApiCaller,
        IHotelService hotelService,
        IPaymentService paymentService,
        IAgencyChannelPriceService agencyChannelPriceService,
        IPermissionCaller permissionCaller,
        ILogger<HotelController> logger,
        IPaymentApiCaller paymentApiCaller,
        IOrderApiCaller orderApiCaller,
        IProductApiCaller productApiCaller,
        IGDSHotelService gdsHotelService)
    {
        _mapper = mapper;
        _hotelApiCaller = hotelApiCaller;
        _resourceApiCaller = resourceApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _marketingApiCaller = marketingApiCaller;
        _hotelService = hotelService;
        _paymentService = paymentService;
        _agencyChannelPriceService = agencyChannelPriceService;
        _permissionCaller = permissionCaller;
        _logger = logger;
        _paymentApiCaller = paymentApiCaller;
        _orderApiCaller = orderApiCaller;
        _productApiCaller = productApiCaller;
        _gdsHotelService = gdsHotelService;
    }

    /// <summary>
    /// 酒店详情
    /// </summary>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200, typeof(GetHotelDetailsBffOutput))]
    public async Task<IActionResult> Detail(long hotelId)
    {
        var result = await DetailData(hotelId, false);
        return Ok(result);
    }

    /// <summary>
    /// 酒店详情 
    /// </summary>
    [HttpGet]
    [SwaggerResponseExt(200, typeof(GetHotelDetailsBffOutput))]
    public async Task<IActionResult> DetailByCustomer(long hotelId)
    {
        var result = await DetailData(hotelId, true);
        return Ok(result);
    }

    /// <summary>
    /// 获取酒店(包括房型)图片
    /// </summary>
    [HttpGet]
    [AllowAnonymous]
    [SwaggerResponseExt(200, typeof(GetHotelPhotosBffOutput))]
    public async Task<IActionResult> GetPhotos(long hotelId)
    {
        var result = new GetHotelPhotosBffOutput();
        var esHotelInfo = await _hotelApiCaller.GetEsHotelDetailV2(hotelId);
        if (esHotelInfo.Any() is false)
        {
            return Ok(result);
        }

        var esHotel = esHotelInfo.First();
        //优先展示本地酒店
        var localHotel = esHotel.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.None);
        if (localHotel != null)
        {
            //查询本地酒店图片
            var localHotelPhotos = await _hotelApiCaller.GetHotelPhotos(localHotel.HotelId);
            result.Id = hotelId;
            result.Name = localHotelPhotos.Name;
            foreach (var roomResult in localHotelPhotos.Rooms.Select(item => new HotelPhotoRoomBffInfo()
            {
                Id = item.ResourceRoomId,
                Name = item.Name,
                Photos = item.Photos
                             .Select(x => new HotelPhotoBffInfo
                             {
                                 Id = x.Id,
                                 Url = x.Url
                             }).ToList()
            }))
            {
                result.Rooms.Add(roomResult);
            }
        }
        else
        {
            //查询资源库酒店图片
            var resourceHotelId = esHotel.ResourceHotelId;
            var resourceHotelPhotos = await _resourceApiCaller.GetHotelPhotos(resourceHotelId);
            result.Id = hotelId;
            result.Name = resourceHotelPhotos.Name;
            foreach (var roomResult in resourceHotelPhotos.Rooms.Select(item => new HotelPhotoRoomBffInfo()
            {
                Id = item.Id,
                Name = item.Name,
                Photos = item.Photos
                             .Select(x => new HotelPhotoBffInfo
                             {
                                 Id = x.Id,
                                 Url = x.Url
                             }).ToList()
            }))
            {
                result.Rooms.Add(roomResult);
            }
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取酒店房型下拉列表
    /// </summary>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200, typeof(List<GetHotelRoomSelectorBffOutput>))]
    public async Task<IActionResult> GetRoomSelector(long hotelId)
    {
        var result = new List<GetHotelRoomSelectorBffOutput>();
        var esHotelInfo = await _hotelApiCaller.GetEsHotelDetailV2(hotelId);
        if (esHotelInfo.Any() is false)
        {
            return Ok(result);
        }

        var esHotel = esHotelInfo.First();
        //本地酒店房型
        var localHotel = esHotel.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.None);
        if (localHotel != null)
        {
            var localRooms = await _hotelApiCaller.GetHotelRooms(new GetHotelRoomsInput { HotelId = localHotel.HotelId });
            result = localRooms.HotelRooms
                .Select(x => new GetHotelRoomSelectorBffOutput
                {
                    RoomId = x.ResourceRoomId,
                    RoomName = x.ZHName,
                    EnRoomName = x.ENName,
                })
                .ToList();
        }

        //第三方酒店房型
        var thirdHotel = esHotel.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.Hop);
        if (thirdHotel != null)
        {
            var thirdRooms = await _resourceApiCaller.GetRoomsByHotelIds(new GetRoomsByHotelIdsInput
            {
                HotelIds = new List<long> { esHotel.ResourceHotelId }
            });
            var thirdRoom = thirdRooms.FirstOrDefault();
            if (thirdRoom != null)
            {
                var thirdSelector = thirdRoom.Rooms
                    .Select(x => new GetHotelRoomSelectorBffOutput
                    {
                        RoomId = x.Id,
                        RoomName = x.ZHName,
                        EnRoomName = x.ENName,
                    })
                    .ToList();
                result.AddRange(thirdSelector);
            }
        }

        result = result.GroupBy(g => new
        {
            g.RoomId,
            g.RoomName,
            g.EnRoomName,
        })
            .Select(x => new GetHotelRoomSelectorBffOutput
            {
                RoomId = x.Key.RoomId,
                RoomName = x.Key.RoomName,
                EnRoomName = x.Key.EnRoomName,
            })
            .ToList();

        return Ok(result);
    }

    /// <summary>
    /// 获取酒店床型下拉列表
    /// </summary>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200, typeof(List<string>))]
    public async Task<IActionResult> GetBedTypeSelector(long hotelId)
    {
        var result = new List<string>();
        var esHotelInfo = await _hotelApiCaller.GetEsHotelDetailV2(hotelId);
        if (esHotelInfo.Any() is false)
        {
            return Ok(result);
        }

        var esHotel = esHotelInfo.First();
        //本地酒店房型
        var localHotel = esHotel.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.None);
        if (localHotel != null)
        {
            var localRooms = await _hotelApiCaller.GetHotelRooms(new GetHotelRoomsInput { HotelId = localHotel.HotelId });
            var localBedTypes = localRooms.HotelRooms
                .Where(x => x.BedType != null)
                .SelectMany(x => x.BedType)
                .Select(x => x.main)
                .ToList();
            result.AddRange(localBedTypes);
        }

        //第三方酒店房型
        var thirdHotel = esHotel.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.Hop);
        if (thirdHotel != null)
        {
            var thirdRooms = await _resourceApiCaller.GetRoomsByHotelIds(new GetRoomsByHotelIdsInput
            {
                HotelIds = new List<long> { esHotel.ResourceHotelId }
            });
            var thirdRoom = thirdRooms.FirstOrDefault();
            foreach (var item in thirdRoom.Rooms)
            {
                if (string.IsNullOrEmpty(item.BedType))
                {
                    continue;
                }

                var convertBedTypeList = JsonConvert.DeserializeObject<List<BedType>>(item.BedType);
                if (convertBedTypeList == null)
                {
                    continue;
                }

                var thirdBedTypes = convertBedTypeList.Select(x => x.main).ToList();
                result.AddRange(thirdBedTypes);
            }
        }

        result = result.Distinct().ToList();

        return Ok(result);
    }

    /// <summary>
    /// 分页数据查询
    /// </summary>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(PagingModel<SearchHotelBffOutput>))]
    public async Task<IActionResult> Search(SearchHotelBffInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var result = new PagingModel<SearchHotelBffOutput>
        {
            PageIndex = input.PageIndex,
            PageSize = input.PageSize
        };

        //校验分销商信息
        var agencyInfo = await _tenantApiCaller.GetAgencyDetail(currentUser.Provider);
        if (agencyInfo.PriceGroupId is null or <= 0) return Ok(result);
        var priceGroupId = agencyInfo.PriceGroupId.Value;

        //校验分组启用状态
        var priceGroup = await _tenantApiCaller.GetPriceGroupDetail(priceGroupId);
        if (!priceGroup.Enable)
            return Ok(result);
        //校验价格配置
        var checkPriceSetting = await _hotelService.CheckAgencyPriceSetting(priceGroupId);
        var localHotelIds = checkPriceSetting.localHotelIds.Select(x => x.hotelId).Distinct().ToList();
        var queryAgencyThirdHotel = checkPriceSetting.queryAgencyThirdHotel;
        if (localHotelIds.Any() is false && queryAgencyThirdHotel is false)
            return Ok(result);

        //查询优惠券信息
        var thirdHotelIds = new List<long>();
        if (input.CouponId.HasValue)
        {
            var couponList = await _marketingApiCaller.GetCouponInfos(new List<long>
            {
                input.CouponId.Value
            });
            var couponInfo = couponList.FirstOrDefault();
            if (couponInfo != null)
            {
                //优惠券使用范围过滤
                /*
                 * 1.优惠券只关联本地酒店.那么只需要判断本地酒店是否在使用范围内.第三方酒店是无数据的
                 * 2.优惠券只关联第三方酒店.那么只需要判断第三方酒店是否在使用范围内.本地酒店是无数据的
                 * 3.优惠券关联本地酒店和第三方酒店.那么需要判断本地酒店是否在价格分组的配置中.第三方酒店会根据配置的售卖开关进行过滤判断
                 */
                var couponHotelIds = new List<long>();
                var couponApiHotelIds = new List<long>();
                foreach (var rangeItem in couponInfo.UserCouponRanges
                             .Where(x => x.ProductType is LimitProductType.Hotel or LimitProductType.ApiHotel))
                {
                    if (rangeItem.RangeType == CouponRangeType.All) continue;
                    switch (rangeItem.ProductType)
                    {
                        case LimitProductType.Hotel:
                            {
                                couponHotelIds = rangeItem.HotelRanges
                                    .Select(x => x.HotelId)
                                    .ToList();
                                break;
                            }
                        case LimitProductType.ApiHotel:
                            couponApiHotelIds = rangeItem.ApiHotelRanges.Select(x => x.ApiHotelId).ToList();
                            break;
                    }
                }

                //不判断是否有值.
                //需要与价格分组配置的本地酒店id比较取交集
                localHotelIds = localHotelIds.Intersect(couponHotelIds).ToList();
                thirdHotelIds = couponApiHotelIds;

                var specializedHotelIds = couponInfo.UserCouponRanges
                      .Where(s => s.ApiHotelRanges?.Any() is true)
                      .SelectMany(s => s.ApiHotelRanges)
                      .Where(s => s?.SpecializedHotelId is > 0)
                      .Select(s => s.SpecializedHotelId!.Value).Distinct()
                      .ToArray();
                if (specializedHotelIds?.Length is > 0)
                {
                    var specializedHotelItems = await _hotelApiCaller.GetSpecializedHotelItems(new Contracts.Common.Hotel.DTOs.SpecializedHotelDetail.SpecializedHotelItemInput
                    {
                        SpecializedHotelIds = specializedHotelIds,
                    });
                    thirdHotelIds.AddRange(specializedHotelItems.Select(x => x.HotelId));
                    thirdHotelIds = thirdHotelIds.Distinct().ToList();
                }
            }
        }

        if (input.ShowOnlyGroupRoom)
        {
            //查询包含可售团房策略的本地酒店
            var strategiesIds = checkPriceSetting.localHotelIds.Select(x => x.strategyId).ToList();
            var inSaleGroupRoomHotelIds = await _hotelApiCaller.GetIncludeGroupRoomHotel(strategiesIds);
            localHotelIds = inSaleGroupRoomHotelIds;
        }

        //校验配置
        if (localHotelIds.Any() is false && queryAgencyThirdHotel is false)
            return Ok(result);

        var esHotelPages = await _hotelApiCaller.SearchEsHotelV2(new EsHotelSearchInput
        {
            PageIndex = input.PageIndex,
            PageSize = input.PageSize,
            KeyWord = input.Name,
            CityCode = input.CityCode,
            StarLevel = input.StarLevel,
            AgencyLocalHotelId = localHotelIds,
            AgencyThirdHotelId = thirdHotelIds,
            AgencyThirdHotel = queryAgencyThirdHotel,
            DefaultIsStaffTag = checkPriceSetting.defaultIsStaffTag,
            DefaultTags = checkPriceSetting.defaultTags,
            DefaultIsReunionRoomTag = checkPriceSetting.defaultIsReunionRoomTag,
            TargetDistance = input.TargetDistance,
            TargetLongitude = input.TargetLongitude,
            TargetLatitude = input.TargetLatitude,
            SortType = input.SortType,
            FacilityIds = input.FacilityIds,
            PriceGroupId = priceGroupId,
            ShowOnlyGroupRoom = input.ShowOnlyGroupRoom
        });
        result.Total = esHotelPages.Total;
        if (esHotelPages.Data.Any() is false) return Ok(result);

        var supplementData = await EsHotelPageDataSupplement(esHotelPages.Data.ToList());
        var facilities = await _resourceApiCaller.GetHotelFacilities();
        facilities.RemoveAll(x => x.Id == ResourceKey.AirportPickupId);

        var pageList = new List<SearchHotelBffOutput>();
        foreach (var pageItem in esHotelPages.Data)
        {
            var supplementItem = supplementData.FirstOrDefault(x => x.esData.Id == pageItem.Id);
            if (supplementItem == default) continue;
            pageList.Add(new SearchHotelBffOutput
            {
                HotelId = supplementItem.esData.Id,
                ZHName = supplementItem.esData.ZHName,
                ENName = supplementItem.esData.ENName,
                FirstPhoto = supplementItem.esData.HotelPicture,
                Address = supplementItem.esData.Address,
                StarLevel = supplementItem.esData.StarLevel,
                CityCode = supplementItem.esData.CityCode,
                CityName = supplementItem.esData.CityName,
                CountryCode = supplementItem.esData.CountryCode,
                CountryName = supplementItem.esData.CountryName,
                Longitude = supplementItem.esData.Longitude,
                Latitude = supplementItem.esData.Latitude,
                HasReunionRoom = supplementItem.esData.ReunionRoom > 0,
                Facilities = facilities.Where(f => supplementItem.facilitiesIds.Contains(f.Id))
                    .Select(f => new HotelFacilitiesBffItem
                    {
                        Type = f.Type,
                        Name = f.Name,
                        ENName = f.ENName
                    }).ToList(),
                HotelIdList = supplementItem.esData.HotelIdList,
            });
        }

        result.Data = pageList;

        #region 补充优惠券信息

        var supplementCoupons = await SupplementCoupons(new SupplementHotelCouponsBffInput
        {
            AgencyId = currentUser.Provider,
            LimitCount = 3, //取前3条可领取
            Items = result.Data.Select(x => new SupplementHotelCouponsBffInputItem
            {
                HotelId = x.HotelId,
                HotelIdList = x.HotelIdList,
            })
            .ToList()
        });
        foreach (var item in result.Data)
        {
            var itemSupplementCoupons = supplementCoupons.Items.FirstOrDefault(x => x.HotelId == item.HotelId)
                ?.HotelCouponInfos;
            if (itemSupplementCoupons.Any() is false)
                continue;
            item.Coupons = itemSupplementCoupons;
        }

        #endregion
        return Ok(result);
    }

    /// <summary>
    /// 首页-查询汇智酒店列表数据
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(List<SearchApiHotelListOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchApiHotelList(SearchApiHotelListInput input)
    {
        var esHotelInfos = await _hotelApiCaller.GetEsHotelDetailV2(input.HotelIds.ToArray());
        var result = _mapper.Map<List<SearchApiHotelListOutput>>(esHotelInfos);
        if (input.ShowPageType.HasValue)
        {
            var tags = await _hotelApiCaller.GetByApiHotelIds(new Contracts.Common.Hotel.DTOs.HotelTag.GetHotelTagsInput
            {
                ShowPageType = input.ShowPageType,
                ApiHotelIds = input.HotelIds,
            });
            result.ForEach(x => {
                x.Tags = tags.FirstOrDefault(t => t.ApiHotelId == x.Id)?.Tags;
            });
        }
        return Ok(result);
    }

    private async Task<SupplementHotelCouponsBffOutput> SupplementCoupons(SupplementHotelCouponsBffInput input)
    {
        var result = new SupplementHotelCouponsBffOutput();
        var localHotels = input.Items.SelectMany(s => s.HotelIdList).Where(s => s.SupplierApiType == SupplierApiType.None);
        var thirdHotels = input.Items.SelectMany(s => s.HotelIdList).Where(s => s.SupplierApiType == SupplierApiType.Hop);
        var hotelIds = input.Items.SelectMany(x => x.HotelIdList.Select(x => x.HotelId)).ToArray();
        var coupons = await _marketingApiCaller.GetCouponInfosByHotelIds(new Contracts.Common.Marketing.DTOs.Coupon.GetByHotelIdsInput
        {
            HotelIds = localHotels.Select(s => s.HotelId).ToArray(),
            ApiHotelIds = thirdHotels.Select(s => s.HotelId).ToArray(),
        });
        var couponIds = coupons.Select(x => x.Id).ToArray();
        //当前分销商领取到的优惠券
        var userCoupons = await GetUserCouponDetails(input.AgencyId, couponIds);
        foreach (var item in input.Items)
        {
            var resultItem = new SupplementHotelCouponsBffOutputItem
            {
                HotelId = item.HotelId,
            };
            var localHotel = item.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.None);
            var thirdHotel = item.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.Hop);
            //当前酒店可领取的优惠券
            var currentHotelCoupons = coupons.Where(x =>
            (localHotel != null && x.HotelIds.Contains(localHotel.HotelId)) || (thirdHotel != null && x.ApiHotelIds.Contains(thirdHotel.HotelId)))
                .OrderByDescending(x => x.CouponActivityCreateTime)
                .ThenBy(x => x.CouponActivityItemId)
                .ToList();
            foreach (var coupon in currentHotelCoupons)
            {
                var status = userCoupons.Data.Any(x => x.Id.Equals(coupon.Id) && x.CouponActivityId.Equals(coupon.CouponActivityId))
                    ? UserCouponReceiveStatus.Received : UserCouponReceiveStatus.Unreceived;
                if (status is not UserCouponReceiveStatus.Unreceived)
                    continue;

                //根据酒店类型（本地/汇智），判断是否可领取优惠券
                //1.酒店是本地酒店,优惠券使用范围不包含本地酒店
                //2.酒店是汇智酒店,优惠券使用范围不包含汇智酒店
                if ((item.HotelIdList.Any(x => x.SupplierApiType is SupplierApiType.None)
                     && !item.HotelIdList.Any(x => x.SupplierApiType is SupplierApiType.Hop)
                     && !coupon.UserCouponRanges.Any(x => x.ProductType is LimitProductType.Hotel))
                    || (item.HotelIdList.Any(x => x.SupplierApiType is SupplierApiType.Hop)
                        && !item.HotelIdList.Any(x => x.SupplierApiType is SupplierApiType.None)
                        && !coupon.UserCouponRanges.Any(x => x.ProductType is LimitProductType.ApiHotel)))
                {
                    continue;
                }

                resultItem.HotelCouponInfos.Add(new HotelCouponInfo
                {
                    Id = coupon.Id,
                    CouponName = coupon.CouponName,
                    CouponType = coupon.CouponType,
                    Amount = coupon.Amount,
                    LimitMinAmt = coupon.LimitMinAmt,
                    CouponActivityId = coupon.CouponActivityId,
                    MaxDiscountAmt = coupon.MaxDiscountAmt,
                });

                //取前n条可领取
                if (input.LimitCount.HasValue)
                {
                    if (resultItem.HotelCouponInfos.Count.Equals(input.LimitCount.Value)) break;
                }
            }

            result.Items.Add(resultItem);
        }

        return result;
    }

    /// <summary>
    /// 查询推荐酒店列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(GetRecommendHotelsBffOutput))]
    public async Task<IActionResult> GetRecommendHotels(GetRecommendHotelsBffInput input)
    {
        var result = new GetRecommendHotelsBffOutput
        {
            MenuConfigEnable = false
        };
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();

        var pageSize = input.RecommendCount!.Value;
        if (input.RecommendPageType == B2BHotelRecommendPageType.OrderPage)
        {
            //B2B后台配置酒店的品类开关关闭
            var configResult = await _tenantApiCaller.GetB2BWebConfiguration();
            var hotelMenu = configResult.B2BMenuConfigs.FirstOrDefault(x => x.Menu == B2BMenuCategory.Hotel);
            if (hotelMenu == null || hotelMenu.IsDisplayed is false)
            {
                return Ok(result);
            }
        }
        else
        {
            //商品页面的推荐需要距离限制,如果为空则默认50km
            input.TargetDistance ??= 50;
        }

        //校验分销商信息
        var agencyInfo = await _tenantApiCaller.GetAgencyDetail(currentUser.Provider);
        if (agencyInfo.PriceGroupId is null or <= 0) return Ok(result);
        var priceGroupId = agencyInfo.PriceGroupId.Value;

        //校验分组启用状态
        var priceGroup = await _tenantApiCaller.GetPriceGroupDetail(priceGroupId);
        if (!priceGroup.Enable)
            return Ok(result);
        //校验价格配置
        var checkPriceSetting = await _hotelService.CheckAgencyPriceSetting(priceGroupId);
        var localHotelIds = checkPriceSetting.localHotelIds.Select(x => x.hotelId).Distinct().ToList();
        var queryAgencyThirdHotel = checkPriceSetting.queryAgencyThirdHotel;
        if (localHotelIds.Any() is false && queryAgencyThirdHotel is false)
            return Ok(result);

        var esHotelResult = new List<SearchEsHotelOutput>();
        var esHotelPageRequest = new EsHotelSearchInput
        {
            PageIndex = 1,
            PageSize = pageSize,
            CityCode = input.CityCode,
            StarLevel = input.StarLevel,
            AgencyLocalHotelId = localHotelIds,
            AgencyThirdHotel = queryAgencyThirdHotel,
            DefaultIsStaffTag = checkPriceSetting.defaultIsStaffTag,
            DefaultTags = checkPriceSetting.defaultTags,
            DefaultIsReunionRoomTag = checkPriceSetting.defaultIsReunionRoomTag,
            TargetDistance = input.TargetDistance ?? 0,
            TargetLongitude = input.TargetLongitude,
            TargetLatitude = input.TargetLatitude,
            SortType = input.RecommendPageType == B2BHotelRecommendPageType.DetailPage
                ? EsHotelSortType.Distance
                : EsHotelSortType.Default,
            PriceGroupId = priceGroupId
        };

        //查询当前分销商1个月内订单数量
        var orderCountRequest = new GetHotelOrderCountInput
        {
            AgencyId = currentUser.Provider,
            SellingPlatforms = new[] { SellingPlatform.B2BWeb, SellingPlatform.B2BApplet }
        };
        var orderCountResult = await _orderApiCaller.GetHotelOrderCount(orderCountRequest);
        orderCountResult = orderCountResult.OrderByDescending(x => x.OrderCount).ToList();//订单量由高到低
        //查询对应酒店城详情数据
        if (orderCountResult.Any())
        {
            var esHotelDetailIds = orderCountResult.Select(x => x.HotelId).ToArray();
            var esHotelDetails = await _hotelApiCaller.GetEsHotelDetailV2(esHotelDetailIds);
            var cityCodeEsHotels = esHotelDetails.Where(x => x.CityCode == input.CityCode).ToList();
            if (cityCodeEsHotels.Any())
            {
                var cityCodeHotelIds = cityCodeEsHotels.SelectMany(x => x.HotelIdList)
                    .Select(x => x.HotelId).ToList();
                orderCountResult = orderCountResult.Where(x => cityCodeHotelIds.Contains(x.HotelId))
                    .OrderByDescending(x => x.OrderCount)
                    .ToList();

                if (input.RecommendPageType == B2BHotelRecommendPageType.OrderPage)
                {
                    //按照订单量的酒店查询
                    esHotelPageRequest.HotelIds = orderCountResult.Select(x => x.HotelId).ToList();
                    esHotelPageRequest.PageSize = orderCountResult.Count;
                    var orderPageEsHotel = await _hotelApiCaller.SearchEsHotelV2(esHotelPageRequest);
                    if (orderPageEsHotel.Data.Any())
                    {
                        //获取订单量最多的前N个酒店数据
                        var orderPageEsHotelId = new List<long>();
                        foreach (var orderCountItem in orderCountResult)
                        {
                            //判断是否超过命中数量
                            if (orderPageEsHotelId.Count >= input.RecommendCount.Value) break;

                            orderPageEsHotelId.AddRange(from item in orderPageEsHotel.Data
                                                        where item.HotelIdList.Any(x => x.HotelId == orderCountItem.HotelId)
                                                        select item.Id);
                        }

                        if (orderPageEsHotelId.Any())
                        {
                            orderPageEsHotel.Data = orderPageEsHotel.Data.Where(x => orderPageEsHotelId.Contains(x.Id)).ToList();
                        }

                        //取前N个数据
                        orderPageEsHotel.Data = orderPageEsHotel.Data.Take(input.RecommendCount.Value).ToList();
                    }

                    esHotelResult.AddRange(orderPageEsHotel.Data);
                    //避免重复数据
                    esHotelPageRequest.PageSize = 50;
                    esHotelPageRequest.HotelIds = new List<long>();
                }
            }
        }

        //查询es酒店数据
        if (esHotelResult.Any() is false || esHotelResult.Count < pageSize)
        {
            var esHotelPages = await _hotelApiCaller.SearchEsHotelV2(esHotelPageRequest);
            var diffCount = pageSize - esHotelResult.Count;
            if (esHotelPages.Data.Any())
            {
                var filterEsId = esHotelResult.Select(x => x.Id).ToList();
                if (filterEsId.Any())
                {
                    //过滤已有数据
                    esHotelPages.Data = esHotelPages.Data.Where(x => !filterEsId.Contains(x.Id));
                }
                //补充差额数据
                var diffPage = esHotelPages.Data.Take(diffCount);
                esHotelResult.AddRange(diffPage);
            }

            if (esHotelResult.Any() is false)
                return Ok(result);
        }

        //查询最价格范围
        var priceRangeTask = _hotelService.GetRangePrice(new GetHotelListPriceBffInput
        {
            HotelIds = esHotelResult.Select(x => x.Id).ToList(),
            LiveDate = input.TravelDate!.Value,
            LeaveDate = input.TravelDate!.Value.AddDays(1)
        }, currentUser);
        //查询汇智酒店标签
        input.TagShowPageType ??= TagShowPageType.B2B_HotelPage_Detail;
        var apiHotelTagsTask = _hotelApiCaller.GetByApiHotelIds(new GetHotelTagsInput
        {
            ApiHotelIds = esHotelResult.SelectMany(x => x.HotelIdList)
                .Where(x => x.SupplierApiType == SupplierApiType.Hop)
                .Select(x => x.HotelId).Distinct().ToList(),
            ShowPageType = input.TagShowPageType
        });

        //补充数据
        var supplementDataTask = EsHotelPageDataSupplement(esHotelResult.ToList());
        await Task.WhenAll(priceRangeTask, supplementDataTask, apiHotelTagsTask);
        var priceRangeResult = priceRangeTask.Result;
        var supplementDataResult = supplementDataTask.Result;
        var apiHotelTagsResult = apiHotelTagsTask.Result;
        // SupplementHotelCouponsBffOutput? supplementCouponsResult = null;
        var userCoupons = new List<UserCouponOutput>();
        if (input.RecommendPageType == B2BHotelRecommendPageType.OrderPage)
        {
            //目前只支持订单页面的推荐
            //当前分销商领取到的优惠券
            var couponRangesTypes = new[] { LimitProductType.ApiHotel, LimitProductType.Hotel };
            var userCouponResponse = await GetUserCouponDetails(currentUser.Provider, null, UserCouponStatus.UnUsed);
            userCoupons.AddRange(userCouponResponse.Data
                .Where(x => x.ValidBeginDate <= input.TravelDate!.Value.Date && input.TravelDate!.Value.Date <= x.ValidEndDate)
                .Where(x => x.UserCouponRanges.Any(r => couponRangesTypes.Contains(r.ProductType))));
        }


        foreach (var item in esHotelResult)
        {
            var supplementItem = supplementDataResult.FirstOrDefault(x => x.esData.Id == item.Id);
            if (supplementItem == default) continue;

            //查询订单数量
            var relatedHotelIds = item.HotelIdList.Select(x => x.HotelId).ToList();
            var apiHotelId = item.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.Hop)?.HotelId;
            var localHotelId = item.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.None)?.HotelId;
            var orderCount = orderCountResult.Where(x => relatedHotelIds.Contains(x.HotelId))
                .Sum(x => x.OrderCount);
            //价格范围
            var rangePrice = priceRangeResult.FirstOrDefault(x => relatedHotelIds.Contains(x.HotelId));
            //汇智酒店标签
            var apiHotelTags = apiHotelId.HasValue
                ? apiHotelTagsResult.FirstOrDefault(x => x.ApiHotelId == apiHotelId)?.Tags.FirstOrDefault()?.Name
                : null;

            //过滤酒店
            if (input.FilterHotelId.HasValue)
            {
                if (relatedHotelIds.Contains(input.FilterHotelId.Value))
                    continue;
            }

            //价格范围过滤
            if (input.AverageMinPrice.HasValue || input.AverageMaxPrice.HasValue)
            {
                //没价格过滤
                if (rangePrice == null)
                    continue;

                bool minCheck = true;
                bool maxCheck = true;

                if (input.AverageMinPrice.HasValue)
                {
                    minCheck = rangePrice.MinPrice >= input.AverageMinPrice.Value;
                }

                if (input.AverageMaxPrice.HasValue)
                {
                    maxCheck = rangePrice.MaxPrice <= input.AverageMaxPrice.Value;
                }

                //价格范围不符合
                if (!minCheck && !maxCheck)
                    continue;
            }

            //补充优惠券信息
            BestDiscountDto? bestCouponDiscount = null;
            if (userCoupons.Any() && rangePrice is { MinPrice: not null })
            {
                decimal bestDiscountAmount = 0;
                var hotelUserCoupons = new List<UserCouponOutput>();
                foreach (var userCoupon in userCoupons)
                {
                    foreach (var userCouponRange in userCoupon.UserCouponRanges.Where(x =>
                                 x.ProductType is LimitProductType.Hotel or LimitProductType.ApiHotel))
                    {
                        switch (userCouponRange.RangeType)
                        {
                            case CouponRangeType.All:
                                //全部参加
                                hotelUserCoupons.Add(userCoupon);
                                break;
                            case CouponRangeType.Part:
                                {
                                    if (userCouponRange.ProductType == LimitProductType.Hotel && localHotelId.HasValue)
                                    {
                                        //本地酒店日历酒店限制范围
                                        if (userCouponRange.HotelRanges.Any(x => x.HotelId == localHotelId))
                                        {
                                            hotelUserCoupons.Add(userCoupon);
                                        }
                                    }

                                    if (userCouponRange.ProductType == LimitProductType.ApiHotel && apiHotelId.HasValue)
                                    {
                                        //API（汇智）酒店限制范围
                                        if (userCouponRange.ApiHotelRanges.Any(x => x.ApiHotelId == apiHotelId))
                                        {
                                            hotelUserCoupons.Add(userCoupon);
                                        }
                                    }

                                    break;
                                }
                        }
                    }
                }
                //计算最优的优惠券
                bestCouponDiscount = GetBestDiscount(hotelUserCoupons, new OrderProductInfo { OrderAmount = rangePrice.MinPrice.Value });
            }

            decimal? linePrice = null;
            decimal? minPrice = null;
            if (rangePrice is { MinPrice: not null })
            {
                //计算原价
                linePrice = rangePrice.MinPriceSetting.PriceSettingType switch
                {
                    //todo:此处的计算并不准确.因为涉及到币种的换算.目前只做划线价展示.
                    ChannelPriceSettingType.SubtractValue => rangePrice.MinPrice.Value + rangePrice.MinPriceSetting.PriceSettingValue,
                    ChannelPriceSettingType.SubtractRate => Math.Round(
                        rangePrice.MinPrice.Value * (1 + rangePrice.MinPriceSetting.PriceSettingValue * 0.01m), 2),
                    _ => null
                };

                //处理优惠折扣后的价格
                minPrice = rangePrice.MinPrice - (bestCouponDiscount?.DiscountAmount ?? 0);
            }
            result.Hotels.Add(new GetRecommendHotelsBffInfo
            {
                HotelId = supplementItem.esData.Id,
                ResourceHotelId = item.ResourceHotelId,
                ZHName = supplementItem.esData.ZHName,
                ENName = supplementItem.esData.ENName,
                FirstPhoto = supplementItem.esData.HotelPicture,
                Address = supplementItem.esData.Address,
                StarLevel = supplementItem.esData.StarLevel,
                Distance = item.Distance.HasValue ? Math.Round(item.Distance.Value / 1000, 2) : null,
                OrderCount = orderCount,
                MinPrice = minPrice,
                MaxPrice = rangePrice?.MaxPrice,
                LinePrice = linePrice,
                PriceSettingType = rangePrice?.MinPriceSetting?.PriceSettingType,
                PriceSettingValue = rangePrice?.MinPriceSetting?.PriceSettingValue,
                CurrencyCode = agencyInfo.CurrencyCode,
                HotelTag = apiHotelTags,
                CouponType = bestCouponDiscount?.CouponType,
                Discount = bestCouponDiscount?.Discount,
                DiscountType = bestCouponDiscount?.CouponType switch
                {
                    CouponType.FullReduction => DiscountType.SubtractValue,
                    CouponType.Discount => DiscountType.SubtractRate,
                    CouponType.Consumption => DiscountType.SubtractValue,
                    _ => null
                }
            });
        }

        if (input.RecommendPageType == B2BHotelRecommendPageType.OrderPage)
        {
            // 排序：1个月内订单数量由高到低，起价由低到高；
            result.Hotels = result.Hotels
                .OrderByDescending(x => x.OrderCount)
                .ThenByDescending(x => x.MinPrice.HasValue)
                .ThenBy(x => x.MinPrice)
                .ToList();
        }
        else
        {
            //距离由近至远，当日有价格 > 当日无价格，1个月内订单数量由高到低，价格由低至高
            result.Hotels = result.Hotels
                .OrderBy(x => x.Distance)
                .ThenByDescending(x => x.MinPrice.HasValue)
                .ThenByDescending(x => x.OrderCount)
                .ThenBy(x => x.MinPrice)
                .ToList();
        }

        result.MenuConfigEnable = result.Hotels.Any();
        return Ok(result);
    }

    /// <summary>
    /// 获取酒店列表最低价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(List<GetHotelListPriceBffOutput>))]
    public async Task<IActionResult> GetMinPrice(GetHotelListPriceBffInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var response = await _hotelService.GetRangePrice(input, currentUser);
        var result = response.Select(x => new GetHotelListPriceBffOutput
        {
            HotelId = x.HotelId,
            Price = x.MinPrice
        });
        return Ok(result);
    }

    /// <summary>
    /// 获取酒店房型信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(HotelRoomOutput))]
    public async Task<HotelRoomOutput> GetHotelRoom(GetHotelRoomInput input)
    {
        var supplierApiType = input.SupplierApiType;
        //本地酒店房型
        if (supplierApiType == Contracts.Common.Tenant.Enums.SupplierApiType.None)
        {
            var localRooms = await _hotelApiCaller.GetHotelRooms(new GetHotelRoomsInput { HotelId = input.HotelId });
            return localRooms.HotelRooms.Select(x => new HotelRoomOutput
            {
                RoomId = x.Id,
                RoomZHName = x.ZHName,
                RoomENName = x.ENName,
                AreaMin = x.AreaMin,
                AreaMax = x.AreaMax,
                FloorMin = x.FloorMin,
                FloorMax = x.FloorMax,
                MaximumOccupancy = x.MaximumOccupancy,
                BedType = x.BedType,
                ResourceRoomId = x.ResourceRoomId,
                RoomQuantity = x.RoomQuantity,
                WindowType = x.WindowType,
                FirstPhoto = x.Photos.FirstOrDefault(),
            }).FirstOrDefault(x => x.RoomId == input.RoomId);
        }
        var apihotel = (await _hotelApiCaller.GetApiHotelDetail(input.HotelId)).FirstOrDefault();
        if (apihotel is null)
            return null;
        var thirdRooms = await _resourceApiCaller.GetRoomsByHotelIds(new Contracts.Common.Resource.DTOs.GetRoomsByHotelIdsInput
        {
            HotelIds = new List<long> { apihotel.ResourceHotelId }
        });
        var thirdRoom = thirdRooms.FirstOrDefault();

        var hotelPhotos = await _resourceApiCaller.GetHotelPhotos(apihotel.ResourceHotelId);

        return thirdRoom.Rooms.Select(x =>
        {
            HotelRoomOutput dto = new()
            {
                RoomId = x.Id,
                RoomZHName = x.ZHName,
                RoomENName = x.ENName,
                AreaMin = x.AreaMin,
                AreaMax = x.AreaMax,
                FloorMin = x.FloorMin,
                FloorMax = x.FloorMax,
                MaximumOccupancy = x.MaximumOccupancy,
                ResourceRoomId = x.Id,
                WindowType = x.WindowType,
                RoomQuantity = x.RoomQuantity,
                FirstPhoto = hotelPhotos?.Rooms?.FirstOrDefault(x => x.Id == input.RoomId)?.Photos?.FirstOrDefault()?.Url ?? string.Empty,
            };
            if (!string.IsNullOrWhiteSpace(x.BedType))
            {
                var bedTypes = JsonConvert.DeserializeObject<List<BedType>>(x.BedType);
                dto.BedType = bedTypes;
            }
            return dto;
        }).FirstOrDefault(x => x.RoomId == input.RoomId);
    }

    /// <summary>
    /// 查询B2B分销商可售价格策略
    /// </summary>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt(200, typeof(List<GetSaleStrategyBffOutput>))]
    [SwaggerResponseExt(default, ErrorTypes.Inventory.GetInventoryFail)]
    [SwaggerResponseExt(default, ErrorTypes.Hotel.PricesNotEnough,
        ErrorTypes.Hotel.EndRoomNotBook,
        ErrorTypes.Hotel.HourRoomNotBook,
        ErrorTypes.Hotel.StayLongDiscountNotBook,
        ErrorTypes.Hotel.CalendarNotEnable)]
    public async Task<IActionResult> GetSaleStrategies(GetSaleStrategyBffInput input)
    {
        var result = await SearchSaleStrategies(input);
        return Ok(result);
    }

    /// <summary>
    /// 查询最优/常规价格策略
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt(200, typeof(HotelStrategyBffOutput))]
    [SwaggerResponseExt(default, ErrorTypes.Inventory.GetInventoryFail)]
    [SwaggerResponseExt(default, ErrorTypes.Hotel.PricesNotEnough,
        ErrorTypes.Hotel.EndRoomNotBook,
        ErrorTypes.Hotel.HourRoomNotBook,
        ErrorTypes.Hotel.StayLongDiscountNotBook,
        ErrorTypes.Hotel.CalendarNotEnable)]
    public async Task<IActionResult> SaleStrategies(GetSaleStrategyBffInput input)
    {
        var saleStrategies = await SearchSaleStrategies(input);
        //从saleStrategies的结果中筛选最优的priceStrategies，按是否含餐BoardCount>0，不含餐BoardCount=0，仅筛选每项单一价格price升序排序

        var grouping = saleStrategies.SelectMany(s => s.PriceStrategies)
             .GroupBy(x => new
             {
                 HasBoardCode = x.BoardCodeType != BoardCodeType.RO && x.BoardCount > 0,
                 CancelRulesType = x.CancelRulesType == CancelRulesType.LimitedTimeCancel
                 || x.CancelRulesType == CancelRulesType.FreeCancel
                 ? CancelRulesType.LimitedTimeCancel : CancelRulesType.CannotCancel,
             })
             .Select(g => new
             {
                 g.Key,
                 g.OrderBy(s => s.PostCouponPrice ?? s.TotalChannelPrice).First().Id
             })
             .ToList();
        var ids = grouping.Select(x => x.Id);
        List<OptimalSaleStrategyBffOutput> optimalSaleStrategies = new();
        foreach (var item in saleStrategies)
        {
            var optimalSaleStrategy = _mapper.Map<OptimalSaleStrategyBffOutput>(item);
            //报价策略筛选
            optimalSaleStrategy.PriceStrategies = _mapper.Map<List<OptimalSaleStrategyBffItem>>(
                item.PriceStrategies.Where(x => ids.Contains(x.Id)));

            //如果没有报价策略则不添加
            if (optimalSaleStrategy.PriceStrategies.Any() is false)
                continue;

            foreach (var priceStrategy in optimalSaleStrategy.PriceStrategies)
            {
                var group = grouping.FirstOrDefault(x => x.Id == priceStrategy.Id);
                switch (group?.Key.CancelRulesType)
                {
                    case CancelRulesType.CannotCancel:
                        priceStrategy.LowestPriceType = group.Key.HasBoardCode
                             ? OptimalSaleStrategyLowestPriceType.MealNonCancelable
                            : OptimalSaleStrategyLowestPriceType.NoMealNonCancelable;
                        break;
                    case CancelRulesType.LimitedTimeCancel:
                        priceStrategy.LowestPriceType = group.Key.HasBoardCode
                             ? OptimalSaleStrategyLowestPriceType.MealCancelable
                            : OptimalSaleStrategyLowestPriceType.NoMealCancelable;
                        break;
                }
            }
            optimalSaleStrategies.Add(optimalSaleStrategy);
        }
        //按报价存在的最低价格排序
        optimalSaleStrategies = optimalSaleStrategies
            .Where(x => x.PriceStrategies.Any())
            .OrderBy(x => x.PriceStrategies.Min(p => p.PostCouponPrice ?? p.TotalChannelPrice))
            .ToList();
        HotelStrategyBffOutput output = new()
        {
            OptimalSaleStrategies = optimalSaleStrategies,
            SaleStrategies = saleStrategies,
        };
        return Ok(output);
    }

    private async Task<List<GetSaleStrategyBffOutput>> SearchSaleStrategies(GetSaleStrategyBffInput input)
    {
        var result = new List<GetSaleStrategyBffOutput>();
        if (input.LiveDate == input.LeaveDate)
            return result;
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        //校验分销商信息
        var agencyInfo = await _tenantApiCaller.GetAgencyDetail(currentUser.Provider);
        if (agencyInfo.PriceGroupId is null or <= 0) return result;
        var priceGroupId = agencyInfo.PriceGroupId.Value;

        //校验分组启用状态
        var priceGroup = await _tenantApiCaller.GetPriceGroupDetail(priceGroupId);
        if (!priceGroup.Enable)
            return result;

        var checkPriceSetting = await _hotelService.CheckAgencyPriceSetting(priceGroupId);
        if (checkPriceSetting.localHotelIds.Any() is false &&
            checkPriceSetting.queryAgencyThirdHotel is false)
            return result;

        var esHotelInfo = await _hotelApiCaller.GetEsHotelDetailV2(input.HotelId);
        if (esHotelInfo.Any() is false)
        {
            return result;
        }

        // 查询saas配置
        var saasTenantApiSupplierConfig = await _tenantApiCaller.TenantApiSupplierConfigDetail(new GetTenantApiSupplierConfigDetailInput
        {
            SupplierApiType = SupplierApiType.Hop,
            TenantId = currentUser.Tenant
        });

        var esHotel = esHotelInfo.First();
        var localHotelInfo = esHotel.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.None);
        var thirdHotelInfo = esHotel.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.Hop);
        // 租户api供应商配置判断
        if (!saasTenantApiSupplierConfig.Enabled ||
            (saasTenantApiSupplierConfig.IsAllHotel == false && !saasTenantApiSupplierConfig.HotelIds.Contains(esHotel.ResourceHotelId)))
        {
            thirdHotelInfo = null;
        }

        //判断第三方酒店是否配置了指定酒店配置
        var thirdHotelAgencyChannelPriceSettings = new List<AgencyChannelPriceSettingOutput>();
        if (checkPriceSetting.queryAgencyThirdHotel is false)
        {
            thirdHotelInfo = null;
        }
        else
        {
            if (thirdHotelInfo != null)
            {
                var checkHuiZhiSellChannelSettings = _hotelService.CheckHuiZhiSellChannelSettings(
                    checkPriceSetting.huiZhiPriceSettings,
                    thirdHotelInfo.HotelId,
                    true,
                    true,
                    Enum.GetValues<SellHotelTag>().ToList());

                thirdHotelAgencyChannelPriceSettings = checkHuiZhiSellChannelSettings.agencyChannelPriceSetting;
                if (checkHuiZhiSellChannelSettings.thirdHotelChannelPriceSettings.Any() is false)
                {
                    //无本地酒店和无汇智酒店
                    if (localHotelInfo == null && checkHuiZhiSellChannelSettings.defaultChannelHotelPriceSettings.Any() is false)
                    {
                        return result;
                    }

                    //无汇智酒店
                    if (checkHuiZhiSellChannelSettings.defaultChannelHotelPriceSettings.Any() is false)
                        thirdHotelInfo = null;
                }
            }
        }

        if (localHotelInfo != null)
        {
            //查询本地酒店
            input.HotelId = localHotelInfo.HotelId;
            result = await GetLocalHotelPriceStrategy(input,
                currentUser,
                agencyInfo);
        }

        if (thirdHotelInfo != null)
        {
            //查询第三方酒店
            input.HotelId = thirdHotelInfo.HotelId;

            var thirdSaleData = await GetThirdPartHotelPriceStrategy(input,
                thirdHotelInfo.SupplierApiType,
                currentUser,
                agencyInfo,
                thirdHotelAgencyChannelPriceSettings,
                esHotel,
                saasTenantApiSupplierConfig.HotelPriceChannelTypes);

            if (result.Any())
            {
                //本地有引用该酒店时,房型排序根据商户侧的排序规则
                //相同房型的合并一起，同一房型下，排序上优先显示本地酒店的价格策略
                var localHotelRooms = await _hotelApiCaller.GetHotelRooms(new GetHotelRoomsInput { HotelId = localHotelInfo.HotelId });

                //合并结果
                var combineResult = new List<GetSaleStrategyBffOutput>();
                foreach (var localHotelRoom in localHotelRooms.HotelRooms.OrderBy(x => x.Sort))
                {
                    var combineItem = new GetSaleStrategyBffOutput();
                    var saleByLocal = result
                        .FirstOrDefault(x => x.ResourceRoomId == localHotelRoom.ResourceRoomId);
                    var saleByThird = thirdSaleData
                        .FirstOrDefault(x => x.ResourceRoomId == localHotelRoom.ResourceRoomId);
                    if (saleByLocal == null && saleByThird == null)
                        continue;

                    if (saleByLocal != null)
                    {
                        combineItem = saleByLocal;
                    }

                    if (saleByThird != null)
                    {
                        if (combineItem.PriceStrategies.Any())
                        {
                            combineItem.PriceStrategies.AddRange(saleByThird.PriceStrategies);
                        }
                        else
                        {
                            combineItem = saleByThird;
                            combineItem.FirstPhoto = localHotelRoom.Photos.FirstOrDefault();
                        }
                    }

                    combineResult.Add(combineItem);
                }

                result = combineResult;
            }
            else
            {
                result.AddRange(thirdSaleData);
            }
        }

        //推送更新API酒店权重
        if (thirdHotelInfo != null && string.IsNullOrWhiteSpace(input.PriceStrategyId))
        {
            _ = _hotelApiCaller.PushApiHotelWeightValue(new PushWeightValueInput[]
            {
                new PushWeightValueInput
                {
                    HotelId = thirdHotelInfo.HotelId,
                    TenantId = currentUser.Tenant,
                    Value = 5 //点击查看详情时，权重分 5
                }
            });
        }

        //过滤
        var filterResult = new List<GetSaleStrategyBffOutput>();
        if (input.RoomId is > 0 || !string.IsNullOrEmpty(input.BedType))
        {
            if (input.RoomId is > 0)
            {
                result = result
                    .Where(x => x.ResourceRoomId == input.RoomId.Value)
                    .ToList();
            }

            if (!string.IsNullOrEmpty(input.BedType))
            {
                result = result
                    .Where(x => x.BedType.Any(b => b.main == input.BedType))
                    .ToList();
            }

            if (result.Any() is false)
                return result;
        }

        //查询资源酒店房型加床政策
        var getHotelExtraBedPolicyInput = new Contracts.Common.Resource.DTOs.HotelExtend.GetHotelExtraBedPolicyInput
        {
            HotelId = esHotel.ResourceHotelId,
        };
        var hotelRoomPolicies = await _resourceApiCaller.GetHotelExtraBedPolicy(getHotelExtraBedPolicyInput);

        foreach (var item in result)
        {
            var saleStrategies = new List<SaleStrategyBffItem>();
            saleStrategies.AddRange(item.PriceStrategies);

            //房型加床政策现解析不完整，先用酒店层级的加床策略
            if (hotelRoomPolicies != null && hotelRoomPolicies.Any())
            {
                var hotelRoomExtraBedPolicies = new List<HotelExtraBedPolicyOutput>();
                //判断加床政策显示哪个
                if (item.ExtraBedType.HasValue && item.ExtraBedType.Value != HotelExtraBedType.None)
                {
                    var matchHotelRoomExtraBedPolicies = new List<HotelExtraBedPolicyOutput>();
                    var queryHotelRoomExtraBedPolicies = hotelRoomPolicies.Where(x => x.Type == HotelExtraBedCategoryType.Extra);

                    if (item.ExtraBedType == HotelExtraBedType.All)
                        matchHotelRoomExtraBedPolicies = queryHotelRoomExtraBedPolicies.ToList();
                    else
                    {
                        matchHotelRoomExtraBedPolicies = hotelRoomPolicies.Where(x => x.BedType == item.ExtraBedType
                            || x.BedType == HotelExtraBedType.All).ToList();
                        matchHotelRoomExtraBedPolicies.ForEach(x =>
                        {
                            x.BedType = item.ExtraBedType.Value;
                        });
                    }

                    if (matchHotelRoomExtraBedPolicies.Any())
                        hotelRoomExtraBedPolicies.AddRange(matchHotelRoomExtraBedPolicies);
                }
                //所有房型有现有床政策就显示
                if (item.IsHasChildrenExistingBed.HasValue && item.IsHasChildrenExistingBed.Value)
                {
                    var existingPolicies = hotelRoomPolicies.Where(x => x.Type == HotelExtraBedCategoryType.Existing).ToList();
                    if (existingPolicies.Any())
                        hotelRoomExtraBedPolicies.AddRange(existingPolicies);
                }
                hotelRoomExtraBedPolicies = hotelRoomExtraBedPolicies
                    .OrderBy(x => x.BedType).ThenBy(x => Math.Abs(x.Min)).ThenBy(x => x.Max)
                    .ToList();
                item.HotelRoomExtraBedPolicies = _mapper.Map<List<HotelRoomExtraBedPolicyBffOutput>>(hotelRoomExtraBedPolicies);
            }

            if (input.PriceStrategyType is > 0)
            {
                saleStrategies = saleStrategies
                    .Where(x => x.PriceStrategyType == input.PriceStrategyType)
                    .ToList();
            }

            if (input.MaximumOccupancy >= 1)
            {
                //筛选满足的价格策略：价格策略可住人数>=入住人数
                saleStrategies = saleStrategies
                    .Where(x => x.MaximumOccupancy >= input.MaximumOccupancy)
                    .ToList();
            }

            if (input.CancelRulesType.HasValue)
            {
                //免费取消，可取消，限时取消；默认全部
                saleStrategies = saleStrategies
                    .Where(x => x.CancelRulesType == input.CancelRulesType.Value)
                    .ToList();
            }

            if (input.IsBreakfast.HasValue)
            {
                //全部，不含早（=0时），含早（>0时）
                saleStrategies = input.IsBreakfast.Value
                    ? saleStrategies
                        .Where(x => x.NumberOfBreakfast > 0)
                        .ToList()
                    : saleStrategies
                        .Where(x => x.NumberOfBreakfast == 0)
                        .ToList();
            }

            //餐食筛选
            if (input.Boards?.Count is > 0)
            {
                saleStrategies = saleStrategies
                    .Where(s => input.Boards.Any(x => x.BoardCodeType == s.BoardCodeType && x.BoardCount == s.BoardCount))
                    .ToList();
            }

            if (input.StrategyConfirmType.HasValue)
            {
                //本地酒店
                //商家确认（设置不自动确认，设置自动确认且库存<=0）
                //立即确认（设置自动确认且库存>0）
                var localStrategies = input.StrategyConfirmType == PriceStrategyConfirmType.Auto
                    ? saleStrategies
                        .Where(x => x.SupplierApiType == SupplierApiType.None
                                    && x.IsAutoConfirm
                                    && x.CalendarPrices.All(c => c.AvailableQuantity > 0))
                        .ToList()
                    : saleStrategies
                        .Where(x => x.SupplierApiType == SupplierApiType.None
                                    && (x.IsAutoConfirm == false || (x.IsAutoConfirm
                                                                     && x.CalendarPrices.Any(c =>
                                                                         c.AvailableQuantity <= 0))))
                        .ToList();

                //汇智酒店
                //商家确认：直采酒店  库存为0 ；非直采酒店
                //立即确认（直采酒店  且有库存）
                var thirdStrategies = input.StrategyConfirmType == PriceStrategyConfirmType.Auto
                    ? saleStrategies
                        .Where(x => x.SupplierApiType == SupplierApiType.Hop
                                    && x.IsDirect!.Value && x.CalendarPrices.All(c => c.AvailableQuantity > 0))
                        .ToList()
                    : saleStrategies
                        .Where(x => x.SupplierApiType == SupplierApiType.Hop
                                    && (x.IsDirect!.Value == false || (x.IsDirect!.Value
                                                                && x.CalendarPrices.Any(c =>
                                                                    c.AvailableQuantity <= 0))))
                        .ToList();

                saleStrategies = new List<SaleStrategyBffItem>();
                saleStrategies.AddRange(localStrategies);
                saleStrategies.AddRange(thirdStrategies);
            }
            //酒店价格策略确认方式
            foreach (var saleStrategy in saleStrategies)
            {
                switch (saleStrategy.SupplierApiType)
                {
                    case SupplierApiType.None:
                        if (saleStrategy.IsAutoConfirm && saleStrategy.CalendarPrices.All(c => c.AvailableQuantity >= input.RoomNum && c.Enabled))
                            saleStrategy.ConfirmType = PriceStrategyConfirmType.Auto;
                        break;
                    default:
                        if (saleStrategy.IsAutoConfirm && saleStrategy.CalendarPrices.All(c => c.AvailableQuantity >= input.RoomNum && c.Enabled))
                            saleStrategy.ConfirmType = PriceStrategyConfirmType.Auto;
                        break;
                }
            }

            if (input.AverageMaxPrice.HasValue || input.AverageMinPrice.HasValue)
            {
                //均价区间
                var avgCheckStrategies = new List<SaleStrategyBffItem>();
                var nightNum = input.LeaveDate.Subtract(input.LiveDate).Days;
                foreach (var strategy in saleStrategies)
                {
                    var sumPrice = strategy.CalendarPrices
                        .Sum(x => x.ChannelPrice);
                    var avgPrice = Math.Round((decimal)(sumPrice / nightNum), 2);
                    bool minCheck = !(avgPrice < input.AverageMinPrice);
                    bool maxCheck = !(avgPrice > input.AverageMaxPrice);
                    if (minCheck && maxCheck)
                    {
                        avgCheckStrategies.Add(strategy);
                    }
                }
                saleStrategies = avgCheckStrategies;
            }

            //含早过滤
            if (input.AdultBreakfasts?.Any() is true)
            {
                saleStrategies = saleStrategies.Where(x => input.AdultBreakfasts.Contains(x.NumberOfBreakfast))
                    .ToList();
            }
            if (input.ChildrenAges is not null && input.ChildrenAges!.Any() is true)
            {
                saleStrategies.ForEach(x =>
                {
                    if (x.IsDirect!.Value && x.NumberOfBreakfast > 0)
                        x.ChildBreakfast = input.ChildrenAges!.Count();
                });
            }

            if (saleStrategies.Any())
            {
                //同一个房型的价格策略排序规则
                //1）按照价格(总价格)从低往高排 
                //2）有库存的往前排(库存≤0与库存＞0)
                saleStrategies = saleStrategies
                    .OrderByDescending(x => x.SupplierApiType)
                    .ThenBy(o => o.SumChannelPrice)
                    .ThenByDescending(o => o.MinAvailableQuantity is null)
                    .ThenByDescending(o => o.MinAvailableQuantity)
                    .ToList();

                var filterRoomInfo = item;

                foreach (var strategy in saleStrategies)
                {
                    #region 预订建议项

                    //1、起订量
                    if (input.RoomNum < strategy.NumberOfRooms)
                        strategy.BookingAdvices.Add(new(BookingAdviceType.NumberOfRooms, strategy.NumberOfRooms));
                    //2、提前预订天数
                    if (strategy.BookingHoursInAdvance > 0)
                        if (input.LiveDate.Date.AddDays(1).AddHours(-strategy.BookingHoursInAdvance) <= DateTime.Now)
                        {
                            strategy.BookingAdvices.Add(new(BookingAdviceType.BookingHoursInAdvance,
                                strategy.BookingHoursInAdvance));
                        }

                    //3、连住天数
                    var nights = input.LeaveDate.Subtract(input.LiveDate).Days;
                    if (nights < strategy.NumberOfNights)
                        strategy.BookingAdvices.Add(new(BookingAdviceType.NumberOfNights, strategy.NumberOfNights));
                    //4.最大连住天数限制
                    if (strategy.LimitNumberOfNights is > 0 && nights > strategy.LimitNumberOfNights.Value)
                    {
                        strategy.BookingAdvices.Add(new(BookingAdviceType.LimitNumberOfNights,
                            strategy.LimitNumberOfNights.Value));
                    }

                    #endregion
                }
                ;

                filterRoomInfo.PriceStrategies = saleStrategies;
                filterResult.Add(filterRoomInfo);
            }
        }
        //不同房型，按照最低价格策略价格(总价)从低往高排
        filterResult = filterResult
            .OrderBy(o => o.PriceStrategies.Min(m => m.SumChannelPrice))
            .ToList();

        //仅人民币适用优惠券
        if (agencyInfo.CurrencyCode == Currency.CNY.ToString())
        {
            await AddAvailableCoupons(filterResult, currentUser.Provider, currentUser.UserId);
        }
        return filterResult;
    }

    /// <summary>
    /// 补充可用优惠券
    /// </summary>
    /// <param name="filterResult"></param>
    /// <param name="agencyId"></param>
    private async Task AddAvailableCoupons(List<GetSaleStrategyBffOutput> filterResult, long agencyId,long userId)
    {
        //可使用的优惠券
        var userCouponDetails = await _marketingApiCaller.GetUserCoupons(new GetUserCouponsInput
        {
            UserCouponStatus = UserCouponStatus.UnUsed,
            AgencyId = agencyId,
            SpecifiedUserId = userId,
        });
        var userCouponDetailItems = userCouponDetails
            .Where(x => x.UserCouponRanges.Any(i => i.ProductType is LimitProductType.Hotel || i.ProductType is LimitProductType.ApiHotel))
            .ToArray();
        var specializedHotelIds = userCouponDetailItems.SelectMany(s => s.UserCouponRanges)
            .Where(s => s.ApiHotelRanges?.Any() is true)
            .SelectMany(s => s.ApiHotelRanges)
            .Where(s => s?.SpecializedHotelId is > 0)
            .Select(s => s.SpecializedHotelId!.Value).Distinct()
            .ToArray();
        var specializedHotelItems = specializedHotelIds?.Length is > 0 ? (await _hotelApiCaller.GetSpecializedHotelItems(new Contracts.Common.Hotel.DTOs.SpecializedHotelDetail.SpecializedHotelItemInput
        {
            SpecializedHotelIds = specializedHotelIds,
            HotelIds = filterResult.SelectMany(s => s.PriceStrategies).Select(s => s.HotelId).ToArray()
        })) : new List<Contracts.Common.Hotel.DTOs.SpecializedHotelDetail.SpecializedHotelItemOutput>();

        foreach (var filter in filterResult)
        {
            foreach (var priceStrategy in filter.PriceStrategies)
            {
                //Hop的价格策略
                //筛选商品类型为汇智酒店，优惠券范围为全部，并且包含当前价格策略标签的优惠券
                //GDS价格策略 不处理
                if (priceStrategy.SupplierApiType.Equals(SupplierApiType.Hop))
                {
                    ApiHotelDirectTagType? hotelDirectTag = GetApiHotelDirectTag(priceStrategy.IsDirect, priceStrategy.PriceStrategyType, priceStrategy.Tag);
                    foreach (var item in userCouponDetailItems)
                    {
                        if (priceStrategy.AvailableCoupons.Any(x => x.Id.Equals(item.Id)))
                            continue;

                        //优惠券品类限制
                        var productTypeRange = item.UserCouponRanges.FirstOrDefault(r => r.ProductType == LimitProductType.ApiHotel);
                        switch (productTypeRange?.RangeType)
                        {
                            case CouponRangeType.All:
                                {
                                    var apiHotelDirectTags = productTypeRange.ApiHotelDirectTags;
                                    if (productTypeRange.ProductType == LimitProductType.ApiHotel && apiHotelDirectTags?.Length is > 0)
                                    {
                                        if (hotelDirectTag is not null && apiHotelDirectTags!.Contains(hotelDirectTag!.Value))
                                        {
                                            priceStrategy.AvailableCoupons.Add(item);
                                        }
                                    }
                                    else
                                        priceStrategy.AvailableCoupons.Add(item);
                                }
                                break;
                            case CouponRangeType.Part:
                                {
                                    var apiHotelRange = productTypeRange.ApiHotelRanges
                                        .FirstOrDefault(r => r.ApiHotelId == priceStrategy.HotelId);
                                    var specializedHotels = productTypeRange.ApiHotelRanges
                                       .Where(r => r.SpecializedHotelId is > 0)
                                       .ToArray();
                                    var specializedHotel = specializedHotels.FirstOrDefault(x => specializedHotelItems.Any(s =>
                                            x.SpecializedHotelId == s.SpecializedHotelId && s.HotelId == priceStrategy.HotelId));
                                    if (apiHotelRange is null && specializedHotel is null)
                                        continue;
                                    ApiHotelDirectTagType[]? apiHotelDirectTagTypes = apiHotelRange?.ApiHotelDirectTags ?? specializedHotel?.ApiHotelDirectTags;
                                    if (apiHotelDirectTagTypes?.Length is > 0)
                                    {
                                        if (hotelDirectTag is not null && apiHotelDirectTagTypes!.Contains(hotelDirectTag!.Value))
                                        {
                                            priceStrategy.AvailableCoupons.Add(item);
                                        }
                                    }
                                    else
                                    {
                                        priceStrategy.AvailableCoupons.Add(item);
                                    }
                                }
                                break;
                        }
                    }
                }
                else if (priceStrategy.SupplierApiType.Equals(SupplierApiType.None))
                {
                    //普通酒店的价格策略
                    var addUserCouponDetailItems = userCouponDetailItems
                        .Where(x => x.UserCouponRanges.Any(i => i.ProductType is LimitProductType.Hotel && i.RangeType is CouponRangeType.All || (i.HotelRanges is not null && i.HotelRanges.Any(hr => hr.PriceStrategyIds.Contains(long.Parse(priceStrategy.Id)))))).DistinctBy(x => x.Id).ToList();
                    priceStrategy.AvailableCoupons.AddRange(addUserCouponDetailItems);
                }
                else if (priceStrategy.SupplierApiType.Equals(SupplierApiType.GDS))
                    continue;
                if (priceStrategy.TotalChannelPrice.HasValue)
                {
                    var total = priceStrategy.SumChannelPrice!.Value * priceStrategy.RoomNum;
                    long.TryParse(priceStrategy.Id, out var itemId);
                    OrderProductInfo orderProductInfo = new()
                    {
                        ProductId = priceStrategy.HotelId,
                        ItemId = itemId,
                        OrderAmount = total,
                        OrderHotelInfo = new OrderHotelInfo()
                        {
                            NumberOfRoom = priceStrategy.RoomNum,
                            IsDirect = priceStrategy.IsDirect,
                            DatePrices = priceStrategy.CalendarPrices.Select(x => x.ChannelPrice ?? 0),
                            PriceStrategyType = priceStrategy.PriceStrategyType,
                            Tag = priceStrategy.Tag,
                        }
                    };
                    var bestDiscount = GetBestDiscount(priceStrategy.AvailableCoupons, orderProductInfo, true);
                    if (bestDiscount.Discount.HasValue)
                    {
                        priceStrategy.AvailableCoupons = priceStrategy.AvailableCoupons
                            .OrderByDescending(x => x.Id == bestDiscount.Id!.Value)
                            .ToList();
                        priceStrategy.PostCouponPrice = Math.Floor(total - bestDiscount.DiscountAmount);//券后价
                    }
                }
            }
        }
    }

    private static ApiHotelDirectTagType? GetApiHotelDirectTag(bool? isDirect, PriceStrategyType priceStrategyType, SellHotelTag? tag)
    {
        ApiHotelDirectTagType? hotelDirectTag = null;
        if (isDirect is true)
        {
            hotelDirectTag = priceStrategyType switch
            {
                Contracts.Common.Hotel.Enums.PriceStrategyType.GroupRoom => ApiHotelDirectTagType.DirectGroupRoom,
                _ => ApiHotelDirectTagType.DirectRoom,
            };
        }
        else
        {
            hotelDirectTag = tag switch
            {
                Contracts.Common.Resource.Enums.SellHotelTag.O => ApiHotelDirectTagType.O,
                Contracts.Common.Resource.Enums.SellHotelTag.A => ApiHotelDirectTagType.A,
                Contracts.Common.Resource.Enums.SellHotelTag.B => ApiHotelDirectTagType.B,
                Contracts.Common.Resource.Enums.SellHotelTag.C => ApiHotelDirectTagType.C,
                Contracts.Common.Resource.Enums.SellHotelTag.D => ApiHotelDirectTagType.D,
                Contracts.Common.Resource.Enums.SellHotelTag.E => ApiHotelDirectTagType.E,
                _ => null,
            };
        }
        return hotelDirectTag;
    }

    /// <summary>
    /// Es酒店分页数据信息补充
    /// </summary>
    private async Task<List<(SearchEsHotelOutput esData, List<long> facilitiesIds)>> EsHotelPageDataSupplement(
        List<SearchEsHotelOutput> pageData)
    {
        var result = new List<(SearchEsHotelOutput esData, List<long> facilitiesIds)>();
        var resourceHotelIds = new List<long>();
        var localHotelIds = new List<long>();
        foreach (var item in pageData)
        {
            //优先查询本地酒店信息.
            var localHotel = item.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.None);
            if (localHotel != null)
            {
                localHotelIds.Add(localHotel.HotelId);
            }
            else
            {
                resourceHotelIds.Add(item.ResourceHotelId);
            }
        }

        //本地酒店信息补充
        if (localHotelIds.Any())
        {
            var hotelBaseInfo = await _hotelApiCaller.GetHotelDetailByIds(new GetHotelByIdsInput
            {
                HotelIds = localHotelIds
            });
            //查询包含可售团房策略的本地酒店
            var inSaleGroupRoomHotelIds = await _hotelApiCaller.GetIncludeGroupRoomHotel(localHotelIds);

            foreach (var item in hotelBaseInfo)
            {
                var pageDataItem = pageData
                    .First(x => x.HotelIdList.Any(h => h.HotelId == item.Id));
                pageDataItem.Id = item.Id;
                pageDataItem.HotelPicture = item.HotelPicture;
                pageDataItem.Latitude = item.Latitude;
                pageDataItem.Longitude = item.Longitude;
                pageDataItem.Address = item.Address;
                pageDataItem.ReunionRoom = pageDataItem.ReunionRoom > 0 &&
                                           pageDataItem.HotelIdList.Exists(
                                               x => x.SupplierApiType == SupplierApiType.Hop)
                    ? pageDataItem.ReunionRoom
                    : inSaleGroupRoomHotelIds.Exists(x => x == item.Id)
                        ? 1
                        : 0;
                result.Add(new ValueTuple<SearchEsHotelOutput, List<long>>(pageDataItem, item.FacilitiesIds));
            }
        }

        //第三方酒店信息补充
        if (resourceHotelIds.Any())
        {
            var simpleInfo = await _resourceApiCaller.GetHotelDetailByHotelIds(resourceHotelIds.ToArray());
            foreach (var item in pageData
                         .Where(x => resourceHotelIds.Contains(x.ResourceHotelId)))
            {
                var simpleInfoItem = simpleInfo.FirstOrDefault(x => x.Id == item.ResourceHotelId);
                item.HotelPicture = simpleInfoItem?.FirstPhoto ?? string.Empty;
                item.Address = simpleInfoItem?.Address ?? string.Empty;
                item.Latitude = simpleInfoItem?.Latitude;
                item.Longitude = simpleInfoItem?.Longitude;

                var nestedItem = item.HotelIdList.First(x => x.SupplierApiType == SupplierApiType.Hop);
                item.Id = nestedItem.HotelId;

                result.Add(
                    new ValueTuple<SearchEsHotelOutput, List<long>>(item, simpleInfoItem?.FacilitiesIds ?? new()));
            }
        }

        return result;
    }

    /// <summary>
    /// 本地酒店价格策略
    /// </summary>
    private async Task<List<GetSaleStrategyBffOutput>> GetLocalHotelPriceStrategy(
        GetSaleStrategyBffInput input,
        CurrentUser currentUser,
        GetAgenciesByIdsOutput agencyInfo)
    {
        var result = new List<GetSaleStrategyBffOutput>();
        //仅筛选团房
        if (input.IsGroupBooking is true)
            return result;
        //如果查询条件存在儿童，不查询本地酒店报价
        if (input.ChildrenAges?.Any() is true)
        {
            return result;
        }
        // var agencyInfo = await _tenantApiCaller.GetAgencyDetail(currentUser.Provider);
        // if (agencyInfo.PriceGroupId is null or <= 0) return result;
        var priceGroupId = agencyInfo.PriceGroupId;

        #region 查询该价格分组下已配置的酒店数据

        var settingRequest = new QueryChannelPriceInput
        {
            PriceGroupId = priceGroupId.Value,
            ProductType = new[] { ChannelProductType.Hotel },
            ProductIds = new[] { input.HotelId }
        };
        if (!string.IsNullOrEmpty(input.PriceStrategyId))
        {
            _ = long.TryParse(input.PriceStrategyId, out long strategyId);
            settingRequest.SkuIds = new[] { strategyId };
        }

        var settingResponse = await _agencyChannelPriceService.QueryPricesSettings(settingRequest);
        if (settingResponse.Any() is false) return result;

        var skuIds = settingResponse.Select(x => x.SkuId).Distinct();

        #endregion

        #region 查询分销商酒店价格策略

        var priceStrategies = await _hotelApiCaller.GetAgencyStrategies(new AgencyGetInput
        {
            HotelId = input.HotelId,
            LiveDate = input.LiveDate,
            LeaveDate = input.LeaveDate,
            RoomNum = input.RoomNum,
            PriceStrategyIds = skuIds,
            VerifySale = !string.IsNullOrEmpty(input.PriceStrategyId)
        });
        int? countryCode = default;
        if (input.Nationality is not null)
        {
            countryCode = input.Nationality?.CountryCode;
            if (countryCode is (int)MixedRegionCountry.MainlandChina
                or (int)MixedRegionCountry.HongKong
                or (int)MixedRegionCountry.Macao
                or (int)MixedRegionCountry.Taiwan)
            {
                countryCode = 10;//中国
            }
        }

        #endregion

        #region 计算汇率

        var getExchangeRateInput = new List<GetExchangeRatesInput>();
        var costToSaleExchangeRateInput = priceStrategies
            .SelectMany(x => x.PriceStrategies)
            .GroupBy(x => new { x.CostCurrencyCode, x.SaleCurrencyCode })
            .Select(x => new GetExchangeRatesInput
            {
                BaseCurrencyCode = x.Key.CostCurrencyCode,
                TargetCurrencyCode = x.Key.SaleCurrencyCode
            })
            .ToList();
        var saleToB2BExchangeRateInput = priceStrategies
            .SelectMany(x => x.PriceStrategies)
            .GroupBy(x => x.SaleCurrencyCode)
            .Select(x => new GetExchangeRatesInput
            {
                BaseCurrencyCode = x.Key,
                TargetCurrencyCode = agencyInfo.CurrencyCode
            })
            .ToList();

        getExchangeRateInput.AddRange(costToSaleExchangeRateInput);
        getExchangeRateInput.AddRange(saleToB2BExchangeRateInput);
        var exchangeRateList = await _paymentService.GetCurrencyExchangeRateList(getExchangeRateInput);

        #endregion

        result = priceStrategies.OrderBy(x => x.Sort)
            .Select(x =>
            {
                var data = new GetSaleStrategyBffOutput
                {
                    RoomId = x.ResourceRoomId,
                    ResourceRoomId = x.ResourceRoomId,
                    RoomZHName = x.RoomZHName,
                    RoomENName = x.RoomENName,
                    FirstPhoto = x.FirstPhoto,
                    WindowType = x.WindowType,
                    RoomQuantity = x.RoomQuantity,
                    MaximumOccupancy = x.MaximumOccupancy,
                    AreaMax = x.AreaMax,
                    AreaMin = x.AreaMin,
                    FloorMax = x.FloorMax,
                    FloorMin = x.FloorMin,
                    BedType = x.BedType,
                    ExtraBedFee = x.ExtraBedFee,
                    ExtraBedType = x.ExtraBedType,
                    IsHasChildren = x.IsHasChildren,
                    ExtraBedFeeCurrency = x.ExtraBedFeeCurrency,
                    IsHasChildrenExistingBed = x.IsHasChildrenExistingBed,
                };

                foreach (var strategy in x.PriceStrategies)
                {
                    if (countryCode.HasValue && strategy.Nationalities.Any() && !strategy.Nationalities.Any(n => n.CountryCode == countryCode))
                        continue;
                    var strategyOutputItem = new SaleStrategyBffItem
                    {
                        Id = strategy.Id.ToString(),
                        Name = strategy.Name,
                        HotelId = strategy.HotelId,
                        PriceStrategyType = strategy.PriceStrategyType,
                        NumberOfBreakfast = strategy.NumberOfBreakfast,
                        CancelRulesType = strategy.CancelRulesType,
                        NumberOfNights = strategy.NumberOfNights,
                        NumberOfRooms = strategy.NumberOfRooms,
                        BookingHoursInAdvance = strategy.BookingHoursInAdvance,
                        IsAutoConfirm = strategy.IsAutoConfirm,
                        CancelRule = strategy.CancelRule,
                        NationalNames = strategy.Nationalities.Select(x => x.ZHName),
                        MaximumOccupancy = strategy.MaximumOccupancy,
                        PreBookingCode = strategy.PreBookingCode,
                    };
                    if (strategy.NumberOfBreakfast > 0)
                    {
                        strategyOutputItem.BoardCodeType = BoardCodeType.BB;
                        strategyOutputItem.BoardCount = strategy.NumberOfBreakfast;
                    }

                    var settingItem = settingResponse.First(s => s.SkuId == strategy.Id);

                    foreach (var priceOutputItem in from price in strategy.CalendarPrices
                                                    let channelPrice = _agencyChannelPriceService.ConvertB2BPrice(exchangeRateList, settingItem,
                                                        price.SalePrice, price.CostPrice,
                                                        strategy.CostCurrencyCode, strategy.SaleCurrencyCode, agencyInfo.CurrencyCode)
                                                    let originalCostPrice = _agencyChannelPriceService.ConvertOriginalPrice(new ConvertOriginalPriceDto
                                                    {
                                                        ExchangeRates = exchangeRateList,
                                                        OriginalCostPrice = price.CostPrice,
                                                        OriginalSalePrice = price.SalePrice,
                                                        OriginalCostCurrencyCode = strategy.CostCurrencyCode,
                                                        OriginalSaleCurrencyCode = strategy.SaleCurrencyCode,
                                                        AgencyCurrencyCode = agencyInfo.CurrencyCode,
                                                    })
                                                        .originalCostPrice
                                                    select new SaleStrategyPriceInventoryBffItem
                                                    {
                                                        Date = price.Date,
                                                        Enabled = price.Enabled,
                                                        AvailableQuantity = price.AvailableQuantity,
                                                        TotalQuantity = price.TotalQuantity,
                                                        ChannelPrice = channelPrice,
                                                        LinePrice = originalCostPrice,
                                                        OverSaleable = strategy.OverSaleable
                                                    })
                    {
                        strategyOutputItem.CalendarPrices.Add(priceOutputItem);
                    }

                    strategyOutputItem.SumChannelPrice = strategyOutputItem.CalendarPrices.Sum(s => s.ChannelPrice);
                    strategyOutputItem.AvgChannelPrice = strategyOutputItem.SumChannelPrice.HasValue
                        ? Math.Floor(strategyOutputItem.SumChannelPrice.Value / strategyOutputItem.CalendarPrices.Count)
                        : null;
                    strategyOutputItem.SumLinePrice = strategyOutputItem.CalendarPrices.Sum(s => s.LinePrice);
                    strategyOutputItem.MinChannelPrice = strategyOutputItem.CalendarPrices.Min(s => s.ChannelPrice);
                    strategyOutputItem.RoomNum = input.RoomNum;
                    if (strategyOutputItem.SumChannelPrice.HasValue)
                    {
                        strategyOutputItem.TotalChannelPrice = agencyInfo.CurrencyCode == Currency.CNY.ToString() ?
                        (int)Math.Floor(strategyOutputItem.SumChannelPrice!.Value * input.RoomNum)
                        : strategyOutputItem.SumChannelPrice!.Value * input.RoomNum;
                    }
                    var noOverSaleableCalendarPrice =
                        strategyOutputItem.CalendarPrices.Where(c => c.OverSaleable == false)
                            .ToList();
                    if (noOverSaleableCalendarPrice.Any())
                    {
                        //最低库存数量.只统计不可超售的日历库存
                        strategyOutputItem.MinAvailableQuantity =
                            noOverSaleableCalendarPrice.Min(c => c.AvailableQuantity);
                    }

                    data.PriceStrategies.Add(strategyOutputItem);
                }

                return data;
            })
            .Where(s => s.PriceStrategies.Any())
            .ToList();

        return result;
    }

    /// <summary>
    /// 分销商第三方酒店价格策略
    /// </summary>
    private async Task<List<GetSaleStrategyBffOutput>> GetThirdPartHotelPriceStrategy(
        GetSaleStrategyBffInput input,
        SupplierApiType supplierApiType,
        CurrentUser currentUser,
        GetAgenciesByIdsOutput agencyInfo,
        List<AgencyChannelPriceSettingOutput> agencyChannelPriceSettings,
        SearchEsHotelOutput esHotel,
        List<HotelPriceChannelType> saasTenantHotelPriceChannelTypes)
    {
        var result = new List<GetSaleStrategyBffOutput>();
        // 可售类型
        var saleTypes = MapApiHotelSaleType(saasTenantHotelPriceChannelTypes);
        //是否验证可售
        var verifySale = !string.IsNullOrEmpty(input.PriceStrategyId);
        var resourceHotelId = esHotel.ResourceHotelId;
        //查询第三方价格策略信息
        var strategies = await _resourceApiCaller.GetThirdHotelPrice(new GetThirdHotelPriceInput
        {
            AdultNum = input.MaximumOccupancy,
            ChildrenAges = input.ChildrenAges,
            RoomNum = input.RoomNum,
            ResourceHotelId = resourceHotelId,
            SupplierApiTypes = new[] { supplierApiType },
            CheckIn = input.LiveDate,
            CheckOut = input.LeaveDate,
            TenantId = currentUser.Tenant,
            IsGroupBooking = input.IsGroupBooking,
            Nationality = input.Nationality != null ? input.Nationality.IsoCode : null
        });
        if (strategies.Rooms.Any() is false) return result;

        //查询资源酒店房型信息
        var resourceRooms = await _resourceApiCaller.GetRoomsByHotelIds(new GetRoomsByHotelIdsInput
        {
            HotelIds = new List<long>
            {
                resourceHotelId
            }
        });

        var resourceRoomInfos = resourceRooms
            .FirstOrDefault(x => x.HotelId == resourceHotelId);
        if (resourceRoomInfos == null) return result;

        //查询租户信息
        var tenantInfoTask = _tenantApiCaller.GetSysConfigByTenantIds(currentUser.Tenant);
        //查询Api供应商
        var apiSupplierTask = _tenantApiCaller.QuerySuppliers(new Contracts.Common.Tenant.DTOs.Supplier.QuerySuppliersInput
        {
            SupplierType = SupplierType.Api,
            SupplierApiParentType = SupplierApiParentType.Hotel,
            SupplierApiType = SupplierApiType.Hop
        });
        //查询房型图片
        var roomPhotosTask = _resourceApiCaller.GetHotelPhotos(resourceHotelId);
        Task.WaitAll(tenantInfoTask, apiSupplierTask, roomPhotosTask);

        var tenantInfo = tenantInfoTask.Result.FirstOrDefault();
        var apiSupplier = apiSupplierTask.Result;
        var roomPhotos = roomPhotosTask.Result;

        #region 计算汇率

        var costCurrencyCode = apiSupplier.First().CurrencyCode;
        var saleCurrencyCode = tenantInfo.CurrencyCode;
        var agencyCurrencyCode = agencyInfo.CurrencyCode;
        var getExchangeRateInput = new List<GetExchangeRatesInput>
        {
            new GetExchangeRatesInput {BaseCurrencyCode = costCurrencyCode, TargetCurrencyCode = saleCurrencyCode},
            new GetExchangeRatesInput {BaseCurrencyCode = saleCurrencyCode, TargetCurrencyCode = agencyCurrencyCode}
        };
        var exchangeRateList = await _paymentService.GetCurrencyExchangeRateList(getExchangeRateInput);

        #endregion

        //入住天数
        var stayDays = input.LeaveDate.Subtract(input.LiveDate).Days;
        foreach (var resourceRoomItem in resourceRoomInfos.Rooms)
        {
            var room = strategies.Rooms.FirstOrDefault(x => x.ResourceRoomId == resourceRoomItem.Id);
            if (room == null)
                continue;

            var roomPriceStrategies = room.Pricestrategies
                .Where(x => x.SaleType is null || saleTypes.Contains(x.SaleType!.Value)) // 过滤报价计划类型
                .ToList();
            if (!string.IsNullOrEmpty(input.PriceStrategyId))
            {
                if (roomPriceStrategies.All(x => x.PricestrategyId != input.PriceStrategyId))
                {
                    continue;
                }

                roomPriceStrategies = roomPriceStrategies
                    .Where(x => x.PricestrategyId == input.PriceStrategyId)
                    .ToList();
            }

            var roomPhoto = roomPhotos.Rooms.Where(x => x.Id == room.ResourceRoomId)
                .SelectMany(x => x.Photos)
                .FirstOrDefault();
            var roomBedType = !string.IsNullOrWhiteSpace(room.BedType)
                ? JsonConvert.DeserializeObject<List<BedType>>(room.BedType)
                : new List<BedType>();
            var resultItem = new GetSaleStrategyBffOutput
            {
                RoomId = room.ResourceRoomId,
                ResourceRoomId = room.ResourceRoomId,
                RoomZHName = room.ZHName,
                RoomENName = room.ENName,
                WindowType = room.WindowType,
                RoomQuantity = room.RoomQuantity,
                MaximumOccupancy = room.MaximumOccupancy,
                AreaMax = room.AreaMax,
                AreaMin = room.AreaMin,
                FloorMax = room.FloorMax,
                FloorMin = room.FloorMin,
                FirstPhoto = roomPhoto?.Url,
                BedType = roomBedType,
                ExtraBedFee = resourceRoomItem.ExtraBedFee,
                ExtraBedType = resourceRoomItem.ExtraBedType,
                IsHasChildren = resourceRoomItem.IsHasChildren,
                ExtraBedFeeCurrency = resourceRoomItem.ExtraBedFeeCurrency,
                IsHasChildrenExistingBed = resourceRoomItem.IsHasChildrenExistingBed,
            };

            foreach (var strategy in roomPriceStrategies)
            {
                if (!strategy.Enabled)
                    continue;

                //过滤日历价格和入住天数不匹配
                if (strategy.Calendars.Count() != stayDays)
                {
                    if (verifySale)
                    {
                        throw new BusinessException("PricesNotEnough", "PricesNotEnough");
                    }
                }

                if (verifySale)
                {
                    if (strategy.Calendars.Any(x => x.Enabled == false))
                        throw new BusinessException("CalendarNotEnable", "CalendarNotEnable");

                    //判断提前预订时间
                    var nowTime = DateTime.Now;
                    if (strategy.BookingHoursInAdvance > 0
                        && input.LiveDate.Date.AddHours(24 - strategy.BookingHoursInAdvance) < nowTime)
                    {
                        continue;
                    }
                }

                var strategyOutputItem = new SaleStrategyBffItem
                {
                    Id = strategy.PricestrategyId,
                    Name = strategy.Name,
                    ENName = !string.IsNullOrWhiteSpace(strategy.ENName) ? strategy.ENName : strategy.Name,
                    HotelId = input.HotelId,
                    PreBookingCode = strategy.PreBookingCode,
                    PriceStrategyType = strategy.PriceStrategyType,
                    NumberOfNights = strategy.NumberOfNights,
                    LimitNumberOfNights = strategy.LimitNumberOfNights,
                    NumberOfRooms = strategy.NumberOfRooms,
                    NumberOfBreakfast = strategy.NumberOfBreakfast,
                    BoardCodeType = strategy.BoardCodeType,
                    BoardCount = strategy.BoardCount,
                    CancelRulesType = strategy.CancelRule?.CancelRulesType,
                    BookingHoursInAdvance = strategy.BookingHoursInAdvance,
                    SupplierApiType = strategy.SupplierApiType,
                    IsDirect = strategy.IsDirect,
                    IsAutoConfirm = strategy.IsAutoConfirm,
                    MaximumOccupancy = strategy.MaxOccupancy,
                    Tag = strategy.Tag,
                    BookingBenefits = strategy.BookingBenefits,
                };

                if (strategy.CancelRule != null)
                {
                    strategyOutputItem.CancelRule = new CancelRule
                    {
                        CancelChargeType = strategy.CancelRule.CancelChargeType,
                        CancelRulesType = strategy.CancelRule.CancelRulesType,
                        BeforeCheckInDays = strategy.CancelRule.BeforeCheckInDays,
                        BeforeCheckInTime = strategy.CancelRule.BeforeCheckInTime,
                        CheckInDateTime = strategy.CancelRule.CheckInDateTime,
                        ChargeValue = strategy.CancelRule.ChargeValue
                    };
                }

                strategyOutputItem.NationalNames = strategy.NationalNames;

                //查询符合价格策略售卖类型的汇智酒店配置
                var isReunionRoomTag = strategy.PriceStrategyType == PriceStrategyType.GroupRoom;
                var convertB2BPriceSetting = _hotelService.GetStrategyChannelPriceSetting(strategy.IsDirect,
                    isReunionRoomTag,
                    strategy.Tag,
                    agencyChannelPriceSettings);

                //查无配置跳过
                if (convertB2BPriceSetting == null) continue;

                foreach (var calendar in strategy.Calendars)
                {
                    var minChannelPrice = _agencyChannelPriceService.ConvertB2BPrice(exchangeRateList,
                        convertB2BPriceSetting,
                        calendar.CostPrice, calendar.CostPrice,
                        costCurrencyCode, tenantInfo.CurrencyCode, agencyInfo.CurrencyCode);

                    var originalCostPrice = _agencyChannelPriceService.ConvertOriginalPrice(new ConvertOriginalPriceDto
                    {
                        ExchangeRates = exchangeRateList,
                        OriginalCostPrice = calendar.CostPrice,
                        OriginalSalePrice = calendar.CostPrice,
                        OriginalCostCurrencyCode = costCurrencyCode,
                        OriginalSaleCurrencyCode = tenantInfo.CurrencyCode,
                        AgencyCurrencyCode = agencyInfo.CurrencyCode,
                    })
                        .originalCostPrice;

                    strategyOutputItem.CalendarPrices.Add(new SaleStrategyPriceInventoryBffItem
                    {
                        Date = calendar.Date,
                        Enabled = calendar.Enabled,
                        AvailableQuantity = calendar.Quantity,
                        ChannelPrice = minChannelPrice,
                        LinePrice = originalCostPrice,
                        OverSaleable = calendar.OverSaleable
                    });
                }

                strategyOutputItem.SumChannelPrice = strategyOutputItem.CalendarPrices.Sum(s => s.ChannelPrice);
                strategyOutputItem.AvgChannelPrice = strategyOutputItem.SumChannelPrice.HasValue
                    ? Math.Floor(strategyOutputItem.SumChannelPrice.Value / strategyOutputItem.CalendarPrices.Count)
                    : null;
                strategyOutputItem.SumLinePrice = strategyOutputItem.CalendarPrices.Sum(s => s.LinePrice);
                strategyOutputItem.MinChannelPrice = strategyOutputItem.CalendarPrices.Min(s => s.ChannelPrice);
                strategyOutputItem.RoomNum = input.RoomNum;
                if (strategyOutputItem.SumChannelPrice.HasValue)
                {
                    strategyOutputItem.TotalChannelPrice = agencyInfo.CurrencyCode == Currency.CNY.ToString() ?
                    (int)Math.Floor(strategyOutputItem.SumChannelPrice!.Value * input.RoomNum)
                    : strategyOutputItem.SumChannelPrice!.Value * input.RoomNum;
                }
                var noOverSaleableCalendarPrice =
                    strategyOutputItem.CalendarPrices.Where(c => c.OverSaleable == false)
                        .ToList();
                if (noOverSaleableCalendarPrice.Any())
                {
                    //最低库存数量.只统计不可超售的日历库存
                    strategyOutputItem.MinAvailableQuantity =
                        noOverSaleableCalendarPrice.Min(c => c.AvailableQuantity);
                }
                resultItem.PriceStrategies.Add(strategyOutputItem);
            }
            resultItem.StaffTag = esHotel.StaffTag;
            result.Add(resultItem);
        }

        return result;
    }

    /// <summary>
    /// 根据酒店获取所有第三方酒店关联关系
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt(200, typeof(IEnumerable<GetThirdHotelBindOutput>))]
    public async Task<IActionResult> GetThirdHotelBinds(GetThirdHotelBindsInput input)
    {
        input.SupplierApiTypes = new List<SupplierApiType> {
            SupplierApiType.Ctrip,
            SupplierApiType.Meituan,
        }.ToArray();
        var thirdHotelBinds = await _resourceApiCaller.GetThirdHotelBinds(input);
        return Ok(thirdHotelBinds);
    }

    /// <summary>
    /// 酒店国籍数据获取
    /// </summary>
    /// <value>国籍数据加入 中国大陆,中国香港，中国澳门，中国台湾 </value>
    /// <returns></returns>
    [Authorize]
    [HttpGet]
    [SwaggerResponseExt(200, typeof(IEnumerable<GetHotelMixedCountriesBffOutput>))]
    public async Task<IActionResult> GetMixedCountries()
    {
        //国籍数据加入 中国大陆,中国香港，中国澳门，中国台湾
        var mixedCountries = await MixCountries();
        return Ok(mixedCountries);
    }

    private async Task<PagingModel<UserCouponOutput>> GetUserCouponDetails(long agencyId, long[] couponIds, UserCouponStatus? userCouponStatus = null)
    {
        var userCoupons = await _marketingApiCaller.GetUserCouponDetails(new GetDetailsInput
        {
            AgencyId = agencyId,
            PageIndex = 1,
            PageSize = 1000000,
            CouponIds = couponIds,
            UserCouponStatus = userCouponStatus ?? UserCouponStatus.Unknown
        });
        return userCoupons;
    }

    private async Task fillExchangeAmount(GetHotelDetailsBffOutput result)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();

        try
        {
            var exchangeInputs = new List<GetExchangeRatesInput>();
            var agencyDetail = await _tenantApiCaller.GetAgencyDetail(currentUser.Provider);
            if (agencyDetail != null && !string.IsNullOrEmpty(agencyDetail.CurrencyCode))
            {
                var baseCurrencyCodes = new List<string>();
                var currencyCode = agencyDetail.CurrencyCode;

                var depositCurrency = result.HotelPolicy.HotelExtend?.DepositCurrency;
                if (!string.IsNullOrEmpty(depositCurrency) && depositCurrency != currencyCode)
                    baseCurrencyCodes.Add(depositCurrency);

                var hotelMealPolicyCurrencies = result.HotelPolicy.HotelMealPolicies?
                    .Where(x => !string.IsNullOrEmpty(x.Currency) && x.Currency != currencyCode)
                    .Select(x => x.Currency)
                    .Distinct()
                    .ToList();

                if (hotelMealPolicyCurrencies.Any())
                    baseCurrencyCodes.AddRange(hotelMealPolicyCurrencies);

                if (baseCurrencyCodes.Any())
                {
                    baseCurrencyCodes = baseCurrencyCodes.Distinct().ToList();

                    exchangeInputs = baseCurrencyCodes.Select(x => new GetExchangeRatesInput
                    {
                        BaseCurrencyCode = x,
                        TargetCurrencyCode = currencyCode
                    }).ToList();

                    var exchangeRates = await _paymentApiCaller.GetExchangeRates(exchangeInputs);
                    if (exchangeRates.Any() is false)
                        return;

                    if (result.HotelPolicy.HotelMealPolicies.Any())
                    {
                        foreach (var hotelMealPolicy in result.HotelPolicy.HotelMealPolicies)
                        {
                            var exchange = exchangeRates.FirstOrDefault(x => x.BaseCurrencyCode == hotelMealPolicy.Currency);
                            if (exchange != null)
                            {
                                hotelMealPolicy.CurrentCurrency = exchange.TargetCurrencyCode;
                                hotelMealPolicy.CurrentAmount = Math.Round(exchange.ExchangeRate * hotelMealPolicy.Amount, 0);
                            }
                        }
                    }
                    if (!string.IsNullOrEmpty(depositCurrency) && depositCurrency != currencyCode)
                    {
                        var exchange = exchangeRates.FirstOrDefault(x => x.BaseCurrencyCode == depositCurrency);
                        if (exchange != null)
                        {
                            result.HotelPolicy.HotelExtend.CurrentAmount = Math.Round(result.HotelPolicy.HotelExtend.Amount.Value * exchange.ExchangeRate, 0);
                            result.HotelPolicy.HotelExtend.CurrentCurrency = exchange.TargetCurrencyCode;
                        }
                    }
                }

            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "酒店早餐收费汇率转换失败,hotelId:{0}", result.Id);
        }
    }

    private async Task<IEnumerable<GetHotelMixedCountriesBffOutput>> MixCountries()
    {
        var countries = await _resourceApiCaller.GetCountries();
        var result = countries.Select(x => new GetHotelMixedCountriesBffOutput
        {
            CountryCode = x.CountryCode,
            ZHName = x.ZHName,
            ENName = x.ENName,
            IsoCode = x.IsoCode,
        })
        .Where(x => x.CountryCode > 10)//排除其他,添加 中国大陆,中国香港，中国澳门，中国台湾
        .ToList();

        result.AddRange(new List<GetHotelMixedCountriesBffOutput>
        {
            new()
            {
                CountryCode = (int)MixedRegionCountry.MainlandChina,
                ZHName = "中国大陆",
                ENName = "Mainland China",
                IsoCode = "CN"
            },
            new()
            {
                CountryCode = (int)MixedRegionCountry.HongKong,
                ZHName = "中国香港",
                ENName = "Hong Kong, China",
                IsoCode = "HK"
            },
            new()
            {
                CountryCode = (int)MixedRegionCountry.Macao,
                ZHName = "中国澳门",
                ENName = "Macao, China",
                IsoCode = "MO"
            },
            new()
            {
                CountryCode = (int)MixedRegionCountry.Taiwan,
                ZHName = "中国台湾",
                ENName = "Taiwan, China",
                IsoCode = "TW"
            }
        });
        result = result.OrderBy(s => s.CountryCode).ToList();
        return result;
    }

    record BestDiscountDto
    {
        public long? Id { get; set; }
        public decimal? Discount { get; set; }

        public decimal DiscountAmount { get; set; }

        public CouponType? CouponType { get; set; }
    }

    /// <summary>
    /// 参考 /UserCoupon/GetByOrderProducts
    /// </summary>
    /// <param name="userCoupons"></param>
    /// <param name="price"></param>
    /// <param name="checkLimitMinAmt">是否检查起用金额</param>
    /// <returns></returns>
    private BestDiscountDto GetBestDiscount(List<UserCouponOutput> userCoupons, OrderProductInfo orderProductInfo, bool checkLimitMinAmt = false)
    {
        var price = orderProductInfo.OrderAmount;
        var dto = new BestDiscountDto();
        foreach (var userCoupon in userCoupons)
        {
            if (checkLimitMinAmt && userCoupon.LimitMinAmt > price) { continue; }
            decimal discount = 0;
            switch (userCoupon.CouponType)
            {
                case CouponType.FullReduction:
                    discount = userCoupon.Amount;
                    break;
                case CouponType.Discount:
                    discount = Math.Round((1 - userCoupon.Amount) * price, 2);
                    break;
                case CouponType.Consumption:
                    discount = userCoupon.Amount;//消费券面额
                    break;
                case CouponType.HotelDiscount:
                    var orderHotelInfo = orderProductInfo?.OrderHotelInfo;
                    if (orderHotelInfo is null) break;
                    var rooms = orderHotelInfo.NumberOfRoom;
                    var nights = orderHotelInfo.DatePrices.Count();
                    var orderAmount = orderProductInfo.OrderAmount;
                    if (userCoupon.NumberOfRoom < orderHotelInfo.NumberOfRoom || userCoupon.Nights < nights)
                    {
                        var enableRooms = userCoupon.NumberOfRoom!.Value < rooms ? userCoupon.NumberOfRoom.Value : rooms;
                        var enableNights = userCoupon.Nights!.Value < nights ? userCoupon.Nights.Value : nights;
                        //可优惠的金额
                        orderAmount = orderHotelInfo.DatePrices.Take(enableNights).Sum() * enableRooms;
                    }
                    discount = Math.Round((1 - userCoupon.Amount) * orderAmount, 2);
                    break;
            }

            if (userCoupon.MaxDiscountAmt > 0 && discount > userCoupon.MaxDiscountAmt)
                discount = userCoupon.MaxDiscountAmt;
            //优惠金额大于可用产品的订单总额 则优惠金额=可用产品的订单总额
            if (discount > price)
                discount = price;

            if (discount > dto.DiscountAmount)
            {
                dto.Id = userCoupon.Id;
                dto.DiscountAmount = discount;
                dto.Discount = userCoupon.CouponType == CouponType.Discount ? (1 - userCoupon.Amount) : userCoupon.Amount;
                dto.CouponType = userCoupon.CouponType;
            }
        }
        return dto;
    }

    private async Task<GetHotelDetailsBffOutput> DetailData(long hotelId, bool isCustomer)
    {
        var result = new GetHotelDetailsBffOutput();
        var esHotelInfo = await _hotelApiCaller.GetEsHotelDetailV2(hotelId);
        if (esHotelInfo.Any() is false)
        {
            return result;
        }
        var esHotel = esHotelInfo.First();
        //优先展示本地酒店
        var localHotel = esHotel.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.None);
        var thirdHotel = esHotel.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.Hop);
        if (localHotel != null)
        {//查询本地酒店
            var localHotelDetail = await _hotelApiCaller.GetHotelDetail(localHotel.HotelId);
            result = _mapper.Map<GetHotelDetailsBffOutput>(localHotelDetail);
            result.Id = hotelId;

            var hotelPolicy = await _hotelApiCaller.GetDetailPolicy(new Contracts.Common.Hotel.DTOs.HotelExtend.HotelPolicyDetailInput
            {
                Id = localHotel.HotelId,
            });

            result.HotelPolicy = _mapper.Map<HotelPolicyBffOutput>(hotelPolicy);

            var resourceHotelDetail = await _resourceApiCaller.GetHotelDetail(esHotel.ResourceHotelId);
            result.Brand = resourceHotelDetail.Brand;
            result.MeetingCount = resourceHotelDetail.MeetingCount;
            result.OfficialWebsite = resourceHotelDetail.OfficialWebsite;
            result.MeetingIntro = resourceHotelDetail.MeetingIntro;
            result.ENMeetingIntro = resourceHotelDetail.ENMeetingIntro;
            result.HotelIntroFeatures = resourceHotelDetail.HotelIntroFeatures;
            result.HotelIntroNearbys = resourceHotelDetail.HotelIntroNearbys;
            result.HotelIntroRooms = resourceHotelDetail.HotelIntroRooms;
            result.Attachments = resourceHotelDetail.Attachments;

        }
        else
        {//查询资源库酒店信息
            var resourceHotelDetail = await _resourceApiCaller.GetHotelDetail(esHotel.ResourceHotelId);
            result = _mapper.Map<GetHotelDetailsBffOutput>(resourceHotelDetail);
            result.Id = hotelId;
            result.ResourceHotelId = resourceHotelDetail.Id;

            var hotelPolicy = await _resourceApiCaller.GetDetailPolicy(new Contracts.Common.Resource.DTOs.HotelExtend.HotelPolicyDetailInput
            {
                Id = result.ResourceHotelId,
            });
            result.HotelPolicy = _mapper.Map<HotelPolicyBffOutput>(hotelPolicy);
        }

        var meetinResult = await _resourceApiCaller.SearchHotelMeeting(new Contracts.Common.Resource.DTOs.HotelMeeting.SearchHotelMeetingInput()
        {
            HotelId = esHotel.ResourceHotelId,
            PageIndex = 1,
            PageSize = 10000,
        });
        result.Meetings = meetinResult.Data.ToList();
        result.MeetingCount = result.Meetings.Count();

        if (result is not null)
        {
            var city = (await _resourceApiCaller.QueryCities(new QueryInput { CityCodes = new int[] { result.CityCode } })).FirstOrDefault();
            if (city is not null)
            {
                result.EnCityName = city.ENName;
                result.EnProvinceName = city.ProvinceEnName;
                result.EnCountryName = city.CountryEnName;
            }
            result.HotelIdList = esHotel.HotelIdList;
        }

        #region 补充优惠券信息
        if (isCustomer == false)
        {
            //获取当前酒店 可领取/已领取 的所有优惠券
            var hotelIds = result.HotelIdList.Select(x => x.HotelId).ToArray();
            var coupons = await _marketingApiCaller.GetCouponInfosByHotelIds(new Contracts.Common.Marketing.DTOs.Coupon.GetByHotelIdsInput
            {
                HotelIds = localHotel != null ? new long[] { localHotel.HotelId } : Array.Empty<long>(),
                ApiHotelIds = thirdHotel != null ? new long[] { thirdHotel.HotelId } : Array.Empty<long>(),
            });
            coupons = coupons.Where(x =>
                (localHotel != null && x.HotelIds.Contains(localHotel.HotelId)) || (thirdHotel != null && x.ApiHotelIds.Contains(thirdHotel.HotelId)))
                    .OrderByDescending(x => x.CouponActivityCreateTime)
                    .ThenBy(x => x.CouponActivityItemId)
                    .ToList();

            foreach (var coupon in coupons)
            {
                //根据酒店类型（本地/汇智），判断是否可领取优惠券
                //1.酒店是本地酒店,优惠券使用范围不包含本地酒店
                //2.酒店是汇智酒店,优惠券使用范围不包含汇智酒店
                if ((result.HotelIdList.Any(x => x.SupplierApiType is SupplierApiType.None)
                    && !result.HotelIdList.Any(x => x.SupplierApiType is SupplierApiType.Hop)
                    && !coupon.UserCouponRanges.Any(x => x.ProductType is LimitProductType.Hotel))
                    || (result.HotelIdList.Any(x => x.SupplierApiType is SupplierApiType.Hop)
                    && !result.HotelIdList.Any(x => x.SupplierApiType is SupplierApiType.None)
                    && !coupon.UserCouponRanges.Any(x => x.ProductType is LimitProductType.ApiHotel)))
                {
                    continue;
                }
                result.Coupons.Add(_mapper.Map<CouponInfo>(coupon));
            }

            var couponIds = result.Coupons.Select(x => x.Id).ToArray();
            var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
            var userCoupons = await GetUserCouponDetails(currentUser.Provider, couponIds);

            foreach (var coupon in result.Coupons)
            {
                coupon.Status = userCoupons.Data.Any(x => x.Id.Equals(coupon.Id) && x.CouponActivityId.Equals(coupon.CouponActivityId))
                    ? UserCouponReceiveStatus.Received : UserCouponReceiveStatus.Unreceived;
            }
        }

        #endregion

        #region 补充酒店政策信息
        if (result.HotelPolicy != null && result.HotelPolicy.HotelExtend != null)
        {
            result.HotelPolicy.HotelMealPolicies.ForEach(x =>
            {
                if (x.BusinessHourInfos != null && x.BusinessHourInfos.Any() is false)
                    x.BusinessHourInfos = null;
            });
            result.HotelPolicy.PetText = result.HotelPolicy.HotelPolicyExtends?
                .FirstOrDefault(x => x.Type == HotelPolicyExtendType.Pet)?.Value;
            result.HotelPolicy.HotelReceptionTip = new HotelReceptionTipsBffOutput
            {
                MixAge = result.HotelPolicy.HotelExtend.MixAge,
                MaxAge = result.HotelPolicy.HotelExtend.MaxAge,
                ApplicabilityType = result.HotelPolicy.HotelExtend.HotelApplicabilityType,
            };

            //加床政策筛选
            var hotelExtraBedPolicies = result.HotelPolicy.HotelExtraBedPolicies;
            if (hotelExtraBedPolicies != null && hotelExtraBedPolicies.Any())
            {
                var hotelExtend = result.HotelPolicy.HotelExtend;
                var hotelRoomExtraBedPolicies = new List<HotelExtraBedPolicyBffOutput>();
                //判断加床政策显示哪个
                var matchHotelRoomExtraBedPolicies = new List<HotelExtraBedPolicyBffOutput>();
                var queryHotelRoomExtraBedPolicies = hotelExtraBedPolicies.Where(x => x.Type == HotelExtraBedCategoryType.Extra);

                if (hotelExtend.HotelExtraBedType == HotelExtraBedSummaryType.All)
                    matchHotelRoomExtraBedPolicies = queryHotelRoomExtraBedPolicies.ToList();
                else if (hotelExtend.HotelExtraBedType == HotelExtraBedSummaryType.Bed
                    || hotelExtend.HotelExtraBedType == HotelExtraBedSummaryType.BedNotChildBed)
                {
                    matchHotelRoomExtraBedPolicies = hotelExtraBedPolicies
                        .Where(x => x.BedType == HotelExtraBedType.Bed || x.BedType == HotelExtraBedType.All)
                        .ToList();
                    matchHotelRoomExtraBedPolicies.ForEach(x =>
                    {
                        x.BedType = HotelExtraBedType.Bed;
                    });
                }
                else if (hotelExtend.HotelExtraBedType == HotelExtraBedSummaryType.ChildBed
                    || hotelExtend.HotelExtraBedType == HotelExtraBedSummaryType.ChildBedNotBed)
                {
                    matchHotelRoomExtraBedPolicies = hotelExtraBedPolicies
                        .Where(x => x.BedType == HotelExtraBedType.ChildBed || x.BedType == HotelExtraBedType.All)
                        .ToList();
                    matchHotelRoomExtraBedPolicies.ForEach(x =>
                    {
                        x.BedType = HotelExtraBedType.ChildBed;
                    });
                }

                if (matchHotelRoomExtraBedPolicies.Any())
                    hotelRoomExtraBedPolicies.AddRange(matchHotelRoomExtraBedPolicies);

                //所有房型可加现有床，才显示
                if (hotelExtend.IsHasChildrenExistingBed.HasValue
                    && hotelExtend.IsHasChildrenExistingBed.Value)
                {
                    var existingPolicies = hotelExtraBedPolicies.Where(x => x.Type == HotelExtraBedCategoryType.Existing).ToList();
                    if (existingPolicies.Any())
                        hotelRoomExtraBedPolicies.AddRange(existingPolicies);
                }
                hotelRoomExtraBedPolicies = hotelRoomExtraBedPolicies
                    .OrderBy(x => x.BedType).ThenBy(x => Math.Abs(x.Min)).ThenBy(x => x.Max)
                    .ToList();
                result.HotelPolicy.HotelExtraBedPolicies = hotelRoomExtraBedPolicies;
            }
            if (isCustomer == false)
                //填充汇率转换
                await fillExchangeAmount(result);
        }
        #endregion

        return result;
    }

    private List<ApiHotelSaleType> MapApiHotelSaleType(List<HotelPriceChannelType> hotelPriceChannelTypes)
    {
        var apiHotelSaleTypes = new List<ApiHotelSaleType>();
        foreach (var item in hotelPriceChannelTypes)
        {
            switch (item)
            {
                case HotelPriceChannelType.Group:
                    apiHotelSaleTypes.Add(ApiHotelSaleType.ReunionRoom);
                    break;
                case HotelPriceChannelType.Calendar:
                    apiHotelSaleTypes.Add(ApiHotelSaleType.CalendarRoom);
                    break;
                case HotelPriceChannelType.BusinessTravel:
                    apiHotelSaleTypes.Add(ApiHotelSaleType.BusinessTravel);
                    break;
                case HotelPriceChannelType.Amap:
                    apiHotelSaleTypes.Add(ApiHotelSaleType.Gaode);
                    break;
                case HotelPriceChannelType.Direct:
                    apiHotelSaleTypes.Add(ApiHotelSaleType.DirectSales);
                    break;
            }
        }

        return apiHotelSaleTypes;
    }
}