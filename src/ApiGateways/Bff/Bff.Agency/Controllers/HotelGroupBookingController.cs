using AutoMapper;
using Bff.Agency.Callers;
using Bff.Agency.Models.HotelGroupBooking;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.GroupBooking;
using Contracts.Common.Order.DTOs.GroupBookingAggregate;
using Contracts.Common.Order.Enums;
using Contracts.Common.User.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Bff.Agency.Controllers;

[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class HotelGroupBookingController : ControllerBase
{
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IUserApiCaller _userApiCaller;
    private readonly IHotelApiCaller _hotelApiCaller;
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly IMapper _mapper;
    private const string _desKey = "hztravel";

    public HotelGroupBookingController(IOrderApiCaller orderApiCaller,
        ITenantApiCaller tenantApiCaller,
        IUserApiCaller userApiCaller,
        IHotelApiCaller hotelApiCaller,
        IResourceApiCaller resourceApiCaller,
        IMapper mapper)
    {
        _orderApiCaller = orderApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _userApiCaller = userApiCaller;
        _hotelApiCaller = hotelApiCaller;
        _resourceApiCaller = resourceApiCaller;
        _mapper = mapper;
    }

    /// <summary>
    /// 团房申请
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [ProducesResponseType(typeof(List<long>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Apply(ApplyInput input)
    {
        var operationUser = GetOperationUser();

        var cities = await _resourceApiCaller.QueryCities(new Contracts.Common.Resource.DTOs.QueryInput
        {
            CityCodes = input.Demands.Select(x => x.CityCode).ToArray()
        });
        ApplicationFormApplyInput applicationFormApplyInput = new()
        {
            TeamNatureType = input.TeamNatureType,
            TeamNatureTitle = input.TeamNatureTitle,
            Demands = input.Demands.Select(x =>
            {
                var city = cities.FirstOrDefault(s => s.CityCode == x.CityCode);
                return new ApplicationDemandInput
                {
                    CountryCode = city?.CountryCode,
                    CountryName = city?.CountryName,
                    ProvinceCode = city?.ProvinceCode,
                    ProvinceName = city?.ProvinceName,
                    CityCode = x.CityCode,
                    CityName = x.CityName,
                    CheckInDate = x.CheckInDate,
                    CheckOutDate = x.CheckOutDate,
                    Hotels = x.Hotels,
                    NeedRecommendHotel = x.NeedRecommendHotel,
                    UnitPrice = x.UnitPrice,
                    CurrencyCode = x.CurrencyCode,
                    AdultNum = x.AdultNum,
                    BedTypeJson = JsonConvert.SerializeObject(x.BedTypes),
                    ChildNum = x.ChildNum,
                    ChildDesc = x.ChildDesc,
                    MeetingBudget = x.MeetingBudget,
                    MeetingRequirement = x.MeetingRequirement,
                    MeetingsNum = x.MeetingsNum,
                    MeetingTimeJson = JsonConvert.SerializeObject(x.MeetingTimes),
                    NeedHotelRoom = x.NeedHotelRoom,
                    OtherRequirement = x.OtherRequirement,
                    NeedMeetingRoom = x.NeedMeetingRoom,
                };
            }).ToList(),
            Applicant = operationUser.Name,
            ApplicantId = operationUser.UserId,
            UserType = operationUser.UserType,
            UserId = operationUser.UserId,
            UserName = operationUser.Name,
            OperationUser = operationUser,
            PromotionTraceId = input.PromotionTraceId,
        };
        switch (operationUser.UserType)
        {
            case UserType.Agency:
                var agency = await _tenantApiCaller.GetAgencyDetail(operationUser.AgencyId!.Value);
                if (agency is not null)
                {
                    applicationFormApplyInput.AgencyId = agency.Id;
                    applicationFormApplyInput.AgencyName = agency.FullName;
                    applicationFormApplyInput.SalespersonId = agency.SalespersonId;
                    applicationFormApplyInput.SalespersonName = agency.SalespersonName;
                    applicationFormApplyInput.CompanyName = agency.FullName;
                }
                var user = await _userApiCaller.GetAgencyUserDetail(operationUser.UserId);
                if (user is not null)
                {
                    applicationFormApplyInput.CountryDialCode = user.CountryDialCode;
                    applicationFormApplyInput.ContactPhone = user.PhoneNumber ?? string.Empty;
                    applicationFormApplyInput.ContactName = user.RealName;
                    applicationFormApplyInput.ContactEmail = user.Email;
                }
                break;
            case UserType.Visitor:
                var contact = await _tenantApiCaller.GetContact(operationUser.UserId);
                if (contact is not null)
                {
                    applicationFormApplyInput.ContactName = contact.Name;
                    applicationFormApplyInput.ContactPhone = contact.PhoneNumber ?? string.Empty;
                    applicationFormApplyInput.ContactEmail = contact.Email;
                    applicationFormApplyInput.CompanyName = contact.CompanyName;
                }
                break;
        }
        var ids = await _orderApiCaller.GroupBookingApplicationFormApply(applicationFormApplyInput);
        return Ok(ids);
    }

    public record AgencyGenerateCodeDto(long AgencyId);

    /// <summary>
    /// 团房申请免登录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<long>), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Tenant.AgencyInvalid)]
    public async Task<IActionResult> ApplyForAgency(ApplyForAgencyInput input)
    {
        long agencyId = 0;
        try
        {
            string code = Common.Utils.SecurityUtil.DESDecrypt(input.AgencyCode, _desKey);

            var agencyGenerate = JsonConvert.DeserializeObject<AgencyGenerateCodeDto>(code);

            agencyId = agencyGenerate?.AgencyId ?? 0;
        }
        catch (Exception ex)
        {
            throw new BusinessException("Code invalid.", ex);
        }

        var agency = await _tenantApiCaller.GetAgencyDetail(agencyId) ?? throw new BusinessException(ErrorTypes.Tenant.AgencyInvalid);

        UserPlatform userPlatform = UserPlatform.None;

        switch (input.RegisterSource)
        {
            case Contracts.Common.Tenant.Enums.AgencyRegisterSourceType.PCWeb:
            case Contracts.Common.Tenant.Enums.AgencyRegisterSourceType.MobileWeb:
                userPlatform = UserPlatform.Web;
                break;
            case Contracts.Common.Tenant.Enums.AgencyRegisterSourceType.WechatApplet:
                userPlatform = UserPlatform.WechatMiniProgram;
                break;
            default:
                break;
        }

        var operationUser = new OperationUserDto
        {
            UserType = UserType.Agency,
            AgencyId = agency.Id,
            UserId = 0,
            Name = string.Empty,
            UserPlatform = userPlatform,
        };

        var cities = await _resourceApiCaller.QueryCities(new Contracts.Common.Resource.DTOs.QueryInput
        {
            CityCodes = input.Demands.Select(x => x.CityCode).ToArray()
        });
        ApplicationFormApplyInput applicationFormApplyInput = new()
        {
            TeamNatureType = input.TeamNatureType,
            TeamNatureTitle = input.TeamNatureTitle,
            Demands = input.Demands.Select(x =>
            {
                var city = cities.FirstOrDefault(s => s.CityCode == x.CityCode);
                return new ApplicationDemandInput
                {
                    CountryCode = city?.CountryCode,
                    CountryName = city?.CountryName,
                    ProvinceCode = city?.ProvinceCode,
                    ProvinceName = city?.ProvinceName,
                    CityCode = x.CityCode,
                    CityName = x.CityName,
                    CheckInDate = x.CheckInDate,
                    CheckOutDate = x.CheckOutDate,
                    Hotels = x.Hotels,
                    NeedRecommendHotel = x.NeedRecommendHotel,
                    UnitPrice = x.UnitPrice,
                    CurrencyCode = x.CurrencyCode,
                    AdultNum = x.AdultNum,
                    BedTypeJson = JsonConvert.SerializeObject(x.BedTypes),
                    ChildNum = x.ChildNum,
                    ChildDesc = x.ChildDesc,
                    MeetingBudget = x.MeetingBudget,
                    MeetingRequirement = x.MeetingRequirement,
                    MeetingsNum = x.MeetingsNum,
                    MeetingTimeJson = JsonConvert.SerializeObject(x.MeetingTimes),
                    NeedHotelRoom = x.NeedHotelRoom,
                    OtherRequirement = x.OtherRequirement,
                    NeedMeetingRoom = x.NeedMeetingRoom,
                };
            }).ToList(),
            Applicant = operationUser.Name,
            ApplicantId = operationUser.UserId,
            UserType = operationUser.UserType,
            UserId = operationUser.UserId,
            UserName = operationUser.Name,
            OperationUser = operationUser,
            PromotionTraceId = input.PromotionTraceId,
            AgencyId = agency.Id,
            AgencyName = agency.FullName,
            SalespersonId = agency.SalespersonId,
            SalespersonName = agency.SalespersonName,
            CompanyName = agency.FullName,
            CountryDialCode = input.CountryDialCode,
            ContactPhone = agency.ContactNumber ?? string.Empty,
            ContactName = agency.Contact,
            ContactEmail = agency.Email
        };

        var ids = await _orderApiCaller.GroupBookingApplicationFormApply(applicationFormApplyInput);
        return Ok(ids);
    }

    /// <summary>
    /// 分销商账号分享申请单提交
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [HttpPost]
    [ProducesResponseType(typeof(List<long>), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.User.VerifyCodeError, ErrorTypes.Order.NoMatchingData)]
    public async Task<IActionResult> ApplyForUser(ApplyForUserInput input)
    {
        // Validate input
        await ContactCheckAsync(input);

        var groupBookingAgencyShare = await _orderApiCaller.GetGroupBookingAgencyShareByCode(input.Code);
        if (groupBookingAgencyShare is null)
        {
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);
        }
        var user = await _userApiCaller.GetAgencyUserDetail(groupBookingAgencyShare.UserId);
        OperationUserDto operationUser = new()
        {
            UserType = UserType.Agency,
            AgencyId = groupBookingAgencyShare.AgencyId,
            UserId = groupBookingAgencyShare.UserId,
            Name = user?.UserName,
            UserPlatform = UserPlatform.Web,
        };

        var cities = await _resourceApiCaller.QueryCities(new Contracts.Common.Resource.DTOs.QueryInput
        {
            CityCodes = input.Demands.Select(x => x.CityCode).ToArray()
        });
        //跟单备注
        var agencyRemark = $"客户信息：公司名：{input.CompanyName}；" +
            $"手机号：[+{input.ContactCountryDialCode}]{input.ContactNumber}";

        ApplicationFormApplyInput applicationFormApplyInput = new()
        {
            TeamNatureType = input.TeamNatureType,
            TeamNatureTitle = input.TeamNatureTitle,
            Demands = input.Demands.Select(x =>
            {
                var city = cities.FirstOrDefault(s => s.CityCode == x.CityCode);
                return new ApplicationDemandInput
                {
                    CountryCode = city?.CountryCode,
                    CountryName = city?.CountryName,
                    ProvinceCode = city?.ProvinceCode,
                    ProvinceName = city?.ProvinceName,
                    CityCode = x.CityCode,
                    CityName = x.CityName,
                    CheckInDate = x.CheckInDate,
                    CheckOutDate = x.CheckOutDate,
                    Hotels = x.Hotels,
                    NeedRecommendHotel = x.NeedRecommendHotel,
                    UnitPrice = x.UnitPrice,
                    CurrencyCode = x.CurrencyCode,
                    AdultNum = x.AdultNum,
                    BedTypeJson = JsonConvert.SerializeObject(x.BedTypes),
                    ChildNum = x.ChildNum,
                    ChildDesc = x.ChildDesc,
                    MeetingBudget = x.MeetingBudget,
                    MeetingRequirement = x.MeetingRequirement,
                    MeetingsNum = x.MeetingsNum,
                    MeetingTimeJson = JsonConvert.SerializeObject(x.MeetingTimes),
                    NeedHotelRoom = x.NeedHotelRoom,
                    OtherRequirement = x.OtherRequirement,
                    NeedMeetingRoom = x.NeedMeetingRoom,
                };
            }).ToList(),
            Applicant = operationUser.Name,
            ApplicantId = operationUser.UserId,
            UserType = operationUser.UserType,
            UserId = operationUser.UserId,
            UserName = operationUser.Name,
            OperationUser = operationUser,
            PromotionTraceId = input.PromotionTraceId,
            AgencyRemark = agencyRemark,
            IsShareOrder = true,
        };
        var agency = await _tenantApiCaller.GetAgencyDetail(operationUser.AgencyId!.Value);
        if (agency is not null)
        {
            applicationFormApplyInput.AgencyId = agency.Id;
            applicationFormApplyInput.AgencyName = agency.FullName;
            applicationFormApplyInput.SalespersonId = agency.SalespersonId;
            applicationFormApplyInput.SalespersonName = agency.SalespersonName;
            applicationFormApplyInput.CompanyName = agency.FullName;
        }

        if (user is not null)
        {
            applicationFormApplyInput.CountryDialCode = user.CountryDialCode;
            applicationFormApplyInput.ContactPhone = user.PhoneNumber ?? string.Empty;
            applicationFormApplyInput.ContactName = user.RealName;
            applicationFormApplyInput.ContactEmail = user.Email;
        }

        var ids = await _orderApiCaller.GroupBookingApplicationFormApply(applicationFormApplyInput);
        return Ok(ids);
    }

    private async Task ContactCheckAsync(ApplyForUserInput input)
    {
        var smsCheck = string.IsNullOrEmpty(input.SmsCode) is false && string.IsNullOrEmpty(input.ContactNumber) is false;

        string chinaDialCode = "86"; // 中国区号

        if (smsCheck && input.ContactCountryDialCode == chinaDialCode)
        {
            var isCorrect = await _userApiCaller.CheckCaptcha(new Contracts.Common.User.DTOs.CaptchaDTO
            {
                TenantId = HttpContext.GetTenantId(),
                Key = input.ContactNumber,
                CaptchaChannelType = CaptchaChannelType.Sms,
                CaptchaType = CaptchaType.AgencyUser_SMS_Register,
                Code = input.SmsCode
            });
            if (isCorrect is false)
                throw new BusinessException(ErrorTypes.User.VerifyCodeError);
        }
        else if (string.IsNullOrEmpty(input.EmailCode) is false && string.IsNullOrEmpty(input.Email) is false)
        {
            var isCorrect = await _userApiCaller.CheckCaptcha(new Contracts.Common.User.DTOs.CaptchaDTO
            {
                TenantId = HttpContext.GetTenantId(),
                Key = input.Email,
                CaptchaChannelType = CaptchaChannelType.Email,
                CaptchaType = CaptchaType.AgencyUser_Email_EmailBingding,
                Code = input.EmailCode
            });
            if (isCorrect is false)
                throw new BusinessException(ErrorTypes.User.VerifyCodeError);
        }
        else
        {
            throw new BusinessException(ErrorTypes.User.VerifyCodeError);
        }
    }

    /// <summary>
    /// 获取分销商团房申请单分享码
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(string), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetShareCode()
    {
        var operationUser = GetOperationUser();
        var result = await _orderApiCaller.GetOrCreateGroupBookingAgencyShare(new Contracts.Common.Order.DTOs.GroupBookingAgencyShare.GetOrCreateInput
        {
            AgencyId = operationUser.AgencyId!.Value,
            UserId = operationUser.UserId,
        });
        return Ok(result.Code);
    }

    /// <summary>
    /// 验证联系方式 (手机/邮箱)和验证码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [HttpPost]
    [ProducesResponseType(typeof(bool), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.User.VerifyCodeError)]
    public async Task VaildContact(VaildContactInput input)
    {
        var checkCodeValue = !string.IsNullOrEmpty(input.CodeValue) && !string.IsNullOrEmpty(input.CodeId);

        //核验验证码  发送手机短信必须验证，邮箱可选验证
        if (input.ContactType is Contracts.Common.Tenant.Enums.TenantUserVaildContactType.Phone || checkCodeValue)
        {
            var isCorrect = await _userApiCaller.CheckImageCaptcha(new Contracts.Common.User.DTOs.ImageCaptchaDto
            {
                CodeId = input.CodeId,
                CodeValue = input.CodeValue,
            });
            if (isCorrect is false)
                throw new BusinessException(ErrorTypes.User.VerifyCodeError);
        }
        //发送验证码
        var captchaInput = new Contracts.Common.User.DTOs.CaptchaDTO
        {
            Key = input.ContactTypeValue,
            TenantId = HttpContext.GetTenantId()
        };
        switch (input.ContactType)
        {
            case Contracts.Common.Tenant.Enums.TenantUserVaildContactType.Phone:
                captchaInput.CaptchaChannelType = CaptchaChannelType.Sms;
                captchaInput.CaptchaType = CaptchaType.AgencyUser_SMS_Register;
                break;
            case Contracts.Common.Tenant.Enums.TenantUserVaildContactType.Email:
                captchaInput.CaptchaChannelType = CaptchaChannelType.Email;
                captchaInput.CaptchaType = CaptchaType.AgencyUser_Email_EmailBingding;
                captchaInput.Variables.Add(new(0, string.Empty));
                break;
        }
        await _userApiCaller.SendCaptcha(captchaInput);
    }

    /// <summary>
    /// 搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [ProducesResponseType(typeof(PagingModel<SearchOutput, List<SearchSupplement>>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        GroupBookingApplicationFormStatus[]? statuses = input.GroupBookingFormPreStatus switch
        {
            GroupBookingFormPreStatus.Submitted => new GroupBookingApplicationFormStatus[] {
                    GroupBookingApplicationFormStatus.NewApplication,
                    GroupBookingApplicationFormStatus.WaitForInquiry,
                    GroupBookingApplicationFormStatus.Inquiried,
                    GroupBookingApplicationFormStatus.UnableQuoted
            },
            GroupBookingFormPreStatus.QuotationWaitforConfirm => new GroupBookingApplicationFormStatus[] {
                    GroupBookingApplicationFormStatus.Quoted,
            },
            GroupBookingFormPreStatus.PreOrderCreating => new GroupBookingApplicationFormStatus[] {
                    GroupBookingApplicationFormStatus.QuotationConfirmed ,
                    GroupBookingApplicationFormStatus.WaitForAuditPreOrder
            },
            GroupBookingFormPreStatus.PreOrderPendingPayment => new GroupBookingApplicationFormStatus[]
            {
                    GroupBookingApplicationFormStatus.PreOrdered
            },
            GroupBookingFormPreStatus.PreOrderConfirmedPayment => new GroupBookingApplicationFormStatus[]
            {
                    GroupBookingApplicationFormStatus.PreOrdered
            },
            GroupBookingFormPreStatus.PreOrderFinished => new GroupBookingApplicationFormStatus[]
            {
                    GroupBookingApplicationFormStatus.PreOrdered
            },
            GroupBookingFormPreStatus.Cancelled => new GroupBookingApplicationFormStatus[]
            {
                    GroupBookingApplicationFormStatus.Cancellation
            },
            _ => null,
        };

        var searchInput = new ApplicationFormSearchInput
        {
            Ids = input.Id.HasValue ? new List<long>() { input.Id.Value } : new List<long>(),
            TenantId = currentUser.Tenant,
            AgencyId = currentUser.Provider,
            UserId = currentUser.IsVisitor ? currentUser.UserId : null,
            BeginDate = input.BeginDate,
            EndDate = input.EndDate,
            PageIndex = input.PageIndex,
            PageSize = input.PageSize,
            Statuses = statuses,
            GroupPreOrderStatus = input.GroupBookingFormPreStatus switch
            {
                GroupBookingFormPreStatus.PreOrderPendingPayment => GroupPreOrderStatus.Unpaid,
                GroupBookingFormPreStatus.PreOrderConfirmedPayment => GroupPreOrderStatus.Paid,
                GroupBookingFormPreStatus.PreOrderFinished => GroupPreOrderStatus.Finished,
                _ => null,
            }
        };
        var result = await _orderApiCaller.GroupBookingApplicationFormSearch(searchInput);

        List<SearchSupplement> supplement = new()
        {
            new SearchSupplement()
            {
                Status= GroupBookingFormPreStatus.Submitted,
                Count=result.Supplement.GroupBookingApplicationFormStatusSupplements
                 .Where(x=>x.Status is GroupBookingApplicationFormStatus.NewApplication
                    or GroupBookingApplicationFormStatus.WaitForInquiry
                    or GroupBookingApplicationFormStatus.Inquiried
                    or GroupBookingApplicationFormStatus.UnableQuoted)
                 .Sum(s=>s.Count)
            },
            new SearchSupplement()
            {
                Status= GroupBookingFormPreStatus.QuotationWaitforConfirm,
                Count=result.Supplement.GroupBookingApplicationFormStatusSupplements
                 .Where(x=>x.Status is GroupBookingApplicationFormStatus.Quoted)
                 .Sum(s=>s.Count)
            },
            new SearchSupplement()
            {
                Status= GroupBookingFormPreStatus.PreOrderCreating,
                Count=result.Supplement.GroupBookingApplicationFormStatusSupplements
                 .Where(x=>x.Status is GroupBookingApplicationFormStatus.QuotationConfirmed or GroupBookingApplicationFormStatus.WaitForAuditPreOrder)
                 .Sum(s=>s.Count)
            },
            new SearchSupplement()
            {
                Status= GroupBookingFormPreStatus.PreOrderPendingPayment,
                Count=result.Supplement.GroupPreOrderStatusSupplements
                 .Where(x=>x.Status== GroupBookingApplicationFormStatus.PreOrdered && x.GroupPreOrderStatus is  GroupPreOrderStatus.Unpaid)
                 .Sum(s=>s.Count)
            },
            new SearchSupplement()
            {
                Status= GroupBookingFormPreStatus.PreOrderConfirmedPayment,
                Count=result.Supplement.GroupPreOrderStatusSupplements
                 .Where(x=>x.Status== GroupBookingApplicationFormStatus.PreOrdered && x.GroupPreOrderStatus is  GroupPreOrderStatus.Paid)
                 .Sum(s=>s.Count)
            },
            new SearchSupplement()
            {
                Status= GroupBookingFormPreStatus.PreOrderFinished,
                Count=result.Supplement.GroupPreOrderStatusSupplements
                 .Where(x=>x.Status== GroupBookingApplicationFormStatus.PreOrdered && x.GroupPreOrderStatus is  GroupPreOrderStatus.Finished)
                 .Sum(s=>s.Count)
            },
            new SearchSupplement()
            {
                Status= GroupBookingFormPreStatus.Cancelled,
                Count=result.Supplement.GroupBookingApplicationFormStatusSupplements
                 .Where(x=>x.Status is GroupBookingApplicationFormStatus.Cancellation)
                 .Sum(s=>s.Count)
            },
        };

        PagingModel<SearchOutput, List<SearchSupplement>> output = new()
        {
            PageIndex = result.PageIndex,
            PageSize = result.PageSize,
            Total = result.Total,
            Supplement = supplement
        };
        var demandHotels = result.Data.Where(s => s.ApplicationDemand is not null)
            .Select(s => new { s.ApplicationDemand.ResourceHotelId, CityCode = s.ApplicationDemand.CityCode })
            .ToList();
        var hotelIds = demandHotels.Where(s => s.ResourceHotelId.HasValue).Select(s => s.ResourceHotelId!.Value).Distinct().ToArray();
        var hotels = hotelIds.Length > 0 ? (await _resourceApiCaller.GetHotelDetailByHotelIds(hotelIds)) : new();
        var cityCodes = demandHotels.Where(s => s.CityCode.HasValue).Select(s => s.CityCode!.Value).Distinct().ToArray();
        var cities = cityCodes.Length > 0 ? (await _resourceApiCaller.QueryCities(new Contracts.Common.Resource.DTOs.QueryInput { CityCodes = cityCodes }))
        : Enumerable.Empty<Contracts.Common.Resource.DTOs.CityOutput>();

        var formIds = result.Data.Select(x => x.Id).Distinct().ToArray();
        var operationLogs = await GetGroupBookingOperationLogs(formIds);

        output.Data = result.Data
            .GroupBy(d => new
            {
                d.Id,
                d.ApplicantId,
                d.Applicant,
                d.ApplicationTime,
                d.FinishTime,
                d.Status,
                d.GroupBookingFormPreOrderStatus,
                d.GroupBookingOrderCount,
                d.GroupBookingPreOrderId,
                d.GroupPreOrderStatus,
                d.GroupBookingOrderId,
                d.HotelOrderStatus,
                d.HotelOrderGuestFilePath,
                d.GuestAddable,
                d.ContractSigned,
            })
            .Select(g =>
            {
                var d = g.Key;
                var searchOutput = new SearchOutput
                {
                    Id = d.Id,
                    ApplicantId = d.ApplicantId,
                    Applicant = d.Applicant,
                    ApplicationTime = d.ApplicationTime,
                    FinishTime = d.FinishTime,
                    Status = d.Status,
                    ContractSigned = d.ContractSigned,
                    GroupBookingPreOrderId = d.GroupBookingPreOrderId,
                    GroupPreOrderStatus = d.GroupPreOrderStatus,
                    GroupBookingOrderId = d.GroupBookingOrderId,
                    HotelOrderStatus = d.HotelOrderStatus,
                    HotelOrderGuestFilePath = d.HotelOrderGuestFilePath,
                    GuestAddable = d.GuestAddable,
                    GroupBookingFormPreOrderStatus = d.GroupBookingFormPreOrderStatus,
                    GroupBookingOrderCount = d.GroupBookingOrderCount,
                    Demands = g.Select(x => x.ApplicationDemand).Select(d =>
                    {
                        SearchDemandOutput demandOutput = _mapper.Map<SearchDemandOutput>(d);
                        demandOutput.EnHotelName = hotels.FirstOrDefault(h => h.Id == d.ResourceHotelId)?.ENName;
                        var city = cities.FirstOrDefault(c => c.CityCode == d.CityCode);
                        if (city is not null)
                        {
                            demandOutput.EnCityName = city.ENName;
                            demandOutput.EnProvinceName = city.ProvinceEnName;
                            demandOutput.EnCountryName = city.CountryEnName;
                        }
                        return demandOutput;
                    }).ToList(),//_mapper.Map<List<SearchDemandOutput>>(g.Select(x => x.ApplicationDemand)),
                    BaseOrderIds = g.First().BaseOrderIds,
                };
                //商户 确认申请 不显示
                var logs = operationLogs.Where(s => s.ApplicationFormId == d.Id && s.OperationType != GrouBookingOperationType.ApplicationConfirmed)
                    .OrderByDescending(x => x.CreateTime);
                searchOutput.LatestOperationLog = logs.FirstOrDefault();
                searchOutput.GroupBookingFormPreStatus = GetGroupBookingFormPreStatus(d.Status, d.GroupPreOrderStatus);
                return searchOutput;
            });

        return Ok(output);
    }

    private static GroupBookingFormPreStatus? GetGroupBookingFormPreStatus(GroupBookingApplicationFormStatus formStatus, GroupPreOrderStatus? preOrderStatus)
    {
        GroupBookingFormPreStatus? groupBookingFormPreStatus = default;
        switch (formStatus)
        {
            case GroupBookingApplicationFormStatus.NewApplication:
            case GroupBookingApplicationFormStatus.WaitForInquiry:
            case GroupBookingApplicationFormStatus.Inquiried:
            case GroupBookingApplicationFormStatus.UnableQuoted:
                groupBookingFormPreStatus = GroupBookingFormPreStatus.Submitted;
                break;
            case GroupBookingApplicationFormStatus.Quoted:
                groupBookingFormPreStatus = GroupBookingFormPreStatus.QuotationWaitforConfirm;
                break;
            case GroupBookingApplicationFormStatus.QuotationConfirmed:
            case GroupBookingApplicationFormStatus.WaitForAuditPreOrder:
                groupBookingFormPreStatus = GroupBookingFormPreStatus.PreOrderCreating;
                break;
            case GroupBookingApplicationFormStatus.PreOrdered:
                groupBookingFormPreStatus = preOrderStatus switch
                {
                    GroupPreOrderStatus.Unpaid => GroupBookingFormPreStatus.PreOrderPendingPayment,
                    GroupPreOrderStatus.Paid => GroupBookingFormPreStatus.PreOrderConfirmedPayment,
                    GroupPreOrderStatus.Finished => GroupBookingFormPreStatus.PreOrderFinished,
                    _ => GroupBookingFormPreStatus.PreOrderPendingPayment
                };
                break;
            case GroupBookingApplicationFormStatus.Cancellation:
                groupBookingFormPreStatus = GroupBookingFormPreStatus.Cancelled;
                break;
        }
        return groupBookingFormPreStatus;
    }

    /// <summary>
    /// 申请单信息
    /// </summary>
    /// <param name="applicationFormId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [ProducesResponseType(typeof(FormOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetApplicationForm(long applicationFormId)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        GetApplicationFormInput getApplicationFormInput = new()
        {
            Id = applicationFormId,
            TenantId = currentUser.Tenant,
            AgencyId = currentUser.Provider,
            UserId = currentUser.IsVisitor ? currentUser.UserId : null,
        };
        var form = await _orderApiCaller.GetApplicationForm(getApplicationFormInput);

        var result = _mapper.Map<FormOutput>(form);
        if (result is not null)
        {
            result.GroupBookingFormPreStatus = GetGroupBookingFormPreStatus(form.Status, form.GroupPreOrderStatus);
        }
        return Ok(result);
    }

    /// <summary>
    /// 编辑修改申请单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Modify(ModifyInput input)
    {
        var operationUser = GetOperationUser();
        var cities = await _resourceApiCaller.QueryCities(new Contracts.Common.Resource.DTOs.QueryInput
        {
            CityCodes = input.Demands.Select(x => x.CityCode).ToArray()
        });
        await _orderApiCaller.GroupBookingApplicationFormModify(new ApplicationFormModifyInput
        {
            Id = input.Id,
            TeamNatureType = input.TeamNatureType,
            TeamNatureTitle = input.TeamNatureTitle,
            Demands = input.Demands.Select(x =>
            {
                var dto = _mapper.Map<ApplicationDemandDto>(x);
                var city = cities.FirstOrDefault(s => s.CityCode == x.CityCode);
                dto.CountryCode = city?.CountryCode;
                dto.CountryName = city?.CountryName;
                dto.ProvinceCode = city?.ProvinceCode;
                dto.ProvinceName = city?.ProvinceName;
                return dto;
            }).ToList(),
            OperationUser = operationUser,
            UserId = input.UserId,
            UserName = input.UserName,
            AgencyId = input.AgencyId,
            AgencyName = input.AgencyName,
        });
        return Ok();
    }

    /// <summary>
    /// 取消 申请单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Cancel(CancelInput input)
    {
        var operationUser = GetOperationUser();
        await _orderApiCaller.GroupBookingApplicationFormStatusModify(new ApplicationFormStatusModifyInput
        {
            ApplicationFormId = input.ApplicationFormId,
            LimitStatuses = new[]
            {
                GroupBookingApplicationFormStatus.NewApplication,
                GroupBookingApplicationFormStatus.WaitForInquiry,
                GroupBookingApplicationFormStatus.Inquiried
            },
            Status = GroupBookingApplicationFormStatus.Cancellation,
            OperationType = GrouBookingOperationType.Cancellation,
            OperationUser = operationUser
        });
        return Ok();
    }


    /// <summary>
    /// 查询操作记录
    /// </summary>
    /// <param name="applicationFormId"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [ProducesResponseType(typeof(IEnumerable<OperationLogOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetOperationLogs(long applicationFormId)
    {
        var result = await GetGroupBookingOperationLogs(applicationFormId);
        return Ok(result);
    }

    private async Task<List<OperationLogOutput>> GetGroupBookingOperationLogs(params long[] applicationFormIds)
    {
        var result = (await _orderApiCaller.GetGroupBookingOperationLogs(new GetOperationLogInput
        {
            ApplicationFormIds = applicationFormIds,
            UserTypes = new[] { UserType.Agency, UserType.Visitor, UserType.Merchant }
        })).ToList();
        //不展示 商户编辑操作 新询单记录
        result.RemoveAll(s => s.OperationType == GrouBookingOperationType.NewApplication && s.UserType == UserType.Merchant);
        return result;
    }

    /// <summary>
    /// 查询关联报价单 状态为已报价 查询报价信息
    /// </summary>
    /// <param name="applicationFormId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [ProducesResponseType(typeof(IEnumerable<GetQuotationOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetQuotations(long applicationFormId)
    {
        var result = await _orderApiCaller.GetGroupBookingQuotations(applicationFormId);
        var quotations = result.Select(x => new GetQuotationOutput
        {
            Id = x.Id,
            IsConfirmed = x.IsConfirmed,
            QuotationItems = x.QuotationItems.Select(s => new GetQuotationItemDto
            {
                AveragePrice = s.AveragePrice,
                BedTypeName = s.BedTypeName,
                Breakfast = s.Breakfast,
                CancellationPolicy = s.CancellationPolicy,
                CheckIn = s.CheckIn,
                CheckOut = s.CheckOut,
                ChildPolicy = s.ChildPolicy,
                CityCode = s.CityCode,
                CityName = s.CityName,
                CurrencyCode = s.CurrencyCode,
                CustomerComments = s.CustomerComments,
                HotelName = s.HotelName,
                OfferValidity = s.OfferValidity,
                OtherExpensesDesc = s.OtherExpensesDesc,
                PaymentPolicy = s.PaymentPolicy,
                RoomCount = s.RoomCount,
                RoomName = s.RoomName,
                Additions = s.Additions.Select(x => new GetQuotationAdditionDto
                {
                    Remark = x.Remark,
                    AdditionName = x.AdditionName,
                    Amount = x.Amount,
                    CurrencyCode = x.CurrencyCode,
                    Quantity = x.Quantity,
                }).ToList(),
            })
        });
        return Ok(quotations);
    }

    /// <summary>
    /// 确认报价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> ConfirmQoutation(ConfirmQoutationInput input)
    {
        await _orderApiCaller.GroupBookingQuotationConfirm(new QuotationConfirmInput
        {
            ApplicationFormId = input.ApplicationFormId,
            GroupBookingQuotationId = input.GroupBookingQuotationId,
            OperationUser = GetOperationUser()
        });
        return Ok();
    }

    /// <summary>
    /// 查询关联预订单 状态为预订单 加载预订单信息
    /// </summary>
    /// <param name="applicationFormId">申请单id</param>
    /// <param name="preOrderId">预订单id 可选 指定预订单必传</param>
    /// <returns></returns>
    [HttpGet]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [ProducesResponseType(typeof(List<GetPreOrderOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetPreOrders(long applicationFormId, long? preOrderId)
    {
        var result = await _orderApiCaller.GetGroupBookingPreOrders(applicationFormId, preOrderId);
        var preOrders = _mapper.Map<List<GetPreOrderOutput>>(result);
        foreach (var preOrder in preOrders)
        {
            var items = preOrder.PreOrderItems;
            foreach (var g in items.GroupBy(x => x.HotelId))
            {
                var adds = g.Where(x => x.PreOrderAdditions?.Any() is true).FirstOrDefault()?.PreOrderAdditions;
                if (adds?.Any() is true)
                {
                    foreach (var item in g)
                    {
                        item.PreOrderAdditions = adds;
                    }
                }
            }
        }
        return Ok(preOrders);
    }

    /// <summary>
    /// 获取预订单设置
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [ProducesResponseType(typeof(GroupBookingPreOrderSettingDto), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetPreOrderSetting()
    {
        var result = await _orderApiCaller.GetGroupBookingPreOrderSetting();
        return Ok(result);
    }

    private OperationUserDto GetOperationUser()
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        Enum.TryParse<UserPlatform>(currentUser.Client, out UserPlatform platform);
        OperationUserDto operationUser = new()
        {
            UserType = !currentUser.IsVisitor ? UserType.Agency : UserType.Visitor,
            AgencyId = currentUser.Provider,
            UserId = currentUser.UserId,
            Name = currentUser.NickName,
            UserPlatform = platform,
        };
        return operationUser;
    }

    /// <summary>
    /// 下载查询申请单报价预订单信息
    /// </summary>
    /// <param name="applicationFormId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [ProducesResponseType(typeof(ApplicationFormDetailDto), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetApplicationFormDetail(long applicationFormId, long preOrderId)
    {
        ApplicationFormDetailDto output = new();
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        if (!currentUser.IsVisitor)
        {
            var agencyId = currentUser.Provider;
            var agency = await _tenantApiCaller.GetAgencyDetail(agencyId);
            long? salespersonId = agency.SalespersonId;
            if (salespersonId is > 0)
            {
                var tenantUser = await _userApiCaller.TenantUserFindOne(new Contracts.Common.User.DTOs.SupplierUser.FindOneInput
                {
                    UserId = salespersonId.Value,
                });
                if (tenantUser is not null)
                {
                    output.Salesperson = new SalespersonDto
                    {
                        Name = tenantUser.Name,
                        NickName = tenantUser.NickName,
                        PhoneNumber = tenantUser.PhoneNumber,
                    };
                }
            }
        }
        var applicationForm = await _orderApiCaller.GetApplicationForm(new GetApplicationFormInput
        {
            Id = applicationFormId,
            TenantId = currentUser.Tenant,
            AgencyId = currentUser.Provider,
            UserId = currentUser.IsVisitor ? currentUser.UserId : null,
        });
        output.ApplicationForm = applicationForm;
        var groupBookingPreOrder = (await _orderApiCaller.GetGroupBookingPreOrders(applicationFormId, preOrderId)).FirstOrDefault();
        var preOrder = _mapper.Map<GetPreOrderOutput>(groupBookingPreOrder);
        var hotelIds = preOrder.PreOrderItems.Select(x => x.HotelId).ToArray();
        var hotels = await _hotelApiCaller.GetApiHotelDetail(hotelIds);
        var resourceHotelIds = hotels.Select(x => x.ResourceHotelId).Distinct().ToArray();
        var hotelPhotos = await _resourceApiCaller.GetHotelFirstPhoto(resourceHotelIds.ToArray());
        var hotelRooms = await GetHotelRooms(resourceHotelIds);
        output.PreOrder = new PreOrderOutDto
        {
            Id = preOrder.Id,
            QuotationId = preOrder.QuotationId,
            FinalPaymentTime = preOrder.FinalPaymentTime,
            CancellationPolicy = preOrder.CancellationPolicy,
            HotelOrderStatus = preOrder.HotelOrderStatus,
            GroupBookingFormPreOrderStatus = preOrder.GroupBookingFormPreOrderStatus,
            GroupBookingOrderId = preOrder.GroupBookingOrderId,
            TotalAmount = preOrder.TotalAmount,
            FinalPaymentAmount = preOrder.FinalPaymentAmount,
            InitialPaymentAmount = preOrder.InitialPaymentAmount,
            InitialPaymentRatio = preOrder.InitialPaymentRatio,
            RemindAdvanceDays = preOrder.RemindAdvanceDays,
            SpecialRemarks = preOrder.SpecialRemarks,
            ValidityPeriod = preOrder.ValidityPeriod,
            PreOrderItems = preOrder.PreOrderItems,
            HotelPreOrders = preOrder.PreOrderItems.GroupBy(x => new { x.HotelId, x.SupplierApiType })
            .Select(x =>
            {
                var hotel = hotels.FirstOrDefault(s => s.Id == x.Key.HotelId);
                return new HotelPreOrderOutDto
                {
                    HotelId = x.Key.HotelId,
                    SupplierApiType = x.Key.SupplierApiType,
                    CityCode = hotel?.CityCode ?? 0,
                    CityName = hotel?.CityName,
                    HotelZHName = hotel?.ZHName,
                    HotelENName = hotel?.ENName,
                    ChildPolicy = x.FirstOrDefault().ChildPolicy,
                    PreOrderAdditions = x.SelectMany(x => x.PreOrderAdditions)
                    .Select(a => new PreOrderAdditionOutDto
                    {
                        AdditionName = a.AdditionName,
                        Amount = a.Amount,
                        Quantity = a.Quantity,
                        Remark = a.Remark,
                        CurrencyCode = a.CurrencyCode,
                    }),
                    PreOrderItems = x.Select(s =>
                    {
                        var hotelRoom = hotelRooms.FirstOrDefault(r => r.Id == s.HotelRoomId);
                        return new PreOrderItemOutDto
                        {
                            Id = s.Id,
                            BedType = s.BedType,
                            CheckIn = s.CheckIn,
                            CheckOut = s.CheckOut,
                            CurrencyCode = s.CurrencyCode,
                            HotelRoomId = s.HotelRoomId,
                            HotelRoomZHName = hotelRoom?.ZHName ?? s.HotelRoomName,
                            HotelRoomENName = hotelRoom?.ENName,
                            NightCount = s.NightCount,
                            NightlyPrices = s.NightlyPrices,
                            NumberOfBreakfast = s.NumberOfBreakfast,
                            Price = s.Price,
                            PriceStrategyId = s.PriceStrategyId,
                            PriceStrategyName = s.PriceStrategyName,
                            RoomCount = s.RoomCount,
                        };
                    })
                };
            }).ToList()
        };
        return Ok(output);
    }

    /// <summary>
    /// 查询报价预订单信息 预订单提交页数据
    /// </summary>
    /// <param name="applicationFormId"></param>
    /// <param name="preOrderId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [ProducesResponseType(typeof(QuotationPreOrderOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetQuotationPreOrder(long applicationFormId, long preOrderId)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var applicationForm = await _orderApiCaller.GetApplicationForm(new GetApplicationFormInput
        {
            Id = applicationFormId,
            TenantId = currentUser.Tenant,
            AgencyId = currentUser.Provider,
            UserId = currentUser.IsVisitor ? currentUser.UserId : null,
        });
        var preOrder = (await _orderApiCaller.GetGroupBookingPreOrders(applicationFormId, preOrderId))
            .FirstOrDefault(x => x.Id == preOrderId);
        var hotelIds = preOrder.PreOrderItems.Select(x => x.HotelId).ToArray();
        var hotels = await _hotelApiCaller.GetApiHotelDetail(hotelIds);
        var resourceHotelIds = hotels.Select(x => x.ResourceHotelId).Distinct().ToArray();
        var hotelPhotos = await _resourceApiCaller.GetHotelFirstPhoto(resourceHotelIds.ToArray());
        var hotelRooms = await GetHotelRooms(resourceHotelIds);
        QuotationPreOrderOutput output = new()
        {
            Id = preOrder.Id,
            QuotationId = preOrder.QuotationId,
            CancellationPolicy = preOrder.CancellationPolicy,
            SpecialRemarks = preOrder.SpecialRemarks,
            FinalPaymentTime = preOrder.FinalPaymentTime,
            ValidityPeriod = preOrder.ValidityPeriod,
            InitialPaymentRatio = preOrder.InitialPaymentRatio,
            RemindAdvanceDays = preOrder.RemindAdvanceDays,
            TotalAmount = preOrder.TotalAmount,
            InitialPaymentAmount = preOrder.InitialPaymentAmount,
            FinalPaymentAmount = preOrder.FinalPaymentAmount,
            HotelPreOrders = preOrder.PreOrderItems.GroupBy(x => new { x.HotelId, x.SupplierApiType })
                .Select(g =>
                {
                    var hotel = hotels.FirstOrDefault(s => s.Id == g.Key.HotelId);
                    var hotelPhoto = hotelPhotos.FirstOrDefault(s => s.HotelId == hotel.ResourceHotelId);
                    HotelPreOrderOutput hotelPreOrderOutput = new()
                    {
                        HotelId = g.Key.HotelId,
                        SupplierApiType = g.Key.SupplierApiType,
                        CityCode = hotel.CityCode,
                        CityName = hotel.CityName,
                        HotelZHName = hotel.ZHName,
                        HotelENName = hotel.ENName,
                        HotelPhotoPath = hotelPhoto?.Path,
                        PreOrderAdditions = g.SelectMany(a => a.PreOrderAdditions).Select(x =>
                            new PreOrderAdditionOutput
                            {
                                AdditionName = x.AdditionName,
                                Amount = x.Amount,
                                CurrencyCode = x.CurrencyCode,
                                Quantity = x.Quantity,
                                Remark = x.Remark
                            }),
                        PreOrderItems = g.Select(x =>
                        {
                            PreOrderItemOutput preOrderItemOutput = new()
                            {
                                Id = x.Id ?? 0,
                                NightlyPrices = x.NightlyPrices,
                                CheckIn = x.CheckIn,
                                CheckOut = x.CheckOut,
                                ChildPolicy = x.ChildPolicy,
                                HotelRoomId = x.HotelRoomId,
                                HotelRoomName = x.HotelRoomName,
                                PriceStrategyId = x.PriceStrategyId,
                                PriceStrategyName = x.PriceStrategyName,
                                RoomCount = x.RoomCount,
                                NightCount = x.NightCount,
                                NumberOfBreakfast = x.NumberOfBreakfast,
                                Price = x.Price,
                                CurrencyCode = x.CurrencyCode,
                                HotelRoom = hotelRooms.FirstOrDefault(s => s.Id == x.HotelRoomId)
                            };
                            return preOrderItemOutput;
                        }).ToList()
                    };

                    return hotelPreOrderOutput;
                })
        };
        return Ok(output);
    }

    private async Task<IEnumerable<HotelRoomDto>> GetHotelRooms(long[] resourceHotelIds)
    {
        var thirdRooms = await _resourceApiCaller.GetRoomsByHotelIds(new Contracts.Common.Resource.DTOs.GetRoomsByHotelIdsInput
        {
            HotelIds = resourceHotelIds.ToList()
        });
        return thirdRooms.SelectMany(x => x.Rooms).Select(x =>
        {
            HotelRoomDto dto = new()
            {
                Id = x.Id,
                ZHName = x.ZHName,
                ENName = x.ENName,
                AreaMin = x.AreaMin,
                AreaMax = x.AreaMax,
                FloorMin = x.FloorMin,
                FloorMax = x.FloorMax,
                MaximumOccupancy = x.MaximumOccupancy
            };
            if (!string.IsNullOrWhiteSpace(x.BedType))
            {
                var bedTypes = JsonConvert.DeserializeObject<List<BedType>>(x.BedType);
                dto.BedType = bedTypes;
            }
            return dto;
        });
    }

    /// <summary>
    /// 团房订单支付尾款前 签署合同
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    public async Task<IActionResult> SignContract(SignContractInput input)
    {
        var operationUser = GetOperationUser();
        FormSignContractInput signContractInput = new()
        {
            ApplicationFormId = input.ApplicationFormId,
            AgencyId = operationUser.AgencyId,
            OperationUser = operationUser
        };
        await _orderApiCaller.GroupBookingSignContract(signContractInput);
        return Ok();
    }

    /// <summary>
    /// 获取团房订单区域历史状态统计时间
    /// </summary>
    /// <param name="applicationFormId"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(List<BffGetGroupBookingStatusTimeOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetGroupBookingStatusTime(List<long> applicationFormIds)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var searchInput = new ApplicationFormSearchInput
        {
            Ids = applicationFormIds,
            TenantId = currentUser.Tenant,
            AgencyId = currentUser.Provider,
            UserId = currentUser.IsVisitor ? currentUser.UserId : null,
            PageIndex = 1,
            PageSize = applicationFormIds.Count,
        };
        var applicationFormRes = await _orderApiCaller.GroupBookingApplicationFormSearch(searchInput);

        var areas = await _orderApiCaller.GetGroupBookingAreaSetting();
        Dictionary<long, List<long>> dic = new Dictionary<long, List<long>>();
        //var areaIds = new List<long>();
        // 参考 search 方法
        var applicationForms = applicationFormRes.Data
            .GroupBy(d => new
            {
                d.Id,
            })
            .Select(g =>
            {
                var d = g.Key;
                var searchOutput = new SearchOutput
                {
                    Id = d.Id,
                    Demands = g.Select(x => x.ApplicationDemand).Select(h =>
                    {
                        SearchDemandOutput demandOutput = _mapper.Map<SearchDemandOutput>(h);

                        return demandOutput;
                    }).ToList(),//_mapper.Map<List<SearchDemandOutput>>(g.Select(x => x.ApplicationDemand)),
                    BaseOrderIds = g.First().BaseOrderIds,
                };
                return searchOutput;
            });

        foreach (var applicationForm in applicationForms)
        {
            var areaIds = new List<long>();
            foreach (var demand in applicationForm.Demands)
            {
                long areaId = 0;
                foreach (var area in areas.Settings)
                {
                    if (area.Type == GroupBookingAreaSettingType.China)
                    {
                        if (area.Details.Any(x => x.CountryCode == demand.CountryCode && x.ProvinceCode == demand.ProvinceCode))
                        {
                            areaId = area.Id!.Value;
                            break;
                        }
                    }
                    else
                    {
                        if (area.Details.Any(x => x.CountryCode == demand.CountryCode))
                        {
                            areaId = area.Id!.Value;
                            break;
                        }
                    }
                }
                if (areaId == 0)
                {
                    areaId = areas.Settings.FirstOrDefault(x => x.Type == GroupBookingAreaSettingType.Other)?.Id ?? 0;
                }
                areaIds.Add(areaId);
            }
            dic.Add(applicationForm.Id, areaIds);
        }
        var areaAllIds = dic.SelectMany(x => x.Value).Where(x => x > 0).Distinct().ToList();
        // 因为统计数据只统计到昨天，所以这里获取昨天的日期作为结束日期
        var date = DateTime.Now.AddDays(-1).Date;
        var allData = new Dictionary<long, List<GetStatusStatisticsOutput>>();
        // 遍历每个区域ID，获取对应的统计数据,因为是获取每个区域的平均时间，所以需要分别获取每个区域的统计数据
        foreach (var id in areaAllIds)
        {
            var input = new GetStatusStatisticsInput()
            {
                StartDate = date.AddDays(-30),
                EndDate = date,
                DimensionType = StatisticalDimensionType.Area,
                AreaIds = new List<long>() { id },
            };
            var apiData = await _orderApiCaller.GetGroupBookingAggregateStatusStatistics(input);
            allData.Add(id, apiData);
        }
        var formIds = applicationForms.Select(x => x.Id).Distinct().ToArray();
        var operationLogs = (await _orderApiCaller.GetGroupBookingOperationLogs(new GetOperationLogInput
        {
            ApplicationFormIds = formIds,
        })).ToList();
        var res = new List<BffGetGroupBookingStatusTimeOutput>();
        foreach (var key in dic.Keys)
        {
            var itemAreaIds = dic[key];
            // 因为 vebk 统计数据的显示状态时间数据是上移了的，所以这里使用对应的实际状态
            var statuss = new List<StatisticsFunnelStatus>() { StatisticsFunnelStatus.HotelRecovery,
                                       StatisticsFunnelStatus.Inquiried, StatisticsFunnelStatus.Quoted,
                                 };
            var data = allData.Where(x => itemAreaIds.Contains(x.Key)).SelectMany(x => x.Value).ToList();
            var communicateVerifyData = data.Where(x => statuss.Contains(x.Status));
            var confirmQuotationData = data.Where(x => x.Status == StatisticsFunnelStatus.WaitForAuditPreOrder
                                                      || x.Status == StatisticsFunnelStatus.PreOrdered);
            var totalCommunicateVerifyTimes = communicateVerifyData.Where(x => x.AvgTime > 0).Sum(x => x.AvgTime) ?? 0;
            var totalConfirmQuotationTimes = confirmQuotationData.Where(x => x.AvgTime > 0).Sum(x => x.AvgTime) ?? 0;
            var areaCount = itemAreaIds.Count;
            var output = new BffGetGroupBookingStatusTimeOutput();
            // 计算区域平均时间
            if (totalCommunicateVerifyTimes > 0)
            {
                output.CommunicateVerifyTimes = (int)Math.Ceiling(totalCommunicateVerifyTimes / areaCount / 60);
            }
            if (data.Where(x => x.Status == StatisticsFunnelStatus.WaitForInquiry
                      || x.Status == StatisticsFunnelStatus.HotelRecovery
                      || x.Status == StatisticsFunnelStatus.Inquiried // 因为只是时间上移，但是其它没有变化
                        ).Any(x => x.Count == 0)) // 如果其中一个状态没有统计数据，则使用默认值 3 天
            {
                output.CommunicateVerifyTimes = 3 * 24;
            }
            if (output.CommunicateVerifyTimes < 1)
            {
                output.CommunicateVerifyTimes = 1; // 最小值为 1 小时
            }
            if (totalConfirmQuotationTimes > 0)
            {
                output.ConfirmQuotationTimes = (int)Math.Ceiling(totalConfirmQuotationTimes / areaCount / 60);
            }
            if (data.Where(x => x.Status == StatisticsFunnelStatus.QuotationConfirmed  // 因为只是时间上移，但是其它没有变化
                               || x.Status == StatisticsFunnelStatus.WaitForAuditPreOrder).Any(x => x.Count == 0))
            // 如果其中一个状态没有统计数据，则使用默认值 1 天
            {
                output.ConfirmQuotationTimes = 1 * 24;
            }
            if (output.ConfirmQuotationTimes < 1)
            {
                output.ConfirmQuotationTimes = 1; // 最小值为 1 小时
            }
            output.ApplicationFormId = key;
            // 待询价时间
            var waitForInquiryTime = GetStatusTime(operationLogs, key,
                                                    GroupBookingApplicationFormStatus.WaitForInquiry,
                                                    new List<GrouBookingOperationType>() {
                                                      GrouBookingOperationType.ApplicationConfirmed,
                                                      GrouBookingOperationType.ApplicationStatusModify,
                                                    });
            if (waitForInquiryTime.HasValue)
                output.CommunicateVerifyUtcTime = waitForInquiryTime!.Value
                                                    .AddHours(output.CommunicateVerifyTimes)
                                                    .ToUniversalTime();

            var quotationConfirmedTime = GetStatusTime(operationLogs,
                                                key,
                                                GroupBookingApplicationFormStatus.QuotationConfirmed,
                                                new List<GrouBookingOperationType>() {
                                                      GrouBookingOperationType.QuotationConfirmed,
                                                      GrouBookingOperationType.ApplicationStatusModify,
                                                    });
            if (quotationConfirmedTime.HasValue)
                output.ConfirmQuotationUtcTime = quotationConfirmedTime!.Value
                                                  .AddHours(output.ConfirmQuotationTimes)
                                                  .ToUniversalTime();

            res.Add(output);
        }
        return Ok(res);
    }

    private DateTime? GetStatusTime(List<OperationLogOutput> logs,
          long applicationFormId,
          GroupBookingApplicationFormStatus status,
          List<GrouBookingOperationType> operationTypes)
    {
        var statuslogs = logs
            .Where(x => x.ApplicationFormId == applicationFormId
            && x.Status == status
            && operationTypes.Contains(x.OperationType));
        // 指定状态的转变操作，排除在当前状态时的其它操作,或者回滚
        var log = statuslogs.OrderByDescending(x => x.CreateTime).FirstOrDefault();
        return log?.CreateTime;
    }

    /// <summary>
    /// 获取申请单跟单备注列表
    /// </summary>
    /// <param name="applicationFormId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(List<GroupBookingApplicationFormRemarkOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetFormRemarkList(long applicationFormId)
    {

        var result = await _orderApiCaller.GetGroupBookingApplicationFormRemarkList(new GetGroupBookingApplicationFormRemarkListInput
        {
            ApplicationFormId = applicationFormId,
            UserType = UserType.Agency
        });
        return Ok(result);
    }

    /// <summary>
    /// 添加申请单跟单备注
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    public async Task<IActionResult> FormRemark(FormRemarkInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var operationUser = GetOperationUser();
        var groupBookingApplicationFormRemarkAddInput = new GroupBookingApplicationFormRemarkAddInput
        {
            ApplicationFormId = input.ApplicationFormId,
            Remark = input.Remark,
            OperationUser = operationUser
        };
        await _orderApiCaller.GroupBookingApplicationFormRemarkAdd(groupBookingApplicationFormRemarkAddInput);
        return Ok();
    }

}
