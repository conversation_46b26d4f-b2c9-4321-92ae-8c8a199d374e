using AutoMapper;
using Bff.Agency.Callers;
using Bff.Agency.Models.B2BIndexPage;
using Cit.Storage.Redis;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs.GroupBooking;
using Contracts.Common.Order.Enums;
using Contracts.Common.Tenant.DTOs.B2BIndexPage;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Agency.Controllers;

/// <summary>
/// B2B首页配置
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class B2BIndexPageController : ControllerBase
{
    private const string _b2bIndexPageKey = "agency:b2bindexpage:{0}:{1}";
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IUserApiCaller _userApiCaller;
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly IHotelApiCaller _hotelApiCaller;
    private readonly IMapper _mapper;
    private readonly IRedisClient _redisClient;
    private readonly IPaymentApiCaller _paymentApiCaller;

    public B2BIndexPageController(ITenantApiCaller tenantApiCaller,
        IOrderApiCaller orderApiCaller,
        IUserApiCaller userApiCaller,
        IResourceApiCaller resourceApiCaller,
        IMapper mapper,
        IHotelApiCaller hotelApiCaller,
        IRedisClient redisClient,
        IPaymentApiCaller paymentApiCaller)
    {
        _tenantApiCaller = tenantApiCaller;
        _orderApiCaller = orderApiCaller;
        _userApiCaller = userApiCaller;
        _resourceApiCaller = resourceApiCaller;
        _mapper = mapper;
        _hotelApiCaller = hotelApiCaller;
        _redisClient = redisClient;
        _paymentApiCaller = paymentApiCaller;
    }

    /// <summary>
    /// 获取组件列表
    /// </summary>
    /// <param name="language">语言 中文zh 英文en 可选</param>
    /// <param name="b2bLinkChooseTypes"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(List<B2BIndexPageComponentDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetComponents(GetComponentInput input)
    {
        var result = await GetComponentsCache(input.B2BIndexPageType.Value, input.Language);
        if (result is null)
        {
            result = await _tenantApiCaller.GetB2BIndexPageComponents(input); 
            await SetComponentsCache(input.B2BIndexPageType, input.Language, result);
        }
        return Ok(result);
    }

    /// <summary>
    /// 数据展示区块
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(DataDisplayOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetDataDisplay()
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var agencyId = currentUser.Provider;

        //缓存统计信息
        var cacheData = await GetDataDisplayOutputCacheAsync(currentUser.Tenant, agencyId);
        if (cacheData is null)
        {
            cacheData = await GetDisplayOutputAsync(agencyId);
            await SetDataDisplayOutputCacheAsync(currentUser.Tenant, agencyId, cacheData);
        }

        var agency = await _tenantApiCaller.GetAgencyDetail(agencyId);
        if (agency.SalespersonId.HasValue)
        {
            var tenantUser = await _userApiCaller.TenantUserFindOne(new Contracts.Common.User.DTOs.SupplierUser.FindOneInput
            {
                UserId = agency.SalespersonId,
            });
            if (tenantUser is not null)
            {
                cacheData.SalespersonDisplay = new SalespersonDisplayDto
                {
                    Name = tenantUser.Name,
                    NickName = tenantUser.NickName,
                    PhoneNumber = tenantUser.PhoneNumber,
                    Qrcode = tenantUser.Qrcode
                };
            }
        }


        return Ok(cacheData);
    }

    private async Task<DataDisplayOutput> GetDisplayOutputAsync(long agencyId)
    {
        DataDisplayOutput output = new();

        var agencyCreditTask = _tenantApiCaller.GetAgencyCredit();
        var receiptPrepaymentTask = _paymentApiCaller.GetReceiptPrepayments(agencyId);

        var hotelOrderSearchTask = _orderApiCaller.HotelOrderSearch(new Contracts.Common.Order.DTOs.HotelOrder.SearchInput
        {
            AgencyId = agencyId,
            SellingPlatform = new SellingPlatform[] {
                SellingPlatform.B2BWeb,
                SellingPlatform.B2BApplet
            },
            PageIndex = 1,
            PageSize = 1,
        });

        var now = DateTime.Now.Date; //使用当前时间的日期

        var hotelOrderCheckTask = _orderApiCaller.GetHotelOrderCheckCounts(new Contracts.Common.Order.DTOs.HotelOrder.SearchInput
        {
            AgencyId = agencyId,
            CheckInDate = now
        });

        var agency = await _tenantApiCaller.GetAgencyDetail(agencyId);
        if (agency.SalespersonId.HasValue)
        {
            var tenantUser = await _userApiCaller.TenantUserFindOne(new Contracts.Common.User.DTOs.SupplierUser.FindOneInput
            {
                UserId = agency.SalespersonId,
            });
            if (tenantUser is not null)
            {
                output.SalespersonDisplay = new SalespersonDisplayDto
                {
                    Name = tenantUser.Name,
                    NickName = tenantUser.NickName,
                    PhoneNumber = tenantUser.PhoneNumber,
                    Qrcode = tenantUser.Qrcode
                };
            }
        }
        var agencyCredit = await agencyCreditTask;

        if (agencyCredit is not null)
        {
            output.AgencyCreditDisplay = new AgencyCreditDisplayDto
            {
                Balance = agencyCredit.Balance,
                CreditLine = agencyCredit.CreditLine,
                CurrencyCode = agencyCredit.CurrencyCode
            };
        }

        var receiptPrepayment = (await receiptPrepaymentTask).FirstOrDefault();
        if (receiptPrepayment is not null)
        {
            output.AgencyCreditDisplay.ReceiptPrepaymentBalance = receiptPrepayment.Balance;
        }

        var hotelOrderSearch = await hotelOrderSearchTask;
        var hotelOrderCheck = await hotelOrderCheckTask;

        var todayLater = now.AddHours(23).AddMinutes(59).AddSeconds(59);

        output.OrderCountDisplay = new OrderCountDisplayDto
        {
            WaitingForConfirm = hotelOrderSearch?.Supplement?.FirstOrDefault(x => x.Status == HotelOrderStatus.WaitingForConfirm)?.Count ?? 0,
            ThreeDaysCount = hotelOrderCheck.Count(x => x.CheckInDate >= now && x.CheckInDate <= now.AddDays(2)),
            TodayCount = hotelOrderCheck.Count(x => x.CheckInDate >= now && x.CheckInDate <= todayLater)
        };

        var orderIvoiceCount = await _orderApiCaller.GetBeInvoiceCount(new Contracts.Common.Order.DTOs.BaseOrder.GetListByCanBeInvoiceInput
        {
            AgencyId = agencyId,
            OrderType = OrderType.Hotel,
            SourceChannel = InvoiceSourceChannel.B2B
        });

        var receiptSettlementIds = await _orderApiCaller.SearchReceiptSettlementIds(new Contracts.Common.Order.DTOs.ReceiptSettlementOrder.SearchInput
        {
            AgencyId = agencyId,
            TenantId = agency.TenantId,
        });

        int billCount = 0;

        if (receiptSettlementIds is not null && receiptSettlementIds.Count > 0)
        {
            var inputCheck = new Contracts.Common.Order.DTOs.Invoice.CheckStatusInput
            {
                OrderIds = receiptSettlementIds
            };
            inputCheck.SourceChannel = InvoiceSourceChannel.SettlementOrder;
            inputCheck.CreatorId = agencyId;
            var result = await _orderApiCaller.InvoiceCheckStatusByOrderIds(inputCheck);
            billCount = result?.Count(x => x.IsCanInvoice) ?? 0;
        }

        output.InvoiceDisplay = new InvoiceDisplayDto
        {
            OrderCount = orderIvoiceCount.Count,
            BillCount = billCount
        };

        return output;
    }

    /// <summary>
    /// 查询首页组件数据（汇智酒店）--暂时指定
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchComponentItemDataBffOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchComponentItemData(SearchComponentItemDataBffInput input)
    {
        var searchInput = _mapper.Map<SearchComponentItemDataInput>(input);

        if (input.TradingAreaIds.Any())
        {
            var tradingAreas = await _resourceApiCaller.SearchTradingArea(new Contracts.Common.Resource.DTOs.TradingArea.SearchTradingAreaInput
            {
                Ids = input.TradingAreaIds,
                PageIndex = 1,
                PageSize = 2,
            });

            searchInput.Points = tradingAreas.Data.Select(x => new SearchComponentItemDataPoint
            {
                Latitude = x.Latitude,
                Longitude = x.Longitude,
                Distance = input.Distance,
            }).FirstOrDefault();
        }
        var datas = await _tenantApiCaller.SearchComponentItemData(searchInput);

        var result = _mapper.Map<PagingModel<SearchComponentItemDataBffOutput>>(datas);
        if (result.Data.Any() is false)
            return Ok(result);

        #region 填充数据
        var cities = await _resourceApiCaller.QueryCities(new Contracts.Common.Resource.DTOs.QueryInput
        {
            CityCodes = datas.Data.Select(x => x.CityCode).Distinct().ToArray(),
        });
        var tags = await _hotelApiCaller.GetByApiHotelIds(new Contracts.Common.Hotel.DTOs.HotelTag.GetHotelTagsInput
        {
            ShowPageType = input.ShowPageType,
            ApiHotelIds = result.Data.Select(x => x.Id).ToList(),
        });
        foreach (var data in result.Data)
        {
            if (input.Language.Equals("zh"))
            {
                data.Tags = tags?.FirstOrDefault(t => t.ApiHotelId == data.Id)?.Tags;
                data.CityName = cities?.FirstOrDefault(x => x.CityCode == data.CityCode)?.ZHName;
            }
            else
            {
                data.HotelName = data.HotelEnName;
                data.CityName = cities?.FirstOrDefault(x => x.CityCode == data.CityCode)?.ENName;
            }
        }
        #endregion

        return Ok(result);
    }

    /// <summary>
    /// 查询-符合部分条件的酒店数据
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [AllowAnonymous]
    [ProducesResponseType(typeof(PagingModel<SearchComponentItemDataBffOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchRecommendComponentItemData(SearchComponentItemDataBffInput input)
    {
        var searchInput = _mapper.Map<SearchComponentItemDataInput>(input);

        if (input.TradingAreaIds.Any())
        {
            var tradingAreas = await _resourceApiCaller.SearchTradingArea(new Contracts.Common.Resource.DTOs.TradingArea.SearchTradingAreaInput
            {
                Ids = input.TradingAreaIds,
                PageIndex = 1,
                PageSize = 2,
            });

            searchInput.Points = tradingAreas.Data.Select(x => new SearchComponentItemDataPoint
            {
                Latitude = x.Latitude,
                Longitude = x.Longitude,
                Distance = input.Distance,
            }).FirstOrDefault();
        }
        var datas = await _tenantApiCaller.SearchRecommendComponentItemData(searchInput);

        var result = _mapper.Map<PagingModel<SearchComponentItemDataBffOutput>>(datas);
        if (result.Data.Any() is false)
            return Ok(result);

        #region 填充数据
        var cities = await _resourceApiCaller.QueryCities(new Contracts.Common.Resource.DTOs.QueryInput
        {
            CityCodes = datas.Data.Select(x => x.CityCode).Distinct().ToArray(),
        });
        var tags = await _hotelApiCaller.GetByApiHotelIds(new Contracts.Common.Hotel.DTOs.HotelTag.GetHotelTagsInput
        {
            ShowPageType = input.ShowPageType,
            ApiHotelIds = result.Data.Select(x => x.Id).ToList(),
        });
        foreach (var data in result.Data)
        {
            if (input.Language.Equals("zh"))
            {
                data.Tags = tags?.FirstOrDefault(t => t.ApiHotelId == data.Id)?.Tags;
                data.CityName = cities?.FirstOrDefault(x => x.CityCode == data.CityCode)?.ZHName;
            }
            else
            {
                data.HotelName = data.HotelEnName;
                data.CityName = cities?.FirstOrDefault(x => x.CityCode == data.CityCode)?.ENName;
            }
        }
        #endregion

        return Ok(result);
    }

    /// <summary>
    /// 查询b2b专题页列表数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<SearchB2BTopicPageDataOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchB2BTopicPageComponentItemData(SearchB2BTopicPageComponentItemDataBffInput input)
    {
        var result = await _tenantApiCaller.SearchB2BTopicPageComponentItemData(new SearchB2BTopicPageDataInput
        {
            B2BIndexPageComponentItemId = input.B2BIndexPageComponentItemId,
        });
        return Ok(result);
    }

    /// <summary>
    /// 查询团房首页组件
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<B2BIndexPageComponentDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetHotelGroupBookingComponents(GetHotelGroupBookingComponentsInput input)
    {
        var result = await GetComponentsCache(B2BIndexPageType.HotelGroupBooking, input.Language);
        if (result is null)
        {
            result = await _tenantApiCaller.GetB2BIndexPageComponents(new GetComponentInput
            {
                Language = input.Language,
                B2BIndexPageType = B2BIndexPageType.HotelGroupBooking,
            });
            await SetComponentsCache(B2BIndexPageType.HotelGroupBooking, input.Language, result);
        }
        return Ok(result);
    }

    /// <summary>
    /// 查询团房首页组件数据
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<SearchHotelGroupBookingDataOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchHotelGroupBookingDatas(SearchHotelGroupBookingDataInput input)
    {
        var hotels = await _hotelApiCaller.GetApiHotelDetail(input.HotelIds.ToArray());
        var result = _mapper.Map<List<SearchHotelGroupBookingDataOutput>>(hotels);
        if (result is null || result.Any() is false)
            return Ok(result);
        var hotelTags = await _hotelApiCaller.GetByApiHotelIds(new Contracts.Common.Hotel.DTOs.HotelTag.GetHotelTagsInput
        {
            ApiHotelIds = input.HotelIds,
            ShowPageType = Contracts.Common.Hotel.Enums.TagShowPageType.B2B_HomePage_HotelList,
        });

        result.ForEach(x =>
        {
            var hotelTag = hotelTags?.FirstOrDefault(o => o.ApiHotelId == x.Id)?.Tags?.FirstOrDefault();
            x.Name = input.Language.Equals("zh") ? x.ZHName : x.ENName;
            x.Tag = input.Language.Equals("zh") ? hotelTag?.Name : hotelTag?.EnName;
        });
        return Ok(result);
    }

    /// <summary>
    /// 查询产品列表组件-汇智酒店的标签
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<SearchB2BIndexPageRegionAllTagsBffOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchB2BIndexPageRegionAllTags(SearchB2BIndexPageRegionAllTagsBffInput input)
    {
        var tagIds = await _tenantApiCaller.SearchB2BIndexPageRegionAllTags(new SearchHotelTagsInput
        {
            B2BIndexPageComponentItemId = input.B2BIndexPageComponentItemId,
            SubRegion = input.SubRegion,
            Region = input.Region,
        });
        if (tagIds == null || tagIds.Any() is false)
            return Ok();

        var tags = await _hotelApiCaller.SearchTag(new Contracts.Common.Hotel.DTOs.Tag.SearchTagInput
        {
            Ids = tagIds,
        });
        var result = _mapper.Map<List<SearchB2BIndexPageRegionAllTagsBffOutput>>(tags);
        if (input.Language.ToLower().Equals("en"))
        {
            result.ForEach(o =>
            {
                o.Name = tags.FirstOrDefault(x => x.Id == o.Id)?.EnName ?? o.Name;
            });
        }
        return Ok(result);
    }

    /// <summary>
    /// 团房首页-查询新询单数据
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(SearchHotelGroupBookingSupplementOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchHotelGroupBookingSupplement()
    {
        var hotels = await _orderApiCaller.GroupBookingApplicationFormSearch(new ApplicationFormSearchInput
        {
            PageIndex = 1,
            PageSize = 1,
        });
        var result = new SearchHotelGroupBookingSupplementOutput
        {
            Total = hotels.Total,
        };
        return Ok(result);
    }

    private async Task<List<B2BIndexPageComponentDto>> GetComponentsCache(B2BIndexPageType? b2bIndexPageType, string language)
    {
        if (!b2bIndexPageType.HasValue)
            return null;

        var cacheKey = string.Format(_b2bIndexPageKey, b2bIndexPageType.ToString(), language);
        var result = await _redisClient.StringGetAsync<List<B2BIndexPageComponentDto>>(cacheKey);
        return result;
    }
    private async Task SetComponentsCache(B2BIndexPageType? b2bIndexPageType, string language, List<B2BIndexPageComponentDto> b2BIndexPages)
    {
        if (!b2bIndexPageType.HasValue)
            return;

        var cacheKey = string.Format(_b2bIndexPageKey, b2bIndexPageType.ToString(), language);
        await _redisClient.StringSetAsync(cacheKey, b2BIndexPages);
    }

    private async Task<DataDisplayOutput> GetDataDisplayOutputCacheAsync(long tenantId, long agencyId)
    {
        string key = GetDataDisplayOutputKey(tenantId, agencyId);
        var result = await _redisClient.StringGetAsync<DataDisplayOutput>(key);
        return result;
    }

    private async Task SetDataDisplayOutputCacheAsync(long tenantId, long agencyId, DataDisplayOutput dataDisplayData)
    {
        string key = GetDataDisplayOutputKey(tenantId, agencyId);
        await _redisClient.StringSetAsync(key, dataDisplayData, TimeSpan.FromMinutes(30));

    }
    private static string GetDataDisplayOutputKey(long tenantId, long agencyId)
    {
        return $"Agency:DataDisplay:{tenantId}:{agencyId}";
    }

}
