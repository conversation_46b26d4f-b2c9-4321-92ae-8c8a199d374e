using AutoMapper;
using Bff.Agency.Callers;
using Bff.Agency.Models.HotelGroupBookingOrder;
using Common.Jwt;
using Common.Swagger;
using Common.Swagger.Header;
using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Marketing.DTOs.FlashSale;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.ExportPdf;
using Contracts.Common.Order.DTOs.GroupBookingOrder;
using Contracts.Common.Order.DTOs.HotelOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.DTOs.Currency;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.DTOs.AgencyChannelPriceSettings;
using Contracts.Common.Product.Enums;
using Contracts.Common.Resource.DTOs.ThirdHotel;
using Contracts.Common.Tenant.DTOs.Agency;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Collections.Concurrent;

namespace Bff.Agency.Controllers;

/// <summary>
/// 酒店团房单
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class HotelGroupBookingOrderController : ControllerBase
{
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IUserApiCaller _userApiCaller;
    private readonly IHotelApiCaller _hotelApiCaller;
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly IMapper _mapper;
    private readonly IPaymentApiCaller _paymentApiCaller;

    public HotelGroupBookingOrderController(IOrderApiCaller orderApiCaller,
        ITenantApiCaller tenantApiCaller,
        IUserApiCaller userApiCaller,
        IHotelApiCaller hotelApiCaller,
        IResourceApiCaller resourceApiCaller,
        IMapper mapper,
        IPaymentApiCaller paymentApiCaller)
    {
        _orderApiCaller = orderApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _userApiCaller = userApiCaller;
        _hotelApiCaller = hotelApiCaller;
        _resourceApiCaller = resourceApiCaller;
        _mapper = mapper;
        _paymentApiCaller = paymentApiCaller;
    }

    /// <summary>
    /// 提交团房单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [ProducesResponseType(typeof(SubmitOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.SomeDatesAreUnavailable, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Submit(SubmitInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var agencyId = currentUser.Provider;
        GetAgenciesByIdsOutput? agency = !currentUser.IsVisitor ? (await _tenantApiCaller.GetAgencyDetail(agencyId)) : default;

        var applicationFormId = input.GroupBookingApplicationFormId;
        var preOrderId = input.GroupBookingPreOrderId;
        var applicationForm = await _orderApiCaller.GetApplicationForm(new Contracts.Common.Order.DTOs.GroupBooking.GetApplicationFormInput
        {
            Id = applicationFormId,
            TenantId = currentUser.Tenant
        });
        if (applicationForm?.AgencyId != agencyId)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        //非预订单状态 不可下单
        if (applicationForm.Status != GroupBookingApplicationFormStatus.PreOrdered)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        //团房预订单
        var preOrders = await _orderApiCaller.GetGroupBookingPreOrders(applicationFormId, preOrderId);
        var preOrder = preOrders.FirstOrDefault(x => x.Id == preOrderId);
        //预订单酒店信息
        var hotelIds = preOrder.PreOrderItems.Select(x => x.HotelId).ToArray();
        var apiHotelDetails = (await _hotelApiCaller.GetApiHotelDetail(hotelIds));

        string paymentCurrencyCode = agency?.CurrencyCode ?? Currency.CNY.ToString();//支付币种
        var userInfo = new OrderUserInfo()
        {
            UserId = currentUser.UserId,
            NickName = currentUser.NickName,
            AgencyId = agencyId,
            AgencyName = agency?.FullName,
            UserType = !currentUser.IsVisitor ? Contracts.Common.Order.Enums.UserType.Agency: Contracts.Common.Order.Enums.UserType.Visitor,
            SalespersonId = agency?.SalespersonId,
            SalespersonName = agency?.SalespersonName,
            VipLevelId = agency?.Level ?? 0,
            VipLevelName = agency?.LevelName ?? string.Empty,
        };
        ConcurrentBag<GroupBookingOrderItemDto> groupBookingOrderItems = new();
        await Parallel.ForEachAsync(preOrder.PreOrderItems, new ParallelOptions
        {
            MaxDegreeOfParallelism = preOrder.PreOrderItems.Count(),
        },
        async (item, cancellationToken) =>
        {
            //检验酒店库存价格房态
            var priceRequest = new CheckSaleInput()
            {
                SupplierApiType = item.SupplierApiType,
                HotelId = item.HotelId,
                RoomId = item.HotelRoomId,
                PriceStrategyId = item.PriceStrategyId,
                PreBookingCode = item.PreBookingCode,
                BeginDate = item.CheckIn,
                EndDate = item.CheckOut,
                Quantity = item.RoomCount,
                SalesChannel = SellingChannels.B2b,
                IsGroupBooking = true
            };
            var priceReponse = await _hotelApiCaller.PreOrderCheckSale(priceRequest);
            if (priceReponse?.Code != CheckPriceStrategySaleCode.Success)
                throw new BusinessException(ErrorTypes.Order.SomeDatesAreUnavailable);
            var room = priceReponse.Data.Room;
            var priceStrategy = priceReponse.Data.PriceStrategy;
            HotelApiOrderInfo? hotelApiOrderInfo = priceStrategy.SupplierApiType != SupplierApiType.None ?
                new HotelApiOrderInfo
                {
                    ArrivalTaxFees = priceStrategy.ArrivalTaxFees,
                    TaxDescription = priceStrategy.TaxDescription,
                    HotelId = priceStrategy.HotelId,
                    RoomId = priceStrategy.RoomId,
                    PriceStrategyId = priceStrategy.Id,
                    ResourceHotelId = priceReponse.Data.ResourceHotelId,
                    SupplierApiType = priceStrategy.SupplierApiType,
                } :
                null;
            //订单日历价格
            _ = long.TryParse(item.PriceStrategyId, out long priceStrategyId);
            var calendarPrices = await GetHotelCalendarPrices(new HotelAgencyChannelPriceInput
            {
                SupplierApiType = item.SupplierApiType,
                AgencyId = agencyId,
                ChannelProductType = ChannelProductType.Hotel,
                ProductId = item.HotelId,
                SkuId = priceStrategyId,
                CostCurrencyCode = priceStrategy.CostCurrencyCode,
                SaleCurrencyCode = priceStrategy.SaleCurrencyCode,
                PaymentCurrencyCode = paymentCurrencyCode,
                StaffTag = priceStrategy.IsDirect,
                Tag = priceStrategy.Tag,
                IsReunionRoomTag = priceStrategy.PriceStrategyType == PriceStrategyType.GroupRoom,
                DatePrices = priceStrategy.DatePrice.Select(x =>
                {
                    return new HotelDatePriceDto
                    {
                        Date = x.Date,
                        SalePrice = x.Price,
                        CostPrice = x.Cost,
                        Stock = x.Stock,
                    };
                })
            });
            foreach (var calendarPrice in calendarPrices)
            {
                //如果是团房预订单 价格为每日单价
                var channelPrice = item.NightlyPrices.FirstOrDefault(n => n.Date == calendarPrice.Date)?.ChannelPrice ?? calendarPrice.SalePrice;
                if (calendarPrice.PaymentCurrencyCode == item.CurrencyCode && channelPrice != calendarPrice.SalePrice)
                    calendarPrice.SalePrice = channelPrice;
            }
            var totalAmount = calendarPrices.Sum(x => x.SalePrice) * item.RoomCount;
            var discountItems = new List<OrderDiscountItemDto>();
            var discountAmount = discountItems.Sum(x => x.DiscountAmount);//优惠金额
            var paymentAmount = totalAmount - discountAmount;
            var createInput = new CreateHotelOrderInput()
            {
                TenantId = currentUser.Tenant,
                SellingPlatform = input.SellingPlatform,
                SellingChannel = SellingChannels.B2b,
                ChannelOrderNo = input.ChannelOrderNo,
                GroupNo = input.GroupNo,
                CheckIn = item.CheckIn,
                CheckOut = item.CheckOut,
                RoomCount = item.RoomCount,
                Adults = item.RoomCount,
                ContactsName = input.ContactsName,
                ContactsPhoneNumber = input.ContactsPhoneNumber,
                ContactsEmail = input.ContactsEmail,
                Message = input.Message,
                DiscountItems = discountItems,
                DiscountAmount = discountAmount,
                PaymentAmount = paymentAmount,
                PaymentCurrencyCode = paymentCurrencyCode,
                TotalAmount = totalAmount,
                CalendarPrices = calendarPrices,
                SupplierApiType = item.SupplierApiType,
                HotelApiOrderInfo = hotelApiOrderInfo,
                HotelId = item.HotelId,
                HotelName = priceReponse.Data.HotelName,
                HotelEnName = priceReponse.Data.HotelEnName,
                IsAutoConfirmRoomStatus = priceReponse.Data.IsAutoConfirmRoomStatus,
                Room = _mapper.Map<CheckSaleRoomDto>(room),
                PriceStrategy = _mapper.Map<CheckSalePriceStrategyDto>(priceStrategy),
                UserInfo = userInfo,
                GroupBookingId = input.GroupBookingApplicationFormId,//团房申请单id
            };

            var groupBookingOrderAdditions = item.PreOrderAdditions
                .Select(x => new GroupBookingOrderAdditionDto
                {
                    AdditionName = x.AdditionName,
                    Amount = x.Amount,
                    Cost = x.Cost,
                    CostCurrencyCode = x.CostCurrencyCode,
                    CurrencyCode = x.CurrencyCode,
                    Quantity = x.Quantity,
                    Remark = x.Remark,
                })
                .ToArray();
            var apiHotel = apiHotelDetails.Where(x => x.Id == item.HotelId).First();
            groupBookingOrderItems.Add(new GroupBookingOrderItemDto
            {
                GroupBookingPreOrderItemId = item.Id!.Value,
                CreateHotelOrderInput = createInput,
                CheckInDate = item.CheckIn,
                CheckOutDate = item.CheckOut,
                CityCode = apiHotel.CityCode,
                CityName = apiHotel.CityName,
                HotelENName = apiHotel.ENName,
                HotelZHName = apiHotel.ZHName,
                ResourceHotelId = apiHotel.ResourceHotelId,
                Additions = groupBookingOrderAdditions,
            });
        });
        GroupBookingOrderDto groupBookingOrderDto = new()
        {
            SellingPlatform = input.SellingPlatform,
            SellingChannels = SellingChannels.B2b,
            GroupBookingPreOrderId = input.GroupBookingPreOrderId,
            GroupBookingApplicationFormId = input.GroupBookingApplicationFormId,
            UserInfo = userInfo,
            ContactsName = input.ContactsName,
            ContactsPhoneNumber = input.ContactsPhoneNumber,
            ContactsEmail = input.ContactsEmail,
            ChannelOrderNo = input.ChannelOrderNo,
            GroupNo = input.GroupNo,
            Message = input.Message,
        };
        //首款
        GroupBookingOrderDownPaymentDto downPayment = new()
        {
            LatestPaymentTime = preOrder.ValidityPeriod,
            InitialPaymentAmount = preOrder.InitialPaymentAmount,
            PaymentRatio = preOrder.InitialPaymentRatio,
            PaymentCurrencyCode = paymentCurrencyCode,
            FinalPaymentTime = preOrder.FinalPaymentTime,
        };
        OrderCreateInput orderCreateInput = new()
        {
            GroupBookingOrder = groupBookingOrderDto,
            OrderItemDtos = groupBookingOrderItems.ToArray(),
            DownPayment = downPayment,
        };
        var result = await _orderApiCaller.GroupBookingOrderCreate(orderCreateInput);
        var output = new SubmitOutput { GroupBookingOrderId = result.GroupBookingOrderId, PaymentId = result.PaymentId };
        return Ok(output);
    }

    private async Task<HotelApiOrderInfo> GetHotelApiOrderInfo(CheckAvailabilityInput input)
    {
        var checkAvailabilityOutput = await _resourceApiCaller.ThirdHotelCheckAvailability(input);
        switch (checkAvailabilityOutput.CheckCode)
        {
            case CheckPriceStrategySaleCode.Success:
                return new HotelApiOrderInfo
                {
                    HotelId = checkAvailabilityOutput.HotelId,
                    RoomId = checkAvailabilityOutput.RoomId,
                    PriceStrategyId = checkAvailabilityOutput.PricestrategyId,
                    ResourceHotelId = input.ResourceHotelId,
                    SupplierApiType = input.SupplierApiType
                };
                break;
            default:
                throw new BusinessException(ErrorTypes.Order.SomeDatesAreUnavailable);
                break;
        }
    }

    private async Task<List<HotelCalendarPriceDto>> GetHotelCalendarPrices(HotelAgencyChannelPriceInput input)
    {

        var priceExchangeRate = await GetOrderPriceExchange(new OrderPriceExchangeRateInput
        {
            CostCurrencyCode = input.CostCurrencyCode,
            SaleCurrencyCode = input.SaleCurrencyCode,
            PaymentCurrencyCode = input.PaymentCurrencyCode,
        });
        decimal costExchangeRate = priceExchangeRate.CostExchangeRate;
        decimal exchangeRate = priceExchangeRate.ExchangeRate;
        var result = (from item in input.DatePrices
                      let channelPrice = item.SalePrice
                      select new HotelCalendarPriceDto
                      {
                          Date = item.Date,
                          PaymentCurrencyCode = input.PaymentCurrencyCode,
                          SalePrice = Math.Round(channelPrice * exchangeRate, 2),
                          CostCurrencyCode = input.CostCurrencyCode,
                          CostPrice = item.CostPrice ?? 0,
                          OrgPriceCurrencyCode = input.SaleCurrencyCode,
                          OrgPrice = channelPrice,
                          CostExchangeRate = costExchangeRate,
                          ExchangeRate = exchangeRate,
                          Stock = item.Stock,
                      })
            .ToList();

        return result;
    }

    private async Task<OrderPriceExchangeRateOutput> GetOrderPriceExchange(OrderPriceExchangeRateInput input)
    {

        var costCurrencyCode = input.CostCurrencyCode;
        var saleCurrencyCode = input.SaleCurrencyCode;
        var paymentCurrencyCode = input.PaymentCurrencyCode;
        List<GetExchangeRatesInput> exchangeRatesInputs = new();
        var costEqualsSale = costCurrencyCode.Equals(saleCurrencyCode, StringComparison.OrdinalIgnoreCase);
        if (!costEqualsSale)
        {
            exchangeRatesInputs.Add(new GetExchangeRatesInput
            {
                BaseCurrencyCode = costCurrencyCode,
                TargetCurrencyCode = saleCurrencyCode
            });
        }
        var saleEqualsPayment = saleCurrencyCode.Equals(paymentCurrencyCode, StringComparison.OrdinalIgnoreCase);
        if (!saleEqualsPayment)
        {
            exchangeRatesInputs.Add(new GetExchangeRatesInput
            {
                BaseCurrencyCode = saleCurrencyCode,
                TargetCurrencyCode = paymentCurrencyCode
            });
        }
        var exchangeRates = await _paymentApiCaller.GetExchangeRates(exchangeRatesInputs);
        return new OrderPriceExchangeRateOutput
        {
            CostExchangeRate = costEqualsSale ? 1 : exchangeRates
                .Where(x => x.BaseCurrencyCode == costCurrencyCode && x.TargetCurrencyCode == saleCurrencyCode)
                .First().ExchangeRate,
            ExchangeRate = saleEqualsPayment ? 1 : exchangeRates
                .Where(x => x.BaseCurrencyCode == saleCurrencyCode && x.TargetCurrencyCode == paymentCurrencyCode)
                .First().ExchangeRate,
        };
    }

    /// <summary>
    /// 团房单查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [ProducesResponseType(typeof(PagingModel<Models.HotelGroupBookingOrder.SearchOutput, IEnumerable<SearchSupplement>>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(Models.HotelGroupBookingOrder.SearchInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var agencyId = currentUser.Provider;
        long? userId = input.UserId;
        if (currentUser.IsVisitor) 
            userId = currentUser.UserId;
        var searchInput = new Contracts.Common.Order.DTOs.GroupBookingOrder.SearchInput
        {
            TenantId = currentUser.Tenant,
            AgencyId = agencyId,
            GroupBookingOrderId = input.GroupBookingOrderId,
            UserId = userId,
            GroupNo = input.GroupNo,
            ChannelOrderNo = input.ChannelOrderNo,
            HotelName = input.HotelName,
            OrderBeginDate = input.OrderBeginDate,
            OrderEndDate = input.OrderEndDate,
            CheckInBeginDate = input.CheckInBeginDate,
            CheckInEndDate = input.CheckInEndDate,
            CheckOutBeginDate = input.CheckOutBeginDate,
            CheckOutEndDate = input.CheckOutEndDate,
            PayStatus = input.PayStatus,
            HotelOrderStatus = input.HotelOrderStatus,
            PageIndex = input.PageIndex,
            PageSize = input.PageSize
        };
        var result = await _orderApiCaller.GroupBookingOrderSearch(searchInput);
        var output = new PagingModel<Models.HotelGroupBookingOrder.SearchOutput, IEnumerable<SearchSupplement>>
        {
            Data = result.Data.Select(x => new Models.HotelGroupBookingOrder.SearchOutput
            {
                GroupBookingOrderId = x.GroupBookingOrderId,
                HotelOrderStatus = x.HotelOrderStatus,
                HotelZHName = x.HotelZHName,
                HotelENName = x.HotelENName,
                CheckInDate = x.CheckInDate,
                CheckOutDate = x.CheckOutDate,
                HotelId = x.HotelId,
                HotelRoomName = x.HotelRoomName,
                HotelRoomEnName = x.HotelRoomEnName,
                PriceStrategyName = x.PriceStrategyName,
                PriceStrategyEnName = x.PriceStrategyEnName,
                RoomCount = x.RoomCount,
                ContactsName = x.ContactsName,
                TotalPayment = x.TotalPayment,
                PaymentCurrencyCode = x.PaymentCurrencyCode,
                DownPaymentAmount = x.DownPaymentAmount,
                LatestDownPaymentTime = x.LatestDownPaymentTime,
                FinalPaymentAmount = x.FinalPaymentAmount,
                LatestFinalPaymentTime = x.LatestFinalPaymentTime,
            }),
            Supplement = result.Supplement,
            Total = result.Total,
            PageIndex = result.PageIndex,
            PageSize = result.PageSize,
        };
        return Ok(output);
    }

    /// <summary>
    /// 团房单详情
    /// </summary>
    /// <param name="groupBookingOrderId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [ProducesResponseType(typeof(GetDetailOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetDetail(long groupBookingOrderId)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var agencyId = currentUser.Provider;
        var result = await _orderApiCaller.GroupBookingOrderDetail(new Contracts.Common.Order.DTOs.GroupBookingOrder.DetailInput
        {
            GroupBookingOrderId = groupBookingOrderId,
            AgencyId = agencyId,
            UserId = currentUser.IsVisitor ? currentUser.UserId : null,
        });
        GetDetailOutput output = _mapper.Map<GetDetailOutput>(result);
        try
        {
            foreach (var item in output.OrderItems)
            {
                if (!string.IsNullOrWhiteSpace(item.BedType))
                {
                    var bedTypes = JsonConvert.DeserializeObject<List<BedType>>(item.BedType!);
                    item.BedTypes = bedTypes;
                }
            }
        }
        catch { }
        var preOrder = (await _orderApiCaller.GetGroupBookingPreOrders(result.GroupBookingOrder.GroupBookingApplicationFormId, result.GroupBookingOrder.GroupBookingPreOrderId)).FirstOrDefault();
        if (preOrder is not null)
        {
            output.PreOrder = new HotelGroupBookingPreOrderOutput
            {
                CancellationPolicy = preOrder.CancellationPolicy,
                SpecialRemarks = preOrder.SpecialRemarks,
                RemindAdvanceDays = preOrder.RemindAdvanceDays,
                PreOrderItems = preOrder.PreOrderItems.Select(x => new PreOrderItemOutput { HotelId = x.HotelId, SupplierApiType = x.SupplierApiType, ChildPolicy = x.ChildPolicy })
            };
        }
        foreach (var item in output.OrderItems)
        {
            var preOrderItem = preOrder.PreOrderItems.FirstOrDefault(s => s.Id == item.GroupBookingPreOrderItemId);
            item.ChildPolicy = preOrderItem?.ChildPolicy;
        }

        //var hotelDetail = await _resourceApiCaller.GetHotelDetail(output.GroupBookingOrder.ResourceHotelId);
        //if (hotelDetail is not null)
        //{
        //    output.GroupBookingOrder.CountryCode = hotelDetail.CountryCode;
        //    output.GroupBookingOrder.HotelAddress = hotelDetail.Address;
        //    output.GroupBookingOrder.HotelTelePhone = hotelDetail.Telephone;
        //}

        return Ok(output);
    }

    /// <summary>
    /// 获取团房单入住名单信息
    /// </summary>
    /// <param name="groupBookingOrderId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [ProducesResponseType(typeof(List<HotelOrderGuestItemOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetHotelOrderGuestItems(long groupBookingOrderId)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var agencyId = currentUser.Provider;
        var detail = await _orderApiCaller.GroupBookingOrderDetail(new Contracts.Common.Order.DTOs.GroupBookingOrder.DetailInput
        {
            GroupBookingOrderId = groupBookingOrderId,
            AgencyId = agencyId,
            UserId = currentUser.IsVisitor ? currentUser.UserId : null,
        });

        var cityCodes = detail.OrderItems.Select(x => x.CityCode).ToArray();
        var cities = await _resourceApiCaller.QueryCities(new Contracts.Common.Resource.DTOs.QueryInput
        {
            CityCodes = cityCodes
        });

        List<HotelOrderGuestItemOutput> result = detail.OrderItems
            .GroupBy(x => new { x.GroupBookingOrderId, x.ResourceHotelId, x.HotelZHName, x.HotelENName, x.CityCode })
            .Select(x => new HotelOrderGuestItemOutput
            {
                GroupBookingOrderId = x.Key.GroupBookingOrderId,
                ResourceHotelId = x.Key.ResourceHotelId,
                HotelZHName = x.Key.HotelZHName,
                HotelENName = x.Key.HotelENName,
                CityCode = x.Key.CityCode,
                CountryCode = cities.FirstOrDefault(s => s.CityCode == x.Key.CityCode)?.CountryCode ?? 0,
                ProvinceCode = cities.FirstOrDefault(s => s.CityCode == x.Key.CityCode)?.ProvinceCode ?? 0,
                BaseOrderIds = x.Select(s => s.BaseOrderId).ToArray(),
                HotelOrderStatus = x.Max(s => s.HotelOrderStatus),
                GuestFilePath = x.First().GuestFilePath,
                GroupBookingOrderItemIds = x.Select(s => s.Id).ToArray(),
            })
            .ToList();
        return Ok(result);
    }

    /// <summary>
    /// 上传入住人名单信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> AddOrderGuest(AddGuestInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var agencyId = currentUser.Provider;
        await _orderApiCaller.AddGroupBookingOrderGuest(new AddOrderGuestInput
        {
            AgencyId = agencyId,
            UserId = currentUser.IsVisitor ? currentUser.UserId : null,
            GroupBookingOrderId = input.GroupBookingOrderId,
            OrderGuests = input.GroupBookingOrderItemIds.Select(i => new OrderGuestInput
            {
                GroupBookingOrderItemId = i,
                FilePath = input.FilePath,
                GuestInfos = input.GuestInfos.Select(g => new HotelOrderGuestInfo
                {
                    RoomNumber = g.RoomNumber,
                    GuestName = g.GuestName,
                    FirstName = g.FirstName,
                    LastName = g.LastName
                })
            }).ToArray(),
        });
        return Ok();
    }

    /// <summary>
    /// 编辑备注
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> EditOrderMessage(EditMessageInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var agencyId = currentUser.Provider;
        await _orderApiCaller.EditGroupBookingOrderMessage(new EditOrderMessageInput
        {
            GroupBookingOrderId = input.GroupBookingOrderId,
            AgencyId = agencyId,
            UserId = currentUser.IsVisitor ? currentUser.UserId : null,
            Message = input.Message
        });
        return Ok();
    }

    /// <summary>
    /// 团房单导出入住单
    /// </summary>
    /// <param name="groupBookingOrderId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [ProducesResponseType(typeof(byte[]), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> ExportPdf(long groupBookingOrderId, string subOrderIds = null, string language = "zh")
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var groupBookingOrderDetails = await _orderApiCaller.GroupBookingOrderDetail(new Contracts.Common.Order.DTOs.GroupBookingOrder.DetailInput
        {
            GroupBookingOrderId = groupBookingOrderId,
            AgencyId = currentUser.Provider,
            UserId = currentUser.IsVisitor ? currentUser.UserId : null,
        });
        var baseOrderIds = subOrderIds.Split(',');
        if (baseOrderIds != null && baseOrderIds.Any())
        {
            groupBookingOrderDetails.OrderItems = groupBookingOrderDetails.OrderItems
                .Where(x => baseOrderIds.Contains(x.BaseOrderId.ToString()))
                .ToList();
        }

        var hotelIds = groupBookingOrderDetails.OrderItems.Select(x=> x.ResourceHotelId).Distinct().ToArray();
        var hotelDetails = await _resourceApiCaller.GetHotelDetailByHotelIds(hotelIds);
        var groupBookingOrderDetailByGroup = groupBookingOrderDetails.OrderItems.GroupBy(x => x.HotelId);
        var exportDetail = new ExportGroupBookingOrderPdfInput
        {
            GroupBookingApplicationFormId = groupBookingOrderDetails.GroupBookingOrder.GroupBookingApplicationFormId,
            GroupBookingOrderId = groupBookingOrderId,
            ExportHotelOrderType = Contracts.Common.Order.DTOs.HotelOrder.ExportHotelOrderType.CheckIn,
            TotalAmount = groupBookingOrderDetails.GroupBookingOrder.TotalPayment,
            PaymentCurrencyCode = groupBookingOrderDetails.GroupBookingOrder.PaymentCurrencyCode,
            Message = groupBookingOrderDetails.GroupBookingOrder.Message,
            Language = language,
            Hotels = groupBookingOrderDetailByGroup.Select(x =>
            {
                var hotelDetail = hotelDetails.FirstOrDefault(h => h.Id == x.First().ResourceHotelId);
                var obj = new ExportGroupBookingOrderOutput
                {
                    HotelId = x.Key,
                    HotelName = x.First().HotelZHName,
                    HotelEnName = x.First().HotelENName,
                    HotelAddress = hotelDetail.Address,
                    HotelEnAddress = hotelDetail.EnAddress,
                    HotelTelePhone = hotelDetail.Telephone,
                    OrderItems = x.Select(o => new ExportGroupBookingOrderItemOutput
                    {
                        BaseOrderId = o.BaseOrderId,
                        HotelRoomName = o.HotelRoomName,
                        HotelRoomEnName = o.HotelRoomEnName,
                        CheckInDate = o.CheckInDate,
                        CheckOutDate = o.CheckOutDate,
                        PriceStrategyNumberOfBreakfast = o.NumberOfBreakfast,
                        PriceStrategyRoomsCount = o.RoomCount,
                        PriceStrategyNightsCount = o.NumberOfBreakfast,
                        ConfirmCode = o.ConfirmCode,
                        HotelOrderGuests = o.HotelOrderGuests,
                    }).OrderBy(x => x.CheckInDate).ToList(),
                };
                return obj;
            }).ToList(),
        };

        var file = await _orderApiCaller.ExportHotelGroupBookingOrderPdf(exportDetail);
        return File(file, "application/pdf", language == "zh" ? "团房入住单.pdf" : "Group Room Reservation Form.pdf");
    }


    /// <summary>
    /// 团房Pdf导出确认单
    /// </summary>
    /// <param name="groupBookingOrderId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
    [ProducesResponseType(typeof(byte[]), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> ExportConfirmPdf(long groupBookingOrderId,string subOrderIds = null,string language = "zh")
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var groupBookingOrderDetails = await _orderApiCaller.GroupBookingOrderDetail(new Contracts.Common.Order.DTOs.GroupBookingOrder.DetailInput
        {
            GroupBookingOrderId = groupBookingOrderId,
            AgencyId = currentUser.Provider,
            UserId = currentUser.IsVisitor ? currentUser.UserId : null,
        });

        var baseOrderIds = subOrderIds.Split(',');
        if (baseOrderIds != null && baseOrderIds.Any())
        {
            groupBookingOrderDetails.OrderItems = groupBookingOrderDetails.OrderItems
                .Where(x => baseOrderIds.Contains(x.BaseOrderId.ToString()))
                .ToList();
        }
        var hotelIds = groupBookingOrderDetails.OrderItems.Select(x => x.ResourceHotelId).Distinct().ToArray();
        var hotelDetails = await _resourceApiCaller.GetHotelDetailByHotelIds(hotelIds);
        var groupBookingOrderDetailByGroup = groupBookingOrderDetails.OrderItems.GroupBy(x => x.HotelId);
        var exportDetail = new ExportGroupBookingOrderPdfInput
        {
            GroupBookingApplicationFormId = groupBookingOrderDetails.GroupBookingOrder.GroupBookingApplicationFormId,
            GroupBookingOrderId = groupBookingOrderId,
            ExportHotelOrderType = Contracts.Common.Order.DTOs.HotelOrder.ExportHotelOrderType.Confirm,
            TotalAmount = groupBookingOrderDetails.GroupBookingOrder.TotalPayment,
            PaymentCurrencyCode = groupBookingOrderDetails.GroupBookingOrder.PaymentCurrencyCode,
            Message = groupBookingOrderDetails.GroupBookingOrder.Message,
            Language = language,
            Hotels = groupBookingOrderDetailByGroup.Select(x =>
            {
                var hotelDetail = hotelDetails.FirstOrDefault(h => h.Id == x.First().ResourceHotelId);
                var obj = new ExportGroupBookingOrderOutput
                {
                    HotelId = x.Key,
                    HotelName = x.First().HotelZHName,
                    HotelEnName = x.First().HotelENName,
                    HotelAddress = hotelDetail.Address,
                    HotelEnAddress = hotelDetail.EnAddress,
                    HotelTelePhone = hotelDetail.Telephone,
                    OrderItems = x.Select(o => new ExportGroupBookingOrderItemOutput
                    {
                        BaseOrderId = o.BaseOrderId,
                        HotelRoomName = o.HotelRoomName,
                        HotelRoomEnName = o.HotelRoomEnName,
                        CheckInDate = o.CheckInDate,
                        CheckOutDate = o.CheckOutDate,
                        PriceStrategyNumberOfBreakfast = o.NumberOfBreakfast,
                        PriceStrategyRoomsCount = o.RoomCount,
                        PriceStrategyNightsCount = o.NumberOfBreakfast,
                        ConfirmCode = o.ConfirmCode,
                        HotelOrderGuests = o.HotelOrderGuests,
                    }).OrderBy(x => x.CheckInDate).ToList(),
                };
                return obj;
            }).ToList(),
        };

        var file = await _orderApiCaller.ExportHotelGroupBookingOrderPdf(exportDetail);
        return File(file, "application/pdf", language == "zh" ? "团房确认单.pdf" : "Group Room Confirmation Form.pdf");
    }
}