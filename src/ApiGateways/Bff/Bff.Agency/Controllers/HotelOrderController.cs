using AutoMapper;
using Bff.Agency.Callers;
using Bff.Agency.Models.GDSHotel;
using Bff.Agency.Models.GDSOrder;
using Bff.Agency.Models.Hotel;
using Bff.Agency.Models.HotelOrder;
using Bff.Agency.Services.Interfaces;
using Cit.Storage.Redis;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Hotel.DTOs.ApiHotel;
using Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Marketing.DTOs.UserCoupon;
using Contracts.Common.Marketing.Enums;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.DTOs.ExportPdf;
using Contracts.Common.Order.DTOs.HotelOrder;
using Contracts.Common.Order.DTOs.HotelOrder.GDS;
using Contracts.Common.Order.DTOs.OrderCommission;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.DTOs.Currency;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.DTOs.AgencyChannelCommissionSettings;
using Contracts.Common.Product.DTOs.AgencyChannelPriceSettings;
using Contracts.Common.Product.Enums;
using Contracts.Common.Resource.DTOs.GDSHotel;
using Contracts.Common.Resource.DTOs.ThirdHotel;
using Contracts.Common.Resource.DTOs.YouxiaHotel;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Tenant.DTOs.SupplierApiSetting;
using Contracts.Common.Tenant.Enums; 
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Bff.Agency.Controllers;

[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[Authorize(policy: AuthorizationPolicyNames.DefaultAndVistor)]
[ApiController]
public class HotelOrderController : ControllerBase
{
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IHotelApiCaller _hotelApiCaller;
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly IProductApiCaller _productApiCaller;
    private readonly IPaymentApiCaller _paymentApiCaller;
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IMarketingApiCaller _marketingApiCaller;
    private readonly IHotelService _hotelService;
    private readonly IPermissionCaller _permissionCaller;
    private readonly IMapper _mapper;
    private readonly IRedisClient _redisClient;
    private readonly IGDSHotelService _gDSHotelService;

    public HotelOrderController(ITenantApiCaller tenantApiCaller,
        IHotelApiCaller hotelApiCaller,
        IResourceApiCaller resourceApiCaller,
        IProductApiCaller productApiCaller,
        IPaymentApiCaller paymentApiCaller,
        IOrderApiCaller orderApiCaller,
        IMarketingApiCaller marketingApiCaller,
        IHotelService hotelService,
        IMapper mapper,
        IPermissionCaller permissionCaller,
        IRedisClient redisClient,
        IGDSHotelService gDSHotelService)
    {
        _tenantApiCaller = tenantApiCaller;
        _hotelApiCaller = hotelApiCaller;
        _resourceApiCaller = resourceApiCaller;
        _productApiCaller = productApiCaller;
        _paymentApiCaller = paymentApiCaller;
        _orderApiCaller = orderApiCaller;
        _marketingApiCaller = marketingApiCaller;
        _hotelService = hotelService;
        _mapper = mapper;
        _permissionCaller = permissionCaller;
        _redisClient = redisClient;
        _gDSHotelService = gDSHotelService;
    }

    /// <summary>
    /// 试单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [HttpPost]
    [SwaggerResponseExt(200, typeof(PreOrderOutput))]
    [SwaggerResponseExt(default, ErrorTypes.Order.SomeDatesAreUnavailable)]
    public async Task<IActionResult> PreOrder(PreOrderInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var agencyId = currentUser.Provider;
        var agency = await _tenantApiCaller.GetAgencyDetail(agencyId);
        //检验酒店库存价格房态
        var priceRequest = new CheckSaleInput()
        {
            SupplierApiType = input.SupplierApiType,
            HotelId = input.HotelId,
            RoomId = input.RoomId,
            PreBookingCode = input.PreBookingCode,
            PriceStrategyId = input.PriceStrategyId,
            BeginDate = input.CheckIn,
            EndDate = input.CheckOut,
            Quantity = input.RoomCount,
            AdultNum = input.AdultNum,
            ChildrenAges = input.ChildrenAges,
            SalesChannel = SellingChannels.B2b,
            IsGroupBooking = input.IsGroupBooking,
        };
        var priceReponse = await _hotelApiCaller.PreOrderCheckSale(priceRequest);
        switch (priceReponse?.Code)
        {
            case CheckPriceStrategySaleCode.Success:
            case CheckPriceStrategySaleCode.PriceChanged:
                break;
            default:
                throw new BusinessException(ErrorTypes.Order.SomeDatesAreUnavailable);
                break;
        }
        var room = priceReponse.Data.Room;
        var priceStrategy = priceReponse.Data.PriceStrategy;
        string paymentCurrencyCode = agency.CurrencyCode;//支付币种
        _ = long.TryParse(input.PriceStrategyId, out long priceStrategyId);
        var calendarPrices = await GetHotelCalendarPrices(agency.PriceGroupId!.Value, new HotelAgencyChannelPriceInput
        {
            Code = priceReponse.Code,
            SupplierApiType = input.SupplierApiType,
            AgencyId = agencyId,
            ChannelProductType = ChannelProductType.Hotel,
            ProductId = input.HotelId,
            SkuId = priceStrategyId,
            CostCurrencyCode = priceStrategy.CostCurrencyCode,
            SaleCurrencyCode = priceStrategy.SaleCurrencyCode,
            PaymentCurrencyCode = paymentCurrencyCode,
            StaffTag = priceStrategy.IsDirect,
            Tag = priceStrategy.Tag,
            IsReunionRoomTag = priceStrategy.PriceStrategyType == PriceStrategyType.GroupRoom,
            DatePrices = priceStrategy.DatePrice.Select(x => new HotelDatePriceDto
            {
                Date = x.Date,
                SalePrice = x.Price,
                CostPrice = x.Cost,
                Stock = x.Stock,
            }),
            CheckDatePrices = priceStrategy.CheckDatePrice.Select(x => new HotelDatePriceDto
            {
                Date = x.Date,
                SalePrice = x.Price,
                CostPrice = x.Cost,
                Stock = x.Stock,
            })
        });

        PreOrderOutput output = new()
        {
            Code = priceReponse.Code,
            CalendarPrices = calendarPrices.Select(x => new CalendarPriceDto
            {
                ChannelPrice = x.SalePrice,
                Date = x.Date,
                PaymentCurrencyCode = x.PaymentCurrencyCode
            }),
            TaxDescription = priceStrategy.TaxDescription,
            ArrivalTaxFees = priceStrategy.ArrivalTaxFees,
        };
        output.SumChannelPrice = output.CalendarPrices.Sum(x => x.ChannelPrice);
        output.TotalChannelPrice = paymentCurrencyCode == Currency.CNY.ToString() ?
          (int)Math.Floor(output.SumChannelPrice * input.RoomCount) : output.SumChannelPrice * input.RoomCount;

        return Ok(output);
    }

    /// <summary>
    /// 创建订单
    /// </summary>
    /// <param name="input">订单id 订单金额等信息</param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(200, typeof(CreateHotelOrderOutput))]
    [SwaggerResponseExt(default,
        ErrorTypes.Order.OrderIsExist,
        ErrorTypes.Order.EachRoomNeedOneGuest,
        ErrorTypes.Order.SomeDatesAreUnavailable,
        ErrorTypes.Order.ChannelPriceSettingChange)]
    [SwaggerResponseExt(default, ErrorTypes.Marketing.UserCouponDisabled)]
    [SwaggerResponseExt(default, ErrorTypes.Inventory.ProductInventoryNotEnough,
        ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Create([FromBody] CreateInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var lockName = $"agency:hotelorder:create:{currentUser.UserId}";
        var lockSecret = Guid.NewGuid().ToString();
        await _redisClient.LockTakeWaitingAsync(lockName, lockSecret, TimeSpan.FromSeconds(5));
        try
        {

            CreateHotelOrderInput orderInput = new();
            if (input.SupplierApiType == SupplierApiType.GDS || input.SupplierApiType == SupplierApiType.Youxia)
            {
                orderInput = await GDSCreate(input, currentUser);
            }
            else
            {
                orderInput = await CreateOrder(input, currentUser);
            }

            var result = await _orderApiCaller.HotelOrderCreate(orderInput);


            //点击下单（不管是否支付），权重分 10
            if (input.SupplierApiType == SupplierApiType.Hop)
            {
                var pushWeightValueInputs = new List<PushWeightValueInput>() {
                        new PushWeightValueInput
                        {
                            HotelId = input.HotelId,
                            TenantId = currentUser.Tenant,
                            Value = 10
                        }
                    };
                _ = _hotelApiCaller.PushApiHotelWeightValue(pushWeightValueInputs);
            }
            return Ok(result);
        }
        finally
        {
            await _redisClient.LockReleaseAsync(lockName, lockSecret);
        }
    }

    private async Task<CreateHotelOrderInput> CreateOrder(CreateInput input, CurrentUser currentUser)
    {
        DateTime? expireStartTime = input.LeftTime.HasValue ? DateTime.Now.AddSeconds(input.LeftTime.Value) : null;
        if (!string.IsNullOrWhiteSpace(input.ChannelOrderNo))
        {
            var isExists = await _orderApiCaller.CheckChannelOrderNoExist(new CheckChannelOrderNoExistInput
            {
                AgencyId = currentUser.Provider,
                ChannelOrderNo = input.ChannelOrderNo
            });
            if (isExists)
                throw new BusinessException(ErrorTypes.Order.OrderIsExist);
        }

        if (input.RoomCount > input.Guests.Count())
        {
            throw new BusinessException(ErrorTypes.Order.EachRoomNeedOneGuest);
        }

        var agencyId = currentUser.Provider;
        var agency = await _tenantApiCaller.GetAgencyDetail(agencyId);
        //检验酒店库存价格房态
        var priceRequest = new CheckSaleInput()
        {
            SupplierApiType = input.SupplierApiType,
            HotelId = input.HotelId,
            RoomId = input.RoomId,
            PriceStrategyId = input.PriceStrategyId,
            PreBookingCode = input.PreBookingCode,
            BeginDate = input.CheckIn,
            EndDate = input.CheckOut,
            Quantity = input.RoomCount,
            SalesChannel = SellingChannels.B2b,
            AdultNum = input.AdultNum,
            ChildrenAges = input.ChildrenAges,
            IsGroupBooking = input.GroupBookingId is > 0
        };
        var priceReponse = await _hotelApiCaller.PreOrderCheckSale(priceRequest);
        switch (priceReponse?.Code)
        {
            case CheckPriceStrategySaleCode.Success:
            case CheckPriceStrategySaleCode.PriceChanged:
                break;
            default:
                throw new BusinessException(ErrorTypes.Order.SomeDatesAreUnavailable);
                break;
        }

        string paymentCurrencyCode = agency.CurrencyCode;//支付币种

        var room = priceReponse.Data.Room;
        var priceStrategy = priceReponse.Data.PriceStrategy;
        if (priceReponse.Data.PriceStrategy.IsDirect)//直采
        {
            var hotelId = priceReponse.Data.HotelId;
            var agencyLevelConfigs = await _tenantApiCaller.GetConfigHotelByHotelIds(new Contracts.Common.Tenant.DTOs.AgencyLevelConfigHotel.AgencyLevelConfigHotelInput
            {
                AgencyId = agencyId,
                HotelIds = new List<long> { hotelId }
            });
            var agencyLevelConfig = agencyLevelConfigs.FirstOrDefault(x => x.HotelId == hotelId);
            if (agencyLevelConfig is not null)
            {
                priceStrategy.CancelRule.Description = agencyLevelConfig.Description;//会员等级 可改退 退改说明
            }
        }

        HotelApiOrderInfo? hotelApiOrderInfo = priceStrategy.SupplierApiType != SupplierApiType.None ?
            new HotelApiOrderInfo
            {
                ArrivalTaxFees = priceStrategy.ArrivalTaxFees,
                TaxDescription = priceStrategy.TaxDescription,
                HotelId = priceStrategy.HotelId,
                RoomId = priceStrategy.RoomId,
                PriceStrategyId = priceStrategy.Id,
                ResourceHotelId = priceReponse.Data.ResourceHotelId,
                SupplierApiType = priceStrategy.SupplierApiType,
            } :
            null;

        //订单日历价格
        _ = long.TryParse(input.PriceStrategyId, out long priceStrategyId);
        var calendarPrices = await GetHotelCalendarPrices(agency.PriceGroupId!.Value, new HotelAgencyChannelPriceInput
        {
            Code = priceReponse.Code,
            SupplierApiType = input.SupplierApiType,
            AgencyId = agencyId,
            ChannelProductType = ChannelProductType.Hotel,
            ProductId = input.HotelId,
            SkuId = priceStrategyId,
            CostCurrencyCode = priceStrategy.CostCurrencyCode,
            SaleCurrencyCode = priceStrategy.SaleCurrencyCode,
            PaymentCurrencyCode = paymentCurrencyCode,
            StaffTag = priceStrategy.IsDirect,
            Tag = priceStrategy.Tag,
            IsReunionRoomTag = priceStrategy.PriceStrategyType == PriceStrategyType.GroupRoom,
            DatePrices = priceStrategy.DatePrice.Select(x => new HotelDatePriceDto
            {
                Date = x.Date,
                SalePrice = x.Price,
                CostPrice = x.Cost,
                Stock = x.Stock,
            }),
            CheckDatePrices = priceStrategy.CheckDatePrice.Select(x => new HotelDatePriceDto
            {
                Date = x.Date,
                SalePrice = x.Price,
                CostPrice = x.Cost,
                Stock = x.Stock,
            }),
        });

        var totalAmount = calendarPrices.Sum(x => x.SalePrice) * input.RoomCount;
        var discountItems = new List<OrderDiscountItemDto>();
        //验证优惠券
        if (input.UserCouponId is > 0 && paymentCurrencyCode == Contracts.Common.Payment.Enums.Currency.CNY.ToString())
        {
            long.TryParse(priceStrategy.Id, out long itemId);
            var getOrderUserCouponRequest = new GetOrderUserCouponsInput()
            {
                AgencyId = agencyId,
                UserCouponId = input.UserCouponId,
                ProductType = input.SupplierApiType == SupplierApiType.None ? LimitProductType.Hotel : LimitProductType.ApiHotel,
                OrderProductInfos = new List<OrderProductInfo>
                    {
                        new ()
                        {
                           ProductId = input.HotelId,
                           ItemId = itemId,
                           OrderAmount = totalAmount,
                           OrderHotelInfo = new OrderHotelInfo {
                               NumberOfRoom = input.RoomCount,
                               DatePrices = calendarPrices.Select(x => x.SalePrice),
                               IsDirect = priceStrategy.IsDirect,
                               PriceStrategyType = priceStrategy.PriceStrategyType,
                               Tag = priceStrategy.Tag,
                           }
                        }
                    }
            };
            var orderUserCoupon = await GetOrderUserCoupon(getOrderUserCouponRequest);
            if (orderUserCoupon?.IsEnable is not true)
            {
                throw new BusinessException(ErrorTypes.Marketing.UserCouponDisabled);//优惠券不可用
            }

            discountItems.Add(new OrderDiscountItemDto
            {
                DiscountType = OrderDiscountType.UserCoupon,
                DiscountId = orderUserCoupon.UserCouponId,
                DiscountAmount = orderUserCoupon.Discount,
                Title = orderUserCoupon.CouponName
            });
        }
        var discountAmount = discountItems.Sum(x => x.DiscountAmount);//优惠金额
        var paymentAmount = totalAmount - discountAmount;
        if (paymentCurrencyCode == Currency.CNY.ToString())//CNY 抹零
        {
            var diff = paymentAmount - Math.Floor(paymentAmount);
            if (diff > 0)
            {
                discountItems.Add(new OrderDiscountItemDto
                {
                    DiscountType = OrderDiscountType.IgnoreDecimals,
                    DiscountAmount = diff,
                });
                discountAmount = discountItems.Sum(x => x.DiscountAmount);
                paymentAmount -= diff;
            }
        }
        List<HotelGuestsInfo> guestsInfos = new();
        foreach (var item in input.Guests)
        {
            guestsInfos.AddRange(item.GuestsInfos.Select(s => new HotelGuestsInfo()
            {
                FirstName = s.FirstName,
                LastName = s.LastName,
                HotelOrderGuestType = s.HotelOrderGuestType,
                Age = s.Age,
                RoomNumber = item.RoomNumber
            }));
        }
        var createInput = new CreateHotelOrderInput()
        {
            TenantId = currentUser.Tenant,
            SellingPlatform = input.SellingPlatform,
            SellingChannel = SellingChannels.B2b,
            ChannelOrderNo = input.ChannelOrderNo,
            GroupNo = input.GroupNo,
            CheckIn = input.CheckIn,
            CheckOut = input.CheckOut,
            RoomCount = input.RoomCount,
            Adults = guestsInfos.Count(x => x.HotelOrderGuestType == HotelOrderGuestType.Adult),
            ChildrenAges = input.ChildrenAges,
            GuestInfos = guestsInfos.ToArray(),
            ContactsName = input.ContactsName,
            ContactsPhoneNumber = input.ContactsPhoneNumber,
            ContactsEmail = input.ContactsEmail,
            Message = input.Message,
            DiscountItems = discountItems,
            DiscountAmount = discountAmount,
            PaymentAmount = paymentAmount,
            PaymentCurrencyCode = paymentCurrencyCode,
            TotalAmount = totalAmount,
            CalendarPrices = calendarPrices,
            SupplierApiType = input.SupplierApiType,
            HotelApiOrderInfo = hotelApiOrderInfo,
            HotelId = input.HotelId,
            HotelName = priceReponse.Data.HotelName,
            HotelEnName = priceReponse.Data.HotelEnName,
            IsAutoConfirmRoomStatus = priceReponse.Data.IsAutoConfirmRoomStatus,
            Room = _mapper.Map<CheckSaleRoomDto>(room),
            PriceStrategy = _mapper.Map<CheckSalePriceStrategyDto>(priceStrategy),
            UserInfo = new OrderUserInfo()
            {
                UserId = currentUser.UserId,
                NickName = currentUser.NickName,
                AgencyId = agencyId,
                AgencyName = agency.FullName,
                UserType = Contracts.Common.Order.Enums.UserType.Agency,
                SalespersonId = agency.SalespersonId,
                SalespersonName = agency.SalespersonName,
                VipLevelId = agency.Level ?? 0,
                VipLevelName = agency.LevelName ?? string.Empty,
            },
            GroupBookingId = input.GroupBookingId,
            Nationality = input.Nationality != null
                ? new NationalityInfo
                {
                    CountryCode = input.Nationality.CountryCode,
                    ZHName = input.Nationality.ZHName,
                    ENName = input.Nationality.ENName,
                    IsoCode = input.Nationality.IsoCode
                }
                : null,
            AutoCloseTimeoutOrder = true,
            ExpireStartTime = expireStartTime,
        };

        return createInput;
    }

    /// <summary>
    /// 获取Hop 酒店订单最新信息
    /// </summary>
    /// <param name="hotelOrderId"></param>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(HopHotelOrderUpdatedDto), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.NoMatchingData)]
    public async Task<IActionResult> GetHotelHopUpdatedInfo(long hotelOrderId)
    {
        var detailOutput = await _orderApiCaller.GetHotelHopUpdatedInfo(new HopHotelOrderUpdatedInput
        {
            HotelOrderId = hotelOrderId
        });
        return Ok(detailOutput);
    }

    /// <summary>
    /// 分销商web-订单详情
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(OrderDetailOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.NoMatchingData)]
    public async Task<IActionResult> Detail(long baseOrderId)
    {

        var user = HttpContext.User.ParseUserInfo<CurrentUser>();
        var agencyId = user.Provider;
        var detailOutput = await _orderApiCaller.HotelOrderDetail(new DetailInput
        {
            BaseOrderId = baseOrderId,
            AgencyId = agencyId
        });
        var result = new OrderDetailOutput
        {
            BaseOrder =
                new B2BHotelBaseOrderDetail
                {
                    Id = detailOutput.Id,
                    UserId = detailOutput.UserId,
                    UserNickName = detailOutput.UserNickName,
                    ContactsName = detailOutput.ContactsName,
                    ContactsPhoneNumber = detailOutput.ContactsPhoneNumber,
                    ContactsEmail = detailOutput.ContactsEmail,
                    Status = detailOutput.Status,
                    ChannelOrderNo = detailOutput.ChannelOrderNo,
                    GroupNo = detailOutput.GroupNo,
                    TotalAmount = detailOutput.TotalAmount ?? 0,
                    DiscountAmount = detailOutput.DiscountAmount ?? 0,
                    PaymentAmount = detailOutput.PaymentAmount ?? 0,
                    PaymentCurrencyCode = detailOutput.PaymentCurrencyCode,
                    PaymentType = detailOutput.PaymentType,
                    PayTime = detailOutput.PayTime,
                    DelayedPayStatus = detailOutput.DelayedPayStatus,
                    DelayedPayDeadline = detailOutput.DelayedPayDeadline,
                    Message = detailOutput.Message,
                    CreateTime = detailOutput.CreateTime,
                    LeftTime = detailOutput.LeftTime,
                },
            HotelOrder = detailOutput.OrderDetail,
            CalendarPrices =
                detailOutput.CalendarPrices.Select(x => new CalendarPriceBase
                {
                    Date = x.Date,
                    PaymentCurrencyCode = x.PaymentCurrencyCode,
                    SalePrice = x.SalePrice
                }),
            DiscountItems = detailOutput.DiscountItems,
            CancelRule = detailOutput.CancelRule,
            HotelGuests = detailOutput.HotelGuests,
            OrderAdditions = detailOutput?.OrderAdditions?.Select(x => new OrderAdditionOutput
            {
                GroupBookingOrderId = x.GroupBookingOrderId,
                AdditionName = x.AdditionName,
                Amount = x.Amount,
                CurrencyCode = x.CurrencyCode,
                Quantity = x.Quantity,
                Remark = x.Remark
            }),
            GroupBookingOrderPayments = detailOutput?.GroupBookingOrderPayments,
            OrderPaymentAdjusts = detailOutput.OrderPaymentAdjusts,
            GroupBookingOrderId = detailOutput?.GroupBookingOrderId,
            BedType = detailOutput?.BedType,
            Guarantee = detailOutput?.Guarantee,
            OrderCommission = detailOutput?.OrderCommission == null ? null : new BffOrderCommissionOutput()
            {
                AgencyCommisionStatus = detailOutput?.OrderCommission.AgencyCommisionStatus,
                AgencyCommissionFee = detailOutput?.OrderCommission.AgencyCommissionFee,
                AgencyCommissionRate = detailOutput?.OrderCommission.AgencyCommissionRate,
                CurrencyCode = detailOutput?.PaymentCurrencyCode
            },
            ExclusivePrivileges = detailOutput?.ExclusivePrivileges,
            RoomDescribe = detailOutput?.RoomDescribe,

        };
        if (result.HotelOrder?.ConfirmByMins.HasValue is not true)
            result.HotelOrder.ConfirmByMins = 120;//默认120分钟 确认时长
        
        var gdsSupplierApiType = new List<SupplierApiType>
        {
            SupplierApiType.GDS,
            SupplierApiType.Youxia
        };
        // 因为高定的订单明细转换成分销商币种加总会与订单总价不一致，差几分钱的，所以显示的是供应商币种数据
        if (gdsSupplierApiType.Contains(result.HotelOrder.SupplierApiType))
        {
            result.BaseOrder.OrgAmount = detailOutput.OrgAmount;
            result.BaseOrder.OrgCurrencyCode = detailOutput.OrgCurrencyCode;
            result.CalendarPrices = detailOutput.CalendarPrices.Select(x => new CalendarPriceBase
            {
                Date = x.Date,
                PaymentCurrencyCode = x.CostCurrencyCode,
                SalePrice = x.CostPrice,
            });
            result.Tax = new PriceItem();
            result.Tax.Price = detailOutput.HotelOrderPrices.Sum(x => x.CostPrice);
            result.Tax.CurrencyCode = detailOutput.HotelOrderPrices.FirstOrDefault()?.CostCurrencyCode;
        }

        //补充English name  (国家+城市+地址）（电话）,roomnameenglish
        if (result.HotelOrder.SupplierApiType is SupplierApiType.Hop)
        {
            if (result.HotelOrder.ResourceHotelId is null) return Ok(result);
            var hotelDetail = await _resourceApiCaller.GetHotelDetail(result.HotelOrder.ResourceHotelId.Value);
            if (hotelDetail is null) return Ok(result);
            result.HotelOrder.CountryCode = hotelDetail.CountryCode;
            result.HotelOrder.HotelAddress = hotelDetail.Address;
            result.HotelOrder.EnHotelAddress = hotelDetail.ENAddress;
            result.HotelOrder.HotelTelePhone = hotelDetail.Telephone;
            result.HotelOrder.HotelCountryName = hotelDetail.CountryName;
            result.HotelOrder.EnHotelCountryName = hotelDetail.EnCountryName;
            result.HotelOrder.HotelCityName = hotelDetail.CityName;
            result.HotelOrder.EnHotelCityName = hotelDetail.EnCityName;
            if (result.HotelOrder.BedTypes is null)
            {
                var room = await _resourceApiCaller.GetRoomByRoomId(result.HotelOrder.HotelRoomId);
                if (room is not null)
                    result.HotelOrder.BedTypes = _mapper.Map<List<Contracts.Common.Hotel.DTOs.Hotel.BedType>>(room.BedTypes);
            }
        }
        else if (gdsSupplierApiType.Contains(result.HotelOrder.SupplierApiType))
        {
            var hotelDetail = await _resourceApiCaller.GDSHotelDetail(result.HotelOrder.HotelId);
            if (hotelDetail is null) return Ok(result);
            result.HotelOrder.CountryCode = hotelDetail.CountryCode;
            result.HotelOrder.HotelAddress = hotelDetail.Address;
            result.HotelOrder.EnHotelAddress = hotelDetail.ENAddress;
            result.HotelOrder.HotelTelePhone = hotelDetail.Telephone;
            result.HotelOrder.HotelCountryName = hotelDetail.CountryName;
            result.HotelOrder.EnHotelCountryName = hotelDetail.EnCountryName;
            result.HotelOrder.HotelCityName = hotelDetail.CityName;
            result.HotelOrder.EnHotelCityName = hotelDetail.EnCityName;

            if (result.HotelOrder.BedTypes is null)
            {
                var room = await _resourceApiCaller.GetGDSRoomByRoomId(result.HotelOrder.HotelRoomId);
                if (room is not null)
                    result.HotelOrder.BedTypes = _mapper.Map<List<Contracts.Common.Hotel.DTOs.Hotel.BedType>>(room.BedTypes);
            }
        }
        else
        {
            var hotelDetail = await _hotelApiCaller.GetHotelDetail(result.HotelOrder.HotelId);
            if (hotelDetail is null) return Ok(result);
            result.HotelOrder.CountryCode = hotelDetail.CountryCode;
            result.HotelOrder.HotelAddress = hotelDetail.Address;
            result.HotelOrder.EnHotelAddress = hotelDetail.ENAddress;
            result.HotelOrder.HotelTelePhone = hotelDetail.Telephone;
            result.HotelOrder.HotelCountryName = hotelDetail.CountryName;
            result.HotelOrder.EnHotelCountryName = hotelDetail.EnCountryName;
            result.HotelOrder.HotelCityName = hotelDetail.CityName;
            result.HotelOrder.EnHotelCityName = hotelDetail.EnCityName;

            if (result.HotelOrder.BedTypes is null)
            {
                var room = await _hotelApiCaller.GetHotelRoomDetails(result.HotelOrder.HotelRoomId);
                if (room is not null)
                    result.HotelOrder.BedTypes = room.BedType;
            }
        }
        // 计算成长值
        if (detailOutput.SellingChannels == SellingChannels.B2b)
        {
            var priceInfo = detailOutput.CalendarPrices.FirstOrDefault();
            result.BaseOrder.GrowUpValue = await ChangeGrowUpValue((decimal)(detailOutput.PaymentAmount * priceInfo.ExchangeRate), detailOutput.OrderDetail.SupplierApiType);
        }
        //汇智酒店补充主推标记
        if (result.HotelOrder.SupplierApiType is SupplierApiType.Hop && result.HotelOrder.HotelId > 0)
        {
            var apiHotelDetail = await _hotelApiCaller.GetApiHotelDetail(new long[] { result.HotelOrder.HotelId });
            if (apiHotelDetail.Any())
                result.HotelOrder.StaffTag = apiHotelDetail.FirstOrDefault().StaffTag;
        }

        return Ok(result);
    }

    ///// <summary>
    ///// OpenApi查单
    ///// </summary>
    //[HttpPost]
    //[ProducesResponseType(typeof(DetailByAgencyOutput), (int)HttpStatusCode.OK)]
    //[SwaggerResponseExt(default, ErrorTypes.Order.NoMatchingData, ErrorTypes.Order.OrderNotFind)]
    //public async Task<IActionResult> Detail(DetailByAgencyInput input)
    //{
    //    var user = HttpContext.User.ParseUserInfo<CurrentUser>();
    //    var result = await _orderApiCaller.HotelOrderDetail(new DetailInput
    //    {
    //        BaseOrderId = input.BaseOrderId,
    //        TenantId = user.Tenant,
    //        AgencyId = input.AgencyId,
    //        SellingChannel = input.SellingChannel
    //    });
    //    var output = _mapper.Map<DetailByAgencyOutput>(result);
    //    //补充English name  (国家+城市+地址）（电话）,roomnameenglish
    //    if (result.OrderDetail.SupplierApiType is SupplierApiType.Hop)
    //    {
    //        var hotelDetail = await _resourceApiCaller.GetHotelDetail(result.OrderDetail.HotelId);
    //        result.OrderDetail.HotelAddress = hotelDetail.Address;
    //        result.OrderDetail.HotelTelePhone = hotelDetail.Telephone;
    //        result.OrderDetail.HotelCountryName = hotelDetail.CountryName;
    //        result.OrderDetail.HotelCityName = hotelDetail.CityName;
    //    }
    //    else
    //    {
    //        var hotelDetail = await _hotelApiCaller.GetHotelDetail(result.OrderDetail.HotelId);
    //        result.OrderDetail.HotelAddress = hotelDetail.Address;
    //        result.OrderDetail.HotelTelePhone = hotelDetail.Telephone;
    //        result.OrderDetail.HotelCountryName = hotelDetail.CountryName;
    //        result.OrderDetail.HotelCityName = hotelDetail.CityName;
    //    }
    //    return Ok(output);
    //}

    private async Task<List<HotelCalendarPriceDto>> GetHotelCalendarPrices(long priceGroupId, HotelAgencyChannelPriceInput input)
    {
        AgencyChannelPriceSettingDto agencyChannelPriceSetting = null;
        switch (input.SupplierApiType)
        {
            case SupplierApiType.None:
                var agencyChannelPriceSettings = await QueryAgencyChannelPrice(priceGroupId, input.ChannelProductType, input.ProductId, input.SkuId);
                var setting = agencyChannelPriceSettings.First(x => x.ProductId == input.ProductId && x.SkuId == input.SkuId);
                agencyChannelPriceSetting = setting;
                break;
            case SupplierApiType.Hop:
                agencyChannelPriceSetting = await GetHuiZhiHotelPriceSetting(priceGroupId, input.ProductId, input.StaffTag, input.IsReunionRoomTag, input.Tag);
                break;
            default:
                throw new NotImplementedException();
                break;
        }

        var priceExchangeRate = await GetOrderPriceExchange(new OrderPriceExchangeRateInput
        {
            CostCurrencyCode = input.CostCurrencyCode,
            SaleCurrencyCode = input.SaleCurrencyCode,
            PaymentCurrencyCode = input.PaymentCurrencyCode,
        });
        decimal costExchangeRate = priceExchangeRate.CostExchangeRate;
        decimal exchangeRate = priceExchangeRate.ExchangeRate;
        var calendarPrices = (from item in input.DatePrices
                              let channelPrice = CalcChannelPrice(agencyChannelPriceSetting, item.SalePrice, item.CostPrice, costExchangeRate)
                              select new HotelCalendarPriceDto
                              {
                                  Date = item.Date,
                                  PaymentCurrencyCode = input.PaymentCurrencyCode,
                                  SalePrice = Math.Round(channelPrice * exchangeRate, 2),
                                  CostCurrencyCode = input.CostCurrencyCode,
                                  CostPrice = item.CostPrice ?? 0,
                                  OrgPriceCurrencyCode = input.SaleCurrencyCode,
                                  OrgPrice = channelPrice,
                                  CostExchangeRate = costExchangeRate,
                                  ExchangeRate = exchangeRate,
                                  Stock = item.Stock,
                              })
            .ToList();

        //如果是价格变更，或者无试单价格数据 返回最新价格
        if (input.Code == CheckPriceStrategySaleCode.PriceChanged
            || input.CheckDatePrices?.Any() is not true)
        {
            return calendarPrices;
        }

        var checkCalendarPrices = (from item in input.CheckDatePrices
                                   let channelPrice = CalcChannelPrice(agencyChannelPriceSetting, item.SalePrice, item.CostPrice, costExchangeRate)
                                   select new HotelCalendarPriceDto
                                   {
                                       Date = item.Date,
                                       PaymentCurrencyCode = input.PaymentCurrencyCode,
                                       SalePrice = Math.Round(channelPrice * exchangeRate, 2),
                                       CostCurrencyCode = input.CostCurrencyCode,
                                       CostPrice = item.CostPrice ?? 0,
                                       OrgPriceCurrencyCode = input.SaleCurrencyCode,
                                       OrgPrice = channelPrice,
                                       CostExchangeRate = costExchangeRate,
                                       ExchangeRate = exchangeRate,
                                       Stock = item.Stock,
                                   })
            .ToList();
        foreach (var item in checkCalendarPrices)
        {
            var price = calendarPrices.FirstOrDefault(x => x.Date == item.Date);
            if (price != null)
            {
                item.CostPrice = price.CostPrice;//采购价取最新价格
            }
        }
        return checkCalendarPrices;
    }

    private async Task<List<AgencyChannelPriceSettingOutput>> QueryAgencyChannelPrice(long priceGroupId,
       ChannelProductType productType, long productId, long skuId)
    {
        //查询价格分组下的产品配置数据
        var request = new QueryChannelPriceInput
        {
            PriceGroupId = priceGroupId,
            ProductType = new[] { productType },
            ProductIds = new[] { productId },
            SkuIds = new[] { skuId }
        };

        var responses = await _productApiCaller.QueryAgencyChannelPrices(request);

        return responses;
    }

    private decimal CalcChannelPrice(AgencyChannelPriceSettingDto setting,
    decimal salePrice,
    decimal? costPrice,
    decimal costExchangeRate)
    {
        var calcValue = setting.BasePriceType switch
        {
            ChannelBasePriceType.SalePrice => salePrice,
            ChannelBasePriceType.CostPrice => costPrice!.Value * costExchangeRate,
            _ => throw new NotImplementedException()
        };
        var value = setting.PriceSettingType switch
        {
            ChannelPriceSettingType.AddValue => Math.Round(calcValue + setting.PriceSettingValue, 2),
            ChannelPriceSettingType.SubtractValue => Math.Round(calcValue - setting.PriceSettingValue, 2),
            ChannelPriceSettingType.AddRate => Math.Round(calcValue * (1 + setting.PriceSettingValue * 0.01m), 2),
            ChannelPriceSettingType.SubtractRate => Math.Round(calcValue * (1 - setting.PriceSettingValue * 0.01m),
                2),
            _ => 0
        };

        if (value < 0) value = 0;

        return value;
    }

    private async Task<OrderPriceExchangeRateOutput> GetOrderPriceExchange(OrderPriceExchangeRateInput input)
    {

        var costCurrencyCode = input.CostCurrencyCode;
        var saleCurrencyCode = input.SaleCurrencyCode;
        var paymentCurrencyCode = input.PaymentCurrencyCode;
        List<GetExchangeRatesInput> exchangeRatesInputs = new();
        var costEqualsSale = costCurrencyCode.Equals(saleCurrencyCode, StringComparison.OrdinalIgnoreCase);
        if (!costEqualsSale)
        {
            exchangeRatesInputs.Add(new GetExchangeRatesInput
            {
                BaseCurrencyCode = costCurrencyCode,
                TargetCurrencyCode = saleCurrencyCode
            });
        }
        var saleEqualsPayment = saleCurrencyCode.Equals(paymentCurrencyCode, StringComparison.OrdinalIgnoreCase);
        if (!saleEqualsPayment)
        {
            exchangeRatesInputs.Add(new GetExchangeRatesInput
            {
                BaseCurrencyCode = saleCurrencyCode,
                TargetCurrencyCode = paymentCurrencyCode
            });
        }
        var exchangeRates = await _paymentApiCaller.GetExchangeRates(exchangeRatesInputs);
        return new OrderPriceExchangeRateOutput
        {
            CostExchangeRate = costEqualsSale ? 1 : exchangeRates
                .Where(x => x.BaseCurrencyCode == costCurrencyCode && x.TargetCurrencyCode == saleCurrencyCode)
                .First().ExchangeRate,
            ExchangeRate = saleEqualsPayment ? 1 : exchangeRates
                .Where(x => x.BaseCurrencyCode == saleCurrencyCode && x.TargetCurrencyCode == paymentCurrencyCode)
                .First().ExchangeRate,
        };
    }

    private async Task<OrderUserCouponOutput> GetOrderUserCoupon(GetOrderUserCouponsInput request)
    {
        var response = await _marketingApiCaller.GetOrderUserCoupons(request);
        var result = response.FirstOrDefault(x => x.UserCouponId == request.UserCouponId);
        return result;
    }

    private async Task<AgencyChannelPriceSettingDto> GetHuiZhiHotelPriceSetting(long priceGroupId, long productId,
        bool staffTag, bool isReunionRoomTag, SellHotelTag? tag)
    {
        var result = new AgencyChannelPriceSettingDto();
        var huiZhiHotelChannelPriceSettings = await _productApiCaller.GetHuiZhiHotelChannelPrice(priceGroupId);
        var sellHotelTags = new List<SellHotelTag>();
        if (tag.HasValue) sellHotelTags.Add(tag.Value);

        var checkHuiZhiSellChannelSettings = _hotelService.CheckHuiZhiSellChannelSettings(
            huiZhiHotelChannelPriceSettings,
            productId,
            staffTag,
            isReunionRoomTag,
            sellHotelTags);

        result = _hotelService.GetStrategyChannelPriceSetting(staffTag, isReunionRoomTag, tag,
            checkHuiZhiSellChannelSettings.agencyChannelPriceSetting);

        if (result == null)
            throw new BusinessException(ErrorTypes.Order.ChannelPriceSettingChange);

        return result;
    }

    /// <summary>
    /// 分销商web-订单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<Contracts.Common.Order.DTOs.HotelOrder.SearchOutput, IList<HotelStatusCountOutput>>), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Permission.OrderViewallDataNotExist)]
    [Authorize]
    public async Task<IActionResult> Search(Contracts.Common.Order.DTOs.HotelOrder.SearchInput input)
    {

        CurrentUser currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        input.AgencyId = currentUser.Provider;
        input.SellingPlatform = new SellingPlatform[] { SellingPlatform.B2BWeb, SellingPlatform.B2BApplet };
        input.IsDisplayCost = false;
        if (input.CheckInDate.HasValue && !input.CheckInDateEnd.HasValue)
        {
            input.CheckInDateEnd = input.CheckInDate;
        }
        if (input.CheckOutDate.HasValue && !input.CheckOutDateEnd.HasValue)
        {
            input.CheckOutDateEnd = input.CheckOutDate;
        }
        if (input.CreateTime.HasValue && !input.CreateTimeEnd.HasValue)
        {
            input.CreateTimeEnd = input.CreateTime;
        }
        var result = await _orderApiCaller.HotelOrderSearch(input);
        return Ok(result);
    }

    /// <summary>
    /// 酒店订单各个状态数量统计
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(IList<HotelStatusCountOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetHotelOrderStatusCounts()
    {
        CurrentUser currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var sellingPlatform = new SellingPlatform[] { SellingPlatform.B2BWeb, SellingPlatform.B2BApplet };
        GetHotelStatusCountInput input = new()
        {
            AgencyId = currentUser.Provider,
            SellingPlatform = sellingPlatform
        };
        var result = await _orderApiCaller.GetHotelOrderStatusCounts(input);
        return Ok(result);
    }

    /// <summary>
    /// 导出酒店订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<ExportDataOutput>), (int)HttpStatusCode.OK)]
    [Authorize]
    public async Task<IActionResult> ExportData(ExportDataInput input)
    {

        input.AgencyId = HttpContext.User.ParseUserInfo<CurrentUser>().Provider;
        input.SellingPlatforms = new SellingPlatform[] { SellingPlatform.B2BWeb, SellingPlatform.B2BApplet };
        input.IsDisplayCost = false;
        if (input.CheckInDate.HasValue && !input.CheckInDateEnd.HasValue)
        {
            input.CheckInDateEnd = input.CheckInDate;
        }
        if (input.CheckOutDate.HasValue && !input.CheckOutDateEnd.HasValue)
        {
            input.CheckOutDateEnd = input.CheckOutDate;
        }
        if (input.CreateTime.HasValue && !input.CreateTimeEnd.HasValue)
        {
            input.CreateTimeEnd = input.CreateTime;
        }
        var exportDataOutputs = await _orderApiCaller.ExportDataHotelOrder(input);
        return Ok(exportDataOutputs);
    }

    /// <summary>
    /// 统计酒店订单数
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(List<CountByGroupBffOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> CountByGroup(PagingInput input)
    {
        List<CountByGroupBffOutput> result = null;
        var now = DateTime.Now;
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var tenantId = HttpContext.GetTenantId();

        if (await _redisClient.KeyExistsAsync(GetCacheKey(tenantId, currentUser.UserId)))
        {
            result = await GetCountByGroupCache(tenantId, currentUser.UserId);
            return Ok(result);
        }

        var countByGroupInput = new CountByGroupInput();
        countByGroupInput.StartTime = now.AddMonths(-3);
        countByGroupInput.EndTime = now;
        countByGroupInput.UserId = currentUser.UserId;
        countByGroupInput.PageIndex = input.PageIndex;
        countByGroupInput.PageSize = input.PageSize;

        var countByGroup = await _orderApiCaller.CountByGroup(countByGroupInput);
        result = _mapper.Map<List<CountByGroupBffOutput>>(countByGroup);
        //设置缓存
        await SetCountByGroupCache(tenantId, currentUser.UserId, result);
        return Ok(result);
    }

    /// <summary>
    /// 导出酒店入住单pdf
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(byte[]), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> ExportHoterOrderPdf(long baseOrderId, string language = "zh")
    {
        var detailOutput = await _orderApiCaller.HotelOrderDetail(new DetailInput
        {
            BaseOrderId = baseOrderId,
        });
        var gdsSupplierApiTypes = new List<SupplierApiType> {
            SupplierApiType.GDS,
            SupplierApiType.Youxia
        };

        var fileName = string.Empty;
        if (!gdsSupplierApiTypes.Contains(detailOutput.OrderDetail.SupplierApiType))
        {
            var esHotelInfo = await _hotelApiCaller.GetEsHotelDetailV2(detailOutput.OrderDetail.HotelId);
            if (esHotelInfo.Any())
            {
                var esHotel = esHotelInfo.First();
                //优先展示本地酒店
                var localHotel = esHotel.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.None);
                if (localHotel != null)
                {//查询本地酒店
                    var localHotelDetail = await _hotelApiCaller.GetHotelDetail(localHotel.HotelId);
                    detailOutput.OrderDetail.HotelTelePhone = localHotelDetail?.Telephone;
                    detailOutput.OrderDetail.HotelAddress = localHotelDetail?.Address;
                    detailOutput.OrderDetail.EnHotelAddress = localHotelDetail?.ENAddress;
                }
                else
                {//查询资源库酒店信息
                    var resourceHotelDetail = await _resourceApiCaller.GetHotelDetail(esHotel.ResourceHotelId);
                    detailOutput.OrderDetail.HotelTelePhone = resourceHotelDetail?.Telephone;
                    detailOutput.OrderDetail.HotelAddress = resourceHotelDetail?.Address;
                    detailOutput.OrderDetail.EnHotelAddress = resourceHotelDetail?.ENAddress;
                }
                fileName = language == "zh" ? esHotelInfo.FirstOrDefault().ZHName : esHotelInfo.FirstOrDefault().ENName;
            }
        }
        else
        {
            var gdsHotel = await _resourceApiCaller.GDSHotelDetail(detailOutput.OrderDetail.HotelId);
            detailOutput.OrderDetail.HotelTelePhone = gdsHotel?.Telephone;
            detailOutput.OrderDetail.HotelAddress = gdsHotel?.Address;
            detailOutput.OrderDetail.EnHotelAddress = gdsHotel?.ENAddress;

            fileName = language == "zh" ? gdsHotel.ZHName : gdsHotel.ENName;
        }

        var input = _mapper.Map<ExportHotelOrderPdfInput>(detailOutput);
        input.OrderDetail.BaseOrderId = baseOrderId;
        input.ExportHotelOrderType = ExportHotelOrderType.CheckIn;
        input.Language = language;

        var file = await _orderApiCaller.ExportHotelOrderPdf(input);
        return File(file, "application/pdf",
            $"{DateTime.Now.ToString("yyyyMMdd")}-{fileName}-{baseOrderId}.pdf");
    }

    /// <summary>
    /// 导出酒店确认单pdf
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(byte[]), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> ExportConfirmHotelOrderPdf(long baseOrderId, string language = "zh")
    {
        var detailOutput = await _orderApiCaller.HotelOrderDetail(new DetailInput
        {
            BaseOrderId = baseOrderId,
        });
        var gdsSupplierApiTypes = new List<SupplierApiType> {
            SupplierApiType.GDS,
            SupplierApiType.Youxia
        };

        if (!gdsSupplierApiTypes.Contains(detailOutput.OrderDetail.SupplierApiType))
        {
            var esHotelInfo = await _hotelApiCaller.GetEsHotelDetailV2(detailOutput.OrderDetail.HotelId);
            if (esHotelInfo.Any())
            {
                var esHotel = esHotelInfo.First();
                //优先展示本地酒店
                var localHotel = esHotel.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.None);
                if (localHotel != null)
                {//查询本地酒店
                    var localHotelDetail = await _hotelApiCaller.GetHotelDetail(localHotel.HotelId);
                    detailOutput.OrderDetail.HotelTelePhone = localHotelDetail?.Telephone;
                    detailOutput.OrderDetail.HotelAddress = localHotelDetail?.Address;
                    detailOutput.OrderDetail.EnHotelAddress = localHotelDetail?.ENAddress;
                }
                else
                {//查询资源库酒店信息
                    var resourceHotelDetail = await _resourceApiCaller.GetHotelDetail(esHotel.ResourceHotelId);
                    detailOutput.OrderDetail.HotelTelePhone = resourceHotelDetail?.Telephone;
                    detailOutput.OrderDetail.HotelAddress = resourceHotelDetail?.Address;
                    detailOutput.OrderDetail.EnHotelAddress = resourceHotelDetail?.ENAddress;
                }
            }
        }
        else
        {
            var gdsHotel = await _resourceApiCaller.GDSHotelDetail(detailOutput.OrderDetail.HotelId);
            detailOutput.OrderDetail.HotelTelePhone = gdsHotel?.Telephone;
            detailOutput.OrderDetail.HotelAddress = gdsHotel?.Address;
            detailOutput.OrderDetail.EnHotelAddress = gdsHotel?.ENAddress;
        }

        var input = _mapper.Map<ExportHotelOrderPdfInput>(detailOutput);
        input.OrderDetail.BaseOrderId = baseOrderId;
        input.OrderDetail.TotalAmount = detailOutput.PaymentAmount;
        input.OrderDetail.PaymentCurrencyCode = detailOutput.PaymentCurrencyCode;
        input.ExportHotelOrderType = ExportHotelOrderType.Confirm;
        input.Language = language;

        var file = await _orderApiCaller.ExportHotelOrderPdf(input);

        return File(file, "application/pdf", language == "zh" ? "酒店确认单.pdf" : "Hotel Confirmation Slip.pdf");
    }

    /// <summary>
    /// 修改来源单号
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(byte[]), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.NoMatchingData)]
    public async Task<IActionResult> EditChannelOrderNo(EditChannelOrderNoBffInput input)
    {
        var editInput = _mapper.Map<EditChannelOrderNoInput>(input);
        editInput.ChannelOrderNo = string.Join(",", input.ChannelOrderNos);
        await _orderApiCaller.EditChannelOrderNoByAgency(editInput);
        return Ok();
    }

    /// <summary>
    /// 修改团号
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(byte[]), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.NoMatchingData)]
    public async Task<IActionResult> EditGroupNo(EditGroupNoBffInput input)
    {
        var editInput = _mapper.Map<EditGroupNoInput>(input);
        await _orderApiCaller.EditGroupNoByAgency(editInput);
        return Ok();
    }

    private static string GetCacheKey(long tenantId, long userId) => $"vebk:hotelOrder:countByGroup:{tenantId}:{userId}";
    private async Task SetCountByGroupCache(long tenantId, long userId, List<CountByGroupBffOutput> objs)
    {
        var key = GetCacheKey(tenantId, userId);
        await _redisClient.StringSetAsync(key, objs, TimeSpan.FromMinutes(60));
    }

    private async Task<List<CountByGroupBffOutput>> GetCountByGroupCache(long tenantId, long userId)
    {
        var key = GetCacheKey(tenantId, userId);
        return await _redisClient.StringGetAsync<List<CountByGroupBffOutput>>(key);
    }

    private async Task<int> ChangeGrowUpValue(decimal amount, SupplierApiType supplierApiType)
    {
        var transformToValue = 0;
        var result = await _tenantApiCaller.GetLevelConfigByCurrentTenantId();
        if (result.Enable)
        {
            var businessType = supplierApiType switch
            {
                SupplierApiType.Hop => AgencyLevelBusinessType.HuiZhiHotel,
                _ => AgencyLevelBusinessType.Hotel
            };
            var expensetransform = result.ExpenseTransforms.FirstOrDefault(x => x.Enable && x.BusinessType == businessType);
            if (expensetransform != null)
            {
                transformToValue = (int)(amount * expensetransform.TransformToValue / expensetransform.TransformFromValue);
            }
        }
        return transformToValue;
    }

    #region GDS

    /// <summary>
    /// 试单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(BffGDSHotelPriceCheckOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GDSPriceCheck(BffGDSHotelPriceCheckInput input)
    {
        var priceRequest = new GDSHotelPriceCheckInput()
        {
            RateKey = input.RateKey,
            IsOriginalJson = true,
            HotelId = input.HotelId,
            RoomId = input.RoomId,
        };
        var priceReponse = await _orderApiCaller.GDSPriceCheck(priceRequest);
        var res = _mapper.Map<BffGDSHotelPriceCheckOutput>(priceReponse);
        res.Room = _mapper.Map<GDSHotelRoomBffOutput>(priceReponse.Room);

        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var agency = await _tenantApiCaller.GetAgencyDetail(currentUser.Provider);
        var commissionSettings = await _productApiCaller.QueryAgencyChannelCommissionSettings(new QueryAgencyChannelCommissionInput
        {
            PriceGroupId = agency.PriceGroupId ?? 0,
            ProductTypes = new List<CommissionProductType> { CommissionProductType.GDSHotel },
        });
        var commissionSetting = commissionSettings.FirstOrDefault();
        var priceStrategy = res?.Room?.PriceStrategies?.FirstOrDefault();
        decimal? supplierCommissionFee = priceStrategy?.Commission?.Amount;
        decimal? suppliercommissionRate = priceStrategy?.Commission?.Percent;
        var paymentAmount = priceStrategy.AmountAfterTax;
        if (supplierCommissionFee == null && suppliercommissionRate != null)
        {
            supplierCommissionFee = decimal.Round((decimal)(paymentAmount * suppliercommissionRate / 100), 2, MidpointRounding.AwayFromZero);
        }
        if (supplierCommissionFee != null && suppliercommissionRate == null && paymentAmount > 0)
        {
            suppliercommissionRate = decimal.Round((decimal)(supplierCommissionFee / paymentAmount) * 100, 2, MidpointRounding.AwayFromZero);
        }
        var commissionRate = (suppliercommissionRate * commissionSetting?.CommissionSettingValue) / 100;
        var commissionFee = supplierCommissionFee * commissionSetting?.CommissionSettingValue / 100;
        // 保留两位小数并向下取整
        commissionFee = Math.Floor(commissionFee!.Value * 100) / 100;
        priceStrategy.Commission.CurrencyCode = priceReponse.ConvertedCurrencyCode;
        priceStrategy.Commission.Amount = commissionFee ?? 0;
        priceStrategy.Commission.Percent = commissionRate ?? 0;
        priceStrategy.SupplierApiType = SupplierApiType.GDS;
        priceStrategy.HotelId = input.HotelId;

        return Ok(res);
    }

    /// <summary>
    /// 下单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>

    private async Task<CreateHotelOrderInput> GDSCreate(CreateInput input, CurrentUser currentUser)
    {
        var tenantId = currentUser.Tenant;
        var agencyId = currentUser.Provider;
        var agency = await _tenantApiCaller.GetAgencyDetail(agencyId);
        var commissionProductType = input.SupplierApiType switch
        {
            SupplierApiType.GDS => CommissionProductType.GDSHotel,
            SupplierApiType.Youxia => CommissionProductType.YouXiaHotel,
        };
        var commissionSettings = await _productApiCaller.QueryAgencyChannelCommissionSettings(new QueryAgencyChannelCommissionInput
        {
            PriceGroupId = agency.PriceGroupId ?? 0,
            ProductTypes = new List<CommissionProductType> { commissionProductType },
        });
        var commissionSetting = commissionSettings.FirstOrDefault();
        if (commissionSetting == null)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        GDSHotelRoomInfo room = null;
        string rateKey = string.Empty;
        string bookingKey = input.BookingKey;
        DateTime? expireStartTime = null;

        switch (input.SupplierApiType)
        {
            case SupplierApiType.GDS:

                var priceCheckInfo = await _orderApiCaller.GetGDSPriceCheckInfo(input.BookingKey);
                room = priceCheckInfo.PriceCheckInfo.Room;
                rateKey = priceCheckInfo.RateKey;
                expireStartTime = priceCheckInfo.CreateTime.AddMinutes(5);
                // bookingKey 5 分钟内有效
                if (expireStartTime < DateTime.Now)
                {
                    throw new BusinessException(ErrorTypes.Order.OrderTimeout);
                }
                break;
            case SupplierApiType.Youxia:
                long.TryParse(input.RateKey, out var priceStrategyId);
                var youxiaPriceStrategy = await _resourceApiCaller.PriceStrategyDetail(new SearchYouxiaHotelPriceStrategyDetailInput()
                {
                    Adults = input.AdultNum,
                    LiveDate = input.CheckIn,
                    LeaveDate = input.CheckOut,
                    PriceStrategyId = priceStrategyId,
                    TenantId = tenantId,
                });
                room = _mapper.Map<GDSHotelRoomInfo>(youxiaPriceStrategy);
                rateKey = input.RateKey?.ToString() ?? string.Empty;
                bookingKey = youxiaPriceStrategy?.PriceStrategies?.FirstOrDefault()?.BookingKey;
                break;
            default:
                throw new BusinessException(ErrorTypes.Order.SupplierApiTypeError);
        }

        if (string.IsNullOrWhiteSpace(bookingKey) || room == null)
        {
            throw new BusinessException(ErrorTypes.Inventory.ProductInventoryNotEnough);
        }
        var supplierSettings = await _tenantApiCaller.GetSupplierApiSettingInfos(new GetApiSettingInfosInput()
        {
            SupplierApiType = input.SupplierApiType,
            TenantId = tenantId,
            SupplierEnabled = true
        });
        var supplierSetting = supplierSettings.FirstOrDefault();
        if (supplierSetting == null)
        {
            throw new BusinessException(ErrorTypes.Inventory.ProductInventoryNotEnough);
        }

        var localHotelDetail = await _resourceApiCaller.GDSHotelDetail(input.HotelId);
        var thirdHotelId = localHotelDetail.ThirdHotelId;

        ////每日价格
        var calendarPrices = new List<HotelCalendarPriceDto>();
        var priceStrategy = room.PriceStrategies.FirstOrDefault();
        //多币种
        string paymentCurrencyCode = priceStrategy.CurrencyCode;
        var dateRates = priceStrategy.CalendarPrices;

        HotelApiOrderInfo? hotelApiOrderInfo = new HotelApiOrderInfo
        {
            HotelId = input.HotelId.ToString(),
            RoomId = input.RoomId.ToString(),
            PriceStrategyId = priceStrategy.Id ?? "0",
            ResourceHotelId = 0,
            SupplierApiType = input.SupplierApiType,
        };

        foreach (var price in dateRates)
        {
            HotelCalendarPriceDto calendarPriceDto = new()
            {
                Date = price.Date,
                PriceType = OrderPriceType.Default,
                CostCurrencyCode = price.OrgCurrencyCode,
                CostPrice = price.OrgPrice,
                OrgPrice = price.Price,
                OrgPriceCurrencyCode = price.CurrencyCode,
                PaymentCurrencyCode = paymentCurrencyCode,
                SalePrice = price.Price,
                CostExchangeRate = 1,
                ExchangeRate = price.OrgPrice == 0 ? 1 : (price.Price / price.OrgPrice),
            };
            calendarPrices.Add(calendarPriceDto);
        }
        var paymentAmount = priceStrategy.AmountAfterTax;

        List<HotelOrderPriceDto> orderMultPrices = new();
        if (priceStrategy.Taxes != null)
        {
            orderMultPrices.Add(new HotelOrderPriceDto()
            {
                CostCurrencyCode = priceStrategy.Taxes.OrgCurrencyCode,
                CostPrice = priceStrategy.Taxes.OrgAmount,
                CostPriceType = OrderPriceType.Default,
                OrgPrice = priceStrategy.Taxes.Amount,
                OrgPriceCurrencyCode = priceStrategy.Taxes.CurrencyCode,
                OrgCostPrice = priceStrategy.Taxes.Amount,
                PaymentCurrencyCode = paymentCurrencyCode,
                PriceType = OrderPriceType.Default,
                Price = priceStrategy.Taxes.Amount,
                CostExchangeRate = 1,
                ExchangeRate = priceStrategy.Taxes.Amount == 0 ? 1 : (priceStrategy.Taxes.OrgAmount / priceStrategy.Taxes.Amount),
                Quantity = 1,
                OrderSubType = HotelOrderSubType.Tax,
            });
        }
        if (priceStrategy.Fees != null)
        {
            orderMultPrices.Add(new HotelOrderPriceDto()
            {
                CostCurrencyCode = priceStrategy.Fees.OrgCurrencyCode,
                CostPrice = priceStrategy.Fees.OrgAmount,
                CostPriceType = OrderPriceType.Default,
                OrgPrice = priceStrategy.Fees.Amount,
                OrgPriceCurrencyCode = priceStrategy.Fees.CurrencyCode,
                OrgCostPrice = priceStrategy.Fees.Amount,
                PaymentCurrencyCode = paymentCurrencyCode,
                PriceType = OrderPriceType.Default,
                Price = priceStrategy.Fees.Amount,
                CostExchangeRate = 1,
                ExchangeRate = priceStrategy.Fees.Amount == 0 ? 1 : (priceStrategy.Fees.OrgAmount / priceStrategy.Fees.Amount),
                Quantity = 1,
                OrderSubType = HotelOrderSubType.Service,
            });
        }
        decimal? supplierCommissionFee = priceStrategy.Commission?.Amount;
        decimal? suppliercommissionRate = priceStrategy.Commission?.Percent;
        if (commissionSetting.BaseCommissionType == ChannelBaseCommissionType.FixedRatio)
        {
            suppliercommissionRate = commissionSetting.BaseValue;
        }
        if ((supplierCommissionFee == null || supplierCommissionFee <= 0) && suppliercommissionRate != null)
        {
            supplierCommissionFee = decimal.Round((decimal)(paymentAmount * suppliercommissionRate / 100), 2, MidpointRounding.AwayFromZero);
        }
        if (supplierCommissionFee > 0 && (suppliercommissionRate == null && suppliercommissionRate <= 0) && paymentAmount > 0)
        {
            suppliercommissionRate = decimal.Round((decimal)(supplierCommissionFee / paymentAmount) * 100, 2, MidpointRounding.AwayFromZero);
        }
        var commissionRate = (suppliercommissionRate * commissionSetting.CommissionSettingValue) / 100;
        var commissionFee = supplierCommissionFee * commissionSetting.CommissionSettingValue / 100;
        // 保留两位小数并向下取整
        commissionFee = Math.Floor(commissionFee!.Value * 100) / 100;
        var commission = new OrderCommissionDto()
        {
            AgencyCommisionStatus = CommisionStatus.NotCredited,
            AgencyCommissionFee = commissionFee,
            AgencyCommissionRate = commissionRate,
            SupplierCommissionFee = supplierCommissionFee,
            SupplierCommissionRate = suppliercommissionRate,
            SupplierCommisionStatus = CommisionStatus.NotCredited,
        };
        var createPriceStrategy = _mapper.Map<CheckSalePriceStrategyDto>(priceStrategy);
        createPriceStrategy.SupplierId = supplierSetting.SupplierId;
        var createRoom = _mapper.Map<CheckSaleRoomDto>(room);
        var roomENName = room.RoomENName;
        var roomZHName = string.IsNullOrEmpty(room.RoomZHName) ? roomENName : room.RoomZHName;
        createRoom.Name = roomZHName;
        createRoom.EnName = roomENName;

        createPriceStrategy.Name = string.IsNullOrEmpty(createPriceStrategy.Name) ? createPriceStrategy.EnName : createPriceStrategy.Name;
        var hotelName = localHotelDetail.ZHName ?? localHotelDetail.ENName;
        List<HotelGuestsInfo> guestsInfos = new();
        foreach (var item in input.Guests)
        {
            guestsInfos.AddRange(item.GuestsInfos.Select(s => new HotelGuestsInfo()
            {
                FirstName = s.FirstName,
                LastName = s.LastName,
                HotelOrderGuestType = s.HotelOrderGuestType,
                Age = s.Age,
                RoomNumber = item.RoomNumber,
                Gender = s.Gender,
            }));
        }
        var createInput = new CreateHotelOrderInput()
        {
            TenantId = currentUser.Tenant,
            SellingPlatform = input.SellingPlatform,
            SellingChannel = SellingChannels.B2b,
            ChannelOrderNo = "",
            CheckIn = input.CheckIn,
            CheckOut = input.CheckOut,
            RoomCount = input.RoomCount,
            Adults = guestsInfos.Count(x => x.HotelOrderGuestType == HotelOrderGuestType.Adult),
            GuestInfos = guestsInfos,
            ContactsName = input.ContactsName,
            ContactsPhoneNumber = input.ContactsPhoneNumber,
            ContactsEmail = input.ContactsEmail,
            Message = input.Message,
            // DiscountItems = discountItems,
            TotalAmount = paymentAmount,
            //DiscountAmount = discountAmount,
            PaymentAmount = paymentAmount,
            PaymentCurrencyCode = paymentCurrencyCode,
            CalendarPrices = calendarPrices,
            SupplierApiType = input.SupplierApiType,
            HotelApiOrderInfo = hotelApiOrderInfo,
            HotelId = input.HotelId,
            HotelName = hotelName,
            HotelEnName = localHotelDetail.ENName,
            IsAutoConfirmRoomStatus = false,
            Room = createRoom,
            PriceStrategy = createPriceStrategy,
            UserInfo = new OrderUserInfo()
            {
                UserId = currentUser.UserId,
                NickName = currentUser.NickName,
                AgencyId = agencyId,
                AgencyName = agency.FullName,
                UserType = Contracts.Common.Order.Enums.UserType.Agency,
                SalespersonId = agency.SalespersonId,
                SalespersonName = agency.SalespersonName,
                VipLevelId = agency.Level ?? 0,
                VipLevelName = agency.LevelName ?? string.Empty,
            },
            GDSHotelRateInfo = new GDSHotelRateInfo()
            {
                RateKey = rateKey,
                BookingKey = bookingKey,
                AdditionalFeesInclusive = priceStrategy.AdditionalFeesInclusive,
                IncidentalsInclusive = priceStrategy.IncidentalsInclusive,
                LocalFeesInclusive = priceStrategy.LocalFeesInclusive,
                SabreHotelCode = thirdHotelId?.ToString() ?? "",
                TaxInclusive = priceStrategy.TaxInclusive,
                GuaranteeJson = priceStrategy.Guarantee == null ? null : JsonConvert.SerializeObject(priceStrategy.Guarantee),
                BedTypeJson = room.BedType == null ? null : JsonConvert.SerializeObject(room.BedType),
                OrgAmount = priceStrategy.OrgAmountAfterTax,
                OrgCurrencyCode = priceStrategy.OrgCurrencyCode,
                RoomDescribe = room.RoomDescribe,
                ExclusivePrivileges = priceStrategy.ExclusivePrivileges,
            },
            CommissionFee = commissionFee,
            CommissionRate = commissionRate,
            OrderMultPrices = orderMultPrices,
            // ServiceItems = serviceItems, //目前没服务
            ChildrenAges = input.ChildrenAges,
            OrderCommission = commission,
            ExpireStartTime = expireStartTime,
            Nationality = input.Nationality != null
                ? new NationalityInfo
                {
                    CountryCode = input.Nationality.CountryCode,
                    ZHName = input.Nationality.ZHName,
                    ENName = input.Nationality.ENName
                }
                : null
        };

        return createInput;
    }
    #endregion

}
