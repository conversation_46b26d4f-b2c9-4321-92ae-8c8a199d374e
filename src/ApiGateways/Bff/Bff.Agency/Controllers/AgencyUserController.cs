using AutoMapper;
using Bff.Agency.Callers;
using Bff.Agency.Models.AgencyUsers;
using Cit.TokenPersistence.Context;
using Common.Jwt;
using Common.Swagger;
using Common.Utils;
using Contracts.Common.Permission.Const;
using Contracts.Common.Tenant.DTOs.Agency;
using Contracts.Common.Tenant.DTOs.AgencyPasswordRecords;
using Contracts.Common.User.DTOs;
using Contracts.Common.User.DTOs.AgencyUser;
using Contracts.Common.User.DTOs.UserBinding;
using Contracts.Common.User.Enums;
using Contracts.Common.WeChat.DTOs;
using Contracts.Common.WeChat.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Agency.Controllers;

/// <summary>
/// 分销商用户
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class AgencyUserController : ControllerBase
{
    private readonly IUserApiCaller _userApiCaller;
    private readonly IPermissionCaller _permissionCaller;
    private readonly IMapper _mapper;
    private readonly ITokenPersistenceContext _tokenPersistenceContext;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IWechatApiCaller _wechatApiCaller;
    private const int _sysRole = (int)SysRole.Agency;

    public AgencyUserController(IUserApiCaller userApiCaller,
        IPermissionCaller permissionCaller,
        IMapper mapper,
        ITokenPersistenceContext tokenPersistenceContext,
        ITenantApiCaller tenantApiCaller,
        IWechatApiCaller wechatApiCaller)
    {
        _userApiCaller = userApiCaller;
        _permissionCaller = permissionCaller;
        _mapper = mapper;
        _tokenPersistenceContext = tokenPersistenceContext;
        _tenantApiCaller = tenantApiCaller;
        _wechatApiCaller = wechatApiCaller;
    }

    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(PagingModel<AgencyUserDto>))]
    public async Task<IActionResult> Search(Models.AgencyUsers.SearchInput bffInput)
    {
        var input = _mapper.Map<Contracts.Common.User.DTOs.AgencyUser.SearchInput>(bffInput);
        input.AgencyId = HttpContext.User.ParseUserInfo<CurrentUser>().Provider;
        input.SearchType ??= AgencyUserSearchType.Name;
        var users = await _userApiCaller.SearchAgencyUser(input);

        var result = _mapper.Map<PagingModel<AgencyUserSearchOutput>>(users);
        if (result?.Data?.Any() is not true)
            return Ok(result);

        await ProcessAgencyUserData(result.Data);

        return Ok(result);
    }

    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200, typeof(DetailOutput))]
    public async Task<IActionResult> Detail(long id)
    {
        var agencyUserDto = await _userApiCaller.GetAgencyUserDetail(id);
        var agencyAcls = await _permissionCaller.GetAclsByUserId(id);
        var result = new DetailOutput
        {
            AgencyAcls = agencyAcls,
            AgencyUserDto = agencyUserDto
        };
        return Ok(result);
    }

    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.User.AccountIsExist, ErrorTypes.User.AgencyNotAllowedToExistHotelLabelDirect)]
    [SwaggerResponseExt(default, ErrorTypes.User.EmailIsBound, ErrorTypes.User.PhoneNumberIsDisabled)]
    public async Task<IActionResult> Add(Models.AgencyUsers.AddInput input)
    {
        if (input.AclKeys.Contains(AgencyUserAclKey.HotelLabelDirect))
            throw new BusinessException(ErrorTypes.User.AgencyNotAllowedToExistHotelLabelDirect);

        var emailCheck = await _userApiCaller.AgencyUsersAnyAsync(new AgencyUserVerifyInput { Email = input.Email });
        if (emailCheck)
        {
            throw new BusinessException(ErrorTypes.User.EmailIsBound);
        }

        var phoneCheck = await _userApiCaller.AgencyUsersAnyAsync(new AgencyUserVerifyInput { PhoneNumber = input.PhoneNumber });
        if (phoneCheck)
        {
            throw new BusinessException(ErrorTypes.User.PhoneNumberIsDisabled);
        }

        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var addRequest = _mapper.Map<Contracts.Common.User.DTOs.AgencyUser.AddInput>(input);
        addRequest.AgencyId = currentUser.Provider;
        var addAgencyUserId = await _userApiCaller.AddAgencyUser(addRequest);
        await _permissionCaller.SetAgencyUserACL(new Contracts.Common.Permission.DTOs.ACL.SetUserAclDTO
        {
            UserId = addAgencyUserId,
            AclKeys = input.AclKeys,
            CreatorId = currentUser.UserId,
            CreatorName = currentUser.NickName
        });
        return Ok();
    }

    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.User.AccountIsExist, ErrorTypes.User.AgencyNotAllowedToExistHotelLabelDirect)]
    [SwaggerResponseExt(default, ErrorTypes.User.EmailIsBound, ErrorTypes.User.PhoneNumberIsDisabled)]
    public async Task<IActionResult> Update(Models.AgencyUsers.UpdateInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var updateRequest = _mapper.Map<Contracts.Common.User.DTOs.AgencyUser.UpdateInput>(input);


        if (!string.IsNullOrEmpty(input.Email))
        {
            var emailCheck = await _userApiCaller.AgencyUsersAnyAsync(new AgencyUserVerifyInput
            {
                Email = input.Email,
                AgencyUserId = input.AgencyUserId
            });
            if (emailCheck)
            {
                throw new BusinessException(ErrorTypes.User.EmailIsBound);
            }
        }

        if (!string.IsNullOrEmpty(input.PhoneNumber))
        {
            var phoneCheck = await _userApiCaller.AgencyUsersAnyAsync(new AgencyUserVerifyInput
            {
                PhoneNumber = input.PhoneNumber,
                AgencyUserId = input.AgencyUserId
            });
            if (phoneCheck)
            {
                throw new BusinessException(ErrorTypes.User.PhoneNumberIsDisabled);
            }
        }


        await _userApiCaller.UpdateAgencyUser(updateRequest);

        //获取当前用户权限标签，过滤直连酒店分级标签查看权限（HotelLabelDirect）的修改
        var acls = await _permissionCaller.GetAclsByUserId(currentUser.UserId);
        var aclKeys = input.AclKeys.ToList();
        if (acls.Contains(AgencyUserAclKey.HotelLabelDirect) && !aclKeys.Contains(AgencyUserAclKey.HotelLabelDirect))
        {
            aclKeys.Add(AgencyUserAclKey.HotelLabelDirect);
            input.AclKeys = aclKeys;
        }
        else if (!acls.Contains(AgencyUserAclKey.HotelLabelDirect) && aclKeys.Contains(AgencyUserAclKey.HotelLabelDirect))
        {
            aclKeys.Remove(AgencyUserAclKey.HotelLabelDirect);
            input.AclKeys = aclKeys;
        }
        await _permissionCaller.SetAgencyUserACL(new Contracts.Common.Permission.DTOs.ACL.SetUserAclDTO
        {
            UserId = input.AgencyUserId,
            AclKeys = input.AclKeys,
            CreatorId = currentUser.UserId,
            CreatorName = currentUser.NickName
        });
        return Ok();
    }

    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(string))]
    [Authorize]
    public async Task<IActionResult> RestPassword(RestPasswordInput input)
    {
        var result = await _userApiCaller.RestPasswordAgencyUser(input);

        DeleteUserAllToken(input.UserId);

        return Ok(result);
    }

    /// <summary>
    /// 设置状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [Authorize]
    public async Task<IActionResult> OnOrOff(OnOrOffInput input)
    {
        await _userApiCaller.OnOrOffAgencyUser(input);

        DeleteUserAllToken(input.AgencyUserId);

        return Ok();
    }

    /// <summary>
    /// 对分销商邮箱发送验证码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [Authorize]
    public async Task<IActionResult> SendVerificationCode(AgencyVerificationInput input)
    {
        var user = HttpContext.User.ParseUserInfo<CurrentUser>();

        var b2bConfigtion = await _tenantApiCaller.GetB2BWebConfiguration();

        string brandName = string.Empty;

        if (b2bConfigtion != null)
        {
            brandName = b2bConfigtion.B2BTitleConfigs.FirstOrDefault(x => x.Language == "zh")?.BrandName ?? string.Empty;
        }

        await _userApiCaller.SendCaptcha(new CaptchaDTO
        {
            TenantId = user.Tenant,
            CaptchaChannelType = CaptchaChannelType.Email,
            CaptchaType = CaptchaType.AgencyUser_Email_EmailBingding,
            Key = input.Email,
            Variables = new List<KeyValuePair<int, string>> { new(0, brandName) }
        });

        return Ok();
    }

    /// <summary>
    /// 对分销商邮箱发送支付密码验证码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [Authorize]
    public async Task<IActionResult> SendPaymentVerificationCode(AgencyVerificationInput input)
    {
        var user = HttpContext.User.ParseUserInfo<CurrentUser>();

        var b2bConfigtion = await _tenantApiCaller.GetB2BWebConfiguration();

        string brandName = string.Empty;

        if (b2bConfigtion != null)
        {
            brandName = b2bConfigtion.B2BTitleConfigs.FirstOrDefault(x => x.Language == "zh")?.BrandName ?? string.Empty;
        }

        await _userApiCaller.SendCaptcha(new CaptchaDTO
        {
            TenantId = user.Tenant,
            CaptchaChannelType = CaptchaChannelType.Email,
            CaptchaType = input.CaptchaType,
            Key = input.Email,
            Variables = new List<KeyValuePair<int, string>> { new(0, brandName) }
        });

        return Ok();
    }

    /// <summary>
    /// 验证分销商邮箱绑定验证码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [Authorize]
    public async Task<IActionResult> VerificationCode(AgencyVerificationCheckInput input)
    {
        var user = HttpContext.User.ParseUserInfo<CurrentUser>();

        var result = await _userApiCaller.CheckCaptcha(new CaptchaDTO
        {
            TenantId = user.Tenant,
            CaptchaChannelType = CaptchaChannelType.Email,
            CaptchaType = input.CaptchaType,
            Key = input.Email,
            Code = input.Code,
            Variables = new List<KeyValuePair<int, string>> { }
        });

        if (result)
        {
            await _userApiCaller.SetEmailStatusAsync(new AgencyUserEmailStatusInput
            {
                AgencyUserId = user.UserId,
                Email = input.Email,
                EmailStatus = true
            });
        }
        else
        {
            throw new BusinessException(ErrorTypes.User.VerifyCodeError);

        }

        return Ok(result);
    }

    /// <summary>
    /// 验证分销商邮箱支付密码验证码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [Authorize]
    public async Task<IActionResult> VerificationPaymentCode(AgencyPaymentCheckInput input)
    {
        var user = HttpContext.User.ParseUserInfo<CurrentUser>();

        var result = await _userApiCaller.CheckCaptcha(new CaptchaDTO
        {
            TenantId = user.Tenant,
            CaptchaChannelType = CaptchaChannelType.Email,
            CaptchaType = input.CaptchaType,
            Key = input.Email,
            Code = input.Code,
            Variables = new List<KeyValuePair<int, string>> { }
        });

        if (result)
        {
            await _tenantApiCaller.SetPaymentPassword(new SetAgencyPaymentPasswordInput
            {
                AgencyId = user.Provider,
                PaymentPassword = SecurityUtil.MD5Encrypt(input.PaymentPassword, Encoding.UTF8),
                AgencyUserId = user.UserId
            });
        }
        else
        {
            throw new BusinessException(ErrorTypes.User.VerifyCodeError);

        }

        return Ok();
    }

    /// <summary>
    /// 查询 分销商密码变更记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(PagingModel<AgencyPasswordRecordDto>))]
    public async Task<IActionResult> SearchAgencyPasswordRecord(AgencyPasswordRecordPageInput input)
    {
        var result = await _tenantApiCaller.SearchAgencyPasswordRecord(input);
        if (result != null)
        {
            var agencyUserIds = result.Data.Select(x => x.AgencyUserId).ToArray();
            var agencyUsers = await _userApiCaller.GetAgencyUserDetail(new GetDetailsInput
            {
                UserIds = agencyUserIds ?? Array.Empty<long>()
            });

            foreach (var item in result.Data)
            {
                item.AgencyUserName = agencyUsers?.FirstOrDefault(x => x.AgencyUserId == item.AgencyUserId)?.RealName;
            }
        }

        return Ok(result);
    }

    /// <summary>
    /// 校验分销商账号-手机号是否已存在
    /// </summary>
    /// <param name="pn">11位手机号</param>
    /// <param name="pncode">微信手机号code</param>
    /// <returns></returns>
    [HttpGet]
    [SwaggerResponseExt(200, typeof(bool))]
    public async Task<IActionResult> IsExistPhoneNumber([FromQuery] string pn, [FromQuery] string pncode)
    {
        if (string.IsNullOrEmpty(pn) && string.IsNullOrEmpty(pncode))
            return Ok(false);

        if (string.IsNullOrEmpty(pncode) == false)
        {
            try
            {
                var rsp = await _wechatApiCaller.GetHuizhiPhoneNumber(new Contracts.Common.WeChat.DTOs.Huizhi.GetPhoneNumberInput
                {
                    Code = pncode,
                    TenantId = HttpContext.GetTenantId()
                });
                if (string.IsNullOrWhiteSpace(rsp?.PhoneNumber))
                    return Ok(false);
                pn = rsp!.PhoneNumber;
            }
            catch
            {
                return Ok(false);
            }
        }

        var exists = await _userApiCaller.AgencyUsersAnyAsync(new AgencyUserVerifyInput
        {
            PhoneNumber = pn
        });

        return Ok(exists);
    }

    /// <summary>
    /// 删除当前用户下 所有持久化token
    /// </summary>
    private void DeleteUserAllToken(long userid)
    {
        var user = HttpContext.User.ParseUserInfo<CurrentUser>();
        //Token 持久化删除 该用户下所有端的token
        var tokenKey = $"TokenPersistence:{user.Tenant}:{user.Provider}:{userid}*";
        _tokenPersistenceContext.Delete(tokenKey);

    }

    /// <summary>
    /// 对分销商用户数据微信绑定处理
    /// </summary>
    /// <param name="users"></param>
    /// <returns></returns>
    private async Task ProcessAgencyUserData(IEnumerable<AgencyUserSearchOutput> users)
    {
        if (users.Any() is false)
            return;

        var agencyUserIds = users.Select(x => x.AgencyUserId);

        //绑定数据
        var bindDataTask = _userApiCaller.GetUserBindings(new Contracts.Common.User.DTOs.UserBinding.GetUserBindingInput
        {
            PlatformType = UserBindPlatformType.B2BWechat,
            UserIds = agencyUserIds.ToArray(),
            SysRole = _sysRole
        });

        var bindData = await bindDataTask;

        foreach (var item in users)
        {
            item.WechatBind = bindData?.Any(x => x.UserId == item.AgencyUserId) is true;
        }
    }

    /// <summary>
    /// 分销商账号微信公众号解绑
    /// </summary>
    /// <param name="agencyUserId"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Unbound([SwaggerParameter(Required = true)] long agencyUserId)
    {
        await _userApiCaller.UnboundUserBindings(new UnboundUserBindingInput
        {
            UserId = agencyUserId,
            SysRole = _sysRole,
            PlatformType = UserBindPlatformType.B2BWechat
        });
        return Ok();
    }

    /// <summary>
    /// 生临时成用户B2B公众号关注二维码(一个小时)
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(CreateMpQrCodeOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetSubscribeQrCode([SwaggerParameter(Required = true)] long agencyUserId)
    {
        //CurrentUser currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var ts = TimeSpan.FromHours(1);
        var result = await _wechatApiCaller.CreateWechatMPQrCode(new CreateMpQrCodeInput
        {
            ActionName = "QR_STR_SCENE",
            AuthType = AuthType.B2BWechatMp,
            ExpireSeconds = (int)ts.TotalSeconds,
            ActionInfo = new CreateMpQrCodeActionInfo
            {
                SceneStr = $"Type={(int)WechatSenceType.AgencyUserSubscribe}&AgencyUserId={agencyUserId}",
            },
        });
        return Ok(result);
    }

}
