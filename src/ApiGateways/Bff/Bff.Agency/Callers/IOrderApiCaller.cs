using Bff.Agency.Models.OrderPrintSetting;
using Common.Caller;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.DTOs.ExportPdf;
using Contracts.Common.Order.DTOs.GDSOrder;
using Contracts.Common.Order.DTOs.GroupBooking;
using Contracts.Common.Order.DTOs.GroupBookingAggregate;
using Contracts.Common.Order.DTOs.GroupBookingAreaSetting;
using Contracts.Common.Order.DTOs.HotelOrder;
using Contracts.Common.Order.DTOs.Insure;
using Contracts.Common.Order.DTOs.Invoice;
using Contracts.Common.Order.DTOs.InvoiceTitle;
using Contracts.Common.Order.DTOs.MailOrder;
using Contracts.Common.Order.DTOs.OffsetOrder;
using Contracts.Common.Order.DTOs.OrderDelayed;
using Contracts.Common.Order.DTOs.ReceiptSettlementOrder;
using Contracts.Common.Order.DTOs.ScenicTicketOrder;
using Contracts.Common.Order.DTOs.TicketOrder;
using Contracts.Common.Order.DTOs.WorkOrder;
using Contracts.Common.Order.DTOs.WorkOrderServiceEvaluation;
using Contracts.Common.Resource.DTOs.GDSHotel;
using EfCoreExtensions.Abstract;
using CarProductOrder = Contracts.Common.Order.DTOs.CarProductOrder;
using GroupBookingOrder = Contracts.Common.Order.DTOs.GroupBookingOrder;
using SearchOutput = Contracts.Common.Order.DTOs.HotelOrder.SearchOutput;
using TravelLineOrder = Contracts.Common.Order.DTOs.TravelLineOrder;
using GroupBookingAgencyShare = Contracts.Common.Order.DTOs.GroupBookingAgencyShare;

namespace Bff.Agency.Callers;

public interface IOrderApiCaller : IHttpCallerBase
{
    #region BaseOrder

    /// <summary>
    /// 检查渠道单号是否已存在
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> CheckChannelOrderNoExist(CheckChannelOrderNoExistInput input);

    /// <summary>
    /// 查询用户不同订单状态的订单数量 [个人中心展示]
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetBaseOrderStatusCountOutPut>> GetBaseOrderStatusCount(GetBaseOrderStatusCountInput input);

    /// <summary>
    /// 获取订单支付简讯
    /// </summary>
    /// <param name="orderId">BaseOrderId</param>
    /// <returns></returns>
    Task<PaymentInfoOutput> GetBaseOrderPaymentInfo(long orderId);

    /// <summary>
    /// 获取能开票的列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<CanBeInvoiceDto>> GetListByCanBeInvoice(GetListByCanBeInvoiceInput input);

    /// <summary>
    ///  获取子订单所有开票信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<CanBeInvoiceDto>> CanIssueInvoiceList(GetListByCanBeInvoiceInput input);

    /// <summary>
    /// 获取能开票的订单数量
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<BeInvoiceCountDto> GetBeInvoiceCount(GetListByCanBeInvoiceInput input);

    #endregion

    #region HotelOrder

    /// <summary>
    /// 酒店日历房下单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CreateHotelOrderOutput> HotelOrderCreate(CreateHotelOrderInput input);

    /// <summary>
    /// 日历房订单搜索列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchOutput, IList<HotelStatusCountOutput>>> HotelOrderSearch(Contracts.Common.Order.DTOs.HotelOrder.SearchInput input);

    /// <summary>
    /// 各酒店状态数量统计
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IList<HotelStatusCountOutput>> GetHotelOrderStatusCounts(GetHotelStatusCountInput input);

    /// <summary>
    /// 使用时间范围统计订单数
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IList<GetHotelOrderCheckCountDto>> GetHotelOrderCheckCounts(Contracts.Common.Order.DTOs.HotelOrder.SearchInput input);

    /// <summary>
    /// 导出酒店订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<ExportDataOutput>> ExportDataHotelOrder(ExportDataInput input);

    /// <summary>
    /// 酒店订单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<Contracts.Common.Order.DTOs.HotelOrder.DetailOutput> HotelOrderDetail(DetailInput input);

    /// <summary>
    /// 获取Hop 酒店订单最新信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<HopHotelOrderUpdatedDto> GetHotelHopUpdatedInfo(HopHotelOrderUpdatedInput input);

    /// <summary>
    /// 统计酒店订单数
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<CountByGroupOutput>> CountByGroup(CountByGroupInput input);

    /// <summary>
    /// 导出酒店入住单pdf
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    Task<byte[]> ExportHotelOrderPdf(ExportHotelOrderPdfInput input);

    /// <summary>
    /// 查询酒店订单数量
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetHotelOrderCountOutput>> GetHotelOrderCount(GetHotelOrderCountInput input);

    /// <summary>
    /// 编辑来源单号
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task EditChannelOrderNoByAgency(EditChannelOrderNoInput input);

    /// <summary>
    /// 修改团号
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task EditGroupNoByAgency(EditGroupNoInput input);
    #endregion

    #region 线路订单 TravelLineOrder

    /// <summary>
    /// 线路订单搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<TravelLineOrder.SearchOutput, TravelLineOrder.OrderStatusStatOutput>> TravelLineOrderSearch(TravelLineOrder.SearchInput input);

    /// <summary>
    /// 线路订单详情
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="ErrorTypes.Order.NoMatchingData"></exception>
    /// <returns></returns>
    Task<TravelLineOrder.OrderDetailOutput> TravelLineOrderDetail(TravelLineOrder.OrderDetailInput input);

    /// <summary>
    /// 线路订单 可退款信息
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="ErrorTypes.Order.OrderCannotRefund"></exception>
    /// <returns></returns>
    Task<TravelLineOrder.OrderRefundableOutput> TravelLineOrderRefundable(TravelLineOrder.OrderRefundableInput input);

    /// <summary>
    /// 线路订单退款
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="ErrorTypes.Order.OrderCannotRefund"></exception>
    /// <exception cref="ErrorTypes.Order.RefundAmountInvalid"></exception>
    /// <returns></returns>
    Task TravelLineOrderRefund(TravelLineOrder.OrderRefundInput input);

    /// <summary>
    /// 创建订单
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    Task<TravelLineOrder.CreateLineOrderOutput> TravelLineOrderCreate(TravelLineOrder.CreateDto input);

    /// <summary>
    /// 查询订单数量
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetOrderCountOutput>> GetTravelLineOrderCount(GetOrderCountInput input);

    /// <summary>
    /// 查询线路订单sku项子类型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<TravelLineOrder.QueryTravelLineOrderSkuTypeItemOutput> QueryTravelLineOrderSkuTypeItems(TravelLineOrder.QueryTravelLineOrderSkuTypeItemInput input);
    
    #endregion

    #region WorkOrder
    /// <summary>
    /// 工单申请
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<long> WorkOrderApply(Contracts.Common.Order.DTOs.WorkOrder.ApplyInput input);

    /// <summary>
    /// 分页查询工单
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<PagingModel<SearchWorkOrderDto>> SearchWorkOrder(Contracts.Common.Order.DTOs.WorkOrder.SearchInput input);

    /// <summary>
    /// 工单详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<Contracts.Common.Order.DTOs.WorkOrder.DetailOutput> WorkOrderDetail(long id);

    /// <summary>
    /// 工单回复
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<Contracts.Common.Order.DTOs.WorkOrder.DetailOutput> WorkOrderReply(ReplyInput input);

    /// <summary>
    /// 获取工单类型列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<string[]> GetWorkOrderTypeList(GetWorkOrderTypeListInput input);
    #endregion

    #region WorkOrderServiceEvaluation
    /// <summary>
    /// 获取服务评价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetOutput>> GetWorkOrderServiceEvaluationList(GetListInput input);
    #endregion

    #region ReceiptSettlementOrder

    /// <summary>
    /// 收款结算单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<Contracts.Common.Order.DTOs.ReceiptSettlementOrder.SearchOutput>> SearchReceiptSettlementOrder(
        Contracts.Common.Order.DTOs.ReceiptSettlementOrder.SearchInput input);

    /// <summary>
    /// 收款结算单-Ids
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<long>> SearchReceiptSettlementIds(Contracts.Common.Order.DTOs.ReceiptSettlementOrder.SearchInput input);

    /// <summary>
    /// 收款结算单-确认
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task ConfirmReceiptSettlementOrder(Contracts.Common.Order.DTOs.ReceiptSettlementOrder.ConfirmInput input);

    /// <summary>
    /// 收款结算单-导出明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ExportDetailOutput> ExportReceiptSettlementOrderDetail(ExportDetailInput input);

    /// <summary>
    /// 收款结算单-账单提醒
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<ReceiptSettlementOrderRemindOutput>> RemindReceiptSettlementOrder(ReceiptSettlementOrderRemindInput input);

    /// <summary>
    /// 标记为已阅读提醒
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task ReadRemindReceiptSettlementOrder(ReadRemindDataInput input);

    /// <summary>
    /// 标记为已阅读提醒
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<GetReceiptSettlementOrderRecordsOutput>> GetReceiptSettlementOrderRecords(GetReceiptSettlementOrderRecordsInput input);
    #endregion

    #region ScenicTicketOrder

    /// <summary>
    /// 门票订单列表查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchScenicTicketOrderOutput, ScenicTicketOrderStatusCount>> SearchScenicTicketOrder(
        SearchScenicTicketOrderInput input);

    /// <summary>
    /// 门票订单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ScenicTicketOrderDetailOutput> ScenicTicketOrderDetail(ScenicTicketOrderDetailInput input);

    /// <summary>
    /// B2B创建门票订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CreateScenicTicketOrderOutput> CreateScenicTicketOrderByB2b(Contracts.Common.Order.DTOs.ScenicTicketOrder.B2BCreateInput input);

    /// <summary>
    /// 查询订单数量
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetOrderCountOutput>> GetScenicTicketOrderCount(GetOrderCountInput input);
    #endregion

    #region TicketOrder
    /// <summary>
    /// B2B券类订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<string> CreateTicketOrderByB2b(Contracts.Common.Order.DTOs.TicketOrder.B2BCreateInput input);

    /// <summary>
    /// 可预约
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    Task<GetReservationabledOutput> GetTicketOrderReservationabled(long baseOrderId);
    #endregion

    #region Invoice
    /// <summary>
    /// 查询发票
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<InvoiceRecordDto>> SearchInvoice(SearchInvoiceRecordInput input);

    /// <summary>
    /// B2B申请发票
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task InvoiceApply(Contracts.Common.Order.DTOs.Invoice.ApplyInput input);

    /// <summary>
    /// B2B批量申请发票
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task InvoiceApplyList(List<Contracts.Common.Order.DTOs.Invoice.ApplyInput> input);

    /// <summary>
    /// 检查发票状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<Contracts.Common.Order.DTOs.Invoice.CheckStatusOutPut>> InvoiceCheckStatusByOrderIds(Contracts.Common.Order.DTOs.Invoice.CheckStatusInput input);
    #endregion

    #region GroupBooking

    /// <summary>
    /// 团房单新申请 分销商/商户 申请
    /// </summary>
    /// <returns></returns>
    Task<List<long>> GroupBookingApplicationFormApply(ApplicationFormApplyInput input);

    /// <summary>
    /// 团房申请单列表搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<ApplicationFormSearchOutput, ApplicationFormSearchSupplement>> GroupBookingApplicationFormSearch(ApplicationFormSearchInput input);

    /// <summary>
    /// 申请单信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ApplicationFormOutput> GetApplicationForm(GetApplicationFormInput input);


    /// <summary>
    /// 申请单信息编辑 
    /// </summary>
    /// <returns></returns>
    Task GroupBookingApplicationFormModify(ApplicationFormModifyInput input);

    /// <summary>
    /// 申请单状态变更
    /// </summary>
    /// <returns></returns>
    Task GroupBookingApplicationFormStatusModify(ApplicationFormStatusModifyInput input);

    /// <summary>
    /// 记录操作
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task LogGroupBookingOperation(GroupBookingOperationLogInput input);

    /// <summary>
    /// 操作日志记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<OperationLogOutput>> GetGroupBookingOperationLogs(GetOperationLogInput input);

    /// <summary>
    /// 商户确认申请 批量生成询价单
    /// </summary>
    /// <returns></returns>
    Task GroupBookingInquiry(InquiryInput input);

    /// <summary>
    /// SaaS询价单批量生成报价单 //商户已询价 客户待询价
    /// </summary>
    /// <returns></returns>
    Task GroupBookingQuotation(QuotationInput input);

    /// <summary>
    /// 查询关联询价报价单
    /// </summary>
    /// <param name="applicationFormId"></param>
    /// <returns></returns>
    Task<IEnumerable<QuotationOutput>> GetGroupBookingQuotations(long applicationFormId);

    /// <summary>
    /// 客户确认保存报价单 //确认报价
    /// </summary>
    /// <returns></returns>
    Task GroupBookingQuotationConfirm(QuotationConfirmInput input);

    /// <summary>
    /// SaaS生成待审预订单
    /// </summary>
    /// <returns></returns>
    Task GroupBookingPreOrder(PreOrderInput input);

    /// <summary>
    /// 查询申请单关联预订单
    /// </summary>
    /// <param name="applicationFormId"></param>
    /// <param name="preOrderId"></param>
    /// <returns></returns>
    Task<IEnumerable<PreOrderOutput>> GetGroupBookingPreOrders(long applicationFormId, long? preOrderId = default);

    /// <summary>
    /// 商户确认预订单 //预订单
    /// </summary>
    /// <returns></returns>
    Task GroupBookingPreOrderConfirm(PreOrderConfirmInput input);

    /// <summary>
    /// 当前商户预订单设置
    /// </summary>
    /// <returns></returns>
    Task<GroupBookingPreOrderSettingDto> GetGroupBookingPreOrderSetting();

    /// <summary>
    /// 团房下单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GroupBookingOrder.OrderCreateOutput> GroupBookingOrderCreate(GroupBookingOrder.OrderCreateInput input);

    /// <summary>
    /// 团房单搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<GroupBookingOrder.SearchOutput, IEnumerable<GroupBookingOrder.SearchSupplement>>> GroupBookingOrderSearch(GroupBookingOrder.SearchInput input);

    /// <summary>
    /// 团房单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GroupBookingOrder.DetailOutput> GroupBookingOrderDetail(GroupBookingOrder.DetailInput input);

    /// <summary>
    /// 添加入住人
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AddGroupBookingOrderGuest(GroupBookingOrder.AddOrderGuestInput input);

    /// <summary>
    /// 编辑备注
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task EditGroupBookingOrderMessage(GroupBookingOrder.EditOrderMessageInput input);

    /// <summary>
    /// 导出团房入住单
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    Task<byte[]> ExportHotelGroupBookingOrderPdf(ExportGroupBookingOrderPdfInput input);


    /// <summary>
    /// 签署团房申请单合同
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task GroupBookingSignContract(FormSignContractInput input);

    /// <summary>
    /// 获取或创建分销商账号分享码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GroupBookingAgencyShare.GroupBookingAgencyShareOutput> GetOrCreateGroupBookingAgencyShare(GroupBookingAgencyShare.GetOrCreateInput input);

    /// <summary>
    /// 根据code获取分销商账号分享码信息
    /// </summary>
    /// <param name="code"></param>
    /// <returns></returns>
    Task<GroupBookingAgencyShare.GroupBookingAgencyShareOutput?> GetGroupBookingAgencyShareByCode(string code);

    /// <summary>
    /// 分销商跟单备注列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GroupBookingApplicationFormRemarkOutput>> GetGroupBookingApplicationFormRemarkList(GetGroupBookingApplicationFormRemarkListInput input);

    /// <summary>
    /// 添加分销商跟单备注
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task GroupBookingApplicationFormRemarkAdd(GroupBookingApplicationFormRemarkAddInput input);

    #endregion

    #region InsureProductRelation
    /// <summary>
    /// 根据产品id获取关联的保险产品
    /// </summary>
    /// <returns></returns>
    Task<InsureProductRelationsOutput> GetInsureProductRelation(long productId);
    #endregion

    #region InvoiceConfig
    /// <summary>
    /// 获取发票接口配置
    /// </summary>
    /// <returns></returns>
    Task<List<GetInvoiceConfigOutput>> GetInvoiceConfig();
    #endregion

    #region InvoiceTitle
    /// <summary>
    /// 获取发票抬头
    /// </summary>
    /// <param name="AgencyId"></param>
    /// <returns></returns>
    Task<List<GetInvoiceTitleOutput>> GetInvoiceTitleList(long AgencyId);

    /// <summary>
    /// 添加发票抬头
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<long> AddInvoiceTitle(AddInvoiceTitleInput input);

    /// <summary>
    /// 编辑抬头
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateInvoiceTitle(UpdateInvoiceTitleInput input);

    /// <summary>
    /// 删除抬头
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task DeleteInvoiceTitle(DeleteInput input);
    #endregion

    #region CarProductOrder 用车订单

    /// <summary>
    /// 用车 - 下单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CarProductOrder.CreateCarProductOrderOutput> CarProductOrderCreate(CarProductOrder.CreateCarProductOrderInput input);

    /// <summary>
    /// 用车 - 订单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CarProductOrder.GetDetailOutput> CarProductOrderDetail(CarProductOrder.GetDetailInput input);

    /// <summary>
    /// 用车 - 订单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<CarProductOrder.SearchOrderOutput>> CarProductOrderSearch(CarProductOrder.SearchOrderInput input);

    /// <summary>
    /// 用车 - 退款
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CarProductOrderRefund(CarProductOrder.RefundOrderInput input);

    /// <summary>
    /// 查询订单数量
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetOrderCountOutput>> GetCarProductOrderCount(GetOrderCountInput input);

    #endregion

    #region ReservationOrder
    /// <summary>
    /// 获取预约单支付简讯
    /// </summary>
    /// <param name="orderId">预约单Id</param>
    /// <returns></returns>
    Task<PaymentInfoOutput> GetReservationOrderPaymentInfo(long orderId);

    #endregion

    #region OffsetOrder
    /// <summary>
    /// 获取抵冲单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<GetOffsetOrderListOutput>> GetOffsetOrderList(GetOffsetOrderListInput input);
    #endregion

    #region MailOrder

    /// <summary>
    /// 订单详情
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    Task<OrderDetailsOutput> GetMailOrderByBaseOrderId(long baseOrderId);

    #endregion

    #region OrderPrintSetting
    Task<OrderPrintSettingBffOutput> GetOrderPrintSetting();
    #endregion

    #region OrderPrice

    /// <summary>
    /// 根据订单id获取订单价格信息
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    Task<List<OrderMultPriceOutput>> GetByBaseOrderId(long baseOrderId);

    #endregion

    #region WorkOrderServiceEvaluation
    /// <summary>
    /// 创建服务评价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CreateWorkOrderServiceEvaluation(Contracts.Common.Order.DTOs.WorkOrderServiceEvaluation.CreateInput input);
    #endregion

    #region OrderDelayedPay

    /// <summary>
    /// 延时支付 订单信息 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<OrderDelayedPayInfoOutput> OrderDelayedPayInfo(OrderDelayedPayInfoInput input);

    /// <summary>
    /// 订单延时支付
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task OrderDelayedPay(OrderDelayedPayInput input);

    /// <summary>
    /// 延时订单 未实付取消
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task OrderDelayedCancel(OrderDelayedCancelInput input);

    #endregion

    #region GDS
    /// <summary>
    /// GDS 检查价格
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GDSHotelPriceCheckOutput> GDSPriceCheck(GDSHotelPriceCheckInput input);

    Task<GetPriceCheckInfoOutput> GetGDSPriceCheckInfo(string bookingkey);
    #endregion

    #region OrderPaymentCard
    Task<bool> AddOrderPaymentCard(CreditCardGuaranteeInput payInput);
    #endregion

    #region GroupBookingAggregate
    Task<List<GetStatusStatisticsOutput>> GetGroupBookingAggregateStatusStatistics(GetStatusStatisticsInput input);
    #endregion

    #region GroupBookingAreaSetting
    /// <summary>
    /// 
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task<GroupBookingAreaSettingOutput> GetGroupBookingAreaSetting(); 
    #endregion
}
