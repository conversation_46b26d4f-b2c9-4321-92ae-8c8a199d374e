using Common.Caller;
using Contracts.Common.User.DTOs;
using Contracts.Common.User.DTOs.AgencyUser;
using Contracts.Common.User.DTOs.OperationLog;
using Contracts.Common.User.DTOs.SupplierUser;
using Contracts.Common.User.DTOs.UserBinding;
using EfCoreExtensions.Abstract;

namespace Bff.Agency.Callers;

public interface IUserApiCaller : IHttpCallerBase
{
    #region Security

    /// <exception cref="ErrorTypes.User.GetCodeTooFrequent"></exception>
    public Task SendCaptcha(CaptchaDTO input);

    public Task<bool> CheckCaptcha(CaptchaDTO input);

    public Task<ImageCaptchaDto> GetImageCaptcha();

    public Task<bool> CheckImageCaptcha(ImageCaptchaDto input);

    #endregion

    #region AgencyUser

    public Task<AgencyUserDto> FindOne(AgencyUserFindOneInput input);

    Task<AgencyUserDto> GetAgencyUserDetail(long id);

    /// <summary>
    /// 获取分销商详细信息
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<List<AgencyUserDto>> GetAgencyUserDetail(GetDetailsInput input);

    /// <summary>
    /// 获取分销商详细信息
    /// </summary>
    /// <returns></returns>
    Task<List<AgencyUserDto>> GetAgencyUserInfo(GetDetailsInput input);

    /// <summary>
    /// 分页查询分销商用户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<AgencyUserDto>> SearchAgencyUser(Contracts.Common.User.DTOs.AgencyUser.SearchInput input);

    /// <summary>
    /// 充值分销商用户密码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<string> RestPasswordAgencyUser(RestPasswordInput input);

    /// <summary>
    /// 更新分销商用户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateAgencyUser(Contracts.Common.User.DTOs.AgencyUser.UpdateInput input);

    /// <summary>
    /// 新增分销商用户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<long> AddAgencyUser(Contracts.Common.User.DTOs.AgencyUser.AddInput input);

    /// <summary>
    /// 设置分销商用户状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task OnOrOffAgencyUser(OnOrOffInput input);

    /// <summary>
    /// 查询分销商用户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<AgencyUserDto> AgencyUserFindOne(AgencyUserFindOneInput input);


    /// <summary>
    /// 修改分销商账号的密码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdatePassword(UpdatePasswordInput input);

    /// <summary>
    /// 对分销商做唯一性校验
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> AgencyUsersAnyAsync(AgencyUserVerifyInput input);

    /// <summary>
    /// 设置分销商 的邮箱验证状态
    /// </summary>
    /// <returns></returns>
    Task SetEmailStatusAsync(AgencyUserEmailStatusInput input);

    #endregion

    #region TenantUser

    Task<UserSearchOuput> TenantUserFindOne(FindOneInput input);

    #endregion

    #region OperationLog
    Task AddOperationLog(OperationLogDto input);

    #endregion

    #region UserBinding

    Task<List<GetUserBindingOutput>> GetUserBindings(GetUserBindingInput input);

    Task UnboundUserBindings(UnboundUserBindingInput input);

    #endregion
}
