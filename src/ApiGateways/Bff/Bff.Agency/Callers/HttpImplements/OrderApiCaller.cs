using Bff.Agency.Models.OrderPrintSetting;
using Common.Caller;
using Common.ServicesHttpClient;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.DTOs.ExportPdf;
using Contracts.Common.Order.DTOs.GDSOrder;
using Contracts.Common.Order.DTOs.GroupBooking;
using Contracts.Common.Order.DTOs.GroupBookingAggregate;
using Contracts.Common.Order.DTOs.GroupBookingAreaSetting;
using Contracts.Common.Order.DTOs.HotelOrder;
using Contracts.Common.Order.DTOs.Insure;
using Contracts.Common.Order.DTOs.Invoice;
using Contracts.Common.Order.DTOs.InvoiceTitle;
using Contracts.Common.Order.DTOs.MailOrder;
using Contracts.Common.Order.DTOs.OffsetOrder;
using Contracts.Common.Order.DTOs.OrderDelayed;
using Contracts.Common.Order.DTOs.ReceiptSettlementOrder;
using Contracts.Common.Order.DTOs.ScenicTicketOrder;
using Contracts.Common.Order.DTOs.TicketOrder;
using Contracts.Common.Order.DTOs.WorkOrder;
using Contracts.Common.Order.DTOs.WorkOrderServiceEvaluation;
using Contracts.Common.Resource.DTOs.GDSHotel;
using EfCoreExtensions.Abstract;
using Microsoft.Extensions.Options;
using CarProductOrder = Contracts.Common.Order.DTOs.CarProductOrder;
using GroupBookingOrder = Contracts.Common.Order.DTOs.GroupBookingOrder;
using SearchOutput = Contracts.Common.Order.DTOs.HotelOrder.SearchOutput;
using TravelLineOrder = Contracts.Common.Order.DTOs.TravelLineOrder;
using GroupBookingAgencyShare = Contracts.Common.Order.DTOs.GroupBookingAgencyShare;

namespace Bff.Agency.Callers.HttpImplements;

public class OrderApiCaller : HttpCallerBase, IOrderApiCaller
{
    public OrderApiCaller(IOptions<ServicesAddress> servicesAddress, IHttpClientFactory httpClientFactory)
        : base(servicesAddress.Value.Order, httpClientFactory)
    {
    }

    #region BaseOrder

    public Task<bool> CheckChannelOrderNoExist(CheckChannelOrderNoExistInput input)
    {
        var relativePath = "/BaseOrder/CheckChannelOrderNoExist";
        return PostAsync<CheckChannelOrderNoExistInput, bool>(relativePath, input);
    }

    public Task<List<GetBaseOrderStatusCountOutPut>> GetBaseOrderStatusCount(GetBaseOrderStatusCountInput input)
    {
        var relativePath = "/BaseOrder/GetBaseOrderStatusCount";
        return PostAsync<GetBaseOrderStatusCountInput, List<GetBaseOrderStatusCountOutPut>>(relativePath, input);
    }

    public Task<PaymentInfoOutput> GetBaseOrderPaymentInfo(long orderId)
    {
        var relativePath = $"/BaseOrder/PaymentInfo?orderId={orderId}";
        return GetAsync<PaymentInfoOutput>(relativePath);
    }

    public async Task<PagingModel<CanBeInvoiceDto>> GetListByCanBeInvoice(GetListByCanBeInvoiceInput input)
    {
        var relativePath = $"/BaseOrder/GetListByCanBeInvoice";
        return await PostAsync<GetListByCanBeInvoiceInput, PagingModel<CanBeInvoiceDto>>(relativePath, input);
    }

    public async Task<List<CanBeInvoiceDto>> CanIssueInvoiceList(GetListByCanBeInvoiceInput input)
    {
        var relativePath = $"/BaseOrder/CanIssueInvoiceList";
        return await PostAsync<GetListByCanBeInvoiceInput, List<CanBeInvoiceDto>>(relativePath, input);
    }


    public async Task<BeInvoiceCountDto> GetBeInvoiceCount(GetListByCanBeInvoiceInput input)
    {
        var relativePath = $"/BaseOrder/GetBeInvoiceCount";
        return await PostAsync<GetListByCanBeInvoiceInput, BeInvoiceCountDto>(relativePath, input);
    }

    #endregion

    #region HotelOrder


    public Task<CreateHotelOrderOutput> HotelOrderCreate(CreateHotelOrderInput input)
    {
        var relativePath = "/HotelOrder/Create";
        return PostAsync<CreateHotelOrderInput, CreateHotelOrderOutput>(relativePath, input);
    }

    public Task<PagingModel<SearchOutput, IList<HotelStatusCountOutput>>> HotelOrderSearch(Contracts.Common.Order.DTOs.HotelOrder.SearchInput input)
    {
        var relativePath = "/HotelOrder/Search";
        return PostAsync<Contracts.Common.Order.DTOs.HotelOrder.SearchInput, PagingModel<SearchOutput, IList<HotelStatusCountOutput>>>(relativePath, input);
    }

    public Task<IList<HotelStatusCountOutput>> GetHotelOrderStatusCounts(GetHotelStatusCountInput input)
    {
        var relativePath = "/HotelOrder/GetHotelOrderStatusCounts";
        return PostAsync<GetHotelStatusCountInput, IList<HotelStatusCountOutput>>(relativePath, input);
    }

    public Task<IList<GetHotelOrderCheckCountDto>> GetHotelOrderCheckCounts(Contracts.Common.Order.DTOs.HotelOrder.SearchInput input)
    {
        var relativePath = "/HotelOrder/GetHotelOrderCheckCounts";
        return PostAsync<Contracts.Common.Order.DTOs.HotelOrder.SearchInput, IList<GetHotelOrderCheckCountDto>>(relativePath, input);
    }

    public Task<List<ExportDataOutput>> ExportDataHotelOrder(ExportDataInput input)
    {
        var relativePath = "/HotelOrder/ExportData";
        return PostAsync<ExportDataInput, List<ExportDataOutput>>(relativePath, input);
    }

    public async Task<Contracts.Common.Order.DTOs.HotelOrder.DetailOutput> HotelOrderDetail(DetailInput input)
    {
        var relativePath = "/HotelOrder/Detail";
        return await PostAsync<DetailInput, Contracts.Common.Order.DTOs.HotelOrder.DetailOutput>(relativePath, input);
    }

    public async Task<HopHotelOrderUpdatedDto> GetHotelHopUpdatedInfo(HopHotelOrderUpdatedInput input)
    {
        var relativePath = "/HotelOrder/GetHotelHopUpdatedInfo";
        return await PostAsync<HopHotelOrderUpdatedInput, HopHotelOrderUpdatedDto>(relativePath, input);
    }

    public async Task<IEnumerable<CountByGroupOutput>> CountByGroup(CountByGroupInput input)
    {
        var relativePath = "/HotelOrder/CountByGroup";
        return await PostAsync<CountByGroupInput, IEnumerable<CountByGroupOutput>>(relativePath, input);
    }

    public async Task<byte[]> ExportHotelOrderPdf(Contracts.Common.Order.DTOs.ExportPdf.ExportHotelOrderPdfInput input)
    {
        var relativePath = "/HotelOrder/ExportHotelOrderPdf";
        return await PostAsync<Contracts.Common.Order.DTOs.ExportPdf.ExportHotelOrderPdfInput, byte[]>(relativePath, input);
    }

    public async Task<List<GetHotelOrderCountOutput>> GetHotelOrderCount(GetHotelOrderCountInput input)
    {
        var relativePath = "/HotelOrder/GetOrderCount";
        return await PostAsync<GetHotelOrderCountInput, List<GetHotelOrderCountOutput>>(relativePath, input);
    }

    public async Task EditChannelOrderNoByAgency(EditChannelOrderNoInput input)
    {
        var relativePath = "/HotelOrder/EditChannelOrderNoByAgency";
        await PostAsync<EditChannelOrderNoInput>(relativePath, input);
    }

    public async Task EditGroupNoByAgency(EditGroupNoInput input)
    {
        var relativePath = "/HotelOrder/EditGroupNoByAgency";
        await PostAsync<EditGroupNoInput>(relativePath, input);
    }
    #endregion

    #region TravelLineOrder

    public Task<PagingModel<TravelLineOrder.SearchOutput, TravelLineOrder.OrderStatusStatOutput>> TravelLineOrderSearch(TravelLineOrder.SearchInput input)
    {
        var relativePath = "/TravelLineOrder/Search";
        return PostAsync<TravelLineOrder.SearchInput, PagingModel<TravelLineOrder.SearchOutput, TravelLineOrder.OrderStatusStatOutput>>(relativePath, input);
    }

    public Task<TravelLineOrder.OrderDetailOutput> TravelLineOrderDetail(TravelLineOrder.OrderDetailInput input)
    {
        var relativePath = "/TravelLineOrder/Detail";
        return PostAsync<TravelLineOrder.OrderDetailInput, TravelLineOrder.OrderDetailOutput>(relativePath, input);
    }

    public Task TravelLineOrderRefund(TravelLineOrder.OrderRefundInput input)
    {
        var relativePath = "/TravelLineOrder/Refund";
        return PostAsync(relativePath, input);
    }

    public Task<TravelLineOrder.OrderRefundableOutput> TravelLineOrderRefundable(TravelLineOrder.OrderRefundableInput input)
    {
        var relativePath = "/TravelLineOrder/OrderRefundable";
        return PostAsync<TravelLineOrder.OrderRefundableInput, TravelLineOrder.OrderRefundableOutput>(relativePath, input);
    }

    public Task<TravelLineOrder.CreateLineOrderOutput> TravelLineOrderCreate(TravelLineOrder.CreateDto input)
    {
        var relativePath = "/TravelLineOrder/Create";
        return PostAsync<TravelLineOrder.CreateDto, TravelLineOrder.CreateLineOrderOutput>(relativePath, input);
    }

    public async Task<List<GetOrderCountOutput>> GetTravelLineOrderCount(GetOrderCountInput input)
    {
        var relativePath = "/TravelLineOrder/GetOrderCount";
        return await PostAsync<GetOrderCountInput, List<GetOrderCountOutput>>(relativePath, input);
    }
    
    public async Task<TravelLineOrder.QueryTravelLineOrderSkuTypeItemOutput> QueryTravelLineOrderSkuTypeItems(TravelLineOrder.QueryTravelLineOrderSkuTypeItemInput input)
    {
        var relativePath = "/TravelLineOrder/QuerySkuTypeItems";
        return await PostAsync<TravelLineOrder.QueryTravelLineOrderSkuTypeItemInput, TravelLineOrder.QueryTravelLineOrderSkuTypeItemOutput>(relativePath, input);
    }

    #endregion

    #region WorkOrder
    public Task<long> WorkOrderApply(Contracts.Common.Order.DTOs.WorkOrder.ApplyInput input)
    {
        var relativePath = "/WorkOrder/Apply";
        return PostAsync<Contracts.Common.Order.DTOs.WorkOrder.ApplyInput, long>(relativePath, input);
    }

    public async Task<PagingModel<SearchWorkOrderDto>> SearchWorkOrder(Contracts.Common.Order.DTOs.WorkOrder.SearchInput input)
    {
        var relativePath = $"/WorkOrder/Search";
        return await PostAsync<Contracts.Common.Order.DTOs.WorkOrder.SearchInput, PagingModel<SearchWorkOrderDto>>(relativePath, input);
    }

    public async Task<Contracts.Common.Order.DTOs.WorkOrder.DetailOutput> WorkOrderDetail(long id)
    {
        var relativePath = $"/WorkOrder/Detail?id={id}";
        return await GetAsync<Contracts.Common.Order.DTOs.WorkOrder.DetailOutput>(relativePath);
    }

    public Task<Contracts.Common.Order.DTOs.WorkOrder.DetailOutput> WorkOrderReply(ReplyInput input)
    {
        var relativePath = $"/WorkOrder/Reply";
        return PostAsync<ReplyInput, Contracts.Common.Order.DTOs.WorkOrder.DetailOutput>(relativePath, input);
    }

    public async Task<string[]> GetWorkOrderTypeList(GetWorkOrderTypeListInput input)
    {
        var relativePath = $"/WorkOrder/GetWorkOrderTypeList";
        return await PostAsync<GetWorkOrderTypeListInput, string[]>(relativePath, input);
    }
    #endregion

    #region WorkOrderServiceEvaluation
    public async Task<List<GetOutput>> GetWorkOrderServiceEvaluationList(GetListInput input)
    {
        var relativePath = $"/WorkOrderServiceEvaluation/GetList";
        return await PostAsync<GetListInput, List<GetOutput>>(relativePath, input);
    }
    #endregion

    #region ReceiptSettlementOrder

    public async Task<PagingModel<Contracts.Common.Order.DTOs.ReceiptSettlementOrder.SearchOutput>>
        SearchReceiptSettlementOrder(
            Contracts.Common.Order.DTOs.ReceiptSettlementOrder.SearchInput input)
    {
        var relativePath = "/ReceiptSettlementOrder/Search";
        return await
            PostAsync<Contracts.Common.Order.DTOs.ReceiptSettlementOrder.SearchInput,
                PagingModel<Contracts.Common.Order.DTOs.ReceiptSettlementOrder.SearchOutput>>(
                relativePath, input);
    }

    public Task<List<long>> SearchReceiptSettlementIds(Contracts.Common.Order.DTOs.ReceiptSettlementOrder.SearchInput input)
    {
        var relativePath = "/ReceiptSettlementOrder/SearchReceiptSettlementIds";
        return PostAsync<Contracts.Common.Order.DTOs.ReceiptSettlementOrder.SearchInput, List<long>>(relativePath, input);
    }

    public async Task ConfirmReceiptSettlementOrder(Contracts.Common.Order.DTOs.ReceiptSettlementOrder.ConfirmInput input)
    {
        var relativePath = "/ReceiptSettlementOrder/Confirm";
        await PostAsync(relativePath, input);
    }

    public async Task<ExportDetailOutput> ExportReceiptSettlementOrderDetail(ExportDetailInput input)
    {
        var relativePath = "/ReceiptSettlementOrder/Export";
        return await PostAsync<ExportDetailInput, ExportDetailOutput>(relativePath, input);
    }

    public async Task<IEnumerable<ReceiptSettlementOrderRemindOutput>> RemindReceiptSettlementOrder(ReceiptSettlementOrderRemindInput input)
    {
        var relativePath = "/ReceiptSettlementOrder/GetRemindDatas";
        return await PostAsync<ReceiptSettlementOrderRemindInput, IEnumerable<ReceiptSettlementOrderRemindOutput>>(relativePath, input);
    }

    public async Task ReadRemindReceiptSettlementOrder(ReadRemindDataInput input)
    {
        var relativePath = "/ReceiptSettlementOrder/ReadRemindData";
        await PostAsync<ReadRemindDataInput>(relativePath, input);
    }

    public async Task<IEnumerable<GetReceiptSettlementOrderRecordsOutput>> GetReceiptSettlementOrderRecords(GetReceiptSettlementOrderRecordsInput input)
    {
        var relativePath = "/ReceiptSettlementOrder/GetReceiptSettlementOrderRecords";
        return await PostAsync < GetReceiptSettlementOrderRecordsInput, IEnumerable<GetReceiptSettlementOrderRecordsOutput>>(relativePath, input);
    }
    #endregion

    #region ScenicTicketOrder

    public async Task<PagingModel<SearchScenicTicketOrderOutput, ScenicTicketOrderStatusCount>> SearchScenicTicketOrder(
        SearchScenicTicketOrderInput input)
    {
        var relativePath = "/ScenicTicketOrder/Search";
        return await
            PostAsync<SearchScenicTicketOrderInput,
                PagingModel<SearchScenicTicketOrderOutput, ScenicTicketOrderStatusCount>>(relativePath, input);
    }

    public async Task<ScenicTicketOrderDetailOutput> ScenicTicketOrderDetail(ScenicTicketOrderDetailInput input)
    {
        var relativePath = "/ScenicTicketOrder/Detail";
        return await PostAsync<ScenicTicketOrderDetailInput, ScenicTicketOrderDetailOutput>(relativePath, input);
    }

    public async Task<CreateScenicTicketOrderOutput> CreateScenicTicketOrderByB2b(Contracts.Common.Order.DTOs.ScenicTicketOrder.B2BCreateInput input)
    {
        var relativePath = "/B2B/ScenicTicketOrder/Create";
        return await PostAsync<Contracts.Common.Order.DTOs.ScenicTicketOrder.B2BCreateInput, CreateScenicTicketOrderOutput>(relativePath, input);
    }

    public async Task<List<GetOrderCountOutput>> GetScenicTicketOrderCount(GetOrderCountInput input)
    {
        var relativePath = "/ScenicTicketOrder/GetOrderCount";
        return await PostAsync<GetOrderCountInput, List<GetOrderCountOutput>>(relativePath, input);
    }

    #endregion

    #region TicketOrder
    public async Task<string> CreateTicketOrderByB2b(Contracts.Common.Order.DTOs.TicketOrder.B2BCreateInput input)
    {
        var relativePath = "/B2B/TicketOrder/Create";
        return await PostAsync<Contracts.Common.Order.DTOs.TicketOrder.B2BCreateInput, string>(relativePath, input);
    }

    public async Task<GetReservationabledOutput> GetTicketOrderReservationabled(long baseOrderId)
    {
        var relativePath = $"/TicketOrder/GetReservationabled?baseOrderId={baseOrderId}";
        return await GetAsync<GetReservationabledOutput>(relativePath);
    }
    #endregion

    #region InvoiceRecord
    public async Task<PagingModel<InvoiceRecordDto>> SearchInvoice(SearchInvoiceRecordInput input)
    {
        var relativePath = "/InvoiceRecord/Search";
        return await PostAsync<SearchInvoiceRecordInput, PagingModel<InvoiceRecordDto>>(relativePath, input);
    }

    public async Task InvoiceApply(Contracts.Common.Order.DTOs.Invoice.ApplyInput input)
    {
        var relativePath = "/InvoiceRecord/Apply";
        await PostAsync<Contracts.Common.Order.DTOs.Invoice.ApplyInput, string>(relativePath, input);
    }

    public async Task InvoiceApplyList(List<Contracts.Common.Order.DTOs.Invoice.ApplyInput> input)
    {
        var relativePath = "/InvoiceRecord/ApplyList";
        await PostAsync<List<Contracts.Common.Order.DTOs.Invoice.ApplyInput>, string>(relativePath, input);
    }

    public async Task<List<CheckStatusOutPut>> InvoiceCheckStatusByOrderIds(CheckStatusInput input)
    {
        var relativePath = "/InvoiceRecord/CheckStatusByOrderIds";
        return await PostAsync<CheckStatusInput, List<CheckStatusOutPut>>(relativePath, input);
    }
    #endregion

    #region GroupBooking

    public Task<List<long>> GroupBookingApplicationFormApply(ApplicationFormApplyInput input)
    {
        var relativePath = "/HotelGroupBooking/ApplicationFormApply";
        return PostAsync<ApplicationFormApplyInput, List<long>>(relativePath, input);
    }

    public Task<PagingModel<ApplicationFormSearchOutput, ApplicationFormSearchSupplement>> GroupBookingApplicationFormSearch(ApplicationFormSearchInput input)
    {
        var relativePath = "/HotelGroupBooking/Search";
        return PostAsync<ApplicationFormSearchInput, PagingModel<ApplicationFormSearchOutput, ApplicationFormSearchSupplement>>(relativePath, input);
    }

    public Task<ApplicationFormOutput> GetApplicationForm(GetApplicationFormInput input)
    {
        var relativePath = "/HotelGroupBooking/GetApplicationForm";
        return PostAsync<GetApplicationFormInput, ApplicationFormOutput>(relativePath, input);
    }

    public Task GroupBookingApplicationFormModify(ApplicationFormModifyInput input)
    {
        var relativePath = "/HotelGroupBooking/ApplicationFormModify";
        return PostAsync(relativePath, input);
    }

    public Task GroupBookingApplicationFormStatusModify(ApplicationFormStatusModifyInput input)
    {
        var relativePath = "/HotelGroupBooking/ApplicationFormStatusModify";
        return PostAsync(relativePath, input);
    }

    public Task LogGroupBookingOperation(GroupBookingOperationLogInput input)
    {
        var relativePath = "/HotelGroupBooking/LogOperation";
        return PostAsync(relativePath, input);
    }

    public Task<IEnumerable<OperationLogOutput>> GetGroupBookingOperationLogs(GetOperationLogInput input)
    {
        var relativePath = "/HotelGroupBooking/GetOperationLogs";
        return PostAsync<GetOperationLogInput, IEnumerable<OperationLogOutput>>(relativePath, input);
    }

    public Task GroupBookingInquiry(InquiryInput input)
    {
        var relativePath = "/HotelGroupBooking/Inquiry";
        return PostAsync(relativePath, input);
    }

    public Task GroupBookingQuotation(QuotationInput input)
    {
        var relativePath = "/HotelGroupBooking/Quotation";
        return PostAsync(relativePath, input);
    }

    public Task<IEnumerable<QuotationOutput>> GetGroupBookingQuotations(long applicationFormId)
    {
        var relativePath = $"/HotelGroupBooking/GetQuotations?applicationFormId={applicationFormId}";
        return GetAsync<IEnumerable<QuotationOutput>>(relativePath);
    }

    public Task GroupBookingQuotationConfirm(QuotationConfirmInput input)
    {
        var relativePath = "/HotelGroupBooking/QuotationConfirm";
        return PostAsync(relativePath, input);
    }

    public Task GroupBookingPreOrder(PreOrderInput input)
    {
        var relativePath = "/HotelGroupBooking/PreOrder";
        return PostAsync(relativePath, input);
    }

    public Task<IEnumerable<PreOrderOutput>> GetGroupBookingPreOrders(long applicationFormId, long? preOrderId = default)
    {
        var relativePath = $"/HotelGroupBooking/GetPreOrders?applicationFormId={applicationFormId}&preOrderId={preOrderId}";
        return GetAsync<IEnumerable<PreOrderOutput>>(relativePath);
    }

    public Task GroupBookingPreOrderConfirm(PreOrderConfirmInput input)
    {
        var relativePath = "/HotelGroupBooking/PreOrderConfirm";
        return PostAsync(relativePath, input);
    }

    public Task<GroupBookingPreOrderSettingDto> GetGroupBookingPreOrderSetting()
    {
        var relativePath = "/HotelGroupBooking/GetPreOrderSetting";
        return GetAsync<GroupBookingPreOrderSettingDto>(relativePath);
    }

    public Task<GroupBookingOrder.OrderCreateOutput> GroupBookingOrderCreate(GroupBookingOrder.OrderCreateInput input)
    {
        var relativePath = "/HotelGroupBookingOrder/OrderCreate";
        return PostAsync<GroupBookingOrder.OrderCreateInput, GroupBookingOrder.OrderCreateOutput>(relativePath, input);
    }

    public Task<PagingModel<GroupBookingOrder.SearchOutput, IEnumerable<GroupBookingOrder.SearchSupplement>>> GroupBookingOrderSearch(GroupBookingOrder.SearchInput input)
    {
        var relativePath = "/HotelGroupBookingOrder/Search";
        return PostAsync<GroupBookingOrder.SearchInput, PagingModel<GroupBookingOrder.SearchOutput, IEnumerable<GroupBookingOrder.SearchSupplement>>>(relativePath, input);
    }

    public Task<GroupBookingOrder.DetailOutput> GroupBookingOrderDetail(GroupBookingOrder.DetailInput input)
    {
        var relativePath = "/HotelGroupBookingOrder/Detail";
        return PostAsync<GroupBookingOrder.DetailInput, GroupBookingOrder.DetailOutput>(relativePath, input);
    }

    public Task AddGroupBookingOrderGuest(GroupBookingOrder.AddOrderGuestInput input)
    {
        var relativePath = "/HotelGroupBookingOrder/AddOrderGuest";
        return PostAsync(relativePath, input);
    }

    public Task EditGroupBookingOrderMessage(GroupBookingOrder.EditOrderMessageInput input)
    {
        var relativePath = "/HotelGroupBookingOrder/EditOrderMessage";
        return PostAsync(relativePath, input);
    }

    public async Task<byte[]> ExportHotelGroupBookingOrderPdf(ExportGroupBookingOrderPdfInput input)
    {
        var relativePath = "/HotelGroupBookingOrder/ExportPdf";
        return await PostAsync<Contracts.Common.Order.DTOs.ExportPdf.ExportGroupBookingOrderPdfInput, byte[]>(relativePath, input);
    }

    public Task GroupBookingSignContract(FormSignContractInput input)
    {
        var relativePath = "/HotelGroupBooking/SignContract";
        return PostAsync<FormSignContractInput>(relativePath, input);
    }

    public Task<GroupBookingAgencyShare.GroupBookingAgencyShareOutput> GetOrCreateGroupBookingAgencyShare(GroupBookingAgencyShare.GetOrCreateInput input)
    {
        var relativePath = "/GroupBookingAgencyShare/GetOrCreate";
        return PostAsync<GroupBookingAgencyShare.GetOrCreateInput, GroupBookingAgencyShare.GroupBookingAgencyShareOutput>(relativePath, input);
    }

    public Task<GroupBookingAgencyShare.GroupBookingAgencyShareOutput?> GetGroupBookingAgencyShareByCode(string code)
    {
        var relativePath = $"/GroupBookingAgencyShare/GetByCode?code={code}";
        return GetAsync<GroupBookingAgencyShare.GroupBookingAgencyShareOutput?>(relativePath);
    }

    public Task<List<GroupBookingApplicationFormRemarkOutput>> GetGroupBookingApplicationFormRemarkList(GetGroupBookingApplicationFormRemarkListInput input)
    {
        var relativePath = "/HotelGroupBooking/GetFormRemarkList";
        return PostAsync<GetGroupBookingApplicationFormRemarkListInput, List<GroupBookingApplicationFormRemarkOutput>>(relativePath, input);
    }

    public Task GroupBookingApplicationFormRemarkAdd(GroupBookingApplicationFormRemarkAddInput input)
    {
        var relativePath = "/HotelGroupBooking/FormRemark";
        return PostAsync(relativePath, input);
    }

    #endregion

    #region InsureProductRelation
    public async Task<InsureProductRelationsOutput> GetInsureProductRelation(long productId)
    {
        var relativePath = "/InsureProduct/GetInsureProductRelation?productId=" + productId;
        return await GetAsync<InsureProductRelationsOutput>(relativePath);
    }
    #endregion

    #region InvoiceConfig
    public async Task<List<GetInvoiceConfigOutput>> GetInvoiceConfig()
    {
        var relativePath = "/InvoiceConfig/Get";
        return await GetAsync<List<GetInvoiceConfigOutput>>(relativePath);
    }
    #endregion

    #region InvoiceTitle
    public async Task<List<GetInvoiceTitleOutput>> GetInvoiceTitleList(long userId)
    {
        var relativePath = $"/InvoiceTitle/GetList?userId={userId}";
        return await GetAsync<List<GetInvoiceTitleOutput>>(relativePath);
    }

    public async Task<long> AddInvoiceTitle(AddInvoiceTitleInput input)
    {
        var relativePath = "/InvoiceTitle/Add";
        return await PostAsync<AddInvoiceTitleInput, long>(relativePath, input);
    }

    public async Task UpdateInvoiceTitle(UpdateInvoiceTitleInput input)
    {
        var relativePath = "/InvoiceTitle/Update";
        await PostAsync(relativePath, input);
    }

    public async Task DeleteInvoiceTitle(DeleteInput input)
    {
        var relativePath = "/InvoiceTitle/Delete";
        await PostAsync(relativePath, input);
    }
    #endregion

    #region CarProductOrder 用车订单

    public Task<CarProductOrder.CreateCarProductOrderOutput> CarProductOrderCreate(CarProductOrder.CreateCarProductOrderInput input)
    {
        var relativePath = "/CarProductOrder/Create";
        return PostAsync<CarProductOrder.CreateCarProductOrderInput, CarProductOrder.CreateCarProductOrderOutput>(relativePath, input);
    }

    public Task<CarProductOrder.GetDetailOutput> CarProductOrderDetail(CarProductOrder.GetDetailInput input)
    {
        var relativePath = "/CarProductOrder/Detail";
        return PostAsync<CarProductOrder.GetDetailInput, CarProductOrder.GetDetailOutput>(relativePath, input);
    }

    public Task<PagingModel<CarProductOrder.SearchOrderOutput>> CarProductOrderSearch(CarProductOrder.SearchOrderInput input)
    {
        var relativePath = "/CarProductOrder/Search";
        return PostAsync<CarProductOrder.SearchOrderInput, PagingModel<CarProductOrder.SearchOrderOutput>>(relativePath, input);
    }

    public Task CarProductOrderRefund(CarProductOrder.RefundOrderInput input)
    {
        var relativePath = "/CarProductOrder/Refund";
        return PostAsync(relativePath, input);
    }

    public async Task<List<GetOrderCountOutput>> GetCarProductOrderCount(GetOrderCountInput input)
    {
        var relativePath = "/CarProductOrder/GetOrderCount";
        return await PostAsync<GetOrderCountInput, List<GetOrderCountOutput>>(relativePath, input);
    }
    #endregion

    #region ReservationOrder

    public Task<PaymentInfoOutput> GetReservationOrderPaymentInfo(long orderId)
    {
        var relativePath = $"/ReservationOrder/PaymentInfo?orderId={orderId}";
        return GetAsync<PaymentInfoOutput>(relativePath);
    }

    #endregion

    #region OffsetOrder
    public async Task<IEnumerable<GetOffsetOrderListOutput>> GetOffsetOrderList(GetOffsetOrderListInput input)
    {
        var relativePath = "/OffsetOrder/GetList";
        return await PostAsync<GetOffsetOrderListInput, IEnumerable<GetOffsetOrderListOutput>>(relativePath, input);
    }
    #endregion

    #region MailOrder

    /// <summary>
    /// 订单详情
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    public Task<OrderDetailsOutput> GetMailOrderByBaseOrderId(long baseOrderId)
    {
        var relativePath = $"/MailOrder/DetailsByTenant?baseOrderId={baseOrderId}";
        return GetAsync<OrderDetailsOutput>(relativePath);
    }

    #endregion

    #region OrderPrintSetting

    public async Task<OrderPrintSettingBffOutput> GetOrderPrintSetting()
    {
        var relativePath = $"/OrderPrintSetting/Get";
        return await GetAsync<OrderPrintSettingBffOutput>(relativePath);
    }
    #endregion

    #region OrderPrice

    public async Task<List<OrderMultPriceOutput>> GetByBaseOrderId(long baseOrderId)
    {
        var relativePath = $"/OrderPrice/GetByBaseOrderId?baseOrderId={baseOrderId}";
        return await GetAsync<List<OrderMultPriceOutput>>(relativePath);
    }

    #endregion

    #region WorkOrderServiceEvaluation
    public async Task CreateWorkOrderServiceEvaluation(Contracts.Common.Order.DTOs.WorkOrderServiceEvaluation.CreateInput input)
    {
        var relativePath = "/WorkOrderServiceEvaluation/Create";
        await PostAsync(relativePath, input);
    }
    #endregion

    #region OrderDelayedPay

    public Task<OrderDelayedPayInfoOutput> OrderDelayedPayInfo(OrderDelayedPayInfoInput input)
    {
        var relativePath = "/OrderDelayed/OrderDelayedPayInfo";
        return PostAsync<OrderDelayedPayInfoInput, OrderDelayedPayInfoOutput>(relativePath, input);
    }

    /// <summary>
    /// 订单延时支付
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public Task OrderDelayedPay(OrderDelayedPayInput input)
    {
        var relativePath = "/OrderDelayed/Pay";
        return PostAsync<OrderDelayedPayInput>(relativePath, input);
    }

    public Task OrderDelayedCancel(OrderDelayedCancelInput input)
    {
        var relativePath = "/OrderDelayed/Cancel";
        return PostAsync<OrderDelayedCancelInput>(relativePath, input);
    }

    #endregion


    #region GDSOrder
    public async Task<GDSHotelPriceCheckOutput> GDSPriceCheck(GDSHotelPriceCheckInput input)
    {
        var relativePath = "/GDSOrder/GDSPriceCheck";
        return await PostAsync<GDSHotelPriceCheckInput, GDSHotelPriceCheckOutput>(relativePath, input);
    }

    public async Task<GetPriceCheckInfoOutput> GetGDSPriceCheckInfo(string bookingKey)
    {
        var relativePath = "/GDSOrder/GetPriceCheckInfo?bookingKey=" + bookingKey;
        return await GetAsync<GetPriceCheckInfoOutput>(relativePath);
    }
    #endregion

    #region OrderPaymentCard
    public async Task<bool> AddOrderPaymentCard(CreditCardGuaranteeInput payInput)
    {
        var relativePath = "/BaseOrder/AddOrderPaymentCard";
        return await PostAsync<CreditCardGuaranteeInput, bool>(relativePath, payInput);
    }
    #endregion

    #region GroupBookingAggregate
    public async Task<List<GetStatusStatisticsOutput>> GetGroupBookingAggregateStatusStatistics(GetStatusStatisticsInput input)
    {
        var relativePath = "/GroupBookingAggregate/GetStatusStatistics";
        return await PostAsync<GetStatusStatisticsInput, List<GetStatusStatisticsOutput>>(relativePath, input);
    }
    #endregion

    #region GroupBookingAreaSetting
    public async Task<GroupBookingAreaSettingOutput> GetGroupBookingAreaSetting()
    {
        var relativePath = "/GroupBookingAreaSetting/Get";
        return await GetAsync<GroupBookingAreaSettingOutput>(relativePath);
    }
    #endregion
}
