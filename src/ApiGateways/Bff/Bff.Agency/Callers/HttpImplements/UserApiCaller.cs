using Common.Caller;
using Common.ServicesHttpClient;
using Contracts.Common.User.DTOs;
using Contracts.Common.User.DTOs.AgencyUser;
using Contracts.Common.User.DTOs.OperationLog;
using Contracts.Common.User.DTOs.SupplierUser;
using Contracts.Common.User.DTOs.UserBinding;
using EfCoreExtensions.Abstract;
using Microsoft.Extensions.Options;

namespace Bff.Agency.Callers.HttpImplements;

public class UserApiCaller : HttpCallerBase, IUserApiCaller
{
    public UserApiCaller(IOptions<ServicesAddress> options, IHttpClientFactory httpClientFactory)
        : base(options.Value.User, httpClientFactory)
    {
    }

    #region Security

    public async Task SendCaptcha(CaptchaDTO input)
    {
        string relativePath = "/Security/SendCaptcha";
        await PostAsync<CaptchaDTO, string>(relativePath, input);
    }

    public async Task<bool> CheckCaptcha(CaptchaDTO input)
    {
        string relativePath = "/Security/CheckCaptcha";
        return await PostAsync<CaptchaDTO, bool>(relativePath, input);
    }

    public async Task<ImageCaptchaDto> GetImageCaptcha()
    {
        string relativePath = "/Security/GetImageCaptcha";
        return await GetAsync<ImageCaptchaDto>(relativePath);
    }

    public async Task<bool> CheckImageCaptcha(ImageCaptchaDto input)
    {
        string relativePath = "/Security/CheckImageCaptcha";
        return await PostAsync<ImageCaptchaDto, bool>(relativePath, input);
    }

    #endregion

    #region AgencyUser

    public async Task<AgencyUserDto> FindOne(AgencyUserFindOneInput input)
    {
        string relativePath = "/AgencyUser/FindOne";
        return await PostAsync<AgencyUserFindOneInput, AgencyUserDto>(relativePath, input);
    }

    public Task<AgencyUserDto> GetAgencyUserDetail(long id)
    {
        string relativePath = $"/AgencyUser/GetDetail?id={id}";
        return GetAsync<AgencyUserDto>(relativePath);
    }
    public Task<List<AgencyUserDto>> GetAgencyUserDetail(GetDetailsInput input)
    {
        string relativePath = "/AgencyUser/GetDetailByUserId";
        return PostAsync<GetDetailsInput, List<AgencyUserDto>>(relativePath, input);
    }

    public Task<List<AgencyUserDto>> GetAgencyUserInfo(GetDetailsInput input)
    {
        string relativePath = "/AgencyUser/GetUserInfo";
        return PostAsync<GetDetailsInput, List<AgencyUserDto>>(relativePath, input);
    }

    public async Task<PagingModel<AgencyUserDto>> SearchAgencyUser(Contracts.Common.User.DTOs.AgencyUser.SearchInput input)
    {
        string relativePath = "/AgencyUser/Search";
        return await PostAsync<Contracts.Common.User.DTOs.AgencyUser.SearchInput, PagingModel<AgencyUserDto>>(relativePath, input);
    }

    public async Task<string> RestPasswordAgencyUser(RestPasswordInput input)
    {
        string relativePath = "/AgencyUser/RestPassword";
        return await PostAsync<RestPasswordInput, string>(relativePath, input);
    }

    public async Task UpdateAgencyUser(Contracts.Common.User.DTOs.AgencyUser.UpdateInput input)
    {
        string relativePath = "/AgencyUser/Update";
        await PostAsync(relativePath, input);
    }

    public async Task<long> AddAgencyUser(Contracts.Common.User.DTOs.AgencyUser.AddInput input)
    {
        string relativePath = "/AgencyUser/Add";
        return await PostAsync<Contracts.Common.User.DTOs.AgencyUser.AddInput, long>(relativePath, input);
    }

    public async Task OnOrOffAgencyUser(OnOrOffInput input)
    {
        string relativePath = "/AgencyUser/OnOrOff";
        await PostAsync(relativePath, input);
    }

    public async Task<AgencyUserDto> AgencyUserFindOne(AgencyUserFindOneInput input)
    {
        string relativePath = "/AgencyUser/FindOne";
        return await PostAsync<AgencyUserFindOneInput, AgencyUserDto>(relativePath, input);
    }

    public Task UpdatePassword(UpdatePasswordInput input)
    {
        string relativePath = "/AgencyUser/UpdatePassword";
        return PostAsync<Contracts.Common.User.DTOs.UpdatePasswordInput>(relativePath, input);
    }

    public Task<bool> AgencyUsersAnyAsync(AgencyUserVerifyInput input)
    {
        string relativePath = "/AgencyUser/AgencyUsersAny";
        return PostAsync<AgencyUserVerifyInput, bool>(relativePath, input);
    }

    public Task SetEmailStatusAsync(AgencyUserEmailStatusInput input)
    {
        string relativePath = $"/AgencyUser/SetEmailStatus";
        return PostAsync<AgencyUserEmailStatusInput>(relativePath, input);
    }

    #endregion

    #region TenantUser

    public Task<UserSearchOuput> TenantUserFindOne(FindOneInput input)
    {
        var relativePath = "/TenantUser/FindOne";
        return PostAsync<FindOneInput, UserSearchOuput>(relativePath, input);
    }

    #endregion


    #region OperationLog
    public async Task AddOperationLog(OperationLogDto input)
    {
        string relativePath = "/OperationLog/AddOperationLog";
        await PostAsync<OperationLogDto>(relativePath, input);
    }

    #endregion

    #region UserBind

    public Task<List<GetUserBindingOutput>> GetUserBindings(GetUserBindingInput input)
    {
        var relativePath = "/UserBinding/GetUserBindings";
        return PostAsync<GetUserBindingInput, List<GetUserBindingOutput>>(relativePath, input);
    }

    public async Task UnboundUserBindings(UnboundUserBindingInput input)
    {
        string relativePath = "/UserBinding/Unbound";
        await PostAsync(relativePath, input);
    }

    #endregion
}
