using AutoMapper;
using Contracts.Common.Order.DTOs.BaseOrder;
using EfCoreExtensions.Abstract;

namespace Bff.Agency.Models.Order;

public class OrderProfile : Profile
{
    public OrderProfile()
    {
        CreateMap<InvoiceStatus, Contracts.Common.Order.Enums.InvoiceStatus>().ReverseMap();
        CreateMap<GetListByCanBeInvoiceBffInput, GetListByCanBeInvoiceInput>();
        CreateMap<CanBeInvoiceDto, GetListByCanBeInvoiceBffOutput>().ReverseMap();
        CreateMap<PagingModel<CanBeInvoiceDto>, PagingModel<GetListByCanBeInvoiceBffOutput>>();

        CreateMap<BffCanIssueInvoiceListInput, GetListByCanBeInvoiceInput>().ReverseMap();
    }
}
