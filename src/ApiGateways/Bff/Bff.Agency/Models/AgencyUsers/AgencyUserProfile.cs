using AutoMapper;
using Contracts.Common.User.DTOs.AgencyUser;
using EfCoreExtensions.Abstract;

namespace Bff.Agency.Models.AgencyUsers;

public class AgencyUserProfile : Profile
{
    public AgencyUserProfile()
    {
        CreateMap<UpdateInput, Contracts.Common.User.DTOs.AgencyUser.UpdateInput>();
        CreateMap<AddInput, Contracts.Common.User.DTOs.AgencyUser.AddInput>();
        CreateMap<SearchInput, Contracts.Common.User.DTOs.AgencyUser.SearchInput>();

        CreateMap<AgencyUserDto, AgencyUserSearchOutput>().ReverseMap();

        CreateMap<PagingModel<AgencyUserDto>, PagingModel<AgencyUserSearchOutput>>().ReverseMap();
    }
}
