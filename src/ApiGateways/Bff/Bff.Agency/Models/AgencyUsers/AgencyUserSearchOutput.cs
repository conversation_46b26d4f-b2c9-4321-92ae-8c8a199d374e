namespace Bff.Agency.Models.AgencyUsers;

public class AgencyUserSearchOutput
{

    /// <summary>
    /// 用户Id
    /// </summary>
    public long AgencyUserId { get; set; }

    public long TenantId { get; set; }

    /// <summary>
    /// 分销商Id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 账号
    /// </summary>
    public string UserName { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string RealName { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string PhoneNumber { get; set; }

    /// <summary>
    /// 是否可用
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// 是否已经绑定微信
    /// </summary>
    public bool WechatBind { get; set; }

    public string QrCodeUrl { get; set; }

    public DateTime CreateTime { get; set; }

    public string AgencyName { get; set; }

    public string? CountryDialCode { get; set; }
}
