using Contracts.Common.Order.Enums;

namespace Bff.Agency.Models.HotelGroupBookingOrder;

public class HotelOrderGuestItemOutput
{
    /// <summary>
    /// 团房单id
    /// </summary>
    public long GroupBookingOrderId { get; set; }

    public long ResourceHotelId { get; set; }
    /// <summary>
    /// 酒店名称
    /// </summary>
    public string HotelZHName { get; set; }
    public string HotelENName { get; set; }

    public int CountryCode { get; set; }

    public int ProvinceCode { get; set; }

    public int CityCode { get; set; }

    /// <summary>
    /// 子单号
    /// </summary>
    public long[] BaseOrderIds { get; set; }

    /// <summary>
    /// 酒店订单状态 仅当文件不存在 && 状态为待确认、待入住时，显示上传按钮
    /// </summary>
    public HotelOrderStatus HotelOrderStatus { get; set; }

    /// <summary>
    /// 入住人信息excel文件路径 存在可以下载
    /// </summary>
    public string? GuestFilePath { get; set; }
    /// <summary>
    /// 订单项id集合
    /// </summary>
    public long[] GroupBookingOrderItemIds { get; set; }
}