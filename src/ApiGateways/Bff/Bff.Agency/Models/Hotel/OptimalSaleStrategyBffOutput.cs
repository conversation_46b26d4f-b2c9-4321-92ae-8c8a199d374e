using System.ComponentModel;

namespace Bff.Agency.Models.Hotel;

public class OptimalSaleStrategyBffOutput : GetSaleStrategyBffOutput
{
    /// <summary>
    /// 房型价格策略
    /// </summary>
    public new List<OptimalSaleStrategyBffItem> PriceStrategies { get; set; } = new();
}

public class OptimalSaleStrategyBffItem : SaleStrategyBffItem
{
    /// <summary>
    /// 最低价类型
    /// </summary>
    public OptimalSaleStrategyLowestPriceType? LowestPriceType { get; set; }
}

public enum OptimalSaleStrategyLowestPriceType
{
    /// <summary>
    /// 无餐不可取消最低价
    /// </summary>
    [Description("无餐不可取消最低价")]
    NoMealNonCancelable,

    /// <summary>
    /// 含餐不可取消最低价
    /// </summary>
    [Description("含餐不可取消最低价")]
    MealNonCancelable,

    /// <summary>
    /// 无餐限时取消最低价
    /// </summary>
    [Description("无餐限时取消最低价")]
    NoMealCancelable,

    /// <summary>
    /// 含餐限时取消最低价
    /// </summary>
    [Description("含餐限时取消最低价")]
    MealCancelable,
}