using Contracts.Common.Hotel.Enums;
using Contracts.Common.Resource.DTOs.ThirdHotel;

namespace Bff.Agency.Models.HotelOrder;

public class PreOrderOutput
{
    /// <summary>
    /// 试单查价code
    /// </summary>
    public CheckPriceStrategySaleCode Code { get; set; }

    /// <summary>
    /// 每日价格和
    /// </summary>
    public decimal SumChannelPrice { get; set; }

    /// <summary>
    /// 每日售价
    /// </summary>
    public IEnumerable<CalendarPriceDto> CalendarPrices { get; set; }

    /// <summary>
    /// 总价 每日价格和*间数
    /// </summary>
    public decimal TotalChannelPrice { get; set; }

    /// <summary>
    /// 税费提醒
    /// </summary>
    public string? TaxDescription { get; set; }

    /// <summary>
    /// 税费说明
    /// </summary>
    public CheckAvailabilityArrivalTaxFeeOutput[]? ArrivalTaxFees { get; set; }
}

public class CalendarPriceDto
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 支付币种 =>用户支付币种，B2B为分销商币种
    /// </summary>
    public string PaymentCurrencyCode { get; set; }

    /// <summary>
    /// 售价（渠道加价之后的值）
    /// </summary>
    public decimal ChannelPrice { get; set; }

}