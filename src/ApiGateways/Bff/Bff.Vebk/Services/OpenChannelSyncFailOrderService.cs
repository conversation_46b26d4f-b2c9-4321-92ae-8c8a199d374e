using Bff.Vebk.Callers;
using Bff.Vebk.Models.ScenicTicketOrder.OTA;
using Bff.Vebk.Services.Interfaces;
using Contracts.Common.Order.DTOs.OpenChannelOrder;
using Contracts.Common.Order.DTOs.OpenChannelSyncFailOrder;
using Contracts.Common.Order.DTOs.TicketsCombinationOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Product.DTOs.CarProductSku;
using Contracts.Common.Scenic.DTOs.TicketsCombinationPackage;
using Contracts.Common.Scenic.DTOs.TicketTimeSlot;
using Newtonsoft.Json;

namespace Bff.Vebk.Services;

public class OpenChannelSyncFailOrderService : IOpenChannelSyncFailOrderService
{
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IProductApiCaller _productApiCaller;
    private readonly IScenicSpotApiCaller _scenicSpotApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IOpenPlatformService _openPlatformService;

    /// <summary>
    /// 同步的订单类型
    /// </summary>
    private readonly OrderType[] _syncOrderTypes = {
        OrderType.ScenicTicket,
        OrderType.TravelLineOrder,
        OrderType.CarProduct
    };

    public OpenChannelSyncFailOrderService(
        IOrderApiCaller orderApiCaller,
        IProductApiCaller productApiCaller,
        IScenicSpotApiCaller scenicSpotApiCaller,
        ITenantApiCaller tenantApiCaller,
        IOpenPlatformService openPlatformService)
    {
        _orderApiCaller = orderApiCaller;
        _productApiCaller = productApiCaller;
        _scenicSpotApiCaller = scenicSpotApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _openPlatformService = openPlatformService;
    }

    public async Task Sync(OpenChannelSyncFailOrderAddInput syncFailOrderInfo)
    {
        if (!_syncOrderTypes.Contains(syncFailOrderInfo.OrderType))
        {
            return;
        }
        
        syncFailOrderInfo.SellingChannels = _openPlatformService.MapSellingPlatformToSellingChannels(syncFailOrderInfo.SellingPlatform);

        switch (syncFailOrderInfo.OrderType)
        {
            case OrderType.ScenicTicket:
                await SupplementByScenicTicket(syncFailOrderInfo);
                break;
            case OrderType.TravelLineOrder:
                await SupplementByTravelLine(syncFailOrderInfo);
                break;
            case OrderType.CarProduct:
                await SupplementByCarProduct(syncFailOrderInfo);
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }
        
        await SupplementByAgency(syncFailOrderInfo);
        
        if (syncFailOrderInfo.TenantId is > 0)
        {
            if (syncFailOrderInfo is {OrderType: OrderType.ScenicTicket, IsCombination: true})
            {
                //处理门票组合异常订单
                var ticketCombinationAbnormalOrderRequest = new AddAbnormalCombinationOrderInput
                {
                    Quantity = syncFailOrderInfo.Quantity,
                    TicketsCombinationId = syncFailOrderInfo.ProductId!.Value,
                    TicketsCombinationName = syncFailOrderInfo.ProductName,
                    TicketsCombinationPackageId = syncFailOrderInfo.SkuId!.Value,
                    TicketsCombinationPackageName = syncFailOrderInfo.SkuName,
                    PaymentAmount = syncFailOrderInfo.PaymentAmount,
                    PaymentCurrencyCode = syncFailOrderInfo.CurrencyCode,
                    ChannelOrderNo = syncFailOrderInfo.ChannelOrderNo,
                    DataContentJson = syncFailOrderInfo.DataContentJson,
                    AbnormalReason = syncFailOrderInfo.Reason,
                    SellingPlatform = syncFailOrderInfo.SellingPlatform,
                    SellingChannels = syncFailOrderInfo.SellingChannels,
                    AgencyId = syncFailOrderInfo.AgencyId,
                    TravelDate = syncFailOrderInfo.TravelDate,
                    CombinationType = syncFailOrderInfo.CombinationType
                };
                _ = _orderApiCaller.SetTenantId(syncFailOrderInfo.TenantId).AddTicketsCombinationAbnormalOrder(ticketCombinationAbnormalOrderRequest);
            }
            else
            {
                //普通异常订单
                _ =  _orderApiCaller.SetTenantId(syncFailOrderInfo.TenantId).AddOpenChannelSyncFailOrder(syncFailOrderInfo);
            }
        }
    }

    private async Task SupplementByScenicTicket(OpenChannelSyncFailOrderAddInput syncFailOrderInfo)
    {
        try
        {
            var skuId = syncFailOrderInfo.SkuId!.Value;

            if (syncFailOrderInfo.IsCombination)
            {
                var combinationPackage = (
                    await _scenicSpotApiCaller.SetTenantId(syncFailOrderInfo.TenantId).QueryTicketsCombinationPackageItems(new QueryTicketsCombinationPackageItemInput
                    {
                        TicketsCombinationPackageId = skuId
                    })).FirstOrDefault();
                syncFailOrderInfo.ProductName = combinationPackage.TicketsCombinationName;
                syncFailOrderInfo.ProductId = combinationPackage.TicketsCombinationId;
                syncFailOrderInfo.SkuId = combinationPackage.TicketsCombinationPackageId;
                syncFailOrderInfo.SkuName = combinationPackage.TicketsCombinationPackageName;
                syncFailOrderInfo.CombinationType = combinationPackage.CombinationType;
            }
            else
            {
                //需要查看是否是时段
                var timeSlot = (await _scenicSpotApiCaller.SetTenantId(syncFailOrderInfo.TenantId).GetTimeSlotsByIds(new GetTicketTimeSlotInput
                {
                    TimeSlotIds = new List<long> {skuId}
                })).FirstOrDefault();
                if (timeSlot != null)
                {
                    syncFailOrderInfo.TimeSlotId = timeSlot.TimeSlotId;
                    syncFailOrderInfo.TimeSlot = timeSlot.Time;
                    syncFailOrderInfo.SkuId = timeSlot.TicketId;
                    skuId = timeSlot.TicketId;
                }

                //查询门票产品信息
                var ticket = await _scenicSpotApiCaller.SetTenantId(syncFailOrderInfo.TenantId).GetScenicTicketDetail(skuId);
                syncFailOrderInfo.SkuName = ticket?.Name;
                syncFailOrderInfo.ProductId = ticket?.ScenicSpotId;
                syncFailOrderInfo.SupplierId = ticket?.SupplierId;
                if (ticket != null)
                {
                    var scenicSpot = await _scenicSpotApiCaller.SetTenantId(syncFailOrderInfo.TenantId).ScenicSpotsDetail(ticket.ScenicSpotId);
                    syncFailOrderInfo.ProductName = scenicSpot?.Name;
                }
            }
        }
        catch (Exception e)
        {
            // ignored
        }
    }

    private async Task SupplementByTravelLine(OpenChannelSyncFailOrderAddInput syncFailOrderInfo)
    {
        try
        {
            var skuId = syncFailOrderInfo.SkuId!.Value;
            var lineProductSku = await _productApiCaller.SetTenantId(syncFailOrderInfo.TenantId).GetLineProductSkuDetail(skuId);
            syncFailOrderInfo.ProductId = lineProductSku?.LineProductId;
            syncFailOrderInfo.SkuName = lineProductSku?.Name;
            if (lineProductSku != null)
            {
                var productId = syncFailOrderInfo.ProductId!.Value;
                var lineProduct = (await _productApiCaller.SetTenantId(syncFailOrderInfo.TenantId).GetLineProductDetail(productId)).FirstOrDefault();
                syncFailOrderInfo.ProductName = lineProduct?.Title;
                syncFailOrderInfo.SupplierId = lineProduct?.SupplierId;
            }
        }
        catch (Exception e)
        {
            // ignored
        }
    }

    private async Task SupplementByCarProduct(OpenChannelSyncFailOrderAddInput syncFailOrderInfo)
    {
        try
        {
            var skuId = syncFailOrderInfo.SkuId!.Value;
            var carProductSku = (await _productApiCaller.SetTenantId(syncFailOrderInfo.TenantId).GetCarProductSkDetails(new CarProductSkuInput
            {
                CarProductSkuIds = new []{skuId}
            })).FirstOrDefault();
            if (carProductSku != null)
            {
                syncFailOrderInfo.ProductId = carProductSku.CarProductId;
                syncFailOrderInfo.SkuName = carProductSku.Name;
                var productId = syncFailOrderInfo.ProductId!.Value;
                var carProduct = await _productApiCaller.SetTenantId(syncFailOrderInfo.TenantId).CarProductDetail(productId);
                syncFailOrderInfo.ProductName = carProduct?.Title;
                syncFailOrderInfo.SupplierId = carProduct?.SupplierId;
            }
        }
        catch (Exception e)
        {
            // ignored
        }
    }

    private async Task SupplementByAgency(OpenChannelSyncFailOrderAddInput syncFailOrderInfo)
    {
        try
        {
            var agencyApiType = _openPlatformService.MapSellingPlatformToAgencyApiType(syncFailOrderInfo.SellingPlatform);
            var agencySetting = syncFailOrderInfo.TenantId is > 0
                ? await _tenantApiCaller.SetTenantId(syncFailOrderInfo.TenantId).GetAgencyApiSetting(agencyApiType)
                : await _tenantApiCaller.GetAgencyApiSetting(agencyApiType);
            if (agencySetting != null)
            {
                syncFailOrderInfo.AgencyId = agencySetting.AgencyId;
                var agency = await _tenantApiCaller.SetTenantId(syncFailOrderInfo.TenantId).GetAgencyDetail(agencySetting.AgencyId);
                syncFailOrderInfo.CurrencyCode = agency.CurrencyCode;
            }
        }
        catch (Exception e)
        {
            //ignore
        }
    }

    record SyncFailOrderContentData(bool NeedConfirmOrder);
    public async Task OpenChannelOrderConfirm(long syncFailOrderId,string[] channelOrderNos)
    {
        // 查询异常单,判断是否需要先接单
        var syncFailOrder = await _orderApiCaller.GetOpenChannelSyncFailOrderDetail(new GetOpenChannelSyncFailOrderInput
        {
            SyncFailOrderId = syncFailOrderId
        });
        if (syncFailOrder != null)
        {
            var syncFailOrderContentData = JsonConvert.DeserializeObject<SyncFailOrderContentData>(syncFailOrder.DataContentJson);
            var needConfirmOrder = syncFailOrderContentData?.NeedConfirmOrder ?? false;
            if (needConfirmOrder)
            {
                foreach (var channelOrderNoItem in channelOrderNos)
                {
                    _ = _orderApiCaller.ConfirmChannelOrder(new ConfirmChannelOrderInput
                    {
                        SellingPlatform = SellingPlatform.TikTok,
                        ChannelOrderNo = channelOrderNoItem
                    });
                }
            }
        }
    }
}