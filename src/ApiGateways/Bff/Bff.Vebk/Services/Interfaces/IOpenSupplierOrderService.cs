using Bff.Vebk.Models.OpenSupplierOrder;
using Contracts.Common.Order.DTOs.OpenSupplierOrder;
using Contracts.Common.Order.Enums;

namespace Bff.Vebk.Services.Interfaces;

public interface IOpenSupplierOrderService
{
    /// <summary>
    /// 发货重试
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<DeliveryRetrySupplierOrderBffOutputDto> DeliveryRetry(DeliveryRetrySupplierOrderBffInputDto input);
    
    /// <summary>
    /// 订单凭证处理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<SupplierOrderBaseNotifyBffOutput> VoucherDeliveryProcess(OpenSupplierVoucherProcessDto input);

    /// <summary>
    /// 创单失败订单检查是否需要附加信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<CheckOrderRelatedExtraInfoBffOutput>> CheckOrderRelatedExtraInfos(
        CheckOrderRelatedExtraInfoBffInput input);

    /// <summary>
    /// 图片下载
    /// </summary>
    /// <param name="downImages"></param>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task<List<string>> ThrottlerDownloadImages(List<string> downImages, long tenantId);
}