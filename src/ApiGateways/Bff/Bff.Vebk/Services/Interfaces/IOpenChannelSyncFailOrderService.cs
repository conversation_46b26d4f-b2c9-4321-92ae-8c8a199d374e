using Contracts.Common.Order.DTOs.OpenChannelSyncFailOrder;

namespace Bff.Vebk.Services.Interfaces;

public interface IOpenChannelSyncFailOrderService
{
    /// <summary>
    /// 同步记录失败订单
    /// </summary>
    Task Sync(OpenChannelSyncFailOrderAddInput syncFailOrderInfo);

    /// <summary>
    /// 异常单渠道订单确认
    /// </summary>
    /// <param name="syncFailOrderId"></param>
    /// <param name="channelOrderNos"></param>
    /// <returns></returns>
    Task OpenChannelOrderConfirm(long syncFailOrderId, string[] channelOrderNos);
}