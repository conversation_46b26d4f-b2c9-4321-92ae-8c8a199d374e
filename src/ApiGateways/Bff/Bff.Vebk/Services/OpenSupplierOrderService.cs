using Bff.Vebk.Callers;
using Bff.Vebk.Models.OpenSupplierOrder;
using Bff.Vebk.Services.Interfaces;
using Contracts.Common.Order.DTOs.OpenSupplierOrder;
using Contracts.Common.Order.DTOs.ScenicTicketSupplierOrder;
using Contracts.Common.Order.DTOs.TravelLineOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Product.DTOs.OpenSupplier;
using Contracts.Common.Resource.DTOs.FileResource;
using Contracts.Common.Resource.Enums;

namespace Bff.Vebk.Services;

public class OpenSupplierOrderService : IOpenSupplierOrderService
{
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IProductApiCaller _productApiCaller;
    private readonly IOpenPlatformService _openPlatformService;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<OpenSupplierOrderService> _logger;
    
    private const int _successCode = 200;
    private const int _errorCode = -1;
    private static readonly SemaphoreSlim _methodThrottler = new(5,5);
    
    public OpenSupplierOrderService(
        IResourceApiCaller resourceApiCaller,
        IOrderApiCaller orderApiCaller,
        IProductApiCaller productApiCaller,
        IOpenPlatformService openPlatformService,
        IHttpClientFactory httpClientFactory,
        ILogger<OpenSupplierOrderService> logger)
    {
        _resourceApiCaller = resourceApiCaller;
        _orderApiCaller = orderApiCaller;
        _productApiCaller = productApiCaller;
        _openPlatformService = openPlatformService;
        _httpClientFactory = httpClientFactory;
        _logger = logger;
    }

    public async Task<DeliveryRetrySupplierOrderBffOutputDto> DeliveryRetry(DeliveryRetrySupplierOrderBffInputDto input)
    {
        var result = new DeliveryRetrySupplierOrderBffOutputDto();
        try
        {
            //查询供应商订单详情
            var supplierOrderDetail =
                await _orderApiCaller.GetOpenSupplierOrderDetail(new GetOpenSupplierOrderDetailInput
                {
                    BaseOrderId = input.BaseOrderId, 
                    OrderType = input.OrderType
                });

            if (supplierOrderDetail.Vouchers.Any() is false && supplierOrderDetail?.OpenSupplierOrderStatus != OpenSupplierOrderDetailStatus.Shipped)
            {
                result.Msg = "查无供应商凭证信息";
            }
            else
            {
                var logTemplate = "[OpenSupplier]手动发货:{0}";
                var logMessage = string.Format(logTemplate, "{@message}");
                var eTicketUrl =
                    supplierOrderDetail.Vouchers.FirstOrDefault(x => x.FileType == OpenSupplierNotifyFileType.Pdf)
                        ?.VoucherUrl;

                var deliveryResult = await VoucherDeliveryProcess(
                    new OpenSupplierVoucherProcessDto
                    {
                        TenantId = input.TenantId,
                        BaseOrderId = input.BaseOrderId,
                        SupplierOrderId = supplierOrderDetail.SupplierOrderId,
                        ETicketUrl = eTicketUrl,
                        OrderType = input.OrderType,
                        LogMessage = logMessage,
                        Vouchers = supplierOrderDetail.Vouchers
                            .Select(x => new DeliveryUrlNotifyVouchers
                            {
                                FileType = x.FileType, VoucherUrl = x.VoucherUrl
                            })
                            .ToList()
                    });

                result.Msg = deliveryResult.Msg;
                result.IsSuccess = deliveryResult.Code == _successCode;
            }
        }
        catch (BusinessException e)
        {
            result.Msg = e.Message;
        }

        return result;
    }
    
    public async Task<SupplierOrderBaseNotifyBffOutput> VoucherDeliveryProcess(OpenSupplierVoucherProcessDto input)
    {
        var result = new SupplierOrderBaseNotifyBffOutput
        {
            Code = _successCode
        };
        //处理开放平台发货凭证文件
        //目前开放平台一次通知只会有一张凭证.支持空凭证数据
        var vouchers = new List<SupplierOrderVoucherItem>();
        if (!string.IsNullOrEmpty(input.ETicketUrl))
        {
            var eTickUrlParseResult = await ParseFile(
                tenantId: input.TenantId, 
                sourceUrl : input.ETicketUrl, 
                logMessage: input.LogMessage,
                vouchers: input.Vouchers);
            if (eTickUrlParseResult != null)
            {
                var deliveryVoucher = new SupplierOrderVoucherItem
                {
                    FilePath = eTickUrlParseResult.FilePath,
                    ThumbnailPath = eTickUrlParseResult.ThumbnailPath,
                    SourcePath = eTickUrlParseResult.SourcePath,
                    ImageSourcePath = eTickUrlParseResult.ImageSourcePath,
                    PdfSourcePath = eTickUrlParseResult.PdfSourcePath
                };

                vouchers.Add(deliveryVoucher);
            }
            else
            {
                //返回转换失败的结果给开放平台
                result.Code = _errorCode;
                result.Msg = "转换失败";
                result.NeedRetry = true;
                return result;
            }
        }

        var deliveryRequest = new SupplierOrderDeliveryInput
        {
            BaseOrderId = input.BaseOrderId,
            SupplierOrderId = input.SupplierOrderId,
            Vouchers = vouchers,
            ErrorCode = vouchers.Any() is false ? (int)OrderBusinessErrorCodeType.SaasDeliveryFailed : null
        };
        
        //区分订单业务类型
        switch (input.OrderType)
        {
            case OrderType.TravelLineOrder:
                
                //线路业务发货
                var lineSupplierOrderDelivery = await _orderApiCaller.SetTenantId(input.TenantId).TravelLineSupplierOrderDelivery(deliveryRequest);
                result.Code = lineSupplierOrderDelivery.Code;
                result.Msg = lineSupplierOrderDelivery.Msg;
                
                break;
            default:
                
                //门票业务发货
                var supplierOrderDelivery = await _orderApiCaller.SetTenantId(input.TenantId).ScenicTicketSupplierOrderDelivery(deliveryRequest);
                result.Code = supplierOrderDelivery.Code;
                result.Msg = supplierOrderDelivery.Msg;
                
                break;
        }
        return result;
    }

    public async Task<List<CheckOrderRelatedExtraInfoBffOutput>> CheckOrderRelatedExtraInfos(CheckOrderRelatedExtraInfoBffInput input)
    {
        var result = new List<CheckOrderRelatedExtraInfoBffOutput>();
        var orderRelatedData =
            new List<(long baseOrerId, OpenSupplierType openSupplierType, string productId, string? optionId, string?
                skuId)>();
        
        var allOrderTypes = input.Items.GroupBy(x => x.OrderType).Select(x => x.Key).ToList();
        if (allOrderTypes.Contains(OrderType.ScenicTicket))
        {
            var baseOrderIds = input.Items.Where(x => x.OrderType == OrderType.ScenicTicket)
                .Select(x=>x.BaseOrderId).ToArray();
            var scenicTicketOrderDetails = await _orderApiCaller.GetScenicTicketOrderSimpleInfo(baseOrderIds);
            var scenicTicketRelatedData = scenicTicketOrderDetails
                .Where(x => x.OpenSupplierType.HasValue)
                .Select(x => new ValueTuple<long, OpenSupplierType, string, string?, string?>(x.BaseOrderId,
                    x.OpenSupplierType!.Value, x.SupplierActivityId, x.SupplierPackageId, x.SupplierSkuId))
                .ToList();
            orderRelatedData.AddRange(scenicTicketRelatedData);
        }

        if (allOrderTypes.Contains(OrderType.TravelLineOrder))
        {
            var baseOrderIds = input.Items.Where(x => x.OrderType == OrderType.TravelLineOrder)
                .Select(x=>x.BaseOrderId).ToList();
            //查询订单关联的套餐子项id
            var orderSkuItems = await _orderApiCaller.QueryTravelLineOrderSkuTypeItems(new QueryTravelLineOrderSkuTypeItemInput
            {
                BaseOrderIds = baseOrderIds
            });
            
            var lineOrderRelatedData = orderSkuItems.SkuTypeItems
                .Where(x => x.OpenSupplierType.HasValue)
                .Select(x => new ValueTuple<long, OpenSupplierType, string, string?, string?>(x.BaseOrderId,
                    x.OpenSupplierType!.Value, x.ActivityId, x.PackageId, x.SkuId))
                .ToList();
            orderRelatedData.AddRange(lineOrderRelatedData);
        }
        
        var request = new QueryOpenSupplierSkuExtraInfoInput
        {
            OpenSupplierTypes = orderRelatedData.Select(x=>x.openSupplierType).Distinct().ToList(),
            ProductIds = orderRelatedData.Select(x=>x.productId).Distinct().ToList(),
            OptionIds =orderRelatedData.Select(x=>x.optionId).Distinct().ToList(),
            SkuIds = orderRelatedData.Select(x=>x.skuId).Distinct().ToList()
        };
        var response = await _productApiCaller.QueryOpenSupplierSkuExtraInfo(request);

        foreach (var relateItem in orderRelatedData)
        {
            var extraInfos = response.Where(x => x.OpenSupplierType == relateItem.openSupplierType
                                                 && x.ProductId == relateItem.productId)
                .SelectMany(x => x.Sub)
                .Where(x => x.OptionId == relateItem.optionId && x.SkuId == relateItem.skuId)
                .SelectMany(x=>x.ExtraInfos)
                .Select(x=> new CheckOrderRelatedExtraInfoBffOutput
                {
                    BaseOrderId = relateItem.baseOrerId,
                    DataType = x.DataType
                })
                .ToList();
            
            result.AddRange(extraInfos);
        }

        return result;
    }

    public async Task<List<string>> ThrottlerDownloadImages(List<string> downImages,long tenantId)
    {
        await _methodThrottler.WaitAsync();
        try
        {
            var result = new List<string>();

            const int maxImages = 20;
            const int maxConcurrentDownloads = 5; //最多同时下载5张图片
            const int downloadTimeoutSeconds = 20; //下载图片超时20秒

            // 目前最多接受20张
            var images = downImages.Take(maxImages).ToList();
            var uploadRequest = new ParseAndUploadInput
            {
                IsPdfToSingleImage = false,
                IsOneImageToPdf = false,
                IsPublishAsync = false,
                OssObjectPublicReadAcl = true, //公共读
                TenantId = tenantId,
                Streams = new List<ParseStream>()
            };
            using var client = _httpClientFactory.CreateClient();
            client.Timeout = TimeSpan.FromSeconds(downloadTimeoutSeconds);

            // 使用信号量控制的异步下载
            var downloadTasks = new List<Task<ParseStream?>>();
            using var throttler = new SemaphoreSlim(maxConcurrentDownloads);

            foreach (var imageUrl in images)
            {
                downloadTasks.Add(DownloadImageAsync(client, imageUrl, throttler));
            }

            var parseStreams = await Task.WhenAll(downloadTasks);
            uploadRequest.Streams.AddRange(parseStreams.Where(x => x != null)!);
            if (uploadRequest.Streams.Count > 0)
            {
                var parseVoucherFileResponse = await _resourceApiCaller.ParseAndUpload(uploadRequest);
                result = parseVoucherFileResponse
                    .Where(x => x is { Success: true, FileType : FileType.Image })
                    .Select(x => x.FilePath)
                    .ToList();
            }

            return result;
        }
        finally
        {
            _methodThrottler.Release();
        }
    }

    #region private

    private async Task<SupplierOrderVoucherItem?> ParseFile(long tenantId, string sourceUrl, string logMessage,
        List<DeliveryUrlNotifyVouchers> vouchers)
    {
        SupplierOrderVoucherItem? voucher;
        try
        {
            // 判断是否需要转换
            var pdfVoucher = vouchers.FirstOrDefault(x => x.FileType == OpenSupplierNotifyFileType.Pdf)
                ?.VoucherUrl;
            var imageVoucher = vouchers.FirstOrDefault(x => x.FileType == OpenSupplierNotifyFileType.Image)
                ?.VoucherUrl;
            
            // 下载`供应商源pdf文件/开放平台转换后的pdf文件`
            // 场景1: 正常发货通知处理.存在pdf和image.所以直接记录
            // 场景2 :重新获取订单信息,存在一个pdf源文件.需要转换成image.并且记录
            var client = _httpClientFactory.CreateClient();
            using var response = await client.GetAsync(sourceUrl);
            response.EnsureSuccessStatusCode();
            using var fileStream = await response.Content.ReadAsStreamAsync();
            var contentType = response.Content.Headers.ContentType.ToString();
            var fileType = _openPlatformService.CheckFileType(contentType);
            var uploadRequest = new ParseAndUploadInput
            {
                TenantId = tenantId,
                IsPdfToSingleImage = imageVoucher == null, //  image源文件不存在时,需要转换成image
                IsOneImageToPdf = pdfVoucher == null, //  pdf源文件不存在时,需要转换成pdf
                IsPublishAsync = false,
                Streams = new List<ParseStream>
                {
                    new ParseStream
                    {
                        FileType = fileType, 
                        Stream = fileStream
                    }
                }
            };
            var parsePdf = (await _resourceApiCaller.ParseAndUpload(uploadRequest)).ToList();
            if (parsePdf.Any(x => x.Success == false))
            {
                var errorMessage = parsePdf.FirstOrDefault(x => x.Success == false)?.ErrorMessage;
                _logger.LogWarning(logMessage, errorMessage);
                voucher = null;
            }
            else
            {
                voucher = parsePdf.Select(x => new SupplierOrderVoucherItem
                    {
                        FilePath = x.FilePath,
                        ThumbnailPath = x.ThumbnailPath, 
                        SourcePath = sourceUrl,
                        PdfSourcePath = pdfVoucher ?? (x.CompositePdfPath ?? string.Empty),
                        ImageSourcePath = imageVoucher ?? (x.CompositeImagePath ?? string.Empty)
                    })
                    .FirstOrDefault();
            }
        }
        catch (Exception e)
        {
            _logger.LogError(logMessage, e);
            voucher = null;
        }

        return voucher;
    }
    
    private async Task<ParseStream?> DownloadImageAsync(HttpClient client, string imageUrl, SemaphoreSlim throttler)
    {
        try
        {
            await throttler.WaitAsync();
            using var response = await client.GetAsync(imageUrl, HttpCompletionOption.ResponseHeadersRead);
            response.EnsureSuccessStatusCode();
            await using var fileStream = new MemoryStream();
            await response.Content.CopyToAsync(fileStream);
            fileStream.Seek(0, SeekOrigin.Begin);
            var contentType = response.Content.Headers.ContentType?.ToString() ?? "";
            var fileType = _openPlatformService.CheckFileType(contentType);
            return new ParseStream { FileType = fileType, Stream = fileStream };
        }
        catch (Exception e) when (e is not OperationCanceledException)
        {
            _logger.LogError(e, "下载产品图片失败: {ImageUrl}", imageUrl);
            return null;
        }
        finally
        {
            throttler.Release();
        }
    }

    #endregion
}