using Bff.Vebk.Models.OpenSupplierProduct;
using FluentValidation;

namespace Bff.Vebk.Validators.OpenSupplierProduct;

public class RetryMatchBasicProductBffInputValidator : AbstractValidator<RetryMatchBasicProductBffInput>
{
    public RetryMatchBasicProductBffInputValidator()
    {
        RuleFor(x => x.SupplierId).NotEmpty();
        RuleFor(x => x.SupplierProductId).NotEmpty();
        RuleFor(x => x.SupplierSkuIds).NotEmpty();
    }
}