using Bff.Vebk.Models.LineProduct;
using FluentValidation;

namespace Bff.Vebk.Validators.LineProduct;

public class UpdateOperationModuleSettingBffInputValidator : AbstractValidator<UpdateOperationModuleSettingBffInput>
{
    public UpdateOperationModuleSettingBffInputValidator()
    {
        RuleFor(x => x.LineProductId).NotEmpty();
        RuleFor(x => x.PreSaleStaff).NotEmpty();
        RuleFor(x => x.AfterSaleStaff).NotEmpty();
    }
}