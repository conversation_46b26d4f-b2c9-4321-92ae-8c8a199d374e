using Bff.Vebk.Models.HotelGroupBookingOrder;
using FluentValidation;

namespace Bff.Vebk.Validators.GroupBookingOrder;

public class AddGuestInputValidator:AbstractValidator<AddGuestInput>
{
    public AddGuestInputValidator() 
    {
        RuleFor(x => x.GroupBookingOrderId).GreaterThan(0);
        RuleFor(x => x.GroupBookingOrderItemIds).NotEmpty();
        RuleFor(x => x.FilePath).NotEmpty();
        RuleFor(x => x.GuestInfos).NotEmpty();
        RuleFor(x => x.GuestInfos).NotEmpty()
           .Must(x => x.All(g => !string.IsNullOrWhiteSpace(g.GuestName)
           || (!string.IsNullOrWhiteSpace(g.FirstName) && !string.IsNullOrWhiteSpace(g.LastName))));
    }
}
