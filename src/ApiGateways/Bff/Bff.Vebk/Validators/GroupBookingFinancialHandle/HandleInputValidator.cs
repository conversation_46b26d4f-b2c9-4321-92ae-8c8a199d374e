using Bff.Vebk.Models.GroupBookingFinancialHandleOrder;
using FluentValidation;

namespace Bff.Vebk.Validators.GroupBookingFinancialHandle;

public class HandleInputValidator : AbstractValidator<HandleInput>
{
    public HandleInputValidator()
    {
        RuleFor(s => s.Id).GreaterThan(0);
        RuleFor(s => s.Status).IsInEnum().Must(s => s is Contracts.Common.Order.Enums.GroupBookingFinancialHandleOrderStatus.Finished
            or Contracts.Common.Order.Enums.GroupBookingFinancialHandleOrderStatus.Rejected
            or Contracts.Common.Order.Enums.GroupBookingFinancialHandleOrderStatus.Withdrawn);
        RuleFor(s => s.RejectReason).NotEmpty().When(s => s.Status == Contracts.Common.Order.Enums.GroupBookingFinancialHandleOrderStatus.Rejected);
    }
}
