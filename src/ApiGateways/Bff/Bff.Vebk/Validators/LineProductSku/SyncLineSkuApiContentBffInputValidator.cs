using Bff.Vebk.Models.LineProductSku;
using FluentValidation;

namespace Bff.Vebk.Validators.LineProductSku;

public class SyncLineSkuApiContentBffInputValidator : AbstractValidator<UpdateLineSkuApiContentBffInput>
{
    public SyncLineSkuApiContentBffInputValidator()
    {
        RuleFor(x => x.SkuContentItems).NotEmpty();
        RuleForEach(x => x.SkuContentItems)
            .ChildRules(c =>
            {
                c.RuleFor(x => x.LineProductSkuId).NotEmpty();
            });
    }
}