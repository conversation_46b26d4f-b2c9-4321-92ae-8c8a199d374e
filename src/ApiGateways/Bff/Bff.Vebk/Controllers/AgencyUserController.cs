using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.AgencyUser;
using Bff.Vebk.Models.Common;
using Cit.TokenPersistence.Context;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Tenant.DTOs.Agency;
using Contracts.Common.User.DTOs;
using Contracts.Common.User.DTOs.AgencyUser;
using Contracts.Common.User.DTOs.UserBinding;
using Contracts.Common.WeChat.DTOs;
using Contracts.Common.WeChat.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Controllers;

/// <summary>
/// 分销商用户
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class AgencyUserController : ControllerBase
{
    private const int _sysRole = (int)SysRole.Agency;

    private readonly IMapper _mapper;
    private readonly IUserApiCaller _userApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IPermissionCaller _permissionCaller;
    private readonly ITokenPersistenceContext _tokenPersistenceContext;
    private readonly IWechatApiCaller _wechatApiCaller;

    public AgencyUserController(
        IMapper mapper,
        IUserApiCaller userApiCaller,
        ITenantApiCaller tenantApiCaller,
        IPermissionCaller permissionCaller,
        ITokenPersistenceContext tokenPersistenceContext,
        IWechatApiCaller wechatApiCaller)
    {
        _mapper = mapper;
        _userApiCaller = userApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _permissionCaller = permissionCaller;
        _tokenPersistenceContext = tokenPersistenceContext;
        _wechatApiCaller = wechatApiCaller;
    }

    /// <summary>
    /// 用户列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt(200, typeof(PagingModel<AgencyUserSearchOutput>))]
    public async Task<IActionResult> Search(AgencyUserSearchInput input)
    {
        var searchInput = _mapper.Map<Contracts.Common.User.DTOs.AgencyUser.SearchInput>(input);
        var users = await _userApiCaller.AgencyUserSearch(searchInput);
        var result = _mapper.Map<PagingModel<AgencyUserSearchOutput>>(users);
        if (result.Data.Any() == false)
            return Ok(result);

        await ProcessAgencyUserData(result.Data);

        return Ok(result);
    }

    /// <summary>
    /// 对分销商用户数据进行脱敏，微信绑定，分销商名称等处理
    /// </summary>
    /// <param name="users"></param>
    /// <returns></returns>
    private async Task ProcessAgencyUserData(IEnumerable<AgencyUserSearchOutput> users)
    {
        if (users.Any() is false)
            return;

        var agencyIds = users.Select(x => x.AgencyId).Distinct();
        var agencyUserIds = users.Select(x => x.AgencyUserId);

        var agenciesTask = _tenantApiCaller.GetAgencySimpleInfos(new GetSimpleInfoInput
        {
            AgencyIds = agencyIds
        });

        //绑定数据
        var bindDataTask = _userApiCaller.GetUserBindings(new GetUserBindingInput
        {
            PlatformType = Contracts.Common.User.Enums.UserBindPlatformType.B2BWechat,
            UserIds = agencyUserIds.ToArray(),
            SysRole = (int)SysRole.Agency
        });

        var agencies = await agenciesTask;
        var bindData = await bindDataTask;

        foreach (var item in users)
        {
            item.PhoneNumber = DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto()
            {
                Id = item.AgencyUserId,
                IdType = SensitiveDataIdType.AgencyUser,
                DataType = SensitiveDataType.Phone,
                Data = item.PhoneNumber,
            });
            item.Email = DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto()
            {
                Id = item.AgencyUserId,
                IdType = SensitiveDataIdType.AgencyUser,
                DataType = SensitiveDataType.Email,
                Data = item.Email,
            });
            item.WechatBind = bindData?.Any(x => x.UserId == item.AgencyUserId) is true;
            item.AgencyName = agencies.FirstOrDefault(x => x.AgencyId == item.AgencyId)?.FullName;
        }

    }

    /// <summary>
    /// 用户列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt(200, typeof(List<AgencyUserSearchOutput>))]
    public async Task<IActionResult> Export(AgencyUserSearchInput input)
    {
        var searchInput = _mapper.Map<Contracts.Common.User.DTOs.AgencyUser.SearchInput>(input);

        var users = await _userApiCaller.AgencyUserList(searchInput);
        var result = _mapper.Map<List<AgencyUserSearchOutput>>(users);
        if (result.Any() is false)
            return Ok(result);

       await ProcessAgencyUserData(result);

        return Ok(result);
    }

    /// <summary>
    /// 解绑
    /// </summary>
    /// <param name="agencyUserId"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Unbound([SwaggerParameter(Required = true)] long agencyUserId)
    {
        await _userApiCaller.UnboundUserBindings(new  UnboundUserBindingInput
        {
            UserId = agencyUserId,
            SysRole = _sysRole,
            PlatformType = Contracts.Common.User.Enums.UserBindPlatformType.B2BWechat
        });
        return Ok();
    }

    /// <summary>
    /// 新增
    /// </summary>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default,
        ErrorTypes.User.AccountIsExist,
        ErrorTypes.User.PhoneNumberIsDisabled,
        ErrorTypes.User.EmailIsBound)]
    public async Task<IActionResult> Add(AgencyUserAddInput input)
    {
        var addRequest = _mapper.Map<Contracts.Common.User.DTOs.AgencyUser.AddInput>(input);

        var emailCheck = await _userApiCaller.AgencyUsersAnyAsync(new AgencyUserVerifyInput { Email = input.Email });
        if (emailCheck)
        {
            throw new BusinessException(ErrorTypes.User.EmailIsBound);
        }

        var phoneCheck = await _userApiCaller.AgencyUsersAnyAsync(new AgencyUserVerifyInput
        {
            PhoneNumber = input.PhoneNumber,
            CountryDialCode = input.CountryDialCode
        });
        if (phoneCheck)
        {
            throw new BusinessException(ErrorTypes.User.PhoneNumberIsDisabled);
        }

        var userId = await _userApiCaller.AgencyUserAdd(addRequest);
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        await _permissionCaller.SetAgencyUserACL(new Contracts.Common.Permission.DTOs.ACL.SetUserAclDTO
        {
            UserId = userId,
            AclKeys = input.AclKeys,
            CreatorId = currentUser.UserId,
            CreatorName = currentUser.NickName
        });
        return Ok();
    }


    /// <summary>
    /// 分销商账号唯一性校验
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(bool))]
    [SwaggerResponseExt(default,
        ErrorTypes.User.AccountIsExist,
        ErrorTypes.User.PhoneNumberIsDisabled,
        ErrorTypes.User.EmailIsBound)]
    public async Task<IActionResult> Check(AgencyUserVerifyInput input)
    {
        var emailCheck = await _userApiCaller.AgencyUsersAnyAsync(new AgencyUserVerifyInput { Email = input.Email });
        if (emailCheck)
        {
            throw new BusinessException(ErrorTypes.User.EmailIsBound);
        }

        var phoneCheck = await _userApiCaller.AgencyUsersAnyAsync(new AgencyUserVerifyInput { PhoneNumber = input.PhoneNumber });
        if (phoneCheck)
        {
            throw new BusinessException(ErrorTypes.User.PhoneNumberIsDisabled);
        }

        var userNameCheck = await _userApiCaller.AgencyUsersAnyAsync(new AgencyUserVerifyInput { UserName = input.UserName });
        if (userNameCheck)
        {
            throw new BusinessException(ErrorTypes.User.AccountIsExist);
        }

        return Ok(true);
    }

    /// <summary>
    /// 修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.User.EmailIsBound)]
    [SwaggerResponseExt(default, ErrorTypes.User.PhoneNumberIsDisabled)]
    public async Task<IActionResult> Update(AgencyUserUpdateInput input)
    {
        var addRequest = _mapper.Map<Contracts.Common.User.DTOs.AgencyUser.UpdateInput>(input);

        if (!string.IsNullOrEmpty(input.Email))
        {
            var emailCheck = await _userApiCaller.AgencyUsersAnyAsync(new AgencyUserVerifyInput
            {
                Email = input.Email,
                AgencyUserId = input.AgencyUserId
            });
            if (emailCheck)
            {
                throw new BusinessException(ErrorTypes.User.EmailIsBound);
            }
        }

        if (!string.IsNullOrEmpty(input.PhoneNumber))
        {
            var phoneCheck = await _userApiCaller.AgencyUsersAnyAsync(new AgencyUserVerifyInput
            {
                PhoneNumber = input.PhoneNumber,
                AgencyUserId = input.AgencyUserId
            });
            if (phoneCheck)
            {
                throw new BusinessException(ErrorTypes.User.PhoneNumberIsDisabled);
            }
        }


        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var updateTask = _userApiCaller.AgencyUserUpdate(addRequest);
        var setAclTask = _permissionCaller.SetAgencyUserACL(new Contracts.Common.Permission.DTOs.ACL.SetUserAclDTO
        {
            UserId = input.AgencyUserId,
            AclKeys = input.AclKeys,
            CreatorId = currentUser.UserId,
            CreatorName = currentUser.NickName
        });
        await setAclTask;
        await updateTask;

        return Ok();
    }

    /// <summary>
    /// 查询分销商用户详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(Contracts.Common.User.DTOs.AgencyUser.AgencyUserDto), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetDetail(long id)
    {
        var result = (await _userApiCaller.GetAgencyUserDetails(id)).FirstOrDefault();
        if (result is not null)
        {
            result.PhoneNumber = DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto()
            {
                Id = result.AgencyUserId,
                IdType = SensitiveDataIdType.AgencyUser,
                DataType = SensitiveDataType.Phone,
                Data = result.PhoneNumber,
            });
            result.Email = DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto()
            {
                Id = result.AgencyUserId,
                IdType = SensitiveDataIdType.AgencyUser,
                DataType = SensitiveDataType.Email,
                Data = result.Email,
            });
        }
        return Ok(result);
    }

    /// <summary>
    /// 重置分销商账号密码
    /// </summary>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(string))]
    public async Task<IActionResult> RestPassword(RestPasswordInput input)
    {
        var result = await _userApiCaller.RestPassword(input);

        await DeleteUserAllTokenAsync(input.UserId);

        return Ok(result);
    }

    /// <summary>
    /// 设置分销商账号状态
    /// </summary>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    public async Task<IActionResult> OnOrOff(OnOrOffInput input)
    {
        await _userApiCaller.OnOrOff(input);

        await DeleteUserAllTokenAsync(input.AgencyUserId);

        return Ok();
    }

    /// <summary>
    /// 删除当前用户下 所有持久化token
    /// </summary>
    /// <param name="userid"></param>
    /// <returns></returns>
    private async Task DeleteUserAllTokenAsync(long userid)
    {
        var agency = (await _userApiCaller.GetAgencyUserDetails(userid)).FirstOrDefault();
        if (agency is not null)
        {
            var current = HttpContext.User.ParseUserInfo<CurrentUser>();

            //Token 持久化删除 该用户下所有端的token
            var tokenKey = $"TokenPersistence:{current.Tenant}:{agency.AgencyId}:{userid}*";
            _tokenPersistenceContext.Delete(tokenKey);
        }
    }

    /// <summary>
    /// 判断分销商用户是否存在
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpGet]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(bool))]
    public async Task<IActionResult> Exist(string email)
    {
        var info = await _userApiCaller.AgencyUserFindOne(new AgencyUserFindOneInput()
        {
            Email = email,
        });
        var result = info is not null;
        return Ok(result);
    }

    /// <summary>
    /// 生临时成用户B2B公众号关注二维码
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(CreateMpQrCodeOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetSubscribeQrCode([SwaggerParameter(Required = true)] long agencyUserId)
    {
        //CurrentUser currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var ts = TimeSpan.FromHours(1);
        var result = await _wechatApiCaller.CreateWechatMPQrCode(new CreateMpQrCodeInput
        {
            ActionName = "QR_STR_SCENE",
            AuthType = AuthType.B2BWechatMp,
            ExpireSeconds = (int)ts.TotalSeconds,
            ActionInfo = new CreateMpQrCodeActionInfo
            {
                SceneStr = $"Type={(int)WechatSenceType.AgencyUserSubscribe}&AgencyUserId={agencyUserId}",
            },
        });
        return Ok(result);
    }
}
