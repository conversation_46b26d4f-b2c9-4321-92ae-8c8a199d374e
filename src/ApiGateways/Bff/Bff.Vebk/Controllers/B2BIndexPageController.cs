using Bff.Vebk.Callers;
using Cit.Storage.Redis;
using Common.Jwt;
using Common.ServicesHttpClient;
using Common.Swagger;
using Contracts.Common.Hotel.DTOs.ApiHotel;
using Contracts.Common.Hotel.DTOs.HotelTag;
using Contracts.Common.Hotel.DTOs.SpecializedHotelDetail;
using Contracts.Common.Resource.DTOs;
using Contracts.Common.Tenant.DTOs.B2BIndexHotel;
using Contracts.Common.Tenant.DTOs.B2BIndexPage;
using Contracts.Common.Tenant.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System.Net.Http;

namespace Bff.Vebk.Controllers;

/// <summary>
/// B2B首页配置
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class B2BIndexPageController : ControllerBase
{
    private const string _b2bIndexPageKey = "agency:b2bindexpage:{0}:{1}";

    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IHotelApiCaller _hotelApiCaller;
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly IRedisClient _redisClient;

    public B2BIndexPageController(ITenantApiCaller tenantApiCaller,
        IHotelApiCaller hotelApiCaller,
        IResourceApiCaller resourceApiCaller,
        IRedisClient redisClient)
    {
        _tenantApiCaller = tenantApiCaller;
        _hotelApiCaller = hotelApiCaller;
        _resourceApiCaller = resourceApiCaller;
        _redisClient = redisClient;
    }

    /// <summary>
    /// 设置序号排序
    /// </summary>
    /// <param name="inputs"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize] 
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> SetComponentIndex(SetComponentIndexInput input)
    {
        await _tenantApiCaller.SetB2BIndexPageComponentIndex(input);
        await RemoveAllComponentsCache();
        return Ok();
    }

    /// <summary>
    /// 获取组件列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(List<B2BIndexPageComponentDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetComponents(B2BIndexPageType? b2bIndexPageType = null)
    {
        var result = await _tenantApiCaller.GetB2BIndexPageComponents(b2bIndexPageType);
        return Ok(result);
    }

    /// <summary>
    /// 更新保存组件信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Tenant.ContentLengthExceedsTheLimit)]
    public async Task<IActionResult> UpdateComponent(UpdateComponentInput input)
    {
        UpdateComponentOutput result = null;
        CheckBodyLength();
        //列表是汇智酒店
        var hopHotelComponents = GetB2BHOPHotelComponent(input.ComponentItems, input.B2BIndexPageType);
        if (hopHotelComponents.Any())
        {
            var apiHotels = await GetApiHotels(hopHotelComponents);
            //填充字段
            fillComponentItem(hopHotelComponents, apiHotels);
            result = await _tenantApiCaller.UpdateB2BIndexPageComponent(input);
            input.Id = result.Id;
            await SyncComponentItemData(result, input.ComponentItems, apiHotels);
        }
        else
            result = await _tenantApiCaller.UpdateB2BIndexPageComponent(input);
        await RemoveComponentsCache(input.B2BIndexPageType,input.ComponentItems.FirstOrDefault().Language);
        return Ok();
    }

    /// <summary>
    /// 删除首页组件
    /// </summary> 
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> DeleteComponent(DeleteComponentInput input)
    {
        await _tenantApiCaller.DeleteB2BIndexPageComponent(input);
        await RemoveAllComponentsCache();
        return Ok();
    }

    /// <summary>
    /// 添加首页组件
    /// </summary>
    /// <param name="input"></param>
    /// <returns>Id</returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(long), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Tenant.ContentLengthExceedsTheLimit)]
    public async Task<IActionResult> AddComponent(AddComponentInput input)
    {
        AddComponentOutput result = null;
        CheckBodyLength();
        //列表是汇智酒店
        var hopHotelComponents = GetB2BHOPHotelComponent(input.ComponentItems, input.B2BIndexPageType);
        if (hopHotelComponents.Any())
        {
            var apiHotels = await GetApiHotels(hopHotelComponents);
            //填充字段
            fillComponentItem(hopHotelComponents, apiHotels);
            result = await _tenantApiCaller.AddB2BIndexPageComponent(input);
            await SyncComponentItemData(result, hopHotelComponents, apiHotels);
            return Ok(result.Id);
        }
        else
            result = await _tenantApiCaller.AddB2BIndexPageComponent(input);
        await RemoveComponentsCache(input.B2BIndexPageType, input.ComponentItems.FirstOrDefault().Language);
        return Ok(result.Id);
    }

    /// <summary>
    /// 同步汇智酒店组件冗余数据
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task SyncComponentItemData()
    {
        var updates = new List<UpdateComponentInput>();
        var result = await _tenantApiCaller.GetB2BIndexPageComponents(B2BIndexPageType.B2B);
        var datas = result.Where(x => x.ComponentItems
            .Where(x => x.Content.ProductList != null && x.Content.ProductList.LinkChooseType == Contracts.Common.Tenant.Enums.B2BLinkChooseType.HOPHotel).Any())
            .ToList();

        if (!datas.Any())
            return;

        var componentItems = datas.SelectMany(x => x.ComponentItems).ToList();
        var apiHotels = await GetApiHotels(componentItems);
        foreach (var data in datas)
        {
            foreach (var item in data.ComponentItems)
            {
                var productListItems = item.Content.ProductList.ProductListItems;
                foreach (var product in productListItems)
                {
                    foreach (var city in product.Cities)
                    {
                        var apiHotelIds = city.ProductItems.Select(x => x.Value).ToList();
                        var productCityItems = apiHotels
                            .Where(x => apiHotelIds.Contains(x.Id))
                            .GroupBy(x => new { CityCode = x.CityCode, CityName = x.CityName, })
                            .Select(x => new ProductCityItem
                            {
                                CityCode = x.Key.CityCode,
                                CityName = x.Key.CityName,
                            });
                        city.ProductCityItems = productCityItems.ToList();
                        foreach (var hotCity in city.ProductItems.Where(x=> x.PopularType.HasValue))
                        {
                            if (city.ProductCityItems.Exists(x => x.CityCode == hotCity.CityCode))
                                continue;
                            city.ProductCityItems.Add(new ProductCityItem { 
                                CityCode= hotCity.CityCode ?? 0,
                            });
                        }
                    }
                }
                updates.Add(new UpdateComponentInput
                {
                    Id = data.Id,
                    ComponentItems = new List<B2BIndexPageComponentItemDto> { item }
                });
            }
        }

        foreach (var update in updates)
        {
            await UpdateComponent(update);
        }
    }

    #region 私有函数
    /// <summary>
    /// 检查Body长度
    /// </summary>
    /// <exception cref="BusinessException"></exception>
    private void CheckBodyLength()
    {
        if (Request.Body.Length > 16777215)
            throw new BusinessException(ErrorTypes.Tenant.ContentLengthExceedsTheLimit);
    }

    /// <summary>
    /// 冗余组件的汇智酒店数据
    /// </summary>
    /// <param name="componentItems"></param>
    private async Task SyncComponentItemData(AddOrUpdateComponentOutput output, List<B2BIndexPageComponentItemDto> componentItems, List<GetApiHotelDetailOutput> hotels)
    {
        componentItems.ForEach(x =>
        {
            x.Id = output.Details.FirstOrDefault(o => x.Language == o.Language)?.B2BIndexPageComponentItemId;
        });

        var hopHotelComponents = GetB2BHOPHotelComponent(componentItems, B2BIndexPageType.B2B);
        //是否包含汇智酒店
        if (!hopHotelComponents.Any())
            return;

        var hotelIds = hotels.Select(x => x.Id).Distinct().ToList();
        var resourceHotelIds = hotels.Select(x => x.ResourceHotelId).Distinct().ToList();
        var hotelFirstPhotos = await _resourceApiCaller.GetHotelFirstPhoto(resourceHotelIds.ToArray());

        #region 查询酒店tag
        //获取酒店对应的tag
        var hotelTags = new List<GetHotelTagsOutput>();
        if (hotelIds.Any())
        {
            var hotelTagInput = new GetHotelTagsInput
            {
                ShowPageType = Contracts.Common.Hotel.Enums.TagShowPageType.B2B_HomePage_Search,
                ApiHotelIds = hotelIds
            };
            hotelTags = (await _hotelApiCaller.GetByApiHotelIdsV2(hotelTagInput)).ToList();
        }
        #endregion

        var addB2BIndexPageHotels = new List<AddB2BIndexPageHotelInput>();
        var addB2BIndexPageTags = new List<AddB2BIndexPageTagInput>();
        //重新组装
        foreach (var hopHotelComponent in hopHotelComponents)
        {
            var b2bIndexPageComponentItemId = hopHotelComponent.Id.Value;
            var sort = 1;

            var productListItems = GetProductlistItems(hopHotelComponent);
            var specializedHotelIds = productListItems
                .SelectMany(x => x.Cities)
                .Where(x => x.ProductItems != null)
                .SelectMany(x => x.ProductItems)
                .Where(x => x.PopularType.HasValue && x.PopularType == B2BIndexPageHotHotelType.Custom)//查询添加的酒店
                .Select(x => x.PopularId.Value)
                .Distinct()
                .ToList();
            var specializedHotelItems = await _hotelApiCaller.GetSpecializedHotelItems(new SpecializedHotelItemInput
            {
                SpecializedHotelIds = specializedHotelIds.ToArray(),
            });

            productListItems.ForEach(async o =>
            {
                foreach (var city in o.Cities)
                {
                    if (city.ProductItems == null)
                        continue;
                    foreach (var product in city.ProductItems)
                    {
                        if (!product.PopularType.HasValue)
                        {
                            var hotel = hotels.FirstOrDefault(x => x.Id == product.Value);
                            if (hotel is null)
                                continue;
                            addB2BIndexPageHotels.Add(new AddB2BIndexPageHotelInput()
                            {
                                B2BIndexPageComponentItemId = b2bIndexPageComponentItemId,
                                HotelId = hotel.Id,
                                ZHName = product.Name,
                                ENName = product.Name,
                                Path = product.Path,
                                CoordinateType = hotel.CoordinateType,
                                Latitude = hotel.Latitude,
                                Longitude = hotel.Longitude,
                                StarLevel = hotel.StarLevel,
                                CityCode = hotel.CityCode,
                                CountryCode = hotel.CountryCode,
                                CreateTime = DateTime.Now,
                                Region = o.CountryName,
                                SubRegion = city.CityName,
                                WeightValue = hotel.WeightValue,
                                OnTop = hotel.OnTop,
                                Discount = product.Discount,
                                B2BPageHotelType = B2BPageHotelType.B2BIndexPage,
                                Sort = sort,
                                ResourceHotelId = hotel.ResourceHotelId,
                            });

                            if (hotelTags.Any())
                            {
                                var addTags = hotelTags.FirstOrDefault(x => x.ApiHotelId == product.Value)?.Tags.Select(x => new AddB2BIndexPageTagInput
                                {
                                    B2BIndexPageComponentItemId = b2bIndexPageComponentItemId,
                                    HotelId = product.Value,
                                    TagId = x.Id,
                                    TagName = x.Name,
                                });
                                if (addTags != null && addTags.Any())
                                    addB2BIndexPageTags.AddRange(addTags);
                            }
                            sort++;
                        }
                        else if(product.PopularType == B2BIndexPageHotHotelType.Custom && product.PopularId.HasValue)
                        {
                            var specializedHotels = specializedHotelItems.Where(x => x.SpecializedHotelId == product.PopularId.Value).ToList();
                            var specializedHotelIds = specializedHotels.Select(x => x.HotelId).ToList();
                            foreach (var specializedHotelItem in specializedHotels)
                            {
                                if (addB2BIndexPageHotels.Any(x => x.HotelId == specializedHotelItem.HotelId && 
                                    x.Region == o.CountryName && 
                                    x.SubRegion == city.CityName))
                                    continue;
                                var hotel = hotels
                                    .FirstOrDefault(x => x.CityCode == product.CityCode && x.Id == specializedHotelItem.HotelId);
                                if (hotel is null)
                                    continue;
                                var path = hotelFirstPhotos?.FirstOrDefault(x => x.HotelId == hotel.ResourceHotelId)?.Path;
                                addB2BIndexPageHotels.Add(new AddB2BIndexPageHotelInput()
                                {
                                    B2BIndexPageComponentItemId = b2bIndexPageComponentItemId,
                                    HotelId = hotel.Id,
                                    ZHName = hotel.ZHName,
                                    ENName = hotel.ENName,
                                    Path = path,
                                    CoordinateType = hotel.CoordinateType,
                                    Latitude = hotel.Latitude,
                                    Longitude = hotel.Longitude,
                                    StarLevel = hotel.StarLevel,
                                    CityCode = hotel.CityCode,
                                    CountryCode = hotel.CountryCode,
                                    CreateTime = DateTime.Now,
                                    Region = o.CountryName,
                                    SubRegion = city.CityName,
                                    WeightValue = hotel.WeightValue,
                                    OnTop = hotel.OnTop,
                                    Discount = product.Discount,
                                    B2BPageHotelType = B2BPageHotelType.B2BIndexPage,
                                    Sort = sort,
                                    ResourceHotelId = hotel.ResourceHotelId,
                                    B2BHotHotelType = B2BIndexPageHotHotelType.Custom,
                                });

                                if (hotelTags.Any())
                                {
                                    var addTags = hotelTags.FirstOrDefault(x => x.ApiHotelId == specializedHotelItem.HotelId)?.Tags.Select(x => new AddB2BIndexPageTagInput
                                    {
                                        B2BIndexPageComponentItemId = b2bIndexPageComponentItemId,
                                        HotelId = product.Value,
                                        TagId = x.Id,
                                        TagName = x.Name,
                                    });
                                    if (addTags != null && addTags.Any())
                                        addB2BIndexPageTags.AddRange(addTags);
                                }
                                sort++;
                            }
                        }
                    }
                }
            });

            var input = new SyncComponentItemDataInput
            {
                B2BIndexPageComponentItemId = b2bIndexPageComponentItemId,
                Hotels = addB2BIndexPageHotels,
                HotelTags = addB2BIndexPageTags,
                B2BPageHotelType = B2BPageHotelType.B2BIndexPage,
                IsDirect = hopHotelComponent.Content.ProductList == null ? false 
                    : hopHotelComponent.Content.ProductList.IsDirect,
                IsHopHotHotel = hopHotelComponent.Content.ProductList == null ? false
                    : hopHotelComponent.Content.ProductList.IsHopHotHotel,
                TenantId = HttpContext.GetTenantId(),
            };
            await _tenantApiCaller.SyncB2BIndexPageComponentItemData(new List<SyncComponentItemDataInput> { input });
        }
    }

    private List<B2BIndexPageComponentItemDto> GetB2BHOPHotelComponent(List<B2BIndexPageComponentItemDto> componentItems, B2BIndexPageType b2BIndexPageType)
    {
        return componentItems.Where(x => b2BIndexPageType == B2BIndexPageType.B2B && 
            (x.Content.ProductList != null && x.Content.ProductList.LinkChooseType == Contracts.Common.Tenant.Enums.B2BLinkChooseType.HOPHotel) ||
            (x.Content.LimitedTimeSpecialOffer != null && x.Content.LimitedTimeSpecialOffer.LinkChooseType == Contracts.Common.Tenant.Enums.B2BLinkChooseType.HOPHotel))
             .ToList();
    }

    private async Task<List<GetApiHotelDetailOutput>> GetApiHotels(List<B2BIndexPageComponentItemDto> componentItems)
    {
        var result = new List<GetApiHotelDetailOutput>();
        var hopHotelComponents = GetB2BHOPHotelComponent(componentItems, B2BIndexPageType.B2B);
        //是否包含汇智酒店
        if (!hopHotelComponents.Any())
            return result;

        //查询出所有汇智酒店的组件
        List<ProductItem> productItems = new List<ProductItem>();
        var specializedHotelIds = new List<long>();
        foreach (var hopHotelComponent in hopHotelComponents)
        {
            var productListItems = GetProductlistItems(hopHotelComponent);
            var items = productListItems
                .SelectMany(x => x.Cities)
                .Where(x=> x.ProductItems != null)
                .SelectMany(x => x.ProductItems)
                .Where(x=> !x.PopularType.HasValue)//查询添加的酒店
                .ToList();
            productItems.AddRange(items);

            var specializedHotelItemIds = productListItems
                .SelectMany(x => x.Cities)
                .Where(x => x.ProductItems != null)
                .SelectMany(x => x.ProductItems)
                .Where(x => x.PopularType.HasValue && x.PopularType == B2BIndexPageHotHotelType.Custom && x.PopularId.HasValue)//查询添加的酒店
                .Select(x=> x.PopularId.Value)
                .Distinct()
                .ToList();
            if(specializedHotelItemIds.Any())
                specializedHotelIds.AddRange(specializedHotelItemIds);
        }

        //去重酒店id
        var hotelIds = productItems.Select(x => x.Value).Distinct().ToList();
        //查询出相关数据
        if (specializedHotelIds.Any())
        {
            var specializedHoteItems = await _hotelApiCaller.GetSpecializedHotelItems(new SpecializedHotelItemInput { 
                SpecializedHotelIds = specializedHotelIds.ToArray(),
            });
            if (specializedHoteItems.Any())
                hotelIds.AddRange(specializedHoteItems.Select(x => x.HotelId));
        }
        if (hotelIds.Any() is false)
            return result;
        #region 查询汇智酒店信息
            //查询酒店信息
        result = await _hotelApiCaller.GetApiHotelDetail(hotelIds.ToArray());
        #endregion

        return result;
    }

    private void fillComponentItem(List<B2BIndexPageComponentItemDto> componentItems, List<GetApiHotelDetailOutput> apiHotels)
    {
        var hopHotelComponents = GetB2BHOPHotelComponent(componentItems,B2BIndexPageType.B2B);
        foreach (var hopHotelComponent in hopHotelComponents)
        {
            var productListItems = GetProductlistItems(hopHotelComponent);
            foreach (var product in productListItems)
            {
                foreach (var city in product.Cities)
                {
                    if (city.ProductItems == null)
                        continue;

                    var apiHotelIds = city.ProductItems
                        .Where(x=> !x.PopularType.HasValue)
                        .Select(x => x.Value).ToList();
                    var productCityItems = apiHotels
                        .Where(x => apiHotelIds.Contains(x.Id))
                        .GroupBy(x => new { CityCode = x.CityCode, CityName = x.CityName, })
                        .Select(x => new ProductCityItem
                        {
                            CityCode = x.Key.CityCode,
                            CityName = x.Key.CityName,
                        });
                    city.ProductCityItems = productCityItems.ToList();
                    
                    if (city.ProductItems.Any(x=> x.PopularType.HasValue) is true)
                    {
                        foreach (var hotHotelCity in city.ProductItems.Where(x=> x.PopularType.HasValue).GroupBy(x=> x.CityCode))
                        {
                            if (city.ProductCityItems.Exists(x => x.CityCode == hotHotelCity.Key))
                                continue;
                            city.ProductCityItems.Add(new ProductCityItem
                            {
                                CityCode = hotHotelCity.Key.Value,
                            });
                        }
                    }
                }

            }
        }
    }

    private List<ProductListItem> GetProductlistItems(B2BIndexPageComponentItemDto hopHotelComponent)
    {
        if (hopHotelComponent.Content.ProductList != null)
            return hopHotelComponent.Content.ProductList.ProductListItems;
        if (hopHotelComponent.Content.LimitedTimeSpecialOffer != null)
            return hopHotelComponent.Content.LimitedTimeSpecialOffer.ProductListItems;

        return null;
    }

    private async Task<bool> RemoveComponentsCache(B2BIndexPageType b2bIndexPageType,string language)
    {
        var cacheKey = string.Format(_b2bIndexPageKey, b2bIndexPageType,language);
        return await _redisClient.KeyDeleteAsync(cacheKey);
    }

    private async Task RemoveAllComponentsCache()
    {
        var delKeys = new List<string> { 
            string.Format(_b2bIndexPageKey,B2BIndexPageType.B2B,"zh"),
            string.Format(_b2bIndexPageKey,B2BIndexPageType.B2B,"en"),
            string.Format(_b2bIndexPageKey,B2BIndexPageType.HotelGroupBooking,"zh"),
            string.Format(_b2bIndexPageKey,B2BIndexPageType.HotelGroupBooking,"en"),
        };
        await _redisClient.KeyDeleteAsync(delKeys);
    } 
    #endregion
}

