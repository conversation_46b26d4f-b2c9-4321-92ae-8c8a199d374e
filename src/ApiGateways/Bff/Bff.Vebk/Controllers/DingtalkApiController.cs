using Bff.Vebk.Callers;
using Bff.Vebk.Models.DingtalkApi;
using Common.Jwt;
using Common.Swagger;
using Common.Swagger.Header;
using Common.Utils;
using Contracts.Common.Order.DTOs.OffsetOrderDingtalkApply;
using Contracts.Common.Tenant.DTOs.DingtalkApi;
using Contracts.Common.User.DTOs.TenantUser;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Bff.Vebk.Controllers;

[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class DingtalkApiController : ControllerBase
{
    private readonly IUserApiCaller _userApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly ILogger _logger;
    public DingtalkApiController(ITenantApiCaller tenantApiCaller,
        IUserApiCaller userApiCaller,
        ILoggerFactory loggerFactory,
        IOrderApiCaller orderApiCaller)
    {
        _tenantApiCaller = tenantApiCaller;
        _userApiCaller = userApiCaller;
        _logger = loggerFactory.CreateLogger<DingtalkApiController>();
        _orderApiCaller = orderApiCaller;
    }

    /// <summary>
    /// 表单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(WorkflowFormsOuput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> WorkflowForms(BffWorkflowFormsInput input)
    {

        var result = await _tenantApiCaller.DingtalkApiWorkflowForms(new WorkflowFormsInput()
        {
            AppId = input.AppId,
            ClientID = input.ClientID,
            ClientSecret = input.ClientSecret
        });
        return Ok(result);
    }

    /// <summary>
    /// 获取钉钉用户可见表单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(GetWorkflowFormInfoOuput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetWorkflowFormInfo(GetWorkflowFormInfoInput input)
    {
        input.AddNew = true;
        var result = await _tenantApiCaller.GetDingtalkApiWorkflowFormInfo(input);
        foreach (var item in result.Items)
        {
            switch (item.ComponentName)
            {
                case "DDSelectField":
                case "DDMultiSelectField":
                case "InnerContactField":
                case "DepartmentField":
                case "RelateField":
                case "FormRelateField":
                case "AddressField":
                case "StarRatingField":
                case "DDDateRangeField":
                    item.CanMap = false;
                    break;
                default:
                    item.CanMap = true;
                    break;
            }
        }
        return Ok(result);
    }

    /// <summary>
    /// 回调通知
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [IgnoreTenantHeader]
    public async Task<IActionResult> Callback(
        [FromQuery] long tenantId,
        [FromQuery] string msg_signature,
        [FromQuery] string timestamp,
        [FromQuery] string nonce,
        [FromQuery] string signature,
        [FromBody] DingtalkCallbackBody body)
    {
        _logger.LogInformation("dingtalk callback body:{@body},tenantId:{@tenantId},msg_signature:{@msg_signature},timestamp:{@timestamp},nonce:{@nonce},signature:{@signature}",
            body, tenantId, msg_signature, timestamp, nonce, signature);
        var config = await _tenantApiCaller.SetTenantId(tenantId).GetDingtalkApiConfig();
        DingTalkEncryptor callbackCrypto = new DingTalkEncryptor(config.Token, config.Aeskey, config.ClientID);
        string encryptMsg = body.Encrypt;
        string decryptMsg = callbackCrypto.GetDecryptMsg(msg_signature, timestamp, nonce, encryptMsg);
        // 3. 反序列化回调事件json数据
        DingtalkCallbackInput callbackInput = JsonConvert.DeserializeObject<DingtalkCallbackInput>(decryptMsg)!;
        _logger.LogInformation("dingtalk callback callbackInput:decryptMsg:{@decryptMsg},{@callbackInput},tenantId:{@tenantId},msg_signature:{@msg_signature}", decryptMsg, callbackInput, tenantId, msg_signature);

        // 4. 根据EventType分类处理
        switch (callbackInput.EventType)
        {
            //审批实例开始、结束、终止、删除
            case "bpms_instance_change":
                await _orderApiCaller.SetTenantId(tenantId).CallbackOffsetOrderDingtalkApplyResult(new CallbackApplyResultInput
                {
                    DingtalkInstanceId = callbackInput.ProcessInstanceId,
                });
                break;
            // 审批模板变更 workflow_form_change
            case "workflow_form_change":
                // "/workflow_form_change/processCode/PROC-2729B443-24A8-46B3-9FD0-C1C258FD521A/type/update"
                var arr = callbackInput.Resource.Split("/");
                var processCode = arr[3];
                await _tenantApiCaller.SetTenantId(tenantId).DingtalkApiWorkflowFormInfoChange(new WorkflowFormInfoChangeInput()
                {
                    TenantId = tenantId,
                    ProcessCode = processCode,
                });
                break;
            case "user_leave_org": // 离职 user_leave_org
                await _userApiCaller.SetTenantId(tenantId).SetUserEnabledByDingtalkUserLeave(new SetEnabledByDingtalkUserLeaveInput()
                {
                    TenantId = tenantId,
                    DingtalkUserIds = callbackInput.UserId,
                    AppId = config.AppId,
                    AppKey = config.ClientID
                });
                break;
            default:
                break;
        }
        // 5. 返回success的加密数据
        var successMap = callbackCrypto.GetEncryptedMap("success");
        return Ok(successMap);
    }
}
