using Bff.Vebk.Callers;
using Bff.Vebk.Models.GroupBookingAggregate;
using Common.Swagger;
using Contracts.Common.Order.DTOs.GroupBookingAggregate;
using Contracts.Common.Order.Enums;
using Contracts.Common.User.DTOs.TenantUser;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
[Authorize]
public class GroupBookingAggregateController : ControllerBase
{
    private readonly IOrderApiCaller _orderApiCaller;

    private readonly IUserApiCaller _userApiCaller;

    public GroupBookingAggregateController(IOrderApiCaller orderApiCaller, IUserApiCaller userApiCaller)
    {
        _orderApiCaller = orderApiCaller;
        _userApiCaller = userApiCaller;
    }

    /// <summary>
    /// 获取基础数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(GetBasicDataOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.AreaChange)]
    public async Task<IActionResult> GetBasicData(GetBasicDataInput input)
    {
        var result = await _orderApiCaller.GetGroupBookingAggregateBasicData(input);
        return Ok(result);
    }

    /// <summary>
    /// 获取数据展示
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(GetChatDataOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.AreaChange)]
    public async Task<IActionResult> GetChatData(GetChatDataInput input)
    {
        var result = await _orderApiCaller.GetGroupBookingAggregateChatData(input);
        return Ok(result);
    }

    /// <summary>
    /// 获取数据占比
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(GetProportionOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetProportion(GetProportionInput input)
    {
        var result = await _orderApiCaller.GetGroupBookingAggregateProportion(input);
        return Ok(result);
    }

    /// <summary>
    /// 其他数据下载
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(QueryOperationDataOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> QueryOperationData(QueryOperationDataInput input)
    {
        var result = await _orderApiCaller.QueryGroupBookingOperationData(input);
        return Ok(result);
    }

    /// <summary>
    /// 漏斗分析
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<GetStatusStatisticsOutput>), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.AreaChange)]
    public async Task<IActionResult> GetStatusStatistics(GetStatusStatisticsInput input)
    {
        var result = await _orderApiCaller.GetGroupBookingAggregateStatusStatistics(input);
        if (result.Any())
        {
            var opUserIds = result.SelectMany(x => x.CancelDetails)
                   .Where(x => x.OperatorUserId > 0)
                   .Select(x => x.OperatorUserId!.Value)
                   .Distinct()
                   .ToList();
            var opUsers = await _userApiCaller.SearchTenantUsers(new SearchTenantUsersInput()
            {
                Ids = opUserIds.ToArray(),
            });
            // 要求把处理时间提取给上一个状态显示
            for (int i = 0; i < result.Count; i++)
            {
                var current = result[i];
                foreach (var item in current.CancelDetails)
                {
                    var opUser = opUsers.FirstOrDefault(x => x.Id == item.OperatorUserId);
                    item.OperatorUserName = opUser?.Name;
                }

                if (i > 0)
                {
                    // 上一个状态
                    var previous = result[i - 1];
                    previous.AvgTime = current.AvgTime;
                    current.AvgTime = 0;// 清除当前状态的处理时间，避免重复显示

                    previous.AvgWorkTime = current.AvgWorkTime;
                    current.AvgWorkTime = 0;
                }
                var nextCount = 0;
                if (i < result.Count - 1) // 首付之后没有待处理数
                {
                    var next = result[i + 1];
                    nextCount = next.Count;
                    current.WaitCount = current.Count - nextCount - current.CancelCount; // 计算待处理订单数
                }
            }
        }

        return Ok(result);
    }

    /// <summary>
    /// 导出漏斗分析
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<BffExportStatusStatisticsUserOutput>), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.AreaChange)]
    public async Task<IActionResult> ExportStatusStatistics(GetStatusStatisticsInput input)
    {
        var data = await _orderApiCaller.ExportGroupBookingAggregateStatusStatistics(input);

        // 加总的统计
        var userGroupTotals = data.GroupBy(x => x.OperatorUserId);
        foreach (var userItem in userGroupTotals)
        {
            var userTotalList = GetAreaTotal(userItem.ToList(), userItem.Key);
            data.InsertRange(0, userTotalList);
        }
        var res = new List<BffExportStatusStatisticsUserOutput>();

        var userIds = data.Where(x => x.OperatorUserId > 0)
               .Select(x => x.OperatorUserId!.Value)
               .Distinct().ToList();
        var opUsers = await _userApiCaller.SearchTenantUsers(new SearchTenantUsersInput()
        {
            Ids = userIds.ToArray()
        });
        var exceptIds = input.OperatorUserIds
          .Where(id => id.HasValue && !userIds.Contains(id.Value))
          .ToList();
        if (input.OperatorUserIds.Any())
            userIds.AddRange(input.OperatorUserIds.Select(x => x!.Value));
        userIds = userIds.Distinct().ToList();
        if (data.Any())
        {
            var userGourpData = data.GroupBy(x => x.OperatorUserId).OrderBy(x => x.Key);
            foreach (var userItem in userGourpData)
            {
                var areaGroupData = userItem.GroupBy(x => x.AreaId);
                foreach (var areaGroupItem in areaGroupData)
                {
                    var childItems = areaGroupItem.OrderBy(x => x.Status).ToList();
                    // 要求把处理时间提取给上一个状态显示
                    for (int i = 0; i < childItems.Count; i++)
                    {
                        if (i == 0)
                            continue; // 第一个状态没有上一个状态
                        var current = childItems[i];
                        // 上一个状态
                        var previous = childItems[i - 1];
                        previous.AvgTime = current.AvgTime;
                        previous.AvgWorkTime = current.AvgWorkTime;

                        current.AvgTime = 0;
                        current.AvgWorkTime = 0;

                        if (current.Status == StatisticsFunnelStatus.InitialPayment)
                        {
                            current.AvgTime = null;
                            current.AvgCancelTime = null;
                        }
                    }
                }
            }
            foreach (var userItem in userGourpData)
            {
                var userOuput = new BffExportStatusStatisticsUserOutput()
                {
                    OperatorUserId = userItem.Key,
                };
                userOuput.OperatorUserName = userItem.Key == null ? "全部运营" : opUsers.FirstOrDefault(x => x.Id == userItem.Key)?.Name;
                var list = new List<BffExportStatusStatisticsOutput>();
                var areaGroupData = userItem.GroupBy(x => new { x.AreaId, x.AreaName });
                foreach (var areaGroupItem in areaGroupData)
                {
                    var info = new BffExportStatusStatisticsOutput() { AreaName = areaGroupItem.Key.AreaName };
                    var items = areaGroupItem.OrderBy(x => x.Status).ToList();
                    for (var i = 0; i < items.Count; i++)
                    {
                        var item = items[i];
                        var waitCount = 0;
                        if (i < items.Count - 1)
                        {
                            var next = items[i + 1];
                            waitCount = item.Count - item.CancelCount - next.Count; // 待处理订单数
                        }
                        switch (item.Status)
                        {
                            case StatisticsFunnelStatus.NewApplication:
                                info.NewApplicationConversionRate = item.ConversionRate;
                                info.NewApplicationCount = item.Count;
                                info.NewApplicationAvgTime = item.AvgTime;
                                info.NewApplicationAvgCancelTime = item.AvgCancelTime;
                                info.NewApplicationCancelCount = item.CancelCount;
                                info.NewApplicationWaitCount = waitCount;
                                break;

                            case StatisticsFunnelStatus.WaitForInquiry:
                                info.WaitForInquiryConversionRate = item.ConversionRate;
                                info.WaitForInquiryCount = item.Count;
                                info.WaitForInquiryAvgTime = item.AvgTime;
                                info.WaitForInquiryAvgCancelTime = item.AvgCancelTime;
                                info.WaitForInquiryCancelCount = item.CancelCount;
                                info.WaitForInquiryWaitCount = waitCount;
                                break;

                            case StatisticsFunnelStatus.Inquiried:
                                info.InquiriedConversionRate = item.ConversionRate;
                                info.InquiriedCount = item.Count;
                                info.InquiriedAvgTime = item.AvgTime;
                                info.InquiriedAvgCancelTime = item.AvgCancelTime;
                                info.InquiriedCancelCount = item.CancelCount;
                                info.InquiriedWaitCount = waitCount;
                                break;

                            case StatisticsFunnelStatus.HotelRecovery:
                                info.HotelRecoveryConversionRate = item.ConversionRate;
                                info.HotelRecoveryCount = item.Count;
                                info.HotelRecoveryAvgTime = item.AvgTime;
                                info.HotelRecoveryAvgCancelTime = item.AvgCancelTime;
                                info.HotelRecoveryCancelCount = item.CancelCount;
                                info.HotelRecoveryWaitCount = waitCount;
                                break;

                            case StatisticsFunnelStatus.Quoted:
                                info.QuotedConversionRate = item.ConversionRate;
                                info.QuotedCount = item.Count;
                                info.QuotedAvgTime = item.AvgTime;
                                info.QuotedAvgCancelTime = item.AvgCancelTime;
                                info.QuotedCancelCount = item.CancelCount;
                                info.QuotedWaitCount = waitCount;
                                break;

                            case StatisticsFunnelStatus.QuotationConfirmed:
                                info.QuotationConfirmedConversionRate = item.ConversionRate;
                                info.QuotationConfirmedCount = item.Count;
                                info.QuotationConfirmedAvgTime = item.AvgTime;
                                info.QuotationConfirmedAvgCancelTime = item.AvgCancelTime;
                                info.QuotationConfirmedCancelCount = item.CancelCount;
                                info.QuotationConfirmedWaitCount = waitCount;
                                break;

                            case StatisticsFunnelStatus.WaitForAuditPreOrder:
                                info.WaitForAuditPreOrderConversionRate = item.ConversionRate;
                                info.WaitForAuditPreOrderCount = item.Count;
                                info.WaitForAuditPreOrderAvgTime = item.AvgTime;
                                info.WaitForAuditPreOrderAvgCancelTime = item.AvgCancelTime;
                                info.WaitForAuditPreOrderCancelCount = item.CancelCount;
                                info.WaitForAuditPreOrderWaitCount = waitCount;
                                break;

                            case StatisticsFunnelStatus.PreOrdered:
                                info.PreOrderedConversionRate = item.ConversionRate;
                                info.PreOrderedCount = item.Count;
                                info.PreOrderedAvgTime = item.AvgTime;
                                info.PreOrderedAvgCancelTime = item.AvgCancelTime;
                                info.PreOrderedCancelCount = item.CancelCount;
                                info.PreOrderedWaitCount = waitCount;
                                break;

                            case StatisticsFunnelStatus.InitialPayment:
                                info.InitialPaymentConversionRate = item.ConversionRate;
                                info.InitialPaymentCount = item.Count;
                                info.InitialPaymentAvgTime = item.AvgTime;
                                info.InitialPaymentAvgCancelTime = item.AvgCancelTime;
                                info.InitialPaymentCancelCount = item.CancelCount;
                                info.InitialPaymentWaitCount = waitCount;
                                break;
                        }
                    }
                    list.Add(info);
                }
                userOuput.List = list;
                res.Add(userOuput);
            }
        }
        if (res.Any() is false)
        {
            var userOutput = new BffExportStatusStatisticsUserOutput()
            {
                List = new()
            };
            userOutput.OperatorUserName = "全部运营";
            res.Add(userOutput);
        }
        // 如果有未查询到的运营人员，则添加到结果中,给生成excel使用
        if (input.OperatorUserIds?.Any() is true && exceptIds.Any())
        {
            foreach (var userId in exceptIds)
            {
                var userOutput = new BffExportStatusStatisticsUserOutput()
                {
                    OperatorUserId = userId,
                    List = new()
                };
                userOutput.OperatorUserName = opUsers.FirstOrDefault(x => x.Id == userId)?.Name;
                res.Add(userOutput);
            }
        }
        return Ok(res);
    }

    /// <summary>
    /// 获取数据同步时间
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(GetSyncTimeOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetSyncTime()
    {
        var result = await _orderApiCaller.GetGroupBookingAggregateSyncTime();
        return Ok(result);
    }

    /// <summary>
    /// 取消原因明细下载
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(BffExportCancelOperationOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> ExportCancelOperationData(BffQueryOperationDataInput input)
    {
        var searchInput = new QueryOperationDataInput()
        {
            OperationType = GrouBookingOperationType.Cancellation,
            StartDate = input.StartDate,
            EndDate = input.EndDate,
            AreaIds = input.AreaIds,
            CountryCodes = input.CountryCodes,
            DimensionType = input.DimensionType,
            OperatorUserIds = input.OperatorUserIds,
        };
        var apiData = await _orderApiCaller.QueryGroupBookingOperationData(searchInput);
        var data = apiData.Data;
        var result = new List<BffExportCancelOperationDataUserOutput>();

        var userIds = data.Where(x => x.OperatorUserId > 0)
               .Select(x => x.OperatorUserId!.Value)
               .Distinct().ToList();
        var exceptIds = input.OperatorUserIds
            .Where(id => id.HasValue && !userIds.Contains(id.Value))
            .ToList();
        if (input.OperatorUserIds?.Any() is true)
            userIds.AddRange(input.OperatorUserIds.Select(x => x!.Value));
        userIds = userIds.Distinct().ToList();
        var opUsers = await _userApiCaller.SearchTenantUsers(new SearchTenantUsersInput()
        {
            Ids = userIds.ToArray()
        });
        var groupData = data.GroupBy(x => x.OperatorUserId)
                     .OrderBy(x => x.Key);
        foreach (var group in groupData)
        {
            var userOutput = new BffExportCancelOperationDataUserOutput()
            {
                OperatorUserId = group.Key,
                List = new List<BffExportCancelOperationDataOutput>()
            };
            result.Add(userOutput);
            userOutput.OperatorUserName = group.Key == null ? "全部运营" : opUsers.FirstOrDefault(x => x.Id == group.Key)?.Name;
            var areaGroupData = group.GroupBy(x => new { x.AreaId, x.AreaName })
                .OrderBy(x => x.Key.AreaId);
            foreach (var area in areaGroupData)
            {
                var info = new BffExportCancelOperationDataOutput() { AreaName = area.Key.AreaName };
                foreach (var item in area)
                {
                    switch (item.Status)
                    {
                        case GroupBookingApplicationFormStatus.NewApplication:
                            AssignNewApplicationReasons(info, item);
                            break;

                        case GroupBookingApplicationFormStatus.WaitForInquiry:
                            AssignWaitForInquiryReasons(info, item);
                            break;

                        case GroupBookingApplicationFormStatus.Inquiried:
                            AssignInquiriedReasons(info, item);
                            break;

                        case GroupBookingApplicationFormStatus.Quoted:
                            AssignQuotedReasons(info, item);
                            break;

                        case GroupBookingApplicationFormStatus.QuotationConfirmed:
                            AssignQuotationConfirmedReasons(info, item);
                            break;

                        case GroupBookingApplicationFormStatus.WaitForAuditPreOrder:
                            AssignWaitForAuditPreOrderReasons(info, item);
                            break;

                        case GroupBookingApplicationFormStatus.PreOrdered:
                            AssignPreOrderedReasons(info, item);
                            break;
                    }
                }
                userOutput.List.Add(info);
            }
        }
        if (result.Any() is false)
        {
            var userOutput = new BffExportCancelOperationDataUserOutput()
            {
                List = new()
            };
            userOutput.OperatorUserName = "全部运营";
            result.Add(userOutput);
        }
        // 如果有未查询到的运营人员，则添加到结果中,给生成excel使用
        if (input.OperatorUserIds?.Any() is true && exceptIds.Any())
        {
            foreach (var userId in exceptIds)
            {
                var userOutput = new BffExportCancelOperationDataUserOutput()
                {
                    OperatorUserId = userId,
                    List = new List<BffExportCancelOperationDataOutput>()
                };
                userOutput.OperatorUserName = opUsers.FirstOrDefault(x => x.Id == userId)?.Name;
                result.Add(userOutput);
            }
        }
        foreach (var item in apiData.Details)
        {
            item.OperatorUserName = opUsers.FirstOrDefault(x => x.Id == item.OperatorUserId)?.Name;
        }
        var output = new BffExportCancelOperationOutput()
        {
            Data = result,
            Details = apiData.Details
        };
        return Ok(output);
    }

    #region ExportCancelOperationData private

    private List<ExportStatusStatisticsOutput> GetAreaTotal(List<ExportStatusStatisticsOutput> list, long? opUserId)
    {
        var totalList = new List<ExportStatusStatisticsOutput>();
        var totalData = list.GroupBy(x => x.Status).Select(x => new
        {
            Status = x.Key,
            Count = x.Sum(s => s.Count),
            TotalTime = x.Sum(s => s.Count * s.AvgTime),
            TotalCancelTime = x.Sum(s => s.CancelCount * s.AvgCancelTime),
            CancelCount = x.Sum(s => s.CancelCount),
        }).OrderBy(x => x.Status).ToList();
        for (int i = 0; i < totalData.Count; i++)
        {
            var info = totalData[i];
            var item = new ExportStatusStatisticsOutput()
            {
                Status = info.Status,
                Count = info.Count,
                CancelCount = info.CancelCount,
                OperatorUserId = opUserId,
                AreaName = "全部区域",
            };
            item.AvgTime = info.Count > 0 && info.TotalTime > 0 ? decimal.Round((decimal)info.TotalTime / info.Count, 2) : 0;
            item.AvgCancelTime = info.CancelCount > 0 && info.TotalCancelTime > 0 ? decimal.Round((decimal)info.TotalCancelTime / info.CancelCount, 2) : 0;
            if (item.Status == StatisticsFunnelStatus.NewApplication)
            {
                item.ConversionRate = null;
            }
            else
            {
                var preCount = totalData[i - 1].Count;
                item.ConversionRate = preCount > 0 ? decimal.Round((decimal)item.Count / preCount * 100, 2) : 0;
            }
            totalList.Add(item);
        }
        return totalList;
    }

    // 辅助方法：处理新询单状态的原因类型
    private static void AssignNewApplicationReasons(BffExportCancelOperationDataOutput info, QueryOperationDataOutput item)
    {
        switch (item.ReasonType)
        {
            case GroupBookingOperationReasonType.CancelNewApplicationDomesticInquiry:
                info.CancelNewApplicationDomesticInquiry = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelNewApplicationDuplicateInquiry:
                info.CancelNewApplicationDuplicateInquiry = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelNewApplicationDemandChanges:
                info.CancelNewApplicationDemandChanges = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelNewApplicationDemandChangesReInquire:
                info.CancelNewApplicationDemandChangesReInquire = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelNewApplicationRoomsNotRequirement:
                info.CancelNewApplicationRoomsNotRequirement = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelNewApplicationOther:
                info.CancelNewApplicationOther = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelNewApplicationBudgetMismatch:
                info.CancelNewApplicationBudgetMismatch = item.Count;
                break;
        }
    }

    // 辅助方法：处理待询价状态的原因类型
    private static void AssignWaitForInquiryReasons(BffExportCancelOperationDataOutput info, QueryOperationDataOutput item)
    {
        switch (item.ReasonType)
        {
            case GroupBookingOperationReasonType.CancelWaitForInquiryUnableQuote:
                info.CancelWaitForInquiryUnableQuote = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelWaitForInquiryFullHouse:
                info.CancelWaitForInquiryFullHouse = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelWaitForInquiryUnableWaitQuotation:
                info.CancelWaitForInquiryUnableWaitQuotation = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelWaitForInquiryBudgetMismatch:
                info.CancelWaitForInquiryBudgetMismatch = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelWaitForInquiryCustomerDemandChanges:
                info.CancelWaitForInquiryCustomerDemandChanges = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelWaitForInquiryOther:
                info.CancelWaitForInquiryOther = item.Count;
                break;
        }
    }

    // 辅助方法：处理报价审核状态的原因类型
    private static void AssignInquiriedReasons(BffExportCancelOperationDataOutput info, QueryOperationDataOutput item)
    {
        switch (item.ReasonType)
        {
            case GroupBookingOperationReasonType.CancelInquiriedCustomerDemandChanges:
                info.CancelInquiriedCustomerDemandChanges = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelInquiriedNoAdvantage:
                info.CancelInquiriedNoAdvantage = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelInquiriedOther:
                info.CancelInquiriedOther = item.Count;
                break;
        }
    }

    // 辅助方法：处理已报价状态的原因类型
    private static void AssignQuotedReasons(BffExportCancelOperationDataOutput info, QueryOperationDataOutput item)
    {
        switch (item.ReasonType)
        {
            case GroupBookingOperationReasonType.CancelQuotedCustomerDemandChanges:
                info.CancelQuotedCustomerDemandChanges = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelQuotedNoAdvantage:
                info.CancelQuotedNoAdvantage = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelQuotedOther:
                info.CancelQuotedOther = item.Count;
                break;
        }
    }

    // 辅助方法：处理确认报价状态的原因类型
    private static void AssignQuotationConfirmedReasons(BffExportCancelOperationDataOutput info, QueryOperationDataOutput item)
    {
        switch (item.ReasonType)
        {
            case GroupBookingOperationReasonType.CancelQuotationConfirmedCustomerDemandChanges:
                info.CancelQuotationConfirmedCustomerDemandChanges = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelQuotationConfirmedQuotationOrRoomTypeChanges:
                info.CancelQuotationConfirmedQuotationOrRoomTypeChanges = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelQuotationConfirmedFullHouse:
                info.CancelQuotationConfirmedFullHouse = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelQuotationConfirmedOther:
                info.CancelQuotationConfirmedOther = item.Count;
                break;
        }
    }

    // 辅助方法：处理待审预订单状态的原因类型
    private static void AssignWaitForAuditPreOrderReasons(BffExportCancelOperationDataOutput info, QueryOperationDataOutput item)
    {
        switch (item.ReasonType)
        {
            case GroupBookingOperationReasonType.CancelWaitForAuditPreOrderCustomerDemandChanges:
                info.CancelWaitForAuditPreOrderCustomerDemandChanges = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelWaitForAuditPreOrderQuotationOrRoomTypeChanges:
                info.CancelWaitForAuditPreOrderQuotationOrRoomTypeChanges = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelWaitForAuditPreOrderFullHouse:
                info.CancelWaitForAuditPreOrderFullHouse = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelWaitForAuditPreOrderOther:
                info.CancelWaitForAuditPreOrderOther = item.Count;
                break;
        }
    }

    // 辅助方法：处理预订单状态的原因类型
    private static void AssignPreOrderedReasons(BffExportCancelOperationDataOutput info, QueryOperationDataOutput item)
    {
        switch (item.ReasonType)
        {
            case GroupBookingOperationReasonType.CancelPreOrderedCustomerDemandChanges:
                info.CancelPreOrderedCustomerDemandChanges = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelPreOrderedQuotationOrRoomTypeChanges:
                info.CancelPreOrderedQuotationOrRoomTypeChanges = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelPreOrderedFullHouse:
                info.CancelPreOrderedFullHouse = item.Count;
                break;
            case GroupBookingOperationReasonType.CancelPreOrderedOther:
                info.CancelPreOrderedOther = item.Count;
                break;
        }
    }
    #endregion

    /// <summary>
    /// 变更原因明细下载
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(BffExportStatusModifyOperationOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> ExportStatusModifyOperationData(BffQueryOperationDataInput input)
    {
        var searchInput = new QueryOperationDataInput()
        {
            OperationType = GrouBookingOperationType.ApplicationStatusModify,
            StartDate = input.StartDate,
            EndDate = input.EndDate,
            AreaIds = input.AreaIds,
            CountryCodes = input.CountryCodes,
            DimensionType = input.DimensionType,
            OperatorUserIds = input.OperatorUserIds,
        };
        var apiData = await _orderApiCaller.QueryGroupBookingOperationData(searchInput);
        var data = apiData.Data;
        var result = new List<BffExportStatusModifyOperationDataUserOutput>();
        var groupData = data.GroupBy(x => x.OperatorUserId)
                        .OrderBy(x => x.Key);
        var userIds = data.Where(x => x.OperatorUserId > 0)
               .Select(x => x.OperatorUserId!.Value)
               .Distinct().ToList();
        var exceptIds = input.OperatorUserIds
          .Where(id => id.HasValue && !userIds.Contains(id.Value))
          .ToList();
        if (input.OperatorUserIds?.Any() is true)
            userIds.AddRange(input.OperatorUserIds.Select(x => x!.Value));
        userIds = userIds.Distinct().ToList();
        var opUsers = await _userApiCaller.SearchTenantUsers(new SearchTenantUsersInput()
        {
            Ids = userIds.ToArray()
        });
        foreach (var group in groupData)
        {
            var userOutput = new BffExportStatusModifyOperationDataUserOutput()
            {
                OperatorUserId = group.Key,
                List = new()
            };
            result.Add(userOutput);
            userOutput.OperatorUserName = group.Key == null ? "全部运营" : opUsers.FirstOrDefault(x => x.Id == group.Key)?.Name;
            var areaGroupData = group.GroupBy(x => new { x.AreaId, x.AreaName })
                .OrderBy(x => x.Key.AreaId);
            foreach (var area in areaGroupData)
            {
                var info = new BffExportStatusModifyOperationDataOutput() { AreaName = area.Key.AreaName };
                foreach (var item in area)
                {
                    switch (item.Status)
                    {
                        case GroupBookingApplicationFormStatus.Inquiried:
                            AssignInquiriedStatusModifyReasons(info, item);
                            break;
                        case GroupBookingApplicationFormStatus.Quoted:
                            AssignQuotedStatusModifyReasons(info, item);
                            break;
                        case GroupBookingApplicationFormStatus.QuotationConfirmed:
                            AssignQuotationConfirmedStatusModifyReasons(info, item);
                            break;
                        case GroupBookingApplicationFormStatus.WaitForAuditPreOrder:
                            AssignWaitForAuditPreOrderStatusModifyReasons(info, item);
                            break;
                        case GroupBookingApplicationFormStatus.PreOrdered:
                            AssignPreOrderedStatusModifyReasons(info, item);
                            break;
                    }
                }
                userOutput.List.Add(info);
            }
        }
        if (result.Any() is false)
        {
            var userOutput = new BffExportStatusModifyOperationDataUserOutput()
            {
                List = new()
            };
            userOutput.OperatorUserName = "全部运营";
            result.Add(userOutput);
        }
        // 如果有未查询到的运营人员，则添加到结果中,给生成excel使用
        if (input.OperatorUserIds?.Any() is true && exceptIds.Any())
        {

            foreach (var userId in exceptIds)
            {
                var userOutput = new BffExportStatusModifyOperationDataUserOutput()
                {
                    OperatorUserId = userId,
                    List = new List<BffExportStatusModifyOperationDataOutput>()
                };
                userOutput.OperatorUserName = opUsers.FirstOrDefault(x => x.Id == userId)?.Name;
                result.Add(userOutput);
            }
        }
        foreach (var item in apiData.Details)
        {
            item.OperatorUserName = opUsers.FirstOrDefault(x => x.Id == item.OperatorUserId)?.Name;
        }
        var output = new BffExportStatusModifyOperationOutput()
        {
            Data = result,
            Details = apiData.Details
        };
        return Ok(output);
    }

    #region ExportStatusOperationData private

    private static void AssignInquiriedStatusModifyReasons(BffExportStatusModifyOperationDataOutput info, QueryOperationDataOutput item)
    {
        switch (item.ReasonType)
        {
            case GroupBookingOperationReasonType.StatusModifyInquiriedErrorPricing:
                info.StatusModifyInquiriedErrorPricing = item.Count;
                break;
            case GroupBookingOperationReasonType.StatusModifyInquiriedErrorMealType:
                info.StatusModifyInquiriedErrorMealType = item.Count;
                break;
            case GroupBookingOperationReasonType.StatusModifyInquiriedErrorBedType:
                info.StatusModifyInquiriedErrorBedType = item.Count;
                break;
            case GroupBookingOperationReasonType.StatusModifyInquiriedNoAdvantage:
                info.StatusModifyInquiriedNoAdvantage = item.Count;
                break;
            case GroupBookingOperationReasonType.StatusModifyInquiriedCustomerDemandChanges:
                info.StatusModifyInquiriedCustomerDemandChanges = item.Count;
                break;
            case GroupBookingOperationReasonType.StatusModifyInquiriedOther:
                info.StatusModifyInquiriedOther = item.Count;
                break;
        }
    }


    //
    private static void AssignQuotedStatusModifyReasons(BffExportStatusModifyOperationDataOutput info, QueryOperationDataOutput item)
    {
        switch (item.ReasonType)
        {
            case GroupBookingOperationReasonType.StatusModifyQuotedErrorPricing:
                info.StatusModifyQuotedErrorPricing = item.Count;
                break;
            case GroupBookingOperationReasonType.StatusModifyQuotedErrorMealType:
                info.StatusModifyQuotedErrorMealType = item.Count;
                break;
            case GroupBookingOperationReasonType.StatusModifyQuotedErrorBedType:
                info.StatusModifyQuotedErrorBedType = item.Count;
                break;
            case GroupBookingOperationReasonType.StatusModifyQuotedNoAdvantage:
                info.StatusModifyQuotedNoAdvantage = item.Count;
                break;
            case GroupBookingOperationReasonType.StatusModifyQuotedCustomerDemandChanges:
                info.StatusModifyQuotedCustomerDemandChanges = item.Count;
                break;
            case GroupBookingOperationReasonType.StatusModifyQuotedOther:
                info.StatusModifyQuotedOther = item.Count;
                break;
        }
    }

    /// <summary>
    /// 处理确认报价状态的回退原因
    /// </summary>
    private static void AssignQuotationConfirmedStatusModifyReasons(
        BffExportStatusModifyOperationDataOutput info,
        QueryOperationDataOutput item)
    {
        switch (item.ReasonType)
        {
            case GroupBookingOperationReasonType.StatusModifyQuotationConfirmedCustomerDemandChanges:
                info.StatusModifyQuotationConfirmedCustomerDemandChanges = item.Count;
                break;
            case GroupBookingOperationReasonType.StatusModifyQuotationConfirmedErrorMealType:
                info.StatusModifyQuotationConfirmedErrorMealType = item.Count;
                break;
            case GroupBookingOperationReasonType.StatusModifyQuotationConfirmedErrorBedType:
                info.StatusModifyQuotationConfirmedErrorBedType = item.Count;
                break;
            case GroupBookingOperationReasonType.StatusModifyQuotationConfirmedOther:
                info.StatusModifyQuotationConfirmedOther = item.Count;
                break;
        }
    }

    /// <summary>
    /// 处理待审预订单状态的回退原因
    /// </summary>
    private static void AssignWaitForAuditPreOrderStatusModifyReasons(
        BffExportStatusModifyOperationDataOutput info,
        QueryOperationDataOutput item)
    {
        switch (item.ReasonType)
        {
            case GroupBookingOperationReasonType.StatusModifyWaitForAuditPreOrderErrorPricing:
                info.StatusModifyWaitForAuditPreOrderErrorPricing = item.Count;
                break;
            case GroupBookingOperationReasonType.StatusModifyWaitForAuditPreOrderErrorMealType:
                info.StatusModifyWaitForAuditPreOrderErrorMealType = item.Count;
                break;
            case GroupBookingOperationReasonType.StatusModifyWaitForAuditPreOrderErrorBedType:
                info.StatusModifyWaitForAuditPreOrderErrorBedType = item.Count;
                break;
            case GroupBookingOperationReasonType.StatusModifyWaitForAuditPreOrderOther:
                info.StatusModifyWaitForAuditPreOrderOther = item.Count;
                break;
        }
    }

    /// <summary>
    /// 处理预订单状态的回退原因
    /// </summary>
    private static void AssignPreOrderedStatusModifyReasons(
        BffExportStatusModifyOperationDataOutput info,
        QueryOperationDataOutput item)
    {
        switch (item.ReasonType)
        {
            case GroupBookingOperationReasonType.StatusModifyPreOrderedShortPaymentTime:
                info.StatusModifyPreOrderedShortPaymentTime = item.Count;
                break;
            case GroupBookingOperationReasonType.StatusModifyPreOrderedCustomerOperationTimeout:
                info.StatusModifyPreOrderedCustomerOperationTimeout = item.Count;
                break;
            case GroupBookingOperationReasonType.StatusModifyPreOrderedCustomerPaymentTimeout:
                info.StatusModifyPreOrderedCustomerPaymentTimeout = item.Count;
                break;
            case GroupBookingOperationReasonType.StatusModifyPreOrderedOther:
                info.StatusModifyPreOrderedOther = item.Count;
                break;
        }
    }
    #endregion
}
