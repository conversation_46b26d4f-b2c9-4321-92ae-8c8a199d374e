using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.CompensationOrder;
using Bff.Vebk.Models.ScenicTicketOrder.OTA;
using Bff.Vebk.Services.Interfaces;
using Cit.Storage.Redis;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.DTOs.CarProductOrder;
using Contracts.Common.Order.DTOs.CompensationOrder;
using Contracts.Common.Order.DTOs.OpenChannelOrder;
using Contracts.Common.Order.DTOs.OpenChannelSyncFailOrder;
using Contracts.Common.Order.DTOs.OrderFieldInformation;
using Contracts.Common.Order.DTOs.ScenicTicketOrder;
using Contracts.Common.Order.DTOs.TravelLineOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.DTOs.CarProduct;
using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using StackExchange.Redis;
using UserType = Contracts.Common.Order.Enums.UserType;

namespace Bff.Vebk.Controllers;

[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class CompensationOrderController : ControllerBase
{
    private readonly IMapper _mapper;
    private readonly IRedisClient _redisClient;
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IScenicSpotApiCaller _scenicSpotApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IProductApiCaller _productApiCaller;
    private readonly IPaymentService _paymentService;
    private readonly IOpenChannelSyncFailOrderService _openChannelSyncFailOrderService;
    
    public CompensationOrderController(
        IMapper mapper,
        IRedisClient redisClient,
        IOrderApiCaller orderApiCaller,
        IScenicSpotApiCaller scenicSpotApiCaller,
        ITenantApiCaller tenantApiCaller,
        IProductApiCaller productApiCaller,
        IPaymentService paymentService,
        IOpenChannelSyncFailOrderService openChannelSyncFailOrderService)
    {
        _mapper = mapper;
        _redisClient = redisClient;
        _orderApiCaller = orderApiCaller;
        _scenicSpotApiCaller = scenicSpotApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _productApiCaller = productApiCaller;
        _paymentService = paymentService;
        _openChannelSyncFailOrderService = openChannelSyncFailOrderService;
    }
    
    /// <summary>
    /// 查询补差关联订单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200,typeof(PagingModel<SearchCompensationRelatedOrderBffOutput,SearchCompensationRelatedOrderExtendBffData>))]
    public async Task<IActionResult> Search(SearchCompensationRelatedOrderBffInput input)
    {
        var request = _mapper.Map<SearchCompensationRelatedOrderInput>(input);
        var response = await _orderApiCaller.SearchCompensationRelatedOrder(request);
        var result = _mapper.Map<PagingModel<SearchCompensationRelatedOrderBffOutput,SearchCompensationRelatedOrderExtendBffData>>(response);
        return Ok(result);
    }
    
    /// <summary>
    /// 补差单绑定
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200)]
    public async Task<IActionResult> Bind(BindCompensationOrderBffInput input)
    {
        var request = _mapper.Map<BindCompensationOrderInput>(input);
        await _orderApiCaller.BindCompensationOrder(request);
        return Ok();
    }

    /// <summary>
    /// 补差单解除绑定
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200)]
    public async Task<IActionResult> UnBind(UnBindCompensationOrderInput input)
    {
        var request = _mapper.Map<UnBindCompensationOrderInput>(input);
        await _orderApiCaller.UnBindCompensationOrder(request);
        return Ok();
    }
    
    /// <summary>
    /// 查询补差关联订单详情
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200,typeof(List<GetCompensationRelatedOrderDetailBffOutput>))]
    public async Task<IActionResult> Get(long baseOrderId)
    {
        var response = await _orderApiCaller.GetCompensationRelatedOrder(baseOrderId);
        var result = _mapper.Map<List<GetCompensationRelatedOrderDetailBffOutput>>(response);
        return Ok(result);
    }

    /// <summary>
    /// 补差单创建
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200,typeof(CreateCompensationOrderBffOutput))]
    [SwaggerResponseExt(default, ErrorTypes.Order.ChannelOrderNoIsExists,
        ErrorTypes.Common.NotSupportedOperation, ErrorTypes.Order.AgencyIsInconsistent)]
    public async Task<IActionResult> Create(CreateCompensationOrderBffInput input)
    {
        var result = new CreateCompensationOrderBffOutput();
        long? compensationOrderId = null;
        long? compensationSubOrderId = null;
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        
        // 分销商校验.限制与补差主单同一个分销商
        await _orderApiCaller.PreBindCompensationOrder(new BindCompensationOrderPreCheckInput
        {
            BaseOrderId = input.CompensationMasterOrderId,
            AgencyId = input.AgencyId
        });
        
        
        // 渠道单号校验 不允许重复
        var channelOrderNo = string.Join(',',input.ChannelOrderNo);
        var checkRequest = new CheckChannelOrderInput {ChannelOrderNo = channelOrderNo};
        var normalOrderCheckResult = await _orderApiCaller.CheckChannelOrder(checkRequest);
        if (normalOrderCheckResult.RelatedBaseOrderInfos.Any())
            throw new BusinessException(ErrorTypes.Order.ChannelOrderNoIsExists);
        var abnormalOrderCheckResult = await _orderApiCaller.CheckChannelOrderAbnormalOrder(checkRequest);
        // 当存在关联异常订单时：
        // 1. 如果是异常订单创建补差单，且存在多个关联订单时抛出异常
        // 2. 普通创建场景直接抛出异常
        if (abnormalOrderCheckResult.RelatedBaseOrderInfos.Any() && 
            (!input.SyncFailOrderId.HasValue || abnormalOrderCheckResult.RelatedBaseOrderInfos.Count > 1))
        {
            throw new BusinessException(ErrorTypes.Order.ChannelOrderNoIsExists);
        }
        
        switch (input.CompensationMasterType)
        {
            case OrderType.ScenicTicket:

                #region 补差单 - 门票处理

                /*
                 * 1.查询补差产品数据
                 */
                var getCompensationTicket = await _scenicSpotApiCaller.GetCompensationScenicTicket();
                
                /*
                 * 2.创建订单
                 */
                var createScenicTicketOrder = await _orderApiCaller.CreateScenicTicketOrderByManual(new CreateByManualInput
                {
                    SyncFailOrderId = input.SyncFailOrderId,
                    UserType = UserType.Merchant,
                    ScenicSpotId = getCompensationTicket.ScenicSpotId,
                    ScenicTicketId = getCompensationTicket.TicketId,
                    AgencyId = input.AgencyId,
                    AgencyName = input.AgencyName,
                    SellingPlatform = SellingPlatform.System,
                    SellingChannels = input.SellingChannel,
                    ChannelOrderNo = input.ChannelOrderNo,
                    Remark = input.Remark,
                    Quantity = 1,
                    SellingPrice = input.TotalAmount,
                    PaymentAmount = input.TotalAmount,
                    CostPrice = 0
                });

                compensationOrderId = createScenicTicketOrder.BaseOrderId;
                compensationSubOrderId = createScenicTicketOrder.ScenicTicketOrderId;
                
                if (createScenicTicketOrder.BaseOrderId is > 0)
                {
                    // 抖音订单确认
                    if (input is { SyncFailOrderId: not null, SellingChannel: SellingChannels.TikTok })
                    {
                        await _openChannelSyncFailOrderService.OpenChannelOrderConfirm(input.SyncFailOrderId.Value, input.ChannelOrderNo);
                    }
                }

                #endregion
                
                break;
            case OrderType.TravelLineOrder:

                #region 补差单 - 线路处理
                
                /*
                 * 1.查询补差产品数据
                 */
                var getCompensationTravelLine = await _productApiCaller.GetCompensationLine();
                
                /*
                 * 2.创建订单
                 */
                var createLineOrder = await CreateLineOrder(productId: getCompensationTravelLine.LineProductId,
                    skuId: getCompensationTravelLine.LineProductSkuId,
                    input: input,
                    currentUser: currentUser);
                
                compensationOrderId = createLineOrder.BaseOrderId;
                compensationSubOrderId = createLineOrder.LineProductOrderId;

                #endregion
                
                break;
            case OrderType.CarProduct:

                #region 补差单 - 用车处理

                /*
                 * 1.查询补差产品数据
                 */
                var getCompensationCar = await _productApiCaller.GetCompensationCarProduct();
                
                /*
                 * 2.创建订单
                 */
                var createCarProductOrder = await CreateCarProductOrder(productId: getCompensationCar.CarProductId,
                    skuId: getCompensationCar.CarProductSkuId,
                    input: input,
                    currentUser: currentUser);
                
                compensationOrderId = createCarProductOrder.BaseOrderId;
                compensationSubOrderId = createCarProductOrder.CarProductOrderId;

                #endregion
                
                break;
            default:
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        if (compensationOrderId.HasValue)
        {
            //关联补差单
            await _orderApiCaller.BindCompensationOrder(new BindCompensationOrderInput
            {
                BaseOrderId = input.CompensationMasterOrderId,
                OrderType = input.CompensationMasterType,
                BindBaseOrderIdArr = new List<long>
                {
                    compensationOrderId.Value
                }
            });
            
            //补差单信息补充
            await _orderApiCaller.SupplementCompensationOrder(new SupplementCompensationOrderDataInput
            {
                CompensationOrderId = compensationOrderId.Value,
                CompensationSubOrderId = compensationSubOrderId.Value,
                CompensationMasterOrderId = input.CompensationMasterOrderId,
                OrderType = input.CompensationMasterType
            });
            
            result.BaseOrderId = compensationOrderId.Value;
        }
        
        return Ok(result);
    }


    private async Task<CreateLineOrderOutput> CreateLineOrder(
        long productId,
        long skuId,
        CreateCompensationOrderBffInput input,
        CurrentUser currentUser)
    {
        var agency = await _tenantApiCaller.GetAgencyDetail(input.AgencyId);
        var lineProductResponse = await _productApiCaller.GetLineProduct(productId);
        var lineProductSkuResponse = new GetLineProductSkuDto
        {
            LineProductSkuId = skuId,
            LineProductSkuName = "补差套餐-系统创建",
            FeeIncludes = new List<FeeInclude>()
        };
        
        //多币种
        string paymentCurrencyCode = agency.CurrencyCode;
        var priceExchangeRate = await _paymentService.GetOrderPriceExchange(new OrderPriceExchangeRateInput
        {
            CostCurrencyCode = lineProductResponse.CostCurrencyCode,
            SaleCurrencyCode = lineProductResponse.SaleCurrencyCode,
            PaymentCurrencyCode = paymentCurrencyCode,
        });
        
        var sellingPlatform = SellingPlatform.System;
        //处理失败异常订单创建
        if (input.SyncFailOrderId.HasValue)
        {
            var syncFailOrder =
                await _orderApiCaller.GetOpenChannelSyncFailOrderDetail(new GetOpenChannelSyncFailOrderInput
                {
                    SyncFailOrderId = input.SyncFailOrderId.Value
                });

            //异常单状态判断
            if (syncFailOrder.SyncStatus != OpenChannelFailOrderSyncStatus.SyncFail)
            {
                throw new BusinessException(ErrorTypes.Order.SyncFailOrderStatusChanged);
            }
            sellingPlatform = syncFailOrder.SellingPlatform;
            input.SellingChannel = syncFailOrder.SellingChannels;
        }

        var travelLineOrderMultPrices = new List<TravelLineOrderMultPriceDto>
        {
            new TravelLineOrderMultPriceDto
            {
                Type = LineSkuPriceType.Adult,
                Quantity = 1,
                OrderMultPrice = new OrderMultPriceDto
                {
                    Quantity = 1,
                    OrgCostPrice = 0,
                    CostPrice = 0,
                    CostCurrencyCode = lineProductResponse.CostCurrencyCode,
                    OrgPrice = input.TotalAmount,
                    Price = input.TotalAmount,
                    SaleCurrencyCode = lineProductResponse.SaleCurrencyCode,
                    PaymentCurrencyCode = paymentCurrencyCode,
                    CostExchangeRate = priceExchangeRate.CostExchangeRate,
                    ExchangeRate = priceExchangeRate.ExchangeRate,
                }
            }
        };
        
        Contracts.Common.Order.DTOs.TravelLineOrder.CreateDto createDto = new()
        {
            SyncFailOrderId = input.SyncFailOrderId,
            TenantId = currentUser.Tenant,
            SellingChannel = input.SellingChannel,
            SellingPlatform = sellingPlatform,
            LineProduct = lineProductResponse,
            LineProductSku = lineProductSkuResponse,
            TravelDate = DateTime.Today,
            NumberOfRooms = 0,
            PaymentCurrencyCode = paymentCurrencyCode,
            TravelLineOrderMultPrices = travelLineOrderMultPrices,
            OrderDiscountItem = null,
            OrderTravelers = new List<OrderTravelerInput>(),
            LineProductRallyPointId = null,
            OrderUserInfo = new OrderUserInfoDto
            {
                UserId = currentUser.UserId,
                UserNickName = currentUser.NickName,
                UserType = UserType.Agency,
                AgencyId = agency.Id,
                AgencyName = agency.FullName,
                SalespersonId = agency.SalespersonId,
                SalespersonName = agency.SalespersonName,
            },
            ContactsName = string.Empty,
            ContactsPhoneNumber = string.Empty,
            ContactsEmail = string.Empty,
            Remark = input.Remark,//租户侧备注信息
            ChannelOrderNo = string.Join(",", input.ChannelOrderNo),
            SupplierOrderId = string.Empty,
            OperationUser = new OperationUserDto
            {
                UserId = currentUser.UserId,
                Name = currentUser.NickName, 
                UserType = UserType.Merchant
            },
            OrderFields = new List<SaveOrderFieldInformationTypeDto>()
        };
        
        var result = await _orderApiCaller.TravelLineOrderCreate(createDto);
        return result;
    }

    private async Task<CreateCarProductOrderOutput> CreateCarProductOrder(
        long productId,
        long skuId,
        CreateCompensationOrderBffInput input,
        CurrentUser currentUser)
    {
        var agencyId = input.AgencyId;
        var tenantId = currentUser.Tenant;
        var travelDate = DateTime.Today;
        var agency = await _tenantApiCaller.GetAgencyDetail(agencyId);
        string paymentCurrencyCode = agency.CurrencyCode;
        
        //用车信息
        var apiInput = new CarProductDetailCalendarPricesInput()
        {
            StartDate = travelDate,
            EndDate = travelDate,
            ProductId = productId,
            SkuIds = new List<long> { skuId },
            B2bSellingStatus = null,
        };
        var carProductDetail = await _productApiCaller.GetCarProductPriceDetails(apiInput);
        var product = carProductDetail.Product;
        var sku = carProductDetail.Skus.FirstOrDefault(x => x.CarProductSkuId == skuId);
        var skuPrice = sku?.CalendarPrices.FirstOrDefault(x => x.Date == travelDate);
        //币种汇率
        OrderPriceExchangeRateOutput priceExchangeRate = await _paymentService.GetOrderPriceExchange(new OrderPriceExchangeRateInput
        {
            CostCurrencyCode = product.CostCurrencyCode,
            SaleCurrencyCode = product.SaleCurrencyCode,
            PaymentCurrencyCode = paymentCurrencyCode
        });
        OrderMultPriceDto orderMultPrice = new()
        {
            SaleCurrencyCode = product.SaleCurrencyCode,
            OrgPrice = input.TotalAmount,
            CostCurrencyCode = product.CostCurrencyCode,
            OrgCostPrice = 0,
            CostPrice = 0,
            PaymentCurrencyCode = paymentCurrencyCode,
            Price = input.TotalAmount,
            Quantity = 1,
            CostExchangeRate = priceExchangeRate.CostExchangeRate,
            ExchangeRate = priceExchangeRate.ExchangeRate,
        };
        
        CarProductInfoDto carProductInfo = new()
        {
            CarProductId = product.Id,
            CarProductType = product.CarProductType,
            PurchaseSourceType = product.PurchaseSourceType,
            Title = product.Title,
            EnTitle = product.Title,
            Instructions = product.Instructions,
            FeeInclude = product.FeeInclude,
            FeeExclude = product.FeeExclude,
            CancelType = product.CancelType,
            CancelRule = product.CancelRule,
            SupplierId = product.SupplierId,
            AirportId = product.AirportId,
            AirportName = product.AirportName,
            IsCompensation = product.IsCompensation,
            AirportTransferType = sku.AirportTransferType,
            CarProductSkuId = sku.CarProductSkuId,
            Name = sku.Name,
            CarTypeGradeId = sku.CarTypeGradeId,
            GradeName = sku.GradeName,

            OrderMultPrice = orderMultPrice,
            AutoConfirm = product.AutoConfirm,
            MinProfit = skuPrice.MinProfit,
        };
        
        CarTravelInfoDto carTravelInfo = new()
        {
            Quantity = 1,
            Passengers = 1,
            Baggages = 1,
            DepartureCityCode = product.CityCode,
            DepartureCityName = product.CityName,
            DepartureAddress = string.Empty,
            DestinationCityCode = product.CityCode,
            DestinationCityName = product.CityName,
            DestinationAddress = string.Empty,
            TravelDate = travelDate,
            AirportId = product.AirportId,
        };
        
        //订单金额
        decimal totalAmount = carProductInfo.OrderMultPrice.Price * carProductInfo.OrderMultPrice.Quantity;
        decimal discountAmount = 0;
        decimal paymentAmount = totalAmount;

        //处理失败异常订单创建
        var sellingPlatform = SellingPlatform.System;
        if (input.SyncFailOrderId.HasValue)
        {
            var syncFailOrder =
                await _orderApiCaller.GetOpenChannelSyncFailOrderDetail(new GetOpenChannelSyncFailOrderInput
                {
                    SyncFailOrderId = input.SyncFailOrderId.Value
                });

            //异常单状态判断
            if (syncFailOrder.SyncStatus != OpenChannelFailOrderSyncStatus.SyncFail)
            {
                throw new BusinessException(ErrorTypes.Order.SyncFailOrderStatusChanged);
            }

            sellingPlatform = syncFailOrder.SellingPlatform;
            input.SellingChannel = syncFailOrder.SellingChannels;
        }
        
        CreateCarProductOrderInput createCarProductOrderInput = new()
        {
            SyncFailOrderId = input.SyncFailOrderId,
            TenantId = currentUser.Tenant,
            SellingChannel = input.SellingChannel,
            SellingPlatform = sellingPlatform,
            ChannelOrderNo = input.ChannelOrderNo?.Length is > 0 ? string.Join(',', input.ChannelOrderNo) : string.Empty,
            SupplierOrderId = string.Empty,
            ContactsName = string.Empty,
            ContactsPhoneNumber = string.Empty,
            ContactsEmail = string.Empty,
            OrderUserInfo = new OrderUserInfo
            {
                AgencyId = agencyId,
                AgencyName = agency.FullName,
                UserType = UserType.Merchant,
                UserId = currentUser.UserId,
                NickName = currentUser.NickName,
                SalespersonId = agency.SalespersonId,
                SalespersonName = agency.SalespersonName,
                VipLevelId = agency.Level ?? 0,
                VipLevelName = agency.LevelName ?? string.Empty,
            },
            TotalAmount = totalAmount,
            DiscountAmount = discountAmount,
            PaymentAmount = paymentAmount,
            PaymentCurrencyCode = paymentCurrencyCode,
            CarProduct = carProductInfo,
            CarServiceItems = new List<CarServiceItemDto>(),
            Message = string.Empty,
            Remark = input.Remark,
            CarTravelInfo = carTravelInfo,
            DevelopUserId = product.DevelopUserId,
            //OperatorUserId = product.ProductOperatorUser?.FirstOrDefault(x => x.SellingPlatform == SellingPlatform.System)?.OperatorUserId,
            OpenSupplierType = OpenSupplierType.System
        };
        
        var result = await _orderApiCaller.CarProductOrderCreate(createCarProductOrderInput);
        return result;
    }
}