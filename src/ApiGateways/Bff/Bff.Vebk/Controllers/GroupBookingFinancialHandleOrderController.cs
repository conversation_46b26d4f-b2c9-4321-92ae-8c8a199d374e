using Bff.Vebk.Callers;
using Bff.Vebk.Models.GroupBookingFinancialHandleOrder;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs.GroupBookingFinancialHandleOrder;
using Contracts.Common.Payment.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

/// <summary>
/// 团房财务处理审核
/// </summary>
[Route("v1/[controller]/[action]")]
[Authorize]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class GroupBookingFinancialHandleOrderController : ControllerBase
{
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IPaymentApiCaller _paymentApiCaller;

    public GroupBookingFinancialHandleOrderController(IOrderApiCaller orderApiCaller,
        IPaymentApiCaller paymentApiCaller)
    {
        _orderApiCaller = orderApiCaller;
        _paymentApiCaller = paymentApiCaller;
    }

    /// <summary>
    /// 转账付款
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(long), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Create(CreateInput input)
    {

        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var detail = await _orderApiCaller.GroupBookingOrderDetail(new Contracts.Common.Order.DTOs.GroupBookingOrder.DetailInput
        {
            GroupBookingOrderId = input.GroupBookingOrderId,
        });
        var payment = detail.Payments.FirstOrDefault(s => s.Id == input.GroupBookingOrderPaymentId);

        if ((payment.PayStatus is PayStatus.Paid or PayStatus.Processing)
            || payment.HandleOrderStatus == Contracts.Common.Order.Enums.GroupBookingFinancialHandleOrderStatus.Confirming)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var tenantBankAccount = await _paymentApiCaller.GetTenantBankAccount(input.TenantBankAccountId);
        var result = await _orderApiCaller.GroupBookingFinancialHandleOrderCreate(new FinancialHandleOrderCreateInput
        {
            GroupBookingOrderPaymentId = input.GroupBookingOrderPaymentId,
            AgencyId = detail.GroupBookingOrder.AgencyId,
            AgencyName = detail.GroupBookingOrder.AgencyName ?? string.Empty,
            Amount = payment.PaymentAmount,
            CurrencyCode = payment.PaymentCurrencyCode,
            PayType = PayType.Offline,
            Creator = currentUser.NickName,
            CreatorId = currentUser.UserId,
            AccountTime = input.AccountTime,
            Remark = input.Remark,
            Proof = input.Proof,
            TenantBankAccount = new Contracts.Common.Order.DTOs.AfterSaleFinancialHandleOrder.TenantBankAccountInfoDto
            {
                Id = tenantBankAccount.Id,
                TenantBankAccountType = tenantBankAccount.TenantBankAccountType,
                AccountName = tenantBankAccount.AccountName,
                BankCode = tenantBankAccount.BankCode,
                BankName = tenantBankAccount.BankName,
                BranchName = tenantBankAccount.BranchName,
                AccountNo = tenantBankAccount.AccountNo,
                Address = tenantBankAccount.Address,
                SwiftCode = tenantBankAccount.SwiftCode,
                CurrencyCode = tenantBankAccount.CurrencyCode,
                OpeningBankCode = tenantBankAccount.OpeningBankCode,
            }
        });
        return Ok(result);
    }

    /// <summary>
    /// 搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(FinancialHandleOrderSearchInput input)
    {
        var result = await _orderApiCaller.GroupBookingFinancialHandleOrderSearch(input);
        PagingModel<SearchOutput> paging = new()
        {
            PageIndex = result.PageIndex,
            PageSize = result.PageSize,
            Total = result.Total,
            Data = result.Data.Select(s =>
            {
                var accountNo = Models.Common.DataSecrecyHelper.DataSecrecyEncrypt(new Models.Common.SensitiveDataDto
                {
                    Data = s.TenantBankAccount.AccountNo,
                    Id = s.Id,
                    IdType = Models.Common.SensitiveDataIdType.BankAccount,
                    DataType = Models.Common.SensitiveDataType.BankAccount,
                    CanView = false,
                });
                return new SearchOutput
                {
                    Id = s.Id,
                    Amount = s.Amount,
                    CurrencyCode = s.CurrencyCode,
                    Creator = s.Creator,
                    CreatorId = s.CreatorId,
                    GroupBookingOrderId = s.GroupBookingOrderId,
                    PayType = s.PayType,
                    Status = s.Status,
                    CreateTime = s.CreateTime,
                    AccountNo = accountNo,
                    AccountTime = s.AccountTime,
                    BankName = s.TenantBankAccount.BankName,
                };
            })
        };
        return Ok(paging);
    }

    /// <summary>
    /// 详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(FinancialHandleOrderDetailOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Detail(long id)
    {
        var result = await _orderApiCaller.GroupBookingFinancialHandleOrderDetail(new FinancialHandleOrderDetailInput
        {
            Id = id
        });
        if (!string.IsNullOrWhiteSpace(result.TenantBankAccount?.AccountNo))
        {
            var accountNo = Models.Common.DataSecrecyHelper.DataSecrecyEncrypt(new Models.Common.SensitiveDataDto
            {
                Data = result.TenantBankAccount.AccountNo,
                Id = result.Id,
                IdType = Models.Common.SensitiveDataIdType.BankAccount,
                DataType = Models.Common.SensitiveDataType.BankAccount,
                CanView = true,
            });
            result.TenantBankAccount.AccountNo = accountNo;
        }
        return Ok(result);
    }

    /// <summary>
    /// 处理 待确认状态才能操作
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Handle(HandleInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        await _orderApiCaller.GroupBookingFinancialHandleOrderHandle(new FinancialHandleOrderHandleInput
        {
            Id = input.Id,
            RejectReason = input.RejectReason,
            Status = input.Status,
            Operator = currentUser.NickName,
            OperatorId = currentUser.UserId
        });
        return Ok();
    }

}
