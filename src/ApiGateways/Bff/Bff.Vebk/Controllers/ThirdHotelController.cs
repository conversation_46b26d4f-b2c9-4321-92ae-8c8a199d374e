using Bff.Vebk.Callers;
using Bff.Vebk.Models.ThirdHotel;
using Bff.Vebk.Services.Interfaces;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Hotel.DTOs.ApiHotel;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Resource.DTOs.Hotel;
using Contracts.Common.Resource.DTOs.ThirdHotel;
using Contracts.Common.Tenant.DTOs.TenantApiSupplierConfig;
using Contracts.Common.Tenant.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Collections.Concurrent;

namespace Bff.Vebk.Controllers;

/// <summary>
/// 第三方平台酒店
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class ThirdHotelController : ControllerBase
{
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly IHotelApiCaller _hotelApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;

    private readonly IHotelService _hotelService;

    public ThirdHotelController(IResourceApiCaller resourceApiCaller,
        IHotelApiCaller hotelApiCaller,
        ITenantApiCaller tenantApiCaller,
        IHotelService hotelService)
    {
        _resourceApiCaller = resourceApiCaller;
        _hotelApiCaller = hotelApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _hotelService = hotelService;
    }

    /// <summary>
    /// 获取酒店报价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(IEnumerable<GetSaleChannelPriceOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetSaleChannelPrices(GetSaleChannelPriceInput input)
    {
        var tenantId = HttpContext.GetTenantId();

        // 查询saas租户api供应商配置
        var saasTenantApiSupplierConfig = await _tenantApiCaller.TenantApiSupplierConfigDetail(new GetTenantApiSupplierConfigDetailInput
        {
            SupplierApiType = SupplierApiType.Hop,
            TenantId = tenantId
        });
        if (saasTenantApiSupplierConfig.Enabled == false
            || (saasTenantApiSupplierConfig.IsAllHotel == false && !saasTenantApiSupplierConfig.HotelIds.Contains(input.ResourceHotelId)))
        {
            return Ok(Enumerable.Empty<GetSaleChannelPriceOutput>());
        }
        var apiSaleType = _hotelService.MapApiHotelSaleType(saasTenantApiSupplierConfig.HotelPriceChannelTypes);

        var checkInDate = input.BeginDate;
        var checkOutDate = input.EndDate.AddDays(1);
        var dates = new List<(DateTime checkIn, DateTime checkOut)>();
        var days = input.EndDate.Subtract(input.BeginDate).TotalDays;
        for (int i = 0; i <= days; i++)
        {
            var checkIn = input.BeginDate.AddDays(i);
            var checkOut = checkIn.AddDays(1);
            dates.Add(new(checkIn, checkOut));
        }
        GetThirdHotelPriceInput getThirdHotelPriceInput = new()
        {
            TenantId = tenantId,
            ResourceHotelId = input.ResourceHotelId,
            SupplierApiTypes = new SupplierApiType[] { input.SupplierApiType },
            CheckIn = checkInDate,
            CheckOut = checkOutDate,
            OnlyCachePrice = true,//仅返回缓存价格
        };
        var getThirdHotelPrice = await _resourceApiCaller.GetThirdHotelPrice(getThirdHotelPriceInput);

        var channelMarkupsTask = _hotelApiCaller.GetApiHotelSettings();
        var suppliersTask = _tenantApiCaller.QuerySuppliers(new Contracts.Common.Tenant.DTOs.Supplier.QuerySuppliersInput
        {
            SupplierType = SupplierType.Api,
            SupplierApiParentType = SupplierApiParentType.Hotel,
            SupplierApiType = input.SupplierApiType,
        });
        var thirdHotelRooms = getThirdHotelPrice.Rooms
            .GroupBy(r => new { r.ResourceRoomId, r.RoomId })
            .Select(r => new ThirdHotelRoomOutput
            {
                ResourceRoomId = r.Key.ResourceRoomId,
                RoomId = r.Key.RoomId,
                ZHName = r.First().ZHName,
                ENName = r.First().ENName,
                AreaMax = r.First().AreaMax,
                AreaMin = r.First().AreaMin,
                BedType = r.First().BedType,
                FloorMax = r.First().FloorMax,
                FloorMin = r.First().FloorMin,
                MaximumOccupancy = r.First().MaximumOccupancy,
                RoomQuantity = r.First().RoomQuantity,
                WindowType = r.First().WindowType,
                Pricestrategies = r.SelectMany(p => p.Pricestrategies)
                    .Where(x => x.SaleType is null || apiSaleType.Contains(x.SaleType!.Value)) // 过滤报价计划类型
                    .GroupBy(p => new { p.PricestrategyId, p.PriceStrategyType, p.SupplierApiType, })
                    .Select(p => new ThirdHotelPricestrategyOutput
                    {
                        PricestrategyId = p.Key.PricestrategyId,
                        PriceStrategyType = p.Key.PriceStrategyType,
                        SupplierApiType = p.Key.SupplierApiType,
                        SupplierId = p.First().SupplierId,
                        Name = p.First().Name,
                        ENName = p.First().ENName,
                        CostCurrencyCode = p.First().CostCurrencyCode,
                        SaleCurrencyCode = p.First().SaleCurrencyCode,
                        NationalNames = p.First().NationalNames,
                        BookingHoursInAdvance = p.First().BookingHoursInAdvance,
                        Enabled = p.First().Enabled,
                        IsAutoConfirm = p.First().IsAutoConfirm,
                        ConfirmByMins = p.First().ConfirmByMins,
                        NumberOfBreakfast = p.First().NumberOfBreakfast,
                        BoardCodeType = p.First().BoardCodeType,
                        BoardCount = p.First().BoardCount,
                        CancelRules = p.First().CancelRules,
                        IsDirect = p.First().IsDirect,
                        NumberOfNights = p.First().NumberOfNights,
                        NumberOfRooms = p.First().NumberOfRooms,
                        Calendars = p.SelectMany(c => c.Calendars).OrderBy(c => c.Date),
                        MaxOccupancy = p.First().MaxOccupancy,
                    })
            })
            .ToList();
        var channelMarkups = await channelMarkupsTask;
        var suppliers = await suppliersTask;
        var channelMarkup = channelMarkups.FirstOrDefault(x => x.ChannelType == input.SellingChannel);
        var result = thirdHotelRooms
            .Select(r =>
            {
                var roomOutput = new GetSaleChannelPriceOutput
                {
                    ResourceRoomId = r.ResourceRoomId,
                    ENName = r.ENName,
                    ZHName = r.ZHName,
                    WindowType = r.WindowType,
                    AreaMin = r.AreaMin,
                    AreaMax = r.AreaMax,
                    FloorMin = r.FloorMin,
                    FloorMax = r.FloorMax,
                    MaximumOccupancy = r.MaximumOccupancy,
                    RoomQuantity = r.RoomQuantity,
                    BedType = GetBedType(r.BedType),

                };
                roomOutput.Pricestrategies = r.Pricestrategies.Select(p =>
                {
                    var supplier = suppliers.FirstOrDefault(x => x.Id == p.SupplierId);
                    var pricestrategyOutput = new PricestrategyOutput
                    {
                        PricestrategyId = p.PricestrategyId,
                        Name = p.Name,
                        ENName = p.ENName,
                        PriceStrategyType = p.PriceStrategyType,
                        SupplierId = p.SupplierId,
                        SupplierName = supplier?.Name,
                        BookingHoursInAdvance = p.BookingHoursInAdvance,
                        NumberOfBreakfast = p.NumberOfBreakfast,
                        BoardCodeType = p.BoardCodeType,
                        BoardCount = p.BoardCount,
                        NumberOfNights = p.NumberOfNights,
                        NumberOfRooms = p.NumberOfRooms,
                        IsAutoConfirm = p.IsAutoConfirm,
                        Enabled = p.Enabled,
                        Calendars = dates.Select(d =>
                        {
                            var date = d.checkIn;
                            var pCalendar = p.Calendars.FirstOrDefault(x => x.Date == date);
                            var r = new PriceStrategyCalendarPriceOutput
                            {
                                Date = date,
                                Quantity = pCalendar?.Quantity ?? 0,
                                OverSaleable = pCalendar?.OverSaleable ?? false,
                                Enabled = pCalendar?.Enabled ?? false,
                                CostCurrencyCode = p.CostCurrencyCode,
                                CostPrice = pCalendar?.CostPrice,
                                SaleCurrencyCode = p.SaleCurrencyCode,
                                ChannelPrice = TranslateChannelPrice(channelMarkup, pCalendar?.CostPrice),
                                SaleChannelPrices = channelMarkups
                            .Select(c => new SaleChannelPriceOutput
                            {
                                SellingChannel = c.ChannelType,
                                SaleCurrencyCode = p.SaleCurrencyCode,
                                Price = TranslateChannelPrice(c, pCalendar?.CostPrice)
                            })
                            .Where(c => c.Price is not null)
                            };
                            return r;
                        }),
                        CancelRule = p.CancelRule is not null ? new PriceStrategyCancelRuleOutput
                        {
                            BeforeCheckInDays = p.CancelRule.BeforeCheckInDays,
                            CancelChargeType = p.CancelRule.CancelChargeType,
                            CancelRulesType = p.CancelRule.CancelRulesType,
                            ChargeValue = p.CancelRule.ChargeValue,
                            CheckInDateTime = p.CancelRule.CheckInDateTime,
                        } : new PriceStrategyCancelRuleOutput { CancelRulesType = CancelRulesType.CannotCancel },
                        NationalNames = p.NationalNames,
                        MaxOccupancy = p.MaxOccupancy,
                    };
                    return pricestrategyOutput;
                }).OrderBy(p => p.PricestrategyId);
                return roomOutput;
            });
        return Ok(result);
    }

    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(Contracts.Common.Resource.DTOs.ThirdHotel.SearchOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetByHopId(long hopId)
    {
        await _resourceApiCaller.SyncOpenHotelByHopId(hopId);
        var searchOutputs = await _resourceApiCaller.ThirdHotelSearch(new Contracts.Common.Resource.DTOs.ThirdHotel.SearchInput
        {
            PageIndex = 1,
            PageSize = 1,
            HopId = hopId
        });
        var result = searchOutputs.Data.FirstOrDefault();
        return Ok(result);
    }

    private static string GetBedType(string bedType)
    {
        if (string.IsNullOrEmpty(bedType))
            return string.Empty;
        //序列化床型
        var bedEnumerable = JsonConvert.DeserializeObject<List<BedType>>(bedType)?
            .Select(x => x.main) ?? new List<string>();
        return string.Join(",", bedEnumerable);
    }

    private static decimal? TranslateChannelPrice(ApiHotelSettingDto? channelMarkup, decimal? price)
    {
        if (channelMarkup?.Enabled is not true || price is null) return null;
        var channelPrice = channelMarkup.MarkupType switch
        {
            MarkupType.AddValue => price + channelMarkup.Value,
            MarkupType.SubtractValue => price - channelMarkup.Value,
            MarkupType.AddRate => price * (1 + channelMarkup.Value),
            MarkupType.SubtractRate => price * (1 - channelMarkup.Value),
            _ => price
        };
        return channelPrice != null ? Math.Round(channelPrice!.Value, 2) : null;
    }

    /// <summary>
    /// 设置汇智酒店价格策略 名称 上下架信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> SetPricestrategy(Contracts.Common.Resource.DTOs.ThirdHotelPricestrategy.SetThirdHotelPricestrategyInput input)
    {
        await _resourceApiCaller.SetThirdHotelPricestrategy(input);
        return Ok();
    }

}
