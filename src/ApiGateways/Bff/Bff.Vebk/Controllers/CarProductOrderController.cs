using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.CarProductOrder;
using Bff.Vebk.Models.CarProductOrder.OTA;
using Bff.Vebk.Models.Common;
using Bff.Vebk.Models.OpenChannelSyncFailOrder;
using Bff.Vebk.Models.Order;
using Bff.Vebk.Models.TravelLineOrder.OTA;
using Bff.Vebk.Services.Interfaces;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.CarProductOrder;
using Contracts.Common.Order.DTOs.CarProductSupplierOrder;
using Contracts.Common.Order.DTOs.OpenChannelSyncFailOrder;
using Contracts.Common.Order.DTOs.OrderFieldInformation;
using Contracts.Common.Order.DTOs.TravelLineOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Product.DTOs.CarProduct;
using Contracts.Common.Product.DTOs.CarProductSku;
using Contracts.Common.Product.DTOs.ProductInformationTemplate;
using Contracts.Common.Product.Enums;
using Contracts.Common.Tenant.DTOs.Supplier;
using Contracts.Common.Tenant.DTOs.SupplierApiSetting;
using Contracts.Common.Tenant.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using UserType = Contracts.Common.Order.Enums.UserType;

namespace Bff.Vebk.Controllers;

/// <summary>
/// 用车订单
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class CarProductOrderController : ControllerBase
{
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IProductApiCaller _productApiCaller;
    private readonly IBaseOrderService _baseOrderService;
    private readonly IPaymentService _paymentService;
    private readonly IAgencyChannelPriceService _agencyChannelPriceService;
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly IMapper _mapper;
    private readonly IOrderFieldInformationService _orderFieldInformationService;

    public CarProductOrderController(IOrderApiCaller orderApiCaller,
        ITenantApiCaller tenantApiCaller,
        IProductApiCaller productApiCaller,
        IBaseOrderService baseOrderService,
        IPaymentService paymentService,
        IAgencyChannelPriceService agencyChannelPriceService,
        IResourceApiCaller resourceApiCaller,
        IMapper mapper,
        IOrderFieldInformationService orderFieldInformationService)
    {
        _orderApiCaller = orderApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _productApiCaller = productApiCaller;
        _baseOrderService = baseOrderService;
        _paymentService = paymentService;
        _agencyChannelPriceService = agencyChannelPriceService;
        _resourceApiCaller = resourceApiCaller;
        _mapper = mapper;
        _orderFieldInformationService = orderFieldInformationService;
    }

    /// <summary>
    /// 订单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(DetailOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Detail(BaseOrderIdInput input)
    {
        var detail = await _orderApiCaller.CarProductOrderDetail(new GetDetailInput
        {
            BaseOrderId = input.BaseOrderId,
        });
        var output = _mapper.Map<DetailOutput>(detail);

        var baseOrder = output.BaseOrder;
        #region 关联人员信息处理

        var orderRelatedPersons =
            await _baseOrderService.GetRelatedPersonsInfo(new QueryOrderRelatedPersonsInfoBffInput
            {
                IsDataSecrecy = true,
                SellingPlatform = baseOrder.SellingPlatform,
                SalespersonId = baseOrder.SalespersonId,
                DevelopUserId = baseOrder.DevelopUserId,
                OperatorUserId = baseOrder.OperatorUserId,
                TrackingUserId = baseOrder.TrackingUserId,
                OperatorAssistantUserId = baseOrder.OperatorAssistantUserId,
                UserId = baseOrder.UserId,
                BaseOrderId = baseOrder.Id,
                IdType = SensitiveDataIdType.CarProduct
            });

        //填充关联人员名称
        baseOrder.SalespersonName = orderRelatedPersons.SalespersonName;
        baseOrder.DevelopUserName = orderRelatedPersons.DevelopUserName;
        baseOrder.OperatorUserName = orderRelatedPersons.OperatorUserName;
        baseOrder.OperatorAssistantUserName = orderRelatedPersons.OperatorAssistantUserName;
        baseOrder.TrackingUserName = orderRelatedPersons.TrackingUserName;
        baseOrder.UserNickName ??= orderRelatedPersons.UserNickName;

        //填充关联人员手机号码
        baseOrder.SalespersonPhoneNumber = orderRelatedPersons.SalespersonPhoneNumber;
        baseOrder.DevelopUserPhoneNumber = orderRelatedPersons.DevelopUserPhoneNumber;
        baseOrder.OperatorUserPhoneNumber = orderRelatedPersons.OperatorUserPhoneNumber;
        baseOrder.TrackingUserPhoneNumber = orderRelatedPersons.TrackingUserPhoneNumber;
        baseOrder.UserPhoneNumber = orderRelatedPersons.UserPhoneNumber;
        baseOrder.OperatorAssistantUserPhoneNumber = orderRelatedPersons.OperatorAssistantUserPhoneNumber;

        //处理联系人信息
        baseOrder.ContactsPhoneNumber = DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto()
        {
            Id = baseOrder.Id,
            IdType = SensitiveDataIdType.CarProduct,
            DataType = SensitiveDataType.OrderContactsPhone,
            Data = baseOrder.ContactsPhoneNumber,
        });
        baseOrder.ContactsEmail = DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto()
        {
            Id = baseOrder.Id,
            IdType = SensitiveDataIdType.CarProduct,
            DataType = SensitiveDataType.OrderContactsEmail,
            Data = baseOrder.ContactsEmail,
        });
        #endregion
        var carProductOrder = output.CarProductOrder;
        if (carProductOrder.CarProductType == CarProductType.AirportTransfer && carProductOrder.AirportId.HasValue)
        {
            var airport = await _resourceApiCaller.GetAirportDetail(carProductOrder.AirportId!.Value);
            if (airport is not null)
            {
                carProductOrder.Airport = new AirportOutput
                {
                    CountryCode = airport.CountryCode,
                    CountryName = airport.CountryName,
                    CityCode = airport.CityCode,
                    CityName = airport.CityName,
                    IATA = airport.IATA,
                    ICAO = airport.ICAO,
                    AirportZHName = airport?.AirportZHName,
                    AirportENName = airport?.AirportENName
                };
                switch (carProductOrder.AirportTransferType)
                {
                    case AirportTransferType.PickUp:
                        carProductOrder.DepartureAddress = $"{airport.CityName} {airport.AirportZHName} {airport.IATA} {airport.CountryName}";
                        break;
                    case AirportTransferType.DropOff:
                        carProductOrder.DestinationAddress = $"{airport.CityName} {airport.AirportZHName} {airport.IATA} {airport.CountryName}";
                        break;
                }
            }
        }
        //if (!string.IsNullOrWhiteSpace(carProductOrder.AlternatePhoneNumber))
        //{
        //    carProductOrder.AlternatePhoneNumber = DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto()
        //    {
        //        Id = baseOrder.Id,
        //        IdType = SensitiveDataIdType.CarProduct,
        //        DataType = SensitiveDataType.OrderContactsPhone,
        //        Data = carProductOrder.AlternatePhoneNumber,
        //    });
        //}
        carProductOrder.SupplierName = (await _tenantApiCaller.GetSupplierDetail(carProductOrder.SupplierId))?.FullName;
        output.OrderFields.ForEach(temp =>
        {
            temp.Fields.ForEach(x =>
            {
                x.FieldValue = _orderFieldInformationService.SensitiveData(new Services.FieldSensitiveDataDto(x, output.BaseOrder.Id,
                    SensitiveDataIdType.CarProduct, temp.TemplateType));
            });
            // 订单详情不需要信息提示
            temp.Fields = temp.Fields.Where(x => x.FieldType != FieldsType.Label).OrderBy(x => x.Sort).ToList();
            temp.Fields.ForEach(x =>
            {
                x.FieldValue ??= "";
            });
        });
        return Ok(output);
    }

    /// <summary>
    /// 确认
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Order.NoMatchingData, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Confirm(BaseOrderIdInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();

        await _orderApiCaller.CarProductOrderConfirm(new ConfirmOrderInput
        {
            BaseOrderId = input.BaseOrderId,
            OperationUser = new Contracts.Common.Order.DTOs.OperationUserDto
            {
                UserType = Contracts.Common.Order.Enums.UserType.Merchant,
                UserId = currentUser.UserId,
                Name = currentUser.NickName,
            }
        });
        return Ok();
    }

    /// <summary>
    /// 完成
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Order.NoMatchingData, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Finish(BaseOrderIdInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();

        await _orderApiCaller.CarProductOrderFinish(new FinishOrderInput
        {
            BaseOrderId = input.BaseOrderId,
            OperationUser = new Contracts.Common.Order.DTOs.OperationUserDto
            {
                UserType = Contracts.Common.Order.Enums.UserType.Merchant,
                UserId = currentUser.UserId,
                Name = currentUser.NickName,
            }
        });
        return Ok();
    }

    /// <summary>
    /// 退款
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Order.NoMatchingData, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Refund(Models.CarProductOrder.RefundInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();

        await _orderApiCaller.CarProductOrderRefund(new RefundOrderInput
        {
            BaseOrderId = input.BaseOrderId,
            Reason = input.Reason,
            Quantity = input.Quantity,
            RefundAmount = input.RefundAmount,
            OperationUser = new Contracts.Common.Order.DTOs.OperationUserDto
            {
                UserType = Contracts.Common.Order.Enums.UserType.Merchant,
                UserId = currentUser.UserId,
                Name = currentUser.NickName,
            }
        });
        return Ok();
    }

    /// <summary>
    /// 编辑用车信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> EditCarOrderUsingInfo(EditInfoInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        EditCarUsingInfoInput editCarUsingInfoInput = new()
        {
            BaseOrderId = input.BaseOrderId,
            OperationUser = new Contracts.Common.Order.DTOs.OperationUserDto
            {
                UserType = Contracts.Common.Order.Enums.UserType.Merchant,
                UserId = currentUser.UserId,
                Name = currentUser.NickName,
            },
            ContactsName = input.ContactsName,
            ContactsPhoneNumber = input.ContactsPhoneNumber,
            ContactsEmail = input.ContactsEmail,
            Passengers = input.Passengers,
            Baggages = input.Baggages,
            AirportTerminal = input.AirportTerminal,
            AlternatePhoneNumber = input.AlternatePhoneNumber,
            DepartureAddress = input.DepartureAddress,
            DestinationAddress = input.DestinationAddress,
            FlightTime = input.FlightTime,
            FlightNumber = input.FlightNumber,
            IsLandingVisa = input.IsLandingVisa,
            WeChatID = input.WeChatID,
        };
        await _orderApiCaller.EditCarOrderUsingInfo(editCarUsingInfoInput);
        return Ok();
    }

    /// <summary>
    /// 创建订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(CreateOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Product.ProductDisabled,
        ErrorTypes.Order.OverMaxPassengers,
        ErrorTypes.Order.OverMaxBaggages,
        ErrorTypes.Order.CarServiceItemMustOneBaggage,
        ErrorTypes.Order.CarServiceItemMustGreaterThanPassengers,
        ErrorTypes.Order.CarServiceItemMustGreaterThanBaggages,
        ErrorTypes.Order.TravelerInformationError,
        ErrorTypes.Order.OrderFieldChange,
        ErrorTypes.Order.OrderFieldValidater,
        ErrorTypes.Order.SyncFailOrderStatusChanged)]
    public async Task<IActionResult> Create(CreateInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var agencyId = input.AgencyId;
        var tenantId = currentUser.Tenant;
        var agency = await _tenantApiCaller.GetAgencyDetail(agencyId);
        string paymentCurrencyCode = agency.CurrencyCode;

        //用车信息
        var apiInput = new CarProductDetailCalendarPricesInput()
        {
            AirportTransferType = input.AirportTransferType,
            StartDate = input.TravelDate,
            EndDate = input.TravelDate,
            ProductId = input.CarProductId,
            SkuIds = new List<long> { input.CarProductSkuId },
            B2bSellingStatus = null,
        };
        var carProductDetail = await _productApiCaller.GetCarProductPriceDetails(apiInput);

        var product = carProductDetail.Product;
        if (product?.InSellingDate is not true)
            throw new BusinessException(ErrorTypes.Product.ProductDisabled);
        if (product.CarProductType != input.CarProductType)
            throw new BusinessException(ErrorTypes.Product.ProductDisabled);


        var sku = carProductDetail.Skus.FirstOrDefault(x => x.CarProductSkuId == input.CarProductSkuId);
        var skuPrice = sku?.CalendarPrices.FirstOrDefault(x => x.Date == input.TravelDate.Date);
        if (skuPrice?.IsSale is not true)
            throw new BusinessException(ErrorTypes.Product.ProductDisabled);

        var tempResult = await _productApiCaller.ProductInformationTemplateGetProductTempFieldsDetail(new GetProductTempFieldsDetailInput()
        {
            ProductId = input.CarProductId,
            ProductSkuId = input.CarProductSkuId,
            ProductType = ProductType.CarUsing,
        });
        ProductTemplateType? productTemplateType = null;
        switch (input.CarProductType)
        {
            case CarProductType.AirportTransfer:
                productTemplateType = input.AirportTransferType switch
                {
                    AirportTransferType.DropOff => ProductTemplateType.AirportTransferDropOff,
                    AirportTransferType.PickUp => ProductTemplateType.AirportTransferPickUp,
                    _ => null
                };
                break;
            case CarProductType.PointToPointTransfer:
                productTemplateType = ProductTemplateType.PointToPointTransfer;
                break;
            case CarProductType.CarChartered:
                productTemplateType = ProductTemplateType.CarChartered;
                break;
            default:
                throw new BusinessException(ErrorTypes.Order.TravelerInformationError);
                break;
        }
        var fieldCount = tempResult.Templates.FirstOrDefault(x => x.ProductTemplateType == productTemplateType)?.Fields?.Count() ?? 0;
        if (productTemplateType == null ||
                     (fieldCount > 0 && !input.OrderFields.Any(x => x.ProductTemplateType == productTemplateType))
                    )
            throw new BusinessException(ErrorTypes.Order.TravelerInformationError);


        // 验证模板参数
        _orderFieldInformationService.Validator(tempResult.Templates, input.OrderFields, true);

        // 新订单是没有订单确认信息的
        var orderSureTemp = tempResult.Templates.FirstOrDefault(x => x.TemplateType == TemplateType.Order);
        if (orderSureTemp != null)
        {
            input.OrderFields.Add(_mapper.Map<SaveOrderFieldInformationTypeDto>(orderSureTemp));
        }


        //币种汇率
        OrderPriceExchangeRateOutput priceExchangeRate = await _paymentService.GetOrderPriceExchange(new OrderPriceExchangeRateInput
        {
            CostCurrencyCode = product.CostCurrencyCode,
            SaleCurrencyCode = product.SaleCurrencyCode,
            PaymentCurrencyCode = paymentCurrencyCode
        });
        var channelPrice = input.CarItems.First(x => x.ItemId == sku.CarProductSkuId).Price;
        OrderMultPriceDto orderMultPrice = new()
        {
            SaleCurrencyCode = product.SaleCurrencyCode,
            OrgPrice = channelPrice,
            CostCurrencyCode = product.CostCurrencyCode,
            OrgCostPrice = skuPrice.CostPrice!.Value,
            CostPrice = skuPrice.CostPrice.Value,
            PaymentCurrencyCode = paymentCurrencyCode,
            Price = Math.Round(channelPrice * priceExchangeRate.ExchangeRate, 2),
            Quantity = input.Quantity,
            CostExchangeRate = priceExchangeRate.CostExchangeRate,
            ExchangeRate = priceExchangeRate.ExchangeRate,
        };
        CarProductInfoDto carProductInfo = new()
        {
            CarProductId = product.Id,
            CarProductType = product.CarProductType,
            PurchaseSourceType = product.PurchaseSourceType,
            Title = product.Title,
            EnTitle = product.Title,
            Instructions = product.Instructions,
            FeeInclude = product.FeeInclude,
            FeeExclude = product.FeeExclude,
            CancelType = product.CancelType,
            CancelRule = product.CancelRule,
            SupplierId = product.SupplierId,
            AirportId = product.AirportId,
            AirportName = product.AirportName,
            AirportTransferType = input.AirportTransferType ?? sku.AirportTransferType,
            CarProductSkuId = sku.CarProductSkuId,
            Name = sku.Name,
            CarTypeGradeId = sku.CarTypeGradeId,
            GradeName = sku.GradeName,

            OrderMultPrice = orderMultPrice,
            AutoConfirm = product.AutoConfirm,
            MinProfit = skuPrice.MinProfit,
        };
        var travelCommon = input.OrderFields.FirstOrDefault(x => x.TemplateType == TemplateType.Travel);
        // 乘客数
        var passengersStr = travelCommon?.Fields?.FirstOrDefault(x => x.FieldCode == "Passengers")?.FieldValue ?? "0";

        // 行李数
        var baggagesStr = travelCommon?.Fields?.FirstOrDefault(x => x.FieldCode == "Baggages")?.FieldValue ?? "0";
        int passengers = 0, baggages = 0;
        int.TryParse(passengersStr, out passengers);
        int.TryParse(baggagesStr, out baggages);
        input.Passengers = passengers;
        input.Baggages = baggages;
        List<CarServiceItemDto> carServiceItems = new();
        // CarItems 手工单是因为把用车产品本身当作默认的第一条数据了
        if (input.CarItems?.Length is > 1)
        {
            foreach (var item in skuPrice.ServiceItems)
            {
                if (item.Enabled is not true)
                    continue;
                var carServiceItem = input.CarItems.FirstOrDefault(x => x.ItemId == item.CarServiceItemId);
                switch (item.ServiceOption)
                {
                    case CarProductSkuServiceOption.None:
                        if (carServiceItem is null)
                            continue;
                        break;
                    case CarProductSkuServiceOption.MustOneBaggage:
                        if (carServiceItem?.Quantity is not > 1)
                            throw new BusinessException(ErrorTypes.Order.CarServiceItemMustOneBaggage);
                        break;
                    case CarProductSkuServiceOption.MustGreaterThanPassengers:
                        if (carServiceItem is null || carServiceItem.Quantity < passengers)
                            throw new BusinessException(ErrorTypes.Order.CarServiceItemMustGreaterThanPassengers);
                        break;
                    case CarProductSkuServiceOption.MustGreaterThanBaggages:
                        if (carServiceItem is null || carServiceItem.Quantity < baggages)
                            throw new BusinessException(ErrorTypes.Order.CarServiceItemMustGreaterThanBaggages);
                        break;
                }

                CarServiceItemDto itemDto = new()
                {
                    CarServiceItemId = item.CarServiceItemId,
                    Description = item.Description,
                    IsNeedCharge = item.IsNeedCharge,
                    ItemName = item.ItemName,
                    Quantity = carServiceItem.Quantity,
                    SupplierId = product.SupplierId,
                };
                if (item.IsNeedCharge)
                {
                    var priceAndQuantityInfo = item.ServicePriceAndQuantityInfos.FirstOrDefault();
                    if (priceAndQuantityInfo.Enabled is not true) continue;
                    var price = carServiceItem.Price;
                    itemDto.OrderMultPrice = new OrderMultPriceDto
                    {
                        SaleCurrencyCode = product.SaleCurrencyCode,
                        OrgPrice = price,
                        CostCurrencyCode = product.CostCurrencyCode,
                        OrgCostPrice = priceAndQuantityInfo.CostPrice!.Value,
                        CostPrice = carServiceItem.CostPrice,
                        PaymentCurrencyCode = paymentCurrencyCode,
                        Price = Math.Round(price * priceExchangeRate.ExchangeRate, 2),
                        Quantity = carServiceItem.Quantity,
                        CostExchangeRate = priceExchangeRate.CostExchangeRate,
                        ExchangeRate = priceExchangeRate.ExchangeRate,
                    };
                }
                carServiceItems.Add(itemDto);
            }
        }
        var departureAddress = input.DepartureAddress;
        if (string.IsNullOrWhiteSpace(departureAddress) && carProductInfo.AirportTransferType == AirportTransferType.PickUp)
        {
            departureAddress = carProductInfo.AirportName;
        }
        var destinationAddress = input.DestinationAddress;
        if (string.IsNullOrWhiteSpace(destinationAddress) && carProductInfo.AirportTransferType == AirportTransferType.DropOff)
        {
            destinationAddress = carProductInfo.AirportName;
        }
        CarTravelInfoDto carTravelInfo = new()
        {
            Quantity = input.Quantity,
            Passengers = input.Passengers,
            Baggages = input.Baggages,
            DepartureCityCode = product.CityCode,
            DepartureCityName = product.CityName,
            DepartureAddress = departureAddress,
            DestinationCityCode = product.CityCode,
            DestinationCityName = product.CityName,
            DestinationAddress = destinationAddress,
            TravelDate = input.TravelDate,
            AirportId = product.AirportId,
            //FlightNumber = input.FlightNumber,
            //FlightTime = input.FlightTime,
            //AirportTerminal = input.AirportTerminal,
            //IsLandingVisa = input.IsLandingVisa,
            //AlternatePhoneNumber = input.AlternatePhoneNumber,
            //WeChatID = input.WeChatID,
        };
        //订单金额
        decimal totalAmount = carProductInfo.OrderMultPrice.Price * carProductInfo.OrderMultPrice.Quantity;
        carServiceItems.ForEach(i => { totalAmount += i.Quantity * i.OrderMultPrice?.Price ?? 0; });
        decimal discountAmount = input.DiscountAmount ?? 0;
        decimal paymentAmount = totalAmount - discountAmount;

        //处理失败异常订单创建
        var sellingPlatform = SellingPlatform.System;
        if (input.SyncFailOrderId.HasValue)
        {
            var syncFailOrder =
                await _orderApiCaller.GetOpenChannelSyncFailOrderDetail(new GetOpenChannelSyncFailOrderInput
                {
                    SyncFailOrderId = input.SyncFailOrderId.Value
                });

            //异常单状态判断
            if (syncFailOrder.SyncStatus != OpenChannelFailOrderSyncStatus.SyncFail)
            {
                throw new BusinessException(ErrorTypes.Order.SyncFailOrderStatusChanged);
            }

            sellingPlatform = syncFailOrder.SellingPlatform;
            input.SellingChannel = syncFailOrder.SellingChannels;
        }

        //判断供应商api类型
        OpenSupplierType openSupplierType = OpenSupplierType.System;
        if (product.SupplierId > 0)
        {
            var supplierApiSettingInfo = (await _tenantApiCaller
                    .GetSupplierApiSettingInfos(new GetApiSettingInfosInput
                    {
                        SupplierIds = new[]
                        {
                            product.SupplierId
                        },
                        TenantId = tenantId
                    }))
                .FirstOrDefault();

            openSupplierType = supplierApiSettingInfo is null
                ? OpenSupplierType.System
                : supplierApiSettingInfo.SupplierApiType switch
                {
                    SupplierApiType.Mozio => OpenSupplierType.Mozio,
                    _ => OpenSupplierType.System
                };
        }

        CreateCarProductOrderInput createCarProductOrderInput = new()
        {
            SyncFailOrderId = input.SyncFailOrderId,
            TenantId = currentUser.Tenant,
            SellingChannel = input.SellingChannel,
            SellingPlatform = sellingPlatform,
            ChannelOrderNo = input.ChannelOrderNos?.Length is > 0 ? string.Join(',', input.ChannelOrderNos) : string.Empty,
            SupplierOrderId = input.SupplierOrderId,
            ContactsName = input.ContactsName,
            ContactsPhoneNumber = input.ContactsPhoneNumber,
            ContactsEmail = input.ContactsEmail,
            OrderUserInfo = new OrderUserInfo
            {
                AgencyId = agencyId,
                AgencyName = agency.FullName,
                UserType = UserType.Merchant,
                UserId = currentUser.UserId,
                NickName = currentUser.NickName,
                SalespersonId = agency.SalespersonId,
                SalespersonName = agency.SalespersonName,
                VipLevelId = agency.Level ?? 0,
                VipLevelName = agency.LevelName ?? string.Empty,
            },
            TotalAmount = totalAmount,
            DiscountAmount = discountAmount,
            PaymentAmount = paymentAmount,
            PaymentCurrencyCode = paymentCurrencyCode,
            CarProduct = carProductInfo,
            CarServiceItems = carServiceItems,
            Message = input.Message,
            Remark = input.Remark,
            SupplierOrderRemark = input.SupplierOrderRemark,
            CarTravelInfo = carTravelInfo,
            DevelopUserId = product.DevelopUserId,
            //OperatorUserId = product.ProductOperatorUser?.FirstOrDefault(x => x.SellingPlatform == SellingPlatform.System)?.OperatorUserId,
            OrderFields = input.OrderFields,
            OpenSupplierType = openSupplierType
        };

        var platform = createCarProductOrderInput.SellingPlatform;
        // 手工单下单时按照所选“来源渠道”给订单赋值运营人
        if (createCarProductOrderInput.SellingPlatform == SellingPlatform.System)
        {
            platform = createCarProductOrderInput.SellingChannel switch
            {
                SellingChannels.Ctrip => SellingPlatform.Ctrip,
                SellingChannels.Meituan => SellingPlatform.Meituan,
                SellingChannels.Fliggy => SellingPlatform.Fliggy,
                SellingChannels.B2b => SellingPlatform.B2BWeb,
                _ => SellingPlatform.System,
            };
        }
        var productOperatorUser = product.ProductOperatorUser?.FirstOrDefault(x => x.SellingPlatform == platform);
        createCarProductOrderInput.OperatorUserId = productOperatorUser?.OperatorUserId;
        createCarProductOrderInput.OperatorAssistantUserId = productOperatorUser?.OperatorAssistantUserId;

        var result = await _orderApiCaller.CarProductOrderCreate(createCarProductOrderInput);

        CreateOutput output = new() { BaseOrderId = result.BaseOrderId };
        return Ok(output);
    }

    /// <summary>
    /// 通过渠道订单号查询渠道订单数据
    /// </summary>
    /// <param name="channelOrderNo"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200, typeof(GetCarProductChannelOrderInfoBffOutput))]
    public async Task<IActionResult> GetChannelOrderInfo(string channelOrderNo)
    {
        var channelOrderInfoResponse = await _orderApiCaller.GetCarProductChannelOrderInfo(channelOrderNo);
        if (channelOrderInfoResponse == null)
        {
            //查询同步失败的订单数据
            var syncFailOrder = await _orderApiCaller.GetOpenChannelSyncFailOrderDetail(new GetOpenChannelSyncFailOrderInput
            {
                ChannelOrderNo = channelOrderNo,
            });

            if (syncFailOrder != null)
            {
                channelOrderInfoResponse = new GetCarProductChannelOrderInfoOutput
                {
                    ChannelOrderNo = channelOrderNo,
                    PaymentAmount = syncFailOrder.PaymentAmount,
                    ContactsEmail = syncFailOrder.ContactsEmail,
                    ContactsName = syncFailOrder.ContactsName,
                    ContactsPhoneNumber = syncFailOrder.ContactsPhoneNumber
                };

                //处理dataJson
                var carProductSyncJsonData = DataContentJsonConvert(syncFailOrder.DataContentJson);
                carProductSyncJsonData.CarProductSkuId ??= syncFailOrder.SkuId;
                channelOrderInfoResponse.DiscountAmount = carProductSyncJsonData.DiscountFee / 100;
                channelOrderInfoResponse.OrderFields = await GetFailOrderFields(carProductSyncJsonData);
                var productPrice = carProductSyncJsonData.Price / 100;
                channelOrderInfoResponse.OrderPrice = new CarChannelOrderPriceOutput
                {
                    Price = productPrice,
                    Quantity = carProductSyncJsonData.Quantity
                };
            }
        }
        //字段模板 中文转拼音
        await _baseOrderService.ConvertChineseNameToPinyin(channelOrderInfoResponse.OrderFields);
        var result = _mapper.Map<GetCarProductChannelOrderInfoBffOutput>(channelOrderInfoResponse);
        return Ok(result);
    }

    /// <summary>
    /// 查询用车同步失败异常订单详情
    /// </summary>
    /// <param name="syncFailOrderId"></param>
    /// <returns></returns>
    [Authorize]
    [HttpGet]
    [SwaggerResponseExt(200, typeof(GetSyncFailCarProductOrderDetailBffOutput))]
    public async Task<IActionResult> GetOpenChannelSyncFailOrderDetail(long syncFailOrderId)
    {
        var request = new GetOpenChannelSyncFailOrderInput
        {
            SyncFailOrderId = syncFailOrderId,
            OrderType = OrderType.CarProduct
        };
        var response = await _orderApiCaller.GetOpenChannelSyncFailOrderDetail(request);
        var result = _mapper.Map<GetSyncFailCarProductOrderDetailBffOutput>(response);
        if (result == null)
        {
            return Ok(result);
        }

        //处理供应商信息
        if (result.SupplierId.HasValue)
        {
            var supplierInfo = (await _tenantApiCaller.GetSupplierShortInfo(new ShortInfoInput
            {
                SupplierIds = new List<long> { result.SupplierId.Value },
                IsEnabled = false
            }))
                .FirstOrDefault();
            result.SupplierName = supplierInfo?.SupplierFullName;
        }

        //处理分销商信息
        if (result.AgencyId.HasValue)
        {
            var agencyInfo = await _tenantApiCaller.GetAgencyDetail(result.AgencyId.Value);
            result.AgencyName = agencyInfo?.FullName;
        }

        //处理dataJson
        var carProductSyncJsonData = DataContentJsonConvert(response.DataContentJson);
        result.OrderFields = await GetFailOrderFields(carProductSyncJsonData);
        var productPrice = carProductSyncJsonData.Price / 100;
        var discountFee = carProductSyncJsonData.DiscountFee / 100;
        result.DiscountAmount = discountFee;
        result.OrderPrice = new CarChannelOrderPriceBffOutput
        {
            Price = productPrice,
            Quantity = carProductSyncJsonData.Quantity,
        };
        //字段模板 中文转拼音
        await _baseOrderService.ConvertChineseNameToPinyin(result.OrderFields);
        return Ok(result);
    }

    /// <summary>
    /// 开放平台- 供应商询价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt(200)]
    public async Task<IActionResult> OpenSupplierRetryQuote(CarSupplierOrderRetryQuoteBffInput input)
    {
        throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var request = new CarProductSupplierOrderQuoteInput
        {
            BaseOrderId = input.BaseOrderId,
            TenantId = currentUser.Tenant
        };
        await _orderApiCaller.CarSupplierOrderRetryQuote(request);
        return Ok();
    }

    /// <summary>
    /// 开放平台 - 供应商订单取消预定
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt(200, typeof(CarSupplierOrderCancelBffOutput))]
    public async Task<IActionResult> OpenSupplierCancel(CarSupplierOrderCancelBffInput input)
    {
        var result = new CarSupplierOrderCancelBffOutput();
        var request = new CancelCarProductSupplierOrderInput
        {
            BaseOrderId = input.BaseOrderId,
        };
        var response = await _orderApiCaller.CarSupplierOrderCancel(request);
        result.IsSuccess = response.IsSuccess;
        result.ResultMessage = response.ResultMessage;
        return Ok(result);
    }

    /// <summary>
    /// 开放平台 - 机场/地址模糊搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt(200, typeof(List<CarSupplierOrderFuzzySearchBffOutput>))]
    public async Task<IActionResult> OpenSupplierFuzzySearch(CarSupplierOrderFuzzySearchBffInput input)
    {
        //目前仅支持mozio
        if (input.SupplierApiType != SupplierApiType.Mozio)
            return Ok(new List<CarSupplierOrderFuzzySearchBffOutput>());

        var request = new CarSupplierOrderFuzzySearchInput
        {
            SupplierType = input.SupplierApiType.ToString().ToLowerInvariant(),
            Address = input.Address,
        };
        var response = await _orderApiCaller.CarSupplierOrderFuzzySearch(request);
        var result = _mapper.Map<List<CarSupplierOrderFuzzySearchBffOutput>>(response);
        return Ok(result);
    }

    private async Task<List<OrderFieldInformationTypeOutput>> GetFailOrderFields(CreateCarProductOtaOrderBffInput carProductSyncJsonData)
    {
        List<OrderFieldInformationTypeOutput> orderFields = new List<OrderFieldInformationTypeOutput>();

        long productId = 0, productSkuId = 0;
        AirportTransferType? productAirportTransferType = null;
        if (carProductSyncJsonData.CarProductSkuId.HasValue)
        {
            var carProduct = (await _productApiCaller.GetCarProductSkDetails(new CarProductSkuInput
            {
                CarProductSkuIds = new[] { carProductSyncJsonData.CarProductSkuId!.Value }
            })).FirstOrDefault();
            productId = carProduct?.CarProductId ?? 0;
            productSkuId = carProduct?.Id ?? 0;
            productAirportTransferType = carProduct?.AirportTransferType;
        }

        // 产品不存在时使用默认模板，把字段显示
        var tempResult = await _productApiCaller.ProductInformationTemplateGetProductTempFieldsDetail(new GetProductTempFieldsDetailInput()
        {
            ProductId = productId,
            ProductSkuId = productSkuId,
            ProductType = ProductType.CarUsing
        });
        var templates = tempResult.Templates;

        var channelNewOrderContact = _mapper.Map<TravelLineOTAOrderContactBffItem>(carProductSyncJsonData.ContactItem);
        if (channelNewOrderContact == null)
        {
            channelNewOrderContact = new TravelLineOTAOrderContactBffItem
            {
                Name = carProductSyncJsonData.ContactsName,
                Email = carProductSyncJsonData.ContactsEmail,
                Mobile = carProductSyncJsonData.ContactsPhoneNumber,
            };
        }
        Contracts.Common.Order.DTOs.TravelLineOrder.CreateDto dto = new Contracts.Common.Order.DTOs.TravelLineOrder.CreateDto()
        {
            ContactsName = carProductSyncJsonData.ContactsName,
            ContactsEmail = carProductSyncJsonData.ContactsEmail,
            ContactsPhoneNumber = carProductSyncJsonData.ContactsPhoneNumber,
            SellingPlatform = carProductSyncJsonData.SellingPlatform,
            OrderTravelers = new List<OrderTravelerInput>()
            // SellingChannel = input.SellingChannel
        };
        foreach (var item in carProductSyncJsonData.TravelerInfos)
        {
            var info = new OrderTravelerInput()
            {
                Birthday = item.Birthday,
                CardValidDate = item.CardValidDate,
                Email = item.Email,
                ExtendInfo = item.ExtendInfo,
                FirstName = item.FirstName,
                Gender = item.Gender,
                Height = item.Height,
                IDCard = item.IDCard,
                IdCardType = item.IdCardType,
                LastName = item.LastName,
                Name = item.Name,
                NationalityCode = item.NationalityCode,
                NationalityName = item.NationalityName,
                Phone = item.PhoneNumber,
                ShoeSize = item.ShoeSize,
                Weight = item.Weight,
            };
            dto.OrderTravelers.Add(info);
        }
        var carProductType = carProductSyncJsonData.CarProductType;
        if (carProductSyncJsonData.CarProductType == null)
        {
            var product = await _productApiCaller.CarProductDetail(productId);
            carProductType = product?.CarProductType;
        }
        var airportTransferType = carProductSyncJsonData.AirportTransferType;
        if (carProductType == CarProductType.AirportTransfer && carProductSyncJsonData.AirportTransferType == null)
        {
            airportTransferType = productAirportTransferType;
        }
        var saveFields = await _orderFieldInformationService.ConvertTravelOrderData(dto, channelNewOrderContact, templates,
                                       OrderType.CarProduct, carProductType, airportTransferType);
        orderFields = _mapper.Map<List<OrderFieldInformationTypeOutput>>(saveFields);

        orderFields.ForEach(temp =>
        {
            // 订单详情不需要信息提示
            temp.Fields = temp.Fields.Where(x => x.FieldType != FieldsType.Label).OrderBy(x => x.Sort).ToList();
            temp.Fields.ForEach(x =>
            {
                x.FieldValue ??= "";
            });
        });
        return orderFields;
    }

    /// <summary>
    /// 处理不同订单类型转换后的差异数据
    /// </summary>
    /// <param name="scenicTicketSyncJsonData"></param>
    private CreateCarProductOtaOrderBffInput DataContentJsonConvert(string dataContentJson)
    {
        var carProductSyncJsonData = JsonConvert.DeserializeObject<CreateCarProductOtaOrderBffInput>(dataContentJson);
        if (carProductSyncJsonData.TravelerInfos.Any() is false)
        {//处理订单类型变动,导致的字段不一致
            var dynamicData = JsonConvert.DeserializeObject<JObject>(dataContentJson);
            if (dynamicData.TryGetValue("TravelerItems", StringComparison.OrdinalIgnoreCase, out var travelerInfosToken))
            {
                carProductSyncJsonData.TravelerInfos = travelerInfosToken.ToObject<List<CarProductOTAOrderTravelerBffItem>>();
            }
        }
        if (carProductSyncJsonData.OrderPriceItems.Any())
        {
            //处理订单类型变动.价格兼容
            carProductSyncJsonData.Price = carProductSyncJsonData.OrderPriceItems.Sum(x => x.Price);
            carProductSyncJsonData.Quantity = carProductSyncJsonData.OrderPriceItems.Sum(x => x.Quantity);
        }

        return carProductSyncJsonData;
    }
}
