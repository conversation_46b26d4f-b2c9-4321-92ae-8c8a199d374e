using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.Common;
using Bff.Vebk.Models.OpenChannelOrder;
using Bff.Vebk.Models.OpenChannelSyncFailOrder;
using Bff.Vebk.Models.OpenSupplierOrder;
using Bff.Vebk.Models.Order;
using Bff.Vebk.Models.ScenicTicketOrder;
using Bff.Vebk.Models.ScenicTicketOrder.OTA;
using Bff.Vebk.Models.ScenicTicketPurchaseOrder;
using Bff.Vebk.Models.TravelLineOrder.OTA;
using Bff.Vebk.Services.Interfaces;
using Common.Jwt;
using Common.Swagger;
using Common.Utils;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.OpenChannelOrder;
using Contracts.Common.Order.DTOs.OpenChannelSyncFailOrder;
using Contracts.Common.Order.DTOs.OrderFieldInformation;
using Contracts.Common.Order.DTOs.OrderManualVoucher;
using Contracts.Common.Order.DTOs.ScenicTicketOrder;
using Contracts.Common.Order.DTOs.ScenicTicketSupplierOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Product.DTOs.ProductInformationTemplate;
using Contracts.Common.Product.Enums;
using Contracts.Common.Resource.DTOs.FileResource;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Scenic.DTOs.TicketsCalendarPrice;
using Contracts.Common.Scenic.Enums;
using Contracts.Common.Tenant.DTOs.Supplier;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text.RegularExpressions;
using UserType = Contracts.Common.Order.Enums.UserType;

namespace Bff.Vebk.Controllers;

/// <summary>
/// 门票订单信息
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class ScenicTicketOrderController : ControllerBase
{
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IScenicSpotApiCaller _scenicSpotApiCaller;
    private readonly IMapper _mapper;

    private readonly IBaseOrderService _baseOrderService;
    private readonly IProductApiCaller _productApiCaller;
    private readonly IPaymentService _paymentService;
    private readonly IOpenSupplierOrderService _openSupplierOrderService;
    private readonly IOrderFieldInformationService _orderFieldInformationService;
    private readonly IOpenChannelOrderService _openChannelOrderService;
    private readonly IOpenPlatformService _openPlatformService;
    private readonly IOpenChannelSyncFailOrderService _openChannelSyncFailOrderService;

    public ScenicTicketOrderController(
        IOrderApiCaller orderApiCaller,
        IResourceApiCaller resourceApiCaller,
        ITenantApiCaller tenantApiCaller,
        IScenicSpotApiCaller scenicSpotApiCaller,
        IMapper mapper,
        IBaseOrderService baseOrderService,
        IProductApiCaller productApiCaller,
        IPaymentService paymentService,
        IOrderFieldInformationService orderFieldInformationService,
        IOpenSupplierOrderService openSupplierOrderService,
        IOpenChannelOrderService openChannelOrderService,
        IOpenPlatformService openPlatformService,
        IOpenChannelSyncFailOrderService openChannelSyncFailOrderService)
    {
        _orderApiCaller = orderApiCaller;
        _resourceApiCaller = resourceApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _scenicSpotApiCaller = scenicSpotApiCaller;
        _mapper = mapper;
        _baseOrderService = baseOrderService;
        _productApiCaller = productApiCaller;
        _paymentService = paymentService;
        _orderFieldInformationService = orderFieldInformationService;
        _openSupplierOrderService = openSupplierOrderService;
        _openChannelOrderService = openChannelOrderService;
        _openPlatformService = openPlatformService;
        _openChannelSyncFailOrderService = openChannelSyncFailOrderService;
    }

    /// <summary>
    /// 门票订单-分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(PagingModel<SearchScenicTicketOrderBffOutput, ScenicTicketOrderStatusCountBffOutput>))]
    public async Task<IActionResult> Search(SearchScenicTicketOrderBffInput input)
    {
        var result = new PagingModel<SearchScenicTicketOrderBffOutput, ScenicTicketOrderStatusCountBffOutput>
        {
            PageIndex = input.PageIndex,
            PageSize = input.PageSize
        };

        //正常门票分页数据
        var searchScenicTicketOrderRequest = _mapper.Map<SearchScenicTicketOrderInput>(input);

        //渠道失败异常订单
        var syncFailOrderRequest = new SearchOpenChannelSyncFailOrderInput
        {
            PageIndex = input.PageIndex,
            PageSize = input.PageSize,
            Id = input.BaseOrderId,
            OrderTypes = new List<OrderType> { OrderType.ScenicTicket },
            ChannelOrderNo = input.ChannelOrderNo,
            SellingChannels = input.SellingChannels,
            SellingPlatform = input.SellingPlatform,
            SyncStatus = new List<OpenChannelFailOrderSyncStatus> { OpenChannelFailOrderSyncStatus.SyncFail },
            TravelDateBegin = input.ValidityBegin,
            TravelDateEnd = input.ValidityEnd,
            CreateDateBegin = input.DateBegin,
            CreateDateEnd = input.DateEnd,
            AgencyId = input.AgencyId,
            SupplierId = input.SupplierId,
            ProductName = input.ResourceName,
            SkuName = input.ProductName,
            SkuId = input.ProductId,
            ContactsName = input.ContactsName,
            ContactsPhoneNumber = input.ContactsPhoneNumber
        };

        if (input.Status == ScenicTicketOrderMixStatus.ChannelAbnormal)
        {
            //异常订单没有采购单号,所以有值的时候不查询异常订单数据
            if (string.IsNullOrEmpty(input.SupplierOrderId))
            {
                //渠道异常订单分页数据
                var syncFailOrderSearchResponse =
                    await _orderApiCaller.SearchOpenChannelSyncFailOrder(syncFailOrderRequest);
                result.PageIndex = syncFailOrderSearchResponse.PageIndex;
                result.PageSize = syncFailOrderSearchResponse.PageSize;
                result.Total = syncFailOrderSearchResponse.Total;
                result.Data = new List<SearchScenicTicketOrderBffOutput>();
                result.Supplement = new ScenicTicketOrderStatusCountBffOutput();
                var dataList = new List<SearchScenicTicketOrderBffOutput>();
                foreach (var item in syncFailOrderSearchResponse.Data)
                {
                    //处理渠道同步订单数据
                    var syncOrderData =
                        JsonConvert.DeserializeObject<CreateScenicOTAOrderBffInput>(item.DataContentJson);
                    var productPrice = syncOrderData.Price / 100;
                    var quantity = syncOrderData.Quantity;
                    var discountFee = syncOrderData.DiscountFee / 100;

                    var resultDataItem = new SearchScenicTicketOrderBffOutput
                    {
                        BaseOrderId = item.Id,
                        ResourceName = item.ProductName,
                        ProductName = item.SkuName,
                        ContactsName = item.ContactsName,
                        ContactsPhoneNumber = item.ContactsPhoneNumber,
                        ContactsEmail = item.ContactsEmail,
                        SellingPlatform = item.SellingPlatform,
                        SellingChannels = item.SellingChannels,
                        CreateTime = item.CreateTime,
                        PaymentAmount = item.PaymentAmount,
                        DiscountAmount = discountFee,
                        TotalAmount = discountFee + item.PaymentAmount,
                        PaymentCurrencyCode = item.CurrencyCode,
                        ChannelOrderNo = item.ChannelOrderNo,
                        SupplierId = item.SupplierId,
                        ValidityBegin = item.TravelDate,
                        ValidityEnd = item.TravelDate,
                        OpenChannelSyncReason = item.Reason,
                        Status = ScenicTicketOrderMixStatus.ChannelAbnormal
                    };

                    dataList.Add(resultDataItem);
                }

                result.Data = dataList;
            }

            //门票订单的状态数量统计
            var scenicTicketOrderStatusCountResponse =
                await _orderApiCaller.SearchScenicTicketOrderStatusCount(searchScenicTicketOrderRequest);
            result.Supplement = _mapper.Map<ScenicTicketOrderStatusCountBffOutput>(scenicTicketOrderStatusCountResponse);
        }
        else
        {
            var searchResponse = await _orderApiCaller.SearchScenicTicketOrder(searchScenicTicketOrderRequest);
            result =
                _mapper.Map<PagingModel<SearchScenicTicketOrderBffOutput, ScenicTicketOrderStatusCountBffOutput>>(
                    searchResponse);
        }

        //补充异常订单状态统计数据
        //异常订单没有采购单号,所以有值的时候不查询异常订单数据
        if (string.IsNullOrEmpty(input.SupplierOrderId))
        {
            var syncFailOrderCountResponse =
                await _orderApiCaller.SearchOpenChannelSyncFailOrderStatusCount(syncFailOrderRequest);
            result.Supplement.OpenChannelSyncFailOrderCount = syncFailOrderCountResponse
                .FirstOrDefault(x => x.OrderType == OrderType.ScenicTicket)?.SyncFailCount ?? 0;

        }
        return Ok(result);
    }


    /// <summary>
    /// 订单重新发码,支付,接单
    /// </summary>
    /// <remarks>
    /// <para>1.API对接 - 供应端&渠道端 重新支付/发货处理</para>
    /// <para>2.采购导入 - 手动发货</para>
    /// <para>3.手工发货 - 手动发货</para>
    /// </remarks>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(AggregateDeliveryBffOutput))]
    [SwaggerResponseExt(default, ErrorTypes.Order.PurchaseVoucherDelivered,
        ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> AggregateDelivery(AggregateDeliveryBffInput input)
    {
        var result = new AggregateDeliveryBffOutput();
        var tenantId = HttpContext.GetTenantId();
        var user = HttpContext.User.ParseUserInfo<CurrentUser>();
        switch (input.CredentialSourceType)
        {
            case CredentialSourceType.PurchasingImport:

                //采购导入
                switch (input.AggregateDeliveryType)
                {
                    case AggregateDeliveryType.SupplierDelivery:

                        var purchaseResponse = await _orderApiCaller.RetryOrderDelivery(new ScenicTicketOrderDeliveryInput
                        {
                            BaseOrderId = input.BaseOrderId,
                            TenantId = tenantId
                        });

                        result.DeliverStatus = purchaseResponse.DeliverStatus;
                        result.DeliverySuccess = purchaseResponse.DeliverStatus == OrderVoucherDeliverStatus.Success;

                        break;
                    case AggregateDeliveryType.ChannelDelivery:

                        var scenicTicketOrderDetail = await _orderApiCaller.ScenicTicketOrderDetail(new ScenicTicketOrderDetailInput
                        {
                            BaseOrderId = input.BaseOrderId
                        });
                        if (scenicTicketOrderDetail.BaseOrderInfo?.SellingPlatform == SellingPlatform.TikTok
                            && scenicTicketOrderDetail.DeliveryResult?.DeliverStatus ==
                            ScenicTicketOrderDeliveryStatus.SyncFailed)
                        {
                            var purchaseChannelDeliveryResponse = await _orderApiCaller.ScenicTicketVoucherManualDelivery(new ManualDeliveryScenicTicketVoucherInput
                            {
                                BaseOrderId = input.BaseOrderId,
                                UserId = user.UserId,
                                VoucherSourceType = ScenicTicketVoucherSourceType.ManualUpload
                            });
                            result.DeliverySuccess = purchaseChannelDeliveryResponse.DeliverySuccess;
                            result.DeliverStatus = purchaseChannelDeliveryResponse.DeliverStatus;
                        }

                        break;
                }

                break;
            case CredentialSourceType.InterfaceDock:

                switch (input.AggregateDeliveryType)
                {
                    case AggregateDeliveryType.SupplierDelivery:

                        //查询门票详情
                        var searchResponse =
                            await _orderApiCaller.GetScenicTicketOpenSupplierOrderDetail(
                                new GetSupplierOrderDetailInput { BaseOrderId = input.BaseOrderId });

                        var operateIsSuccess = false;
                        if (searchResponse is { IsOldKlook: false }) //旧客路不需要再维护了
                        {
                            switch (searchResponse.OrderStatus)
                            {
                                case ScenicTicketSupplierOrderStatus.WaitingForPay:
                                    //供应端订单未支付.走支付流程
                                    var payResponse =
                                        await _orderApiCaller.RetryPayOpenSupplierOrder(
                                            new SupplierOrderRetryPayInput
                                            {
                                                BaseOrderId = input.BaseOrderId,
                                                OrderType = OrderType.ScenicTicket
                                            });
                                    operateIsSuccess = payResponse.IsSuccess;
                                    break;
                                case ScenicTicketSupplierOrderStatus.WaitingForDeliver:
                                case ScenicTicketSupplierOrderStatus.DeliveryFail:
                                    //重新发货流程

                                    var manualDeliveryResult = await _openSupplierOrderService.DeliveryRetry(
                                        input: new DeliveryRetrySupplierOrderBffInputDto
                                        {
                                            BaseOrderId = input.BaseOrderId,
                                            OrderType = OrderType.ScenicTicket,
                                            TenantId = tenantId
                                        });

                                    operateIsSuccess = manualDeliveryResult.IsSuccess;

                                    break;
                            }
                        }

                        result.DeliverStatus = operateIsSuccess
                            ? OrderVoucherDeliverStatus.Success
                            : OrderVoucherDeliverStatus.SyncFailed;

                        result.DeliverySuccess = operateIsSuccess;

                        break;
                    case AggregateDeliveryType.ChannelDelivery:

                        var scenicTicketOrderDetail = await _orderApiCaller.ScenicTicketOrderDetail(new ScenicTicketOrderDetailInput
                        {
                            BaseOrderId = input.BaseOrderId
                        });
                        if (scenicTicketOrderDetail.DistributionChannelInfo?.AgencyApiType == AgencyApiType.TikTokGroupPurchaseReservation
                            && scenicTicketOrderDetail.DeliveryResult?.DeliverStatus ==
                            ScenicTicketOrderDeliveryStatus.SyncFailed)
                        {
                            //目前只有抖音分销渠道支持.需要重试接单(同步失败)
                            var retryDeliveryResponse = await _orderApiCaller.ScenicTicketVoucherManualDelivery(
                                new ManualDeliveryScenicTicketVoucherInput
                                {
                                    BaseOrderId = input.BaseOrderId,
                                    UserId = user.UserId,
                                    VoucherSourceType = ScenicTicketVoucherSourceType.InterfaceDockPush
                                });
                            result.DeliverySuccess = retryDeliveryResponse.DeliverySuccess;
                            result.DeliverStatus = retryDeliveryResponse.DeliverStatus;
                        }

                        break;
                }

                break;
            case CredentialSourceType.ManualDelivery:

                //手工发货
                var manualDeliveryResponse = await _orderApiCaller.ScenicTicketVoucherManualDelivery(new ManualDeliveryScenicTicketVoucherInput
                {
                    BaseOrderId = input.BaseOrderId,
                    UserId = user.UserId,
                    VoucherSourceType = ScenicTicketVoucherSourceType.ManualUpload
                });
                result.DeliverySuccess = manualDeliveryResponse.DeliverySuccess;
                result.DeliverStatus = manualDeliveryResponse.DeliverStatus;

                break;
        }

        return Ok(result);
    }

    /// <summary>
    /// 门票订单详情(加密)
    /// (包含异常订单详情和正常订单详情)
    /// </summary>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200, typeof(ScenicTicketOrderDetailBffOutput))]
    public async Task<IActionResult> Detail(long baseOrderId)
    {
        ScenicTicketOrderDetailBffOutput result;

        //判断是否未处理的异常订单
        var syncFailOrderDetail = await SyncFailOrderDetailProcess(new GetOpenChannelSyncFailOrderInput
        {
            SyncFailOrderId = baseOrderId
        });
        if (syncFailOrderDetail is { SyncStatus: OpenChannelFailOrderSyncStatus.SyncFail })
        {
            result = ConvertFailOrderToNormalOrderDetail(syncFailOrderDetail);
        }
        else
        {
            //正常订单详情
            var searchResponse =
                await _orderApiCaller.ScenicTicketOrderDetail(new ScenicTicketOrderDetailInput { BaseOrderId = baseOrderId });
            result = _mapper.Map<ScenicTicketOrderDetailBffOutput>(searchResponse);

            if (result.ScenicTicketOrderInfo.CredentialSourceType == CredentialSourceType.InterfaceDock)
            {
                // 查询附加信息
                var orderExtraInfos =
                    (await _orderApiCaller.QueryOpenSupplierOrderExtraInfo(baseOrderId)).FirstOrDefault();
                if (orderExtraInfos.OrderExtraInfos.Any())
                {
                    result.OrderExtraInfos =
                        orderExtraInfos.OrderExtraInfos.Select(x => new QueryOpenSupplierOrderExtraInfoBffOutput
                        {
                            DataType = x.DataType,
                            OptionKey = x.OptionKey,
                            OptionValue = x.OptionValue
                        }).ToList();
                }
                else
                {
                    // 供应端采购失败查询对应关联产品的采购失败信息
                    if (result.SupplyChannelInfo.ChannelOrderStatus == ScenicTicketSupplierOrderStatus.CreateFail)
                    {
                        var checkOrderRelatedExtraInfos = await _openSupplierOrderService.CheckOrderRelatedExtraInfos(
                            new CheckOrderRelatedExtraInfoBffInput
                            {
                                Items = new List<CheckOrderRelatedExtraInfoItem>
                                {
                                    new CheckOrderRelatedExtraInfoItem
                                    {
                                        BaseOrderId = baseOrderId,
                                        OrderType = OrderType.ScenicTicket
                                    }
                                }
                            });
                        result.OrderExtraInfos = checkOrderRelatedExtraInfos
                            .GroupBy(x => x.DataType)
                            .Select(x => new QueryOpenSupplierOrderExtraInfoBffOutput
                        {
                            DataType = x.Key
                        }).ToList();
                    }
                }
            }
            
        }
        await DataSecrecy(result, true);

        return Ok(result);
    }

    /// <summary>
    /// 包含敏感信息的门票详情
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200, typeof(ScenicTicketOrderDetailBffOutput))]
    public async Task<IActionResult> SensitiveDetail(long baseOrderId)
    {
        ScenicTicketOrderDetailBffOutput result;

        //判断是否未处理的异常订单
        var syncFailOrderDetail = await SyncFailOrderDetailProcess(new GetOpenChannelSyncFailOrderInput
        {
            SyncFailOrderId = baseOrderId
        });
        if (syncFailOrderDetail is { SyncStatus: OpenChannelFailOrderSyncStatus.SyncFail })
        {
            result = ConvertFailOrderToNormalOrderDetail(syncFailOrderDetail);
        }
        else
        {
            //正常订单详情   
            var searchResponse =
                await _orderApiCaller.ScenicTicketOrderDetail(new ScenicTicketOrderDetailInput { BaseOrderId = baseOrderId });
            result = _mapper.Map<ScenicTicketOrderDetailBffOutput>(searchResponse);
        }
        await DataSecrecy(result, false);
        return Ok(result);
    }


    /// <summary>
    /// 手动添加发货凭证
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt(200, typeof(ManualAddScenicTicketVoucherBffOutput))]
    public async Task<IActionResult> ManualAddVoucher([FromForm] ManualAddScenicTicketVoucherBffInput input)
    {
        var result = new ManualAddScenicTicketVoucherBffOutput
        {
            Success = true
        };
        var user = HttpContext.User.ParseUserInfo<CurrentUser>();
        try
        {
            // 支持的文件类型
            var supportedFileTypesRegex = @"^(image\/|application\/pdf)";
            var contentType = input.File.ContentType;
            FileType? streamFileType = FileType.Unknown;
            if (!Regex.IsMatch(contentType, supportedFileTypesRegex))
            {
                result.Success = false;
                result.ErrorMessage = "仅支持图片和PDF";
                return Ok(result);
            }

            //只能图片和pdf
            if (contentType.Contains("image"))
            {
                streamFileType = FileType.Image;
            }
            else if (contentType.Contains("pdf"))
            {
                streamFileType = FileType.Pdf;
            }

            //获取tenantId
            var tenantId = HttpContext.GetTenantId();
            var parseStream = new ParseStream();
            using (var stream = input.File.OpenReadStream())
            {
                using (var memoryStream = new MemoryStream())
                {
                    await stream.CopyToAsync(memoryStream);
                    parseStream.FileType = streamFileType.Value;
                    parseStream.Stream = memoryStream;
                }
            }

            var vouchers = new List<ManualAddVoucherItem>();
            var uploadRequest = new ParseAndUploadInput
            {
                IsPdfToSingleImage = streamFileType.Value == FileType.Pdf,
                IsOneImageToPdf = streamFileType.Value == FileType.Image,
                TenantId = tenantId,
                Streams = new List<ParseStream>
                {
                    parseStream
                }
            };
            var parseVoucherFileResponse = (await _resourceApiCaller.ParseAndUpload(uploadRequest)).ToList();
            if (parseVoucherFileResponse.Any(x => x.Success == false))
            {
                result.Success = false;
                result.ErrorMessage = $"数据解析失败";
                return Ok(result);
            }

            vouchers = parseVoucherFileResponse.Select(x => new ManualAddVoucherItem
            {
                FilePath = x.FilePath,
                Thumbnail = x.ThumbnailPath,
                CompositeImagePath = x.CompositeImagePath,
                CompositePdfPath = x.CompositePdfPath,
            })
                .ToList();

            await _orderApiCaller.ScenicTicketVoucherManualAdd(new ManualAddVoucherInput
            {
                BaseOrderId = input.BaseOrderId,
                Vouchers = vouchers,
                UserId = user.UserId,
            });
        }
        catch (Exception e)
        {
            result.Success = false;
            result.ErrorMessage = e.Message;
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取渠道单信息
    /// </summary>
    /// <param name="channelOrderNo"></param>
    /// <returns></returns>
    [Authorize]
    [HttpGet]
    [SwaggerResponseExt(200, typeof(GetChannelOrderInfoBffOutput))]
    public async Task<IActionResult> GetChannelOrderInfo(string channelOrderNo)
    {
        GetChannelOrderInfoBffOutput? result;
        //优先查询渠道端的订单信息
        var queryChannelOrderRequest = new QueryChannelOrderDetailInput
        {
            OtaOrderId = channelOrderNo,
            SellingPlatform = SellingPlatform.Fliggy //目前只支持飞猪
        };
        var openChannelOrderDetail = await _orderApiCaller.QueryChannelOrderDetail(queryChannelOrderRequest);
        if (openChannelOrderDetail.Passengers.Any()) //查到信息
        {
            var orderTotalFee = openChannelOrderDetail.OrderItems.Sum(x => x.CostPrice);
            var orderDiscountFee = openChannelOrderDetail.OrderItems.Sum(x => x.DiscountFee);
            var quantity = openChannelOrderDetail.OrderItems.Sum(x => x.Quantity);
            var price = openChannelOrderDetail.OrderItems.First().Price;
            var otaInputData = ConvertChannelOrderToOTAInput(openChannelOrderDetail);
            result = new GetChannelOrderInfoBffOutput
            {
                ChannelOrderNo = channelOrderNo,
                Quantity = quantity,
                PaymentAmount = orderTotalFee / 100,
                DiscountAmount = orderDiscountFee / 100,
                ContactsEmail = openChannelOrderDetail.ContactItem.Email,
                ContactsName = openChannelOrderDetail.ContactItem.Name,
                ContactsPhoneNumber = openChannelOrderDetail.ContactItem.LocalPhone,
                OrderFields = await GetFailOrderFields(otaInputData)
            };
            result.OrderPrices.Add(new MultPriceOutput
            {
                Quantity = quantity,
                Price = price / 100,
            });
            //字段模板 中文转拼音
            await _baseOrderService.ConvertChineseNameToPinyin(result.OrderFields);
            return Ok(result);
        }
        
        var channelOrderInfoResponse = await _orderApiCaller.GetScenicTicketChannelOrderInfo(channelOrderNo);
        if (channelOrderInfoResponse == null)
        {
            channelOrderInfoResponse = new GetChannelOrderInfoOutput
            {
                ChannelOrderNo = channelOrderNo
            };
            //查询同步失败的订单数据
            var syncFailOrder = await SyncFailOrderDetailProcess(new GetOpenChannelSyncFailOrderInput
            {
                ChannelOrderNo = channelOrderNo
            });
            if (syncFailOrder != null)
            {
                channelOrderInfoResponse = new GetChannelOrderInfoOutput
                {
                    ChannelOrderNo = syncFailOrder.ChannelOrderNo.FirstOrDefault(),
                    PaymentAmount = syncFailOrder.PaymentAmount,
                    DiscountAmount = syncFailOrder.DiscountAmount,
                    Quantity = syncFailOrder.OrderPrices.Sum(x=>x.Quantity),
                    ContactsName = syncFailOrder.ContactsName,
                    ContactsEmail = syncFailOrder.ContactsEmail,
                    ContactsPhoneNumber = syncFailOrder.ContactsPhoneNumber,
                    TravelerInfos = syncFailOrder.TravelerInfos,
                    OrderPrices = syncFailOrder.OrderPrices,
                    OrderFields = syncFailOrder.OrderFields,
                    OrderExtraInfos = syncFailOrder.OrderExtraInfos
                };
            }
        }
        
        //字段模板 中文转拼音
        await _baseOrderService.ConvertChineseNameToPinyin(channelOrderInfoResponse.OrderFields);
        result = _mapper.Map<GetChannelOrderInfoBffOutput>(channelOrderInfoResponse);
        return Ok(result);
    }

    /// <summary>
    /// 查询门票同步失败异常订单详情
    /// </summary>
    /// <param name="syncFailOrderId"></param>
    /// <returns></returns>
    [Authorize]
    [HttpGet]
    [SwaggerResponseExt(200, typeof(GetSyncFailScenicTicketOrderDetailBffOutput))]
    public async Task<IActionResult> GetOpenChannelSyncFailOrderDetail(long syncFailOrderId)
    {
        var result = await SyncFailOrderDetailProcess(new GetOpenChannelSyncFailOrderInput
        {
            SyncFailOrderId = syncFailOrderId
        });
        if (result != null)
        {
            //字段模板 中文转拼音
            await _baseOrderService.ConvertChineseNameToPinyin(result.OrderFields);
        }
        return Ok(result);
    }

    /// <summary>
    /// 创建门票手工单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [HttpPost]
    [ProducesResponseType(typeof(CreateScenicTicketOrderOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.InvalidTravelDate, ErrorTypes.Order.SomeDatesAreUnavailable,
        ErrorTypes.Order.NotSupportSystemTicketReplace, ErrorTypes.Order.NotSupportAgencyReplace,
        ErrorTypes.Order.NotSupportMultiChannelOrderNo, ErrorTypes.Order.OrderFieldValidater,
        ErrorTypes.Order.TravelerNotCompleted, ErrorTypes.Order.OrderFieldChange, ErrorTypes.Order.OrderFieldValidater,
        ErrorTypes.Order.SyncFailOrderStatusChanged,ErrorTypes.Order.IdCardIsRequired,ErrorTypes.Product.ProductDisabled,
        ErrorTypes.Inventory.ProductInventoryNotEnough)]
    [Authorize]
    public async Task<IActionResult> Create(CreateByManualInput input)
    {
        var ticket = await _scenicSpotApiCaller.GetScenicTicketDetail(input.ScenicTicketId);
        if (ticket == null)
        {
            throw new BusinessException(ErrorTypes.Scenic.ScenicTicketsInvalid);
        }

        var tempResult = await _scenicSpotApiCaller.ProductInformationTemplateGetProductTempFieldsDetail(new GetProductTempFieldsDetailInput()
        {
            ProductId = input.ScenicSpotId,
            ProductSkuId = input.ScenicTicketId,
            ProductType = ProductType.Scenic
        });
        var productTemplateType = ticket.TouristInfoType switch
        {
            TouristInfoType.Every => ProductTemplateType.EachPerson,
            TouristInfoType.One => ProductTemplateType.JustOnePerson,
            _ =>  ProductTemplateType.EachPerson
        };
        var travelerCount = input.OrderFields.Count(x => x.TemplateType == TemplateType.Travel && x.ProductTemplateType == productTemplateType);
        var tempCount = tempResult.Templates.Count(x => x.TemplateType == TemplateType.Travel && x.ProductTemplateType == productTemplateType); // 无模板的判断
        var fieldCount = tempResult.Templates.FirstOrDefault(x => x.ProductTemplateType == productTemplateType)?.Fields.Count() ?? 0;// 空模板判断
        switch (ticket.TouristInfoType)
        {
            case TouristInfoType.Every:
                if (tempCount > 0 && fieldCount > 0 && travelerCount != input.Quantity)
                {
                    throw new BusinessException(ErrorTypes.Order.TravelerNotCompleted);//出行人不完整
                }
                // 排除ProductTemplateType.JustOne类型模板
                tempResult.Templates = tempResult.Templates
                    .Where(x => x.ProductTemplateType != ProductTemplateType.JustOnePerson)
                    .ToList();
                break;
            case TouristInfoType.One:
                if (tempCount > 0 && fieldCount > 0 && travelerCount == 0)
                {
                    throw new BusinessException(ErrorTypes.Order.TravelerNotCompleted);//出行人不完整
                }
                // 排除ProductTemplateType.EachPerson类型模板
                tempResult.Templates = tempResult.Templates
                    .Where(x => x.ProductTemplateType != ProductTemplateType.EachPerson)
                    .ToList();
                break;
        }

        // 验证模板参数
        _orderFieldInformationService.Validator(tempResult.Templates, input.OrderFields, true);
        // 新订单是没有订单确认信息的
        var orderSureTemp = tempResult.Templates.FirstOrDefault(x => x.TemplateType == TemplateType.Order);
        if (orderSureTemp != null)
        {
            input.OrderFields.Add(_mapper.Map<SaveOrderFieldInformationTypeDto>(orderSureTemp));
        }
        var result = await _orderApiCaller.CreateScenicTicketOrderByManual(input);
        if (result.BaseOrderId is > 0)
        {
            // 抖音订单确认
            if (input is { SyncFailOrderId: not null, SellingChannels: SellingChannels.TikTok })
            {
                await _openChannelSyncFailOrderService.OpenChannelOrderConfirm(input.SyncFailOrderId.Value, input.ChannelOrderNo);
            }
        }
        return Ok(result);
    }


    /// <summary>
    /// 产品替换
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default,ErrorTypes.Order.NoMatchingData,ErrorTypes.Common.NotSupportedOperation,
        ErrorTypes.Scenic.ScenicTicketsInvalid,ErrorTypes.Scenic.InvalidTimeSlot,ErrorTypes.Tenant.SupplierInvalid)]
    public async Task<IActionResult> ReplaceOrderProduct(ReplaceOrderProductBffInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        
        //查询门票详情
        var ticketDetailResponse = await _scenicSpotApiCaller.GetScenicTicketDetail(input.TicketId);
        if(ticketDetailResponse == null) throw new BusinessException(ErrorTypes.Scenic.ScenicTicketsInvalid);

        TimeSpan? timeSlot = null;
        if (input.TimeSlotId.HasValue)
        {
            var timeSlotInfo = ticketDetailResponse.TimeSlotInfos.FirstOrDefault(x => x.TimeSlotId == input.TimeSlotId!.Value);
            if(timeSlotInfo == null) throw new BusinessException(ErrorTypes.Scenic.InvalidTimeSlot);
            timeSlot = timeSlotInfo.Time;
        }
        
        //查询供应商配置
        var supplierInfo = (await _tenantApiCaller.QuerySuppliers(new QuerySuppliersInput
        {
            SupplierIds = new List<long> {ticketDetailResponse.SupplierId}
        })).FirstOrDefault();

        if (supplierInfo == null) throw new BusinessException(ErrorTypes.Tenant.SupplierInvalid);
        
        //汇率计算
        var priceExchangeRate = await _paymentService.GetOrderPriceExchange(new OrderPriceExchangeRateInput
        {
            CostCurrencyCode = ticketDetailResponse.CostCurrencyCode,
            SaleCurrencyCode = ticketDetailResponse.SaleCurrencyCode,
            PaymentCurrencyCode = ticketDetailResponse.SaleCurrencyCode,
        });
        
        //查询门票日历价格信息
        var responses = await _scenicSpotApiCaller.SearchTicketCalendarPrice(new SearchTicketCalendarInput
        {
            ScenicSpotId = ticketDetailResponse.ScenicSpotId,
            TicketIds = new[] { input.TicketId },
            TimeSlotIds = input.TimeSlotId.HasValue ? new[] { input.TimeSlotId.Value } : Array.Empty<long>(),
            BeginDate = input.TravelDate,
            EndDate = input.TravelDate
        });
        var ticketCalendar = responses.FirstOrDefault()?.Calendars.FirstOrDefault();
        if(ticketCalendar == null)
            throw new BusinessException(ErrorTypes.Order.CalendarNotEnable);
        if(ticketCalendar.SellingPrice == null || ticketCalendar.CostPrice == null)
            throw new BusinessException(ErrorTypes.Order.CalendarNotEnable);
        
        await _orderApiCaller.ReplaceScenicTicketOrderProduct(new ReplaceOrderProductInput
        {
            BaseOrderId = input.BaseOrderId,
            TicketId = input.TicketId,
            TimeSlotId = input.TimeSlotId,
            TravelDate = input.TravelDate,
            TimeSlot = timeSlot,
            Price = ticketCalendar.SellingPrice!.Value,
            CostPrice =  ticketCalendar.CostPrice!.Value,
            CostCurrencyCode = supplierInfo.CurrencyCode,
            CostExchangeRate = priceExchangeRate.CostExchangeRate,
            SupplierApiType = supplierInfo.SupplierApiType,
            TicketDetail = ticketDetailResponse,
            OperationUserDto = new OperationUserDto
            {
                UserType = UserType.Merchant,
                UserId = currentUser.UserId,
                Name = currentUser.NickName
            }
        });
        return Ok();
    }
    
    
    /// <summary>
    /// 修改确认信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<GetOrderCountOutput>), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> UpdateOrderConfirmation(UpdateOrderConfirmationInput input)
    {
        var res = await _orderApiCaller.UpdateScenicTicketOrderConfirmation(input);
        return Ok(res);
    }

    /// <summary>
    /// 修改出行信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<GetOrderCountOutput>), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> UpdateTravelInfo(UpdateTravelInfoInput input)
    {
        var res = await _orderApiCaller.UpdateScenicTicketTravelInfo(input);
        return Ok(res);
    }

    #region private

    /// <summary>
    /// 门票详情是否加密
    /// </summary>
    /// <param name="result"></param>
    private async Task DataSecrecy(ScenicTicketOrderDetailBffOutput result, bool isDataSecrecy)
    {
        #region 关联人员信息处理

        var orderRelatedPersons =
            await _baseOrderService.GetRelatedPersonsInfo(new QueryOrderRelatedPersonsInfoBffInput
            {
                IsDataSecrecy = isDataSecrecy,
                SellingPlatform = result.BaseOrderInfo.SellingPlatform,
                SalespersonId = result.BaseOrderInfo.SalespersonId,
                DevelopUserId = result.BaseOrderInfo.DevelopUserId,
                OperatorUserId = result.BaseOrderInfo.OperatorUserId,
                TrackingUserId = result.BaseOrderInfo.TrackingUserId,
                OperatorAssistantUserId = result.BaseOrderInfo.OperatorAssistantUserId,
                UserId = result.BaseOrderInfo.UserId,
                BaseOrderId = result.BaseOrderInfo.Id,
                IdType = SensitiveDataIdType.ScenicTicket
            });

        //填充关联人员名称
        result.BaseOrderInfo.SalespersonName = orderRelatedPersons.SalespersonName;
        result.BaseOrderInfo.DevelopUserName = orderRelatedPersons.DevelopUserName;
        result.BaseOrderInfo.OperatorUserName = orderRelatedPersons.OperatorUserName;
        result.BaseOrderInfo.OperatorAssistantUserName = orderRelatedPersons.OperatorAssistantUserName;
        result.BaseOrderInfo.TrackingUserName = orderRelatedPersons.TrackingUserName;
        result.BaseOrderInfo.UserNickName = result.BaseOrderInfo.UserId > 0
            ? orderRelatedPersons.UserNickName
            : result.BaseOrderInfo.UserNickName;

        //填充关联人员手机号码
        result.BaseOrderInfo.SalespersonPhoneNumber = orderRelatedPersons.SalespersonPhoneNumber;
        result.BaseOrderInfo.DevelopUserPhoneNumber = orderRelatedPersons.DevelopUserPhoneNumber;
        result.BaseOrderInfo.OperatorUserPhoneNumber = orderRelatedPersons.OperatorUserPhoneNumber;
        result.BaseOrderInfo.TrackingUserPhoneNumber = orderRelatedPersons.TrackingUserPhoneNumber;
        result.BaseOrderInfo.UserPhoneNumber = orderRelatedPersons.UserPhoneNumber;
        result.BaseOrderInfo.OperatorAssistantUserPhoneNumber = orderRelatedPersons.OperatorAssistantUserPhoneNumber;

        //处理联系人信息
        result.BaseOrderInfo.ContactsEmail =
            isDataSecrecy
                ? DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto()
                {
                    Id = result.BaseOrderInfo.Id,
                    IdType = SensitiveDataIdType.ScenicTicket,
                    DataType = SensitiveDataType.OrderContactsEmail,
                    Data = result.BaseOrderInfo.ContactsEmail,
                })
                : result.BaseOrderInfo.ContactsEmail;

        result.BaseOrderInfo.ContactsPhoneNumber =
            isDataSecrecy
                ? DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto()
                {
                    Id = result.BaseOrderInfo.Id,
                    IdType = SensitiveDataIdType.ScenicTicket,
                    DataType = SensitiveDataType.OrderContactsPhone,
                    Data = result.BaseOrderInfo.ContactsPhoneNumber,
                })
                : result.BaseOrderInfo.ContactsPhoneNumber;

        #endregion

        #region 出行人信息处理

        if (result.TravelerInfos.Any())
        {
            foreach (var travelerInfo in result.TravelerInfos)
            {
                travelerInfo.PhoneNumber =
                    isDataSecrecy
                        ? DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto()
                        {
                            Id = result.BaseOrderInfo.Id,
                            IdType = SensitiveDataIdType.ScenicTicket,
                            DataType = SensitiveDataType.OrderTravelerPhone,
                            Data = travelerInfo.PhoneNumber,
                        })
                        : travelerInfo.PhoneNumber;
                travelerInfo.IDCard = isDataSecrecy
                    ? DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto()
                    {
                        Id = result.BaseOrderInfo.Id,
                        IdType = SensitiveDataIdType.ScenicTicket,
                        DataType = SensitiveDataType.OrderTravelerIDCard,
                        Data = travelerInfo.IDCard,
                    })
                    : travelerInfo.IDCard;
                travelerInfo.Email = isDataSecrecy
                    ? DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto()
                    {
                        Id = result.BaseOrderInfo.Id,
                        IdType = SensitiveDataIdType.ScenicTicket,
                        DataType = SensitiveDataType.OrderTravelerEmail,
                        Data = travelerInfo.Email,
                    })
                    : travelerInfo.Email;
            }
        }

        #endregion

        #region 供应商/分销商信息处理

        result.BaseOrderInfo.SupplierId = result.ScenicTicketOrderInfo.SupplierId;
        result.BaseOrderInfo.SupplierName = result.SupplyChannelInfo?.SupplierName;
        result.BaseOrderInfo.SupplierShortName = result.SupplyChannelInfo?.ShortName;

        if (result.BaseOrderInfo.AgencyId.HasValue)
        {
            var agency = await _tenantApiCaller.GetAgencyDetail(result.BaseOrderInfo.AgencyId.Value);
            result.BaseOrderInfo.AgencyName = agency?.FullName;
        }

        #endregion

        result.OrderFields.ForEach(temp =>
        {
            temp.Fields.ForEach(x =>
            {
                if (isDataSecrecy)
                    x.FieldValue = _orderFieldInformationService.SensitiveData(new Services.FieldSensitiveDataDto(x,
                        result.BaseOrderInfo.Id,
                        SensitiveDataIdType.ScenicTicket, temp.TemplateType));
            });
            // 订单详情不需要信息提示
            temp.Fields = temp.Fields.Where(x => x.FieldType != FieldsType.Label).OrderBy(x => x.Sort).ToList();
            temp.Fields.ForEach(x =>
            {
                x.FieldValue ??= "";
            });
        });
    }

    /// <summary>
    /// 同步失败订单详情处理
    /// </summary>
    /// <param name="syncFailOrderId"></param>
    /// <returns></returns>
    private async Task<GetSyncFailScenicTicketOrderDetailBffOutput> SyncFailOrderDetailProcess(GetOpenChannelSyncFailOrderInput input)
    {
        input.OrderType = OrderType.ScenicTicket;
        var response = await _orderApiCaller.GetOpenChannelSyncFailOrderDetail(input);
        var result = _mapper.Map<GetSyncFailScenicTicketOrderDetailBffOutput>(response);
        if (result == null)
        {
            return null;
        }
        var scenicTicketSyncJsonData = DataContentJsonConvert(response.DataContentJson);
        //处理供应商信息
        if (result.SupplierId.HasValue)
        {
            var supplierInfo = (await _tenantApiCaller.GetSupplierShortInfo(new ShortInfoInput
                {
                    SupplierIds = new List<long> {result.SupplierId.Value}, IsEnabled = false
                }))
                .FirstOrDefault();
            result.SupplierName = supplierInfo?.SupplierFullName;
        }
        //处理分销商信息
        if (result.AgencyId.HasValue)
        {
            var agencyInfo = await _tenantApiCaller.GetAgencyDetail(result.AgencyId.Value);
            result.AgencyName = agencyInfo?.FullName;
        }
        //查询门票信息
        if (result.ScenicTicketId.HasValue)
        {
            var ticketInfo =
                (await _scenicSpotApiCaller.GetTicketInfoByIds(result.ScenicTicketId.Value)).FirstOrDefault();
            result.TicketsType = ticketInfo?.TicketsType;
            result.CredentialSourceType = ticketInfo?.CredentialSourceType;
            if (result.CredentialSourceType == CredentialSourceType.InterfaceDock && scenicTicketSyncJsonData.ExtraInfoIds.Any())
            {
                var matchExtraInfoRequest = new CreateOrderMatchExtraInfosDto
                {
                    MatchExtraInfoIds = scenicTicketSyncJsonData.ExtraInfoIds,
                    OpenSupplierType =
                        _openPlatformService.MapPriceInventorySourceToOpenSupplierType(
                            ticketInfo.PriceInventorySource!.Value)
                };
                matchExtraInfoRequest.ProductId = ticketInfo.ActivityId;
                if (!string.IsNullOrEmpty(ticketInfo.PackageId))
                    matchExtraInfoRequest.OptionId = ticketInfo.PackageId;
                if (!string.IsNullOrEmpty(ticketInfo.SkuId))
                    matchExtraInfoRequest.SkuIds.Add(ticketInfo.SkuId);
                result.OrderExtraInfos  = await _openChannelOrderService.CreateOrderMatchExtraInfos(matchExtraInfoRequest);
            }
        }
        //处理dataJson
        scenicTicketSyncJsonData.TicketId ??= result.ScenicTicketId;
        var productPrice = scenicTicketSyncJsonData.Price / 100;
        var discountFee = scenicTicketSyncJsonData.DiscountFee / 100;
        result.DiscountAmount = discountFee;
        result.OrderPrices = new List<MultPriceOutput>
        {
            new() {Price = productPrice, Quantity = scenicTicketSyncJsonData.Quantity,}
        };
        result.OrderFields = await GetFailOrderFields(scenicTicketSyncJsonData);
        return result;
    }

    private ScenicTicketOrderDetailBffOutput ConvertFailOrderToNormalOrderDetail(
        GetSyncFailScenicTicketOrderDetailBffOutput? syncFailOrderDetail)
    {
        var result = new ScenicTicketOrderDetailBffOutput();

        #region BaseOrderInfo

        result.BaseOrderInfo = new ScenicTicketBaseOrderBffInfo();
        result.BaseOrderInfo.Id = syncFailOrderDetail.SyncFailOrderId;
        result.BaseOrderInfo.AgencyId = syncFailOrderDetail.AgencyId;
        result.BaseOrderInfo.AgencyName = syncFailOrderDetail.AgencyName;
        result.BaseOrderInfo.ChannelOrderNo = syncFailOrderDetail.ChannelOrderNo;
        result.BaseOrderInfo.ContactsName = syncFailOrderDetail.ContactsName;
        result.BaseOrderInfo.ContactsPhoneNumber = syncFailOrderDetail.ContactsPhoneNumber;
        result.BaseOrderInfo.ContactsEmail = syncFailOrderDetail.ContactsEmail;
        result.BaseOrderInfo.CreateTime = syncFailOrderDetail.CreateTime;
        result.BaseOrderInfo.OrderType = syncFailOrderDetail.OrderType;
        result.BaseOrderInfo.SellingChannels = syncFailOrderDetail.SellingChannels;
        result.BaseOrderInfo.SellingPlatform = syncFailOrderDetail.SellingPlatform;
        result.BaseOrderInfo.Status = ScenicTicketOrderMixStatus.ChannelAbnormal;
        result.BaseOrderInfo.PaymentAmount = syncFailOrderDetail.PaymentAmount;
        result.BaseOrderInfo.TotalAmount = syncFailOrderDetail.PaymentAmount + syncFailOrderDetail.DiscountAmount;
        result.BaseOrderInfo.PaymentCurrencyCode = syncFailOrderDetail.CurrencyCode;
        result.BaseOrderInfo.ResourceName = syncFailOrderDetail.ScenicSpotName;
        result.BaseOrderInfo.ProductName = syncFailOrderDetail.TicketName;
        result.BaseOrderInfo.SupplierId = syncFailOrderDetail.SupplierId;

        #endregion

        #region scenicTicketOrderInfo

        result.ScenicTicketOrderInfo = new ScenicTicketOrderDetailBffInfo();
        result.ScenicTicketOrderInfo.ScenicSpotId = syncFailOrderDetail.ScenicSpotId ?? 0;
        result.ScenicTicketOrderInfo.ScenicTicketId = syncFailOrderDetail.ScenicTicketId ?? 0;
        result.ScenicTicketOrderInfo.SupplierId = syncFailOrderDetail.SupplierId ?? 0;
        result.ScenicTicketOrderInfo.Quantity = syncFailOrderDetail.OrderPrices.Sum(x => x.Quantity);
        result.ScenicTicketOrderInfo.ValidityBegin = null;
        result.ScenicTicketOrderInfo.ValidityEnd = null;
        result.ScenicTicketOrderInfo.ScenicTicketsType = syncFailOrderDetail.TicketsType;
        result.ScenicTicketOrderInfo.CredentialSourceType = syncFailOrderDetail.CredentialSourceType;
        result.ScenicTicketOrderInfo.TimeSlot = syncFailOrderDetail.TimeSlotName;

        #endregion

        #region supplyChannelInfo

        result.SupplyChannelInfo = new SupplyChannelInfo();
        result.SupplyChannelInfo.SupplierId = syncFailOrderDetail.SupplierId ?? 0;
        result.SupplyChannelInfo.SupplierName = syncFailOrderDetail.SupplierName;

        #endregion

        #region distributionChannelInfo

        result.DistributionChannelInfo = new DistributionChannelInfo();
        result.DistributionChannelInfo.ChannelOrderNo = result.BaseOrderInfo.ChannelOrderNo;
        result.DistributionChannelInfo.AgencyId = syncFailOrderDetail.AgencyId ?? 0;
        result.DistributionChannelInfo.AgencyName = syncFailOrderDetail.AgencyName;
        result.DistributionChannelInfo.ChannelOrderStatus = ScenicTicketOrderStatus.WaitingForDeliver;
        result.DistributionChannelInfo.SellingChannels = syncFailOrderDetail.SellingChannels;
        result.DistributionChannelInfo.SellingPlatform = syncFailOrderDetail.SellingPlatform;

        #endregion

        result.OrderFields = syncFailOrderDetail.OrderFields;
        return result;
    }

    private async Task<List<OrderFieldInformationTypeOutput>> GetFailOrderFields(
        CreateScenicOTAOrderBffInput syncJsonData)
    {
        List<OrderFieldInformationTypeOutput> orderFields = new List<OrderFieldInformationTypeOutput>();

        long scenicSpotId = 0, ticketId = 0;
        TouristInfoType? touristInfoType = null;
        if (syncJsonData.TicketId.HasValue)
        {
            var checkChannelTicketProduct =
                await _scenicSpotApiCaller.CheckOpenChannelProduct(syncJsonData.TicketId!.Value);
            scenicSpotId = checkChannelTicketProduct.FirstOrDefault()?.ScenicSpotId ?? 0;
            ticketId = checkChannelTicketProduct.FirstOrDefault()?.TicketId ?? 0;
            touristInfoType = checkChannelTicketProduct.FirstOrDefault()?.TouristInfoType ?? TouristInfoType.None;
        }

        // 产品不存在时使用默认模板，把字段显示
        var tempResult = await _scenicSpotApiCaller.ProductInformationTemplateGetProductTempFieldsDetail(
            new GetProductTempFieldsDetailInput()
            {
                ProductId = scenicSpotId, ProductSkuId = ticketId, ProductType = ProductType.Scenic
            });
        var templates = tempResult.Templates;

        var channelNewOrderContact = _mapper.Map<TravelLineOTAOrderContactBffItem>(syncJsonData.ContactItem);
        if (channelNewOrderContact == null)
        {
            channelNewOrderContact = new TravelLineOTAOrderContactBffItem
            {
                Name = syncJsonData.ContactsName,
                Email = syncJsonData.ContactsEmail,
                Mobile = syncJsonData.ContactsPhoneNumber,
            };
        }

        Contracts.Common.Order.DTOs.TravelLineOrder.CreateDto dto =
            new Contracts.Common.Order.DTOs.TravelLineOrder.CreateDto()
            {
                ContactsName = syncJsonData.ContactsName,
                ContactsEmail = syncJsonData.ContactsEmail,
                ContactsPhoneNumber = syncJsonData.ContactsPhoneNumber,
                SellingPlatform = syncJsonData.SellingPlatform,
                OrderTravelers = new List<Contracts.Common.Order.DTOs.TravelLineOrder.OrderTravelerInput>()
                // SellingChannel = input.SellingChannel
            };
        foreach (var item in syncJsonData.TravelerInfos)
        {
            var info = new Contracts.Common.Order.DTOs.TravelLineOrder.OrderTravelerInput()
            {
                Birthday = item.Birthday,
                CardValidDate = item.CardValidDate,
                Email = item.Email,
                ExtendInfo = item.ExtendInfo,
                FirstName = item.FirstName,
                Gender = item.Gender,
                Height = item.Height,
                IDCard = item.IDCard,
                IdCardType = item.IdCardType,
                LastName = item.LastName,
                Name = item.Name,
                NationalityCode = item.NationalityCode,
                NationalityName = item.NationalityName,
                Phone = item.PhoneNumber,
                ShoeSize = item.ShoeSize,
                Weight = item.Weight,
            };
            dto.OrderTravelers.Add(info);
        }

        var saveFields = await _orderFieldInformationService.ConvertTravelOrderData(dto, channelNewOrderContact,
            templates,
            OrderType.ScenicTicket,
            touristInfoType: touristInfoType);
        orderFields = _mapper.Map<List<OrderFieldInformationTypeOutput>>(saveFields);

        orderFields.ForEach(temp =>
        {
            // 订单详情不需要信息提示
            temp.Fields = temp.Fields.Where(x => x.FieldType != FieldsType.Label).OrderBy(x => x.Sort).ToList();
            temp.Fields.ForEach(x =>
            {
                x.FieldValue ??= "";
            });
        });
        return orderFields;
    }

    /// <summary>
    /// 处理不同订单类型转换后的差异数据
    /// </summary>
    /// <param name="scenicTicketSyncJsonData"></param>
    private CreateScenicOTAOrderBffInput DataContentJsonConvert(string dataContentJson)
    {
        var scenicTicketSyncJsonData = JsonConvert.DeserializeObject<CreateScenicOTAOrderBffInput>(dataContentJson);
        if (scenicTicketSyncJsonData.TravelerInfos.Any() is false)
        {
            //处理订单类型变动,导致的字段不一致
            var dynamicData = JsonConvert.DeserializeObject<JObject>(dataContentJson);
            if (dynamicData.TryGetValue("TravelerItems", StringComparison.OrdinalIgnoreCase,
                    out var travelerInfosToken))
            {
                scenicTicketSyncJsonData.TravelerInfos = travelerInfosToken.ToObject<List<TicketOrderTravelerInfo>>();
            }
        }

        if (scenicTicketSyncJsonData.OrderPriceItems.Any())
        {
            //处理订单类型变动.价格兼容
            scenicTicketSyncJsonData.Price = scenicTicketSyncJsonData.OrderPriceItems.Sum(x => x.Price);
            scenicTicketSyncJsonData.Quantity = scenicTicketSyncJsonData.OrderPriceItems.Sum(x => x.Quantity);
        }

        return scenicTicketSyncJsonData;
    }

    private CreateScenicOTAOrderBffInput ConvertChannelOrderToOTAInput(QueryChannelOrderDetailOutput channelOrderDetail)
    {
        var orderTravelerItems = (from lineOtaOrderPassenger in channelOrderDetail.Passengers
            select new TicketOrderTravelerInfo
            {
                ExtendInfo = lineOtaOrderPassenger.ExtendInfo
            }).ToList();

        _ = long.TryParse(channelOrderDetail.OutSkuId, out long skuId);
        var contactItem = _mapper.Map<ScenicTicketOTAOrderContactBffItem>(channelOrderDetail.ContactItem);
        var inputData = new CreateScenicOTAOrderBffInput
        {
            TravelerInfos = orderTravelerItems,
            TicketId = skuId,
            SellingPlatform = channelOrderDetail.SellingPlatform,
            ContactItem = contactItem,
            ContactsName = contactItem.Name,
            ContactsEmail = contactItem.Email,
            ContactsPhoneNumber = contactItem.Mobile,
        };
        return inputData;
    }
    #endregion
}