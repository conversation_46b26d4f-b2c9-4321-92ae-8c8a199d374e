using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.OrderPayment;
using Common.Swagger;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.DTOs.OffsetOrder;
using Contracts.Common.Order.DTOs.ReceiptSettlementOrder;
using Contracts.Common.Order.DTOs.SettlementOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Tenant.DTOs.Agency;
using Contracts.Common.Tenant.DTOs.Supplier;
using Contracts.Common.User.DTOs.DarenBonus;
using Contracts.Common.User.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UserType = Contracts.Common.Payment.Enums.UserType;

namespace Bff.Vebk.Controllers;

[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class OrderPaymentController : ControllerBase
{
    private readonly IPaymentApiCaller _paymentApiCaller;
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IUserApiCaller _userApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IMapper _mapper;

    public OrderPaymentController(IPaymentApiCaller paymentApiCaller,
        IOrderApiCaller orderApiCaller,
        IUserApiCaller userApiCaller,
        ITenantApiCaller tenantApiCaller,
        IMapper mapper)
    {
        _paymentApiCaller = paymentApiCaller;
        _orderApiCaller = orderApiCaller;
        _userApiCaller = userApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _mapper = mapper;
    }
    /// <summary>
    /// 获取账户订单对账单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(PagingModel<OrderAccountStatementOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> OrderAccountStatements(OrderAccountStatementInput input)
    {
        //查询订单支付单明细信息
        var orderStatementRequest = new GetOrderStatementInput
        {
            OrderType = input.OrderType,
            BeginDate = input.BeginDate,
            EndDate = input.EndDate,
            OrderId = input.OrderId,
            OrderPaymentType = input.OrderPaymentType,
            StatementType = input.StatementType,
            PageIndex = input.PageIndex,
            PageSize = input.PageSize
        };
        var pagings = await _paymentApiCaller.GetOrderStatements(orderStatementRequest);

        var orderStatements = pagings.Data;

        var orderAccountStatements = await FillOrderStatements(orderStatements);
        var result = new PagingModel<OrderAccountStatementOutput>()
        {
            Total = pagings.Total,
            PageIndex = pagings.PageIndex,
            PageSize = pagings.PageSize,
            Data = orderAccountStatements
        };

        return Ok(result);
    }

    /// <summary>
    /// 获取账户订单对账单导出数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(IEnumerable<OrderAccountStatementOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> OrderStatementExportData(OrderAccountStatementInput input)
    {
        //查询订单支付单导出数据
        var orderStatementRequest = new GetOrderStatementInput
        {
            OrderType = input.OrderType,
            BeginDate = input.BeginDate,
            EndDate = input.EndDate,
            OrderId = input.OrderId,
            OrderPaymentType = input.OrderPaymentType,
            StatementType = input.StatementType
        };
        var orderStatements = await _paymentApiCaller.GetOrderStatementExportData(orderStatementRequest);

        var orderAccountStatements = await FillOrderStatements(orderStatements);
        return Ok(orderAccountStatements);
    }


    /// <summary>
    /// 获取订单支付财务信息
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(GetOrderFinancialDataOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetFinancialData(GetOrderFinancialDataInput input)
    {
        var result = new GetOrderFinancialDataOutput();
        var refundOrderIds = new List<long>();
        //挂账支付和在线实付 当所有记录对应币种相同时，且不是券类订单（需要预约） 才计算毛利
        var creditPayCurrencyCode = new List<string>();
        var onlinePayCurrencyCode = new List<string>();

        #region 查询支付信息

        var paymentInfo = await GetPaymentInfo(input.OrderId, input.OrderPaymentType);

        #region 汇总订单基础信息
        var orderPrices = await _orderApiCaller.GetOrderPriceByBaseOrderId(input.OrderId);
        var orgPurchaseAmount = orderPrices != null ? orderPrices.Where(x => x.CostPrice.HasValue).Sum(x => x.CostPrice.Value * x.Quantity) : 0;
        decimal orgPayAmount = orgPurchaseAmount;
        if (paymentInfo.CostDiscountAmount > 0)
            orgPayAmount = orgPayAmount - paymentInfo.CostDiscountAmount;
        else if (orderPrices.Any(x => x.CostDiscountRate > 0))
            orgPayAmount = orgPayAmount -
                orderPrices.Where(x => x.CostPrice.HasValue).Sum(x => x.CostPrice.Value * x.Quantity * x.CostDiscountRate / 100);
        var baseStatisticsInfo = new BaseStatisticsInfo
        {
            OrgPurchaseAmount = Math.Round(orgPurchaseAmount, 2),
            OrgPayAmount = paymentInfo.PaymentType == PayType.None ? 0 :Math.Round(orgPayAmount, 2),
        };
        result.BaseStatisticsInfo = baseStatisticsInfo;
        #endregion

        //未支付不展示所有数据
        if (paymentInfo.PaymentType is PayType.None) return Ok(result);
        onlinePayCurrencyCode.Add(paymentInfo.CurrencyCode);

        //判断用户类型(C端用户还是B2B)
        var userType = paymentInfo.AgencyId > 0
            ? UserType.Agency
            : UserType.Customer;
        var userName = paymentInfo.AgencyId > 0
            ? paymentInfo.AgencyName
            : paymentInfo.UserNickName;

        result.PaymentInfo = new OrderFinancialPaymentInfo
        {
            Amount = paymentInfo.Amount,
            CurrencyCode = paymentInfo.CurrencyCode,
            PaymentType = paymentInfo.PaymentType,
            UniqueOrderNo = paymentInfo.PaymentExternalNo,
            UserType = userType,
            UserName = userName
        };
        result.PaymentInfos = new List<OrderFinancialPaymentInfo> { result.PaymentInfo };

        if (input.OrderPaymentType == OrderPaymentType.GroupBookingOrder)
        {
            var payments = await _orderApiCaller.BaseOrderItemPayments(new Contracts.Common.Order.DTOs.GroupBookingOrder.BaseOrderItemPaymentInput
            {
                BaseOrderId = input.OrderId,
            });
            result.PaymentInfos = payments
                .Where(x => x.PayStatus == PayStatus.Paid)
                .Select(x => new OrderFinancialPaymentInfo
                {
                    OrderId = x.Id,
                    Amount = x.PaymentAmount,
                    CurrencyCode = x.PaymentCurrencyCode,
                    PaymentType = x.PayType,
                    UniqueOrderNo = x.PaymentExternalNo,
                    UserType = userType,
                    UserName = userName
                }).ToList();
        }

        #endregion

        #region 已支付订单查询已退款信息

        var refundInfo = await GetRefundOrder(input.OrderId, input.OrderPaymentType);
        if (refundInfo.Any())
        {
            result.RefundInfo = refundInfo
                .Where(x => x.Status == RefundOrderStatus.Refunded)
                .Select(x => new OrderFinancialPaymentInfo
                {
                    OrderId = x.Id,
                    Amount = x.TotalAmount,
                    CurrencyCode = x.PaymentCurrencyCode,
                    CostAmount = x.CostAmount,
                    CostCurrencyCode = x.CostCurrencyCode,
                    PaymentType = result.PaymentInfo.PaymentType,
                    UserType = userType,
                    UserName = userName,
                })
                .ToList();

            onlinePayCurrencyCode.AddRange(result.RefundInfo.Select(x => x.CurrencyCode));
            refundOrderIds = result.RefundInfo.Select(x => x.OrderId).ToList();
        }

        #endregion

        ////线下支付只展示付款和退款信息
        //if (paymentInfo.PaymentType == PayType.Offline) return Ok(result);

        //券类订单（需要预约），无结算单信息,无毛利值
        var ticketOrderNeedReservation = false;
        if (paymentInfo.OrderType == OrderType.Ticket && input.OrderPaymentType == OrderPaymentType.OrderPay)
        {
            var ticketOrderInfo = await _orderApiCaller.GetTicketOrderReservationabled(input.OrderId);
            ticketOrderNeedReservation = ticketOrderInfo.NeedReservation;
        }

        #region 订单产品

        var productDataList = new List<(long baseOrderId, long subOrderId, string productName, string skuName)>();
        if (paymentInfo.OrderType == OrderType.Mail)
        {
            //邮寄订单特殊处理:(存在多条不同供应商的子单)
            var mailOrderDetail = await _orderApiCaller.GetMailOrderByBaseOrderId(input.OrderId);
            var subOrderList = mailOrderDetail.SubOrders
                .SelectMany(m => m.Details)
                .ToList();

            productDataList = subOrderList.Select(x =>
                    new ValueTuple<long, long, string, string>(
                        input.OrderId,
                        x.Id,
                        x.ProductName,
                        x.ProductSkuName))
                .ToList();
        }
        else
        {
            //券类预约单.
            if (input.OrderPaymentType == OrderPaymentType.ReservationOrderPay)
            {
                productDataList = new List<(long baseOrderId, long subOrderId, string productName, string skuName)>
                {
                    new(paymentInfo.BaseOrderId, input.OrderId, paymentInfo.ProductName, paymentInfo.ProductSkuName)
                };
            }
            else
            {
                var subOrderId = paymentInfo.SubOrderIds.FirstOrDefault();
                switch (paymentInfo.OrderType)
                {
                    case OrderType.Ticket:
                    case OrderType.Hotel:
                    case OrderType.TravelLineOrder:
                    case OrderType.CarProduct:
                        productDataList =
                            new List<(long baseOrderId, long subOrderId, string productName, string skuName)>
                            {
                                new(paymentInfo.BaseOrderId, subOrderId, paymentInfo.ProductName,
                                    paymentInfo.ProductSkuName)
                            };
                        break;
                    case OrderType.ScenicTicket:
                        productDataList =
                            new List<(long baseOrderId, long subOrderId, string productName, string skuName)>
                            {
                                new(paymentInfo.BaseOrderId, subOrderId, paymentInfo.ResourceName,
                                    paymentInfo.ProductName)
                            };
                        break;
                }
            }
        }

        #endregion

        #region 查询抵冲单

        List<FinancialOffsetOrderInfo> offsetOrderInfos = new List<FinancialOffsetOrderInfo>();
        if (!ticketOrderNeedReservation)
        {
            result.OffsetOrderInfos = await GetFinancialOffsetOrderInfo(
                input.OrderId,
                input.OrderPaymentType,
                paymentInfo.OrderType,
                productDataList);
            foreach (var item in result.OffsetOrderInfos)
            {
                var info = new FinancialOffsetOrderInfo()
                {
                    BaseOrderId = item.BaseOrderId,
                    BusinessOrderId = item.BaseOrderId,
                    ProductName = item.ProductName,
                    ProductSkuName = item.ProductSkuName,
                };
                List<FinancialOffsetOrderItem> infoOffsetOrders = new List<FinancialOffsetOrderItem>();
                info.OffsetOrderItems = item.OffsetOrderItems
                    .Where(x => (x.ProcessStatus != OffsetOrderProcessingStatus.Failed
                                   && !(x.OffsetType == OffsetOrderType.Receipt && x.MoneyType == OffsetOrderMoneyType.Additional))
                      || (x.OffsetType == OffsetOrderType.Receipt && x.MoneyType == OffsetOrderMoneyType.Additional && x.ProcessStatus == OffsetOrderProcessingStatus.Success)
                    ) // 1、过滤失败的抵充单，如审核不通过、撤销等的不参与后续计算，防止超额申请
                      // 2、销售总金额线下加收的时候，必须售后财务处理通过后才参与计算
                    .ToList();
                offsetOrderInfos.Add(info);
            }
        }

        #endregion

        #region 查询入账结算单/退款入账结算单

        //券类订单（需要预约），无结算单信息,无毛利值
        if (paymentInfo.PaymentType == PayType.AgencyCreditPay && !ticketOrderNeedReservation)
        {
            var receivableOffsetOrderIds = offsetOrderInfos
                .SelectMany(x => x.OffsetOrderItems)
                .Where(x => x.OffsetType == OffsetOrderType.Receipt)
                .Select(x => x.OffsetOrderId)
                .ToList();

            var receivableFinancialData = await GetReceivableFinancialData(
                input.OrderId,
                paymentInfo.SubOrderIds.FirstOrDefault(),
                input.OrderPaymentType,
                refundOrderIds,
                receivableOffsetOrderIds,
                paymentInfo.Amount,
                paymentInfo.CurrencyCode,
                paymentInfo.AgencyId);

            result.ReceiptSettlementOrderInfo = receivableFinancialData.ReceiptOrderList;
            result.RefundReceiptSettlementOrderInfo = receivableFinancialData.RefundReceiptOrderList;
            creditPayCurrencyCode.AddRange(receivableFinancialData.AllCurrencyCode);
            foreach (var offsetOrderInfo in offsetOrderInfos)
            {
                foreach (var offsetOrderItem in offsetOrderInfo.OffsetOrderItems)
                {
                    var offsetPayableOrder = receivableFinancialData.OffsetReceiptOrderList
                        .FirstOrDefault(x => x.BusinessOrderId == offsetOrderItem.OffsetOrderId);
                    if (offsetPayableOrder is null) continue;
                    offsetOrderItem.SettlementOrderId = offsetPayableOrder.SettlementOrderId;
                }
            }
        }

        #endregion

        #region 查询出账结算单/退款出账结算单

        //券类订单（需要预约），无结算单信息,无毛利值
        if (!ticketOrderNeedReservation)
        {
            var payableOffsetOrderIds = offsetOrderInfos
                .SelectMany(x => x.OffsetOrderItems)
                .Where(x => x.OffsetType == OffsetOrderType.Payable)
                .Select(x => x.OffsetOrderId)
                .ToList();

            var payableFinancialData = await GetPayableFinancialData(
                input.OrderId,
                input.OrderPaymentType,
                paymentInfo.OrderType,
                refundOrderIds,
                payableOffsetOrderIds,
                productDataList);

            result.PayableSettlementOrderInfo = payableFinancialData.PayableOrderList;
            result.RefundPayableSettlementOrderInfo = payableFinancialData.RefundPayableOrderList;
            creditPayCurrencyCode.AddRange(payableFinancialData.AllCurrencyCode);
            onlinePayCurrencyCode.AddRange(payableFinancialData.AllCurrencyCode);
            foreach (var offsetOrderInfo in offsetOrderInfos)
            {
                foreach (var offsetOrderItem in offsetOrderInfo.OffsetOrderItems)
                {
                    var offsetPayableOrder = payableFinancialData.OffsetPayableOrderList
                        .FirstOrDefault(x => x.BusinessOrderId == offsetOrderItem.OffsetOrderId);
                    if (offsetPayableOrder is null) continue;
                    offsetOrderItem.SettlementOrderId = offsetPayableOrder.SettlementOrderId;
                }
            }
        }

        #endregion

        #region 达人佣金,平台佣金,支付手续费,支付退款流水号(默认币种CNY)

        result.GrossMarginInfo = new OrderFinancialGrossMarginInfo();
        //已支付订单查询达人佣金
        var darenBonusReq = input.OrderPaymentType == OrderPaymentType.OrderPay
            ? new GetDarenBonusByOrderInput { BaseOrderId = input.OrderId }
            : new GetDarenBonusByOrderInput { SubOrderId = input.OrderId };

        var darenBonusList = await _userApiCaller.GetDarenBonusByOrder(darenBonusReq);
        var developBonusAmount = darenBonusList.Sum(x => x.DevelopBonuses) ?? 0;
        var shareBonusAmount = darenBonusList.Sum(x => x.ShareBonuses) ?? 0;
        result.GrossMarginInfo.DarenBonusAmount = developBonusAmount + shareBonusAmount;

        //查询订单支付佣金,手续费.流水号信息
        var paymentData = await _paymentApiCaller.GetOrderPaymentData(input.OrderId);
        result.GrossMarginInfo.PlatformCommission = paymentData.PlatformCommission;
        result.GrossMarginInfo.PaymentFee = paymentData.PaymentFee;

        //达人佣金,平台佣金,支付手续费其一有值才默认CNY币种
        if (result.GrossMarginInfo.DarenBonusAmount > 0 ||
            result.GrossMarginInfo.PaymentFee > 0 ||
            result.GrossMarginInfo.PlatformCommission > 0)
        {
            creditPayCurrencyCode.Add(Currency.CNY.ToString());
            onlinePayCurrencyCode.Add(Currency.CNY.ToString());
        }

        //支付流水号赋值
        result.PaymentInfo.UniqueOrderNo = paymentData.UniqueOrderNo;
        //退款支付流水号赋值
        foreach (var item in result.RefundInfo)
        {
            var refundNoItem = paymentData.OrderpaymentRefunds
                .FirstOrDefault(x => x.RefundOrderId == item.OrderId);
            if (refundNoItem is null) continue;

            item.UniqueOrderNo = refundNoItem.UniqueRefundNo;
        }

        #endregion

        #region 计算毛利

        var allReceiptOrders = result.ReceiptSettlementOrderInfo.Concat(result.RefundReceiptSettlementOrderInfo)
        .ToList();
        var allReceiptCompleted = allReceiptOrders.All(x => x.Status == ReceiptSettlementOrderStatus.ReceiptComplete);

        var allPayableOrders = result.PayableSettlementOrderInfo.Concat(result.RefundPayableSettlementOrderInfo)
            .ToList();
        var allPayableCompleted = allPayableOrders.All(x => x.Status == SettlementOrderStatus.PaymentCompleted);

        if (allReceiptCompleted && allPayableCompleted && !ticketOrderNeedReservation)
        {
            var receivedAmount = result.ReceiptSettlementOrderInfo.Sum(x => x.ReceivedAmount);
            var refundReceivedAmount = result.RefundReceiptSettlementOrderInfo.Sum(x => x.ReceivedAmount);
            var payableAmount = result.PayableSettlementOrderInfo.Sum(x => x.PaymentAmount);
            var refundPayableAmount = result.RefundPayableSettlementOrderInfo.Sum(x => x.PaymentAmount);
            var refundAmount = result.RefundInfo.Sum(x => x.Amount);

            switch (paymentInfo.PaymentType)
            {
                case PayType.AgencyCreditPay:
                    {
                        //未入账不计算
                        if (allReceiptOrders.Any() is false)
                            break;

                        //当所有记录对应币种相同时，且不是券类订单（需要预约） 才计算毛利
                        creditPayCurrencyCode = creditPayCurrencyCode.Distinct().ToList();
                        var allCurrencyEqual = creditPayCurrencyCode.All(x => x.Equals(creditPayCurrencyCode[0]));
                        if (!allCurrencyEqual)
                            break;

                        //挂账支付时：毛利=实际入账金额+退款实际入账金额(负数)-平台佣金-支付手续费-达人佣金-实际出账金额-退款出账金额(负数)
                        result.GrossMarginInfo.GrossMargin = receivedAmount
                                                             + refundReceivedAmount
                                                             - result.GrossMarginInfo.PlatformCommission
                                                             - result.GrossMarginInfo.PaymentFee
                                                             - result.GrossMarginInfo.DarenBonusAmount
                                                             - payableAmount
                                                             - refundPayableAmount;
                        break;
                    }
                case PayType.YeePay:
                case PayType.UserStoredValueCardPay:
                    {
                        //未出账不计算
                        if (allPayableOrders.Any() is false)
                            break;

                        //当所有记录对应币种相同时，且不是券类订单（需要预约） 才计算毛利
                        onlinePayCurrencyCode = onlinePayCurrencyCode.Distinct().ToList();
                        var allCurrencyEqual = onlinePayCurrencyCode.All(x => x.Equals(onlinePayCurrencyCode[0]));
                        if (!allCurrencyEqual)
                            break;

                        decimal? paymentAmount = paymentInfo.Amount;
                        //预约单 支付金额 = (单价+预约加价) * 已完结份数
                        if (input.OrderPaymentType == OrderPaymentType.ReservationOrderPay)
                        {
                            paymentAmount = await GetReservationOrderAmount(input.OrderId);
                        }

                        //在线支付时：毛利=支付金额-退款金额(非负数)-平台佣金-支付手续费-达人佣金-实际出账金额-退款出账金额(负数)
                        result.GrossMarginInfo.GrossMargin = paymentAmount
                                                             - refundAmount
                                                             - result.GrossMarginInfo.PlatformCommission
                                                             - result.GrossMarginInfo.PaymentFee
                                                             - result.GrossMarginInfo.DarenBonusAmount
                                                             - payableAmount
                                                             - refundPayableAmount;
                        break;
                    }
            }
        }

        #endregion

        #region 保险信息
        if (paymentInfo.OrderType == OrderType.TravelLineOrder)
        {
            // 获取保险采购单列表
            var insureRecord = await _orderApiCaller.GetInsureRecordByOrder(input.OrderId);
            if (insureRecord?.TotalAmount.HasValue == true)
            {
                result.InsureRecordInfo = new OffsetOrderCollectInfo
                {
                    Amount = insureRecord?.TotalAmount.Value ?? 0,
                    CurrencyCode = result.PaymentInfo.CurrencyCode,
                };
            }
        }
        #endregion

        #region 汇总抵充单信息
        var offsetOrderItems = offsetOrderInfos
            .SelectMany(x => x.OffsetOrderItems)
            .ToList();
        var totalSalesInfo = new OffsetOrderCollectInfo
        {
            Amount = result.PaymentInfo.Amount.Value,
            CurrencyCode = result.PaymentInfo.CurrencyCode,
        };
        var totalPurchaseInfo = new OffsetOrderCollectInfo
        {
            Amount = default,
            CurrencyCode = totalSalesInfo.CurrencyCode,
        };
        if (result.PayableSettlementOrderInfo.Any())
        {
            //出账结算单会包含抵充单信息,此处只过滤掉抵充单.抵冲单数据下面计算
            var payableSettlementOrderInfo =
                result.PayableSettlementOrderInfo.Where(x => x.BusinessType != SettlementBusinessType.OffsetOrder);

            totalPurchaseInfo = new OffsetOrderCollectInfo
            {
                Amount = payableSettlementOrderInfo.Sum(x => x.TotalAmount),
                CurrencyCode = result.PayableSettlementOrderInfo.FirstOrDefault().TotalAmountCurrencyCode,
            }; 
        }

        //总销售金额：paymentInfo.amount + { 所有「加收」类型的抵充数据}
        //总采购金额：payableSettlementOrderInfo[].totalAmount 总和 + { 所有「退款 + 赔付」类型的抵充数据 }
        foreach (var item in offsetOrderItems)
        {
            switch (item.OffsetType)
            {
                case OffsetOrderType.Receipt:
                    totalSalesInfo.Amount += item.OffsetAmount;
                    break;
                case OffsetOrderType.Payable:
                    totalPurchaseInfo.Amount += item.OffsetAmount;
                    break;
            }
        }
        //总金额计算减去退款金额
        if (result.RefundInfo?.Count is > 0)
        {
            totalPurchaseInfo.Amount -= result.RefundInfo.Sum(s => s.CostAmount);
            totalSalesInfo.Amount -= result.RefundInfo.Sum(s => s.Amount ?? 0);
        }
        totalPurchaseInfo.Amount = Math.Round(totalPurchaseInfo.Amount, 2);
        totalSalesInfo.Amount = Math.Round(totalSalesInfo.Amount, 2);
        result.TotalPurchaseInfo = totalPurchaseInfo;
        result.TotalSalesInfo = totalSalesInfo;

        if (paymentInfo.CommissionFee.HasValue)
        {
            result.ChannelCommissionInfo = new OffsetOrderCollectInfo
            {
                Amount = paymentInfo.CommissionFee.Value,
                CurrencyCode = totalSalesInfo.CurrencyCode,
            };
        }

        if (totalPurchaseInfo.CurrencyCode.Equals(totalSalesInfo.CurrencyCode))
            result.GrossProfitInfo = new OffsetOrderCollectInfo
            {
                Amount = totalSalesInfo.Amount - totalPurchaseInfo.Amount - (result.ChannelCommissionInfo?.Amount ?? 0) - (result.InsureRecordInfo?.Amount ?? 0),
                CurrencyCode = totalSalesInfo.CurrencyCode
            };
        else
        {
            orderPrices = await _orderApiCaller.GetOrderPriceByBaseOrderId(input.OrderId);
            if (orderPrices != null && orderPrices.Any() is true)
            {
                var tenant = await _tenantApiCaller.GetTenantSimpleInfo();
                var orderPrice = orderPrices.FirstOrDefault();
                var paymentAmountTenantCurrency = Math.Round((totalSalesInfo.Amount / orderPrice.CurrentExchangeRate), 2);
                var totalCostTenantCurrency = Math.Round((totalPurchaseInfo.Amount * orderPrice.CostExchangeRate), 2);
                result.GrossProfitInfo = new OffsetOrderCollectInfo
                {
                    Amount = paymentAmountTenantCurrency - totalCostTenantCurrency - (result.ChannelCommissionInfo?.Amount ?? 0) - (result.InsureRecordInfo?.Amount ?? 0),
                    CurrencyCode = tenant.CurrencyCode //默认是人民币
                };
            }
        }

        #endregion

        var receiptRefundAmount = offsetOrderItems
            .Where(x => x.OffsetType == OffsetOrderType.Receipt)
            .Where(x => x.MoneyType == OffsetOrderMoneyType.Refund)
            .Sum(x => x.Amount);
        var receiptRefundableAmount = (result.PaymentInfo.Amount ?? 0) - receiptRefundAmount;
        // 支付可退金额
        result.ReceiptRefundableInfo = new OffsetOrderCollectInfo()
        {
            Amount = receiptRefundableAmount < 0 ? 0 : receiptRefundableAmount,
            CurrencyCode = result.TotalSalesInfo.CurrencyCode,
        };

        return Ok(result);
    }

    private async Task<IEnumerable<OrderAccountStatementOutput>> FillOrderStatements(
        IEnumerable<GetOrderStatementOutput> orderStatements)
    {
        if (orderStatements?.Any() is not true)
            return Enumerable.Empty<OrderAccountStatementOutput>();
        //查询订单金额信息
        var orderAmountInfoRequest = new OrderAmountInfoInput
        {
            OrderInfos = orderStatements.Select(x => new OrderInfoInput
            {
                OrderId = x.OrderId,
                OrderPaymentType = (byte)x.OrderPaymentType
            })
        };
        var orderAmountInfos = await _orderApiCaller.GetOrderAmountInfos(orderAmountInfoRequest);
        //查询订单达人奖励信息
        var orderBonusStatRequest = new GetOrderBonusStatInput
        {
            BaseOrderIds = orderStatements.Select(x => x.OrderId)
        };

        var orderBonusStats = await _userApiCaller.GetOrderBonusStat(orderBonusStatRequest);
        //数据聚合
        var orderAccountStatements = new List<OrderAccountStatementOutput>();

        foreach (var orderStatement in orderStatements)
        {
            var orderAmountInfo = orderAmountInfos
                .Where(x => x.OrderId == orderStatement.OrderId)
                .FirstOrDefault();

            var bonusStats = orderBonusStats
                .Where(x => x.BaseOrderId == orderStatement.OrderId)
                .ToList();
            var shareAmountReceivable =
                bonusStats.Where(x => x.BonusType == DarenBonusType.Share).Sum(x => x.AmountReceivable);
            var developAmountReceivable = bonusStats.Where(x => x.BonusType == DarenBonusType.Develop)
                .Sum(x => x.AmountReceivable);
            var refundShareAmountReceivable = bonusStats
                .Where(x => x.BonusType == DarenBonusType.Share && x.Status == DarenBonusStatus.Canceled)
                .Sum(x => x.AmountReceivable);
            var refundDevelopAmountReceivable = bonusStats
                .Where(x => x.BonusType == DarenBonusType.Develop && x.Status == DarenBonusStatus.Canceled)
                .Sum(x => x.AmountReceivable);
            OrderAccountStatementOutput statement = new()
            {
                CreateTime = orderStatement.CreateTime,
                OrderType = orderStatement.OrderType,
                OrderPaymentType = orderStatement.OrderPaymentType,
                OrderId = orderStatement.OrderId,
                PayChannel = orderStatement.PayChannel,
                StatementType = orderStatement.StatementType,
                Amount = orderStatement.Amount,
                PaymentFee = orderStatement.PaymentFee,
                PlatformCommission = orderStatement.PlatformCommission,
                TotalAmount = orderAmountInfo?.TotalAmount ?? 0,
                DiscountAmount = orderAmountInfo?.DiscountAmount ?? 0,
                DarenShareAmount = orderStatement.StatementType switch
                {
                    1 => shareAmountReceivable > 0 ? -shareAmountReceivable : 0,
                    2 => refundShareAmountReceivable,
                    _ => 0
                },
                DarenDevelopAmount = orderStatement.StatementType switch
                {
                    1 => developAmountReceivable > 0 ? -developAmountReceivable : 0,
                    2 => refundDevelopAmountReceivable,
                    _ => 0
                }
            };
            orderAccountStatements.Add(statement);
        }

        return orderAccountStatements;
    }

    /// <summary>
    /// 查询入账结算单/退款入账结算单
    /// </summary>
    /// <param name="orderId">订单id</param>
    /// <param name="orderPaymentType">订单支付类型</param>
    /// <param name="refundOrderIds">退款单id列表</param>
    /// <param name="offsetOrderIds">抵冲单id列表</param>
    /// <param name="paymentAmount">支付金额</param>
    /// <param name="paymentCurrencyCode">支付币种</param>
    /// <param name="agencyId">分销商id</param>
    /// <returns></returns>
    private async Task<(List<GetReceivableOrderDetailOutput> ReceiptOrderList,
            List<GetReceivableOrderDetailOutput> RefundReceiptOrderList,
            List<GetReceivableOrderDetailOutput> OffsetReceiptOrderList,
            List<string> AllCurrencyCode)>
        GetReceivableFinancialData(long orderId,
            long subOrderId,
            OrderPaymentType orderPaymentType,
            List<long> refundOrderIds,
            List<long> offsetOrderIds,
            decimal paymentAmount, string paymentCurrencyCode, long agencyId)
    {
        var receiptOrderList = new List<GetReceivableOrderDetailOutput>();
        var refundReceiptOrderList = new List<GetReceivableOrderDetailOutput>();
        var offsetReceiptOrderList = new List<GetReceivableOrderDetailOutput>();
        var allCurrencyCode = new List<string>();

        var orderIds = new List<long> { orderId };
        if (offsetOrderIds.Any())
        {
            orderIds.AddRange(offsetOrderIds);
        }

        //查询入账结算单(收款结算单)
        var receiptSettlementReq = orderPaymentType == OrderPaymentType.OrderPay
            ? new GetReceivableOrderDetailInput { BaseOrderIds = orderIds }
            : new GetReceivableOrderDetailInput { BusinessOrderIds = orderIds };

        var receiptSettlementOrderInfo = await _orderApiCaller.GetReceivableOrderDetailByOrderId(receiptSettlementReq);
        if (receiptSettlementOrderInfo.Any())
        {
            allCurrencyCode.AddRange(receiptSettlementOrderInfo
                .Where(x => x.ReceivedAmountCurrencyCode is not null)
                .Select(x => x.ReceivedAmountCurrencyCode));

            //入账
            if (orderPaymentType == OrderPaymentType.OrderPay)
            {//其他订单
                receiptOrderList =
                    receiptSettlementOrderInfo.Where(x => x.BaseOrderId == orderId
                                                          && x.BusinessType !=
                                                          ReceiptSettlementBusinessType.RefundOrder)
                        .ToList();
            }
            else
            {
                //预约单
                receiptOrderList =
                    receiptSettlementOrderInfo.Where(x => x.BusinessOrderId == orderId)
                        .ToList();
            }

            //退款入账单
            refundReceiptOrderList = receiptSettlementOrderInfo
                .Where(x => refundOrderIds.Contains(x.BusinessOrderId))
                .ToList();

            offsetReceiptOrderList = receiptSettlementOrderInfo
                .Where(x => offsetOrderIds.Contains(x.BusinessOrderId)
                            && x.BusinessType == ReceiptSettlementBusinessType.OffsetOrder)
                .ToList();
        }
        else
        {
            //预约单暂时不展示
            if (orderPaymentType == OrderPaymentType.OrderPay)
            {
                allCurrencyCode.Add(paymentCurrencyCode);
                receiptOrderList.Add(new GetReceivableOrderDetailOutput
                {
                    BaseOrderId = orderId,
                    BusinessOrderId = subOrderId,
                    TotalAmount = paymentAmount,
                    TotalAmountCurrencyCode = paymentCurrencyCode,
                    AgencyId = agencyId
                });
            }
        }

        #region 数据补充

        if (receiptOrderList.Any() || refundReceiptOrderList.Any())
        {
            //查询分销商名称
            var agencyIds = new List<long>();
            agencyIds.AddRange(receiptOrderList.Select(x => x.AgencyId).Distinct());
            agencyIds.AddRange(refundReceiptOrderList.Select(x => x.AgencyId).Distinct());
            var agencyInfo = await _tenantApiCaller.GetAgencyByIds(new GetAgenciesByIdsInput { AgencyIds = agencyIds });
            foreach (var item in receiptOrderList)
            {
                var agencyItem = agencyInfo.FirstOrDefault(x => x.Id == item.AgencyId);
                if (agencyItem is null) continue;
                item.AgencyName = agencyItem.FullName;
            }

            foreach (var item in refundReceiptOrderList)
            {
                var agencyItem = agencyInfo.FirstOrDefault(x => x.Id == item.AgencyId);
                if (agencyItem is null) continue;
                item.AgencyName = agencyItem.FullName;
            }
        }

        #endregion

        return new ValueTuple<List<GetReceivableOrderDetailOutput>,
            List<GetReceivableOrderDetailOutput>,
            List<GetReceivableOrderDetailOutput>,
            List<string>>(
            receiptOrderList,
            refundReceiptOrderList,
            offsetReceiptOrderList,
            allCurrencyCode);
    }

    /// <summary>
    /// 查询出账结算单/退款出账结算单
    /// </summary>
    /// <param name="orderId">订单id</param>
    /// <param name="orderPaymentType">订单支付类型</param>
    /// <param name="refundOrderIds">退款单id列表</param>
    /// <param name="orderType">订单类型</param>
    /// <param name="offsetOrderIds">抵冲单id列表</param>
    /// <param name="productDataList">订单产品列表</param>
    /// <returns></returns>
    private async Task<(List<GetPayableOrderDetailOutput> PayableOrderList,
            List<GetPayableOrderDetailOutput> RefundPayableOrderList,
            List<GetPayableOrderDetailOutput> OffsetPayableOrderList,
            List<string> AllCurrencyCode)>
        GetPayableFinancialData(long orderId,
            OrderPaymentType orderPaymentType,
            OrderType orderType,
            List<long> refundOrderIds,
            List<long> offsetOrderIds,
            List<(long baseOrderId, long subOrderId, string productName, string skuName)> productDataList)
    {
        var payableOrderList = new List<GetPayableOrderDetailOutput>();
        var refundPayableOrderList = new List<GetPayableOrderDetailOutput>();
        var offsetPayableOrderList = new List<GetPayableOrderDetailOutput>();
        var allCurrencyCode = new List<string>();
        //未生成出账单的订单Id列表
        var notInSettlementSubOrderIds = new List<long>();

        var orderIds = new List<long> { orderId };
        if (offsetOrderIds.Any())
        {
            orderIds.AddRange(offsetOrderIds);
        }
        //查询出账结算单(付款结算单)
        var payableSettlementReq = orderPaymentType == OrderPaymentType.OrderPay
            ? new GetPayableOrderDetailInput { BaseOrderIds = orderIds }
            : new GetPayableOrderDetailInput { BusinessOrderIds = orderIds };

        var payableSettlementOrderInfo = await _orderApiCaller.GetPayableOrderDetailByOrderId(payableSettlementReq);

        //已生成出账单的订单数据
        if (payableSettlementOrderInfo.Any())
        {
            allCurrencyCode.AddRange(payableSettlementOrderInfo
                .Where(x => x.PaymentAmountCurrencyCode is not null)
                .Select(x => x.PaymentAmountCurrencyCode));

            if (orderPaymentType == OrderPaymentType.OrderPay)
            {
                payableOrderList =
                    payableSettlementOrderInfo.Where(x => x.BaseOrderId == orderId
                                                          && x.BusinessType != SettlementBusinessType.RefundOrder)
                        .ToList();
            }
            else
            {
                payableOrderList =
                    payableSettlementOrderInfo.Where(x => x.BusinessOrderId == orderId)
                        .ToList();
            }

            //退款出账单
            refundPayableOrderList = payableSettlementOrderInfo
                .Where(x => refundOrderIds.Contains(x.BusinessOrderId))
                .ToList();

            //抵冲单
            offsetPayableOrderList = payableSettlementOrderInfo
                .Where(x => offsetOrderIds.Contains(x.BusinessOrderId)
                            && x.BusinessType == SettlementBusinessType.OffsetOrder)
                .ToList();

        }
        else
        {
            notInSettlementSubOrderIds.Add(orderId);
        }

        //订单产品信息
        var payableSubOrderIds = payableOrderList.Select(x => x.BusinessOrderId).ToList();

        //邮寄订单特殊处理:(存在多条不同供应商的子单)
        if (orderType == OrderType.Mail)
        {
            var subOrderIds = productDataList.Select(x => x.subOrderId).ToList();
            //获取未入账的订单id
            notInSettlementSubOrderIds = subOrderIds.Except(payableSubOrderIds).ToList();
        }

        //未生成出账单的订单数据(预约单不展示)
        if (notInSettlementSubOrderIds.Any() && orderPaymentType == OrderPaymentType.OrderPay)
        {
            var payableOrderAmountList = await _orderApiCaller.GetPayableOrderAmount(new GetPayableOrderAmountInput
            {
                OrderType = orderType,
                BaseOrderId = orderId
            });

            //过滤掉已结算的邮寄产品子订单
            if (orderType == OrderType.Mail)
            {
                payableOrderAmountList = payableOrderAmountList
                    .Where(x => notInSettlementSubOrderIds.Contains(x.SubOrderId))
                    .ToList();
            }

            //过滤掉自营订单
            payableOrderList.AddRange(payableOrderAmountList
                .Where(x => x.SupplierIid > 0)
                .Select(item => new GetPayableOrderDetailOutput
                {
                    BaseOrderId = orderId,
                    BusinessOrderId = item.SubOrderId,
                    TotalAmount = item.Total,
                    TotalAmountCurrencyCode = item.CostCurrencyCode,
                    SupplierId = item.SupplierIid
                }));
        }

        #region 出账单数据补充赋值

        if (payableOrderList.Any() || refundPayableOrderList.Any())
        {
            //查询供应商名称
            var supplierIds = new List<long>();
            supplierIds.AddRange(payableOrderList.Select(x => x.SupplierId).Distinct());
            supplierIds.AddRange(refundPayableOrderList.Select(x => x.SupplierId).Distinct());

            var supplierInfo = await _tenantApiCaller.GetSupplierShortInfo(new ShortInfoInput { SupplierIds = supplierIds });

            foreach (var item in payableOrderList)
            {
                var supplierItem = supplierInfo.FirstOrDefault(x => x.SupplierId == item.SupplierId);
                if (supplierItem != null)
                {
                    item.SupplierName = supplierItem.SupplierFullName;
                }

                //产品信息赋值
                if (productDataList.Any())
                {
                    var productItem = productDataList
                        .FirstOrDefault(x => x.subOrderId == item.BusinessOrderId);
                    item.ProductName = productItem.productName;
                    item.ProductSkuName = productItem.skuName;
                }
            }

            foreach (var item in refundPayableOrderList)
            {
                var supplierItem = supplierInfo.FirstOrDefault(x => x.SupplierId == item.SupplierId);
                if (supplierItem != null)
                {
                    item.SupplierName = supplierItem.SupplierFullName;
                }
            }
        }

        #endregion

        return new ValueTuple<List<GetPayableOrderDetailOutput>,
            List<GetPayableOrderDetailOutput>,
            List<GetPayableOrderDetailOutput>,
            List<string>>(
            payableOrderList,
            refundPayableOrderList,
            offsetPayableOrderList,
            allCurrencyCode);
    }

    /// <summary>
    /// 查询抵冲单
    /// </summary>
    /// <param name="orderId">订单id</param>
    /// <param name="orderPaymentType">订单支付类型</param>
    /// <param name="orderType">订单类型</param>
    /// <returns></returns>
    private async Task<List<FinancialOffsetOrderInfo>> GetFinancialOffsetOrderInfo(long orderId,
        OrderPaymentType orderPaymentType,
        OrderType orderType,
        List<(long baseOrderId, long subOrderId, string productName, string skuName)> productDataList)
    {
        var result = new List<FinancialOffsetOrderInfo>();
        var getOffsetOrderRequest = orderPaymentType == OrderPaymentType.OrderPay || orderPaymentType == OrderPaymentType.GroupBookingOrder
            ? new GetOffsetOrderListInput { BaseOrderIds = new List<long> { orderId } }
            : new GetOffsetOrderListInput { BusinessOrderIds = new List<long> { orderId } };

        var offsetOrderList = await _orderApiCaller.GetOffsetOrderList(getOffsetOrderRequest);
        var financialOffsetOrderList = _mapper.Map<List<FinancialOffsetOrderItem>>(offsetOrderList);
        foreach (var item in financialOffsetOrderList)
        {
            item.OffsetAmount = item.Amount;
            item.Amount = Math.Abs(item.Amount);
        }

        //根据产品子单显示，每一子单有一个抵冲单列表
        foreach (var item in productDataList)
        {
            var financialOffsetOrderInfo = new FinancialOffsetOrderInfo
            {
                BaseOrderId = item.baseOrderId,
                BusinessOrderId = item.subOrderId,
                ProductName = item.productName,
                ProductSkuName = item.skuName,
                OffsetOrderItems = financialOffsetOrderList
                        .Where(x => x.BusinessOrderId == item.subOrderId)
                        .OrderByDescending(x => x.CreatTime)
                        .ToList()
            };
            result.Add(financialOffsetOrderInfo);
        }

        return result;
    }

    /// <summary>
    /// 返回预约单已完结分数金额
    /// (单价+预约加价) * 已完结份数
    /// </summary>
    /// <param name="reservationOrderId">预约单id</param>
    /// <returns></returns>
    private async Task<decimal?> GetReservationOrderAmount(long reservationOrderId)
    {
        //查询券码和预约加价信息
        var orderInfo = await _orderApiCaller.GetReservationOrder(reservationOrderId);

        //查询订单价格信息
        var orderPrice = await _orderApiCaller.GetOrderPriceByBaseOrderId(orderInfo.TicketOrderInfo.BaseOrderId);

        //单价
        var price = orderPrice
            .First(x => x.SubOrderId == orderInfo.TicketOrderInfo.TicketOrderId).Price;
        //预约加价
        var amount = orderInfo.Detail.PaymentInfos.Sum(x => x.Amount);
        //已完结份数
        var ticketCodes = orderInfo.Detail.ReservationTicketCodes
            .Where(x => x.Status == ReservationTicketCodeStatus.Finished)
        .SelectMany(x => x.Codes)
            .ToList();
        var quantity = ticketCodes.Count;

        return (price + amount) * quantity;
    }

    /// <summary>
    /// 查询订单支付信息
    /// </summary>
    /// <param name="orderId"></param>
    /// <param name="orderPaymentType"></param>
    /// <returns></returns>
    private async Task<PaymentInfoOutput> GetPaymentInfo(long orderId, OrderPaymentType orderPaymentType)
    {
        switch (orderPaymentType)
        {
            case OrderPaymentType.ReservationOrderPay:
                return await _orderApiCaller.GetReservationOrderPaymentInfo(orderId);
            default:
                return await _orderApiCaller.GetBaseOrderPaymentInfo(orderId);
        }
    }

    /// <summary>
    /// 获取退款单数据
    /// </summary>
    private async Task<List<RefundOrderDetailOutDto>> GetRefundOrder(long orderId, OrderPaymentType orderPaymentType)
    {
        Task<List<RefundOrderDetailOutDto>> task = orderPaymentType switch
        {
            OrderPaymentType.ReservationOrderPay => _orderApiCaller.GetRefundsBySubOrderId(orderId),
            _ => _orderApiCaller.GetRefundsByBaseOrderId(orderId)
        };
        return await task;
    }

}
