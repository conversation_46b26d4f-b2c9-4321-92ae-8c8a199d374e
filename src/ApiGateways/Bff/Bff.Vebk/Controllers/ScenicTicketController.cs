using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.OrderPayment;
using Bff.Vebk.Models.ScenicTicket;
using Bff.Vebk.Services.Interfaces;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Inventory.DTOs;
using Contracts.Common.Order.Enums;
using Contracts.Common.Product.DTOs.OpenSupplier;
using Contracts.Common.Product.DTOs.OpenSupplierProductSyncLog;
using Contracts.Common.Product.Enums;
using Contracts.Common.Resource.DTOs.FileResource;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Scenic.DTOs;
using Contracts.Common.Scenic.DTOs.OpenSupplier;
using Contracts.Common.Scenic.DTOs.Ticket;
using Contracts.Common.Scenic.DTOs.TicketChannelSetting;
using Contracts.Common.Scenic.DTOs.TicketsCalendarPrice;
using Contracts.Common.Scenic.DTOs.TicketSupplierSetting;
using Contracts.Common.Scenic.DTOs.TicketTimeSlot;
using Contracts.Common.Scenic.Enums;
using Contracts.Common.Tenant.DTOs.Supplier;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.Abstract;
using Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Text.Json;

namespace Bff.Vebk.Controllers;

/// <summary>
/// 景点门票
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class ScenicTicketController : ControllerBase
{
    private readonly IScenicSpotApiCaller _scenicApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IOpenPlatformService _openPlatformService;
    private readonly IPaymentService _paymentService;
    private readonly IScenicTicketService _scenicTicketService;
    private readonly IProductApiCaller _productApiCaller;
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IMapper _mapper;
    private readonly ILogger<ScenicTicketController> _logger;
    
    private readonly IOpenSupplierOrderService _openSupplierOrderService;

    public ScenicTicketController(
        IScenicSpotApiCaller scenicApiCaller,
        ITenantApiCaller tenantApiCaller,
        IMapper mapper,
        IOpenPlatformService openPlatformService,
        IPaymentService paymentService,
        IScenicTicketService scenicTicketService,
        IProductApiCaller productApiCaller,
        IResourceApiCaller resourceApiCaller,
        IHttpClientFactory httpClientFactory,
        ILogger<ScenicTicketController> logger,
        IOpenSupplierOrderService openSupplierOrderService)
    {
        _scenicApiCaller = scenicApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _mapper = mapper;
        _openPlatformService = openPlatformService;
        _paymentService = paymentService;
        _scenicTicketService = scenicTicketService;
        _productApiCaller = productApiCaller;
        _resourceApiCaller = resourceApiCaller;
        _httpClientFactory = httpClientFactory;
        _logger = logger;
        
        _openSupplierOrderService = openSupplierOrderService;
    }

    /// <summary>
    /// 门票新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200,typeof(long))]
    [SwaggerResponseExt(default, ErrorTypes.Common.ResourceInvalid)]
    public async Task<IActionResult> Add(AddTicketBffInput input)
    {
        _ = HttpContext.User.TryParseUserInfo<CurrentUser>(out var user);
        input.DevelopUserId ??= user.UserId;
        var addRequest = _mapper.Map<AddTicketInput>(input);
        var addResponse = await _scenicApiCaller.AddTicket(addRequest);
        return Ok(addResponse.TicketId);
    }
    
    /// <summary>
    /// 门票编辑
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Common.ResourceInvalid,ErrorTypes.Tenant.SupplierInvalid)]
    public async Task<IActionResult> Edit(EditTicketBffInput input)
    {
        _ = HttpContext.User.TryParseUserInfo<CurrentUser>(out var user);
        var editRequest = _mapper.Map<EditTicketInput>(input);
        var editResponse = await _scenicApiCaller.EditTicket(editRequest);
        return Ok();
    }

    
    /// <summary>
    /// 设置供应商配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Scenic.ScenicTicketsInvalid,
        ErrorTypes.Scenic.ScenicSpotInvalid,ErrorTypes.Common.NotSupportedOperation,ErrorTypes.Tenant.SupplierInvalid)]
    public async Task<IActionResult> SetSupplierSetting(SetTicketSupplierSettingBffInput input)
    {
        _ = HttpContext.User.TryParseUserInfo<CurrentUser>(out var user);
        var request = _mapper.Map<SetTicketSupplierSettingInput>(input);
        var response = await _scenicApiCaller.SetSupplierSetting(request);
        //后台异步执行请求.不等待价库同步的结果
        _ = _scenicApiCaller.SyncThirdPriceInventory(new SyncThirdPriceInventoryInput
        {
            TicketId = input.TicketId,
            SkuId = response.SkuId,
            OpenSupplierType = response.OpenSupplierType,
            EditTicketSyncInfo = response.EditTicketSyncInfo
        });
        if (response.OpenSupplierType != OpenSupplierType.System)
        {
            _ = _productApiCaller.SyncOpenSupplierExtraInfo(new SyncOpenSupplierExtraInfoInput
            {
                TenantIds = new List<long> {user.Tenant},
                OpenSupplierType = response.OpenSupplierType,
                ProductId = response.ActivityId,
                OptionId = response.PackageId,
                SkuId = response.SkuId
            });
        }

        return Ok();
    }

    /// <summary>
    /// 查询供应商配置
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200,typeof(GetTicketSupplierSettingBffOutput))]
    public async Task<IActionResult> GetSupplierSetting(long id)
    {
        var response = await _scenicApiCaller.GetSupplierSetting(id);
        var result = _mapper.Map<GetTicketSupplierSettingBffOutput>(response);
        if (result.CredentialSourceType == CredentialSourceType.InterfaceDock)
        {
            //查询供应商信息
            var supplierInfo = (await _tenantApiCaller.QuerySuppliers(new QuerySuppliersInput
                {
                    SupplierIds = new List<long> { result.SupplierId }, SupplierType = SupplierType.Api
                }))
                .FirstOrDefault();
            if (supplierInfo != null)
            {
                //查询第三方产品详情
                var productDetail = await _productApiCaller.GetOpenSupplierProductDetail(
                    new GetOpenSupplierProductDetailInput
                    {
                        OutProductId = response.ActivityId,
                        OutProductOptionId = result.PackageId,
                        OutSkuId = response.SkuId,
                        SupplierApiType = supplierInfo.SupplierApiType!.Value
                    });
                var sku = productDetail.SkuList.FirstOrDefault(x => x.OutSkuId == response.SkuId);
                result.CostDiscountRate = sku?.CommissionRate ?? 0m;
                result.IsInstant = productDetail?.Instant ?? false;
                result.NeedTravelerInfo = sku?.NeedTravelerInfo ?? false;
                result.HasContent = productDetail?.HasContent ?? false;


                // 查询saas维护的产品附加信息
                var openSupplierType =
                    _openPlatformService.MapSupplierApiTypeToOpenSupplierType(supplierInfo.SupplierApiType!.Value);
                var extraInfoRequest = new QueryOpenSupplierSkuExtraInfoInput()
                {
                    OpenSupplierTypes = new List<OpenSupplierType> { openSupplierType },
                    ProductIds = new List<string> { response.ActivityId }
                };
                if (!string.IsNullOrEmpty(response.PackageId))
                    extraInfoRequest.OptionIds.Add(response.PackageId);

                if (!string.IsNullOrEmpty(response.SkuId))
                    extraInfoRequest.SkuIds.Add(response.SkuId);

                var extraInfo =
                    (await _productApiCaller.QueryOpenSupplierSkuExtraInfo(extraInfoRequest)).FirstOrDefault();
                result.IsNeedExtraInfo = extraInfo?.Sub
                    .SelectMany(x => x.ExtraInfos).Any() ?? false;
            }
        }
        return Ok(result);
    }

    /// <summary>
    /// 设置渠道配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Scenic.ScenicTicketsInvalid, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> SetChannelSetting(SetTicketChannelSettingBffInput input)
    {
        var request = _mapper.Map<SetTicketChannelSettingInput>(input);
        var response = await _scenicApiCaller.SetChannelSetting(request);
        if (response.CredentialSourceType == CredentialSourceType.InterfaceDock)
        {
            // 价库变更推送渠道
            _ = _scenicApiCaller.SetChannelSettingSyncPriceStocks(new SetChannelSettingSyncPriceStocksInput
            {
                TicketId = response.TicketId,
                PushEmptyOtaChannelSettings = response.PushEmptyOtaChannelSettings,
                NewSyncOtaChannels = response.NewSyncOtaChannels
            });
        }
        return Ok();
    }

    /// <summary>
    /// 查询渠道配置
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200,typeof(GetTicketChannelSettingBffOutput))]
    public async Task<IActionResult> GetChannelSetting(long id)
    {
        var response = await _scenicApiCaller.GetChannelSetting(id);
        var result = _mapper.Map<GetTicketChannelSettingBffOutput>(response);
        return Ok(result);
    }
    
    /// <summary>
    /// 分页查询景点门票分页基础数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(PagingModel<SearchTicketsBffOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchTicketsBffInput input)
    {
        var request = _mapper.Map<SearchTicketsInput>(input);
        
        //判断入参 skuId
        if (!string.IsNullOrWhiteSpace(input.TimeSlotId))
        {
            if (!long.TryParse(input.TimeSlotId, out var skuId))
            {
                return Ok(new PagingModel<SearchTicketsBffOutput>
                {
                    PageIndex = input.PageIndex,
                    PageSize = input.PageSize
                });
            }
            request.TimeSlotIds.Add(skuId);
        }
        var response = await _scenicApiCaller.SearchTicket(request);
        var result = _mapper.Map<PagingModel<SearchTicketsBffOutput>>(response);
        if (!result.Data.Any()) return Ok(result);
        
        //查询供应商
        var supplierIds = result.Data.Where(x => x.SupplierId > 0)
            .Select(x => x.SupplierId)
            .Distinct();
        var supplierInfos = await _tenantApiCaller.GetSupplierShortInfo(new ShortInfoInput
        {
            SupplierIds = supplierIds,
            IsEnabled = false
        });
        
        foreach (var item in result.Data)
        {
            item.SupplierName = supplierInfos.FirstOrDefault(x => x.SupplierId == item.SupplierId)?.SupplierFullName;
        }
        
        return Ok(result);
    }

    /// <summary>
    /// 查询可预订日期
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200,typeof(List<QueryAvailableDateTimesBffOutput>))]
    public async Task<IActionResult> QueryAvailableDateTimes(QueryAvailableDateTimesBffInput input)
    {
        var result = new List<QueryAvailableDateTimesBffOutput>();
        if (input.TicketId.Any() is false) return Ok();
        
        var availableDateTimes = await _scenicApiCaller.GetScenicTicketAvailableDateTimes(input.TicketId.ToArray());
        foreach (var item in input.TicketId)
        {
            var lastCanReservationDate = availableDateTimes.FirstOrDefault(x => x.TicketId == item)
                ?.LatestDateTime?.ToString("yyyy-MM-dd");
            var recentCanReservationDate = availableDateTimes.FirstOrDefault(x => x.TicketId == item)
                ?.MostRecentDateTime?.ToString("yyyy-MM-dd");
            
            result.Add(new QueryAvailableDateTimesBffOutput
            {
                TicketId = item,
                LastCanReservationDate = lastCanReservationDate,
                RecentCanReservationDate = recentCanReservationDate
            });
        }

        return Ok(result);
    }
    
    /// <summary>
    /// 获取期票价格设置信息
    /// </summary>
    /// <param name="ticketsId">门票id</param>
    /// <returns></returns>
    [Authorize]
    [HttpGet]
    [ProducesResponseType(typeof(GetPromissoryNoteTicketPriceBffOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetPromissoryNoteTicketPrice(long ticketsId)
    {
        var response = (await _scenicApiCaller.GetTicketPriceList(new[] { ticketsId })).FirstOrDefault();
        var result = _mapper.Map<GetPromissoryNoteTicketPriceBffOutput>(response);
        return Ok(result);
    }
    
    /// <summary>
    /// 获取预订票日历价格设置预览
    /// </summary>
    /// <param name="ticketsId">门票id</param>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<SearchCalendarPricePreviewBffOutput>), (int)HttpStatusCode.OK)]
    [Authorize]
    public async Task<IActionResult> SearchCalendarPricePreview(long ticketsId)
    {
        var response = await _scenicApiCaller.SearchTicketCalendarPricePreview(new long[] { ticketsId });
        var result = _mapper.Map<IEnumerable<SearchCalendarPricePreviewBffOutput>>(response);
        return Ok(result);
    }
    
    
    /// <summary>
    /// 查询景点门票日历价格库存信息
    /// - 系统通用价库
    /// - 时段日历价库
    /// - 门票日历价库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    [SwaggerResponseExt(200, typeof(SearchCalendarBffOutput))]
    public async Task<IActionResult> SearchCalendarPrice(SearchCalendarBffInput input)
    {
        var result = new SearchCalendarBffOutput
        {
            TicketId = input.TicketId
        };

        //查询门票信息
        var ticket = await _scenicApiCaller.GetScenicTicketDetail(input.TicketId);
        if (ticket == null)
            throw new BusinessException(ErrorTypes.Scenic.ScenicTicketsInvalid);

        //查询门票日历价格信息
        var responses = await _scenicApiCaller.SearchTicketCalendarPrice(new SearchTicketCalendarInput
        {
            ScenicSpotId = input.ScenicSpotId,
            TicketIds = new[] { input.TicketId },
            TimeSlotIds = input.TimeSlotId.HasValue ? new[] { input.TimeSlotId.Value } : Array.Empty<long>(),
            BeginDate = input.BeginDate,
            EndDate = input.EndDate
        });
        var ticketCalendars = responses.ToList();
        if (ticketCalendars.Any() is false)
            return Ok(result);

        //查询库存信息
        var inventoryResponse = await _scenicApiCaller.GenericScenicInventoryQuery(new GenericScenicInventoryQueryInput
        {
            ScenicSpotId = input.ScenicSpotId,
            TicketIds = new List<long>{ input.TicketId },
            TimeSlotId = input.TimeSlotId,
            StartDate = input.BeginDate,
            EndDate = input.EndDate
        });
        var inventories = inventoryResponse.Where(s => s.TicketId == input.TicketId).ToList();

        decimal exchangeRate = 1;
        var agencyCurrency = ticket.SaleCurrencyCode;
        if (input.AgencyId.HasValue)
        {
            //查询新分销商的币种.需要重算币种汇率
            var agencyCurrencyExchangeRate = await _paymentService.GetAgencyCurrencyExchangeRate(new GetAgencyCurrencyExchangeRateInput
            {
                AgencyId = input.AgencyId!.Value,
                BaseCurrencyCode = ticket.SaleCurrencyCode
            });
            agencyCurrency = agencyCurrencyExchangeRate.SaleCurrencyCode;
            exchangeRate = agencyCurrencyExchangeRate.ExchangeRate;
        }
        
        
        foreach (var ticketCalendarItem in ticketCalendars)
        {
            foreach (var calendarItem in ticketCalendarItem.Calendars)
            {
                if (ticket.NeedToBuyInAdvance)
                {
                    if (!CheckAdvanceTime(calendarItem.Date, ticket.BuyDaysInAdvance, ticket.BuyTimeInAdvance))
                        continue;
                }

                //兼容系统期票通用库存
                var inventory = ticketCalendarItem.IsSystemPromissoryNoteTicket
                    ? inventories?.FirstOrDefault()
                    : inventories?.FirstOrDefault(i => i.Date == calendarItem.Date);

                var sellingPrice = calendarItem.SellingPrice;
                var saleCurrencyCode = calendarItem.SaleCurrencyCode;
                
                //汇率换算
                if (input.AgencyId.HasValue)
                {
                    //分销商币种
                    saleCurrencyCode = agencyCurrency;
                    //汇率换算
                    if (calendarItem.SellingPrice.HasValue)
                        sellingPrice = Math.Round(calendarItem.SellingPrice.Value * exchangeRate, 2);
                }

                result.Calendars.Add(new TicketCalendarBffOutput
                {
                    Date = calendarItem.Date,
                    SellingPrice = sellingPrice,
                    CostPrice = calendarItem.CostPrice,
                    LinePrice = calendarItem.LinePrice ?? 0,
                    AvailableQuantity = inventory?.AvailableQuantity ?? 0,
                    Enabled = inventory?.Enabled ?? false,
                    SaleCurrencyCode = saleCurrencyCode,
                    TimeSlotId = ticketCalendarItem.TimeSlotId,
                    TimeSlotName = ticketCalendarItem.TimeSlotName
                });
            }
        }
        return Ok(result);
    }

    /// <summary>
    /// 查询系统通用价库价格库存状态信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    [SwaggerResponseExt(200, typeof(SearchSinglePriceBffOutput))]
    public async Task<IActionResult> SearchSinglePrice(SearchSinglePriceBffInput input)
    {
        var result = new SearchSinglePriceBffOutput
        {
            TicketId = input.TicketId
        };
        var responses = await _scenicApiCaller.SearchTicketCalendarPrice(new SearchTicketCalendarInput
        {
            ScenicSpotId = input.ScenicSpotId,
            TicketIds = new long[] { input.TicketId },
            BeginDate = DateTime.Today,
            EndDate = DateTime.Today
        });

        var inventoryResponse = await _scenicApiCaller.GenericScenicInventoryQuery(new GenericScenicInventoryQueryInput
        {
            ScenicSpotId = input.ScenicSpotId,
            TicketIds = new List<long>{ input.TicketId },
        });

        var ticketPrice = responses.FirstOrDefault(x => x.TicketId == input.TicketId);
        var outputs = ticketPrice?.Calendars.Select(s =>
        {
            var inv = inventoryResponse.FirstOrDefault(i => i.TicketId == ticketPrice.TicketId);
            return new SearchSinglePriceBffOutput
            {
                TicketId = ticketPrice.TicketId,
                LinePrice = s.LinePrice,
                CostPrice = s.CostPrice,
                SellingPrice = s.SellingPrice,
                AvailableQuantity = inv?.AvailableQuantity ?? 0,
                Enabled = inv?.Enabled ?? false,
                SaleCurrencyCode = s.SaleCurrencyCode
            };
        }).ToList();
        var output = outputs?.FirstOrDefault(x => x.TicketId == input.TicketId);
        output ??= result;
        return Ok(output);
    }


    /// <summary>
    /// 查询第三方价库数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(List<SearchThirdPriceInventoryBffOutput>))]
    public async Task<IActionResult> SearchThirdPriceInventory(SearchThirdPriceInventoryBffInput input)
    {
        var result = new List<SearchThirdPriceInventoryBffOutput>();
        if (input.BeginDate < DateTime.Today)
            input.BeginDate = DateTime.Today.AddDays(-1);
        
        //查询门票信息
        var ticketInfos = await _scenicApiCaller.GetTicketInfoByIds(input.TicketIds.ToArray());
        if (ticketInfos.Any() is false)
            return Ok(result);

        if (input.TimeSlotIds.Any() is false)
        {
            input.TimeSlotIds = ticketInfos.SelectMany(x => x.TimeSlotInfos)
                .Select(x => x.TimeSlotId)
                .ToList();
        }

        var scenicSpotId = ticketInfos.First().ScenicSpotId;
        //查询门票日历日历价格信息
        var calendarPrices = await _scenicApiCaller.SearchTicketCalendarPrice(new SearchTicketCalendarInput
        {
            ScenicSpotId = scenicSpotId,
            TicketIds = input.TicketIds,
            TimeSlotIds = input.TimeSlotIds,
            BeginDate = input.BeginDate,
            EndDate = input.EndDate,
            PriceChannelType = input.PriceChannelType!.Value
        });
        
        var earlyWarningItems = new List<InventoryEarlyWarningItem>();
        #region 非时段-第三方同步库存

        var nonTimeSlotTicketIds = ticketInfos.Where(x =>
                x.PriceInventorySource != PriceInventorySource.System &&
                x.PriceInventoryType == PriceInventoryType.DailyCalendar)
            .Select(x => x.Id)
            .ToList();

        var nonTimeSlotCalendarInventories = await _scenicTicketService.FetchNonTimeSlotInventories(
            beginDate: input.BeginDate,
            endDate: input.EndDate,
            scenicSpotId: scenicSpotId,
            nonTimeSlotTicketIds: nonTimeSlotTicketIds,
            earlyWarningItems: earlyWarningItems);

        #endregion

        #region 时段-第三方同步库存

        var timeSlotTicketIds = ticketInfos.Where(x =>
                x.PriceInventorySource != PriceInventorySource.System &&
                x.PriceInventoryType == PriceInventoryType.TimeSlotCalendar)
            .Select(x => x.Id)
            .ToList();

        var timeSlotCalendarPriceInventories = await _scenicTicketService.FetchTimeSlotInventories(
            beginDate: input.BeginDate,
            endDate: input.EndDate,
            scenicSpotId: scenicSpotId,
            timeSlotTicketIds: timeSlotTicketIds,
            timeSlotIds: input.TimeSlotIds,
            earlyWarningItems: earlyWarningItems);
        
        #endregion

        result = _scenicTicketService.BuildThirdPriceInventoryOutput(
            input, 
            ticketInfos, 
            calendarPrices,
            earlyWarningItems, 
            nonTimeSlotCalendarInventories, 
            timeSlotCalendarPriceInventories);
        
        //过滤
        if (input.InventoryWarningType.HasValue)
        {
            result = result.Where(x => x.InventoryWarningType == input.InventoryWarningType).ToList();
        }

        return Ok(result);
    }

    /// <summary>
    /// 批量更新第三方价库产品的日历价库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt(200)]
    public async Task<IActionResult> UpdateThirdCalendarPrice(UpdateThirdCalendarPriceBffInput input)
    {
        var request = _mapper.Map<UpdateThirdCalendarPriceInput>(input);
        await _scenicApiCaller.UpdateThirdCalendarPrice(request);
        return Ok();
    }

    /// <summary>
    /// 第三方时段数据检查
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt(200, typeof(CheckThirdTimeSlotBffOutput))]
    [SwaggerResponseExt(default, ErrorTypes.Scenic.NonTimeSlot)]
    public async Task<IActionResult> CheckThirdTimeSlot(CheckThirdTimeSlotBffInput input)
    {
        var result = new CheckThirdTimeSlotBffOutput();

        //校验供应商
        var supplierInfo = await _tenantApiCaller.GetSupplierDetail(input.SupplierId);
        if (supplierInfo is null)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        //转换供应商类型
        var supplierApiType = supplierInfo.SupplierApiSetting.SupplierApiType!.Value;
        OpenSupplierType openSupplierType = _openPlatformService.MapSupplierApiTypeToOpenSupplierType(supplierApiType);

        var checkThirdTimeSlotRequest = _mapper.Map<CheckThirdTimeSlotInput>(input);
        checkThirdTimeSlotRequest.OpenSupplierType = openSupplierType;
        var checkThirdTimeSlotResponse = await _scenicApiCaller.CheckThirdTimeSlot(checkThirdTimeSlotRequest);
        if (checkThirdTimeSlotResponse.Any() is false)
        {
            //如果不分时段，在“分时段库存”开关后显示提示文字“当前产品不分时段
            throw new BusinessException(ErrorTypes.Scenic.NonTimeSlot);
        }

        result.TimeSlots = _mapper.Map<List<CheckThirdTimeSlotBffItem>>(checkThirdTimeSlotResponse);
        return Ok(result);
    }

    /// <summary>
    /// 查询门票的时段信息
    /// </summary>
    /// <param name="ticketId"></param>
    /// <returns></returns>
    [Authorize]
    [HttpGet]
    [SwaggerResponseExt(200, typeof(List<GetTicketTimeSlotBffOutput>))]
    public async Task<IActionResult> GetTicketTimeSlots(long ticketId)
    {
        var response = await _scenicApiCaller.GetTimeSlotsByIds(new GetTicketTimeSlotInput
        {
            TicketIds = new[] { ticketId }
        });
        var result = response.Select(x => new GetTicketTimeSlotBffOutput
        {
            TicketId = ticketId,
            TimeSlotId = x.TimeSlotId,
            TimeSlotName = x.TimeSlotName
        })
            .ToList();
        return Ok(result);
    }

    /// <summary>
    /// 查询门票列表数据(非分页)
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt(200, typeof(IEnumerable<TicketListQueryBffOutput>))]
    public async Task<IActionResult> QueryList(TicketListQueryBffInput input)
    {
        var request = _mapper.Map<TicketListQueryInput>(input);
        var response = await _scenicApiCaller.QueryScenicTicketList(request);
        var result = _mapper.Map<IEnumerable<TicketListQueryBffOutput>>(response);
        return Ok(result);
    }

    #region 共用

    /// <summary>
    /// 校验预订门票提前预订时间
    /// </summary>
    /// <param name="travelDate">出行日期</param>
    /// <param name="buyDaysInAdvance">提前天数</param>
    /// <param name="buyTimeInAdvance">时分</param>
    /// <returns>true:可以预订, false:无法预订</returns>
    private bool CheckAdvanceTime(DateTime travelDate, int buyDaysInAdvance, TimeSpan buyTimeInAdvance)
    {
        //提前X天.在X时间前可购买
        var advanceTime = travelDate
            .AddDays(-buyDaysInAdvance)
            .Add(buyTimeInAdvance);
        var nowTime = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd HH:mm"));
        //判断是否超过提前预订时间
        var checkResult = advanceTime >= nowTime;
        return checkResult;
    }
    #endregion

    /// <summary>
    /// 查询门票下拉框（带景点条件）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(List<TicketSelectionByVebkOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetSelection(TicketSelectionByVebkInput input)
    {
        input.PageIndex = 1;
        input.PageSize = 20;
        var result = await _scenicApiCaller.TicketSelection(input);
        var supplierIds = result.Select(o => o.SupplierId).Distinct().ToList();

        var suppliers = await _tenantApiCaller.GetSupplierShortInfo(new Contracts.Common.Tenant.DTOs.Supplier.ShortInfoInput
        {
            SupplierIds = supplierIds,
            IsEnabled = true
        });

        result.ForEach(x =>
        {
            x.SupplierName = suppliers.FirstOrDefault(o => o.SupplierId == x.SupplierId)?.SupplierFullName;
        });

        return Ok(result);
    }

    /// <summary>
    /// 分页查询景点门票关联列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200,typeof(PagingModel<GetScenicAndTicketsBffOutput>))]
    public async Task<IActionResult> GetScenicAndTickets(GetScenicAndTicketsBffInput input)
    {
        var request = _mapper.Map<GetScenicAndTicketsInput>(input);
        var response = await _scenicApiCaller.GetScenicAndTickets(request);
        var result = _mapper.Map<PagingModel<GetScenicAndTicketsBffOutput>>(response);
        if (result.Data.Any())
        {
            var supplierIds = result.Data.Select(o => o.SupplierId).Distinct().ToList();
            var suppliers = await _tenantApiCaller.GetSupplierShortInfo(new ShortInfoInput
            {
                SupplierIds = supplierIds,
                IsEnabled = false
            });
            foreach (var item in result.Data)
            {
                var supplier = suppliers.FirstOrDefault(o => o.SupplierId == item.SupplierId);
                item.SupplierName = supplier?.SupplierFullName;
            }
        }
        return Ok(result);
    }

    /// <summary>
    /// 获取门票详情
    /// </summary>
    /// <param name="ticketId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200, typeof(GetTicketDetailBffOutput))]
    public async Task<IActionResult> Detail(long id)
    {
        var response = await _scenicApiCaller.GetScenicTicketDetail(id);
        var result = _mapper.Map<GetTicketDetailBffOutput>(response);
        if (result == null) return Ok(new GetTicketDetailBffOutput());

        if (result is { CredentialSourceType: CredentialSourceType.InterfaceDock, SupplierId: >0 })
        {
            //查询供应商信息
            var supplierInfo = (await _tenantApiCaller.QuerySuppliers(new QuerySuppliersInput
            {
                SupplierIds = new List<long>{result.SupplierId},
                SupplierType = SupplierType.Api
            }))
            .FirstOrDefault();
            if (supplierInfo != null)
            {
                // 查询saas维护的产品附加信息
                var openSupplierType =
                    _openPlatformService.MapSupplierApiTypeToOpenSupplierType(supplierInfo.SupplierApiType!.Value);
                var extraInfoRequest = new QueryOpenSupplierSkuExtraInfoInput()
                {
                    OpenSupplierTypes = new List<OpenSupplierType>{openSupplierType},
                    ProductIds = new List<string> {response.ActivityId}
                };
                if(!string.IsNullOrEmpty(response.PackageId))
                    extraInfoRequest.OptionIds.Add(response.PackageId);
                
                if(!string.IsNullOrEmpty(response.SkuId))
                    extraInfoRequest.SkuIds.Add(response.SkuId);

                var extraInfo = (await _productApiCaller.QueryOpenSupplierSkuExtraInfo(extraInfoRequest)).FirstOrDefault();
                result.IsNeedExtraInfo = extraInfo?.Sub
                    .SelectMany(x=>x.ExtraInfos).Any() ?? false;
            }
        }
        return Ok(result);
    }

    /// <summary>
    /// 更新门票采购比例
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200)]
    public async Task<IActionResult> UpdateCostDiscountRate(UpdateTicketCostDiscountRateBffInput input)
    {
        var request = new UpdateTicketCostDiscountRateInput();
        request.Items.Add(new UpdateTicketCostDiscountRateInputItem
        {
            TicketId = input.TicketId,
            CostDiscountRate = input.CostDiscountRate
        });
        await _scenicApiCaller.UpdateCostDiscountRate(request);
        return Ok();
    }

    /// <summary>
    /// 获取API供应商产品内容
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200,typeof(GetOpenSupplierProductContentBffOutput))]
    public async Task<IActionResult> GetOpenSupplierProductContent(GetOpenSupplierProductContentBffInput input)
    {
        _ = HttpContext.User.TryParseUserInfo<CurrentUser>(out var user);
        var supplierInfo = (await _tenantApiCaller.QuerySuppliers(new QuerySuppliersInput
        {
            SupplierIds = new List<long>{input.SupplierId},
            SupplierType = SupplierType.Api
        })).FirstOrDefault();
        if (supplierInfo == null)
            throw new BusinessException(ErrorTypes.Tenant.SupplierInvalid);
        var productDetail = await _productApiCaller.GetOpenSupplierProductContent(new GetOpenSupplierProductContentInput
        {
            OutProductId = input.ActivityId,
            SupplierApiType = supplierInfo.SupplierApiType!.Value,
            LanguageType = input.LanguageType
        });
        var syncResult = productDetail.Code == 200
            ? OpenSupplierProductSyncResult.Success
            : OpenSupplierProductSyncResult.Failed;
        var businessErrorType = ErrorTypes.Product.SupplierNotProvideChineseInfo;
        var failedMessage = productDetail.Code == 200 ? null : productDetail.Msg;
        var skuDetail = productDetail.SkuList
            .FirstOrDefault(x => x.OutProductOptionId == input.PackageId && x.OutSkuId == input.SkuId);
        var result = new GetOpenSupplierProductContentBffOutput();
        if (skuDetail != null)
        {
            result = new GetOpenSupplierProductContentBffOutput
            {
                Highlights = productDetail.Highlight,
                Address = productDetail.PickUpLocation,
                ProductDetails = productDetail.Description,
                FeeNote = skuDetail.Inclusions,
                FeeNotNote = skuDetail.Exclusions,
                BusinessHours = skuDetail.OpenHours,
                ValidityDescription = skuDetail.UsageValidity,
                Precautions = skuDetail.AgeLimit,
                UsageInstructions = skuDetail.HowToUse,
                CancellationPolicy = skuDetail.CancelPolicy,
            };

            // 名称拼接 
            result.Name = productDetail.Title;
            if (!string.IsNullOrEmpty(skuDetail.OutProductOptionName))
            {
                result.Name = JoinNameParts(result.Name, skuDetail.OutProductOptionName);
            }

            if (!string.IsNullOrEmpty(skuDetail.OutSkuName))
            {
                result.Name = JoinNameParts(result.Name, skuDetail.OutSkuName);
            }

            if (productDetail.Images?.Any() == true)
            {
                result.Pictures =
                    await _openSupplierOrderService.ThrottlerDownloadImages(productDetail.Images, user.Tenant);
            }
        }
        else
        {
            if (productDetail.Code == 200)
            {
                syncResult = OpenSupplierProductSyncResult.Failed;
                failedMessage = "查无编码关联数据";
            }
        }

        // 返回同步字段信息
        result.SyncFields = GetSyncFields(productDetail,input.PackageId,input.SkuId);
        
        // 添加同步日志
        _ = _productApiCaller.AddOpenSupplierProductSyncLog(new AddProductSyncLogInput
        {
            ProductType = ProductType.Scenic,
            ProductId = input.TicketId,
            OpenSupplierType = _openPlatformService.MapSupplierApiTypeToOpenSupplierType(supplierInfo.SupplierApiType!.Value),
            SyncResult = syncResult,
            FailedMessage = failedMessage,
            UserId = user.UserId,
            UserName = user.NickName
        });

        if (syncResult == OpenSupplierProductSyncResult.Failed)
            throw new BusinessException(businessErrorType);
        
        return Ok(result);
    }

    /// <summary>
    /// 获取供应商产品同步字段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200,typeof(List<GetOpenSupplierProductSyncFieldBffOutput>))]
    public async Task<IActionResult> GetOpenSupplierProductSyncField(GetOpenSupplierProductSyncFieldBffInput input)
    {
        var supplierInfo = (await _tenantApiCaller.QuerySuppliers(new QuerySuppliersInput
        {
            SupplierIds = new List<long>{input.SupplierId},
            SupplierType = SupplierType.Api
        })).FirstOrDefault();
        if (supplierInfo == null)
            throw new BusinessException(ErrorTypes.Tenant.SupplierInvalid);
        var productDetail = await _productApiCaller.GetOpenSupplierProductContent(new GetOpenSupplierProductContentInput
        {
            OutProductId = input.ActivityId,
            SupplierApiType = supplierInfo.SupplierApiType!.Value,
            LanguageType = input.LanguageType
        });
        if (productDetail.Code != 200)
        {
            throw new BusinessException(ErrorTypes.Scenic.QueryThirdProductDetailError);
        }
        var result = GetSyncFields(productDetail,input.PackageId,input.SkuId);
        return Ok(result);
    }


    #region private

    // api 供应商产品内容 - 名称拼接
    private string JoinNameParts(string currentName, string newPart)
    {
        return string.IsNullOrEmpty(currentName) 
            ? newPart 
            : $"{currentName}/{newPart}";
    }
    
    private List<GetOpenSupplierProductSyncFieldBffOutput> GetSyncFields(GetOpenSupplierProductContentOutput productDetail,string? packageId,string? skuId)
    {
        var result = new List<GetOpenSupplierProductSyncFieldBffOutput>();
        var skuDetail = productDetail.SkuList
            .FirstOrDefault(x => x.OutProductOptionId == packageId && x.OutSkuId == skuId);
        var mappingConfigs = OpenSupplierProductSyncFieldMappingConfig.Mappings;
        foreach (var moduleType in Enum.GetValues<OpenSupplierProductModuleType>())
        {
            var mappings = mappingConfigs.Where(x => x.ModuleType == moduleType).ToList();
            var mappingFields = mappings.Select(x => x.FieldType).ToList();
            foreach (var fieldType in mappingFields)
            {
                var mappingItem = mappings.FirstOrDefault(x => x.FieldType == fieldType);
                var resultItem = new GetOpenSupplierProductSyncFieldBffOutput
                {
                    ModuleType = moduleType,
                    ModuleName = moduleType.GetDescription(),
                    FieldName = fieldType.GetDescription(),
                    FieldKey = JsonNamingPolicy.CamelCase.ConvertName(fieldType.ToString()),// 返回驼峰
                    IsRequired = mappingItem?.IsRequired ?? false
                };
                switch (fieldType)
                {
                    case OpenSupplierProductSystemFieldType.Pictures:

                        resultItem.IsReturned = productDetail.Images?.Any() == true;

                        break;
                    case OpenSupplierProductSystemFieldType.Name:

                        resultItem.IsReturned = !string.IsNullOrEmpty(productDetail.Title) ||
                                                !string.IsNullOrEmpty(skuDetail?.OutProductOptionName) ||
                                                !string.IsNullOrEmpty(skuDetail?.OutSkuName);

                        break;
                    case OpenSupplierProductSystemFieldType.Highlights:

                        resultItem.IsReturned = !string.IsNullOrEmpty(productDetail.Highlight);

                        break;
                    case OpenSupplierProductSystemFieldType.BusinessHours:

                        resultItem.IsReturned = !string.IsNullOrEmpty(skuDetail?.OpenHours);

                        break;
                    case OpenSupplierProductSystemFieldType.ScenicNotice:

                        resultItem.IsReturned = false;

                        break;
                    case OpenSupplierProductSystemFieldType.Address:

                        resultItem.IsReturned = !string.IsNullOrEmpty(productDetail.PickUpLocation);

                        break;
                    case OpenSupplierProductSystemFieldType.ProductDetails:

                        resultItem.IsReturned = !string.IsNullOrEmpty(productDetail.Description);

                        break;
                    case OpenSupplierProductSystemFieldType.FeeNote:

                        resultItem.IsReturned = !string.IsNullOrEmpty(skuDetail?.Inclusions);

                        break;
                    case OpenSupplierProductSystemFieldType.FeeNotNote:

                        resultItem.IsReturned = !string.IsNullOrEmpty(skuDetail?.Exclusions);

                        break;
                    case OpenSupplierProductSystemFieldType.ValidityDescription:

                        resultItem.IsReturned = !string.IsNullOrEmpty(skuDetail?.UsageValidity);

                        break;
                    case OpenSupplierProductSystemFieldType.Precautions:

                        resultItem.IsReturned = !string.IsNullOrEmpty(skuDetail?.AgeLimit);

                        break;
                    case OpenSupplierProductSystemFieldType.UsageInstructions:

                        resultItem.IsReturned = !string.IsNullOrEmpty(skuDetail?.HowToUse);

                        break;
                    case OpenSupplierProductSystemFieldType.OtherInstructions:

                        resultItem.IsReturned = false;

                        break;
                    case OpenSupplierProductSystemFieldType.CancellationPolicy:

                        resultItem.IsReturned = !string.IsNullOrEmpty(skuDetail?.CancelPolicy);

                        break;
                }

                result.Add(resultItem);
            }
        }

        return result;
    }

    #endregion
}