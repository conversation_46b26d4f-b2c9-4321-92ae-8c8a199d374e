using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.LineProduct;
using Bff.Vebk.Models.ScenicTicket;
using Bff.Vebk.Services.Interfaces;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs.Insure;
using Contracts.Common.Product.DTOs.LineProduct;
using Contracts.Common.Product.DTOs.LineProductOpenChannelSetting;
using Contracts.Common.Product.DTOs.LineProductOpenSupplierSetting;
using Contracts.Common.Product.DTOs.OpenSupplier;
using Contracts.Common.Product.DTOs.OpenSupplier.Attributes;
using Contracts.Common.Product.DTOs.OpenSupplierProductSyncLog;
using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.DTOs.OpenSupplier;
using Contracts.Common.Tenant.DTOs.Supplier;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.Abstract;
using Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using GetOpenSupplierProductContentBffInput = Bff.Vebk.Models.LineProduct.GetOpenSupplierProductContentBffInput;
using GetOpenSupplierProductContentBffOutput = Bff.Vebk.Models.LineProduct.GetOpenSupplierProductContentBffOutput;

namespace Bff.Vebk.Controllers;

/// <summary>
/// 线路产品信息
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class LineProductController : ControllerBase
{
    private readonly IMapper _mapper;
    private readonly IProductApiCaller _productApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IResourceApiCaller _resourceApiCaller;
    
    private readonly IOpenPlatformService _openPlatformService;
    private readonly IOpenSupplierOrderService _openSupplierOrderService;

    public LineProductController(
        IMapper mapper,
        IProductApiCaller productApiCaller,
        ITenantApiCaller tenantApiCaller,
        IOrderApiCaller orderApiCaller,
        IResourceApiCaller resourceApiCaller,
        IOpenPlatformService openPlatformService,
        IOpenSupplierOrderService openSupplierOrderService)
    {
        _mapper = mapper;
        _productApiCaller = productApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _orderApiCaller = orderApiCaller;
        _resourceApiCaller = resourceApiCaller;
        _openPlatformService = openPlatformService;
        _openSupplierOrderService = openSupplierOrderService;
    }

    /// <summary>
    /// 分页列表查询
    /// </summary>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(PagingModel<SearchLineProductBffOutput, SearchLineProductSummaryBffOutput>))]
    public async Task<IActionResult> Search(SearchLineProductBffInput input)
    {
        var searchInput = _mapper.Map<SearchLineProductInput>(input);
        //判断入参 skuId
        bool isValid = true;

        if (!string.IsNullOrWhiteSpace(input.SkuId))
        {
            isValid &= long.TryParse(input.SkuId, out var skuId);
            if (isValid) searchInput.SkuId = skuId;
        }

        if (!string.IsNullOrWhiteSpace(input.SkuTypeItemId))
        {
            isValid &= long.TryParse(input.SkuTypeItemId, out var skuItemId);
            if (isValid) searchInput.SkuTypeItemId = skuItemId;
        }

        if (!isValid)
        {
            return Ok(new PagingModel<SearchLineProductBffOutput, SearchLineProductSummaryBffOutput>
            {
                PageIndex = input.PageIndex,
                PageSize = input.PageSize
            });
        }
        
        searchInput.NeedSummary = true;
        var searchResponse = await _productApiCaller.SearchLineProduct(searchInput);
        var result = _mapper.Map<PagingModel<SearchLineProductBffOutput, SearchLineProductSummaryBffOutput>>(searchResponse);
        if (result.Data.Any())
        {
            var cityCodes = result.Data.Select(x => x.DestinationCityId).Distinct().ToArray();
            var cities = await _resourceApiCaller.QueryCities(new Contracts.Common.Resource.DTOs.QueryInput
            {
                CityCodes = cityCodes,
            });
            foreach (var d in result.Data)
            {
                if (d.PurchaseSourceType == LineProductPurchaseSourceType.InterfaceDock)
                {
                    foreach (var skuItem in d.SkuItems)
                    {
                        if(skuItem.CostDiscountRate != 0) continue;
                        skuItem.CostDiscountRate = null;
                    }
                }
                
                var city = cities.FirstOrDefault(x => x.CityCode == d.DestinationCityId);
                if (city != null)
                {
                    d.EnDestinationCityName = city.ENName;
                    d.EnDestinationCountryName = city.CountryEnName;
                }
            }
        }
        return Ok(result);
    }

    /// <summary>
    /// 批量移除线路产品
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    public async Task<IActionResult> BatchRemove(IEnumerable<long> ids)
    {
        if (ids?.Any() is true)
            await _productApiCaller.RemoveLineProducts(ids);
        return Ok();
    }

    /// <summary>
    /// 获取产品详情
    /// </summary>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(GetLineProductOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Get(long productId)
    {
        var result = await _productApiCaller.GetLineProductV2(productId);
        result.InsureProductRelation = await _orderApiCaller.GetInsureProductRelation(productId);
        return Ok(result);
    }

    /// <summary>
    /// 创建产品
    /// </summary>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Tenant.SupplierInvalid, ErrorTypes.Product.GroupInvalid,
        ErrorTypes.Common.ThirdProductConfigurationError)]
    public async Task<IActionResult> Add(AddLineProductInput input)
    {
        await FillDestinationAndDeparture(input);
        var result = await _productApiCaller.AddLineProduct(input);
        return Ok(result.ProductId);
    }

    /// <summary>
    /// 修改产品
    /// </summary>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Tenant.SupplierInvalid, ErrorTypes.Product.GroupInvalid,
        ErrorTypes.Common.ThirdProductConfigurationError)]
    public async Task<IActionResult> Update(UpdateLineProductInput input)
    {
        await FillDestinationAndDeparture(input);
        await _productApiCaller.UpdateLineProduct(input);
        return Ok();
    }

    #region 供应商模块配置

    /// <summary>
    /// 修改供应商模块配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    public async Task<IActionResult> UpdateSupplierModuleSetting(UpdateSupplierModuleSettingBffInput input)
    {
        // 更新配置
        var request = _mapper.Map<UpdateLineProductSupplierModuleSettingInput>(input);
        await _productApiCaller.UpdateLineProductSupplierModuleSetting(request);
        
        // 处理保险
        var insureProductRelationInput = new SaveInsureProductRelationsInput
        {
            Id = input.InsureProductRelation?.Id,
            ProductId = input.LineProductId,
            IsAuto = input.InsureProductRelation?.IsAuto,
            InsureProductId = input.InsureProductRelation?.InsureProductId,
        };
        await _orderApiCaller.SaveInsureProductRelation(insureProductRelationInput);
        
        return Ok();
    }
    
    /// <summary>
    /// 获取供应商模块配置
    /// </summary>
    /// <param name="lineProductId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200,typeof(GetSupplierModuleSettingBffOutput))]
    public async Task<IActionResult> GetSupplierModuleSetting(long lineProductId)
    {
        var response = await _productApiCaller.GetLineProductSupplierModuleSetting(lineProductId);
        var result =  _mapper.Map<GetSupplierModuleSettingBffOutput>(response);
        
        // 保险查询
        result.InsureProductRelation = await _orderApiCaller.GetInsureProductRelation(lineProductId);
        
        // 获取供应商接口对接信息
        if (result.PurchaseSourceType == LineProductPurchaseSourceType.InterfaceDock)
        {
            //查询供应商信息
            var supplierInfo = (await _tenantApiCaller.QuerySuppliers(new QuerySuppliersInput
                {
                    SupplierIds = new List<long>{result.SupplierId},
                    SupplierType = SupplierType.Api
                }))
                .FirstOrDefault();
            if (supplierInfo != null)
            {
                //查询第三方产品详情
                var productDetail = await _productApiCaller.GetOpenSupplierProductDetail(new GetOpenSupplierProductDetailInput
                {
                    OutProductId = result.ActivityId,
                    SupplierApiType = supplierInfo.SupplierApiType!.Value
                });
                result.IsInstant = productDetail?.Instant ?? false;
                result.NeedTravelerInfo = productDetail?.SkuList.FirstOrDefault()?.NeedTravelerInfo ?? false;
            }
        }
        return Ok(result);
    }
    
    /// <summary>
    /// 修改api供应商同步配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    public async Task<IActionResult> UpdateOpenSupplierSetting(UpdateOpenSupplierSyncSettingBffInput input)
    {
        var request = new UpdateOpenSupplierSyncSettingInput
        {
            LineProductId = input.LineProductId,
            PriceInventorySyncType = input.PriceInventorySyncType,
            SyncDateRange = input.SyncDateRange,
            SyncInterval = input.SyncInterval
        };
        await _productApiCaller.UpdateLineProductOpenSupplierSetting(request);
        return Ok();
    }

    /// <summary>
    /// 获取api供应商同步配置
    /// </summary>
    /// <param name="lineProductId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200,typeof(GetOpenSupplierSettingInfoBffOutput))]
    public async Task<IActionResult> GetOpenSupplierSettingInfo(long lineProductId)
    {
        var response = await _productApiCaller.GetLineProductOpenSupplierSetting(lineProductId);
        if (response == null) return Ok();
        var result = new GetOpenSupplierSettingInfoBffOutput
        {
            LineProductId = response.LineProductId,
            PriceInventorySyncType = response.PriceInventorySyncType!.Value,
            SyncDateRange = response.SyncDateRange!.Value,
            SyncInterval = response.SyncInterval!.Value
        };
        return Ok(result);
    }

    #endregion

    #region 渠道模块配置

    /// <summary>
    /// 修改渠道模块配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default,ErrorTypes.Tenant.SupplierInvalid)]
    public async Task<IActionResult> UpdateChannelModuleSetting(UpdateChannelModuleSettingBffInput input)
    {
        var request = _mapper.Map<UpdateLineProductChannelModuleSettingInput>(input);
        await _productApiCaller.UpdateLineProductChannelModuleSetting(request);
        return Ok();
    }
    
    /// <summary>
    /// 获取渠道模块配置
    /// </summary>
    /// <param name="lineProductId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200,typeof(GetChannelModuleSettingBffOutput))]
    public async Task<IActionResult> GetChannelModuleSetting(long lineProductId)
    {
        var response = await _productApiCaller.GetLineProductChannelModuleSetting(lineProductId);
        var result = _mapper.Map<GetChannelModuleSettingBffOutput>(response);
        return Ok(result);
    }

    /// <summary>
    /// 修改渠道价库同步配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    public async Task<IActionResult> UpdateOpenChannelSetting(UpdateOpenChannelSyncSettingBffInput input)
    {
        var request = new UpdateOpenChannelSyncSettingInput
        {
            LineProductId = input.LineProductId,
            SettingInfos = input.SettingInfos.Select(x => new LineProductOpenChannelSettingInfo
            {
                LineProductId = input.LineProductId,
                PriceInventorySyncChannelType = x.PriceInventorySyncChannelType,
                ChannelProductId = x.ChannelProductId,
                SupplierProductId = x.SupplierProductId,
                Area = x.Area,
                PriceInventorySyncType = x.PriceInventorySyncType,
                SyncSkuIds = x.SyncSkuIds,
                ZeroStockThreshold = x.ZeroStockThreshold
            }).ToList()
        };
        
         await _productApiCaller.UpdateLineProductOpenChannelSetting(request);
         return Ok();
    }

    /// <summary>
    /// 查询渠道同步配置
    /// </summary>
    /// <param name="lineProductId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200,typeof(List<GetOpenChannelSettingInfoBffOutput>))]
    public async Task<IActionResult> GetOpenChannelSetting(long lineProductId)
    {
        var response = await _productApiCaller.GetLineProductOpenChannelSetting(lineProductId);
        var output = _mapper.Map<List<GetOpenChannelSettingInfoBffOutput>>(response);
        return Ok(output);
    }
    
    /// <summary>
    /// 修改渠道时效配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    public async Task<IActionResult> UpdateOpenChannelTimelinessSetting(UpdateOpenChannelTimelinessSettingBffInput input)
    {
        var request = new UpdateOpenChannelTimelinessSettingInput
        {
            LineProductId = input.LineProductId,
            IsChannelTimeliness = input.IsChannelTimeliness,
            TimelinessChannelSettingInfos = input.TimelinessChannelSettingInfos
        };
        await _productApiCaller.UpdateLineProductChannelTimelinessSetting(request);
        return Ok();
    }
    
    /// <summary>
    /// 查询渠道时效配置
    /// </summary>
    /// <param name="lineProductId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200, typeof(GetOpenChannelTimelinessSettingBffOutput))]
    public async Task<IActionResult> GetOpenChannelTimelinessSetting(long lineProductId)
    {
        var response = await _productApiCaller.GetLineProductChannelTimelinessSetting(lineProductId);
        var output = _mapper.Map<GetOpenChannelTimelinessSettingBffOutput>(response);
        return Ok(output);
    }

    #endregion

    #region 运营模块配置

    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default,ErrorTypes.Product.OperatorUserPlatformCannotMultiple,
        ErrorTypes.Common.ProductInvalid)]
    public async Task<IActionResult> UpdateOperationModuleSetting(UpdateOperationModuleSettingBffInput input)
    {
        CurrentUser currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        input.DevelopUserId ??= currentUser.UserId;
        var request = _mapper.Map<UpdateLineProductOperationModuleSettingInput>(input);
        await _productApiCaller.UpdateLineProductOperationModuleSetting(request);
        return Ok();
    }

    /// <summary>
    /// 获取运营模块配置
    /// </summary>
    /// <param name="lineProductId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200,typeof(GetOperationModuleSettingBffOutput))]
    public async Task<IActionResult> GetOperationModuleSetting(long lineProductId)
    {
        var response = await _productApiCaller.GetLineProductOperationModuleSetting(lineProductId);
        var result = _mapper.Map<GetOperationModuleSettingBffOutput>(response);
        return Ok(result);
    }

    #endregion

    #region 供应商产品信息

    /// <summary>
    /// 获取API供应商产品内容
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(GetOpenSupplierProductContentBffOutput))]
    public async Task<IActionResult> GetOpenSupplierProductContent(GetOpenSupplierProductContentBffInput input)
    {
        _ = HttpContext.User.TryParseUserInfo<CurrentUser>(out var user);
        var supplierInfo = (await _tenantApiCaller.QuerySuppliers(new QuerySuppliersInput
        {
            SupplierIds = new List<long>{input.SupplierId},
            SupplierType = SupplierType.Api
        })).FirstOrDefault();
        if (supplierInfo == null)
            throw new BusinessException(ErrorTypes.Tenant.SupplierInvalid);
        var lineSkuList = await _productApiCaller.GetLineSkuByProductId(input.LineProductId);
        var productDetail = await _productApiCaller.GetOpenSupplierProductContent(new GetOpenSupplierProductContentInput
        {
            OutProductId = input.ActivityId,
            SupplierApiType = supplierInfo.SupplierApiType!.Value,
            LanguageType = input.LanguageType
        });
        var syncResult = productDetail.Code == 200
            ? OpenSupplierProductSyncResult.Success
            : OpenSupplierProductSyncResult.Failed;
        var businessErrorType = ErrorTypes.Product.SupplierNotProvideChineseInfo;
        var failedMessage = productDetail.Code == 200 ? null : productDetail.Msg;
        var result = new GetOpenSupplierProductContentBffOutput
        {
            ProductContentItem = new GetOpenSupplierLineProductContentItem(),
            SkuContentItems = new List<GetOpenSupplierLineSkuContentItem>()
        };
        if (productDetail.SkuList.Any())
        {
            // 图片转换
            if (productDetail.Images?.Any() == true)
            {
                result.ProductContentItem.ProductPictures =
                    await _openSupplierOrderService.ThrottlerDownloadImages(productDetail.Images, user.Tenant);
            }
            result.ProductContentItem.Title = productDetail.Title;
            result.ProductContentItem.SellPointDescribe = productDetail.Highlight;
            result.ProductContentItem.Content = productDetail.Description;
            
            // 套餐处理
            foreach (var lineSkuItem in lineSkuList)
            {
                var optionSkuDetail =
                    productDetail.SkuList.FirstOrDefault(x => x.OutProductOptionId == lineSkuItem.PackageId);
                if(optionSkuDetail == null) continue;
                
                result.SkuContentItems.Add(new GetOpenSupplierLineSkuContentItem
                {
                    LineProductSkuId = lineSkuItem.Id,
                    FeeNote = optionSkuDetail.Inclusions,
                    FeeNotNote = optionSkuDetail.Exclusions,
                    ValidityDescription = optionSkuDetail.UsageValidity,
                    Precautions = optionSkuDetail.AgeLimit,
                    UsageInstructions = optionSkuDetail.HowToUse,
                    CancellationPolicy = optionSkuDetail.CancelPolicy
                });
            }
        }
        else
        {
            if (productDetail.Code == 200)
            {
                syncResult = OpenSupplierProductSyncResult.Failed;
                failedMessage = "查无编码关联数据";
            }
        }
        
        // 返回同步字段信息
        result.SyncFields = GetSyncFields(productDetail);
        
        // 添加同步日志
        _ = _productApiCaller.AddOpenSupplierProductSyncLog(new AddProductSyncLogInput
        {
            ProductType = ProductType.Line,
            ProductId = input.LineProductId,
            OpenSupplierType = _openPlatformService.MapSupplierApiTypeToOpenSupplierType(supplierInfo.SupplierApiType!.Value),
            SyncResult = syncResult,
            FailedMessage = failedMessage,
            UserId = user.UserId,
            UserName = user.NickName
        });

        if (syncResult == OpenSupplierProductSyncResult.Failed)
            throw new BusinessException(businessErrorType);
        
        return Ok(result);
    }

    #endregion

    /// <summary>
    /// 查询已添加酒店的城市列表
    /// </summary>
    /// <param name="isCache">是否取缓存</param>
    /// <param name="needAvailable">是否取上架景区</param>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(BffGetLineProductCitiesOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetCities(bool? isCache, bool? needAvailable)
    {
        if (!isCache.HasValue) isCache = true;
        if (!needAvailable.HasValue) needAvailable = false;

        var result = await _productApiCaller.GetLineProductCities(isCache.Value, needAvailable.Value);
        var outRes = _mapper.Map<BffGetLineProductCitiesOutput>(result);
        if (outRes.DepartureCities.Any()|| outRes.DestinationCities.Any())
        {
            var cityCodes = outRes.DepartureCities.Select(x => x.CityId).ToList();
            var desCityCodes = outRes.DestinationCities.Select(x => x.CityId).ToList();
            cityCodes.AddRange(desCityCodes);
            var cities = await _resourceApiCaller.QueryCities(new Contracts.Common.Resource.DTOs.QueryInput
            {
                CityCodes = cityCodes.ToArray()
            });
            outRes.DepartureCities.ForEach(item =>
            {
                var city = cities.FirstOrDefault(x => x.CityCode == item.CityId);
                item.EnCityName = city?.ENName;
                item.EnCountryName = city?.CountryEnName;
            });
            outRes.DestinationCities.ForEach(item =>
            {
                var city = cities.FirstOrDefault(x => x.CityCode == item.CityId);
                item.EnCityName = city?.ENName;
                item.EnCountryName = city?.CountryEnName;
            });
        }
        return Ok(outRes);
    }

    #region private
    
    private async Task FillDestinationAndDeparture<T>(T input) where T : UpdateLineProductInfo
    {
        switch (input.DestinationCityId)
        {
            case > 0:
                {
                    var city = (await _resourceApiCaller.QueryCities(new Contracts.Common.Resource.DTOs.QueryInput()
                    {
                        CityCodes = new int[] { input.DestinationCityId }
                    })).FirstOrDefault();
                    input.DestinationCoordinateType = city?.CoordinateType;
                    input.DestinationLongitude = city?.Longitude;
                    input.DestinationLatitude = city?.Latitude;
                    input.DestinationGooglePalceId = city?.GooglePalceId;
                    break;
                }
        }
    }

    private List<GetOpenSupplierProductSyncFieldBffOutput> GetSyncFields(GetOpenSupplierProductContentOutput productDetail)
    {
        var result = new List<GetOpenSupplierProductSyncFieldBffOutput>();
        var mappingConfigs = OpenSupplierProductSyncFieldMappingConfig.Mappings;
        foreach (var moduleType in Enum.GetValues<OpenSupplierProductModuleType>())
        {
            var mappings = mappingConfigs.Where(x => x.ModuleType == moduleType).ToList();
            var mappingFields = mappings.Select(x => x.FieldType).ToList();
            foreach (var fieldType in mappingFields)
            {
                var mappingItem = mappings.FirstOrDefault(x => x.FieldType == fieldType);
                var fieldKey = fieldType.GetEnumAttributes<OpenSupplierProductSystemFieldAttribute>()
                    .FirstOrDefault(x=>x.ProductType == ProductType.Line)
                    ?.FieldKey;
                if (string.IsNullOrWhiteSpace(fieldKey)) continue;
                var resultItem = new GetOpenSupplierProductSyncFieldBffOutput
                {
                    ModuleType = moduleType,
                    ModuleName = moduleType.GetDescription(),
                    FieldName = fieldType.GetDescription(),
                    FieldKey = JsonNamingPolicy.CamelCase.ConvertName(fieldKey),// 返回驼峰
                    IsRequired = mappingItem?.IsRequired ?? false
                };

                switch (fieldType)
                {
                    case OpenSupplierProductSystemFieldType.Pictures:

                        resultItem.IsReturned = productDetail.Images?.Any() == true;

                        break;
                    case OpenSupplierProductSystemFieldType.Name:

                        resultItem.IsReturned = !string.IsNullOrEmpty(productDetail.Title);

                        break;
                    case OpenSupplierProductSystemFieldType.Highlights:

                        resultItem.IsReturned = !string.IsNullOrEmpty(productDetail.Highlight);

                        break;
                    case OpenSupplierProductSystemFieldType.BusinessHours:
                        
                        resultItem.IsReturned = productDetail.SkuList.Any(x => !string.IsNullOrEmpty(x.OpenHours));

                        break;
                    case OpenSupplierProductSystemFieldType.ScenicNotice:

                        resultItem.IsReturned = false;

                        break;
                    case OpenSupplierProductSystemFieldType.Address:

                        resultItem.IsReturned = !string.IsNullOrEmpty(productDetail.PickUpLocation);

                        break;
                    case OpenSupplierProductSystemFieldType.ProductDetails:

                        resultItem.IsReturned = !string.IsNullOrEmpty(productDetail.Description);

                        break;
                    case OpenSupplierProductSystemFieldType.FeeNote:
                        
                        resultItem.IsReturned = productDetail.SkuList.Any(x => !string.IsNullOrEmpty(x.Inclusions));

                        break;
                    case OpenSupplierProductSystemFieldType.FeeNotNote:
                        
                        resultItem.IsReturned = productDetail.SkuList.Any(x => !string.IsNullOrEmpty(x.Exclusions));

                        break;
                    case OpenSupplierProductSystemFieldType.ValidityDescription:
                        
                        resultItem.IsReturned = productDetail.SkuList.Any(x => !string.IsNullOrEmpty(x.UsageValidity));

                        break;
                    case OpenSupplierProductSystemFieldType.Precautions:
                        
                        resultItem.IsReturned = productDetail.SkuList.Any(x => !string.IsNullOrEmpty(x.AgeLimit));

                        break;
                    case OpenSupplierProductSystemFieldType.UsageInstructions:
                        
                        resultItem.IsReturned = productDetail.SkuList.Any(x => !string.IsNullOrEmpty(x.HowToUse));

                        break;
                    case OpenSupplierProductSystemFieldType.OtherInstructions:

                        resultItem.IsReturned = false;

                        break;
                    case OpenSupplierProductSystemFieldType.CancellationPolicy:
                        
                        resultItem.IsReturned = productDetail.SkuList.Any(x => !string.IsNullOrEmpty(x.CancelPolicy));

                        break;
                }

                result.Add(resultItem);
            }
        }
        
        return result;
    }
    
    #endregion
}