using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.OffsetOrder;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs.HotelOrder;
using Contracts.Common.Order.DTOs.OffsetOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Tenant.DTOs.Agency;
using Contracts.Common.Tenant.DTOs.DingtalkApiConfig;
using Contracts.Common.Tenant.DTOs.Supplier;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NetTopologySuite.Geometries;
using Newtonsoft.Json;

namespace Bff.Vebk.Controllers;

[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class OffsetOrderController : ControllerBase
{
    private readonly IMapper _mapper;
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IUserApiCaller _userApiCaller;

    public OffsetOrderController(IMapper mapper,
        IOrderApiCaller orderApiCaller,
        ITenantApiCaller tenantApiCaller,
        IUserApiCaller userApiCaller)
    {
        _mapper = mapper;
        _orderApiCaller = orderApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _userApiCaller = userApiCaller;
    }

    /// <summary>
    /// 抵冲单列表查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(PagingModel<SearchOffsetOrderPageOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search([FromBody] SearchOffsetOrderInput input)
    {
        var pageResponse = await _orderApiCaller.SearchOffsetOrder(input);
        var offsetOrderPages = _mapper.Map<PagingModel<SearchOffsetOrderPageOutput>>(pageResponse);
        if (offsetOrderPages.Data.Any())
        {
            await FillOffsetOrderNames(offsetOrderPages.Data);
        }
        return Ok(offsetOrderPages);
    }
    
    /// <summary>
    /// 抵冲单列表导出
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(IEnumerable<ExportSearchOffsetOrderBffOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Export(ExportSearchOffsetOrderBffInput input)
    {
        var request = _mapper.Map<SearchOffsetOrderInput>(input);
        var response = await _orderApiCaller.ExportSearchOffsetOrder(request);
        var result = _mapper.Map<List<ExportSearchOffsetOrderBffOutput>>(response);
        if (result.Any())
        {
            await FillOffsetOrderNames(result);
        }
        return Ok(result);
    }

    /// <summary>
    /// 抵冲单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(OffsetOrderDetailBffOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Detail(long id)
    {
        var offsetOrder = await _orderApiCaller.OffsetOrderDetail(id);
        var result = _mapper.Map<OffsetOrderDetailBffOutput>(offsetOrder);
        if (result.OffsetType == OffsetOrderType.Receipt)
        {
            var agency = await _tenantApiCaller.GetAgencyDetail(result.AgencyId);
            result.AgencyName = agency?.FullName;
        }
        else if (result.OffsetType == OffsetOrderType.Payable)
        {
            var supplier = await _tenantApiCaller.GetSupplierDetail(result.SupplierId);
            result.SupplierName = supplier?.FullName;
        }
        return Ok(result);
    }

    /// <summary>
    /// 添加抵冲单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(long), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation,
    ErrorTypes.Order.PayableOffsetOrderNeedAgencyProduct,
    ErrorTypes.Order.ReceiptOffsetOrderRefundOutoffAmount,
    ErrorTypes.Order.ReceiptOffsetOrderCannotCompensated,
    ErrorTypes.Order.PayableOffsetOrderRefundOutoffAmount,
    ErrorTypes.Order.PayableOffsetOrderCannotCompensated,
    ErrorTypes.Tenant.AgencyCreditDisabled,
    ErrorTypes.Tenant.CreditNotEnough,
         ErrorTypes.Order.DingtalkApplayIsRequired,
         ErrorTypes.User.DingtalkUserIdIsNoFind,
         ErrorTypes.Order.DingtalkApplayMoneyTypeError,
         ErrorTypes.Order.DingtalkApplayRequiredFieldNotFind,
         ErrorTypes.Order.OffsetOrderNeedConfirmed)]
    public async Task<IActionResult> Add(AddInput input)
    {
        CurrentUser currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        AddOffsetOrderInput addOffsetOrderInput = new()
        {
            BaseOrderId = input.BaseOrderId,
            Amount = input.Amount,
            MoneyType = input.MoneyType,
            OffsetType = input.OffsetType,
            Remark = input.Remark,
            WorkOrderId = input.WorkOrderId,
            BusinessOrderId = input.BusinessOrderId,
            BusinessType = input.BusinessType,
            Images = input.Images,
            OperationUser = new Contracts.Common.Order.DTOs.OperationUserDto
            {
                Name = currentUser.NickName,
                UserId = currentUser.UserId,
                UserType = UserType.Merchant
            },
            DingtalkApply = input.DingtalkApply,
        };
        var dingApplyMoneyTypes = new List<OffsetOrderMoneyType>() {
            OffsetOrderMoneyType.Refund,
            OffsetOrderMoneyType.Compensate,
             OffsetOrderMoneyType.RefundOffline,
              OffsetOrderMoneyType.CompensateCredit,
        };
        if (dingApplyMoneyTypes.Contains(input.MoneyType) && input.BusinessType == OffsetOrderBusinessType.HotelOrder)
        {
            await CheckDingtalkApplay(addOffsetOrderInput, currentUser);
        }
        var result = await _orderApiCaller.AddOffsetOrder(addOffsetOrderInput);
        return Ok(result);
    }

    /// <summary>
    /// 抵冲单钉钉审核Map详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(DingtalkMapDetailOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> DingtalkMapDetail(DingtalkMapDetailInput input)
    {
        var res = new DingtalkMapDetailOutput();
        var config = await _tenantApiCaller.GetDingtalkApiConfig();
        if (config.Enable != true)
        {
            return Ok(null);
        }
        var detail = await _orderApiCaller.HotelOrderDetail(new DetailInput { BaseOrderId = input.BaseOrderId });
        //供应商名称
        if (detail.OrderDetail?.PriceStrategySupplierId is > 0)
        {
            var supplier = await _tenantApiCaller.GetSupplierDetail(detail.OrderDetail.PriceStrategySupplierId);
            detail.OrderDetail.SupplierName = supplier?.FullName;
        }
        var eventInfo = config?.TriggerEvents?.FirstOrDefault(x => x.EventType == input.EventType);
        if (detail != null && eventInfo != null)
        {
            var result = await _tenantApiCaller.GetWorkflowFormInfoByProcessCode(eventInfo.ProcessCode);
            res = SaasFieldMapDingtalkManager.MapSaasOrderField(detail, eventInfo, result);
        }

        return Ok(res);
    }

    /// <summary>
    /// 填充抵冲单的代理商和供应商名称信息
    /// </summary>
    /// <typeparam name="T">抵冲单输出类型</typeparam>
    /// <param name="offsetOrders">抵冲单列表</param>
    private async Task FillOffsetOrderNames<T>(IEnumerable<T> offsetOrders) where T : SearchOffsetOrderPageOutput
    {
        var agencyIds = offsetOrders
            .Where(x => x is { OffsetType: OffsetOrderType.Receipt, AgencyId: not null })
            .Select(x => x.AgencyId!.Value)
            .Distinct()
            .ToList();
        var supplierIds = offsetOrders
            .Where(x => x is { OffsetType: OffsetOrderType.Payable, SupplierId: not null })
            .Select(x => x.SupplierId!.Value)
            .Distinct()
            .ToList();

        var supplierInfo = new List<ShortInfoOutput>();
        var agencyInfo = new List<GetAgenciesByIdsOutput>();
        
        if (supplierIds.Any())
        {
            var shortInfoInput = new ShortInfoInput { SupplierIds = supplierIds };
            supplierInfo = (await _tenantApiCaller.GetSupplierShortInfo(shortInfoInput)).ToList();
        }

        if (agencyIds.Any())
        {
            agencyInfo = await _tenantApiCaller.GetAgencyByIds(new GetAgenciesByIdsInput { AgencyIds = agencyIds });
        }

        foreach (var item in offsetOrders)
        {
            if (item.OffsetType == OffsetOrderType.Receipt)
            {
                item.AgencyName = agencyInfo.FirstOrDefault(x => x.Id == item.AgencyId)?.FullName;
            }
            else
            {
                item.SupplierName = supplierInfo.FirstOrDefault(x => x.SupplierId == item.SupplierId)?.SupplierFullName;
            }
        }
    }

    private async Task CheckDingtalkApplay(AddOffsetOrderInput input, CurrentUser currentUser)
    {
        var config = await _tenantApiCaller.GetDingtalkApiConfig();
        if (config.Enable != true)
        {
            input.DingtalkApply = null;//钉钉配置未开启,不需要钉钉申请单
            return;
        }
        if (input.OffsetType == OffsetOrderType.Payable)
        {
            return;
        }
        var user = (await _userApiCaller.SearchTenantUsers(new Contracts.Common.User.DTOs.TenantUser.SearchTenantUsersInput()
        {
            Ids = new long[] { currentUser.UserId },
        })).FirstOrDefault();
        if (string.IsNullOrEmpty(user.DingtalkUserId) || string.IsNullOrEmpty(user.DingtalkDeptId))
        {
            throw new BusinessException(ErrorTypes.User.DingtalkUserIdIsNoFind);
        }
        var eventType = input.MoneyType switch
        {
            OffsetOrderMoneyType.Refund => Contracts.Common.Tenant.Enums.DingtalkTriggerEventType.OnlineRefund,
            OffsetOrderMoneyType.CompensateCredit => Contracts.Common.Tenant.Enums.DingtalkTriggerEventType.OnlineLimitCompensation,
            OffsetOrderMoneyType.RefundOffline => Contracts.Common.Tenant.Enums.DingtalkTriggerEventType.OfflineTransferRefund,
            OffsetOrderMoneyType.Compensate => Contracts.Common.Tenant.Enums.DingtalkTriggerEventType.OfflineTransferCompensation,
            _ => throw new BusinessException(ErrorTypes.Order.DingtalkApplayMoneyTypeError)
        };
        var eventInfo = config?.TriggerEvents?.FirstOrDefault(x => x.EventType == eventType);
        if (eventInfo == null)// 说明没配置钉钉审核
            return;
        var result = await _tenantApiCaller.GetWorkflowFormInfoByProcessCode(eventInfo.ProcessCode);

        var fields = input.DingtalkApply.Items;
        foreach (var item in result.Items)
        {
            var field = fields.FirstOrDefault(x => x.Id == item.Id);
            var name = $"[{item.Label}]";
            // 钉钉表单字段与创建后字段映射不需要验证
            var afterfeild = SaasFieldMapDingtalkManager.SaasOrderField.FirstOrDefault(x => x.Code == field?.FiedCode && x.Type == SaasOrderFieldType.CreateAfter);
            if (afterfeild != null)
            {
                switch (afterfeild.Code)
                {
                    case "Offset_Remark":
                        field.Value = input.Remark;
                        name = $"[{item.Label}]映射字段[{afterfeild.Name}]";
                        break;
                    case "Offset_Images":
                        field.Value = input.Images?.Any() is true ? JsonConvert.SerializeObject(input.Images) : null;
                        name = $"[{item.Label}]映射字段[{afterfeild.Name}]";
                        break;
                    default:
                        continue;
                        break;
                }
            }
            if (item.Required && string.IsNullOrEmpty(field?.Value))
            {
                throw new BusinessException(ErrorTypes.Order.DingtalkApplayRequiredFieldNotFind.ToString(), $"缺少[{name}]字段信息",
                       new
                       {
                           FieldName = item.Label,
                           IsRequired = true,
                       });
            }
        }
    }
}
