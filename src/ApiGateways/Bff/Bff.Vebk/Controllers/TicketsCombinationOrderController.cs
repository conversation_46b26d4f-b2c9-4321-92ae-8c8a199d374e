using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.ScenicTicketOrder;
using Bff.Vebk.Models.ScenicTicketOrder.OTA;
using Bff.Vebk.Models.TicketsCombinationOrder;
using Bff.Vebk.Services.Interfaces;
using Cit.Storage.Redis;
using Common.Jwt;
using Common.Swagger;
using Common.Utils;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.DTOs.OpenChannelOrder;
using Contracts.Common.Order.DTOs.OpenSupplierOrder;
using Contracts.Common.Order.DTOs.OrderFieldInformation;
using Contracts.Common.Order.DTOs.TicketsCombinationOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.DTOs.OpenSupplier;
using Contracts.Common.Product.DTOs.ProductInformationTemplate;
using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.DTOs.TicketsCombinationPackage;
using Contracts.Common.Scenic.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Bff.Vebk.Controllers;

[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class TicketsCombinationOrderController : ControllerBase
{
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IScenicSpotApiCaller _scenicSpotApiCaller;
    private readonly IPaymentApiCaller _paymentApiCaller;
    private readonly IProductApiCaller _productApiCaller;

    private readonly IRedisClient _redisClient;
    private readonly IScenicOtaOrderService _scenicOtaOrderService;
    private readonly IOrderFieldInformationService _orderFieldInformationService;
    private readonly IOpenPlatformService _openPlatformService;
    private readonly IBaseOrderService _baseOrderService;
    private readonly ILogger<TicketsCombinationOrderController> _logger;
    private readonly IMapper _mapper;
    
    private const int _errorCode = -1;
    private const string _lockKey = "scenicticket:combination:abnormal:create";
    private readonly string _logMessageCreateTemplate = "门票组合异常订单创建:{0},{1}";

    public TicketsCombinationOrderController(ITenantApiCaller tenantApiCaller, 
        IOrderApiCaller orderApiCaller,
        IScenicSpotApiCaller scenicSpotApiCaller,
        IPaymentApiCaller paymentApiCaller,
        IProductApiCaller productApiCaller,
        
        IRedisClient redisClient,
        IScenicOtaOrderService scenicOtaOrderService,
        IOrderFieldInformationService orderFieldInformationService,
        IOpenPlatformService openPlatformService,
        IBaseOrderService baseOrderService,
        ILogger<TicketsCombinationOrderController> logger,
        IMapper mapper)
    {
        _tenantApiCaller = tenantApiCaller;
        _orderApiCaller = orderApiCaller;
        _scenicSpotApiCaller = scenicSpotApiCaller;
        _paymentApiCaller = paymentApiCaller;
        _productApiCaller = productApiCaller;

        _redisClient = redisClient; 
        _scenicOtaOrderService = scenicOtaOrderService;
        _orderFieldInformationService = orderFieldInformationService;
        _openPlatformService = openPlatformService;
        _baseOrderService = baseOrderService;
        _logger = logger;
        _mapper = mapper;
    }

    /// <summary>
    /// 查询门票组合订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(PagingModel<SearchTicketsCombinationOrderBffOutput,CombinationOrderStatistics>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchTicketsCombinationOrderBffInput input)
    {
        var searchRequest = _mapper.Map<SearchTicketsCombinationOrderInput>(input);
        var searchResponse = await _orderApiCaller.SearchTicketsCombinationOrder(searchRequest);
        var result =
            _mapper.Map<PagingModel<SearchTicketsCombinationOrderBffOutput, CombinationOrderStatistics>>(
                searchResponse);
        
        if (result.Data.Any() == false)
            return Ok(result);

        //根据供应商Id列表获取供应商名称
        var supplierIds = result.Data
            .SelectMany(x => x.ScenicTicketOrderInfos.Select(d => d.SupplierId))
            .Distinct()
            .ToList();
        var baseOrderIds = result.Data
            .SelectMany(x => x.ScenicTicketOrderInfos.Select(d => d.BaseOrderId))
            .Distinct()
            .ToList();
        //组合异常单组合产品信息
        var abnormalCombinationIds = result.Data
            .Where(x => x.OrderStatus == TicketsCombinationOrderStatus.ChannelAbnormal
                        || x is {OrderStatus: TicketsCombinationOrderStatus.Closed, AbnormalOrderSourceType: not null})
            .Select(x => x.TicketsCombinationId)
            .Distinct()
            .ToArray();
        var supplierShortInfosTask = _tenantApiCaller.GetSupplierShortInfo(new Contracts.Common.Tenant.DTOs.Supplier.ShortInfoInput
        {
            SupplierIds = supplierIds
        });
        var refundOrdersTask = _orderApiCaller.GetRefundOrders(baseOrderIds);
        var abnormalCombinationDetailTask = _scenicSpotApiCaller.GetTicketsCombinationDetailByIds(abnormalCombinationIds);
        
        var supplierShortInfos = await supplierShortInfosTask;
        var refundOrders = await refundOrdersTask;
        var abnormalCombinationDetails = await abnormalCombinationDetailTask;
        
        var refundingStatus = new[] { RefundOrderStatus.Refunding, RefundOrderStatus.UnderReview };
        foreach (var item in result.Data)
        {
            //组合异常单渠道信息补充
            if (item.OrderStatus == TicketsCombinationOrderStatus.ChannelAbnormal //异常单
                || item is {OrderStatus: TicketsCombinationOrderStatus.Closed, AbnormalOrderSourceType: not null})//关闭的异常单
            {
                //异常订单产品数据补充
                var abnormalCombinationDetail = abnormalCombinationDetails.FirstOrDefault(x => x.Id == item.TicketsCombinationId);
                if(abnormalCombinationDetail?.TicketsCombinationSettingInfos == null) continue;
                item.ScenicTicketOrderInfos = abnormalCombinationDetail.TicketsCombinationSettingInfos
                    .Where(x => x.IsEffective)
                    .Where(x=>x.TicketsCombinationPackageId == item.TicketsCombinationPackageId)
                    .Select(x => new ScenicTicketOrderInfo
                    {
                        ScenicSpotId = x.ScenicSpotId,
                        ScenicSpotName = x.ScenicSpotName,
                        TicketId = x.TicketsId,
                        TicketsName = x.TicketsName,
                        Quantity = x.Quantity
                    })
                    .ToList();
            }
            else
            {
                //正常订单数据补充
                foreach (var scenicTicketOrderInfo in item.ScenicTicketOrderInfos)
                {
                    scenicTicketOrderInfo.SupplierName = supplierShortInfos
                        .Where(x => x.SupplierId.Equals(scenicTicketOrderInfo.SupplierId))
                        .Select(x => x.SupplierFullName)
                        .FirstOrDefault();
                    scenicTicketOrderInfo.IsRefunding = refundOrders
                        .Any(x => x.BaseOrderId == scenicTicketOrderInfo.BaseOrderId && refundingStatus.Contains(x.Status));
                }
            }
        }
        return Ok(result);
    }

    /// <summary>
    /// 替换门票组合订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> ReplaceTicketsOrder(ReplaceTicketsOrderInput input)
    {
        await _orderApiCaller.ReplaceTicketsCombinationOrder(input);
        return Ok();
    }

    /// <summary>
    /// 组合订单重新发货.
    /// </summary>
    /// <remarks>发送到渠道端</remarks>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200,typeof(CombinationOrderDeliveryBffOutput))]
    public async Task<IActionResult> Delivery(CombinationOrderDeliveryBffInput input)
    {
        var result = new CombinationOrderDeliveryBffOutput();
        //校验通过执行发货(凭证发送到渠道端)
        var deliveryResult = await _orderApiCaller.TicketsCombinationOrderDelivery(input.Id);
        result.IsSuccess = deliveryResult.IsSuccess;
        result.Message = deliveryResult.Message;
        return Ok(result);
    }

    /// <summary>
    /// 组合异常单 - 重走渠道流程创建订单
    /// <remarks>直接创建</remarks>
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200,typeof(CreateAbnormalCombinationOrderBffOutput))]
    [SwaggerResponseExt(default,ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> CreateAbnormalOrder(CreateAbnormalCombinationOrderBffInput input)
    {
        var user = HttpContext.User.ParseUserInfo<CurrentUser>();
        var result = new CreateAbnormalCombinationOrderBffOutput
        {
            Code = _errorCode
        };
        var logMessage = string.Format(_logMessageCreateTemplate, "{@input}", "{@msg}");
        
        var lockSecret = Guid.NewGuid().ToString();
        var lockKey = $"{_lockKey}:{input.Id}";
        try
        {
            await _redisClient.LockTakeWaitingAsync(lockKey,lockSecret,TimeSpan.FromSeconds(10));
            
            //查询组合异常单信息
            var abnormalOrderDetail = await _orderApiCaller.GetTicketsCombinationAbnormalOrderDetail(input.Id);
            if (abnormalOrderDetail == null)
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        
            if(abnormalOrderDetail.OrderStatus != TicketsCombinationOrderStatus.ChannelAbnormal)
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        
            if(string.IsNullOrEmpty(abnormalOrderDetail.DataContentJson))
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
            
            
            var baseOrderIds = new List<long>();
            //查询组合产品信息
            var combinationPackage = (
                await _scenicSpotApiCaller.SetTenantId(user.Tenant).QueryTicketsCombinationPackageItems(new QueryTicketsCombinationPackageItemInput
                {
                    TicketsCombinationPackageId = abnormalOrderDetail.TicketsCombinationPackageId
                })).FirstOrDefault();
            if(combinationPackage == null) throw new BusinessException(ErrorTypes.Scenic.TicketsCombinationInvalid);
            
            var otaCreateInput = JsonConvert.DeserializeObject<CreateScenicOTAOrderBffInput>(abnormalOrderDetail.DataContentJson);
            
            //以最新组合产品配置下单(正常组合场景下SubOutSkuId是组合套餐id,虚拟组合场景下SubOutSkuId是门票/时段id)
            foreach (var item in otaCreateInput.OrderPriceItems.Where(x =>
                         x.SubOutSkuId == abnormalOrderDetail.TicketsCombinationPackageId.ToString()))
            {
                item.CombinationItems = combinationPackage.PackageItems
                    .Select(x => new ScenicTicketOTAOrderPriceCombinationItem
                    {
                        CombinationId = abnormalOrderDetail.TicketsCombinationPackageId,
                        TicketsId = x.TicketsId,
                        TimeSlotId = x.TimeSlotId,
                        Quantity = x.Quantity
                    }).ToList();
            }
            
            //创建组合产品
            var createResponse = await _scenicOtaOrderService.CreateTicketCombinationOrder(
                combinationPackage: combinationPackage,
                input: otaCreateInput,
                abnormalOrderId: abnormalOrderDetail.Id);
                
            //创建组合单失败
            if(createResponse.Code == _errorCode)
                throw new BusinessException(createResponse.Msg);
                
            baseOrderIds = createResponse.Data.CombinationBaseOrderIds;
            result.IsSuccess = true;
            result.Code = createResponse.Code;
            result.Message = createResponse.Msg;
            
            foreach (var orderPayInput in baseOrderIds.Select(baseOrderId => new OrderPayInput
                     {
                         OrderId = baseOrderId,
                         OrderPaymentType = OrderPaymentType.OrderPay,
                         PayType = PayType.AgencyCreditPay
                     }))
            {
                await _paymentApiCaller.SetTenantId(user.Tenant).OrderPay(orderPayInput);
            }
        }
        catch (BusinessException businessException)
            when (businessException.BusinessErrorType != ErrorTypes.Common.NotSupportedOperation.ToString())
        {
            result.Message = businessException.Message;
            _logger.LogWarning(logMessage, input, businessException.Message);
        }
        finally
        {
            //记录同步失败订单
            if (result.Code == _errorCode)
            {
                //更新异常单异常信息
                await _orderApiCaller.SetTenantId(user.Tenant)
                    .UpdateTicketsCombinationAbnormalOrder(
                        new UpdateAbnormalCombinationOrderInput
                        {
                            Id = input.Id, 
                            AbnormalReason = result.Message
                        });
            }

            await _redisClient.LockReleaseAsync(lockKey, lockSecret);
        }
            
        return Ok(result);
    }
    
    
    /// <summary>
    /// 关闭组合异常单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200)]
    [SwaggerResponseExt(default,ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> CloseAbnormalOrder(CloseAbnormalCombinationOrderBffInput input)
    {
        var lockSecret = Guid.NewGuid().ToString();
        var lockKey = $"{_lockKey}:{input.Id}";
        try
        {
            await _redisClient.LockTakeWaitingAsync(lockKey,lockSecret,TimeSpan.FromSeconds(10));
            
            await _orderApiCaller.CloseTicketsCombinationAbnormalOrder(new CloseAbnormalCombinationOrderInput
            {
                Id = input.Id
            });
        }
        finally
        {
            await _redisClient.LockReleaseAsync(lockKey, lockSecret);
        }

        return Ok();
    }

    /// <summary>
    /// 替换异常订单组合产品
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200)]
    [SwaggerResponseExt(default,ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> ReplaceTicketCombination(ReplaceTicketCombinationBffInput input)
    {
        var combinationPackage = (
            await _scenicSpotApiCaller.QueryTicketsCombinationPackageItems(new QueryTicketsCombinationPackageItemInput
            {
                TicketsCombinationPackageId = input.TicketsCombinationPackageId
            })).FirstOrDefault();
        
        var lockSecret = Guid.NewGuid().ToString();
        var lockKey = $"{_lockKey}:{input.Id}";
        try
        {
            await _redisClient.LockTakeWaitingAsync(lockKey,lockSecret,TimeSpan.FromSeconds(10));
            
            await _orderApiCaller.ReplaceTicketCombination(new ReplaceTicketCombinationInput
            {
                Id = input.Id,
                TicketsCombinationId = input.TicketsCombinationId,
                TicketsCombinationName = combinationPackage.TicketsCombinationName,
                TicketsCombinationPackageId = combinationPackage.TicketsCombinationPackageId,
                TicketsCombinationPackageName = combinationPackage.TicketsCombinationPackageName
            });

        }
        finally
        {
            await _redisClient.LockReleaseAsync(lockKey, lockSecret);
        }
        return Ok();
    }
    
    
    /// <summary>
    /// 手工单-创建组合订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200,typeof(CreateCombinationOrderBffOutput))]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation
        , ErrorTypes.Scenic.TicketsCombinationInvalid
        , ErrorTypes.Scenic.ScenicTicketsInvalid
        , ErrorTypes.Order.CombinationAbnormalOrderTotalAmountError
        , ErrorTypes.Scenic.CombinationPackageProductDataChanged
        , ErrorTypes.Order.ChannelOrderNoIsExists
        , ErrorTypes.Order.SyncFailOrderStatusChanged)]
    public async Task<IActionResult> CreateOrder (CreateCombinationOrderBffInput input)
    {
        //渠道单号校验
        var checkRequest = new CheckChannelOrderInput {ChannelOrderNo = input.ChannelOrderNo};
        var normalOrderCheckResult = await _orderApiCaller.CheckChannelOrder(checkRequest);//正常订单渠道单号校验
        if (normalOrderCheckResult.RelatedBaseOrderInfos.Any())
            throw new BusinessException(ErrorTypes.Order.ChannelOrderNoIsExists);
        var abnormalOrderCheckResult = await _orderApiCaller.CheckChannelOrderAbnormalOrder(checkRequest); // 异常订单渠道单号校验
        if (abnormalOrderCheckResult.RelatedBaseOrderInfos.Any() && 
            (!input.SyncFailOrderId.HasValue || abnormalOrderCheckResult.RelatedBaseOrderInfos.Count > 1))
        {
            throw new BusinessException(ErrorTypes.Order.ChannelOrderNoIsExists);
        }
        //查询组合异常单信息
        var openChannelOrderNeedConfirm = false; // 渠道订单是否需要确认
        if (input.SyncFailOrderId.HasValue)
        {
            var abnormalOrderDetail = await _orderApiCaller.GetTicketsCombinationAbnormalOrderDetail(input.SyncFailOrderId!.Value);
            if (abnormalOrderDetail == null)
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

            if (abnormalOrderDetail.OrderStatus != TicketsCombinationOrderStatus.ChannelAbnormal)
                throw new BusinessException(ErrorTypes.Order.SyncFailOrderStatusChanged);
            
            // 判断实收总额
            if (abnormalOrderDetail.PaymentAmount != input.SalesInfos.Sum(x => x.PaymentAmount))
                throw new BusinessException(ErrorTypes.Order.CombinationAbnormalOrderTotalAmountError);

            if (!string.IsNullOrEmpty(abnormalOrderDetail.DataContentJson))
            {
                var contentData = JsonConvert.DeserializeObject<SyncFailCombinationOrderContentDataDto>(abnormalOrderDetail.DataContentJson);
                openChannelOrderNeedConfirm = contentData?.NeedConfirmOrder ?? false;
            }
        }
        //查询组合产品信息
        var combinationPackage = (
            await _scenicSpotApiCaller.QueryTicketsCombinationPackageItems(new QueryTicketsCombinationPackageItemInput
            {
                TicketsCombinationPackageId = input.TicketsCombinationPackageId
            })).FirstOrDefault();
        if(combinationPackage == null) throw new BusinessException(ErrorTypes.Scenic.TicketsCombinationInvalid);
        //校验组合产品配置数量的门票有效数量
        if (combinationPackage.PackageItems.Any() is false)
            throw new BusinessException(ErrorTypes.Scenic.TicketsCombinationInvalid);
        // 检查组合门票是否全是有效的门票
        var invalidTickets = combinationPackage.PackageItems?
            .Where(x => !x.IsEffective)
            .ToList();
        if(invalidTickets?.Any() == true)
            throw new BusinessException(ErrorTypes.Scenic.ScenicTicketsInvalid);
        // 以组合套餐的门票数据为准.判断传入的门票数据是否完全匹配
        if (combinationPackage.PackageItems?.Count != input.SalesInfos.Count)
            throw new BusinessException(ErrorTypes.Scenic.ScenicTicketsInvalid);
        if (combinationPackage.PackageItems.Any(packageItem =>
                !input.SalesInfos.Any(x =>
                    x.TicketId == packageItem.TicketsId && x.TimeSlotId == packageItem.TimeSlotId)))
        {
            throw new BusinessException(ErrorTypes.Scenic.CombinationPackageProductDataChanged);
        }
        input.SalesInfos = input.SalesInfos.Where(x => x.Quantity > 0).ToList();
        if(input.SalesInfos.Any() is false) 
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        #region 字段模板处理

        var allocateOrderFields = new List<CreateCombinationOrderFieldBffInfo>();// 记录分配后的字段模板数据
        foreach (var item in input.SalesInfos)
        {
            var combinationPackageItem = combinationPackage.PackageItems.First(x => x.TicketsId == item.TicketId);
            var ticketOrderFieldInfo = input.OrderFieldInfos.FirstOrDefault(x => x.TicketId == item.TicketId) ?? new CreateCombinationOrderFieldBffInfo
            {
                TicketId = item.TicketId,
                TimeSlotId = item.TimeSlotId
            };
            var orderFields = ticketOrderFieldInfo?.OrderFields.ToList() ?? new List<SaveOrderFieldInformationTypeDto>();
            var tempResult = await _scenicSpotApiCaller.ProductInformationTemplateGetProductTempFieldsDetail(
                new GetProductTempFieldsDetailInput()
                {
                    ProductId = combinationPackageItem.ScenicSpotId!.Value, 
                    ProductSkuId = item.TicketId, 
                    ProductType = ProductType.Scenic
                });
            var productTemplateType = combinationPackageItem.TouristInfoType switch
            {
                TouristInfoType.Every => ProductTemplateType.EachPerson,
                TouristInfoType.One => ProductTemplateType.JustOnePerson,
                _ =>  ProductTemplateType.EachPerson
            };
            var travelerCount = orderFields.Count(x => x.TemplateType == TemplateType.Travel && x.ProductTemplateType == productTemplateType);
            var tempCount = tempResult.Templates.Count(x => x.TemplateType == TemplateType.Travel && x.ProductTemplateType == productTemplateType); // 无模板的判断
            var fieldCount = tempResult.Templates.FirstOrDefault(x => x.ProductTemplateType == productTemplateType)?.Fields.Count() ?? 0;// 空模板判断
            switch (combinationPackageItem.TouristInfoType)
            {
                case TouristInfoType.Every:
                    if (tempCount > 0 && fieldCount > 0 && travelerCount != item.Quantity)
                    {
                        throw new BusinessException(ErrorTypes.Order.TravelerNotCompleted);//出行人不完整
                    }
                    // 排除ProductTemplateType.JustOne类型模板
                    tempResult.Templates = tempResult.Templates
                        .Where(x => x.ProductTemplateType != ProductTemplateType.JustOnePerson)
                        .ToList();
                    break;
                case TouristInfoType.One:
                    if (tempCount > 0 && fieldCount > 0 && travelerCount == 0)
                    {
                        throw new BusinessException(ErrorTypes.Order.TravelerNotCompleted);//出行人不完整
                    }
                    // 排除ProductTemplateType.EachPerson类型模板
                    tempResult.Templates = tempResult.Templates
                        .Where(x => x.ProductTemplateType != ProductTemplateType.EachPerson)
                        .ToList();
                    break;
            }
            // 联系人信息填充
            var contactOrderFields = _orderFieldInformationService.CombinationOrderContactToTempField(new CombinationOrderContactToTempFieldDto
            {
                Templates = tempResult.Templates,
                ContactName = input.ContactName,
                ContactPhoneNumber = input.ContactPhoneNumber,
                ContactAreaCode = input.ContactAreaCode,
                ContactEmail = input.ContactEmail,
                EnglishName = input.EnglishName,
                EnglishSurname = input.EnglishSurname
            });
            if (contactOrderFields != null)
            {
                // 替换传入orderFields的联系人模板数据
                orderFields = orderFields.Where(x => x.TemplateType != TemplateType.Contacts).ToList();
                orderFields.Add(contactOrderFields);
            }
            // 验证模板参数
            _orderFieldInformationService.Validator(tempResult.Templates, orderFields, true);
            // 新订单是没有订单确认信息的
            var orderSureTemp = tempResult.Templates.FirstOrDefault(x => x.TemplateType == TemplateType.Order);
            if (orderSureTemp != null)
            {
                orderFields.Add(_mapper.Map<SaveOrderFieldInformationTypeDto>(orderSureTemp));
            }
            ticketOrderFieldInfo.OrderFields = orderFields;
            allocateOrderFields.Add(ticketOrderFieldInfo);
        }

        input.OrderFieldInfos = allocateOrderFields;

        #endregion

        var createRequest = _mapper.Map<CreateCombinationOrderInput>(input);
        createRequest.TicketsCombinationName = combinationPackage.TicketsCombinationName;
        createRequest.TicketsCombinationPackageName = combinationPackage.TicketsCombinationPackageName;
        createRequest.CombinationType = combinationPackage.CombinationType;
        if (input.SyncFailOrderId.HasValue)
        {
            createRequest.SellingPlatform =
                _openPlatformService.MapSellingPlatformToSellingChannels(input.SellingChannels);
        }
        var createResponse = await _orderApiCaller.CreateCombinationOrder(createRequest);
        var result = _mapper.Map<CreateCombinationOrderBffOutput>(createResponse);
        // 订单支付
        foreach (var orderPayInput in result.BaseOrderIds.Select(baseOrderId => new OrderPayInput
                 {
                     OrderId = baseOrderId,
                     OrderPaymentType = OrderPaymentType.OrderPay,
                     PayType = PayType.AgencyCreditPay
                 }))
        {
            await _paymentApiCaller.OrderPay(orderPayInput);
        }
        
        if (result.BaseOrderIds.Any())
        {
            // 抖音订单确认
            if (input is { SyncFailOrderId: not null, SellingChannels: SellingChannels.TikTok })
            {
                if (openChannelOrderNeedConfirm)
                {
                    var channelOrderSegments = input.ChannelOrderNo
                        .Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
                        .ToList();
                    foreach (var channelOrderNoItem in channelOrderSegments)
                    {
                        _ = _orderApiCaller.ConfirmChannelOrder(new ConfirmChannelOrderInput
                        {
                            SellingPlatform = SellingPlatform.TikTok,
                            ChannelOrderNo = channelOrderNoItem
                        });
                    }
                }
            }
        }
        
        return Ok(result);
    }

    /// <summary>
    /// 组合产品异常单 - 详情
    /// </summary>
    /// <param name="syncFailOrderId"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200,typeof(QueryAbnormalCombinationOrderDetailBffOutput))]
    public async Task<IActionResult> GetAbnormalOrderDetail(QueryAbnormalCombinationOrderDetailBffInput input)
    {
        var result = new QueryAbnormalCombinationOrderDetailBffOutput();
        var abnormalOrderDetail = await _orderApiCaller.GetTicketsCombinationAbnormalOrderDetail(input.SyncFailOrderId);
        if (abnormalOrderDetail == null) return Ok();
        if (abnormalOrderDetail.OrderStatus != TicketsCombinationOrderStatus.ChannelAbnormal) return Ok();
        var dataContentJson = JsonConvert.DeserializeObject<CreateScenicOTAOrderBffInput>(abnormalOrderDetail.DataContentJson);
        var combinationRequest = new QueryTicketsCombinationPackageItemInput
        {
            TicketsCombinationPackageId = input.TicketsCombinationPackageId ?? abnormalOrderDetail.TicketsCombinationPackageId
        };
        var combinationPackage = (await _scenicSpotApiCaller.QueryTicketsCombinationPackageItems(combinationRequest)).FirstOrDefault();
        if (combinationPackage != null)
        {
            abnormalOrderDetail.TicketsCombinationId = combinationPackage.TicketsCombinationId;
            abnormalOrderDetail.TicketsCombinationName = combinationPackage.TicketsCombinationName;
            abnormalOrderDetail.TicketsCombinationPackageId = combinationPackage.TicketsCombinationPackageId;
            abnormalOrderDetail.TicketsCombinationPackageName = combinationPackage.TicketsCombinationPackageName;
            abnormalOrderDetail.CombinationType = combinationPackage.CombinationType;
        }
        else
        {
            combinationPackage = new QueryTicketsCombinationPackageItemOutput();
        }
        var exTraInfoRequest = new QueryOpenSupplierSkuExtraInfoInput();
        var extraInfoResponse = new List<QueryOpenSupplierSkuExtraInfoOutput>();
        if (combinationPackage.PackageItems.Any(x => !string.IsNullOrEmpty(x.ActivityId)))
        {
            exTraInfoRequest.ProductIds = combinationPackage.PackageItems.Where(x => !string.IsNullOrEmpty(x.ActivityId))
                .Select(x => x.ActivityId!).Distinct().ToList();
            exTraInfoRequest.ProductIds = combinationPackage.PackageItems.Where(x => !string.IsNullOrEmpty(x.PackageId))
                .Select(x => x.PackageId!).Distinct().ToList();
            exTraInfoRequest.SkuIds = combinationPackage.PackageItems.Where(x => !string.IsNullOrEmpty(x.SkuId))
                .Select(x => x.SkuId!).Distinct().ToList();
            extraInfoResponse = await _productApiCaller.QueryOpenSupplierSkuExtraInfo(exTraInfoRequest);
        }

        // 赋值
        #region 基础信息

        result.TicketsCombinationId = abnormalOrderDetail.TicketsCombinationId;
        result.TicketsCombinationName = abnormalOrderDetail.TicketsCombinationName;
        result.TicketsCombinationPackageId = abnormalOrderDetail.TicketsCombinationPackageId;
        result.TicketsCombinationPackageName = abnormalOrderDetail.TicketsCombinationPackageName;
        result.ChannelOrderNo = abnormalOrderDetail.ChannelOrderNo;
        result.Quantity = abnormalOrderDetail.Quantity;
        result.PaymentAmount = abnormalOrderDetail.PaymentAmount;
        result.PaymentCurrencyCode = abnormalOrderDetail.PaymentCurrencyCode;
        result.AbnormalReason = abnormalOrderDetail.AbnormalReason;
        result.TravelDate = abnormalOrderDetail.TravelDate;
        result.SellingChannels = abnormalOrderDetail.SellingChannels;
        result.AgencyId = abnormalOrderDetail.AgencyId;
        result.CombinationType = abnormalOrderDetail.CombinationType;

        #endregion

        #region 联系人信息

        result.ContactName = dataContentJson.ContactItem.Name;
        result.ContactPhoneNumber = dataContentJson.ContactItem.Mobile;
        result.ContactAreaCode = dataContentJson.ContactItem.AreaCode;
        result.ContactEmail = dataContentJson.ContactItem.Email;
        result.EnglishName = dataContentJson.ContactItem.FirstName;
        result.EnglishSurname = dataContentJson.ContactItem.LastName;
        if (!string.IsNullOrEmpty(result.ContactName))
        {
            var (firstName, lastName) = ChinesePinyinUtil.ParseChineseName(result.ContactName);
            if (string.IsNullOrEmpty(result.EnglishName))
                result.EnglishName = firstName;
            if (string.IsNullOrEmpty(result.EnglishSurname))
                result.EnglishSurname = lastName;
        }

        #endregion

        var manualAddCombinationSettings = dataContentJson.OrderPriceItems.SelectMany(x => x.CombinationItems).ToList();
        foreach (var item in combinationPackage.PackageItems.Where(x=>x.IsEffective))
        {
            #region 销售信息

            //正常组合场景下SubOutSkuId是组合套餐id,虚拟组合场景下SubOutSkuId是门票/时段id
            ScenicTicketOTAOrderPriceItem? orderPriceItem = null;
            //判断是否手工创建的组合
            var manualAddCombinationSetting = manualAddCombinationSettings.FirstOrDefault(x =>
                x.TicketsId == item.TicketsId
                && x.TimeSlotId == item.TimeSlotId);
            if (manualAddCombinationSetting == null)
            {
                orderPriceItem = dataContentJson.OrderPriceItems
                    .Where(x => !string.IsNullOrEmpty(x.SubOutSkuId))
                    .FirstOrDefault(x => x.SubOutSkuId == item.TicketsId.ToString() ||
                                         x.SubOutSkuId == item.TimeSlotId.ToString());
            }
            else
            {
                orderPriceItem = dataContentJson.OrderPriceItems.FirstOrDefault(x =>
                    x.SubOutSkuId == manualAddCombinationSetting.CombinationId.ToString());
            }

            var salesInfoItem = new QueryAbnormalCombinationOrderSalesBffInfo
            {
                TicketId = item.TicketsId,
                TicketsName = item.TicketsName,
                TimeSlotId = item.TimeSlotId,
                TimeSlotName = item.TimeSlotName,
                Quantity = item.Quantity,
                SellingPrice = 0m,
                CostPrice = 0m,
                DiscountAmount = 0m,
                PaymentAmount = 0m
            };
            if (orderPriceItem != null)
            {
                salesInfoItem.SellingPrice = orderPriceItem.Price / 100m;
                salesInfoItem.DiscountAmount = orderPriceItem.DiscountFee / 100m;
                salesInfoItem.PaymentAmount = orderPriceItem.CostPrice / 100m;
            }
            result.SalesInfos.Add(salesInfoItem);

            #endregion
            
            #region 字段模板信息
            
            var getFieldsInput = _mapper.Map<GetSaveOrderFieldsInputDto>(dataContentJson);
            var saveOrderFields = new List<SaveOrderFieldInformationTypeDto>();
            try
            {
                getFieldsInput.TicketId = item.TicketsId;
                getFieldsInput.ScenicSpotId = item.ScenicSpotId!.Value;
                getFieldsInput.TouristInfoType = item.TouristInfoType ?? TouristInfoType.None;
                saveOrderFields  = await _scenicOtaOrderService.GetSaveOrderFields(getFieldsInput);
                // 由于组合异常单创建`联系信息`是固定字段,所以这里字段模板过滤掉`联系信息类型`
                saveOrderFields = saveOrderFields.Where(x => x.TemplateType != TemplateType.Contacts)
                    .ToList();
                if (saveOrderFields.Any())
                {
                    //字段模板 中文转拼音
                    var mapperOrderFields = _mapper.Map<List<OrderFieldInformationTypeOutput>>(saveOrderFields);
                    await _baseOrderService.ConvertChineseNameToPinyin(mapperOrderFields);
                    saveOrderFields = _mapper.Map<List<SaveOrderFieldInformationTypeDto>>(mapperOrderFields);
                }
            }
            catch (BusinessException businessException)
            {
            }
            result.OrderFieldInfos.Add(new QueryAbnormalCombinationOrderFieldBffInfo
            {
                TicketId = item.TicketsId,
                TicketsName = item.TicketsName,
                TimeSlotId = item.TimeSlotId,
                TimeSlotName = item.TimeSlotName,
                OrderFields = saveOrderFields
            });

            #endregion

            #region 附加信息匹配

            if (dataContentJson.ExtraInfoIds.Any() && !string.IsNullOrEmpty(item.ActivityId))
            {
                var relatedExtraInfos = extraInfoResponse.Where(x => x.ProductId == item.ActivityId)
                    .SelectMany(x => x.Sub)
                    .Where(x => x.OptionId == item.PackageId && x.SkuId == item.SkuId)
                    .SelectMany(x => x.ExtraInfos)
                    .ToList();
                foreach (var relatedExtraItem in relatedExtraInfos.Where(x=>x.ValueType == ApiSkuExtraInfoValueType.Select))
                {
                    var matchOrderExtraInfos = relatedExtraItem.ValueOptions.Where(x => dataContentJson.ExtraInfoIds.Contains(x.Id))
                        .Select(x => new AddOpenSupplierOrderExtraInfoItem
                        {
                            DataType = relatedExtraItem.DataType,
                            OptionKey = x.Key,
                            OptionValue = x.Value
                        })
                        .ToList();
                    if(matchOrderExtraInfos.Count > 1)
                        break;
                    
                    result.OrderExtraInfos.Add(new QueryAbnormalCombinationOrderExtraBffInfo
                    {
                        TicketId = item.TicketsId,
                        TicketsName = item.TicketsName,
                        TimeSlotId = item.TimeSlotId,
                        TimeSlotName = item.TimeSlotName,
                        OrderExtraInfos = matchOrderExtraInfos
                    });
                }
            }

            #endregion
        }

        return Ok(result);
    }
}
