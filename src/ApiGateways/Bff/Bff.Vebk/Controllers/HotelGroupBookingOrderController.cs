using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.Common;
using Bff.Vebk.Models.HotelGroupBookingOrder;
using Common.Jwt;
using Common.Swagger;
using Common.Utils;
using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.ExportPdf;
using Contracts.Common.Order.DTOs.GroupBookingOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.DTOs.Currency;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Tenant.DTOs.Agency;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Collections.Concurrent;

namespace Bff.Vebk.Controllers;

[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class HotelGroupBookingOrderController : ControllerBase
{
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly IHotelApiCaller _hotelApiCaller;
    private readonly IPaymentApiCaller _paymentApiCaller;
    private readonly IMapper _mapper;

    public HotelGroupBookingOrderController(IOrderApiCaller orderApiCaller,
        ITenantApiCaller tenantApiCaller,
        IResourceApiCaller resourceApiCaller,
        IHotelApiCaller hotelApiCaller,
        IPaymentApiCaller paymentApiCaller,
        IMapper mapper)
    {
        _orderApiCaller = orderApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _resourceApiCaller = resourceApiCaller;
        _hotelApiCaller = hotelApiCaller;
        _paymentApiCaller = paymentApiCaller;
        _mapper = mapper;
    }

    /// <summary>
    /// 提交团房单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(SubmitOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.SomeDatesAreUnavailable, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Submit(SubmitInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var applicationFormId = input.GroupBookingApplicationFormId;
        var preOrderId = input.GroupBookingPreOrderId;
        var applicationForm = await _orderApiCaller.GetApplicationForm(new Contracts.Common.Order.DTOs.GroupBooking.GetApplicationFormInput
        {
            Id = applicationFormId,
            TenantId = currentUser.Tenant
        });
        var agencyId = applicationForm.AgencyId;
        GetAgenciesByIdsOutput? agency = agencyId is > 0 ? (await _tenantApiCaller.GetAgencyDetail(agencyId)) : default;

        //非预订单状态 不可下单
        if (applicationForm.Status != GroupBookingApplicationFormStatus.PreOrdered)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        //团房预订单
        var preOrders = await _orderApiCaller.GetGroupBookingPreOrders(applicationFormId, preOrderId);
        var preOrder = preOrders.FirstOrDefault(x => x.Id == preOrderId);
        //如果预订单已存在团房订单,不可下单
        if (preOrder.GroupBookingOrderId.HasValue)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        //预订单酒店信息
        var hotelIds = preOrder.PreOrderItems.Select(x => x.HotelId).ToArray();
        var apiHotelDetails = (await _hotelApiCaller.GetApiHotelDetail(hotelIds));

        string paymentCurrencyCode = agency?.CurrencyCode ?? Currency.CNY.ToString();//支付币种
        var userInfo = new Contracts.Common.Order.DTOs.HotelOrder.OrderUserInfo()
        {
            UserId = currentUser.UserId,
            NickName = currentUser.NickName,
            AgencyId = agencyId,
            AgencyName = agency?.FullName,
            UserType = Contracts.Common.Order.Enums.UserType.Manager,
            SalespersonId = agency?.SalespersonId,
            SalespersonName = agency?.SalespersonName,
            VipLevelId = agency?.Level ?? 0,
            VipLevelName = agency?.LevelName ?? string.Empty,
        };
        ConcurrentBag<GroupBookingOrderItemDto> groupBookingOrderItems = new();
        await Parallel.ForEachAsync(preOrder.PreOrderItems, new ParallelOptions
        {
            MaxDegreeOfParallelism = preOrder.PreOrderItems.Count(),
        },
        async (item, cancellationToken) =>
        {
            //检验酒店库存价格房态
            var priceRequest = new CheckSaleInput()
            {
                SupplierApiType = item.SupplierApiType,
                HotelId = item.HotelId,
                PriceStrategyId = item.PriceStrategyId,
                PreBookingCode = item.PreBookingCode,
                RoomId = item.HotelRoomId,
                BeginDate = item.CheckIn,
                EndDate = item.CheckOut,
                Quantity = item.RoomCount,
                SalesChannel = SellingChannels.B2b,
                IsGroupBooking = true
            };
            var priceReponse = await _hotelApiCaller.PreOrderCheckSale(priceRequest);
            if (priceReponse?.Code != CheckPriceStrategySaleCode.Success)
                throw new BusinessException(ErrorTypes.Order.SomeDatesAreUnavailable);
            var room = priceReponse.Data.Room;
            var priceStrategy = priceReponse.Data.PriceStrategy;
            Contracts.Common.Order.DTOs.HotelOrder.HotelApiOrderInfo? hotelApiOrderInfo = priceStrategy.SupplierApiType != SupplierApiType.None ?
                new()
                {
                    ArrivalTaxFees = priceStrategy.ArrivalTaxFees,
                    TaxDescription = priceStrategy.TaxDescription,
                    HotelId = priceStrategy.HotelId,
                    RoomId = priceStrategy.RoomId,
                    PriceStrategyId = priceStrategy.Id,
                    ResourceHotelId = priceReponse.Data.ResourceHotelId,
                    SupplierApiType = priceStrategy.SupplierApiType,
                } :
                null;
            //订单日历价格
            _ = long.TryParse(item.PriceStrategyId, out long priceStrategyId);
            var calendarPrices = await GetHotelCalendarPrices(new Contracts.Common.Order.DTOs.HotelOrder.HotelAgencyChannelPriceInput
            {
                SupplierApiType = item.SupplierApiType,
                AgencyId = agencyId,
                ChannelProductType = Contracts.Common.Product.Enums.ChannelProductType.Hotel,
                ProductId = item.HotelId,
                SkuId = priceStrategyId,
                CostCurrencyCode = priceStrategy.CostCurrencyCode,
                SaleCurrencyCode = priceStrategy.SaleCurrencyCode,
                PaymentCurrencyCode = paymentCurrencyCode,
                StaffTag = priceStrategy.IsDirect,
                Tag = priceStrategy.Tag,
                IsReunionRoomTag = priceStrategy.PriceStrategyType == PriceStrategyType.GroupRoom,
                DatePrices = priceStrategy.DatePrice.Select(x =>
                {
                    return new Contracts.Common.Order.DTOs.HotelOrder.HotelDatePriceDto
                    {
                        Date = x.Date,
                        SalePrice = x.Price,
                        CostPrice = x.Cost,
                        Stock = x.Stock,
                    };
                })
            });
            foreach (var calendarPrice in calendarPrices)
            {
                //如果是团房预订单 价格为每日单价
                var channelPrice = item.NightlyPrices.FirstOrDefault(n => n.Date == calendarPrice.Date)?.ChannelPrice ?? calendarPrice.SalePrice;
                if (calendarPrice.PaymentCurrencyCode == item.CurrencyCode && channelPrice != calendarPrice.SalePrice)
                    calendarPrice.SalePrice = channelPrice;
            }
            var totalAmount = calendarPrices.Sum(x => x.SalePrice) * item.RoomCount;
            var discountItems = new List<OrderDiscountItemDto>();
            var discountAmount = discountItems.Sum(x => x.DiscountAmount);//优惠金额
            var paymentAmount = totalAmount - discountAmount;
            var createInput = new Contracts.Common.Order.DTOs.HotelOrder.CreateHotelOrderInput()
            {
                TenantId = currentUser.Tenant,
                SellingPlatform = SellingPlatform.System,
                SellingChannel = SellingChannels.B2b,
                ChannelOrderNo = input.ChannelOrderNo,
                GroupNo = input.GroupNo,
                CheckIn = item.CheckIn,
                CheckOut = item.CheckOut,
                RoomCount = item.RoomCount,
                Adults = item.RoomCount,
                ContactsName = input.ContactsName,
                ContactsPhoneNumber = input.ContactsPhoneNumber,
                ContactsEmail = input.ContactsEmail,
                Message = input.Message,
                DiscountItems = discountItems,
                DiscountAmount = discountAmount,
                PaymentAmount = paymentAmount,
                PaymentCurrencyCode = paymentCurrencyCode,
                TotalAmount = totalAmount,
                CalendarPrices = calendarPrices,
                SupplierApiType = item.SupplierApiType,
                HotelApiOrderInfo = hotelApiOrderInfo,
                HotelId = item.HotelId,
                HotelName = priceReponse.Data.HotelName,
                HotelEnName = priceReponse.Data.HotelEnName,
                IsAutoConfirmRoomStatus = priceReponse.Data.IsAutoConfirmRoomStatus,
                Room = _mapper.Map<Contracts.Common.Order.DTOs.HotelOrder.CheckSaleRoomDto>(room),
                PriceStrategy = _mapper.Map<Contracts.Common.Order.DTOs.HotelOrder.CheckSalePriceStrategyDto>(priceStrategy),
                UserInfo = userInfo,
                GroupBookingId = input.GroupBookingApplicationFormId,//团房申请单id
            };

            var groupBookingOrderAdditions = item.PreOrderAdditions
                .Select(x => new GroupBookingOrderAdditionDto
                {
                    AdditionName = x.AdditionName,
                    Amount = x.Amount,
                    Cost = x.Cost,
                    CostCurrencyCode = x.CostCurrencyCode,
                    CurrencyCode = x.CurrencyCode,
                    Quantity = x.Quantity,
                    Remark = x.Remark,
                })
                .ToArray();
            var apiHotel = apiHotelDetails.Where(x => x.Id == item.HotelId).First();
            groupBookingOrderItems.Add(new GroupBookingOrderItemDto
            {
                GroupBookingPreOrderItemId = item.Id!.Value,
                CreateHotelOrderInput = createInput,
                CheckInDate = item.CheckIn,
                CheckOutDate = item.CheckOut,
                CityCode = apiHotel.CityCode,
                CityName = apiHotel.CityName,
                HotelENName = apiHotel.ENName,
                HotelZHName = apiHotel.ZHName,
                ResourceHotelId = apiHotel.ResourceHotelId,
                Additions = groupBookingOrderAdditions,
            });
        });
        GroupBookingOrderDto groupBookingOrderDto = new()
        {
            SellingPlatform = SellingPlatform.System,
            SellingChannels = SellingChannels.B2b,
            GroupBookingPreOrderId = input.GroupBookingPreOrderId,
            GroupBookingApplicationFormId = input.GroupBookingApplicationFormId,
            UserInfo = userInfo,
            ContactsName = input.ContactsName,
            ContactsPhoneNumber = input.ContactsPhoneNumber,
            ContactsEmail = input.ContactsEmail,
            ChannelOrderNo = input.ChannelOrderNo,
            GroupNo = input.GroupNo,
            Message = input.Message,
        };
        //首款
        GroupBookingOrderDownPaymentDto downPayment = new()
        {
            LatestPaymentTime = preOrder.ValidityPeriod,
            InitialPaymentAmount = preOrder.InitialPaymentAmount,
            PaymentRatio = preOrder.InitialPaymentRatio,
            PaymentCurrencyCode = paymentCurrencyCode,
            FinalPaymentTime = preOrder.FinalPaymentTime,
        };
        OrderCreateInput orderCreateInput = new()
        {
            GroupBookingOrder = groupBookingOrderDto,
            OrderItemDtos = groupBookingOrderItems.ToArray(),
            DownPayment = downPayment,
        };
        var result = await _orderApiCaller.GroupBookingOrderCreate(orderCreateInput);
        var output = new SubmitOutput { GroupBookingOrderId = result.GroupBookingOrderId, PaymentId = result.PaymentId };
        return Ok(output);
    }
    private async Task<Contracts.Common.Order.DTOs.HotelOrder.HotelApiOrderInfo> GetHotelApiOrderInfo(Contracts.Common.Resource.DTOs.ThirdHotel.CheckAvailabilityInput input)
    {
        var checkAvailabilityOutput = await _resourceApiCaller.ThirdHotelCheckAvailability(input);
        switch (checkAvailabilityOutput.CheckCode)
        {
            case CheckPriceStrategySaleCode.Success:
                return new Contracts.Common.Order.DTOs.HotelOrder.HotelApiOrderInfo
                {
                    HotelId = checkAvailabilityOutput.HotelId,
                    RoomId = checkAvailabilityOutput.RoomId,
                    PriceStrategyId = checkAvailabilityOutput.PricestrategyId,
                    ResourceHotelId = input.ResourceHotelId,
                    SupplierApiType = input.SupplierApiType
                };
                break;
            default:
                throw new BusinessException(ErrorTypes.Order.SomeDatesAreUnavailable);
                break;
        }
    }

    private async Task<List<Contracts.Common.Order.DTOs.HotelOrder.HotelCalendarPriceDto>> GetHotelCalendarPrices(Contracts.Common.Order.DTOs.HotelOrder.HotelAgencyChannelPriceInput input)
    {

        var priceExchangeRate = await GetOrderPriceExchange(new OrderPriceExchangeRateInput
        {
            CostCurrencyCode = input.CostCurrencyCode,
            SaleCurrencyCode = input.SaleCurrencyCode,
            PaymentCurrencyCode = input.PaymentCurrencyCode,
        });
        decimal costExchangeRate = priceExchangeRate.CostExchangeRate;
        decimal exchangeRate = priceExchangeRate.ExchangeRate;
        var result = (from item in input.DatePrices
                      let channelPrice = item.SalePrice
                      select new Contracts.Common.Order.DTOs.HotelOrder.HotelCalendarPriceDto
                      {
                          Date = item.Date,
                          PaymentCurrencyCode = input.PaymentCurrencyCode,
                          SalePrice = Math.Round(channelPrice * exchangeRate, 2),
                          CostCurrencyCode = input.CostCurrencyCode,
                          CostPrice = item.CostPrice ?? 0,
                          OrgPriceCurrencyCode = input.SaleCurrencyCode,
                          OrgPrice = channelPrice,
                          CostExchangeRate = costExchangeRate,
                          ExchangeRate = exchangeRate,
                          Stock = item.Stock,
                      })
            .ToList();

        return result;
    }

    private async Task<OrderPriceExchangeRateOutput> GetOrderPriceExchange(OrderPriceExchangeRateInput input)
    {

        var costCurrencyCode = input.CostCurrencyCode;
        var saleCurrencyCode = input.SaleCurrencyCode;
        var paymentCurrencyCode = input.PaymentCurrencyCode;
        List<GetExchangeRatesInput> exchangeRatesInputs = new();
        var costEqualsSale = costCurrencyCode.Equals(saleCurrencyCode, StringComparison.OrdinalIgnoreCase);
        if (!costEqualsSale)
        {
            exchangeRatesInputs.Add(new GetExchangeRatesInput
            {
                BaseCurrencyCode = costCurrencyCode,
                TargetCurrencyCode = saleCurrencyCode
            });
        }
        var saleEqualsPayment = saleCurrencyCode.Equals(paymentCurrencyCode, StringComparison.OrdinalIgnoreCase);
        if (!saleEqualsPayment)
        {
            exchangeRatesInputs.Add(new GetExchangeRatesInput
            {
                BaseCurrencyCode = saleCurrencyCode,
                TargetCurrencyCode = paymentCurrencyCode
            });
        }
        var exchangeRates = await _paymentApiCaller.GetExchangeRates(exchangeRatesInputs);
        return new OrderPriceExchangeRateOutput
        {
            CostExchangeRate = costEqualsSale ? 1 : exchangeRates
                .Where(x => x.BaseCurrencyCode == costCurrencyCode && x.TargetCurrencyCode == saleCurrencyCode)
                .First().ExchangeRate,
            ExchangeRate = saleEqualsPayment ? 1 : exchangeRates
                .Where(x => x.BaseCurrencyCode == saleCurrencyCode && x.TargetCurrencyCode == paymentCurrencyCode)
                .First().ExchangeRate,
        };
    }



    /// <summary>
    /// 团房单查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(PagingModel<SearchOutput, IEnumerable<SearchSupplement>>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search([FromBody] OrderSearchInput input)
    {
        var searchInput = _mapper.Map<SearchInput>(input);
        searchInput.TenantId = HttpContext.GetTenantId();
        var result = await _orderApiCaller.GroupBookingOrderSearch(searchInput);
        return Ok(result);
    }

    /// <summary>
    /// 团房单导出数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(List<SearchOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> ExportData(ExportDataInput input)
    {
        var searchInput = _mapper.Map<SearchInput>(input);
        searchInput.TenantId = HttpContext.GetTenantId();
        searchInput.PageIndex = 1;
        searchInput.PageSize = 10000;
        var result = await _orderApiCaller.GroupBookingOrderSearch(searchInput);
        return Ok(result.Data);
    }

    /// <summary>
    /// 团房单详情
    /// </summary>
    /// <param name="groupBookingOrderId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(GetDetailOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetDetail(long groupBookingOrderId)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var result = await _orderApiCaller.GroupBookingOrderDetail(new Contracts.Common.Order.DTOs.GroupBookingOrder.DetailInput
        {
            GroupBookingOrderId = groupBookingOrderId,
        });
        GetDetailOutput output = _mapper.Map<GetDetailOutput>(result);
        output.GroupBookingOrder.ContactsEmail = DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto()
        {
            Id = groupBookingOrderId,
            IdType = SensitiveDataIdType.Hotel,
            DataType = SensitiveDataType.OrderContactsEmail,
            Data = output.GroupBookingOrder.ContactsEmail
        });
        output.GroupBookingOrder.ContactsPhoneNumber = DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto()
        {
            Id = groupBookingOrderId,
            IdType = SensitiveDataIdType.Hotel,
            DataType = SensitiveDataType.OrderContactsPhone,
            Data = output.GroupBookingOrder.ContactsPhoneNumber
        });
        var supplierIds = result.OrderItems.Where(x => x.SupplierId.HasValue)
            .Select(x => x.SupplierId!.Value)
            .ToArray();
        if (supplierIds.Any())
        {
            var suppliers = await _tenantApiCaller.GetSupplierDetailByIds(supplierIds);
            try
            {
                foreach (var item in output.OrderItems)
                {
                    item.SupplierName = suppliers.FirstOrDefault(x => x.SupplierId == item.SupplierId)?.FullName;
                    if (!string.IsNullOrWhiteSpace(item.BedType))
                    {
                        var bedTypes = JsonConvert.DeserializeObject<List<BedType>>(item.BedType!);
                        item.BedTypes = bedTypes;
                    }
                }
            }
            catch { }
        }

        var preOrder = (await _orderApiCaller.GetGroupBookingPreOrders(result.GroupBookingOrder.GroupBookingApplicationFormId, result.GroupBookingOrder.GroupBookingPreOrderId)).FirstOrDefault();
        if (preOrder is not null)
        {
            output.PreOrder = new HotelGroupBookingPreOrderOutput
            {
                CancellationPolicy = preOrder.CancellationPolicy,
                SpecialRemarks = preOrder.SpecialRemarks,
                RemindAdvanceDays = preOrder.RemindAdvanceDays,
            };
        }
        foreach (var item in output.OrderItems)
        {
            var preOrderItem = preOrder.PreOrderItems.FirstOrDefault(s => s.Id == item.GroupBookingPreOrderItemId);
            item.ChildPolicy = preOrderItem?.ChildPolicy;
        }

        //var hotelDetail = await _resourceApiCaller.GetHotelDetail(output.GroupBookingOrder.ResourceHotelId);
        //if (hotelDetail is not null)
        //{
        //    output.GroupBookingOrder.CountryCode=hotelDetail.CountryCode;
        //    output.GroupBookingOrder.HotelAddress = hotelDetail.Address;
        //    output.GroupBookingOrder.HotelTelePhone = hotelDetail.Telephone;
        //}

        return Ok(output);
    }

    /// <summary>
    /// 获取团房单入住名单信息
    /// </summary>
    /// <param name="groupBookingOrderId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(List<HotelOrderGuestItemOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetHotelOrderGuestItems(long groupBookingOrderId)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var detail = await _orderApiCaller.GroupBookingOrderDetail(new Contracts.Common.Order.DTOs.GroupBookingOrder.DetailInput
        {
            GroupBookingOrderId = groupBookingOrderId,
        });

        var cityCodes = detail.OrderItems.Select(x => x.CityCode).ToArray();
        var cities = await _resourceApiCaller.QueryCities(new Contracts.Common.Resource.DTOs.QueryInput
        {
            CityCodes = cityCodes
        });

        List<HotelOrderGuestItemOutput> result = detail.OrderItems
            .GroupBy(x => new { x.GroupBookingOrderId, x.ResourceHotelId, x.HotelZHName, x.HotelENName, x.CityCode })
            .Select(x => new HotelOrderGuestItemOutput
            {
                GroupBookingOrderId = x.Key.GroupBookingOrderId,
                ResourceHotelId = x.Key.ResourceHotelId,
                HotelZHName = x.Key.HotelZHName,
                HotelENName = x.Key.HotelENName,
                CityCode = x.Key.CityCode,
                CountryCode = cities.FirstOrDefault(s => s.CityCode == x.Key.CityCode)?.CountryCode ?? 0,
                ProvinceCode = cities.FirstOrDefault(s => s.CityCode == x.Key.CityCode)?.ProvinceCode ?? 0,
                BaseOrderIds = x.Select(s => s.BaseOrderId).ToArray(),
                HotelOrderStatus = x.Max(s => s.HotelOrderStatus),
                GuestFilePath = x.First().GuestFilePath,
                GroupBookingOrderItemIds = x.Select(s => s.Id).ToArray(),
            })
            .ToList();
        return Ok(result);
    }

    /// <summary>
    /// 上传入住人名单信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> AddOrderGuest(AddGuestInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        await _orderApiCaller.AddGroupBookingOrderGuest(new AddOrderGuestInput
        {
            GroupBookingOrderId = input.GroupBookingOrderId,
            OrderGuests = input.GroupBookingOrderItemIds.Select(i => new OrderGuestInput
            {
                GroupBookingOrderItemId = i,
                FilePath = input.FilePath,
                GuestInfos = input.GuestInfos.Select(g => new Contracts.Common.Order.DTOs.HotelOrder.HotelOrderGuestInfo
                {
                    RoomNumber = g.RoomNumber,
                    GuestName = g.GuestName,
                    FirstName = g.FirstName,
                    LastName = g.LastName
                })
            }).ToArray(),
        });
        return Ok();
    }

    /// <summary>
    /// 团房单调整尾款
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> AdjustFinalPayment(FinalPaymentAdjustInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        AdjustFinalPaymentInput adjustFinalPaymentInput = new()
        {
            GroupBookingOrderPaymentId = input.GroupBookingOrderPaymentId,
            BaseOrderId = input.BaseOrderId,
            WorkOrderId = input.WorkOrderId,
            AdjustAmount = input.AdjustAmount,
            Remark = input.Remark,
            OperationUser = new OperationUserDto
            {
                UserId = currentUser.UserId,
                UserType = Contracts.Common.Order.Enums.UserType.Merchant,
                Name = currentUser.NickName,
            }
        };
        await _orderApiCaller.AdjustFinalPayment(adjustFinalPaymentInput);
        return Ok();
    }

    /// <summary>
    /// 团房单导出Pdf
    /// </summary>
    /// <param name="groupBookingOrderId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(byte[]), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> ExportPdf(long groupBookingOrderId, string subOrderIds = null, string language = "zh")
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var groupBookingOrderDetails = await _orderApiCaller.GroupBookingOrderDetail(new Contracts.Common.Order.DTOs.GroupBookingOrder.DetailInput
        {
            GroupBookingOrderId = groupBookingOrderId,
        });

        var baseOrderIds = subOrderIds.Split(',');
        if (baseOrderIds != null && baseOrderIds.Any())
        {
            groupBookingOrderDetails.OrderItems = groupBookingOrderDetails.OrderItems
                .Where(x => baseOrderIds.Contains(x.BaseOrderId.ToString()))
                .ToList();
        }

        var hotelIds = groupBookingOrderDetails.OrderItems.Select(x => x.ResourceHotelId).Distinct().ToArray();
        var hotelDetails = await _resourceApiCaller.GetHotelDetailByHotelIds(hotelIds);
        var groupBookingOrderDetailByGroup = groupBookingOrderDetails.OrderItems.GroupBy(x => x.HotelId);
        var exportDetail = new ExportGroupBookingOrderPdfInput
        {
            GroupBookingApplicationFormId = groupBookingOrderDetails.GroupBookingOrder.GroupBookingApplicationFormId,
            GroupBookingOrderId = groupBookingOrderId,
            ExportHotelOrderType = Contracts.Common.Order.DTOs.HotelOrder.ExportHotelOrderType.CheckIn,
            TotalAmount = groupBookingOrderDetails.GroupBookingOrder.TotalPayment,
            PaymentCurrencyCode = groupBookingOrderDetails.GroupBookingOrder.PaymentCurrencyCode,
            Message = groupBookingOrderDetails.GroupBookingOrder.Message,
            Language = language,
            Hotels = groupBookingOrderDetailByGroup.Select(x =>
            {
                var hotelDetail = hotelDetails.FirstOrDefault(h => h.Id == x.First().ResourceHotelId);
                var obj = new ExportGroupBookingOrderOutput
                {
                    HotelId = x.Key,
                    HotelName = x.First().HotelZHName,
                    HotelEnName = x.First().HotelENName,
                    HotelAddress = hotelDetail.Address,
                    HotelEnAddress = hotelDetail.EnAddress,
                    HotelTelePhone = hotelDetail.Telephone,
                    OrderItems = x.Select(o => new ExportGroupBookingOrderItemOutput
                    {
                        BaseOrderId = o.BaseOrderId,
                        HotelRoomName = o.HotelRoomName,
                        HotelRoomEnName = o.HotelRoomEnName,
                        CheckInDate = o.CheckInDate,
                        CheckOutDate = o.CheckOutDate,
                        PriceStrategyNumberOfBreakfast = o.NumberOfBreakfast,
                        PriceStrategyRoomsCount = o.RoomCount,
                        PriceStrategyNightsCount = o.NumberOfBreakfast,
                        ConfirmCode = o.ConfirmCode,
                        HotelOrderGuests = o.HotelOrderGuests,
                    }).OrderBy(x => x.CheckInDate).ToList(),
                };
                return obj;
            }).ToList(),
        };

        var file = await _orderApiCaller.ExportHotelGroupBookingOrderPdf(exportDetail);
        return File(file, "application/pdf", language == "zh" ? "团房入住单.pdf" : "Group Room Reservation Form.pdf");
    }

    /// <summary>
    /// 团房Pdf导出确认单
    /// </summary>
    /// <param name="groupBookingOrderId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(byte[]), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> ExportConfirmPdf(long groupBookingOrderId, string subOrderIds = null, string language = "zh")
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var groupBookingOrderDetails = await _orderApiCaller.GroupBookingOrderDetail(new Contracts.Common.Order.DTOs.GroupBookingOrder.DetailInput
        {
            GroupBookingOrderId = groupBookingOrderId,
        });

        var baseOrderIds = subOrderIds.Split(',');
        if (baseOrderIds != null && baseOrderIds.Any())
        {
            groupBookingOrderDetails.OrderItems = groupBookingOrderDetails.OrderItems
                .Where(x => baseOrderIds.Contains(x.BaseOrderId.ToString()))
                .ToList();
        }
        var hotelIds = groupBookingOrderDetails.OrderItems.Select(x => x.ResourceHotelId).Distinct().ToArray();
        var hotelDetails = await _resourceApiCaller.GetHotelDetailByHotelIds(hotelIds);
        var groupBookingOrderDetailByGroup = groupBookingOrderDetails.OrderItems.GroupBy(x => x.HotelId);
        var exportDetail = new ExportGroupBookingOrderPdfInput
        {
            GroupBookingApplicationFormId = groupBookingOrderDetails.GroupBookingOrder.GroupBookingApplicationFormId,
            GroupBookingOrderId = groupBookingOrderId,
            ExportHotelOrderType = Contracts.Common.Order.DTOs.HotelOrder.ExportHotelOrderType.Confirm,
            TotalAmount = groupBookingOrderDetails.GroupBookingOrder.TotalPayment,
            PaymentCurrencyCode = groupBookingOrderDetails.GroupBookingOrder.PaymentCurrencyCode,
            Message = groupBookingOrderDetails.GroupBookingOrder.Message,
            Language = language,
            Hotels = groupBookingOrderDetailByGroup.Select(x =>
            {
                var hotelDetail = hotelDetails.FirstOrDefault(h => h.Id == x.First().ResourceHotelId);
                var obj = new ExportGroupBookingOrderOutput
                {
                    HotelId = x.Key,
                    HotelName = x.First().HotelZHName,
                    HotelEnName = x.First().HotelENName,
                    HotelAddress = hotelDetail.Address,
                    HotelEnAddress = hotelDetail.EnAddress,
                    HotelTelePhone = hotelDetail.Telephone,
                    OrderItems = x.Select(o => new ExportGroupBookingOrderItemOutput
                    {
                        BaseOrderId = o.BaseOrderId,
                        HotelRoomName = o.HotelRoomName,
                        HotelRoomEnName = o.HotelRoomEnName,
                        CheckInDate = o.CheckInDate,
                        CheckOutDate = o.CheckOutDate,
                        PriceStrategyNumberOfBreakfast = o.NumberOfBreakfast,
                        PriceStrategyRoomsCount = o.RoomCount,
                        PriceStrategyNightsCount = o.NumberOfBreakfast,
                        ConfirmCode = o.ConfirmCode,
                        HotelOrderGuests = o.HotelOrderGuests,
                    }).OrderBy(x => x.CheckInDate).ToList(),
                };
                return obj;
            }).ToList(),
        };

        var file = await _orderApiCaller.ExportHotelGroupBookingOrderPdf(exportDetail);
        return File(file, "application/pdf", language == "zh" ? "团房确认单.pdf" : "Group Room Confirmation Form.pdf");
    }
}
