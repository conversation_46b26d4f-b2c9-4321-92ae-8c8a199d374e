using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.ReceiptSettlementOrder;
using Bff.Vebk.Services.Interfaces;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs.ReceiptSettlementOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.DTOs.TenantBankAccount;
using Contracts.Common.Tenant.DTOs.Agency;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

/// <summary>
/// 收款结算单
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class ReceiptSettlementOrderController : ControllerBase
{
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IReceiptSettlementOrderService _receiptSettlementOrderService;
    private readonly IMapper _mapper;
    private readonly IPaymentApiCaller _paymentApiCaller;

    public ReceiptSettlementOrderController(
        ITenantApiCaller tenantApiCaller,
        IOrderApiCaller orderApiCaller,
        IMapper mapper,
        IPaymentApiCaller paymentApiCaller,
        IReceiptSettlementOrderService receiptSettlementOrderService)
    {
        _tenantApiCaller = tenantApiCaller;
        _orderApiCaller = orderApiCaller;
        _mapper = mapper;
        _receiptSettlementOrderService = receiptSettlementOrderService;
        _paymentApiCaller = paymentApiCaller;
    }

    /// <summary>
    /// 收款结算单分页数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(PagingModel<SearchReceiptSettlementOrderOutput>))]
    public async Task<IActionResult> Search(SearchReceiptSettlementOrderInput input)
    {
        //对账中数据默认展示B2B设置增加默认收款账号
        var searchResponse = await _orderApiCaller.SearchReceiptSettlementOrder(input);
        var pageResult = _mapper.Map<PagingModel<SearchReceiptSettlementOrderOutput>>(searchResponse);

        if (pageResult.Data.Any())
        {
            var agencyIds = pageResult.Data.Select(x => x.AgencyId).Distinct().ToList();
            var agencyList = await _tenantApiCaller.GetAgencyByIds(new GetAgenciesByIdsInput
            {
                AgencyIds = agencyIds
            });
            foreach (var item in pageResult.Data)
            {
                var agencyItem = agencyList.FirstOrDefault(x => x.Id == item.AgencyId);
                item.AgencyName = agencyItem?.FullName;     
                if (item.TenantDepartmentId.HasValue)
                {
                    item.TenantDepartmentName = agencyItem?.TenantDepartmentName;
                }
                if (item.Status != ReceiptSettlementOrderStatus.ReceiptComplete)
                {
                    var receivableAccount = await _receiptSettlementOrderService.GetReceivableAccount();
                    if (receivableAccount != null)
                    {
                        item.AccountName = receivableAccount.AccountName;
                        item.BankAccount = receivableAccount.AccountNo;
                        item.BankName = receivableAccount.BankName;
                        item.BankCode = receivableAccount.BankCode;
                        item.TenantBankAccountType = receivableAccount.TenantBankAccountType;
                        item.BankAccountType = receivableAccount.BankAccountType;
                    }
                }
                else
                {
                    var receivableAccounts = await _paymentApiCaller.GetTenantBankAccount(new GetTenantBankAccountInput()
                    {
                        AccountName = item.AccountName,
                        AccountNo = item.BankAccount,
                        BankCode = item.BankCode,
                    });
                    var receivableAccount = receivableAccounts?.FirstOrDefault();
                    item.TenantBankAccountType = receivableAccount?.TenantBankAccountType ?? 0;
                    item.BankAccountType = receivableAccount?.BankAccountType ?? 0;
                }
            }
        }

        return Ok(pageResult);
    }

    /// <summary>
    /// 收款结算单明细导出
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(ExportDetailBffOutput))]
    public async Task<IActionResult> ExportDetail(ExportDetailBffInput input)
    {

        var searchResponse = (await _receiptSettlementOrderService.ExportDetail(input.SettlementOrderId)).FirstOrDefault();
        var result = _mapper.Map<ExportDetailBffOutput>(searchResponse);
        return Ok(result);
    }

    /// <summary>
    /// 收款结算单-导出
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(IEnumerable<ExportOrderBffOutput>))]
    public async Task<IActionResult> ExportOrder(ExportOrderBffInput input)
    {
        var searchRequest = _mapper.Map<ExportOrderInput>(input);
        searchRequest.TenantId = HttpContext.GetTenantId();
        var searchResponse = await _orderApiCaller.ExportReceiptSettlementOrder(searchRequest);
        var result = _mapper.Map<IEnumerable<ExportOrderBffOutput>>(searchResponse);
        if (result.Any())
        {
            var agencyIds = result.Select(x => x.AgencyId).Distinct().ToList();
            var agencyList = await _tenantApiCaller.GetAgencyByIds(new GetAgenciesByIdsInput
            {
                AgencyIds = agencyIds
            });
            foreach (var item in result)
            {
                var agencyItem = agencyList.FirstOrDefault(x => x.Id == item.AgencyId);
                item.AgencyName = agencyItem?.FullName;
                if (item.TenantDepartmentId.HasValue)
                {
                    item.TenantDepartmentName = agencyItem?.TenantDepartmentName;
                }
            }
        }

        return Ok(result);
    }

    /// <summary>
    /// 发送创建邮件
    /// </summary>
    /// <param name="bffInput"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200)]
    public async Task<IActionResult> SendCreateEmail(SendCreateEmailBffInput bffInput)
    {
        var emailAttachmentsData = await _receiptSettlementOrderService.ExportDetail(bffInput.Ids);
        var setting = await _orderApiCaller.GetGroupBookingPreOrderSetting();
        foreach (var item in emailAttachmentsData)
        {
            item.ElectronicSeal = setting?.ElectronicSeal;
        }
        await _orderApiCaller.SendReceiptSettlementOrderCreateEmail(emailAttachmentsData);
        return Ok();
    }

    #region 收款记录
    /// <summary>
    /// 获取收款记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(IEnumerable<GetReceiptSettlementOrderRecordsBffOutput>))]
    public async Task<IActionResult> GetReceiptSettlementOrderRecords(GetReceiptSettlementOrderRecordsBffInput input)
    {
        var searchInput = _mapper.Map<GetReceiptSettlementOrderRecordsInput>(input);
        var result = await _orderApiCaller.GetReceiptSettlementOrderRecords(searchInput);
        return Ok(result);
    }

    /// <summary>
    /// 添加收款记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(long))]
    [SwaggerResponseExt(default, ErrorTypes.Order.ReceivedAmountNotMoreThan, ErrorTypes.Order.ReceivedAmountCurrencyCodeChanged)]
    public async Task<IActionResult> AddReceiptSettlementOrderRecord(AddReceiptSettlementOrderRecordBffInput input)
    {
        long? id = input.Id ;
        if (!input.Id.HasValue)
        {
            var addInput = _mapper.Map<AddReceiptSettlementOrderRecordInput>(input);
            id = await _orderApiCaller.AddReceiptSettlementOrderRecord(addInput);
        }
        if (input.IsReceipt)
        {
            var receiptInput = _mapper.Map<ReceiptInput>(input);
            await _orderApiCaller.ReceiptSettlementOrder(receiptInput);
        }
        return Ok(id);
    }
    #endregion

    /// <summary>
    /// 未收款预警总数
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(long))]
    public async Task<IActionResult> UnReceiptByEarlyWarningCount()
    {
        var datas = await _orderApiCaller.UnReceiptByEarlyWarning(new UnReceiptByEarlyWarningInput { 
            PageIndex = 1,
            PageSize = 1,
        });
        return Ok(datas.Total);
    }

    /// <summary>
    /// 未收款预警列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(PagingModel<UnReceiptByEarlyWarningBffOutput>))]
    public async Task<IActionResult> UnReceiptByEarlyWarning(UnReceiptByEarlyWarningBffInput input)
    {
        var searchInput = _mapper.Map<UnReceiptByEarlyWarningInput>(input);
        var datas = await _orderApiCaller.UnReceiptByEarlyWarning(searchInput);
        var result = _mapper.Map<PagingModel<UnReceiptByEarlyWarningBffOutput>>(datas);

        var agencyIds = datas.Data.Select(x => x.AgencyId);
        var agencies = await _tenantApiCaller.GetAgencyByIds(new GetAgenciesByIdsInput { 
            AgencyIds = agencyIds.ToList(),
        });
        var agencyCreditDetails = await _tenantApiCaller.AgencyCreditDetails(agencyIds.ToArray());
        foreach (var data in result.Data)
        { 
            data.AgencyName = agencies?.FirstOrDefault(x=> x.Id == data.AgencyId)?.FullName ?? string.Empty;
            data.AgencyCreditStatus = agencyCreditDetails?.FirstOrDefault(x => x.AgencyId == data.AgencyId)?.Status ?? false;
        }

        return Ok(result);
    }

    /// <summary>
    /// 未收款预警明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(PagingModel<UnReceiptDetailsByEarlyWarningBffOutput>))]
    public async Task<IActionResult> UnReceiptDetailsByEarlyWarning(UnReceiptDetailsByEarlyWarningBffInput input)
    {
        var searchInput = _mapper.Map<UnReceiptDetailsByEarlyWarningInput>(input);
        var datas = await _orderApiCaller.UnReceiptDetailsByEarlyWarning(searchInput);
        var result = _mapper.Map<PagingModel<UnReceiptDetailsByEarlyWarningBffOutput>>(datas);
        return Ok(result);
    }
}