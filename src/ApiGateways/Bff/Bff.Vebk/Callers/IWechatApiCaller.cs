using Common.Caller;
using Contracts.Common.WeChat.DTOs;
using Contracts.Common.WeChat.DTOs.Huizhi;
using Contracts.Common.WeChat.DTOs.XiaoHongShuConfiguration;
using Contracts.Common.WeChat.Enums;

namespace Bff.Vebk.Callers;

public interface IWechatApiCaller : IHttpCallerBase
{
    Task<GetOauth2AccessTokenOutput> GetHuizhiOpenId(GetOauth2AccessTokenInput input);

    #region WechatConfiguration
    /// <summary>
    /// 获取微信配置
    /// </summary>
    /// <param name="authType"></param>
    /// <returns></returns>
    Task<WechatConfigurationOutput> GetWechatConfiguration(AuthType authType);
    #endregion

    Task<GetOutput> GetXiaoHongShuConfiguration();

    Task SetXiaoHongShuConfiguration(XiaoHongShuConfigurationDto dto);

    #region Huizhi

    /// <summary>
    /// 汇智 获取小程序码
    /// 调用本 API 可以获取小程序码，适用于需要的码数量极多的业务场景。通过该接口生成的小程序码，永久有效，数量暂无限制。
    /// </summary>
    /// <param name="input"></param>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task<string> GetHuiZhiWxacodeUnlimit(GetWxacodeUnlimitInput input);

    #endregion

    #region WechatMP

    /// <summary>
    /// 获取公众号二维码url
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<string> GetWechatMPQrCode(CreateMpQrCodeInput input);

    /// <summary>
    /// 生成带参数的二维码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CreateMpQrCodeOutput> CreateWechatMPQrCode(CreateMpQrCodeInput input);

    #endregion
}
