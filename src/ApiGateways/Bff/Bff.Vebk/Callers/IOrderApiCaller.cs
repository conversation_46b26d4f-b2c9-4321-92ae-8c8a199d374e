using Bff.Vebk.Models.OrderPrintSetting;
using Bff.Vebk.Models.ReceiptOrder;
using Bff.Vebk.Models.ReceiptSettlementOrder;
using Bff.Vebk.Models.SettlementPayables;
using Common.Caller;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.AggregateOrder;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.DTOs.BaseOrderRemark;
using Contracts.Common.Order.DTOs.BatchOrderOperationTask;
using Contracts.Common.Order.DTOs.CarProductSupplierOrder;
using Contracts.Common.Order.DTOs.CompensationOrder;
using Contracts.Common.Order.DTOs.ExportPdf;
using Contracts.Common.Order.DTOs.Finance;
using Contracts.Common.Order.DTOs.GroupBooking;
using Contracts.Common.Order.DTOs.HotelApiOrder;
using Contracts.Common.Order.DTOs.HotelOrder;
using Contracts.Common.Order.DTOs.Insure;
using Contracts.Common.Order.DTOs.Invoice;
using Contracts.Common.Order.DTOs.InvoiceTitle;
using Contracts.Common.Order.DTOs.Kingdee.KingdeePush;
using Contracts.Common.Order.DTOs.KlookOrder;
using Contracts.Common.Order.DTOs.OffsetOrder;
using Contracts.Common.Order.DTOs.OffsetOrderDingtalkApply;
using Contracts.Common.Order.DTOs.OpenChannelOrder;
using Contracts.Common.Order.DTOs.OpenChannelRefundApplyOrder;
using Contracts.Common.Order.DTOs.OpenChannelSyncFailOrder;
using Contracts.Common.Order.DTOs.OpenSupplierOrder;
using Contracts.Common.Order.DTOs.OrderDelayed;
using Contracts.Common.Order.DTOs.OrderManualVoucher;
using Contracts.Common.Order.DTOs.OrderOperationTask;
using Contracts.Common.Order.DTOs.OrderPrintSetting;
using Contracts.Common.Order.DTOs.ReceiptSettlementOrder;
using Contracts.Common.Order.DTOs.RefundOrder;
using Contracts.Common.Order.DTOs.ReservationOrder;
using Contracts.Common.Order.DTOs.ScenicTicketOrder;
using Contracts.Common.Order.DTOs.ScenicTicketOrder.OTA;
using Contracts.Common.Order.DTOs.ScenicTicketPurchaseOrder;
using Contracts.Common.Order.DTOs.ScenicTicketSupplierOrder;
using Contracts.Common.Order.DTOs.SettlementPayables;
using Contracts.Common.Order.DTOs.ThirdInsureProduct;
using Contracts.Common.Order.DTOs.TicketOrder;
using Contracts.Common.Order.DTOs.TicketsCombinationOrder;
using Contracts.Common.Order.DTOs.TravelLineOrder;
using Contracts.Common.Order.DTOs.TravelLineOrder.OTA;
using Contracts.Common.Order.DTOs.WorkOrder;
using Contracts.Common.Order.DTOs.WorkOrderServiceEvaluation;
using EfCoreExtensions.Abstract;
using AfterSaleFinancialHandleOrder = Contracts.Common.Order.DTOs.AfterSaleFinancialHandleOrder;
using CarProductOrder = Contracts.Common.Order.DTOs.CarProductOrder;
using GroupBookingOrder = Contracts.Common.Order.DTOs.GroupBookingOrder;
using MailOrder = Contracts.Common.Order.DTOs.MailOrder;
using SettlementOrder = Contracts.Common.Order.DTOs.SettlementOrder;
using TravelLineOrder = Contracts.Common.Order.DTOs.TravelLineOrder;
using GroupBookingFinancialHandleOrder = Contracts.Common.Order.DTOs.GroupBookingFinancialHandleOrder;
using Contracts.Common.Order.DTOs.GroupBookingAreaSetting;
using Contracts.Common.Order.DTOs.GroupBookingAggregate;
using Contracts.Common.Order.DTOs.TicketCombinationOrderAfterSalse;
using Contracts.Common.Order.DTOs.YouxiaTripOrder;

namespace Bff.Vebk.Callers;

public interface IOrderApiCaller : IHttpCallerBase
{
    #region HotelApiOrder

    /// <summary>
    /// 汇智酒店状态变更通知
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<HopOrderStatusNotifyOutput> HopOrderStatusNotify(HopOrderStatusNotifyInput input);

    /// <summary>
    /// 汇智酒店订单变更通知
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<HopOrderNotifyOutput> HopOrderNotify(HopOrderNotifyInput input);

    #endregion

    #region 线路订单

    /// <summary>
    /// 线路订单搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<TravelLineOrder.SearchOutput, OrderStatusStatOutput>> TravelLineOrderSearch(TravelLineOrder.SearchInput input);

    /// <summary>
    /// 线路订单详情
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="ErrorTypes.Order.NoMatchingData"></exception>
    /// <returns></returns>
    Task<TravelLineOrder.OrderDetailOutput> TravelLineOrderDetail(TravelLineOrder.OrderDetailInput input);

    /// <summary>
    /// 线路订单认领
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="ErrorTypes.Common.NotSupportedOperation"></exception>
    /// <returns></returns>
    Task TravelLineOrderClaim(TravelLineOrder.ClaimInput input);

    /// <summary>
    /// 线路订单确认
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="ErrorTypes.Common.NotSupportedOperation"></exception>
    /// <returns></returns>
    Task TravelLineOrderConfirm(TravelLineOrder.ConfirmInput input);

    /// <summary>
    /// 线路订单完成
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="ErrorTypes.Common.NotSupportedOperation"></exception>
    /// <returns></returns>
    Task TravelLineOrderFinish(TravelLineOrder.FinishInput input);

    /// <summary>
    /// 线路订单重发确认短信
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="ErrorTypes.Common.NotSupportedOperation"></exception>
    /// <exception cref="ErrorTypes.Notify.NotifyNotOpen"></exception>
    /// <returns></returns>
    Task TravelLineOrderSendConfirmSms(TravelLineOrder.SendConfirmSmsInput input);

    /// <summary>
    /// 线路订单更新导游确认信息
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="ErrorTypes.Common.NotSupportedOperation"></exception>
    /// <returns></returns>
    Task TravelLineOrderUpdateTourGuide(TravelLineOrder.UpdateTourGuideInput input);

    /// <summary>
    /// 线路订单 可退款信息
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="ErrorTypes.Order.OrderCannotRefund"></exception>
    /// <returns></returns>
    Task<TravelLineOrder.OrderRefundableOutput> TravelLineOrderRefundable(TravelLineOrder.OrderRefundableInput input);

    /// <summary>
    /// 线路订单退款
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="ErrorTypes.Order.OrderCannotRefund"></exception>
    /// <exception cref="ErrorTypes.Order.RefundAmountInvalid"></exception>
    /// <returns></returns>
    Task TravelLineOrderRefund(TravelLineOrder.OrderRefundInput input);

    /// <summary>
    /// 创建订单
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    Task<CreateLineOrderOutput> TravelLineOrderCreate(TravelLineOrder.CreateDto input);

    /// <summary>
    /// 创建OTA线路订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CreateTravelLineOTAOrderOutput> CreateOTATravelLineOrder(TravelLineOrder.CreateDto input);

    /// <summary>
    /// 获取渠道单信息
    /// </summary>
    /// <param name="channelOrderNo"></param>
    /// <returns></returns>
    Task<TravelLineOrder.GetLineChannelOrderInfoOutput> GetLineChannelOrderInfo(string channelOrderNo);

    Task UpdateTravelLineOrderSupplierOrderId(UpdateSupplierOrderIdInput input);

    Task EditTravelLineOrderCost(UpdateCostInput input);

    /// <summary>
    /// 修改线路订单联系人
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    Task<bool> UpdateTravelLineOrderContact(UpdateContactInput input);

    /// <summary>
    /// 修改线路订单出行人信息
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    Task<bool> UpdateTravelLineOrderTravelInfo(TravelLineOrder.UpdateTravelInfoInput input);

    /// <summary>
    /// 更新线路订单渠道单号
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateTravelLineOrderChannelOrderNo(UpdateChannelOrderNoInput input);

    /// <summary>
    /// 更新线路订单折扣比例
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateTravelLineCostDiscountRate(UpdateCostDiscountRateInput input);

    Task UpdateTravelLineTravelTime(UpdateTravelTimeInput input);


    /// <summary>
    /// 查询线路订单sku项子类型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<QueryTravelLineOrderSkuTypeItemOutput> QueryTravelLineOrderSkuTypeItems(QueryTravelLineOrderSkuTypeItemInput input);
    
    /// <summary>
    /// 线路订单替换产品
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task ReplaceLineOrderProduct(Contracts.Common.Order.DTOs.TravelLineOrder.ReplaceOrderProductInput input);

    /// <summary>
    /// 线路订单手动添加凭证
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task TravelLineOrderVoucherManualAdd(ManualAddVoucherInput input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ManualDeliveryVoucherOutput> TravelLineOrderVoucherManualDelivery(ManualDeliveryVoucherInput input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<LineOrderSendEmailSyncOutput> LineOrderSendEmailSync(LineOrderSendEmailSyncInput input);
    #endregion

    #region 票券订单

    /// <summary>
    /// 可预约
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    Task<GetReservationabledOutput> GetTicketOrderReservationabled(long baseOrderId);

    Task UpdateTicketOrderSupplierOrderId(UpdateSupplierOrderIdInput input);

    Task<GetTicketOrderOutput> GetTicketOrderId(long baseOrderId);

    #endregion

    #region MailOrder

    /// <summary>
    /// 订单详情
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    Task<MailOrder.OrderDetailsOutput> GetMailOrderByBaseOrderId(long baseOrderId);

    #endregion

    #region ReservationOrder

    /// <summary>
    /// 获取预约单信息
    /// </summary>
    /// <param name="reservationOrderId"></param>
    /// <returns></returns>
    Task<GetReservationOrderOutput> GetReservationOrder(long reservationOrderId);

    /// <summary>
    /// 获取预约单支付简讯
    /// </summary>
    /// <param name="orderId">预约单Id</param>
    /// <returns></returns>
    Task<PaymentInfoOutput> GetReservationOrderPaymentInfo(long orderId);

    #endregion

    #region Baseorder

    /// <summary>
    /// 获取订单支付简讯
    /// </summary>
    /// <param name="orderId">BaseOrderId</param>
    /// <returns></returns>
    Task<PaymentInfoOutput> GetBaseOrderPaymentInfo(long orderId);

    /// <summary>
    /// 根据订单类型订单号 获取订单价格信息（对账单订单总额、优惠金额）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IList<OrderAmountInfoOutput>> GetOrderAmountInfos(OrderAmountInfoInput input);

    /// <summary>
    /// 根据主单id获取多价格信息
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    Task<List<OrderMultPriceOutput>> GetOrderPriceByBaseOrderId(long baseOrderId);

    /// <summary>
    /// 获取主单号关联的退款单
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    Task<List<RefundOrderDetailOutDto>> GetRefundsByBaseOrderId(long baseOrderId);

    /// <summary>
    /// 获取子单号关联的退款单
    /// </summary>
    /// <param name="subOrderId"></param>
    /// <returns></returns>
    Task<List<RefundOrderDetailOutDto>> GetRefundsBySubOrderId(long subOrderId);

    /// <summary>
    /// 通过用户id查询消费统计
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    Task<List<SearchUserConsumptionOutput>> GetConsumptionByIds(List<long> ids);

    /// <summary>
    /// 查询用户消费统计分页数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchUserConsumptionOutput>> SearchConsumptionStatistic(SearchUserConsumptionPageInput input);

    /// <summary>
    /// 检查分销商渠道单信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CheckChannelOrderOutput> CheckChannelOrder(CheckChannelOrderInput input);

    /// <summary>
    /// 检查渠道异常单-渠道单号
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CheckChannelOrderOutput> CheckChannelOrderAbnormalOrder(CheckChannelOrderInput input);
    
    /// <summary>
    /// 检查供应商订单信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CheckSupplierOrderOutput> CheckSupplierOrder(CheckSupplierOrderInput input);

    /// <summary>
    /// 设置跟单人
    /// </summary>
    /// <param name="orderId"></param>
    /// <param name="userId"></param>
    /// <returns></returns>
    Task<bool> SetTrackingUserId(SetTrackingUserIdInput input);

    /// <summary>
    /// 取消未支付订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CancelBaseOrder(CancelOrderInput input);

    /// <summary>
    /// 订单操作记录
    /// </summary>
    /// <param name="orderId"></param>
    /// <returns></returns>
    Task<IEnumerable<OrderLogsOutput>> OrderOperatingRecords(long orderId);

    Task<bool> SetOrderOpUserId(SetOrderOpUserIdInput input);

    #endregion

    #region SettlementOrder

    /// <summary>
    /// 查询应付款订单的金额(暂时不包括预约单)
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<SettlementOrder.GetPayableOrderAmountOutput>> GetPayableOrderAmount(SettlementOrder.GetPayableOrderAmountInput input);

    /// <summary>
    /// 通过订单id查询应付结算单数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<SettlementOrder.GetPayableOrderDetailOutput>> GetPayableOrderDetailByOrderId(SettlementOrder.GetPayableOrderDetailInput input);

    Task<List<SettlementOrder.SettlementOrderTransferRecordOutput>> SearchSettlementOrderTransferRecordByIds(IEnumerable<long> ids);

    #endregion

    #region OffsetOrder

    /// <summary>
    /// 获取抵冲单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<GetOffsetOrderListOutput>> GetOffsetOrderList(GetOffsetOrderListInput input);

    /// <summary>
    /// 搜索抵冲单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchOffsetOrderOutput>> SearchOffsetOrder(SearchOffsetOrderInput input);

    /// <summary>
    /// 导出抵冲单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<SearchOffsetOrderOutput>> ExportSearchOffsetOrder(SearchOffsetOrderInput input);

    /// <summary>
    /// 抵冲单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GetOffsetOrderDetailOutput> OffsetOrderDetail(long id);

    /// <summary>
    /// 添加抵充单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<long> AddOffsetOrder(AddOffsetOrderInput input);

    #endregion

    #region ReceiptSettlementOrder

    /// <summary>
    /// 通过订单id查询应收结算单数据
    /// </summary>
    /// <param name="orderId"></param>
    /// <returns></returns>
    Task<IEnumerable<GetReceivableOrderDetailOutput>> GetReceivableOrderDetailByOrderId(GetReceivableOrderDetailInput input);

    /// <summary>
    /// 收款结算单-分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<Contracts.Common.Order.DTOs.ReceiptSettlementOrder.SearchOutput>> SearchReceiptSettlementOrder(
        SearchReceiptSettlementOrderInput input);

    /// <summary>
    /// 收款结算单-导出明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ExportDetailOutput> ExportReceiptSettlementOrderDetail(ExportDetailInput input);

    /// <summary>
    /// 收款结算单-导出订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<ExportOrderOutput>> ExportReceiptSettlementOrder(ExportOrderInput input);

    /// <summary>
    /// 收款结算单-导出，明细查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<SearchDetailExportOutput>> SearchExportReceiptSettlementOrderDetail(SearchDetailExportInput input);

    /// <summary>
    /// 查询收款结算单记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<GetReceiptSettlementOrderRecordsOutput>> GetReceiptSettlementOrderRecords(GetReceiptSettlementOrderRecordsInput input);

    /// <summary>
    /// 添加收款结算单记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<long> AddReceiptSettlementOrderRecord(AddReceiptSettlementOrderRecordInput input);

    /// <summary>
    /// 添加收款结算单记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task ReceiptSettlementOrder(ReceiptInput input);

    /// <summary>
    /// 获取自动生成账单配置
    /// </summary>
    /// <returns></returns>
    Task<ReceiptOrderSettingOutput> GetReceiptOrderSetting();

    /// <summary>
    /// 更新自动生成账单配置
    /// </summary>
    /// <returns></returns>
    Task<long> UpdateReceiptOrderSetting(UpdateReceiptOrderSettingInput input);

    /// <summary>
    /// 未收款预警
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<UnReceiptByEarlyWarningOutput>> UnReceiptByEarlyWarning(UnReceiptByEarlyWarningInput input);

    /// <summary>
    /// 未收款预警明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<UnReceiptDetailsByEarlyWarningOutput>> UnReceiptDetailsByEarlyWarning(UnReceiptDetailsByEarlyWarningInput input);
    #endregion

    #region ScenicTicketPurchaseOrder

    /// <summary>
    /// 创建门票采购订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CreateScenicTicketPurchaseOrder(CreateTicketPurchaseOrderInput input);

    /// <summary>
    /// 编辑门票采购订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task EditScenicTicketPurchaseOrder(EditTicketPurchaseOrderInput input);

    /// <summary>
    /// 查询采购单基础详情
    /// </summary>
    /// <param name="purchaseOrderIds"></param>
    /// <returns></returns>
    Task<List<GetTicketPurchaseOrderDetailOutput>> ScenicTicketPurchaseOrderDetails(params long[] purchaseOrderIds);

    /// <summary>
    /// 门票采购单分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchTicketPurchaseOrderOutput>> SearchScenicTicketPurchaseOrder(SearchTicketPurchaseOrderInput input);

    /// <summary>
    /// 门票采购单明细导出
    /// </summary>
    /// <param name="purchaseOrderId"></param>
    /// <returns></returns>
    Task<List<ExportTicketPurchaseOutput>> ExportScenicTicketPurchaseOrder(long purchaseOrderId);

    /// <summary>
    /// 查询采购批次总库存
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GetScenicTicketPurchaseInventoryOutput> GetScenicTicketPurchaseInventory(GetScenicTicketPurchaseInventoryInput input);


    /// <summary>
    /// 采购门票PDF发货
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ScenicTicketOrderDeliveryOutput> RetryOrderDelivery(ScenicTicketOrderDeliveryInput input);

    /// <summary>
    /// 采购导入订单导出
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<ExportPurchaseOrderListOutput>> ExportPurchaseOrderList(ExportPurchaseOrderListInput input);

    /// <summary>
    /// 门票采购单出库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task OutboundScenicTicketPurchaseOrder(OutboundPurchaseOrderInput input);

    /// <summary>
    /// 查询出库的凭证
    /// </summary>
    /// <param name="purchaseOrderIds"></param>
    /// <returns></returns>
    Task<List<GetOutboundPurchaseVoucherOutput>> GetScenicTicketPurchaseOutboundVouchers(params long[] purchaseOrderIds);

    /// <summary>
    /// 解析采购文件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<BatchCreateParsePurchaseFileDataOutput>> ParsePurchaseFileData(BatchCreateParsePurchaseFileDataInput input);

    /// <summary>
    /// 批量创建门票采购单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<long>> BatchCreateTicketPurchaseOrder(BatchCreateTicketPurchaseOrderInput input);

    /// <summary>
    /// 库存盘点
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<TicketPurchaseInvCheckOutput> TicketPurchaseInventoryCheck(TicketPurchaseInvCheckInput input);


    /// <summary>
    /// 库存盘点记录查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchTicketPurchaseInvCheckRecordOutput>> SearchTicketPurchaseInventoryCheckRecord(
        SearchTicketPurchaseInvCheckRecordInput input);

    /// <summary>
    /// 查询采购囤票消耗明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchPurchaseConsumptionOutput>> SearchPurchaseConsumption(SearchPurchaseConsumptionInput input);

    #endregion

    #region ScenicTicketOrder

    /// <summary>
    /// 创建门票OTA订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CreateScenicOTAOrderOutput> CreateScenicOTAOrder(CreateScenicOTAOrderInput input);

    /// <summary>
    /// 创建门票组合套餐OTA订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CreateScenicOTACombinationOrderOutput> CreateScenicCombinationOTAOrderCreate(CreateScenicOTACombinationOrderInput input);

    /// <summary>
    /// OTA平台手动发货
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task ScenicOTAFinishByManual(OTAOrderManualDeliveryInput input);

    /// <summary>
    /// 门票订单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ScenicTicketOrderDetailOutput> ScenicTicketOrderDetail(ScenicTicketOrderDetailInput input);

    /// <summary>
    /// 查询门票订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchScenicTicketOrderOutput, ScenicTicketOrderStatusCount>> SearchScenicTicketOrder(SearchScenicTicketOrderInput input);


    /// <summary>
    /// 查询景区门票订单分页数据 - 订单状态数量统计
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ScenicTicketOrderStatusCount> SearchScenicTicketOrderStatusCount(SearchScenicTicketOrderInput input);

    /// <summary>
    /// 门票凭证手动新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task ScenicTicketVoucherManualAdd(ManualAddVoucherInput input);

    /// <summary>
    /// 门票凭证手动发货
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ManualDeliveryVoucherOutput> ScenicTicketVoucherManualDelivery(ManualDeliveryScenicTicketVoucherInput input);

    /// <summary>
    /// 查询门票-渠道单数据
    /// </summary>
    /// <param name="channelOrderNo"></param>
    /// <returns></returns>
    Task<GetChannelOrderInfoOutput> GetScenicTicketChannelOrderInfo(string channelOrderNo);

    Task UpdateScenicTicketSupplierOrderId(UpdateSupplierOrderIdInput input);

    Task EditScenicTicketOrderCost(UpdateCostInput input);


    /// <summary>
    /// 创建门票订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CreateScenicTicketOrderOutput> CreateScenicTicketOrderByManual(Contracts.Common.Order.DTOs.ScenicTicketOrder.CreateByManualInput input);

    Task<bool> UpdateScenicTicketTravelInfo(Contracts.Common.Order.DTOs.ScenicTicketOrder.UpdateTravelInfoInput input);

    Task<bool> UpdateScenicTicketOrderConfirmation(Contracts.Common.Order.DTOs.ScenicTicketOrder.UpdateOrderConfirmationInput input);

    /// <summary>
    /// 修改订单联系人
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    Task<bool> UpdateScenicTicketOrderContact(UpdateContactInput input);


    /// <summary>
    /// 
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    Task<List<GetScenicOrderSimpleInfoOutput>> GetScenicTicketOrderSimpleInfo(params long[] baseOrderIds);

    Task UpdateScenicTicketCostDiscountRate(UpdateCostDiscountRateInput input);

    Task UpdateScenicTicketTravelTime(UpdateTravelTimeInput input);

    /// <summary>
    /// 门票订单替换产品
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task ReplaceScenicTicketOrderProduct(Contracts.Common.Order.DTOs.ScenicTicketOrder.ReplaceOrderProductInput input);


    /// <summary>
    /// 门票  - 订单重发邮件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ScenicTicketOrderSendEmailSyncOutput> ScenicTicketOrderSendEmailSync(ScenicTicketOrderSendEmailSyncInput input);
    #endregion

    #region ReceiptOrder

    /// <summary>
    /// (入账)应收款结算单预览
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<PreCreateOutput>> ReceiptSettlementOrderPreCreate(PreCreateInput input);

    /// <summary>
    /// (入账)应收款结算单创建
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ReceiptSettlementOrderCreateOutput> ReceiptSettlementOrderCreate(PreCreateInput input);

    /// <summary>
    /// 应收款创建成功发送邮件
    /// </summary>
    /// <param name="attachmentsData"></param>
    /// <returns></returns>
    Task SendReceiptSettlementOrderCreateEmail(List<ExportDetailOutput> attachmentsData);

    /// <summary>
    /// 应收款-酒店订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<ReceivablesHotelOrderOutput>> ReceivablesHotelOrder(ReceivablesSearchBffInput input);

    /// <summary>
    /// 应收款-券类订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<ReceivablesTicketOrderOutput>> ReceivablesTicketOrder(ReceivablesSearchBffInput input);

    /// <summary>
    /// 应收款-预约单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<ReceivablesReservationOrderOutput>> ReceivablesReservationOrder(ReceivablesSearchBffInput input);

    /// <summary>
    /// 应收款-门票订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<ReceivablesScenicOrderOutput>> ReceivablesScenicOrder(ReceivablesSearchBffInput input);

    /// <summary>
    /// 应收款-线路订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<ReceivablesLineOrderOutput>> ReceivablesLineOrder(ReceivablesSearchBffInput input);

    /// <summary>
    /// 应收款-用车订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<ReceivablesCarProductOrderOutput>> ReceivablesCarProductOrder(ReceivablesSearchBffInput input);

    /// <summary>
    /// 应收款-退款订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<ReceivablesRefundOrderOutput>> ReceivablesRefundOrder(ReceivablesSearchBffInput input);

    /// <summary>
    /// 关联退款单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<ReceivablesRefundOrderOutput>> ReceivablesRelatedRefundOrder(RelatedReceivablesSearchInput input);


    /// <summary>
    /// 应收款-入账抵冲单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<ReceivablesOffsetOrderOutput>> ReceivablesOffsetOrder(ReceivablesSearchBffInput input);

    /// <summary>
    /// 关联抵冲单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<ReceivablesOffsetOrderOutput>> ReceivablesRelatedOffsetOrder(RelatedReceivablesSearchInput input);

    /// <summary>
    /// 应收款-订单导出
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ReceivablesOrderExportOutput> ExportReceivablesOrder(ReceivablesOrderExportInput input);

    /// <summary>
    /// 获取自动对账模板
    /// </summary>
    /// <returns></returns>
    Task<byte[]> ExportAutoReconciliationTemplate();

    /// <summary>
    /// 自动对账预览
    /// </summary>
    /// <returns></returns>
    Task<AutoReconciliationOutput> AutoReconciliationPre(AutoReconciliationInput input);
    #endregion

    #region HotelOrder

    /// <summary>
    /// 获取Hop 酒店订单最新信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<HopHotelOrderUpdatedDto> GetHotelHopUpdatedInfo(HopHotelOrderUpdatedInput input);

    /// <summary>
    /// 酒店日历房下单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CreateHotelOrderOutput> HotelOrderCreate(CreateHotelOrderInput input);

    /// <summary>
    /// 日历房订单可退款信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GetRefundableDto> GetHotelOrderRefundable(GetRefundableInput input);

    /// <summary>
    /// 酒店日历房详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<Contracts.Common.Order.DTOs.HotelOrder.DetailOutput> HotelOrderDetail(DetailInput input);

    Task UpdateHotelOrderSupplierOrderId(UpdateSupplierOrderIdInput input);


    /// <summary>
    /// 导出酒店入住单pdf
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    Task<byte[]> ExportHotelOrderPdf(ExportHotelOrderPdfInput input);


    /// <summary>
    /// 团房入住单pdf
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    Task<byte[]> ExportHotelGroupBookingOrderPdf(ExportGroupBookingOrderPdfInput input);

    /// <summary>
    /// 酒店订单搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<Contracts.Common.Order.DTOs.HotelOrder.SearchOutput, IList<HotelStatusCountOutput>>> HotelOrderSearch(Contracts.Common.Order.DTOs.HotelOrder.SearchInput input);
    
    #endregion

    #region WorkOrder

    /// <summary>
    /// 分页查询工单
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    Task<PagingModel<SearchWorkOrderDto>> SearchWorkOrder(Contracts.Common.Order.DTOs.WorkOrder.SearchInput input);

    /// <summary>
    /// 工单详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<Contracts.Common.Order.DTOs.WorkOrder.DetailOutput> WorkOrderDetail(long id);

    /// <summary>
    /// 更新Hop工单通知
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<HopWorkOrderNotifyOutput> UpdateHopWorkOrderNotify(UpdateHopWorkOrderNotifyInput input);

    /// <summary>
    /// 删除Hop工单回复通知
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<HopWorkOrderNotifyOutput> DeleteHopWorkOrderReplayNotify(DeleteHopWorkOrderReplayNotifyInput input);

    /// <summary>
    /// Hop工单回复通知
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<HopWorkOrderNotifyOutput> HopWorkOrderReplayNotify(HopWorkOrderReplayNotifyInput input);
    #endregion

    #region SettlementPayables

    /// <summary>
    /// 应付款管理-酒店订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SettlementOrder.PayablesHotelOrderInfo>> SettlementPayablesHotelOrder(PayablesSearchBffInput input);

    /// <summary>
    /// 应付款管理-邮寄订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SettlementOrder.PayablesMailOrderInfo>> SettlementPayablesMailOrder(PayablesSearchBffInput input);

    /// <summary>
    /// 应付款管理-券类订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SettlementOrder.PayablesTicketOrderInfo>> SettlementPayablesTicketOrder(PayablesSearchBffInput input);

    /// <summary>
    /// 应付款管理-预约单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SettlementOrder.PayablesReservationOrderInfo>> SettlementPayablesReservationOrder(PayablesSearchBffInput input);

    /// <summary>
    /// 应付款管理-退款订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SettlementOrder.PayablesRefundOrderInfo>> SettlementPayablesRefundOrder(PayablesSearchBffInput input);

    /// <summary>
    /// 应付款管理-门票订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SettlementOrder.PayablesScenicTicketOrderInfo>> SettlementPayablesScenicTicketOrder(PayablesSearchBffInput input);

    /// <summary>
    /// 应付款管理- 线路订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SettlementOrder.PayablesLineOrderInfo>> SettlementPayablesLineOrder(PayablesSearchBffInput input);

    /// <summary>
    /// 应付款管理- 用车订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SettlementOrder.PayablesCarProductOrderInfo>> SettlementPayablesCarProductOrder(PayablesSearchBffInput input);

    /// <summary>
    /// 应付款管理- 出账抵冲单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SettlementOrder.PayableOffsetOrderInfo>> SettlementPayablesOffsetOrder(PayablesSearchBffInput input);

    /// <summary>
    /// 账单预览
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<Contracts.Common.Order.DTOs.SettlementOrder.PreCreateOutput>> SettlementPayablesPreCreate(Contracts.Common.Order.DTOs.SettlementOrder.PreCreateInput input);

    /// <summary>
    /// 账单创建
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> SettlementPayablesCreate(Contracts.Common.Order.DTOs.SettlementOrder.PreCreateInput input);

    /// <summary>
    /// 应付款-关联退款单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SettlementOrder.PayablesRefundOrderInfo>> SettlementPayablesRelatedRefundOrder(SettlementOrder.RelatedPayablesSearchInput input);

    /// <summary>
    /// 应付款-关联抵冲单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SettlementOrder.PayableOffsetOrderInfo>> SettlementPayablesRelatedOffsetOrder(SettlementOrder.RelatedPayablesSearchInput input);

    /// <summary>
    /// /应付款管理导出订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<SettlementOrder.PayablesOrderExportOutput> SettlementPayablesOrderExport(SettlementOrder.PayablesOrderExportInput input);

    /// <summary>
    /// 自动对账预览
    /// </summary>
    /// <returns></returns>
    Task<AutoPreCreateSettlementPayablesOutput> AutoPreCreateSettlementPayables(AutoSettlementPayablesInput input);
    #endregion

    #region KlookOrder

    /// <summary>
    /// 客路订单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GetKlookOrderOutput> KlookOrderDetail(GetKlookOrderInput input);

    /// <summary>
    /// 客路订单发货
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<KlookOrderDeliveryOutput> KlookOrderDelivery(KlookOrderDeliveryInput input);

    /// <summary>
    /// 客路订单支付重试
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<KlookOrderRetryPayOutput> KlookOrderRetryPay(KlookOrderRetryPay input);

    /// <summary>
    /// 客路订单回调通知
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<KlookOrderCallBackNotifyOutput> KlookOrderNotify(KlookOrderCallBackNotifyInput input);

    #endregion

    #region TicketsCombinationOrder
    /// <summary>
    /// 查询门票组合订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchTicketsCombinationOrderOutput, CombinationOrderStatistics>> SearchTicketsCombinationOrder(SearchTicketsCombinationOrderInput input);

    /// <summary>
    /// 创建门票组合订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CreateTicketsCombinationOrder(CreateTicketsCombinationOrderInput input);

    /// <summary>
    /// 替换门票组合订单
    /// </summary>
    /// <param name="input"></param>
    Task ReplaceTicketsCombinationOrder(ReplaceTicketsOrderInput input);

    /// <summary>
    /// 组合订单手动发货
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<CombinationOrderDeliveryOutput> TicketsCombinationOrderDelivery(long id);

    /// <summary>
    /// 组合订单金额校验
    /// </summary>
    /// <param name="orderId"></param>
    /// <returns></returns>
    Task<bool> TicketsCombinationOrderPaymentAmountCheck(long orderId);
    
    /// <summary>
    /// 组合异常单添加
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AddTicketsCombinationAbnormalOrder(AddAbnormalCombinationOrderInput input);
    
    /// <summary>
    /// 更新组合异常订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateTicketsCombinationAbnormalOrder(UpdateAbnormalCombinationOrderInput input);

    /// <summary>
    /// 异常单详情
    /// </summary>
    /// <param name="orderId"></param>
    /// <returns></returns>
    Task<GetAbnormalCombinationOrderDetailOutPut> GetTicketsCombinationAbnormalOrderDetail(long orderId);

    /// <summary>
    /// 关闭组合异常订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CloseTicketsCombinationAbnormalOrder(CloseAbnormalCombinationOrderInput input);

    /// <summary>
    /// 替换组合异常单组合产品
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task ReplaceTicketCombination(ReplaceTicketCombinationInput input);

    
    /// <summary>
    /// 手工单 - 创建组合订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CreateCombinationOrderOutput> CreateCombinationOrder(CreateCombinationOrderInput input);

    #endregion

    #region TicketsCombinationOrderAfterSale

    /// <summary>
    /// 组合订单 - 售后
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<AddCombinationOrderAfterSaleOutput>> CombinationOrderRefund(AddCombinationOrderAfterSaleInput input);

    /// <summary>
    /// 组合订单 - 可退信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetCombinationOrderRefundableOutput>> GetCombinationOrderRefundable(GetCombinationOrderRefundableInput input);
    #endregion

    #region RefundOrder

    Task<IEnumerable<GetRefundOrdersOutput>> GetRefundOrders(IEnumerable<long> baseOrderIds);

    #endregion

    #region Kingdee

    /// <summary>
    /// 添加金蝶客户
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    Task KingdeeCustomerAdd(Contracts.Common.Order.Messages.KingdeeCustomerAddMessage receive);

    /// <summary>
    /// 添加金蝶供应商
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    Task KingdeeSupplierAdd(Contracts.Common.Order.Messages.KingdeeSupplierAddMessage receive);

    /// <summary>
    /// 金蝶 - 单据推送
    /// </summary>
    /// <param name="input"></param>
    /// <returns>推送id</returns>
    Task<long> KingdeeBillPush(KingdeeBillPushInput input);

    Task KingdeeBillPushExecute(Contracts.Common.Order.DTOs.Kingdee.KingdeeBillPushExecuteInput input);

    /// <summary>
    /// 金蝶推送结果通知
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    Task KingdeeBillPushResultNotify(long[] ids);

    #endregion

    #region ScenicTicketSupplierOrder

    Task<SupplierOrderDeliveryOutput> ScenicTicketSupplierOrderDelivery(SupplierOrderDeliveryInput input);
    Task<GetSupplierOrderDetailOutput> GetScenicTicketOpenSupplierOrderDetail(GetSupplierOrderDetailInput input);
    #endregion

    #region GroupBooking

    /// <summary>
    /// 团房单新申请 分销商/商户 申请
    /// </summary>
    /// <returns></returns>
    Task<List<long>> GroupBookingApplicationFormApply(ApplicationFormApplyInput input);

    /// <summary>
    /// 团房申请单列表搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<ApplicationFormSearchOutput, ApplicationFormSearchSupplement>> GroupBookingApplicationFormSearch(ApplicationFormSearchInput input);

    /// <summary>
    /// 团房申请单导出数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<ApplicationFormSearchOutput>> GroupBookingApplicationFormExportData(ApplicationFormSearchInput input);

    /// <summary>
    /// 申请单信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ApplicationFormOutput> GetApplicationForm(GetApplicationFormInput input);

    /// <summary>
    /// 申请单信息编辑 
    /// </summary>
    /// <returns></returns>
    Task GroupBookingApplicationFormModify(ApplicationFormModifyInput input);

    /// <summary>
    /// 申请单状态变更
    /// </summary>
    /// <returns></returns>
    Task GroupBookingApplicationFormStatusModify(ApplicationFormStatusModifyInput input);

    /// <summary>
    /// 操作日志记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<OperationLogOutput>> GetGroupBookingOperationLogs(GetOperationLogInput input);

    /// <summary>
    /// 商户确认申请 批量生成询价单
    /// </summary>
    /// <returns></returns>
    Task GroupBookingInquiry(InquiryInput input);

    /// <summary>
    /// SaaS询价单批量生成报价单 //商户已询价 客户待询价
    /// </summary>
    /// <returns></returns>
    Task GroupBookingQuotation(QuotationInput input);

    /// <summary>
    /// 商户审核 确认报价 //商户已报价 客户待确认报价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task GroupBookingQuotated(QuotatedInput input);

    /// <summary>
    /// 查询关联询价报价单
    /// </summary>
    /// <param name="applicationFormId"></param>
    /// <returns></returns>
    Task<IEnumerable<QuotationOutput>> GetGroupBookingQuotations(long applicationFormId);

    /// <summary>
    /// 客户确认保存报价单 //确认报价
    /// </summary>
    /// <returns></returns>
    Task GroupBookingQuotationConfirm(QuotationConfirmInput input);

    /// <summary>
    /// SaaS生成待审预订单
    /// </summary>
    /// <returns></returns>
    Task GroupBookingPreOrder(PreOrderInput input);

    /// <summary>
    /// 查询申请单关联预订单
    /// </summary>
    /// <param name="applicationFormId">申请单id</param>
    /// <param name="preOrderId">预订单id</param>
    /// <returns></returns>
    Task<List<PreOrderOutput>> GetGroupBookingPreOrders(long applicationFormId, long? preOrderId = default);

    /// <summary>
    /// 商户确认预订单 //预订单
    /// </summary>
    /// <returns></returns>
    Task GroupBookingPreOrderConfirm(PreOrderConfirmInput input);

    /// <summary>
    /// 当前商户预订单设置
    /// </summary>
    /// <returns></returns>
    Task<GroupBookingPreOrderSettingDto> GetGroupBookingPreOrderSetting();

    /// <summary>
    /// 保存预订单配置
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    Task GroupBookingPreOrderSettingSave(GroupBookingPreOrderSettingDto dto);

    /// <summary>
    /// 获取所有城市信息
    /// </summary>
    /// <returns></returns>
    Task<CityOutput> GroupBookingGetCities();

    /// <summary>
    /// 查询存在申请单的分销商
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchAgencyOutput>> SearchGroupBookingAgencies(SearchAgenciesInput input);

    /// <summary>
    /// 团房单搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<GroupBookingOrder.SearchOutput, IEnumerable<GroupBookingOrder.SearchSupplement>>> GroupBookingOrderSearch(GroupBookingOrder.SearchInput input);

    /// <summary>
    /// 团房单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GroupBookingOrder.DetailOutput> GroupBookingOrderDetail(GroupBookingOrder.DetailInput input);

    /// <summary>
    /// 添加入住人
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AddGroupBookingOrderGuest(GroupBookingOrder.AddOrderGuestInput input);

    /// <summary>
    /// 团房关联子单首尾款支付信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GroupBookingOrder.GroupBookingOrderPaymentOutput>> BaseOrderItemPayments(GroupBookingOrder.BaseOrderItemPaymentInput input);

    /// <summary>
    /// 团房单尾款调整
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AdjustFinalPayment(GroupBookingOrder.AdjustFinalPaymentInput input);


    /// <summary>
    /// 团房下单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GroupBookingOrder.OrderCreateOutput> GroupBookingOrderCreate(GroupBookingOrder.OrderCreateInput input);

    /// <summary>
    /// 设置保存跟单指派
    /// </summary>
    /// <param name="inputs"></param>
    /// <returns></returns>
    Task SaveGroupBookingOperatorUsers(List<Contracts.Common.Order.DTOs.GroupBookingOperatorUser.GroupBookingOperatorUserDto> inputs);

    /// <summary>
    /// 获取跟单指派列表
    /// </summary>
    /// <param name="enabbled"></param>
    /// <returns></returns>
    Task<List<Contracts.Common.Order.DTOs.GroupBookingOperatorUser.GroupBookingOperatorUserDto>> GetAllGroupBookingOperatorUsers();

    /// <summary>
    /// 统计分销商团房单数量
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetAgencyHistoricalStatisticsOutput>> GetAgencyHistoricalStatistics(GetAgencyHistoricalStatisticsInput input);
    #endregion

    #region GroupBookingFinancialHandleOrder

    Task<long> GroupBookingFinancialHandleOrderCreate(GroupBookingFinancialHandleOrder.FinancialHandleOrderCreateInput input);

    Task<PagingModel<GroupBookingFinancialHandleOrder.FinancialHandleOrderSearchOutput>> GroupBookingFinancialHandleOrderSearch(GroupBookingFinancialHandleOrder.FinancialHandleOrderSearchInput input);

    Task<GroupBookingFinancialHandleOrder.FinancialHandleOrderDetailOutput> GroupBookingFinancialHandleOrderDetail(GroupBookingFinancialHandleOrder.FinancialHandleOrderDetailInput input);

    Task GroupBookingFinancialHandleOrderHandle(GroupBookingFinancialHandleOrder.FinancialHandleOrderHandleInput input);

    #endregion

    #region AfterSaleFinancialHandleOrder

    /// <summary>
    /// 售后财务处理单搜索
    /// </summary>
    /// <param name=""></param>
    /// <returns></returns>
    Task<PagingModel<AfterSaleFinancialHandleOrder.SearchOutput>> AfterSaleFinancialHandleOrderSearch(AfterSaleFinancialHandleOrder.SearchInput input);

    /// <summary>
    /// 售后财务处理单详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<AfterSaleFinancialHandleOrder.DetailOutput> GetAfterSaleFinancialHandleOrder(long id);

    /// <summary>
    /// 售后财务处理单处理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AfterSaleFinancialHandleOrderHandle(AfterSaleFinancialHandleOrder.HandleInput input);

    #endregion

    #region InvoiceRecord
    /// <summary>
    /// 查询开票记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<InvoiceRecordDto, IList<InvoiceRecordTotalDto>>> SearchInvoice(SearchInvoiceRecordInput input);

    /// <summary>
    /// 申请开票 转审核中
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task ApplyInvoice(Contracts.Common.Order.DTOs.Invoice.ApplyInput input);

    /// <summary>
    /// 检查发票状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<CheckStatusOutPut>> InvoiceCheckStatusByOrderIds(CheckStatusInput input);

    /// <summary>
    /// 支持额度收款开票配置的结算单
    /// </summary>
    /// <param name="settlementOrderIds"></param>
    /// <returns></returns>
    Task<List<long>> InvoiceGetIsSupportIdsBySettlementOrder(long[] settlementOrderIds);

    /// <summary>
    /// 批量开票
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task Invoicing(ApplyInvoicingInput input);

    #endregion

    #region Insure

    /// <summary>
    /// 立即投保
    /// </summary>
    /// <returns></returns>
    Task CreateInsurePolicy(CreateInsureInput input);

    #endregion

    #region InsurePolicyHolder

    /// <summary>
    /// 获取投保人详情
    /// </summary>
    /// <returns></returns>
    Task<InsurePolicyHolderOutput> GetInsurePolicyHolder();

    /// <summary>
    /// 保存投保人信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SaveInsurePolicyHolder(SaveInsurePolicyHolderInput input);

    #endregion

    #region InsureProduct

    /// <summary>
    /// 获取保险产品列表
    /// </summary>
    /// <returns></returns>
    Task<List<InsureProductOutput>> GetInsureProductList();

    /// <summary>
    /// 获取保险产品
    /// </summary>
    /// <returns></returns>
    Task<InsureProductOutput> GetInsureProduct(long id);

    /// <summary>
    /// 获取保险产品-根据baseOrderId
    /// </summary>
    /// <returns></returns>
    Task<InsureProductOutput> GetInsureProductByBaseOrderId(long baseOrderId);

    /// <summary>
    /// 保存保险产品
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<InsureProductOutput> SaveInsureProduct(SaveInsureProductInput input);

    /// <summary>
    /// 删除保险产品
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task DeleteInsureProduct(long id);

    /// <summary>
    /// 保险产品上移下移功能
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task MoveInsureProduct(List<MoveInsureProductInput> input);

    /// <summary>
    /// 获取保险产品下拉框
    /// </summary>
    /// <returns></returns>
    Task<List<InsureProductOutput>> GetInsureProductSelection();

    /// <summary>
    /// 根据产品id获取关联的保险产品
    /// </summary>
    /// <returns></returns>
    Task<InsureProductRelationsOutput> GetInsureProductRelation(long productId);

    /// <summary>
    /// 保存产品和保险产品配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SaveInsureProductRelation(SaveInsureProductRelationsInput input);

    /// <summary>
    /// 保存产品和保险产品配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<SupplierInsureProductOutput>> SearchSupplierInsureProducts(SearchSupplierInsureProductInput input);
    #endregion

    #region InsureProductSkuRelation
    /// <summary>
    /// 获取保险和产品sku配置
    /// </summary>
    /// <returns></returns>
    Task<GetInsureProductSkuRelationOutput> GetInsureProductSkuRelation(GetInsureProductSkuRelationInput input);

    /// <summary>
    /// 保存保险和产品sku配置
    /// </summary>
    /// <returns></returns>
    Task SaveInsureProductSkuRelation(SaveInsureProductSkuRelationInput input);

    /// <summary>
    /// 删除保险和产品sku配置
    /// </summary>
    /// <returns></returns>
    Task DeleteInsureProductSkuRelation(DeleteInsureProductSkuRelationInput input);
    #endregion

    #region InsurePurchaseRecord

    /// <summary>
    /// 查询保险采购单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<InsurePurchaseRecordOutput>> GetInsurePurchaseRecordList(SearchInsurePurchaseRecordInput input);

    /// <summary>
    /// 查询保险采购单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<InsurePurchaseRecordDetailOutput>> SearchInsurePurchaseRecordDetail(SearchInsurePurchaseRecordDetailInput input);

    /// <summary>
    /// 根据订单id查询保险采购单数据
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    Task<InsurePurchaseRecordByOrderOutput> GetInsureRecordByOrder(long baseOrderId);

    /// <summary>
    /// 导出保险采购单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<ExportInsurePurchaseRecordOutput>> ExportInsurePurchaseRecords(SearchInsurePurchaseRecordInput input);

    /// <summary>
    /// 根据订单ids查询投保总额
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetInsureTotalAmountOutput>> GetInsureTotalAmount(GetInsureTotalAmountInput input);
    #endregion

    #region InsureOrderRelation

    /// <summary>
    /// 设置订单的保险是否自动购买
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SetInsureOrderRelationAuto(UpdateInsureOrderRelationInput input);
    #endregion

    #region ThirdInsureProduct
    /// <summary>
    /// 查询第三方保险产品列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<SupplierInsureProductOutput>> SearchThirdInsureProduct(SearchThirdInsureProductInput input);
    #endregion

    #region OpenChannelSyncFailOrder

    /// <summary>
    /// 查询-同步失败订单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchOpenChannelSyncFailOrderOutput>> SearchOpenChannelSyncFailOrder(SearchOpenChannelSyncFailOrderInput input);

    /// <summary>
    /// 查询-同步失败订单状态统计
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<GetSyncFailOrderCountOutput>> SearchOpenChannelSyncFailOrderStatusCount(SearchOpenChannelSyncFailOrderInput input);

    /// <summary>
    /// 添加-同步失败订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AddOpenChannelSyncFailOrder(OpenChannelSyncFailOrderAddInput input);

    /// <summary>
    /// 查询同步失败订单数据
    /// </summary>
    /// <param name="channelOrderNo"></param>
    /// <returns></returns>
    Task<GetOpenChannelSyncFailOrderOutput> GetOpenChannelSyncFailOrderDetail(GetOpenChannelSyncFailOrderInput input);

    /// <summary>
    /// 作废同步失败订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task InvalidOpenChannelSyncFailOrder(InvalidSyncFailOrderInput input);

    /// <summary>
    /// 恢复同步失败订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task RestoredOpenChannelSyncFailOrder(RestoredSyncFailOrderInput input);


    /// <summary>
    /// 修改同步失败订单类型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateOpenChannelSyncFailOrderType(UpdateChannelSyncFailOrderTypeInput input);

    #endregion

    #region SettlementOrder
    Task<PagingModel<SettlementOrder.SettlementOrderOutput>> SearchSettlementOrder(SettlementOrder.SearchInput input);

    Task<IEnumerable<SettlementOrder.SettlementOrderOutput>> ExportSettlementOrder(SettlementOrder.ExportSettlementOrderInput input);
    #endregion

    #region Finance

    /// <summary>
    /// 分销商授信关联单据信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<AgencyCreditRecordOrderInfoOutput> GetAgencyCreditRecordOrderInfo(AgencyCreditRecordOrderInfoInput input);

    #endregion

    #region CarHailingOrder
    /// <summary>
    /// 获取接送车订单详情
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    Task<Contracts.Common.Order.DTOs.CarHailingOrder.DetailOutput> GetCarHailingOrder(long baseOrderId);

    Task EditCarHailingOrderCost(UpdateCostInput input);
    #endregion

    #region WorkOrder
    /// <summary>
    /// 工单申请
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task WorkOrderApply(Contracts.Common.Order.DTOs.WorkOrder.ApplyInput input);
    #endregion

    #region InvoiceConfig
    /// <summary>
    /// 获取发票接口配置
    /// </summary>
    /// <returns></returns>
    Task<List<GetInvoiceConfigOutput>> GetInvoiceConfig();

    /// <summary>
    /// 删除发票接口配置
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task DeleteInvoiceConfig(long id);

    /// <summary>
    /// 设置发票接口配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<long> SetInvoiceConfig(SetInvoiceConfigInput input);

    /// <summary>
    /// 设置发票接口配置开启状态
    /// </summary>
    /// <param name="enable"></param>
    /// <returns></returns>
    Task SetInvoiceConfigEnabled(bool enable);
    #endregion

    #region InvoiceTitle
    Task<List<GetInvoiceTitleOutput>> GetInvoiceTitleList(long AgencyId);

    Task<long> AddInvoiceTitle(AddInvoiceTitleInput input);

    Task UpdateInvoiceTitle(UpdateInvoiceTitleInput input);
    #endregion

    #region AggregateOrder
    Task<PagingModel<SearchAggregateOrderOutput, SearchAggregateStatusStat>> AggregateOrderSearch(SearchAggregateOrderInput input);

    Task<List<ExportAggregateOrderOutput>> AggregateOrderExport(ExportAggregateOrderInput input);

    Task SetAggregateOrderRemark(SetAggregateOrderRemarkInput input);

    Task<List<SearchAggregateStatusOuput>> SearchAggregateOrderStatus(SearchAggregateStatusInput input);

    /// <summary>
    /// 玩乐财务报表查询 数据汇总
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchAggregateOrderFinanceOutput, AggregateOrderFinanceSummary>> AggregateOrderFinanceSearch(SearchAggregateOrderInput input);

    /// <summary>
    /// 移除订单处理等级标签
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task RemoveAggregateOrderProcessingLevelTag(RemoveProcessingLevelTagInput input);
    
    #endregion

    #region OrderLog
    /// <summary>
    /// 新增订单操作日志
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CreateOrderLog(Contracts.Common.Order.DTOs.OrderLog.CreateInput input);
    #endregion

    #region OrderPrice

    /// <summary>
    /// 根据订单id获取订单价格信息
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    Task<List<OrderMultPriceOutput>> GetByBaseOrderId(long baseOrderId);

    #endregion

    #region CarProductOrder 用车订单

    /// <summary>
    /// 用车 - 下单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CarProductOrder.CreateCarProductOrderOutput> CarProductOrderCreate(CarProductOrder.CreateCarProductOrderInput input);

    /// <summary>
    /// 用车 - 订单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CarProductOrder.GetDetailOutput> CarProductOrderDetail(CarProductOrder.GetDetailInput input);

    /// <summary>
    /// 用车 - 订单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<CarProductOrder.SearchOrderOutput>> CarProductOrderSearch(CarProductOrder.SearchOrderInput input);

    /// <summary>
    /// 用车 - 确认
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CarProductOrderConfirm(CarProductOrder.ConfirmOrderInput input);

    /// <summary>
    /// 用车 - 完成
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CarProductOrderFinish(CarProductOrder.FinishOrderInput input);

    /// <summary>
    /// 用车 - 退款
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CarProductOrderRefund(CarProductOrder.RefundOrderInput input);

    /// <summary>
    /// 用车 - 编辑采购价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CarProductOrderEditCost(UpdateCostInput input);

    /// <summary>
    /// 编辑用车信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task EditCarOrderUsingInfo(CarProductOrder.EditCarUsingInfoInput input);

    Task<bool> UpdateCarProductOrderTravelInfo(Contracts.Common.Order.DTOs.CarProductOrder.UpdateTravelInfoInput input);

    Task<bool> UpdateCarProductOrderConfirmation(Contracts.Common.Order.DTOs.CarProductOrder.UpdateOrderConfirmationInput input);

    /// <summary>
    /// 修改订单联系人
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    Task<bool> UpdateCarProductOrderContact(UpdateContactInput input);

    /// <summary>
    /// 通过渠道订单号获取订单信息
    /// </summary>
    /// <param name="channelOrderNo"></param>
    /// <returns></returns>
    Task<CarProductOrder.GetCarProductChannelOrderInfoOutput> GetCarProductChannelOrderInfo(string channelOrderNo);

    Task UpdateCarProductOrderSupplierOrderId(UpdateSupplierOrderIdInput input);

    #endregion

    #region OrderPrintSetting
    /// <summary>
    /// 获取订单设置
    /// </summary>
    /// <returns></returns>
    Task<OrderPrintSettingBffOutput> GetOrderPrintSetting();

    /// <summary>
    /// 编辑订单设置
    /// </summary>
    /// <returns></returns>
    Task<long> UpdateOrderPrintSetting(UpdateOrderPrintSettingInput input);
    #endregion

    #region OrderOperationTask
    Task<long> AddOrderOperationTask(AddOrderOperationTaskInput input);

    Task<PagingModel<SearchOrderOperationTaskOutput>> SearchOrderOperationTask(SearchOrderOperationTaskInput input);

    Task OrderOperationTaskExportCallBack(int result, CallbackData callback);
    #endregion

    #region CarProductSupplierOrder

    /// <summary>
    /// 开放平台-用车机场/地址模糊搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<CarSupplierOrderFuzzySearchOutput>> CarSupplierOrderFuzzySearch(CarSupplierOrderFuzzySearchInput input);

    /// <summary>
    /// 开放平台-根据通知ID查询用车订单信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<QueryCarOrderByNotifyIdOutput> QueryCarProductOrderByNotifyId(QueryCarOrderByNotifyIdInput input);

    /// <summary>
    /// 开放平台- 用车订单询价通知处理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CarProductSupplierOrderQuoteNotifyProcess(CarProductSupplierOrderQuoteNotifyInput input);

    /// <summary>
    /// 开放平台- 用车订单预订通知处理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CarSupplierOrderReservationNotifyProcess(CarProductSupplierOrderReservationNotifyInput input);

    /// <summary>
    /// 开放平台- 用车订单重新询价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CarSupplierOrderRetryQuote(CarProductSupplierOrderQuoteInput input);

    /// <summary>
    /// 开放平台- 用车订单预订取消
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CancelCarProductSupplierOrderOutput> CarSupplierOrderCancel(CancelCarProductSupplierOrderInput input);

    #endregion

    #region WorkOrderServiceEvaluation
    Task<List<GetOutput>> GetWorkOrderServiceEvaluationList(GetListInput input);
    #endregion

    #region OpenChannelOrder

    /// <summary>
    /// 开放平台-渠道订单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<QueryChannelOrderDetailOutput> QueryChannelOrderDetail(QueryChannelOrderDetailInput input);

    /// <summary>
    /// 开放平台 - 渠道订单确认
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task ConfirmChannelOrder(ConfirmChannelOrderInput input);

    #endregion

    #region OpenSupplierOrder

    /// <summary>
    /// 获取关联saas订单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GetOpenSupplierSaasOrderDetailOutput> GetOpenSupplierSaasOrderDetail(GetOpenSupplierSaasOrderDetailInput input);

    /// <summary>
    /// 开放平台 - 创建供应商订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CreateSupplierOrderOutput> CreateOpenSupplierOrder(CreateSupplierOrderInput input);
    
    /// <summary>
    /// 开放平台 - 获取供应商订单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GetOpenSupplierOrderDetailOutput> GetOpenSupplierOrderDetail(GetOpenSupplierOrderDetailInput input);

    /// <summary>
    /// 同步供应商订单数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ResyncSupplierOrderDataOutput> ResyncOpenSupplierOrderData(ResyncSupplierOrderDataInput input);

    /// <summary>
    /// 供应商订单支付重试
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<SupplierOrderRetryPayOutput> RetryPayOpenSupplierOrder(SupplierOrderRetryPayInput input);
    
    /// <summary>
    /// 供应端订单状态通知
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task OpenSupplierOrderStatusNotify(SupplierOrderStatusNotifyInput input);

    /// <summary>
    /// 保存订单附加信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AddOpenSupplierOrderExtraInfo(AddOpenSupplierOrderExtraInfosInput input);

    /// <summary>
    /// 查询订单的附加信息
    /// </summary>
    /// <param name="baseOrderIds"></param>
    /// <returns></returns>
    Task<List<QueryOpenSupplierOrderExtraInfosOutput>> QueryOpenSupplierOrderExtraInfo(params long[] baseOrderIds);

    #endregion

    #region LineSupplierOrder

    /// <summary>
    /// 线路业务订单发货
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<SupplierOrderDeliveryOutput> TravelLineSupplierOrderDelivery(SupplierOrderDeliveryInput input);

    #endregion


    #region OrderDelayedPay

    /// <summary>
    /// 延时订单 未实付取消
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task OrderDelayedCancel(OrderDelayedCancelInput input);

    #endregion

    #region CompensationOrder

    /// <summary>
    /// 获取补差关联订单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchCompensationRelatedOrderOutput,SearchCompensationRelatedOrderExtendData>> SearchCompensationRelatedOrder(SearchCompensationRelatedOrderInput input);
    
    /// <summary>
    /// 绑定补差单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task BindCompensationOrder(BindCompensationOrderInput input);

    /// <summary>
    /// 绑定补差单前置校验
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task PreBindCompensationOrder(BindCompensationOrderPreCheckInput input);
    
    /// <summary>
    /// 解绑补差单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UnBindCompensationOrder(UnBindCompensationOrderInput input);

    /// <summary>
    /// 获取补差关联订单详情
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    Task<List<GetCompensationRelatedOrderDetailOutput>> GetCompensationRelatedOrder(long baseOrderId);


    /// <summary>
    /// 补差单信息补充
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SupplementCompensationOrder(SupplementCompensationOrderDataInput input);
    #endregion
    #region OffsetOrderDingtalkApply

    Task<PagingModel<OffsetOrderDingtalkApplySearchOutput>> SearchOffsetOrderDingtalkApply(OffsetOrderDingtalkApplySearchInput input);

    Task CallbackOffsetOrderDingtalkApplyResult(CallbackApplyResultInput input);

    #endregion

    #region OpenChannelRefundApplyOrder

    /// <summary>
    /// 创建渠道退单申请
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CreateChannelRefundApplyOrder (CreateChannelRefundApplyOrderInput input);

    #endregion

    #region GroupBookingAreaSetting
    Task SaveGroupBookingAreaSetting(SaveGroupBookingAreaSettingInput input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task<GroupBookingAreaSettingOutput> GetGroupBookingAreaSetting();
    #endregion

    #region GroupBookingAggregate
    Task<GetBasicDataOutput> GetGroupBookingAggregateBasicData(GetBasicDataInput input);

    Task<GetChatDataOutput> GetGroupBookingAggregateChatData(GetChatDataInput input);

    Task<GetProportionOutput> GetGroupBookingAggregateProportion(GetProportionInput input);

    Task<QueryOperationOutput> QueryGroupBookingOperationData(QueryOperationDataInput input);

    Task<List<GetStatusStatisticsOutput>> GetGroupBookingAggregateStatusStatistics(GetStatusStatisticsInput input);

    Task<List<ExportStatusStatisticsOutput>> ExportGroupBookingAggregateStatusStatistics(GetStatusStatisticsInput input);

    Task<GetSyncTimeOutput> GetGroupBookingAggregateSyncTime();
    #endregion

    #region OrderRemark

    /// <summary>
    /// 查询订单备注
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<BaseOrderRemarkOutput>> GetAllOrderRemark(QueryBaseOrderRemarkInput input);

    #endregion

    #region YouxiaTripOrder
    Task YouxiaTripOrderStatusChange(OrderStatusChangeInput input);

    Task YouxiaTripOrderPriceChange(OrderPriceChangeInput input);
    #endregion
}
