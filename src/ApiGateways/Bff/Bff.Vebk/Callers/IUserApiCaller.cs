using Bff.Vebk.Models.TenantUser;
using Common.Caller;
using Contracts.Common.User.DTOs;
using Contracts.Common.User.DTOs.AgencyUser;
using Contracts.Common.User.DTOs.CustomerUser;
using Contracts.Common.User.DTOs.DarenBonus;
using Contracts.Common.User.DTOs.DarenUser;
using Contracts.Common.User.DTOs.OperationLog;
using Contracts.Common.User.DTOs.PlatformBind;
using Contracts.Common.User.DTOs.SupplierUser;
using Contracts.Common.User.DTOs.TenantUser;
using Contracts.Common.User.DTOs.TenantUserSecurityVerification;
using Contracts.Common.User.DTOs.UserBinding;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using SearchInput = Contracts.Common.User.DTOs.SupplierUser.SearchInput;

namespace Bff.Vebk.Callers;

public interface IUserApiCaller : IHttpCallerBase
{
    #region Captcha
    Task SendCaptcha(CaptchaDTO input);

    Task<bool> CheckCaptcha(CaptchaDTO input);
    #endregion

    #region Huizhi

    public Task<IEnumerable<HuiZhiBindData>> GetHuiZhiBindData(GetBindDataInput input);

    public Task<IEnumerable<CreateBindUrlsOutput>> CreateBindUrls(IEnumerable<CreateBindUrlsInput> input);

    public Task Unbound(PlatformUnboundInput input);

    #endregion

    #region UserBinding

    Task<List<GetUserBindingOutput>> GetUserBindings(GetUserBindingInput input);

    Task UnboundUserBindings(UnboundUserBindingInput input);

    #endregion

    #region AgencyUser

    Task<List<string>> SearchPhone(List<string> phones);

    public Task<PagingModel<Contracts.Common.User.DTOs.AgencyUser.AgencyUserDto>> AgencyUserSearch(Contracts.Common.User.DTOs.AgencyUser.SearchInput input);

    Task<List<Contracts.Common.User.DTOs.AgencyUser.AgencyUserDto>> AgencyUserList(Contracts.Common.User.DTOs.AgencyUser.SearchInput input);

    public Task<Contracts.Common.User.DTOs.AgencyUser.AgencyUserDto> AgencyUserFindOne(Contracts.Common.User.DTOs.AgencyUser.AgencyUserFindOneInput input);

    /// <exception cref="ErrorTypes.User.AccountIsExist"></exception>
    public Task<long> AgencyUserAdd(Contracts.Common.User.DTOs.AgencyUser.AddInput input);

    public Task AgencyUserUpdate(Contracts.Common.User.DTOs.AgencyUser.UpdateInput input);

    /// <summary>
    /// 批量获取分销商用户信息
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    public Task<List<AgencyUserDto>> GetAgencyUserDetails(params long[] ids);

    /// <summary>
    /// 设置分销商账号状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task OnOrOff(OnOrOffInput input);

    /// <summary>
    /// 重置分销商账号的密码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<string> RestPassword(RestPasswordInput input);

    /// <summary>
    /// 修改分销商账号的密码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdatePassword(UpdatePasswordInput input);

    /// <summary>
    /// 对分销商做唯一性校验
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> AgencyUsersAnyAsync(AgencyUserVerifyInput input);

    /// <summary>
    /// 设置分销商 的邮箱验证状态
    /// </summary>
    /// <returns></returns>
    Task SetEmailStatusAsync(AgencyUserEmailStatusInput input);

    #endregion

    #region Supplier

    public Task<PagingModel<SupplierUserDto>> SupplierUserSearch(SearchInput input);

    #endregion

    #region TenantUser

    public Task<PagingModel<UserSearchOuput>> TenantUserSearch(UserSearchInput input);

    Task<UserSearchOuput> TenantUserFindOne(FindOneInput input);

    /// <summary>
    /// 启用/停用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> SetEnabled(SetUserEnabledInput input);

    /// <summary>
    /// TenantUser 重置密码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<string> TenantUserRestPassword(RestPasswordInput input);

    /// <summary>
    /// 更新租户昵称
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> UpdateTenantUserName(UpdateTenantUserNameInput input);

    /// <summary>
    /// 验证联系方式
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> UpdateTenantUserVaildStatus(UpdateTenantUserVaildInput input);

    Task<List<UserSearchOuput>> SearchTenantUsers(SearchTenantUsersInput input);

    Task UpdateTenantUserJobId(UpdateJobIdInput input);

  

    #endregion

    #region Daren

    /// <summary>
    /// 获取订单奖金统计（对账单）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<GetOrderBonusStatOutput>> GetOrderBonusStat(GetOrderBonusStatInput input);

    /// <summary>
    /// 获取订单达人奖励
    /// </summary>
    Task<List<GetDarenBonusByOrderOutput>> GetDarenBonusByOrder(GetDarenBonusByOrderInput input);

    /// <summary>
    /// 获取达人信息
    /// </summary>
    Task<List<GetDarenOutput>> GetDaren(params long[] userIds);
    
    #endregion

    #region CustomerUser

    /// <summary>
    /// 批量获取用户信息
    /// </summary>
    Task<List<CustomerUserDTO>> GetCustomers(GetCustomersInput input);
    
    /// <summary>
    /// 通过筛选条件查询分页
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchCustomerUserPageOutput>> SearchCustomerUsers(SearchCustomerUserPageInput input);
    
    /// <summary>
    /// 通过ID列表获取C端用户信息
    /// </summary>
    Task<List<GetCustomerUserByIdsOutput>> GetCustomerUserByIds(List<long> userIds);

    #endregion

    #region DarenWithdrawals

    Task<IEnumerable<Contracts.Common.User.DTOs.DarenWithdrawal.DarenWithdrawalOutput>> SearchDarenWithdrawalByIds(IEnumerable<long> ids);

    #endregion


    #region SupplierUser
    Task<SupplierUserDto> SupplierUserFindOne(FindOneInput input);
    #endregion

    #region TenantUserSecurityVerification
    /// <summary>
    /// 判断是否触发双因素认证
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> CheckTenantUserSecurityVerification(CheckSecurityVerificationInput input);

    /// <summary>
    /// 保存指纹数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> SaveTenantUserSecurityVerification(SaveTenantUserSecurityVerificationInput input);
    #endregion

    #region OperationLog
    Task AddOperationLog(OperationLogDto input);

    Task<PagingModel<OperationLogOutput>> OperationLogSearch(SearchOperationLogInput input);
    #endregion

    #region DingtalkUser
    Task SetUserEnabledByDingtalkUserLeave(SetEnabledByDingtalkUserLeaveInput input); 
    #endregion
}
