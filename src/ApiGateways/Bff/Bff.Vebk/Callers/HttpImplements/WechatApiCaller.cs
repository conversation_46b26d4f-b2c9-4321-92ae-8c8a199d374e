using Common.Caller;
using Common.ServicesHttpClient;
using Contracts.Common.WeChat.DTOs;
using Contracts.Common.WeChat.DTOs.Huizhi;
using Contracts.Common.WeChat.DTOs.XiaoHongShuConfiguration;
using Contracts.Common.WeChat.Enums;
using Microsoft.Extensions.Options;

namespace Bff.Vebk.Callers.HttpImplements;

public class WechatApiCaller : HttpCallerBase, IWechatApiCaller
{
    public WechatApiCaller(IOptions<ServicesAddress> servicesAddress, IHttpClientFactory httpClientFactory)
        : base(servicesAddress.Value.WeChat, httpClientFactory)
    {
    }

    public async Task<GetOauth2AccessTokenOutput> GetHuizhiOpenId(GetOauth2AccessTokenInput input)
    {
        var relativePath = "/Huizhi/GetOauth2AccessToken";
        return await PostAsync<GetOauth2AccessTokenInput, GetOauth2AccessTokenOutput>(relativePath, input);
    }

    public async Task<WechatConfigurationOutput> GetWechatConfiguration(AuthType authType)
    {
        string relativePath = "/WechatConfiguration/Get?authType=" + authType;
        return await GetAsync<WechatConfigurationOutput>(relativePath);
    }

    public async Task<GetOutput> GetXiaoHongShuConfiguration()
    {
        string relativePath = "/XiaoHongShuConfiguration/Get";
        return await GetAsync<GetOutput>(relativePath);
    }

    public async Task SetXiaoHongShuConfiguration(XiaoHongShuConfigurationDto dto)
    {
        var relativePath = "/XiaoHongShuConfiguration/Set";
        await PostAsync(relativePath, dto);
    }

    #region Huizhi

    public Task<string> GetHuiZhiWxacodeUnlimit(GetWxacodeUnlimitInput input)
    {
        var relativePath = "/Huizhi/GetWxacodeUnlimit";
        return PostAsync<GetWxacodeUnlimitInput, string>(relativePath, input);
    }

    #endregion

    #region WechatMP

    public Task<string> GetWechatMPQrCode(CreateMpQrCodeInput input)
    {
        var relativePath = "/WechatMp/GetQrCode";
        return PostAsync<CreateMpQrCodeInput, string>(relativePath, input);
    }

    public Task<CreateMpQrCodeOutput> CreateWechatMPQrCode(CreateMpQrCodeInput input)
    {
        var relativePath = "/WechatMp/CreateQrCode";
        return PostAsync<CreateMpQrCodeInput, CreateMpQrCodeOutput>(relativePath, input);
    }

    #endregion

}
