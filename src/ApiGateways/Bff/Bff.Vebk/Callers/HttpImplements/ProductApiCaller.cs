using Common.Caller;
using Common.ServicesHttpClient;
using Contracts.Common.Order.DTOs.TravelLineOrder;
using Contracts.Common.Order.Messages;
using Contracts.Common.Product.DTOs.AgencyChannelCommissionSettings;
using Contracts.Common.Product.DTOs.AgencyChannelPriceSettings;
using Contracts.Common.Product.DTOs.CarHailingProduct;
using Contracts.Common.Product.DTOs.CarProduct;
using Contracts.Common.Product.DTOs.CarProductSku;
using Contracts.Common.Product.DTOs.CarProductSkuCalendarPrice;
using Contracts.Common.Product.DTOs.CarServiceItem;
using Contracts.Common.Product.DTOs.CarServiceItemCalendarPrices;
using Contracts.Common.Product.DTOs.CarTypeGrade;
using Contracts.Common.Product.DTOs.Disclaimer;
using Contracts.Common.Product.DTOs.Fields;
using Contracts.Common.Product.DTOs.Group;
using Contracts.Common.Product.DTOs.InformationTemplate;
using Contracts.Common.Product.DTOs.LineProduct;
using Contracts.Common.Product.DTOs.LineProductOpenChannelSetting;
using Contracts.Common.Product.DTOs.LineProductOpenSupplierSetting;
using Contracts.Common.Product.DTOs.LineProductSku;
using Contracts.Common.Product.DTOs.LineProductSkuCalendarPrice;
using Contracts.Common.Product.DTOs.LineProductSkuTypeItem;
using Contracts.Common.Product.DTOs.LineProductTimeSlot;
using Contracts.Common.Product.DTOs.OpenChannel;
using Contracts.Common.Product.DTOs.OpenPlatformPricingSyncLog;
using Contracts.Common.Product.DTOs.OpenSupplier;
using Contracts.Common.Product.DTOs.OpenSupplierProductSyncLog;
using Contracts.Common.Product.DTOs.ProductDefaultInformationTemplate;
using Contracts.Common.Product.DTOs.ProductInformationTemplate;
using Contracts.Common.Product.DTOs.ProductSku;
using Contracts.Common.Product.DTOs.Restaurant;
using Contracts.Common.Product.DTOs.Store;
using Contracts.Common.Product.DTOs.TicketProduct;
using Contracts.Common.Scenic.DTOs.OpenSupplier;
using Contracts.Common.Scenic.DTOs.Ticket;
using Contracts.Common.Scenic.DTOs.TicketsCalendarPrice;
using EfCoreExtensions.Abstract;
using Microsoft.Extensions.Options;
using TicketInfo = Contracts.Common.Product.DTOs.TicketProduct.TicketInfo;

namespace Bff.Vebk.Callers.HttpImplements;

public class ProductApiCaller : HttpCallerBase, IProductApiCaller
{
    private readonly string _baseAddress;

    public ProductApiCaller(IOptions<ServicesAddress> servicesAddress, IHttpClientFactory httpClientFactory)
        : base(servicesAddress.Value.Product, httpClientFactory)
    {
        _baseAddress = servicesAddress.Value.Product;
    }

    #region lineproduct

    public Task<GetLineProductDto> GetLineProduct(long productId)
    {
        var relativePath = $"/LineProduct/Get?productId={productId}";
        return GetAsync<GetLineProductDto>(relativePath);
    }

    public Task<GetLineProductOutput> GetLineProductV2(long productId)
    {
        var relativePath = $"/LineProduct/Get?productId={productId}";
        return GetAsync<GetLineProductOutput>(relativePath);
    }

    public Task<GetLineProductSkuDto> GetLineProductSkuCalendarPriceBySkuId(GetLineProductSkuCalendarPriceInput input)
    {
        var relativePath = $"/LineProductSkuCalendarPrice/GetBySkuId";
        return PostAsync<GetLineProductSkuCalendarPriceInput, GetLineProductSkuDto>(relativePath, input);
    }

    public async Task<PagingModel<SearchLineProductOutput, SearchLineProductSummary>> SearchLineProduct(
        SearchLineProductInput input)
    {
        var relativePath = "/LineProduct/Search";
        return await PostAsync<SearchLineProductInput, PagingModel<SearchLineProductOutput, SearchLineProductSummary>>(
            relativePath, input);
    }

    public async Task<AddLineProductOutput> AddLineProduct(AddLineProductInput input)
    {
        var relativePath = $"/LineProduct/Add";
        return await PostAsync<AddLineProductInput, AddLineProductOutput>(relativePath, input);
    }

    public async Task<UpdateLineProductOutput> UpdateLineProduct(UpdateLineProductInput input)
    {
        var relativePath = $"/LineProduct/Update";
        return await PostAsync<UpdateLineProductInput, UpdateLineProductOutput>(relativePath, input);
    }

    public async Task<List<GetLineProductSimpleInfoOutput>> GetLineProductDetail(params long[] productIds)
    {
        var relativePath = "/LineProduct/GetSimpleInfo";
        return await PostAsync<long[], List<GetLineProductSimpleInfoOutput>>(
            relativePath, productIds);
    }

    public Task RemoveLineProducts(IEnumerable<long> ids)
    {
        var relativePath = "/LineProduct/Remove";
        return PostAsync(relativePath, ids);
    }

    public async Task<List<GetLineProductSkuDetailOutput>> GetLineProductSkusByIds(params long[] ids)
    {
        var relativePath = "/LineProductSku/GetByIds";
        return await PostAsync<long[], List<GetLineProductSkuDetailOutput>>(relativePath, ids);
    }

    public async Task UpdateLineProductSupplierModuleSetting(UpdateLineProductSupplierModuleSettingInput input)
    {
        var relativePath = "/LineProduct/UpdateSupplierModuleSetting";
        await PostAsync(relativePath, input);
    }

    public async Task<GetLineProductSupplierModuleSettingOutput> GetLineProductSupplierModuleSetting(long lineProductId)
    {
        var relativePath = $"/LineProduct/GetSupplierModuleSetting?lineProductId={lineProductId}";
        return await GetAsync<GetLineProductSupplierModuleSettingOutput>(relativePath);
    }

    public async Task UpdateLineProductChannelModuleSetting(UpdateLineProductChannelModuleSettingInput input)
    {
        var relativePath = "/LineProduct/UpdateChannelModuleSetting";
        await PostAsync(relativePath, input);
    }

    public async Task<GetLineProductChannelModuleSettingOutput> GetLineProductChannelModuleSetting(long lineProductId)
    {
        var relativePath = $"/LineProduct/GetChannelModuleSetting?lineProductId={lineProductId}";
        return await GetAsync<GetLineProductChannelModuleSettingOutput>(relativePath);
    }
    
    public async Task UpdateLineProductOpenSupplierSetting(UpdateOpenSupplierSyncSettingInput input)
    {
        var relativePath = "/LineProduct/UpdateOpenSupplierSetting";
        await PostAsync(relativePath, input);
    }

    public async Task<LineProductOpenSupplierSettingInfo> GetLineProductOpenSupplierSetting(long lineProductId)
    {
        var relativePath = $"/LineProduct/GetOpenSupplierSetting?lineProductId={lineProductId}";
        return await GetAsync<LineProductOpenSupplierSettingInfo>(relativePath);
    }

    public async Task<GetLineProductCitiesOutput> GetLineProductCities(bool isCache, bool needAvailable)
    {
        var relativePath = $"/LineProduct/GetCities?isCache={isCache}&needAvailable={needAvailable}";
        return await GetAsync<GetLineProductCitiesOutput>(relativePath);
    }

    public async Task UpdateLineProductOpenChannelSetting(UpdateOpenChannelSyncSettingInput input)
    {
        var relativePath = "/LineProduct/UpdateOpenChannelSetting";
        await PostAsync(relativePath, input);
    }

    public async Task<List<LineProductOpenChannelSettingInfo>> GetLineProductOpenChannelSetting(long lineProductId)
    {
        var relativePath = $"/LineProduct/GetOpenChannelSetting?lineProductId={lineProductId}";
        return await GetAsync<List<LineProductOpenChannelSettingInfo>>(relativePath);
    }

    public async Task UpdateLineProductChannelTimelinessSetting(UpdateOpenChannelTimelinessSettingInput input)
    {
        var relativePath = "/LineProduct/UpdateChannelTimelinessSetting";
        await PostAsync(relativePath, input);
    }

    public async Task<GetOpenChannelTimelinessSettingOutput> GetLineProductChannelTimelinessSetting(long lineProductId)
    {
        var relativePath = $"/LineProduct/GetChannelTimelinessSetting?lineProductId={lineProductId}";
        return await GetAsync<GetOpenChannelTimelinessSettingOutput>(relativePath);
    }

    public async Task UpdateLineProductOperationModuleSetting(UpdateLineProductOperationModuleSettingInput input)
    {
        var relativePath = "/LineProduct/UpdateOperationModuleSetting";
        await PostAsync(relativePath, input);
    }

    public async Task<GetLineProductOperationModuleSettingOutput> GetLineProductOperationModuleSetting(
        long lineProductId)
    {
        var relativePath = $"/LineProduct/GetOperationModuleSetting?lineProductId={lineProductId}";
        return await GetAsync<GetLineProductOperationModuleSettingOutput>(relativePath);
    }

    public async Task<GetCompensationLineOutput> GetCompensationLine()
    {
        var relativePath = $"/LineProduct/GetCompensationLine";
        return await GetAsync<GetCompensationLineOutput>(relativePath);
    }
    
    #endregion

    #region LineProductSku
    public async Task<long> AddLineProductSku(AddLineProductSkuInput input)
    {
        string relativePath = "/LineProductSku/Add";
        return await PostAsync<AddLineProductSkuInput, long>(relativePath, input);
    }

    public async Task EditLineProductSku(EditLineProductSkuInput input)
    {
        string relativePath = "/LineProductSku/Edit";
        await PostAsync<EditLineProductSkuInput>(relativePath, input);
    }

    public async Task DeleteLineProductSku(RemoveProductSkuInput input)
    {
        string relativePath = "/LineProductSku/Remove";
        await PostAsync<RemoveProductSkuInput>(relativePath, input);
    }

    public async Task<GetLineProductSkuDetailOutput> LineProductSkuDetail(long id)
    {
        string relativePath = "/LineProductSku/detail?id=" + id;
        return await GetAsync<GetLineProductSkuDetailOutput>(relativePath);
    }

    public async Task SetLineProductSkuB2bSellingStatus(Contracts.Common.Product.DTOs.ProductSku.SetB2bSellingStatusInput input)
    {
        var relativePath = "/LineProductSku/SetB2bSellingStatus";
        await PostAsync(relativePath, input);
    }

    public async Task<GetLineProductSkuDetailOutput> GetLineProductSkuDetail(long id)
    {
        var relativePath = $"/LineProductSku/Detail?id={id}";
        return await GetAsync<GetLineProductSkuDetailOutput>(relativePath);
    }

    public async Task<List<GetLineProductSkuDetailOutput>> GetLineSkuByProductId(long id)
    {
        var relativePath = $"/LineProductSku/GetByProductId?id={id}";
        return await GetAsync<List<GetLineProductSkuDetailOutput>>(relativePath);
    }

    public async Task UpdateLineSkuApiContent(UpdateLineSkuApiContentInput input)
    {
        var relativePath = $"/LineProductSku/UpdateApiContent";
        await PostAsync(relativePath, input);
    }

    #endregion

    #region LineProductTimeSlot

    public async Task<List<CheckTimeSlotOutput>> CheckLineProductTimeSlot(CheckTimeSlotInput input)
    {
        var relativePath = $"/LineProductTimeSlot/CheckTimeSlot";
        return await PostAsync<CheckTimeSlotInput, List<CheckTimeSlotOutput>>(relativePath, input);
    }

    #endregion

    #region Group

    public async Task<List<GetProductStatisticsOutput>> GroupGetStatistics(long groupId)
    {
        string relativePath = $"/Group/GetStatistics?groupId={groupId}";
        return await GetAsync<List<GetProductStatisticsOutput>>(relativePath);
    }
    #endregion

    #region Disclaimer
    public async Task<List<GetDisclaimerUsedDetailsOutput>> DisclaimerGetUsedDetails(GetDisclaimerUsedDetailsInput input)
    {
        var relativePath = "/Disclaimer/GetUsedDetails";
        return await PostAsync<GetDisclaimerUsedDetailsInput, List<GetDisclaimerUsedDetailsOutput>>(relativePath, input);
    }
    #endregion

    #region AgencyChannelPriceSettings

    public async Task<IEnumerable<AgencyChannelPriceSettingOutput>> QueryAgencyChannelPriceSettings(
        QueryChannelPriceInput input)
    {
        var relativePath = "/AgencyChannelPriceSettings/Query";
        return await PostAsync<QueryChannelPriceInput, IEnumerable<AgencyChannelPriceSettingOutput>>(relativePath,
            input);
    }

    public async Task SetHuiZhiHotelChannelPriceSettings(SetHuiZhiHotelChannelPriceInput input)
    {
        var relativePath = "/AgencyChannelPriceSettings/SetHuiZhiHotel";
        await PostAsync(relativePath, input);
    }

    public async Task<GetHuiZhiHotelChannelPriceOutput> GetHuiZhiHotelChannelPriceSettings(long priceGroupId)
    {
        var relativePath = $"/AgencyChannelPriceSettings/GetHuiZhiHotel?priceGroupId={priceGroupId}";
        return await GetAsync<GetHuiZhiHotelChannelPriceOutput>(relativePath);
    }

    public async Task SetListAgencyChannelPriceSettings(SetChannelPriceDto dto)
    {
        var relativePath = $"/AgencyChannelPriceSettings/SetList";
        await PostAsync(relativePath, dto);
    }

    public async Task RemoveListAgencyChannelPriceSettings(RemoveChannelPriceDto dto)
    {
        var relativePath = $"/AgencyChannelPriceSettings/RemoveList";
        await PostAsync(relativePath, dto);
    }

    #endregion

    #region TicketProduct

    public Task<PagingModel<SearchTicketInfo, SearchTicketSummary>> TicketProductSearch(SearchTicketInput input)
    {
        var relativePath = "/TicketProduct/Search";
        return PostAsync<SearchTicketInput, PagingModel<SearchTicketInfo, SearchTicketSummary>>(relativePath, input);
    }

    public async Task<List<CheckProductValidityOutput>> CheckTicketProductValidity(params long[] productIds)
    {
        var relativePath = "/TicketProduct/CheckProductValidity";
        return await PostAsync<long[], List<CheckProductValidityOutput>>(
            relativePath, productIds);
    }

    public async Task<TicketInfo> GetTicketProductDetail(long productId)
    {
        var relativePath = $"/TicketProduct/Detail?productId={productId}";
        return await GetAsync<TicketInfo>(relativePath);
    }

    public async Task<List<GetProductSkusOutput>> GetTicketSkuByIds(List<long> skuIds)
    {
        var relativePath = "/ProductSku/GetByIds";
        return await PostAsync<List<long>, List<GetProductSkusOutput>>(relativePath, skuIds);
    }

    public async Task<List<GetProductSkusOutput>> GetTicketProductSkus(long productId)
    {
        var relativePath = $"/ProductSku/GetProductSkus?productId={productId}";
        return await GetAsync<List<GetProductSkusOutput>>(relativePath);
    }

    #endregion

    #region ProductSku
    public async Task SetProductSkuB2bSellingStatus(Contracts.Common.Product.DTOs.ProductSku.SetB2bSellingStatusInput input)
    {
        var relativePath = "/ProductSku/SetB2bSellingStatus";
        await PostAsync(relativePath, input);
    }

    public async Task ProductSkuSetEnabled(SetEnabledInput input)
    {
        var relativePath = "/ProductSku/SetEnabled";
        await PostAsync(relativePath, input);
    }
    #endregion

    #region LineProductSkuCalendarPrice
    public async Task BatchUpdateLineProductSkuCalendarPrice(BatchUpdateInput input)
    {
        var relativePath = "/LineProductSkuCalendarPrice/BatchUpdate";
        await PostAsync(relativePath, input);
    }

    public async Task<List<GetLineProductSkuCalendarPriceOutput>> GetLineProductSkuCalendarPriceByProductId(
        GetLineProductSkuCalendarPriceInput input)
    {
        var relativePath = "/LineProductSkuCalendarPrice/GetByProductId";
        return await PostAsync<GetLineProductSkuCalendarPriceInput, List<GetLineProductSkuCalendarPriceOutput>>(
            relativePath, input);
    }

    public async Task BatchDeleteLineProductSkuCalendarPrice(BatchDeleteInput input)
    {
        var relativePath = "/LineProductSkuCalendarPrice/BatchDelete";
        await PostAsync(relativePath, input);
    }

    public async Task SetLineProductRedundantData(SetProductRedundantDataInput input)
    {
        var relativePath = "/LineProductSkuCalendarPrice/SetProductRedundantData";
        await PostAsync(relativePath, input);
    }

    #endregion

    #region CarHailingProduct
    public async Task<GetCarHailingProductOutput> GetCarHailingProduct(long id)
    {
        var relativePath = $"/CarHailingProduct/Get?id={id}";
        return await GetAsync<GetCarHailingProductOutput>(relativePath);
    }

    #endregion

    #region CarProduct
    public async Task<long> AddCarProduct(AddCarProductInput input)
    {
        var relativePath = "/CarProduct/Add";
        return await PostAsync<AddCarProductInput, long>(relativePath, input);
    }
    public async Task UpdateCarProduct(UpdateCarProductInput input)
    {
        var relativePath = "/CarProduct/Update";
        await PostAsync(relativePath, input);
    }

    public async Task<PagingModel<SearchCarProductOuput, SearchCarProductSummary>> SearchCarProduct(SearchCarProductInput input)
    {
        var relativePath = "/CarProduct/Search";
        return await PostAsync<SearchCarProductInput, PagingModel<SearchCarProductOuput, SearchCarProductSummary>>(relativePath, input);
    }

    public async Task<CarProductDetailOutput> CarProductDetail(long id)
    {
        var relativePath = $"/CarProduct/Detail?id={id}";
        return await GetAsync<CarProductDetailOutput>(relativePath);
    }

    public async Task SetCarProductEnabled(SetCarProductEnabledInput input)
    {
        var relativePath = "/CarProduct/SetEnabled";
        await PostAsync(relativePath, input);
    }

    public async Task<PagingModel<GetCarProductsAndSkuOutput>> GetCarProductsAndSku(GetProductsAndSkuInput input)
    {
        var relativePath = "/CarProduct/GetProductsAndSku";
        return await PostAsync<GetProductsAndSkuInput, PagingModel<GetCarProductsAndSkuOutput>>(relativePath, input);
    }

    public async Task<CarProductDetailCalendarPricesOutput> GetCarProductPriceDetails(CarProductDetailCalendarPricesInput input)
    {
        var relativePath = "/CarProduct/GetProductPriceDetails";
        return await PostAsync<CarProductDetailCalendarPricesInput, CarProductDetailCalendarPricesOutput>(relativePath, input);
    }

    public async Task<string> CarProductSetMozio(List<long> productIdsInput)
    {
        var relativePath = "/CarProduct/SetMozio";
        return await PostAsync<List<long>, string>(relativePath, productIdsInput);
    }

    public async Task<GetCompensationCarProductOutput> GetCompensationCarProduct()
    {
        var relativePath = "/CarProduct/GetCompensationCarProduct";
        return await GetAsync<GetCompensationCarProductOutput>(relativePath);
    }

    #endregion

    #region CarTypeGrade
    public async Task<long> AddCarTypeGrade(AddCarTypeGradeInput input)
    {
        var relativePath = "/CarTypeGrade/Add";
        return await PostAsync<AddCarTypeGradeInput, long>(relativePath, input);
    }
    public async Task UpdateCarTypeGrade(UpdateCarTypeGradeInput input)
    {
        var relativePath = "/CarTypeGrade/Update";
        await PostAsync(relativePath, input);
    }

    public async Task<PagingModel<SearchCarTypeGradeOuput>> SearchCarTypeGrade(SearchCarTypeGradeInput input)
    {
        var relativePath = "/CarTypeGrade/Search";
        return await PostAsync<SearchCarTypeGradeInput, PagingModel<SearchCarTypeGradeOuput>>(relativePath, input);
    }

    #endregion

    #region CarServiceItem
    public async Task<long> AddCarServiceItem(AddCarServiceItemInput input)
    {
        var relativePath = "/CarServiceItem/Add";
        return await PostAsync<AddCarServiceItemInput, long>(relativePath, input);
    }
    public async Task UpdateCarServiceItem(UpdateCarServiceItemInput input)
    {
        var relativePath = "/CarServiceItem/Update";
        await PostAsync(relativePath, input);
    }

    public async Task<CarServiceItemOutput> DetailCarServiceItem(long id)
    {
        var relativePath = $"/CarServiceItem/Detail?id={id}";
        return await GetAsync<CarServiceItemOutput>(relativePath);
    }

    public async Task<PagingModel<SearchCarServiceItemOutput>> SearchCarServiceItem(SearchCarServiceItemInput input)
    {
        var relativePath = "/CarServiceItem/Search";
        return await PostAsync<SearchCarServiceItemInput, PagingModel<SearchCarServiceItemOutput>>(relativePath, input);
    }

    public async Task DeleteCarServiceItem(List<long> ids)
    {
        var relativePath = $"/CarServiceItem/Delete";
        await PostAsync<List<long>>(relativePath, ids);
    }

    public async Task SetCarServiceItemB2bSellingStatus(Contracts.Common.Product.DTOs.ProductSku.SetB2bSellingStatusInput input)
    {
        var relativePath = "/CarServiceItem/SetB2bSellingStatus";
        await PostAsync(relativePath, input);
    }

    public async Task SetCarServiceItemEnabled(SetCarServiceItemEnabledInput input)
    {
        var relativePath = "/CarServiceItem/SetEnabled";
        await PostAsync(relativePath, input);
    }
    #endregion

    #region CarProductSku
    public async Task<long> AddCarProductSku(AddCarProductSkuInput input)
    {
        var relativePath = "/CarProductSku/Add";
        return await PostAsync<AddCarProductSkuInput, long>(relativePath, input);
    }
    public async Task UpdateCarProductSku(UpdateCarProductSkuInput input)
    {
        var relativePath = "/CarProductSku/Update";
        await PostAsync(relativePath, input);
    }

    public async Task<CarProductSkuOutput> DetailCarProductSku(long id)
    {
        var relativePath = $"/CarProductSku/Detail?id={id}";
        return await GetAsync<CarProductSkuOutput>(relativePath);
    }

    public async Task DeleteCarProductSku(List<long> ids)
    {
        var relativePath = "/CarProductSku/Delete";
        await PostAsync(relativePath, ids);
    }
    public async Task SetCarProductSkuB2bSellingStatus(Contracts.Common.Product.DTOs.ProductSku.SetB2bSellingStatusInput input)
    {
        var relativePath = "/CarProductSku/SetB2bSellingStatus";
        await PostAsync(relativePath, input);
    }

    public async Task SetCarProductSkuEnabled(SetCarProductSkuEnabledInput input)
    {
        var relativePath = "/CarProductSku/SetEnabled";
        await PostAsync(relativePath, input);
    }
    public Task<List<CarProductSkuOutput>> GetCarProductSkDetails(CarProductSkuInput input)
    {
        var relativePath = $"/CarProductSku/Details";
        return PostAsync<CarProductSkuInput, List<CarProductSkuOutput>>(relativePath, input);
    }

    #endregion

    #region CarServiceItemCalendarPrice
    public async Task<List<GetCarServiceItemCalendarPriceOutput>> GetCarServiceItemCalendarPrice(GetCarServiceItemCalendarPriceInput input)
    {
        var relativePath = "/CarServiceItemCalendarPrice/Get";
        return await PostAsync<GetCarServiceItemCalendarPriceInput, List<GetCarServiceItemCalendarPriceOutput>>(relativePath, input);
    }
    public async Task UpdateCarServiceItemCalendarPrice(UpdateCarServiceItemCalendarPriceInput input)
    {
        var relativePath = "/CarServiceItemCalendarPrice/Update";
        await PostAsync(relativePath, input);
    }

    public async Task BatchUpdateCarServiceItemCalendarPrice(BatchUpdateCarServiceItemCalendarPriceInput input)
    {
        var relativePath = "/CarServiceItemCalendarPrice/BatchUpdate";
        await PostAsync(relativePath, input);
    }
    #endregion

    #region CarProductSkuCalendarPrice
    public async Task<List<GetCarProductSkuCalendarPriceOutput>> GetCarProductSkuCalendarPrice(GetCarProductSkuCalendarPriceInput input)
    {
        var relativePath = "/CarProductSkuCalendarPrice/Get";
        return await PostAsync<GetCarProductSkuCalendarPriceInput, List<GetCarProductSkuCalendarPriceOutput>>(relativePath, input);
    }

    public async Task BatchUpdateCarProductSkuCalendarPrice(BatchUpdateCarProductSkuCalendarPriceInput input)
    {
        var relativePath = "/CarProductSkuCalendarPrice/BatchUpdate";
        await PostAsync(relativePath, input);
    }
    #endregion

    #region Fields
    public async Task<List<FieldsOutput>> FieldsList(SearchFieldsInput input)
    {
        var relativePath = "/Fields/List";
        return await PostAsync<SearchFieldsInput, List<FieldsOutput>>(relativePath, input);
    }
    #endregion

    #region InformationTemplate
    public async Task<long> InformationTemplateSave(SaveInformationTemplateInput input)
    {
        var relativePath = "/InformationTemplate/Save";
        return await PostAsync<SaveInformationTemplateInput, long>(relativePath, input);
    }

    public async Task<PagingModel<SearchInformationTemplateOutput>> InformationTemplateSearch(SearchInformationTemplateInput input)
    {
        var relativePath = "/InformationTemplate/Search";
        return await PostAsync<SearchInformationTemplateInput, PagingModel<SearchInformationTemplateOutput>>(relativePath, input);
    }

    public async Task<InformationTemplateOutput> InformationTemplateDetail(long id)
    {
        var relativePath = "/InformationTemplate/Detail?id=" + id;
        return await GetAsync<InformationTemplateOutput>(relativePath);
    }

    public async Task InformationTemplateDelete(List<long> input)
    {
        var relativePath = "/InformationTemplate/Delete";
        await PostAsync<List<long>>(relativePath, input);
    }

    #endregion

    #region ProductDefaultInformationTemplate
    public async Task ProductDefaultTempSave(ProductDefaultInformationTemplateInput input)
    {
        var relativePath = "/ProductDefaultInformationTemplate/Save";
        await PostAsync<ProductDefaultInformationTemplateInput>(relativePath, input);
    }

    public async Task<List<SearchProductDefaultInformationTemplateOutput>> ProductDefaultTempList(SearchProductDefaultTemplateInput input)
    {
        var relativePath = "/ProductDefaultInformationTemplate/List";
        return await PostAsync<SearchProductDefaultTemplateInput, List<SearchProductDefaultInformationTemplateOutput>>(relativePath, input);
    }
    #endregion

    #region ProductInformationTemplate
    public async Task ProductInformationTemplateSave(SaveProductInformationTemplateInput input)
    {
        var relativePath = "/ProductInformationTemplate/Save";
        await PostAsync<SaveProductInformationTemplateInput>(relativePath, input);
    }

    public async Task<ProductInformationTemplateOutput> ProductInformationTemplateGetProductTemps(SearchProductInformationTemplateInput input)
    {
        var relativePath = "/ProductInformationTemplate/GetProductTemps";
        return await PostAsync<SearchProductInformationTemplateInput, ProductInformationTemplateOutput>(relativePath, input);
    }

    /// <summary>
    /// 更小颗粒度上配置的模版优先，套餐层模版设置 > 产品层模版设置 > 默认模版设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<GetProductTempFieldsDetailOutput> ProductInformationTemplateGetProductTempFieldsDetail(GetProductTempFieldsDetailInput input)
    {
        var relativePath = "/ProductInformationTemplate/GetProductTempFieldsDetail";
        return await PostAsync<GetProductTempFieldsDetailInput, GetProductTempFieldsDetailOutput>(relativePath, input);
    }
    #endregion

    #region openchannel

    public async Task<List<OpenChannelCheckProductOutput>> CheckOpenChannelProduct(params long[] channelProductSkuIds)
    {
        var relativePath = $"/BaseProduct/CheckOpenChannelProduct";
        return await PostAsync<long[], List<OpenChannelCheckProductOutput>>(relativePath, channelProductSkuIds);
    }

    #endregion

    #region opensupplier

    public async Task<GetOpenSupplierProductDetailOutput> GetOpenSupplierProductDetail(
        GetOpenSupplierProductDetailInput input)
    {
        var relativePath = "/OpenSupplierProduct/GetProductDetail";
        return await PostAsync<GetOpenSupplierProductDetailInput, GetOpenSupplierProductDetailOutput>(relativePath, input);
    }

    public async Task<GetOpenSupplierProductContentOutput> GetOpenSupplierProductContent(GetOpenSupplierProductContentInput input)
    {
        var relativePath = "/OpenSupplierProduct/GetProductContent";
        return await PostAsync<GetOpenSupplierProductContentInput, GetOpenSupplierProductContentOutput>(relativePath, input);
    }

    public async Task<List<GetOpenSupplierBasicProductOutput>> GetOpenSupplierBasicProducts(
        GetOpenSupplierBasicProductInput input)
    {
        var relativePath = "/OpenSupplierProduct/GetBasicProducts";
        return await PostAsync<GetOpenSupplierBasicProductInput, List<GetOpenSupplierBasicProductOutput>>(relativePath, input);
    }

    public async Task<List<QueryOpenSupplierSkuExtraInfoOutput>> QueryOpenSupplierSkuExtraInfo(
        QueryOpenSupplierSkuExtraInfoInput input)
    {
        var relativePath = "/OpenSupplierProduct/QuerySkuExtraInfo";
        return await PostAsync<QueryOpenSupplierSkuExtraInfoInput,List<QueryOpenSupplierSkuExtraInfoOutput>>(relativePath, input);
    }

    public async Task SyncOpenSupplierExtraInfo(SyncOpenSupplierExtraInfoInput receive)
    {
        var relativePath = "/OpenSupplierProduct/SyncExtraInfo";
        await PostAsync(relativePath, receive);
    }

    public async Task RetryMatchOpenSupplierBasicProduct(RetryMatchBasicProductInput input)
    {
        var relativePath = "/OpenSupplierProduct/RetryMatchBasicProduct";
        await PostAsync<RetryMatchBasicProductInput>(relativePath, input);
    }

    #endregion

    #region LineProductSkuTypeItem

    public async Task<List<GetOpenSupplierBasicProductOutput>> GetLineSkuItemMatchBasicProducts(
        GetBasicProductInput input)
    {
        var relativePath = "/LineProductSkuTypeItem/GetBasicProducts";
        return await PostAsync<GetBasicProductInput, List<GetOpenSupplierBasicProductOutput>>(relativePath, input);
    }

    public async Task AddLineSkuItem(AddLineSkuTypeItemInput input)
    {
        var relativePath = "/LineProductSkuTypeItem/Add";
        await PostAsync<AddLineSkuTypeItemInput>(relativePath, input);
    }

    public async Task NotifySyncLineSkuItemPriceInventory(NotifySyncThirdInventoryInput input)
    {
        var relativePath = "/LineProductSkuTypeItem/NotifySyncPriceInventory";
        await PostAsync<NotifySyncThirdInventoryInput>(relativePath, input);
    }

    public async Task<List<QueryLineSkuTypeItemOutput>> QueryLineSkuTypeItems(QueryLineSkuTypeItemInput input)
    {
        var relativePath = "/LineProductSkuTypeItem/Query";
        return await PostAsync<QueryLineSkuTypeItemInput, List<QueryLineSkuTypeItemOutput>>(relativePath, input);
    }

    public async Task<List<OpenSupplierQueryProductStockPollOutput>> QueryLineProductStockPoll(
        OpenSupplierQueryProductStockPollInput input)
    {
        var relativePath = "/LineProductSkuTypeItem/QueryProductStockPoll";
        return await PostAsync<OpenSupplierQueryProductStockPollInput, List<OpenSupplierQueryProductStockPollOutput>>(relativePath, input);
    }

    #endregion

    #region Store
    public async Task<PagingModel<SearchStoreOutput>> SearchStore(SearchStoreInput input)
    {
        var relativePath = "/Store/Search";
        return await PostAsync<SearchStoreInput, PagingModel<SearchStoreOutput>>(relativePath, input);
    }

    public async Task<GetStoreOutput> GetStore(long id)
    {
        var relativePath = "/Store/Get?id=" + id;
        return await GetAsync<GetStoreOutput>(relativePath);
    }

    public async Task AddStore(AddStoreInput input)
    {
        var relativePath = "/Store/Add";
        await PostAsync<AddStoreInput>(relativePath, input);
    }


    public async Task UpdateStore(UpdateStoreInput input)
    {
        var relativePath = "/Store/Update";
        await PostAsync<UpdateStoreInput>(relativePath, input);
    }
    #endregion

    #region Restaurant
    public async Task<PagingModel<SearchRestaurantOutput>> SearchRestaurant(SearchRestaurantInput input)
    {
        var relativePath = "/Restaurant/Search";
        return await PostAsync<SearchRestaurantInput, PagingModel<SearchRestaurantOutput>>(relativePath, input);
    }

    public async Task<GetRestaurantOutput> GetRestaurant(long id)
    {
        var relativePath = "/Restaurant/Get?id=" + id;
        return await GetAsync<GetRestaurantOutput>(relativePath);
    }

    public async Task AddRestaurant(AddRestaurantInput input)
    {
        var relativePath = "/Restaurant/Add";
        await PostAsync<AddRestaurantInput>(relativePath, input);
    }


    public async Task UpdateRestaurant(UpdateRestaurantInput input)
    {
        var relativePath = "/Restaurant/Update";
        await PostAsync<UpdateRestaurantInput>(relativePath, input);
    }
    #endregion

    #region OpenPlatform

    public async Task<PagingModel<SearchSyncLogOutput>> SearchOpenPlatformPricingSyncLog(SearchSyncLogInput input)
    {
        var relativePath = "/OpenPlatformPricingSyncLog/Search";
        return await PostAsync<SearchSyncLogInput, PagingModel<SearchSyncLogOutput>>(relativePath, input);
    }
    #endregion

    public async Task SetAgencyChannelCommissionSettings(SetAgencyChannelCommissionInput input)
    {
        var relativePath = "/AgencyChannelCommissionSettings/Set";
        await PostAsync<SetAgencyChannelCommissionInput>(relativePath, input);
    }

    public async Task<List<QueryAgencyChannelCommissionOutput>> QueryAgencyChannelCommissionSettings(QueryAgencyChannelCommissionInput input)
    {
        var relativePath = "/AgencyChannelCommissionSettings/Query";
        return await PostAsync<QueryAgencyChannelCommissionInput, List<QueryAgencyChannelCommissionOutput>>(relativePath, input);
    }

    #region OpenSupplierProductSyncLog

    public async Task AddOpenSupplierProductSyncLog(AddProductSyncLogInput input)
    {
        var relativePath = "/OpenSupplierProductSyncLog/Add";
        await PostAsync<AddProductSyncLogInput>(relativePath, input);
    }

    public async Task<PagingModel<SearchProductSyncLogOutput>> SearchOpenSupplierProductSyncLog(SearchProductSyncLogInput input)
    {
        var relativePath = "/OpenSupplierProductSyncLog/Search";
        return await PostAsync<SearchProductSyncLogInput, PagingModel<SearchProductSyncLogOutput>>(relativePath, input);
    }

    #endregion
}
