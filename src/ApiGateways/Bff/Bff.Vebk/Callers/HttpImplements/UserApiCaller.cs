using Bff.Vebk.Models.TenantUser;
using Common.Caller;
using Common.ServicesHttpClient;
using Contracts.Common.User.DTOs;
using Contracts.Common.User.DTOs.AgencyUser;
using Contracts.Common.User.DTOs.CustomerUser;
using Contracts.Common.User.DTOs.DarenBonus;
using Contracts.Common.User.DTOs.DarenUser;
using Contracts.Common.User.DTOs.OperationLog;
using Contracts.Common.User.DTOs.PlatformBind;
using Contracts.Common.User.DTOs.SupplierUser;
using Contracts.Common.User.DTOs.TenantUser;
using Contracts.Common.User.DTOs.TenantUserSecurityVerification;
using Contracts.Common.User.DTOs.UserBinding;
using EfCoreExtensions.Abstract;
using Microsoft.Extensions.Options;
using SearchInput = Contracts.Common.User.DTOs.SupplierUser.SearchInput;

namespace Bff.Vebk.Callers.HttpImplements;

public class UserApiCaller : HttpCallerBase, IUserApiCaller
{
    public UserApiCaller(IOptions<ServicesAddress> options, IHttpClientFactory httpClientFactory)
        : base(options.Value.User, httpClientFactory)
    {
    }

    #region Security

    public async Task SendCaptcha(CaptchaDTO input)
    {
        string relativePath = "/Security/SendCaptcha";
        await PostAsync<CaptchaDTO, string>(relativePath, input);
    }

    public async Task<bool> CheckCaptcha(CaptchaDTO input)
    {
        string relativePath = "/Security/CheckCaptcha";
        return await PostAsync<CaptchaDTO, bool>(relativePath, input);
    }

    #endregion

    #region Huizhi

    public async Task<IEnumerable<HuiZhiBindData>> GetHuiZhiBindData(GetBindDataInput input)
    {
        string relativePath = "/HuiZhiBind/GetBindData";
        return await PostAsync<GetBindDataInput, IEnumerable<HuiZhiBindData>>(relativePath, input);
    }

    public async Task<IEnumerable<CreateBindUrlsOutput>> CreateBindUrls(IEnumerable<CreateBindUrlsInput> input)
    {
        string relativePath = "/HuiZhiBind/CreateBindUrls";
        return await PostAsync<IEnumerable<CreateBindUrlsInput>, IEnumerable<CreateBindUrlsOutput>>(relativePath, input);
    }

    public async Task Unbound(PlatformUnboundInput input)
    {
        string relativePath = "/HuiZhiBind/Unbound";
        await PostAsync(relativePath, input);
    }

    #endregion

    #region UserBind

    public Task<List<GetUserBindingOutput>> GetUserBindings(GetUserBindingInput input)
    {
        var relativePath = "/UserBinding/GetUserBindings";
        return PostAsync<GetUserBindingInput, List<GetUserBindingOutput>>(relativePath, input);
    }

    public async Task UnboundUserBindings(UnboundUserBindingInput input)
    {
        string relativePath = "/UserBinding/Unbound";
        await PostAsync(relativePath, input);
    }

    #endregion

    #region AgencyUser

    public async Task<PagingModel<Contracts.Common.User.DTOs.AgencyUser.AgencyUserDto>> AgencyUserSearch(Contracts.Common.User.DTOs.AgencyUser.SearchInput input)
    {
        string relativePath = "/AgencyUser/Search";
        return await PostAsync<Contracts.Common.User.DTOs.AgencyUser.SearchInput, PagingModel<Contracts.Common.User.DTOs.AgencyUser.AgencyUserDto>>(relativePath, input);
    }

    public Task<List<Contracts.Common.User.DTOs.AgencyUser.AgencyUserDto>> AgencyUserList(Contracts.Common.User.DTOs.AgencyUser.SearchInput input)
    {
        string relativePath = "/AgencyUser/List";
        return PostAsync<Contracts.Common.User.DTOs.AgencyUser.SearchInput, List<Contracts.Common.User.DTOs.AgencyUser.AgencyUserDto>>(relativePath, input);
    }

    public async Task<Contracts.Common.User.DTOs.AgencyUser.AgencyUserDto> AgencyUserFindOne(Contracts.Common.User.DTOs.AgencyUser.AgencyUserFindOneInput input)
    {
        string relativePath = "/AgencyUser/FindOne";
        return await PostAsync<Contracts.Common.User.DTOs.AgencyUser.AgencyUserFindOneInput, Contracts.Common.User.DTOs.AgencyUser.AgencyUserDto>(relativePath, input);
    }

    public async Task<long> AgencyUserAdd(Contracts.Common.User.DTOs.AgencyUser.AddInput input)
    {
        string relativePath = "/AgencyUser/Add";
        return await PostAsync<Contracts.Common.User.DTOs.AgencyUser.AddInput, long>(relativePath, input);
    }

    public async Task AgencyUserUpdate(Contracts.Common.User.DTOs.AgencyUser.UpdateInput input)
    {
        string relativePath = "/AgencyUser/Update";
        await PostAsync(relativePath, input);
    }

    public async Task<List<AgencyUserDto>> GetAgencyUserDetails(params long[] ids)
    {
        var relativePath = "/AgencyUser/GetDetails";
        return await PostAsync<long[], List<AgencyUserDto>>(relativePath, ids);
    }

    public Task OnOrOff(OnOrOffInput input)
    {
        string relativePath = "/AgencyUser/OnOrOff";
        return PostAsync<Contracts.Common.User.DTOs.AgencyUser.OnOrOffInput>(relativePath, input);
    }

    public Task<string> RestPassword(RestPasswordInput input)
    {
        string relativePath = "/AgencyUser/RestPassword";
        return PostAsync<Contracts.Common.User.DTOs.RestPasswordInput, string>(relativePath, input);
    }

    public Task UpdatePassword(UpdatePasswordInput input)
    {
        string relativePath = "/AgencyUser/UpdatePassword";
        return PostAsync<Contracts.Common.User.DTOs.UpdatePasswordInput>(relativePath, input);
    }

    public Task<bool> AgencyUsersAnyAsync(AgencyUserVerifyInput input)
    {
        string relativePath = "/AgencyUser/AgencyUsersAny";
        return PostAsync<AgencyUserVerifyInput, bool>(relativePath, input);
    }

    public Task SetEmailStatusAsync(AgencyUserEmailStatusInput input)
    {
        string relativePath = $"/AgencyUser/SetEmailStatus";
        return PostAsync<AgencyUserEmailStatusInput>(relativePath, input);
    }

    public Task<List<string>> SearchPhone(List<string> phones)
    {
        string relativePath = $"/AgencyUser/SearchPhone";
        return PostAsync<List<string>, List<string>>(relativePath, phones);
    }

    #endregion

    #region SupplierUser

    public async Task<PagingModel<SupplierUserDto>> SupplierUserSearch(SearchInput input)
    {
        string relativePath = "/SupplierUser/Search";
        return await PostAsync<SearchInput, PagingModel<SupplierUserDto>>(relativePath, input);
    }
    public async Task<SupplierUserDto> SupplierUserFindOne(FindOneInput input)
    {
        string relativePath = "/SupplierUser/FindOne";
        return await PostAsync<FindOneInput, SupplierUserDto>(relativePath, input);
    }

    #endregion

    #region TenantUser

    public async Task<PagingModel<UserSearchOuput>> TenantUserSearch(UserSearchInput input)
    {
        string relativePath = "/TenantUser/Search";
        return await PostAsync<UserSearchInput, PagingModel<UserSearchOuput>>(relativePath, input);
    }

    public async Task<UserSearchOuput> TenantUserFindOne(FindOneInput input)
    {
        string relativePath = "/TenantUser/FindOne";
        return await PostAsync<FindOneInput, UserSearchOuput>(relativePath, input);
    }

    public async Task<bool> SetEnabled(SetUserEnabledInput input)
    {
        string relativePath = "/TenantUser/SetEnabled";
        return await PostAsync<SetUserEnabledInput, bool>(relativePath, input);
    }

    public async Task<string> TenantUserRestPassword(RestPasswordInput input)
    {
        string relativePath = "/TenantUser/RestPassword";
        return await PostAsync<RestPasswordInput, string>(relativePath, input);
    }

    public async Task<bool> UpdateTenantUserName(UpdateTenantUserNameInput input)
    {
        string relativePath = "/TenantUser/UpdateName";
        return await PostAsync<UpdateTenantUserNameInput, bool>(relativePath, input);
    }

    public async Task<bool> UpdateTenantUserVaildStatus(UpdateTenantUserVaildInput input)
    {
        string relativePath = "/TenantUser/UpdateVaildStatus";
        return await PostAsync<UpdateTenantUserVaildInput, bool>(relativePath, input);
    }

    public Task<List<UserSearchOuput>> SearchTenantUsers(SearchTenantUsersInput input)
    {
        string relativePath = "/TenantUser/SearchUsers";
        return PostAsync<SearchTenantUsersInput, List<UserSearchOuput>>(relativePath, input);
    }

    public async Task UpdateTenantUserJobId(UpdateJobIdInput input)
    {
        string relativePath = "/TenantUser/UpdateJobId";
        await PostAsync<UpdateJobIdInput>(relativePath, input);
    }
    #endregion

    #region Daren

    public Task<IEnumerable<GetOrderBonusStatOutput>> GetOrderBonusStat(GetOrderBonusStatInput input)
    {
        var relativePath = "/DarenBonus/OrderBonusStat";
        return PostAsync<GetOrderBonusStatInput, IEnumerable<GetOrderBonusStatOutput>>(relativePath, input);
    }

    public Task<List<GetDarenBonusByOrderOutput>> GetDarenBonusByOrder(GetDarenBonusByOrderInput input)
    {
        var relativePath = "/DarenBonus/GetByOrder";
        return PostAsync<GetDarenBonusByOrderInput, List<GetDarenBonusByOrderOutput>>(relativePath, input);
    }

    public async Task<List<GetDarenOutput>> GetDaren(params long[] userIds)
    {
        var relativePath = "/DarenUser/GetDaren";
        return await PostAsync<long[], List<GetDarenOutput>>(relativePath, userIds);
    }
    #endregion

    #region CustomerUser

    public async Task<List<CustomerUserDTO>> GetCustomers(GetCustomersInput input)
    {
        var relativePath = "/CustomerUser/GetCustomers";
        return await PostAsync<GetCustomersInput, List<CustomerUserDTO>>(relativePath, input);
    }

    public async Task<PagingModel<SearchCustomerUserPageOutput>> SearchCustomerUsers(SearchCustomerUserPageInput input)
    {
        var relativePath = "/CustomerUser/Search";
        return await PostAsync<SearchCustomerUserPageInput, PagingModel<SearchCustomerUserPageOutput>>(relativePath, input);
    }

    public async Task<List<GetCustomerUserByIdsOutput>> GetCustomerUserByIds(List<long> userIds)
    {
        var relativePath = "/CustomerUser/GetByIds";
        return await PostAsync<List<long>, List<GetCustomerUserByIdsOutput>>(relativePath, userIds);
    }

    #endregion

    #region DarenWithdrawals

    public Task<IEnumerable<Contracts.Common.User.DTOs.DarenWithdrawal.DarenWithdrawalOutput>> SearchDarenWithdrawalByIds(IEnumerable<long> ids)
    {
        var relativePath = "/DarenWithdrawals/SearchByIds";
        return PostAsync<IEnumerable<long>, IEnumerable<Contracts.Common.User.DTOs.DarenWithdrawal.DarenWithdrawalOutput>>(relativePath, ids);
    }

    #endregion

    #region TenantUserSecurityVerification
    /// <summary>
    /// 判断是否触发双因素认证
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> CheckTenantUserSecurityVerification(CheckSecurityVerificationInput input)
    {
        string relativePath = "/TenantUserSecurityVerification/Check";
        return await PostAsync<CheckSecurityVerificationInput, bool>(relativePath, input);
    }

    /// <summary>
    /// 保存指纹数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> SaveTenantUserSecurityVerification(SaveTenantUserSecurityVerificationInput input)
    {
        string relativePath = "/TenantUserSecurityVerification/Save";
        return await PostAsync<SaveTenantUserSecurityVerificationInput, bool>(relativePath, input);
    }
    #endregion

    #region OperationLog
    public async Task AddOperationLog(OperationLogDto input)
    {
        string relativePath = "/OperationLog/AddOperationLog";
        await PostAsync<OperationLogDto>(relativePath, input);
    }

    public async Task<PagingModel<OperationLogOutput>> OperationLogSearch(SearchOperationLogInput input)
    {
        string relativePath = "/OperationLog/Search";
        return await PostAsync<SearchOperationLogInput, PagingModel<OperationLogOutput>>(relativePath, input);
    }

    #endregion

    public async Task SetUserEnabledByDingtalkUserLeave(SetEnabledByDingtalkUserLeaveInput input)
    {
        string relativePath = "/DingtalkUser/SetEnabledByDingtalkUserLeave";
        await PostAsync<SetEnabledByDingtalkUserLeaveInput>(relativePath, input);
    }
}
