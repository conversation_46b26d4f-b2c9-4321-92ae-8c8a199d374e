using Bff.Vebk.Models.OrderPrintSetting;
using Bff.Vebk.Models.ReceiptOrder;
using Bff.Vebk.Models.ReceiptSettlementOrder;
using Bff.Vebk.Models.SettlementPayables;
using Common.Caller;
using Common.ServicesHttpClient;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.AggregateOrder;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.DTOs.BaseOrderRemark;
using Contracts.Common.Order.DTOs.BatchOrderOperationTask;
using Contracts.Common.Order.DTOs.CarProductSupplierOrder;
using Contracts.Common.Order.DTOs.CompensationOrder;
using Contracts.Common.Order.DTOs.Finance;
using Contracts.Common.Order.DTOs.GroupBooking;
using Contracts.Common.Order.DTOs.HotelApiOrder;
using Contracts.Common.Order.DTOs.HotelOrder;
using Contracts.Common.Order.DTOs.Insure;
using Contracts.Common.Order.DTOs.Invoice;
using Contracts.Common.Order.DTOs.InvoiceTitle;
using Contracts.Common.Order.DTOs.Kingdee.KingdeePush;
using Contracts.Common.Order.DTOs.KlookOrder;
using Contracts.Common.Order.DTOs.OffsetOrder;
using Contracts.Common.Order.DTOs.OffsetOrderDingtalkApply;
using Contracts.Common.Order.DTOs.OpenChannelOrder;
using Contracts.Common.Order.DTOs.OpenChannelRefundApplyOrder;
using Contracts.Common.Order.DTOs.OpenChannelSyncFailOrder;
using Contracts.Common.Order.DTOs.OpenSupplierOrder;
using Contracts.Common.Order.DTOs.OrderDelayed;
using Contracts.Common.Order.DTOs.OrderManualVoucher;
using Contracts.Common.Order.DTOs.OrderOperationTask;
using Contracts.Common.Order.DTOs.OrderPrintSetting;
using Contracts.Common.Order.DTOs.ReceiptSettlementOrder;
using Contracts.Common.Order.DTOs.RefundOrder;
using Contracts.Common.Order.DTOs.ReservationOrder;
using Contracts.Common.Order.DTOs.ScenicTicketOrder;
using Contracts.Common.Order.DTOs.ScenicTicketOrder.OTA;
using Contracts.Common.Order.DTOs.ScenicTicketPurchaseOrder;
using Contracts.Common.Order.DTOs.ScenicTicketSupplierOrder;
using Contracts.Common.Order.DTOs.SettlementPayables;
using Contracts.Common.Order.DTOs.ThirdInsureProduct;
using Contracts.Common.Order.DTOs.TicketOrder;
using Contracts.Common.Order.DTOs.TicketsCombinationOrder;
using Contracts.Common.Order.DTOs.TravelLineOrder;
using Contracts.Common.Order.DTOs.TravelLineOrder.OTA;
using Contracts.Common.Order.DTOs.WorkOrder;
using Contracts.Common.Order.DTOs.WorkOrderServiceEvaluation;
using EfCoreExtensions.Abstract;
using Microsoft.Extensions.Options;
using AfterSaleFinancialHandleOrder = Contracts.Common.Order.DTOs.AfterSaleFinancialHandleOrder;
using CarProductOrder = Contracts.Common.Order.DTOs.CarProductOrder;
using GroupBookingOrder = Contracts.Common.Order.DTOs.GroupBookingOrder;
using MailOrder = Contracts.Common.Order.DTOs.MailOrder;
using SettlementOrder = Contracts.Common.Order.DTOs.SettlementOrder;
using TravelLineOrder = Contracts.Common.Order.DTOs.TravelLineOrder;
using GroupBookingFinancialHandleOrder = Contracts.Common.Order.DTOs.GroupBookingFinancialHandleOrder;
using Contracts.Common.Order.DTOs.GroupBookingAreaSetting;
using Contracts.Common.Order.DTOs.GroupBookingAggregate;
using Contracts.Common.Order.DTOs.TicketCombinationOrderAfterSalse;
using Contracts.Common.Order.DTOs.YouxiaTripOrder;

namespace Bff.Vebk.Callers.HttpImplements;

public class OrderApiCaller : HttpCallerBase, IOrderApiCaller
{
    public OrderApiCaller(IOptions<ServicesAddress> servicesAddress, IHttpClientFactory httpClientFactory)
        : base(servicesAddress.Value.Order, httpClientFactory)
    {
    }

    #region HotelApiOrder

    public Task<HopOrderStatusNotifyOutput> HopOrderStatusNotify(HopOrderStatusNotifyInput input)
    {
        string relativePath = "/HotelApiOrder/HopOrderStatusNotify";
        return PostAsync<HopOrderStatusNotifyInput, HopOrderStatusNotifyOutput>(relativePath, input);
    }

    public Task<HopOrderNotifyOutput> HopOrderNotify(HopOrderNotifyInput input)
    {
        string relativePath = "/HotelApiOrder/HopOrderNotify";
        return PostAsync<HopOrderNotifyInput, HopOrderNotifyOutput>(relativePath, input);
    }

    #endregion

    #region TravelLineOrder

    public Task<PagingModel<TravelLineOrder.SearchOutput, OrderStatusStatOutput>> TravelLineOrderSearch(TravelLineOrder.SearchInput input)
    {
        var relativePath = "/TravelLineOrder/Search";
        return PostAsync<TravelLineOrder.SearchInput, PagingModel<TravelLineOrder.SearchOutput, OrderStatusStatOutput>>(relativePath, input);
    }

    public Task<TravelLineOrder.OrderDetailOutput> TravelLineOrderDetail(TravelLineOrder.OrderDetailInput input)
    {
        var relativePath = "/TravelLineOrder/Detail";
        return PostAsync<TravelLineOrder.OrderDetailInput, TravelLineOrder.OrderDetailOutput>(relativePath, input);
    }

    public Task TravelLineOrderClaim(TravelLineOrder.ClaimInput input)
    {
        var relativePath = "/TravelLineOrder/Claim";
        return PostAsync<TravelLineOrder.ClaimInput>(relativePath, input);
    }

    public Task TravelLineOrderConfirm(TravelLineOrder.ConfirmInput input)
    {
        var relativePath = "/TravelLineOrder/Confirm";
        return PostAsync<TravelLineOrder.ConfirmInput>(relativePath, input);
    }

    public Task TravelLineOrderFinish(TravelLineOrder.FinishInput input)
    {
        var relativePath = "/TravelLineOrder/Finish";
        return PostAsync<TravelLineOrder.FinishInput>(relativePath, input);
    }

    public Task TravelLineOrderSendConfirmSms(TravelLineOrder.SendConfirmSmsInput input)
    {
        var relativePath = "/TravelLineOrder/SendConfirmSms";
        return PostAsync(relativePath, input);
    }

    public Task TravelLineOrderUpdateTourGuide(TravelLineOrder.UpdateTourGuideInput input)
    {
        var relativePath = "/TravelLineOrder/UpdateTourGuide";
        return PostAsync(relativePath, input);
    }

    public Task<TravelLineOrder.OrderRefundableOutput> TravelLineOrderRefundable(TravelLineOrder.OrderRefundableInput input)
    {
        var relativePath = "/TravelLineOrder/OrderRefundable";
        return PostAsync<TravelLineOrder.OrderRefundableInput, TravelLineOrder.OrderRefundableOutput>(relativePath, input);
    }

    public Task TravelLineOrderRefund(TravelLineOrder.OrderRefundInput input)
    {
        var relativePath = "/TravelLineOrder/Refund";
        return PostAsync(relativePath, input);
    }

    public Task<CreateLineOrderOutput> TravelLineOrderCreate(TravelLineOrder.CreateDto input)
    {
        var relativePath = "/TravelLineOrder/Create";
        return PostAsync<TravelLineOrder.CreateDto, CreateLineOrderOutput>(relativePath, input);
    }

    public async Task<CreateTravelLineOTAOrderOutput> CreateOTATravelLineOrder(TravelLineOrder.CreateDto input)
    {
        var relativePath = "/TravelLineOTAOrder/Create";
        return await PostAsync<TravelLineOrder.CreateDto, CreateTravelLineOTAOrderOutput>(relativePath, input);
    }

    public async Task<TravelLineOrder.GetLineChannelOrderInfoOutput> GetLineChannelOrderInfo(string channelOrderNo)
    {
        var relativePath = $"/TravelLineOrder/GetChannelOrderInfo?channelOrderNo={channelOrderNo}";
        return await GetAsync<TravelLineOrder.GetLineChannelOrderInfoOutput>(relativePath);
    }

    public Task UpdateTravelLineOrderSupplierOrderId(UpdateSupplierOrderIdInput input)
    {
        var relativePath = "/TravelLineOrder/UpdateSupplierOrderId";
        return PostAsync(relativePath, input);
    }

    public async Task EditTravelLineOrderCost(UpdateCostInput input)
    {
        var relativePath = "/TravelLineOrder/EditCost";
        await PostAsync<UpdateCostInput>(relativePath, input);
    }

    public async Task<bool> UpdateTravelLineOrderContact(UpdateContactInput input)
    {
        var relativePath = "/TravelLineOrder/UpdateContact";
        return await PostAsync<UpdateContactInput, bool>(relativePath, input);
    }

    public async Task<bool> UpdateTravelLineOrderTravelInfo(TravelLineOrder.UpdateTravelInfoInput input)
    {
        var relativePath = "/TravelLineOrder/UpdateTravelerInfo";
        return await PostAsync<TravelLineOrder.UpdateTravelInfoInput, bool>(relativePath, input);
    }

    public async Task UpdateTravelLineOrderChannelOrderNo(UpdateChannelOrderNoInput input)
    {
        var relativePath = "/TravelLineOrder/UpdateChannelOrderNo";
        await PostAsync(relativePath, input);
    }

    public async Task UpdateTravelLineCostDiscountRate(UpdateCostDiscountRateInput input)
    {
        var relativePath = "/TravelLineOrder/UpdateCostDiscountRate";
        await PostAsync(relativePath, input);
    }

    public async Task UpdateTravelLineTravelTime(UpdateTravelTimeInput input)
    {
        var relativePath = "/TravelLineOrder/UpdateTravelTime";
        await PostAsync(relativePath, input);
    }

    public async Task<QueryTravelLineOrderSkuTypeItemOutput> QueryTravelLineOrderSkuTypeItems(QueryTravelLineOrderSkuTypeItemInput input)
    {
        var relativePath = "/TravelLineOrder/QuerySkuTypeItems";
        return await PostAsync<QueryTravelLineOrderSkuTypeItemInput, QueryTravelLineOrderSkuTypeItemOutput>(relativePath, input);
    }

    public async Task ReplaceLineOrderProduct(
        Contracts.Common.Order.DTOs.TravelLineOrder.ReplaceOrderProductInput input)
    {
        var relativePath = "/TravelLineOrder/ReplaceOrderProduct";
        await PostAsync(relativePath, input);
    }

    public async Task TravelLineOrderVoucherManualAdd(ManualAddVoucherInput input)
    {
        var relativePath = "/TravelLineVoucher/ManualAdd";
        await PostAsync<ManualAddVoucherInput>(relativePath, input);
    }

    public async Task<ManualDeliveryVoucherOutput> TravelLineOrderVoucherManualDelivery(
        ManualDeliveryVoucherInput input)
    {
        var relativePath = "/TravelLineVoucher/ManualDelivery";
        return await PostAsync<ManualDeliveryVoucherInput, ManualDeliveryVoucherOutput>(relativePath, input);
    }

    public async Task<LineOrderSendEmailSyncOutput> LineOrderSendEmailSync(LineOrderSendEmailSyncInput input)
    {
        var relativePath = "/TravelLineOrder/SendEmailSync";
        return await PostAsync<LineOrderSendEmailSyncInput, LineOrderSendEmailSyncOutput>(relativePath, input);
    }

    #endregion

    #region TicketOrder

    public Task<GetReservationabledOutput> GetTicketOrderReservationabled(long baseOrderId)
    {
        var relativePath = $"/TicketOrder/GetReservationabled?baseOrderId={baseOrderId}";
        return GetAsync<GetReservationabledOutput>(relativePath);
    }


    public Task UpdateTicketOrderSupplierOrderId(UpdateSupplierOrderIdInput input)
    {
        var relativePath = "/TicketOrder/UpdateSupplierOrderId";
        return PostAsync(relativePath, input);
    }

    public Task<GetTicketOrderOutput> GetTicketOrderId(long baseOrderId)
    {
        var relativePath = $"/TicketOrder/Get?baseOrderId={baseOrderId}";
        return GetAsync<GetTicketOrderOutput>(relativePath);
    }

    #endregion

    #region MailOrder

    /// <summary>
    /// 订单详情
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    public Task<MailOrder.OrderDetailsOutput> GetMailOrderByBaseOrderId(long baseOrderId)
    {
        var relativePath = $"/MailOrder/DetailsByTenant?baseOrderId={baseOrderId}";
        return GetAsync<MailOrder.OrderDetailsOutput>(relativePath);
    }

    #endregion

    #region ReservationOrder

    public Task<GetReservationOrderOutput> GetReservationOrder(long reservationOrderId)
    {
        var relativePath = $"/ReservationOrder/Get?reservationOrderId={reservationOrderId}";
        return GetAsync<GetReservationOrderOutput>(relativePath);
    }

    public Task<PaymentInfoOutput> GetReservationOrderPaymentInfo(long orderId)
    {
        var relativePath = $"/ReservationOrder/PaymentInfo?orderId={orderId}";
        return GetAsync<PaymentInfoOutput>(relativePath);
    }

    #endregion

    #region BaseOrder

    public Task<PaymentInfoOutput> GetBaseOrderPaymentInfo(long orderId)
    {
        var relativePath = $"/BaseOrder/PaymentInfo?orderId={orderId}";
        return GetAsync<PaymentInfoOutput>(relativePath);
    }

    public Task<IList<OrderAmountInfoOutput>> GetOrderAmountInfos(OrderAmountInfoInput input)
    {
        var relativePath = "/BaseOrder/OrderAmountInfos";
        return PostAsync<OrderAmountInfoInput, IList<OrderAmountInfoOutput>>(relativePath, input);
    }

    public Task<List<OrderMultPriceOutput>> GetOrderPriceByBaseOrderId(long baseOrderId)
    {
        var relativePath = $"/OrderPrice/GetByBaseOrderId?baseOrderId={baseOrderId}";
        return GetAsync<List<OrderMultPriceOutput>>(relativePath);
    }

    public Task<List<RefundOrderDetailOutDto>> GetRefundsByBaseOrderId(long baseOrderId)
    {
        var relativePath = $"/Refund/GetRefundsByBaseOrderId?baseOrderId={baseOrderId}";
        return GetAsync<List<RefundOrderDetailOutDto>>(relativePath);
    }

    public Task<List<RefundOrderDetailOutDto>> GetRefundsBySubOrderId(long subOrderId)
    {
        var relativePath = $"/Refund/GetRefundsBySubOrderId?subOrderId={subOrderId}";
        return GetAsync<List<RefundOrderDetailOutDto>>(relativePath);
    }

    public async Task<List<SearchUserConsumptionOutput>> GetConsumptionByIds(List<long> ids)
    {
        var relativePath = "/BaseOrder/GetConsumptionByIds";
        return await PostAsync<List<long>, List<SearchUserConsumptionOutput>>(relativePath, ids);
    }

    public async Task<PagingModel<SearchUserConsumptionOutput>> SearchConsumptionStatistic(
        SearchUserConsumptionPageInput input)
    {
        var relativePath = "/BaseOrder/SearchConsumption";
        return await PostAsync<SearchUserConsumptionPageInput, PagingModel<SearchUserConsumptionOutput>>(relativePath,
            input);
    }

    public async Task<CheckChannelOrderOutput> CheckChannelOrder(CheckChannelOrderInput input)
    {
        var relativePath = "/BaseOrder/CheckChannelOrder";
        return await PostAsync<CheckChannelOrderInput, CheckChannelOrderOutput>(relativePath, input);
    }

    public async Task<CheckChannelOrderOutput> CheckChannelOrderAbnormalOrder(CheckChannelOrderInput input)
    {
        var relativePath = "/BaseOrder/CheckChannelOrderAbnormalOrder";
        return await PostAsync<CheckChannelOrderInput, CheckChannelOrderOutput>(relativePath, input);
    }

    public async Task<CheckSupplierOrderOutput> CheckSupplierOrder(CheckSupplierOrderInput input)
    {
        var relativePath = "/BaseOrder/CheckSupplierOrder";
        return await PostAsync<CheckSupplierOrderInput, CheckSupplierOrderOutput>(relativePath, input);
    }

    public Task<bool> SetTrackingUserId(SetTrackingUserIdInput input)
    {
        var relativePath = "/BaseOrder/SetTrackingUserId";
        return PostAsync<SetTrackingUserIdInput, bool>(relativePath, input);
    }

    public Task CancelBaseOrder(CancelOrderInput input)
    {
        var relativePath = "/BaseOrder/Cancel";
        return PostAsync(relativePath, input);
    }

    public Task<IEnumerable<OrderLogsOutput>> OrderOperatingRecords(long orderId)
    {
        var relativePath = $"/BaseOrder/OperatingRecords?orderId={orderId}";
        return GetAsync<IEnumerable<OrderLogsOutput>>(relativePath);
    }

    public Task<bool> SetOrderOpUserId(SetOrderOpUserIdInput input)
    {
        var relativePath = "/BaseOrder/SetOrderOpUserId";
        return PostAsync<SetOrderOpUserIdInput, bool>(relativePath, input);
    }
    #endregion

    #region SettlementOrder

    public Task<List<SettlementOrder.GetPayableOrderAmountOutput>> GetPayableOrderAmount(SettlementOrder.GetPayableOrderAmountInput input)
    {
        var relativePath = "/SettlementPayables/GetPayableOrderAmount";
        return PostAsync<SettlementOrder.GetPayableOrderAmountInput, List<SettlementOrder.GetPayableOrderAmountOutput>>(relativePath, input);
    }

    public Task<IEnumerable<SettlementOrder.GetPayableOrderDetailOutput>> GetPayableOrderDetailByOrderId(SettlementOrder.GetPayableOrderDetailInput input)
    {
        var relativePath = "/SettlementOrder/GetByOrderId";
        return PostAsync<SettlementOrder.GetPayableOrderDetailInput, IEnumerable<SettlementOrder.GetPayableOrderDetailOutput>>(relativePath, input);
    }

    public Task<List<SettlementOrder.SettlementOrderTransferRecordOutput>> SearchSettlementOrderTransferRecordByIds(IEnumerable<long> ids)
    {
        var relativePath = "/SettlementOrder/SearchTransferRecordByIds";
        return PostAsync<IEnumerable<long>, List<SettlementOrder.SettlementOrderTransferRecordOutput>>(relativePath, ids);
    }

    #endregion

    public Task<IEnumerable<GetOffsetOrderListOutput>> GetOffsetOrderList(GetOffsetOrderListInput input)
    {
        var relativePath = "/OffsetOrder/GetList";
        return PostAsync<GetOffsetOrderListInput, IEnumerable<GetOffsetOrderListOutput>>(relativePath, input);
    }

    public Task<PagingModel<SearchOffsetOrderOutput>> SearchOffsetOrder(SearchOffsetOrderInput input)
    {
        var relativePath = "/OffsetOrder/Search";
        return PostAsync<SearchOffsetOrderInput, PagingModel<SearchOffsetOrderOutput>>(relativePath, input);
    }

    public async Task<IEnumerable<SearchOffsetOrderOutput>> ExportSearchOffsetOrder(SearchOffsetOrderInput input)
    {
        var relativePath = "/OffsetOrder/ExportSearch";
        return await PostAsync<SearchOffsetOrderInput, IEnumerable<SearchOffsetOrderOutput>>(relativePath, input);
    }

    public async Task<GetOffsetOrderDetailOutput> OffsetOrderDetail(long id)
    {
        var relativePath = "/OffsetOrder/Detail?id=" + id;
        return await GetAsync<GetOffsetOrderDetailOutput>(relativePath);
    }

    public Task<long> AddOffsetOrder(AddOffsetOrderInput input)
    {
        var relativePath = "/OffsetOrder/Add";
        return PostAsync<AddOffsetOrderInput, long>(relativePath, input);
    }

    #region ReceiptSettlementOrder

    /// <summary>
    /// 通过订单id查询应收结算单数据
    /// </summary>
    /// <param name="orderId"></param>
    /// <returns></returns>
    public Task<IEnumerable<GetReceivableOrderDetailOutput>> GetReceivableOrderDetailByOrderId(GetReceivableOrderDetailInput input)
    {
        var relativePath = "/ReceiptSettlementOrder/GetByOrderId";
        return PostAsync<GetReceivableOrderDetailInput, IEnumerable<GetReceivableOrderDetailOutput>>(relativePath, input);
    }

    public async Task<PagingModel<Contracts.Common.Order.DTOs.ReceiptSettlementOrder.SearchOutput>>
        SearchReceiptSettlementOrder(SearchReceiptSettlementOrderInput input)
    {
        var relativePath = "/ReceiptSettlementOrder/Search";
        return await PostAsync<SearchReceiptSettlementOrderInput,
            PagingModel<Contracts.Common.Order.DTOs.ReceiptSettlementOrder.SearchOutput>>(relativePath, input);
    }

    public async Task<ExportDetailOutput> ExportReceiptSettlementOrderDetail(ExportDetailInput input)
    {
        var relativePath = "/ReceiptSettlementOrder/Export";
        return await PostAsync<ExportDetailInput, ExportDetailOutput>(relativePath, input);
    }

    public async Task<IEnumerable<ExportOrderOutput>> ExportReceiptSettlementOrder(ExportOrderInput input)
    {
        var relativePath = "/ReceiptSettlementOrder/ExportOrder";
        return await PostAsync<ExportOrderInput, IEnumerable<ExportOrderOutput>>(relativePath, input);
    }

    public async Task<IEnumerable<SearchDetailExportOutput>> SearchExportReceiptSettlementOrderDetail(SearchDetailExportInput input)
    {
        var relativePath = "/ReceiptSettlementOrder/SearchDetailExport";
        return await PostAsync<SearchDetailExportInput, IEnumerable<SearchDetailExportOutput>>(relativePath, input);
    }

    public async Task<IEnumerable<GetReceiptSettlementOrderRecordsOutput>> GetReceiptSettlementOrderRecords(GetReceiptSettlementOrderRecordsInput input)
    {
        var relativePath = "/ReceiptSettlementOrder/GetReceiptSettlementOrderRecords";
        return await PostAsync<GetReceiptSettlementOrderRecordsInput, IEnumerable<GetReceiptSettlementOrderRecordsOutput>>(relativePath, input);
    }

    public async Task<long> AddReceiptSettlementOrderRecord(AddReceiptSettlementOrderRecordInput input)
    {
        var relativePath = "/ReceiptSettlementOrder/AddReceiptSettlementOrderRecord";
        return await PostAsync<AddReceiptSettlementOrderRecordInput, long>(relativePath, input);
    }

    public async Task ReceiptSettlementOrder(ReceiptInput input)
    {
        var relativePath = "/ReceiptSettlementOrder/Receipt";
        await PostAsync<ReceiptInput>(relativePath, input);
    }

    public async Task<long> UpdateReceiptOrderSetting(UpdateReceiptOrderSettingInput input)
    {
        var relativePath = "/ReceiptOrder/UpdateSetting";
        return await PostAsync<UpdateReceiptOrderSettingInput, long>(relativePath, input);
    }
    public async Task<ReceiptOrderSettingOutput> GetReceiptOrderSetting()
    {
        var relativePath = "/ReceiptOrder/GetSetting";
        return await GetAsync<ReceiptOrderSettingOutput>(relativePath);
    }

    public async Task<PagingModel<UnReceiptByEarlyWarningOutput>> UnReceiptByEarlyWarning(UnReceiptByEarlyWarningInput input)
    {
        var relativePath = "/ReceiptSettlementOrder/UnReceiptByEarlyWarning";
        return await PostAsync<UnReceiptByEarlyWarningInput, PagingModel<UnReceiptByEarlyWarningOutput>>(relativePath, input);
    }

    public async Task<PagingModel<UnReceiptDetailsByEarlyWarningOutput>> UnReceiptDetailsByEarlyWarning(UnReceiptDetailsByEarlyWarningInput input)
    {
        var relativePath = "/ReceiptSettlementOrder/UnReceiptDetailsByEarlyWarning";
        return await PostAsync<UnReceiptDetailsByEarlyWarningInput, PagingModel<UnReceiptDetailsByEarlyWarningOutput>>(relativePath, input);
    }
    #endregion

    #region ScenicTicketPurchaseOrder

    public async Task CreateScenicTicketPurchaseOrder(CreateTicketPurchaseOrderInput input)
    {
        var relativePath = "/ScenicTicketPurchaseOrder/Create";
        await PostAsync(relativePath, input);
    }

    public async Task EditScenicTicketPurchaseOrder(EditTicketPurchaseOrderInput input)
    {
        var relativePath = "/ScenicTicketPurchaseOrder/Edit";
        await PostAsync(relativePath, input);
    }

    public async Task<List<GetTicketPurchaseOrderDetailOutput>> ScenicTicketPurchaseOrderDetails(params long[] purchaseOrderIds)
    {
        var relativePath = "/ScenicTicketPurchaseOrder/Details";
        return await PostAsync<long[], List<GetTicketPurchaseOrderDetailOutput>>(relativePath, purchaseOrderIds);
    }

    public async Task<PagingModel<SearchTicketPurchaseOrderOutput>> SearchScenicTicketPurchaseOrder(SearchTicketPurchaseOrderInput input)
    {
        var relativePath = "/ScenicTicketPurchaseOrder/Search";
        return await PostAsync<SearchTicketPurchaseOrderInput, PagingModel<SearchTicketPurchaseOrderOutput>>(
            relativePath, input);
    }

    public async Task<List<ExportTicketPurchaseOutput>> ExportScenicTicketPurchaseOrder(long purchaseOrderId)
    {
        var relativePath = "/ScenicTicketPurchaseOrder/Export";
        return await PostAsync<long, List<ExportTicketPurchaseOutput>>(relativePath, purchaseOrderId);
    }

    public async Task<GetScenicTicketPurchaseInventoryOutput> GetScenicTicketPurchaseInventory(
        GetScenicTicketPurchaseInventoryInput input)
    {
        var relativePath = "/ScenicTicketPurchaseOrder/GetPurchaseInventory";
        return await PostAsync<GetScenicTicketPurchaseInventoryInput, GetScenicTicketPurchaseInventoryOutput>(relativePath, input);
    }

    public async Task<ScenicTicketOrderDeliveryOutput> RetryOrderDelivery(ScenicTicketOrderDeliveryInput input)
    {
        var relativePath = "/ScenicTicketPurchaseOrder/RetryOrderDelivery";
        return await PostAsync<ScenicTicketOrderDeliveryInput, ScenicTicketOrderDeliveryOutput>(relativePath, input);
    }

    public async Task<List<ExportPurchaseOrderListOutput>> ExportPurchaseOrderList(ExportPurchaseOrderListInput input)
    {
        var relativePath = "/ScenicTicketPurchaseOrder/ExportOrderList";
        return await PostAsync<ExportPurchaseOrderListInput, List<ExportPurchaseOrderListOutput>>(relativePath, input);
    }

    public async Task OutboundScenicTicketPurchaseOrder(OutboundPurchaseOrderInput input)
    {
        var relativePath = "/ScenicTicketPurchaseOrder/Outbound";
        await PostAsync(relativePath, input);
    }

    public async Task<List<GetOutboundPurchaseVoucherOutput>> GetScenicTicketPurchaseOutboundVouchers(params long[] purchaseOrderIds)
    {
        var relativePath = "/ScenicTicketPurchaseOrder/GetOutboundVouchers";
        return await PostAsync<long[], List<GetOutboundPurchaseVoucherOutput>>(relativePath, purchaseOrderIds);
    }

    public async Task<List<BatchCreateParsePurchaseFileDataOutput>> ParsePurchaseFileData(
        BatchCreateParsePurchaseFileDataInput input)
    {
        var relativePath = "/ScenicTicketPurchaseOrder/ParsePurchaseFileData";
        return await PostAsync<BatchCreateParsePurchaseFileDataInput, List<BatchCreateParsePurchaseFileDataOutput>>(
            relativePath, input);
    }

    public async Task<List<long>> BatchCreateTicketPurchaseOrder(BatchCreateTicketPurchaseOrderInput input)
    {
        var relativePath = "/ScenicTicketPurchaseOrder/BatchCreate";
        return await PostAsync<BatchCreateTicketPurchaseOrderInput, List<long>>(relativePath, input);
    }

    public async Task<TicketPurchaseInvCheckOutput> TicketPurchaseInventoryCheck(TicketPurchaseInvCheckInput input)
    {
        var relativePath = "/ScenicTicketPurchaseOrder/InventoryCheck";
        return await PostAsync<TicketPurchaseInvCheckInput, TicketPurchaseInvCheckOutput>(relativePath, input);
    }

    public async Task<PagingModel<SearchTicketPurchaseInvCheckRecordOutput>> SearchTicketPurchaseInventoryCheckRecord(
        SearchTicketPurchaseInvCheckRecordInput input)
    {
        var relativePath = "/ScenicTicketPurchaseOrder/SearchInventoryCheckRecord";
        return await PostAsync<SearchTicketPurchaseInvCheckRecordInput,
            PagingModel<SearchTicketPurchaseInvCheckRecordOutput>>(relativePath, input);
    }

    public async Task<PagingModel<SearchPurchaseConsumptionOutput>> SearchPurchaseConsumption(
        SearchPurchaseConsumptionInput input)
    {
        var relativePath = "/ScenicTicketPurchaseOrder/SearchPurchaseConsumption";
        return await PostAsync<SearchPurchaseConsumptionInput, PagingModel<SearchPurchaseConsumptionOutput>>(
            relativePath, input);
    }

    #endregion

    #region ScenicTicketOrder

    public async Task<CreateScenicOTAOrderOutput> CreateScenicOTAOrder(CreateScenicOTAOrderInput input)
    {
        var relativePath = "/ScenicTicketOTAOrder/Create";
        return await PostAsync<CreateScenicOTAOrderInput, CreateScenicOTAOrderOutput>(relativePath, input);
    }

    public async Task<CreateScenicOTACombinationOrderOutput> CreateScenicCombinationOTAOrderCreate(
        CreateScenicOTACombinationOrderInput input)
    {
        var relativePath = "/ScenicTicketOTAOrder/CombinationOrderCreate";
        return await PostAsync<CreateScenicOTACombinationOrderInput, CreateScenicOTACombinationOrderOutput>(
            relativePath, input);
    }

    public async Task ScenicOTAFinishByManual(OTAOrderManualDeliveryInput input)
    {
        var relativePath = "/ScenicTicketOTAOrder/FinishByManual";
        await PostAsync<OTAOrderManualDeliveryInput>(relativePath, input);
    }

    public async Task<ScenicTicketOrderDetailOutput> ScenicTicketOrderDetail(ScenicTicketOrderDetailInput input)
    {
        var relativePath = "/ScenicTicketOrder/Detail";
        return await PostAsync<ScenicTicketOrderDetailInput, ScenicTicketOrderDetailOutput>(relativePath, input);
    }

    public async Task<PagingModel<SearchScenicTicketOrderOutput, ScenicTicketOrderStatusCount>> SearchScenicTicketOrder(SearchScenicTicketOrderInput input)
    {
        var relativePath = "/ScenicTicketOrder/Search";
        return await PostAsync<SearchScenicTicketOrderInput, PagingModel<SearchScenicTicketOrderOutput, ScenicTicketOrderStatusCount>>(relativePath, input);
    }

    public async Task<ScenicTicketOrderStatusCount> SearchScenicTicketOrderStatusCount(SearchScenicTicketOrderInput input)
    {
        var relativePath = "/ScenicTicketOrder/SearchOrderStatusCount";
        return await PostAsync<SearchScenicTicketOrderInput, ScenicTicketOrderStatusCount>(relativePath, input);
    }

    public async Task ScenicTicketVoucherManualAdd(ManualAddVoucherInput input)
    {
        var relativePath = "/ScenicTicketVoucher/ManualAdd";
        await PostAsync(relativePath, input);
    }

    public async Task<ManualDeliveryVoucherOutput> ScenicTicketVoucherManualDelivery(ManualDeliveryScenicTicketVoucherInput input)
    {
        var relativePath = "/ScenicTicketVoucher/ManualDelivery";
        return await PostAsync<ManualDeliveryScenicTicketVoucherInput, ManualDeliveryVoucherOutput>(relativePath, input);
    }

    public async Task<GetChannelOrderInfoOutput> GetScenicTicketChannelOrderInfo(string channelOrderNo)
    {
        var relativePath = $"/ScenicTicketOrder/GetChannelOrderInfo?channelOrderNo={channelOrderNo}";
        return await GetAsync<GetChannelOrderInfoOutput>(relativePath);
    }

    public Task UpdateScenicTicketSupplierOrderId(UpdateSupplierOrderIdInput input)
    {
        var relativePath = "/ScenicTicketOrder/UpdateSupplierOrderId";
        return PostAsync(relativePath, input);
    }

    public async Task EditScenicTicketOrderCost(UpdateCostInput input)
    {
        var relativePath = "/ScenicTicketOrder/EditCost";
        await PostAsync<UpdateCostInput>(relativePath, input);
    }

    public async Task<CreateScenicTicketOrderOutput> CreateScenicTicketOrderByManual(Contracts.Common.Order.DTOs.ScenicTicketOrder.CreateByManualInput input)
    {
        var relativePath = "/ScenicTicketOrder/CreateByManual";
        return await PostAsync<Contracts.Common.Order.DTOs.ScenicTicketOrder.CreateByManualInput, CreateScenicTicketOrderOutput>(relativePath, input);
    }

    public async Task<bool> UpdateScenicTicketTravelInfo(Contracts.Common.Order.DTOs.ScenicTicketOrder.UpdateTravelInfoInput input)
    {
        var relativePath = "/ScenicTicketOrder/UpdateTravelInfo";
        return await PostAsync<Contracts.Common.Order.DTOs.ScenicTicketOrder.UpdateTravelInfoInput, bool>(relativePath, input);
    }

    public async Task<bool> UpdateScenicTicketOrderConfirmation(Contracts.Common.Order.DTOs.ScenicTicketOrder.UpdateOrderConfirmationInput input)
    {
        var relativePath = "/ScenicTicketOrder/UpdateOrderConfirmation";
        return await PostAsync<Contracts.Common.Order.DTOs.ScenicTicketOrder.UpdateOrderConfirmationInput, bool>(relativePath, input);
    }

    public async Task<bool> UpdateScenicTicketOrderContact(UpdateContactInput input)
    {
        var relativePath = "/ScenicTicketOrder/UpdateContact";
        return await PostAsync<UpdateContactInput, bool>(relativePath, input);
    }

    public async Task<List<GetScenicOrderSimpleInfoOutput>> GetScenicTicketOrderSimpleInfo(params long[] baseOrderIds)
    {
        var relativePath = $"/ScenicTicketOrder/GetOrderSimpleInfo";
        return await PostAsync<long[], List<GetScenicOrderSimpleInfoOutput>>(relativePath, baseOrderIds);
    }

    public async Task UpdateScenicTicketCostDiscountRate(UpdateCostDiscountRateInput input)
    {
        var relativePath = "/ScenicTicketOrder/UpdateCostDiscountRate";
        await PostAsync(relativePath, input);
    }

    public async Task UpdateScenicTicketTravelTime(UpdateTravelTimeInput input)
    {
        var relativePath = "/ScenicTicketOrder/UpdateTravelTime";
        await PostAsync(relativePath, input);
    }

    public async Task ReplaceScenicTicketOrderProduct(Contracts.Common.Order.DTOs.ScenicTicketOrder.ReplaceOrderProductInput input)
    {
        var relativePath = "/ScenicTicketOrder/ReplaceOrderProduct";
        await PostAsync(relativePath, input);
    }

    public async Task<ScenicTicketOrderSendEmailSyncOutput> ScenicTicketOrderSendEmailSync(
        ScenicTicketOrderSendEmailSyncInput input)
    {
        var relativePath = "/ScenicTicketOrder/SendEmailSync";
        return await PostAsync<ScenicTicketOrderSendEmailSyncInput, ScenicTicketOrderSendEmailSyncOutput>(relativePath, input);
    }

    #endregion

    #region ReceiptOrder

    public async Task<IEnumerable<PreCreateOutput>> ReceiptSettlementOrderPreCreate(PreCreateInput input)
    {
        var relativePath = "/ReceiptOrder/PreCreate";
        return await PostAsync<PreCreateInput, IEnumerable<PreCreateOutput>>(relativePath, input);
    }

    public async Task<ReceiptSettlementOrderCreateOutput> ReceiptSettlementOrderCreate(PreCreateInput input)
    {
        var relativePath = "/ReceiptOrder/Create";
        return await PostAsync<PreCreateInput, ReceiptSettlementOrderCreateOutput>(relativePath, input);
    }

    public async Task SendReceiptSettlementOrderCreateEmail(List<ExportDetailOutput> attachmentsData)
    {
        var relativePath = "/ReceiptOrder/SendCreateEmail";
        await PostAsync(relativePath, attachmentsData);
    }

    public async Task<PagingModel<ReceivablesHotelOrderOutput>> ReceivablesHotelOrder(ReceivablesSearchBffInput input)
    {
        var relativePath = "/ReceiptOrder/HotelOrder";
        return await PostAsync<ReceivablesSearchBffInput, PagingModel<ReceivablesHotelOrderOutput>>(relativePath, input);
    }

    public async Task<PagingModel<ReceivablesTicketOrderOutput>> ReceivablesTicketOrder(ReceivablesSearchBffInput input)
    {
        var relativePath = "/ReceiptOrder/TicketOrder";
        return await PostAsync<ReceivablesSearchBffInput, PagingModel<ReceivablesTicketOrderOutput>>(relativePath, input);
    }

    public async Task<PagingModel<ReceivablesReservationOrderOutput>> ReceivablesReservationOrder(ReceivablesSearchBffInput input)
    {
        var relativePath = "/ReceiptOrder/ReservationOrder";
        return await PostAsync<ReceivablesSearchBffInput, PagingModel<ReceivablesReservationOrderOutput>>(relativePath, input);
    }

    public async Task<PagingModel<ReceivablesScenicOrderOutput>> ReceivablesScenicOrder(ReceivablesSearchBffInput input)
    {
        var relativePath = "/ReceiptOrder/ScenicOrder";
        return await PostAsync<ReceivablesSearchBffInput, PagingModel<ReceivablesScenicOrderOutput>>(relativePath, input);
    }

    public async Task<PagingModel<ReceivablesLineOrderOutput>> ReceivablesLineOrder(ReceivablesSearchBffInput input)
    {
        var relativePath = "/ReceiptOrder/LineOrder";
        return await PostAsync<ReceivablesSearchBffInput, PagingModel<ReceivablesLineOrderOutput>>(relativePath, input);
    }

    public async Task<PagingModel<ReceivablesCarProductOrderOutput>> ReceivablesCarProductOrder(ReceivablesSearchBffInput input)
    {
        var relativePath = "/ReceiptOrder/CarProductOrder";
        return await PostAsync<ReceivablesSearchBffInput, PagingModel<ReceivablesCarProductOrderOutput>>(relativePath, input);
    }

    public async Task<PagingModel<ReceivablesRefundOrderOutput>> ReceivablesRefundOrder(ReceivablesSearchBffInput input)
    {
        var relativePath = "/ReceiptOrder/RefundOrder";
        return await PostAsync<ReceivablesSearchBffInput, PagingModel<ReceivablesRefundOrderOutput>>(relativePath, input);
    }

    public async Task<PagingModel<ReceivablesRefundOrderOutput>> ReceivablesRelatedRefundOrder(RelatedReceivablesSearchInput input)
    {
        var relativePath = "/ReceiptOrder/RelatedRefundOrder";
        return await PostAsync<RelatedReceivablesSearchInput, PagingModel<ReceivablesRefundOrderOutput>>(relativePath, input);
    }

    public async Task<PagingModel<ReceivablesOffsetOrderOutput>> ReceivablesOffsetOrder(ReceivablesSearchBffInput input)
    {
        var relativePath = "/ReceiptOrder/OffsetOrder";
        return await PostAsync<ReceivablesSearchBffInput, PagingModel<ReceivablesOffsetOrderOutput>>(relativePath, input);
    }

    public async Task<PagingModel<ReceivablesOffsetOrderOutput>> ReceivablesRelatedOffsetOrder(RelatedReceivablesSearchInput input)
    {
        var relativePath = "/ReceiptOrder/RelatedOffsetOrder";
        return await PostAsync<RelatedReceivablesSearchInput, PagingModel<ReceivablesOffsetOrderOutput>>(relativePath, input);
    }

    public async Task<ReceivablesOrderExportOutput> ExportReceivablesOrder(ReceivablesOrderExportInput input)
    {
        var relativePath = "/ReceiptOrder/Export";
        return await PostAsync<ReceivablesOrderExportInput, ReceivablesOrderExportOutput>(relativePath, input);
    }

    public async Task<byte[]> ExportAutoReconciliationTemplate()
    {
        var relativePath = "/ReceiptOrder/ExportTemplate";
        return await GetAsync<byte[]>(relativePath);
    }
    public async Task<AutoReconciliationOutput> AutoReconciliationPre(AutoReconciliationInput input)
    {
        var relativePath = "/ReceiptOrder/AutoReconciliationPre";
        return await PostAsync<AutoReconciliationInput, AutoReconciliationOutput>(relativePath, input);
    }
    #endregion


    #region HotelOrder

    public async Task<HopHotelOrderUpdatedDto> GetHotelHopUpdatedInfo(HopHotelOrderUpdatedInput input)
    {
        var relativePath = "/HotelOrder/GetHotelHopUpdatedInfo";
        return await PostAsync<HopHotelOrderUpdatedInput, HopHotelOrderUpdatedDto>(relativePath, input);
    }

    public Task<CreateHotelOrderOutput> HotelOrderCreate(CreateHotelOrderInput input)
    {
        var relativePath = "/HotelOrder/Create";
        return PostAsync<CreateHotelOrderInput, CreateHotelOrderOutput>(relativePath, input);
    }

    /// <summary>
    /// 日历房订单可退款信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public Task<GetRefundableDto> GetHotelOrderRefundable(GetRefundableInput input)
    {
        var relativePath = "/HotelOrder/GetRefundable";
        return PostAsync<GetRefundableInput, GetRefundableDto>(relativePath, input);
    }

    public async Task<Contracts.Common.Order.DTOs.HotelOrder.DetailOutput> HotelOrderDetail(DetailInput input)
    {
        var relativePath = "/HotelOrder/Detail";
        return await PostAsync<DetailInput, Contracts.Common.Order.DTOs.HotelOrder.DetailOutput>(relativePath, input);
    }

    public Task UpdateHotelOrderSupplierOrderId(UpdateSupplierOrderIdInput input)
    {
        var relativePath = "/HotelOrder/UpdateSupplierOrderId";
        return PostAsync(relativePath, input);
    }

    public async Task<byte[]> ExportHotelOrderPdf(Contracts.Common.Order.DTOs.ExportPdf.ExportHotelOrderPdfInput input)
    {
        var relativePath = "/HotelOrder/ExportHotelOrderPdf";
        return await PostAsync<Contracts.Common.Order.DTOs.ExportPdf.ExportHotelOrderPdfInput, byte[]>(relativePath, input);
    }

    public async Task<byte[]> ExportHotelGroupBookingOrderPdf(Contracts.Common.Order.DTOs.ExportPdf.ExportGroupBookingOrderPdfInput input)
    {
        var relativePath = "/HotelGroupBookingOrder/ExportPdf";
        return await PostAsync<Contracts.Common.Order.DTOs.ExportPdf.ExportGroupBookingOrderPdfInput, byte[]>(relativePath, input);
    }

    public Task<PagingModel<Contracts.Common.Order.DTOs.HotelOrder.SearchOutput, IList<HotelStatusCountOutput>>> HotelOrderSearch(
        Contracts.Common.Order.DTOs.HotelOrder.SearchInput input)
    {
        var relativePath = "/HotelOrder/Search";
        return PostAsync<Contracts.Common.Order.DTOs.HotelOrder.SearchInput, PagingModel<
            Contracts.Common.Order.DTOs.HotelOrder.SearchOutput, IList<HotelStatusCountOutput>>>(relativePath, input);
    }

    #endregion

    #region WorkOrder

    public async Task<PagingModel<SearchWorkOrderDto>> SearchWorkOrder(Contracts.Common.Order.DTOs.WorkOrder.SearchInput input)
    {
        var relativePath = $"/WorkOrder/Search";
        return await PostAsync<Contracts.Common.Order.DTOs.WorkOrder.SearchInput, PagingModel<SearchWorkOrderDto>>(relativePath, input);
    }

    public async Task<Contracts.Common.Order.DTOs.WorkOrder.DetailOutput> WorkOrderDetail(long id)
    {
        var relativePath = $"/WorkOrder/Detail?id={id}";
        return await GetAsync<Contracts.Common.Order.DTOs.WorkOrder.DetailOutput>(relativePath);
    }

    public async Task<HopWorkOrderNotifyOutput> UpdateHopWorkOrderNotify(UpdateHopWorkOrderNotifyInput input)
    {
        var relativePath = $"/WorkOrder/UpdateHopWorkOrderNotify";
        return await PostAsync<UpdateHopWorkOrderNotifyInput, HopWorkOrderNotifyOutput>(relativePath, input);
    }

    public async Task<HopWorkOrderNotifyOutput> DeleteHopWorkOrderReplayNotify(DeleteHopWorkOrderReplayNotifyInput input)
    {
        var relativePath = $"/WorkOrder/DeleteHopWorkOrderReplayNotify";
        return await PostAsync<DeleteHopWorkOrderReplayNotifyInput, HopWorkOrderNotifyOutput>(relativePath, input);
    }

    public async Task<HopWorkOrderNotifyOutput> HopWorkOrderReplayNotify(HopWorkOrderReplayNotifyInput input)
    {
        var relativePath = $"/WorkOrder/HopWorkOrderReplayNotify";
        return await PostAsync<HopWorkOrderReplayNotifyInput, HopWorkOrderNotifyOutput>(relativePath, input);
    }
    #endregion

    #region WorkOrderServiceEvaluation
    public async Task<List<GetOutput>> GetWorkOrderServiceEvaluationList(GetListInput input)
    {
        var relativePath = $"/WorkOrderServiceEvaluation/GetList";
        return await PostAsync<GetListInput, List<GetOutput>>(relativePath, input);
    }
    #endregion

    #region SettlementPayables

    public async Task<PagingModel<SettlementOrder.PayablesHotelOrderInfo>> SettlementPayablesHotelOrder(PayablesSearchBffInput input)
    {
        var relativePath = "/SettlementPayables/HotelOrder";
        return await
            PostAsync<PayablesSearchBffInput, PagingModel<SettlementOrder.PayablesHotelOrderInfo>>(
                relativePath, input);
    }

    public async Task<PagingModel<SettlementOrder.PayablesMailOrderInfo>> SettlementPayablesMailOrder(PayablesSearchBffInput input)
    {
        var relativePath = "/SettlementPayables/MailOrder";
        return await
            PostAsync<PayablesSearchBffInput, PagingModel<SettlementOrder.PayablesMailOrderInfo>>(
                relativePath, input);
    }

    public async Task<PagingModel<SettlementOrder.PayablesTicketOrderInfo>> SettlementPayablesTicketOrder(PayablesSearchBffInput input)
    {
        var relativePath = "/SettlementPayables/TicketOrder";
        return await
            PostAsync<PayablesSearchBffInput, PagingModel<SettlementOrder.PayablesTicketOrderInfo>>(
                relativePath, input);
    }

    public async Task<PagingModel<SettlementOrder.PayablesReservationOrderInfo>> SettlementPayablesReservationOrder(PayablesSearchBffInput input)
    {
        var relativePath = "/SettlementPayables/ReservationOrder";
        return await
            PostAsync<PayablesSearchBffInput, PagingModel<SettlementOrder.PayablesReservationOrderInfo>>(
                relativePath, input);
    }

    public async Task<PagingModel<SettlementOrder.PayablesRefundOrderInfo>> SettlementPayablesRefundOrder(PayablesSearchBffInput input)
    {
        var relativePath = "/SettlementPayables/RefundOrder";
        return await
            PostAsync<PayablesSearchBffInput, PagingModel<SettlementOrder.PayablesRefundOrderInfo>>(
                relativePath, input);
    }

    public async Task<PagingModel<SettlementOrder.PayablesScenicTicketOrderInfo>> SettlementPayablesScenicTicketOrder(PayablesSearchBffInput input)
    {
        var relativePath = "/SettlementPayables/ScenicTicketOrder";
        return await
            PostAsync<PayablesSearchBffInput, PagingModel<SettlementOrder.PayablesScenicTicketOrderInfo>>(
                relativePath, input);
    }

    public async Task<PagingModel<SettlementOrder.PayablesLineOrderInfo>> SettlementPayablesLineOrder(PayablesSearchBffInput input)
    {
        var relativePath = "/SettlementPayables/LineOrder";
        return await
            PostAsync<PayablesSearchBffInput, PagingModel<SettlementOrder.PayablesLineOrderInfo>>(
                relativePath, input);
    }

    public async Task<PagingModel<SettlementOrder.PayablesCarProductOrderInfo>> SettlementPayablesCarProductOrder(PayablesSearchBffInput input)
    {
        var relativePath = "/SettlementPayables/CarProductOrder";
        return await
            PostAsync<PayablesSearchBffInput, PagingModel<SettlementOrder.PayablesCarProductOrderInfo>>(
                relativePath, input);
    }

    public async Task<PagingModel<SettlementOrder.PayableOffsetOrderInfo>> SettlementPayablesOffsetOrder(PayablesSearchBffInput input)
    {
        var relativePath = "/SettlementPayables/OffsetOrder";
        return await
            PostAsync<PayablesSearchBffInput, PagingModel<SettlementOrder.PayableOffsetOrderInfo>>(
                relativePath, input);
    }

    public async Task<IEnumerable<Contracts.Common.Order.DTOs.SettlementOrder.PreCreateOutput>>
        SettlementPayablesPreCreate(Contracts.Common.Order.DTOs.SettlementOrder.PreCreateInput input)
    {
        var relativePath = "/SettlementPayables/PreCreate";
        return await
            PostAsync<Contracts.Common.Order.DTOs.SettlementOrder.PreCreateInput,
                IEnumerable<Contracts.Common.Order.DTOs.SettlementOrder.PreCreateOutput>>(relativePath, input);
    }

    public async Task<bool> SettlementPayablesCreate(Contracts.Common.Order.DTOs.SettlementOrder.PreCreateInput input)
    {
        var relativePath = "/SettlementPayables/Create";
        return await PostAsync<Contracts.Common.Order.DTOs.SettlementOrder.PreCreateInput, bool>(relativePath, input);
    }

    public async Task<PagingModel<SettlementOrder.PayablesRefundOrderInfo>> SettlementPayablesRelatedRefundOrder(
        SettlementOrder.RelatedPayablesSearchInput input)
    {
        var relativePath = "/SettlementPayables/RelatedRefundOrder";
        return await
            PostAsync<SettlementOrder.RelatedPayablesSearchInput, PagingModel<SettlementOrder.PayablesRefundOrderInfo>>(
                relativePath, input);
    }

    public async Task<PagingModel<SettlementOrder.PayableOffsetOrderInfo>> SettlementPayablesRelatedOffsetOrder(
        SettlementOrder.RelatedPayablesSearchInput input)
    {
        var relativePath = "/SettlementPayables/RelatedOffsetOrder";
        return await
            PostAsync<SettlementOrder.RelatedPayablesSearchInput, PagingModel<SettlementOrder.PayableOffsetOrderInfo>>(
                relativePath, input);
    }

    public async Task<SettlementOrder.PayablesOrderExportOutput> SettlementPayablesOrderExport(SettlementOrder.PayablesOrderExportInput input)
    {
        var relativePath = "/SettlementPayables/Export";
        return await PostAsync<SettlementOrder.PayablesOrderExportInput, SettlementOrder.PayablesOrderExportOutput>(relativePath, input);
    }

    public async Task<AutoPreCreateSettlementPayablesOutput> AutoPreCreateSettlementPayables(AutoSettlementPayablesInput input)
    {
        var relativePath = "/SettlementPayables/AutoPreCreate";
        return await PostAsync<AutoSettlementPayablesInput, AutoPreCreateSettlementPayablesOutput>(relativePath, input);
    }

    #endregion

    #region KlookOrder

    public async Task<GetKlookOrderOutput> KlookOrderDetail(GetKlookOrderInput input)
    {
        var relativePath = "/KlookOrder/Detail";
        return await PostAsync<GetKlookOrderInput, GetKlookOrderOutput>(relativePath, input);
    }

    public async Task<KlookOrderDeliveryOutput> KlookOrderDelivery(KlookOrderDeliveryInput input)
    {
        var relativePath = "/KlookOrder/Delivery";
        return await PostAsync<KlookOrderDeliveryInput, KlookOrderDeliveryOutput>(relativePath, input);
    }

    public async Task<KlookOrderRetryPayOutput> KlookOrderRetryPay(KlookOrderRetryPay input)
    {
        var relativePath = "/KlookOrder/RetryPay";
        return await PostAsync<KlookOrderRetryPay, KlookOrderRetryPayOutput>(relativePath, input);
    }

    public async Task<KlookOrderCallBackNotifyOutput> KlookOrderNotify(KlookOrderCallBackNotifyInput input)
    {
        var relativePath = "/KlookOrder/Notify";
        return await PostAsync<KlookOrderCallBackNotifyInput, KlookOrderCallBackNotifyOutput>(relativePath, input);
    }

    #endregion

    #region TicketsCombinationOrder
    public async Task<PagingModel<SearchTicketsCombinationOrderOutput, CombinationOrderStatistics>> SearchTicketsCombinationOrder(SearchTicketsCombinationOrderInput input)
    {
        var relativePath = "/TicketsCombinationOrder/Search";
        return await PostAsync<SearchTicketsCombinationOrderInput, PagingModel<SearchTicketsCombinationOrderOutput, CombinationOrderStatistics>>(relativePath, input);
    }

    public async Task CreateTicketsCombinationOrder(CreateTicketsCombinationOrderInput input)
    {
        var relativePath = "/TicketsCombinationOrder/Create";
        await PostAsync(relativePath, input);
    }

    public async Task ReplaceTicketsCombinationOrder(ReplaceTicketsOrderInput input)
    {
        var relativePath = "/TicketsCombinationOrder/ReplaceTicketsOrder";
        await PostAsync(relativePath, input);
    }

    public async Task<CombinationOrderDeliveryOutput> TicketsCombinationOrderDelivery(long id)
    {
        var relativePath = $"/TicketsCombinationOrder/Delivery?id={id}";
        return await PostAsync<long, CombinationOrderDeliveryOutput>(relativePath, id);
    }

    public async Task<bool> TicketsCombinationOrderPaymentAmountCheck(long orderId)
    {
        var relativePath = $"/TicketsCombinationOrder/PaymentAmountCheck?orderId={orderId}";
        return await GetAsync<bool>(relativePath);
    }

    public Task AddTicketsCombinationAbnormalOrder(AddAbnormalCombinationOrderInput input)
    {
        var relativePath = "/TicketsCombinationOrder/AddAbnormalOrder";
        return PostAsync(relativePath, input);
    }

    public async Task UpdateTicketsCombinationAbnormalOrder(UpdateAbnormalCombinationOrderInput input)
    {
        var relativePath = "/TicketsCombinationOrder/UpdateAbnormalOrder";
        await PostAsync(relativePath, input);
    }

    public async Task<GetAbnormalCombinationOrderDetailOutPut> GetTicketsCombinationAbnormalOrderDetail(long orderId)
    {
        var relativePath = $"/TicketsCombinationOrder/GetAbnormalOrderDetail?orderId={orderId}";
        return await GetAsync<GetAbnormalCombinationOrderDetailOutPut>(relativePath);
    }

    public async Task CloseTicketsCombinationAbnormalOrder(CloseAbnormalCombinationOrderInput input)
    {
        var relativePath = $"/TicketsCombinationOrder/CloseAbnormalOrder";
        await PostAsync(relativePath, input);
    }

    public async Task ReplaceTicketCombination(ReplaceTicketCombinationInput input)
    {
        var relativePath = "/TicketsCombinationOrder/ReplaceTicketCombination";
        await PostAsync(relativePath, input);
    }

    public async Task<CreateCombinationOrderOutput> CreateCombinationOrder(CreateCombinationOrderInput input)
    {
        var relativePath = "/TicketsCombinationOrder/CreateOrder";
        return await PostAsync<CreateCombinationOrderInput, CreateCombinationOrderOutput>(relativePath, input);
    }

    #endregion

    #region TicketsCombinationOrderAfterSale

    public async Task<List<AddCombinationOrderAfterSaleOutput>> CombinationOrderRefund(
        AddCombinationOrderAfterSaleInput input)
    {
        var relativePath = "/TicketsCombinationOrderAfterSale/Refund";
        return await PostAsync<AddCombinationOrderAfterSaleInput, List<AddCombinationOrderAfterSaleOutput>>(relativePath, input);
    }

    public async Task<List<GetCombinationOrderRefundableOutput>> GetCombinationOrderRefundable(
        GetCombinationOrderRefundableInput input)
    {
        var relativePath = "/TicketsCombinationOrderAfterSale/OrderRefundable";
        return await PostAsync<GetCombinationOrderRefundableInput, List<GetCombinationOrderRefundableOutput>>(relativePath, input);
    }

    #endregion

    #region RefundOrder

    public async Task<IEnumerable<GetRefundOrdersOutput>> GetRefundOrders(IEnumerable<long> baseOrderIds)
    {
        var relativePath = $"/Refund/GetRefundOrders";
        return await PostAsync<IEnumerable<long>, IEnumerable<GetRefundOrdersOutput>>(relativePath, baseOrderIds);
    }

    #endregion

    #region Kingdee

    public Task KingdeeCustomerAdd(Contracts.Common.Order.Messages.KingdeeCustomerAddMessage receive)
    {
        var relativePath = $"/Kingdee/KingdeeCustomerAdd";
        return PostAsync(relativePath, receive);
    }

    public Task KingdeeSupplierAdd(Contracts.Common.Order.Messages.KingdeeSupplierAddMessage receive)
    {
        var relativePath = $"/Kingdee/KingdeeSupplierAdd";
        return PostAsync(relativePath, receive);
    }

    public Task<long> KingdeeBillPush(KingdeeBillPushInput input)
    {
        var relativePath = $"/Kingdee/BillPush";
        return PostAsync<KingdeeBillPushInput, long>(relativePath, input);
    }

    public Task KingdeeBillPushExecute(Contracts.Common.Order.DTOs.Kingdee.KingdeeBillPushExecuteInput input)
    {
        var relativePath = $"/Kingdee/KingdeeBillPushExecute";
        return PostAsync(relativePath, input);
    }

    public Task KingdeeBillPushResultNotify(long[] ids)
    {
        var relativePath = $"/Kingdee/BillPushResultNotify";
        return PostAsync(relativePath, ids);
    }

    #endregion

    #region ScenicTicketSupplierOrder

    public async Task<SupplierOrderDeliveryOutput> ScenicTicketSupplierOrderDelivery(SupplierOrderDeliveryInput input)
    {
        var relativePath = "/ScenicTicketSupplierOrder/OrderDelivery";
        return await PostAsync<SupplierOrderDeliveryInput, SupplierOrderDeliveryOutput>(relativePath, input);
    }

    public async Task<GetSupplierOrderDetailOutput> GetScenicTicketOpenSupplierOrderDetail(GetSupplierOrderDetailInput input)
    {
        var relativePath = "/ScenicTicketSupplierOrder/GetOrderDetail";
        return await PostAsync<GetSupplierOrderDetailInput, GetSupplierOrderDetailOutput>(relativePath, input);
    }

    #endregion

    #region GroupBooking

    public Task<List<long>> GroupBookingApplicationFormApply(ApplicationFormApplyInput input)
    {
        var relativePath = "/HotelGroupBooking/ApplicationFormApply";
        return PostAsync<ApplicationFormApplyInput, List<long>>(relativePath, input);
    }

    public Task<PagingModel<ApplicationFormSearchOutput, ApplicationFormSearchSupplement>> GroupBookingApplicationFormSearch(ApplicationFormSearchInput input)
    {
        var relativePath = "/HotelGroupBooking/Search";
        return PostAsync<ApplicationFormSearchInput, PagingModel<ApplicationFormSearchOutput, ApplicationFormSearchSupplement>>(relativePath, input);
    }

    public Task<List<ApplicationFormSearchOutput>> GroupBookingApplicationFormExportData(ApplicationFormSearchInput input)
    {
        var relativePath = "/HotelGroupBooking/ExportData";
        return PostAsync<ApplicationFormSearchInput, List<ApplicationFormSearchOutput>>(relativePath, input);
    }

    public Task<ApplicationFormOutput> GetApplicationForm(GetApplicationFormInput input)
    {
        var relativePath = "/HotelGroupBooking/GetApplicationForm";
        return PostAsync<GetApplicationFormInput, ApplicationFormOutput>(relativePath, input);
    }


    public Task GroupBookingApplicationFormModify(ApplicationFormModifyInput input)
    {
        var relativePath = "/HotelGroupBooking/ApplicationFormModify";
        return PostAsync(relativePath, input);
    }

    public Task GroupBookingApplicationFormStatusModify(ApplicationFormStatusModifyInput input)
    {
        var relativePath = "/HotelGroupBooking/ApplicationFormStatusModify";
        return PostAsync(relativePath, input);
    }

    public Task<IEnumerable<OperationLogOutput>> GetGroupBookingOperationLogs(GetOperationLogInput input)
    {
        var relativePath = "/HotelGroupBooking/GetOperationLogs";
        return PostAsync<GetOperationLogInput, IEnumerable<OperationLogOutput>>(relativePath, input);
    }

    public Task GroupBookingInquiry(InquiryInput input)
    {
        var relativePath = "/HotelGroupBooking/Inquiry";
        return PostAsync(relativePath, input);
    }

    public Task GroupBookingQuotation(QuotationInput input)
    {
        var relativePath = "/HotelGroupBooking/Quotation";
        return PostAsync(relativePath, input);
    }

    public Task GroupBookingQuotated(QuotatedInput input)
    {
        var relativePath = "/HotelGroupBooking/Quotated";
        return PostAsync(relativePath, input);
    }

    public Task<IEnumerable<QuotationOutput>> GetGroupBookingQuotations(long applicationFormId)
    {
        var relativePath = $"/HotelGroupBooking/GetQuotations?applicationFormId={applicationFormId}";
        return GetAsync<IEnumerable<QuotationOutput>>(relativePath);
    }

    public Task GroupBookingQuotationConfirm(QuotationConfirmInput input)
    {
        var relativePath = "/HotelGroupBooking/QuotationConfirm";
        return PostAsync(relativePath, input);
    }

    public Task GroupBookingPreOrder(PreOrderInput input)
    {
        var relativePath = "/HotelGroupBooking/PreOrder";
        return PostAsync(relativePath, input);
    }

    public Task<List<PreOrderOutput>> GetGroupBookingPreOrders(long applicationFormId, long? preOrderId = default)
    {
        var relativePath = $"/HotelGroupBooking/GetPreOrders?applicationFormId={applicationFormId}&preOrderId={preOrderId}";
        return GetAsync<List<PreOrderOutput>>(relativePath);
    }

    public Task GroupBookingPreOrderConfirm(PreOrderConfirmInput input)
    {
        var relativePath = "/HotelGroupBooking/PreOrderConfirm";
        return PostAsync(relativePath, input);
    }
    public Task<GroupBookingPreOrderSettingDto> GetGroupBookingPreOrderSetting()
    {
        var relativePath = "/HotelGroupBooking/GetPreOrderSetting";
        return GetAsync<GroupBookingPreOrderSettingDto>(relativePath);
    }

    public Task GroupBookingPreOrderSettingSave(GroupBookingPreOrderSettingDto dto)
    {
        var relativePath = "/HotelGroupBooking/PreOrderSettingSave";
        return PostAsync<GroupBookingPreOrderSettingDto>(relativePath, dto);
    }

    public Task<CityOutput> GroupBookingGetCities()
    {
        var relativePath = "/HotelGroupBooking/GetCities";
        return GetAsync<CityOutput>(relativePath);
    }

    public Task<PagingModel<SearchAgencyOutput>> SearchGroupBookingAgencies(SearchAgenciesInput input)
    {
        var relativePath = "/HotelGroupBooking/SearchAgencies";
        return PostAsync<SearchAgenciesInput, PagingModel<SearchAgencyOutput>>(relativePath, input);
    }

    public Task<PagingModel<GroupBookingOrder.SearchOutput, IEnumerable<GroupBookingOrder.SearchSupplement>>> GroupBookingOrderSearch(GroupBookingOrder.SearchInput input)
    {
        var relativePath = "/HotelGroupBookingOrder/Search";
        return PostAsync<GroupBookingOrder.SearchInput, PagingModel<GroupBookingOrder.SearchOutput, IEnumerable<GroupBookingOrder.SearchSupplement>>>(relativePath, input);
    }

    public Task<GroupBookingOrder.DetailOutput> GroupBookingOrderDetail(GroupBookingOrder.DetailInput input)
    {
        var relativePath = "/HotelGroupBookingOrder/Detail";
        return PostAsync<GroupBookingOrder.DetailInput, GroupBookingOrder.DetailOutput>(relativePath, input);
    }

    public Task AddGroupBookingOrderGuest(GroupBookingOrder.AddOrderGuestInput input)
    {
        var relativePath = "/HotelGroupBookingOrder/AddOrderGuest";
        return PostAsync(relativePath, input);
    }

    public Task<List<GroupBookingOrder.GroupBookingOrderPaymentOutput>> BaseOrderItemPayments(GroupBookingOrder.BaseOrderItemPaymentInput input)
    {
        var relativePath = "/HotelGroupBookingOrder/BaseOrderItemPayments";
        return PostAsync<GroupBookingOrder.BaseOrderItemPaymentInput, List<GroupBookingOrder.GroupBookingOrderPaymentOutput>>(relativePath, input);
    }

    public Task AdjustFinalPayment(GroupBookingOrder.AdjustFinalPaymentInput input)
    {
        var relativePath = "/HotelGroupBookingOrder/AdjustFinalPayment";
        return PostAsync<GroupBookingOrder.AdjustFinalPaymentInput>(relativePath, input);
    }

    public Task<GroupBookingOrder.OrderCreateOutput> GroupBookingOrderCreate(GroupBookingOrder.OrderCreateInput input)
    {
        var relativePath = "/HotelGroupBookingOrder/OrderCreate";
        return PostAsync<GroupBookingOrder.OrderCreateInput, GroupBookingOrder.OrderCreateOutput>(relativePath, input);
    }

    public Task SaveGroupBookingOperatorUsers(List<Contracts.Common.Order.DTOs.GroupBookingOperatorUser.GroupBookingOperatorUserDto> inputs)
    {
        var relativePath = "/GroupBookingOperatorUser/Save";
        return PostAsync<List<Contracts.Common.Order.DTOs.GroupBookingOperatorUser.GroupBookingOperatorUserDto>>(relativePath, inputs);
    }

    public Task<List<Contracts.Common.Order.DTOs.GroupBookingOperatorUser.GroupBookingOperatorUserDto>> GetAllGroupBookingOperatorUsers()
    {
        var relativePath = "/GroupBookingOperatorUser/GetAll";
        return GetAsync<List<Contracts.Common.Order.DTOs.GroupBookingOperatorUser.GroupBookingOperatorUserDto>>(relativePath);
    }
    public Task<List<GetAgencyHistoricalStatisticsOutput>> GetAgencyHistoricalStatistics(GetAgencyHistoricalStatisticsInput input)
    {
        var relativePath = "/HotelGroupBooking/GetAgencyHistoricalStatistics";
        return PostAsync<GetAgencyHistoricalStatisticsInput, List<GetAgencyHistoricalStatisticsOutput>>(relativePath, input);
    }
    #endregion

    #region GroupBookingFinancialHandleOrder

    public Task<long> GroupBookingFinancialHandleOrderCreate(GroupBookingFinancialHandleOrder.FinancialHandleOrderCreateInput input)
    {
        var relativePath = "/GroupBookingFinancialHandleOrder/Create";
        return PostAsync<GroupBookingFinancialHandleOrder.FinancialHandleOrderCreateInput, long>(relativePath, input);
    }

    public Task<PagingModel<GroupBookingFinancialHandleOrder.FinancialHandleOrderSearchOutput>> GroupBookingFinancialHandleOrderSearch(GroupBookingFinancialHandleOrder.FinancialHandleOrderSearchInput input)
    {
        var relativePath = "/GroupBookingFinancialHandleOrder/Search";
        return PostAsync<GroupBookingFinancialHandleOrder.FinancialHandleOrderSearchInput, PagingModel<GroupBookingFinancialHandleOrder.FinancialHandleOrderSearchOutput>>(relativePath, input);
    }

    public Task<GroupBookingFinancialHandleOrder.FinancialHandleOrderDetailOutput> GroupBookingFinancialHandleOrderDetail(GroupBookingFinancialHandleOrder.FinancialHandleOrderDetailInput input)
    {
        var relativePath = "/GroupBookingFinancialHandleOrder/Detail";
        return PostAsync<GroupBookingFinancialHandleOrder.FinancialHandleOrderDetailInput, GroupBookingFinancialHandleOrder.FinancialHandleOrderDetailOutput>(relativePath, input);
    }

    public Task GroupBookingFinancialHandleOrderHandle(GroupBookingFinancialHandleOrder.FinancialHandleOrderHandleInput input)
    {
        var relativePath = "/GroupBookingFinancialHandleOrder/Handle";
        return PostAsync<GroupBookingFinancialHandleOrder.FinancialHandleOrderHandleInput>(relativePath, input);
    }

    #endregion

    #region AfterSaleFinancialHandleOrder

    /// <summary>
    /// 售后财务处理单搜索
    /// </summary>
    /// <param name=""></param>
    /// <returns></returns>
    public Task<PagingModel<AfterSaleFinancialHandleOrder.SearchOutput>> AfterSaleFinancialHandleOrderSearch(AfterSaleFinancialHandleOrder.SearchInput input)
    {
        var relativePath = "/AfterSaleFinancialHandleOrder/Search";
        return PostAsync<AfterSaleFinancialHandleOrder.SearchInput, PagingModel<AfterSaleFinancialHandleOrder.SearchOutput>>(relativePath, input);
    }


    public Task<AfterSaleFinancialHandleOrder.DetailOutput> GetAfterSaleFinancialHandleOrder(long id)
    {
        var relativePath = $"/AfterSaleFinancialHandleOrder/Get?id={id}";
        return GetAsync<AfterSaleFinancialHandleOrder.DetailOutput>(relativePath);
    }

    public Task AfterSaleFinancialHandleOrderHandle(AfterSaleFinancialHandleOrder.HandleInput input)
    {
        var relativePath = "/AfterSaleFinancialHandleOrder/Handle";
        return PostAsync<AfterSaleFinancialHandleOrder.HandleInput>(relativePath, input);
    }

    #endregion

    #region InvoiceRecord
    public async Task<PagingModel<InvoiceRecordDto, IList<InvoiceRecordTotalDto>>> SearchInvoice(SearchInvoiceRecordInput input)
    {
        var relativePath = "/InvoiceRecord/Search";
        return await PostAsync<SearchInvoiceRecordInput, PagingModel<InvoiceRecordDto, IList<InvoiceRecordTotalDto>>>(relativePath, input);
    }

    public async Task ApplyInvoice(Contracts.Common.Order.DTOs.Invoice.ApplyInput input)
    {
        var relativePath = "/InvoiceRecord/Apply";
        await PostAsync<Contracts.Common.Order.DTOs.Invoice.ApplyInput, string>(relativePath, input);
    }

    public async Task<List<CheckStatusOutPut>> InvoiceCheckStatusByOrderIds(CheckStatusInput input)
    {
        var relativePath = "/InvoiceRecord/CheckStatusByOrderIds";
        return await PostAsync<CheckStatusInput, List<CheckStatusOutPut>>(relativePath, input);
    }

    public async Task<List<long>> InvoiceGetIsSupportIdsBySettlementOrder(long[] settlementOrderIds)
    {
        var relativePath = "/InvoiceRecord/GetIsSupportIdsBySettlementOrder";
        return await PostAsync<long[], List<long>>(relativePath, settlementOrderIds);
    }


    public Task Invoicing(ApplyInvoicingInput input)
    {
        var relativePath = "/InvoiceRecord/Invoicing";
        return PostAsync<ApplyInvoicingInput>(relativePath, input);
    }

    #endregion

    #region Insure
    public async Task CreateInsurePolicy(CreateInsureInput input)
    {
        var relativePath = "/Insure/Create";
        await PostAsync<CreateInsureInput>(relativePath, input);
    }
    #endregion

    #region InsurePolicyHolder

    public async Task<InsurePolicyHolderOutput> GetInsurePolicyHolder()
    {
        var relativePath = "/InsurePolicyHolder/Detail";
        return await GetAsync<InsurePolicyHolderOutput>(relativePath);
    }

    public async Task SaveInsurePolicyHolder(SaveInsurePolicyHolderInput input)
    {
        var relativePath = "/InsurePolicyHolder/Save";
        await PostAsync<SaveInsurePolicyHolderInput, string>(relativePath, input);
    }
    #endregion

    #region InsureProduct

    public async Task<List<InsureProductOutput>> GetInsureProductList()
    {
        var relativePath = "/InsureProduct/GetList";
        return await GetAsync<List<InsureProductOutput>>(relativePath);
    }

    public async Task<InsureProductOutput> GetInsureProduct(long id)
    {
        var relativePath = "/InsureProduct/Get?id=" + id;
        return await GetAsync<InsureProductOutput>(relativePath);
    }

    public async Task<InsureProductOutput> GetInsureProductByBaseOrderId(long baseOrderId)
    {
        var relativePath = "/InsureProduct/GetByBaseOrderId?baseOrderId=" + baseOrderId;
        return await GetAsync<InsureProductOutput>(relativePath);
    }

    public async Task<InsureProductOutput> SaveInsureProduct(SaveInsureProductInput input)
    {
        var relativePath = "/InsureProduct/Save";
        return await PostAsync<SaveInsureProductInput, InsureProductOutput>(relativePath, input);
    }

    public async Task DeleteInsureProduct(long id)
    {
        var relativePath = "/InsureProduct/Delete?id=" + id;
        await PostAsync<long>(relativePath, id);
    }

    public async Task MoveInsureProduct(List<MoveInsureProductInput> input)
    {
        var relativePath = "/InsureProduct/Move";
        await PostAsync<List<MoveInsureProductInput>, string>(relativePath, input);
    }

    public async Task<List<InsureProductOutput>> GetInsureProductSelection()
    {
        var relativePath = "/InsureProduct/GetSelection";
        return await GetAsync<List<InsureProductOutput>>(relativePath);
    }

    public async Task<InsureProductRelationsOutput> GetInsureProductRelation(long productId)
    {
        var relativePath = "/InsureProduct/GetInsureProductRelation?productId=" + productId;
        return await GetAsync<InsureProductRelationsOutput>(relativePath);
    }

    public async Task SaveInsureProductRelation(SaveInsureProductRelationsInput input)
    {
        var relativePath = "/InsureProduct/SaveInsureProductRelation";
        await PostAsync<SaveInsureProductRelationsInput, string>(relativePath, input);
    }

    public async Task<List<SupplierInsureProductOutput>> SearchSupplierInsureProducts(SearchSupplierInsureProductInput input)
    {
        var relativePath = "/InsureProduct/SearchSupplierInsureProducts";
        return await PostAsync<SearchSupplierInsureProductInput, List<SupplierInsureProductOutput>>(relativePath, input);
    }
    #endregion

    #region InsureProductSkuRelation
    public async Task<GetInsureProductSkuRelationOutput> GetInsureProductSkuRelation(GetInsureProductSkuRelationInput input)
    {
        var relativePath = "/InsureProductRelation/GetSku";
        return await PostAsync<GetInsureProductSkuRelationInput, GetInsureProductSkuRelationOutput>(relativePath, input);
    }

    public async Task SaveInsureProductSkuRelation(SaveInsureProductSkuRelationInput input)
    {
        var relativePath = "/InsureProductRelation/SaveSku";
        await PostAsync<SaveInsureProductSkuRelationInput>(relativePath, input);
    }

    public async Task DeleteInsureProductSkuRelation(DeleteInsureProductSkuRelationInput input)
    {
        var relativePath = "/InsureProductRelation/DeleteSku";
        await PostAsync<DeleteInsureProductSkuRelationInput>(relativePath, input);
    }
    #endregion

    #region InsurePurchaseRecord

    public async Task<PagingModel<InsurePurchaseRecordOutput>> GetInsurePurchaseRecordList(SearchInsurePurchaseRecordInput input)
    {
        var relativePath = "/InsurePurchaseRecord/GetList";
        return await PostAsync<SearchInsurePurchaseRecordInput, PagingModel<InsurePurchaseRecordOutput>>(relativePath, input);
    }

    public async Task<PagingModel<InsurePurchaseRecordDetailOutput>> SearchInsurePurchaseRecordDetail(SearchInsurePurchaseRecordDetailInput input)
    {
        var relativePath = "/InsurePurchaseRecord/Detail";
        return await PostAsync<SearchInsurePurchaseRecordDetailInput, PagingModel<InsurePurchaseRecordDetailOutput>>(relativePath, input);
    }

    public async Task<InsurePurchaseRecordByOrderOutput> GetInsureRecordByOrder(long baseOrderId)
    {
        var relativePath = "/InsurePurchaseRecord/GetInsureRecordByOrder?baseOrderId=" + baseOrderId;
        return await GetAsync<InsurePurchaseRecordByOrderOutput>(relativePath);
    }

    public async Task<List<ExportInsurePurchaseRecordOutput>> ExportInsurePurchaseRecords(SearchInsurePurchaseRecordInput input)
    {
        var relativePath = "/InsurePurchaseRecord/ExportData";
        return await PostAsync<SearchInsurePurchaseRecordInput, List<ExportInsurePurchaseRecordOutput>>(relativePath, input);
    }

    public async Task<List<GetInsureTotalAmountOutput>> GetInsureTotalAmount(GetInsureTotalAmountInput input)
    {
        var relativePath = "/InsurePurchaseRecord/GetInsureTotalAmount";
        return await PostAsync<GetInsureTotalAmountInput, List<GetInsureTotalAmountOutput>>(relativePath, input);
    }
    #endregion

    #region InsureOrderRelation
    public async Task SetInsureOrderRelationAuto(UpdateInsureOrderRelationInput input)
    {
        var relativePath = "/InsureOrderRelation/SetAuto";
        await PostAsync<UpdateInsureOrderRelationInput, string>(relativePath, input);
    }

    #endregion

    #region  ThirdInsureProduct
    public async Task<List<SupplierInsureProductOutput>> SearchThirdInsureProduct(SearchThirdInsureProductInput input)
    {
        var relativePath = "/ThirdInsureProduct/Search";
        return await PostAsync<SearchThirdInsureProductInput, List<SupplierInsureProductOutput>>(relativePath, input);
    }
    #endregion

    #region OpenChannelSyncFailOrder

    public async Task<PagingModel<SearchOpenChannelSyncFailOrderOutput>> SearchOpenChannelSyncFailOrder(
        SearchOpenChannelSyncFailOrderInput input)
    {
        var relativePath = "/OpenChannelSyncFailOrder/Search";
        return await PostAsync<SearchOpenChannelSyncFailOrderInput, PagingModel<SearchOpenChannelSyncFailOrderOutput>>(relativePath, input);
    }

    public Task AddOpenChannelSyncFailOrder(OpenChannelSyncFailOrderAddInput input)
    {
        var relativePath = "/OpenChannelSyncFailOrder/Add";
        return PostAsync(relativePath, input);
    }

    public async Task<IEnumerable<GetSyncFailOrderCountOutput>> SearchOpenChannelSyncFailOrderStatusCount(SearchOpenChannelSyncFailOrderInput input)
    {
        var relativePath = "/OpenChannelSyncFailOrder/SearchStatusCount";
        return await PostAsync<SearchOpenChannelSyncFailOrderInput, IEnumerable<GetSyncFailOrderCountOutput>>(relativePath, input);
    }

    public async Task<GetOpenChannelSyncFailOrderOutput> GetOpenChannelSyncFailOrderDetail(GetOpenChannelSyncFailOrderInput input)
    {
        var relativePath = $"/OpenChannelSyncFailOrder/Detail";
        return await PostAsync<GetOpenChannelSyncFailOrderInput, GetOpenChannelSyncFailOrderOutput>(relativePath, input);
    }

    public async Task InvalidOpenChannelSyncFailOrder(InvalidSyncFailOrderInput input)
    {
        var relativePath = "/OpenChannelSyncFailOrder/Invalid";
        await PostAsync(relativePath, input);
    }

    public async Task RestoredOpenChannelSyncFailOrder(RestoredSyncFailOrderInput input)
    {
        var relativePath = "/OpenChannelSyncFailOrder/Restored";
        await PostAsync(relativePath, input);
    }

    public async Task UpdateOpenChannelSyncFailOrderType(UpdateChannelSyncFailOrderTypeInput input)
    {
        var relativePath = "/OpenChannelSyncFailOrder/UpdateOrderType";
        await PostAsync(relativePath, input);
    }

    #endregion

    public async Task<PagingModel<SettlementOrder.SettlementOrderOutput>> SearchSettlementOrder(SettlementOrder.SearchInput input)
    {
        var relativePath = "/SettlementOrder/Search";
        return await PostAsync<SettlementOrder.SearchInput, PagingModel<SettlementOrder.SettlementOrderOutput>>(relativePath, input);
    }

    public async Task<IEnumerable<SettlementOrder.SettlementOrderOutput>> ExportSettlementOrder(SettlementOrder.ExportSettlementOrderInput input)
    {
        var relativePath = "/SettlementOrder/Export";
        return await PostAsync<SettlementOrder.ExportSettlementOrderInput, IEnumerable<SettlementOrder.SettlementOrderOutput>>(relativePath, input);
    }

    #region Finance

    public Task<AgencyCreditRecordOrderInfoOutput> GetAgencyCreditRecordOrderInfo(AgencyCreditRecordOrderInfoInput input)
    {
        var relativePath = "/Finance/GetAgencyCreditRecordOrderInfo";
        return PostAsync<AgencyCreditRecordOrderInfoInput, AgencyCreditRecordOrderInfoOutput>(relativePath, input);
    }

    #endregion

    #region CarHailingOrder
    public async Task<Contracts.Common.Order.DTOs.CarHailingOrder.DetailOutput> GetCarHailingOrder(long baseOrderId)
    {
        var relativePath = $"/CarHailingOrder/Get?baseOrderId={baseOrderId}";
        return await GetAsync<Contracts.Common.Order.DTOs.CarHailingOrder.DetailOutput>(relativePath);
    }

    public async Task EditCarHailingOrderCost(UpdateCostInput input)
    {
        var relativePath = "/CarHailingOrder/EditCost";
        await PostAsync<UpdateCostInput>(relativePath, input);
    }
    #endregion

    #region WorkOrder
    public async Task WorkOrderApply(Contracts.Common.Order.DTOs.WorkOrder.ApplyInput input)
    {
        var relativePath = "/WorkOrder/Apply";
        await PostAsync(relativePath, input);
    }
    #endregion
    #region InvoiceConfig

    public async Task<List<GetInvoiceConfigOutput>> GetInvoiceConfig()
    {
        var relativePath = "/InvoiceConfig/Get";
        return await GetAsync<List<GetInvoiceConfigOutput>>(relativePath);
    }

    public async Task DeleteInvoiceConfig(long id)
    {
        var relativePath = $"/InvoiceConfig/Delete?id={id}";
        await PostAsync(relativePath, id);
    }

    public async Task<long> SetInvoiceConfig(SetInvoiceConfigInput input)
    {
        var relativePath = "/InvoiceConfig/Set";
        return await PostAsync<SetInvoiceConfigInput, long>(relativePath, input);
    }

    public async Task SetInvoiceConfigEnabled(bool enabled)
    {
        var relativePath = $"/InvoiceConfig/SetEnabled?enabled={enabled}";
        await PostAsync(relativePath, enabled);
    }
    #endregion

    #region InvoiceTitle
    public async Task<List<GetInvoiceTitleOutput>> GetInvoiceTitleList(long AgencyId)
    {
        var relativePath = $"/InvoiceTitle/GetList?UserId={AgencyId}";
        return await GetAsync<List<GetInvoiceTitleOutput>>(relativePath);
    }

    public async Task<long> AddInvoiceTitle(AddInvoiceTitleInput input)
    {
        var relativePath = "/InvoiceTitle/Add";
        return await PostAsync<AddInvoiceTitleInput, long>(relativePath, input);
    }

    public async Task UpdateInvoiceTitle(UpdateInvoiceTitleInput input)
    {
        var relativePath = "/InvoiceTitle/Update";
        await PostAsync(relativePath, input);
    }
    #endregion

    #region AggregateOrder
    public async Task<PagingModel<SearchAggregateOrderOutput, SearchAggregateStatusStat>> AggregateOrderSearch(SearchAggregateOrderInput input)
    {
        var relativePath = "/AggregateOrder/Search";
        return await PostAsync<SearchAggregateOrderInput, PagingModel<SearchAggregateOrderOutput, SearchAggregateStatusStat>>(relativePath, input);
    }

    public async Task<List<ExportAggregateOrderOutput>> AggregateOrderExport(ExportAggregateOrderInput input)
    {
        var relativePath = "/AggregateOrder/Export";
        return await PostAsync<ExportAggregateOrderInput, List<ExportAggregateOrderOutput>>(relativePath, input);
    }

    public async Task SetAggregateOrderRemark(SetAggregateOrderRemarkInput input)
    {
        var relativePath = "/AggregateOrder/SetOrderRemark";
        await PostAsync<SetAggregateOrderRemarkInput>(relativePath, input);
    }

    public async Task<List<SearchAggregateStatusOuput>> SearchAggregateOrderStatus(SearchAggregateStatusInput input)
    {
        var relativePath = "/AggregateOrder/SearchAggregateOrderStatus";
        return await PostAsync<SearchAggregateStatusInput, List<SearchAggregateStatusOuput>>(relativePath, input);
    }

    public Task<PagingModel<SearchAggregateOrderFinanceOutput, AggregateOrderFinanceSummary>> AggregateOrderFinanceSearch(SearchAggregateOrderInput input)
    {
        var relativePath = "/AggregateOrder/FinanceSearch";
        return PostAsync<SearchAggregateOrderInput, PagingModel<SearchAggregateOrderFinanceOutput, AggregateOrderFinanceSummary>>(relativePath, input);
    }

    public async Task RemoveAggregateOrderProcessingLevelTag(RemoveProcessingLevelTagInput input)
    {
        var relativePath = "/AggregateOrder/RemoveProcessingLevelTag";
        await PostAsync(relativePath, input);
    }

    #endregion

    #region OrderLog
    public async Task CreateOrderLog(Contracts.Common.Order.DTOs.OrderLog.CreateInput input)
    {
        var relativePath = "/OrderLog/Create";
        await PostAsync(relativePath, input);
    }
    #endregion

    #region OrderPrice

    public async Task<List<OrderMultPriceOutput>> GetByBaseOrderId(long baseOrderId)
    {
        var relativePath = $"/OrderPrice/GetByBaseOrderId?baseOrderId={baseOrderId}";
        return await GetAsync<List<OrderMultPriceOutput>>(relativePath);
    }

    #endregion

    #region CarProductOrder 用车订单

    public Task<CarProductOrder.CreateCarProductOrderOutput> CarProductOrderCreate(CarProductOrder.CreateCarProductOrderInput input)
    {
        var relativePath = "/CarProductOrder/Create";
        return PostAsync<CarProductOrder.CreateCarProductOrderInput, CarProductOrder.CreateCarProductOrderOutput>(relativePath, input);
    }

    public Task<CarProductOrder.GetDetailOutput> CarProductOrderDetail(CarProductOrder.GetDetailInput input)
    {
        var relativePath = "/CarProductOrder/Detail";
        return PostAsync<CarProductOrder.GetDetailInput, CarProductOrder.GetDetailOutput>(relativePath, input);
    }

    public Task<PagingModel<CarProductOrder.SearchOrderOutput>> CarProductOrderSearch(CarProductOrder.SearchOrderInput input)
    {
        var relativePath = "/CarProductOrder/Search";
        return PostAsync<CarProductOrder.SearchOrderInput, PagingModel<CarProductOrder.SearchOrderOutput>>(relativePath, input);
    }

    public Task CarProductOrderConfirm(CarProductOrder.ConfirmOrderInput input)
    {
        var relativePath = "/CarProductOrder/Confirm";
        return PostAsync(relativePath, input);
    }

    public Task CarProductOrderFinish(CarProductOrder.FinishOrderInput input)
    {
        var relativePath = "/CarProductOrder/Finish";
        return PostAsync(relativePath, input);
    }

    public Task CarProductOrderRefund(CarProductOrder.RefundOrderInput input)
    {
        var relativePath = "/CarProductOrder/Refund";
        return PostAsync(relativePath, input);
    }

    public Task CarProductOrderEditCost(UpdateCostInput input)
    {
        var relativePath = "/CarProductOrder/EditCost";
        return PostAsync(relativePath, input);
    }
    public Task EditCarOrderUsingInfo(CarProductOrder.EditCarUsingInfoInput input)
    {
        var relativePath = "/CarProductOrder/EditCarUsingInfo";
        return PostAsync(relativePath, input);
    }

    public async Task<bool> UpdateCarProductOrderTravelInfo(Contracts.Common.Order.DTOs.CarProductOrder.UpdateTravelInfoInput input)
    {
        var relativePath = "/CarProductOrder/UpdateTravelInfo";
        return await PostAsync<Contracts.Common.Order.DTOs.CarProductOrder.UpdateTravelInfoInput, bool>(relativePath, input);
    }

    public async Task<bool> UpdateCarProductOrderConfirmation(Contracts.Common.Order.DTOs.CarProductOrder.UpdateOrderConfirmationInput input)
    {
        var relativePath = "/CarProductOrder/UpdateOrderConfirmation";
        return await PostAsync<Contracts.Common.Order.DTOs.CarProductOrder.UpdateOrderConfirmationInput, bool>(relativePath, input);
    }

    public async Task<bool> UpdateCarProductOrderContact(UpdateContactInput input)
    {
        var relativePath = "/CarProductOrder/UpdateContact";
        return await PostAsync<UpdateContactInput, bool>(relativePath, input);
    }

    public async Task<CarProductOrder.GetCarProductChannelOrderInfoOutput> GetCarProductChannelOrderInfo(string channelOrderNo)
    {
        var relativePath = $"/CarProductOrder/GetChannelOrderInfo?channelOrderNo={channelOrderNo}";
        return await GetAsync<CarProductOrder.GetCarProductChannelOrderInfoOutput>(relativePath);
    }

    public Task UpdateCarProductOrderSupplierOrderId(UpdateSupplierOrderIdInput input)
    {
        var relativePath = "/CarProductOrder/UpdateSupplierOrderId";
        return PostAsync(relativePath, input);
    }
    #endregion

    #region OrderPrintSetting

    public async Task<OrderPrintSettingBffOutput> GetOrderPrintSetting()
    {
        var relativePath = $"/OrderPrintSetting/Get";
        return await GetAsync<OrderPrintSettingBffOutput>(relativePath);
    }

    public async Task<long> UpdateOrderPrintSetting(UpdateOrderPrintSettingInput input)
    {
        var relativePath = $"/OrderPrintSetting/Update";
        return await PostAsync<UpdateOrderPrintSettingInput, long>(relativePath, input);
    }

    #endregion

    public async Task<long> AddOrderOperationTask(AddOrderOperationTaskInput input)
    {
        var relativePath = $"/OrderOperationTask/Add";
        return await PostAsync<AddOrderOperationTaskInput, long>(relativePath, input);
    }

    public async Task<PagingModel<SearchOrderOperationTaskOutput>> SearchOrderOperationTask(SearchOrderOperationTaskInput input)
    {
        var relativePath = $"/OrderOperationTask/Search";
        return await PostAsync<SearchOrderOperationTaskInput, PagingModel<SearchOrderOperationTaskOutput>>(relativePath, input);
    }

    public async Task OrderOperationTaskExportCallBack(int result, CallbackData callback)
    {
        var relativePath = $"/OrderOperationTask/ExportCallBack?result=" + result;
        await PostAsync<CallbackData>(relativePath, callback);
    }

    #region CarProductSupplierOrder

    public async Task<List<CarSupplierOrderFuzzySearchOutput>> CarSupplierOrderFuzzySearch(
        CarSupplierOrderFuzzySearchInput input)
    {
        var relativePath = "/CarProductSupplierOrder/FuzzySearch";
        return await PostAsync<CarSupplierOrderFuzzySearchInput, List<CarSupplierOrderFuzzySearchOutput>>(relativePath, input);
    }

    public async Task<QueryCarOrderByNotifyIdOutput> QueryCarProductOrderByNotifyId(QueryCarOrderByNotifyIdInput input)
    {
        var relativePath = "/CarProductSupplierOrder/QueryByNotifyId";
        return await PostAsync<QueryCarOrderByNotifyIdInput, QueryCarOrderByNotifyIdOutput>(relativePath, input);
    }

    public async Task CarProductSupplierOrderQuoteNotifyProcess(CarProductSupplierOrderQuoteNotifyInput input)
    {
        var relativePath = "/CarProductSupplierOrder/QuoteNotifyProcess";
        await PostAsync(relativePath, input);
    }

    public async Task CarSupplierOrderReservationNotifyProcess(CarProductSupplierOrderReservationNotifyInput input)
    {
        var relativePath = "/CarProductSupplierOrder/ReservationNotifyProcess";
        await PostAsync(relativePath, input);
    }

    public async Task CarSupplierOrderRetryQuote(CarProductSupplierOrderQuoteInput input)
    {
        var relativePath = "/CarProductSupplierOrder/RetryQuote";
        await PostAsync(relativePath, input);
    }

    public async Task<CancelCarProductSupplierOrderOutput> CarSupplierOrderCancel(CancelCarProductSupplierOrderInput input)
    {
        var relativePath = "/CarProductSupplierOrder/Cancel";
        return await PostAsync<CancelCarProductSupplierOrderInput, CancelCarProductSupplierOrderOutput>(relativePath, input);
    }

    #endregion

    #region OpenChannelOrder

    /// <summary>
    /// 开放平台-渠道订单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<QueryChannelOrderDetailOutput> QueryChannelOrderDetail(QueryChannelOrderDetailInput input)
    {
        var relativePath = "/OpenChannelOrder/OrderDetail";
        return await PostAsync<QueryChannelOrderDetailInput, QueryChannelOrderDetailOutput>(relativePath, input);
    }

    /// <summary>
    /// 开放平台 - 渠道订单确认
    /// </summary>
    /// <param name="input"></param>
    public async Task ConfirmChannelOrder(ConfirmChannelOrderInput input)
    {
        var relativePath = "/OpenChannelOrder/Confirm";
        await PostAsync(relativePath, input);
    }

    #endregion

    #region OpenSupplierOrder

    public async Task<GetOpenSupplierSaasOrderDetailOutput> GetOpenSupplierSaasOrderDetail(
        GetOpenSupplierSaasOrderDetailInput input)
    {
        var relativePath = "/OpenSupplierOrder/GetSaasOrderDetail";
        return await PostAsync<GetOpenSupplierSaasOrderDetailInput, GetOpenSupplierSaasOrderDetailOutput>(relativePath, input);
    }

    public async Task<CreateSupplierOrderOutput> CreateOpenSupplierOrder(CreateSupplierOrderInput input)
    {
        var relativePath = "/OpenSupplierOrder/CreateOrder";
        return await PostAsync<CreateSupplierOrderInput, CreateSupplierOrderOutput>(relativePath, input);
    }

    public async Task<GetOpenSupplierOrderDetailOutput> GetOpenSupplierOrderDetail(GetOpenSupplierOrderDetailInput input)
    {
        var relativePath = "/OpenSupplierOrder/GetOpenSupplierOrderDetail";
        return await PostAsync<GetOpenSupplierOrderDetailInput, GetOpenSupplierOrderDetailOutput>(relativePath, input);
    }

    public async Task<ResyncSupplierOrderDataOutput> ResyncOpenSupplierOrderData(ResyncSupplierOrderDataInput input)
    {
        var relativePath = "/OpenSupplierOrder/ResyncOrderData";
        return await PostAsync<ResyncSupplierOrderDataInput, ResyncSupplierOrderDataOutput>(relativePath, input);
    }

    public async Task<SupplierOrderRetryPayOutput> RetryPayOpenSupplierOrder(SupplierOrderRetryPayInput input)
    {
        var relativePath = "/OpenSupplierOrder/RetryPay";
        return await PostAsync<SupplierOrderRetryPayInput, SupplierOrderRetryPayOutput>(relativePath, input);
    }

    public async Task OpenSupplierOrderStatusNotify(SupplierOrderStatusNotifyInput input)
    {
        var relativePath = "/OpenSupplierOrder/OrderStatusNotify";
        await PostAsync(relativePath, input);
    }

    public async Task AddOpenSupplierOrderExtraInfo(AddOpenSupplierOrderExtraInfosInput input)
    {
        var relativePath = "/OpenSupplierOrder/AddExtraInfo";
        await PostAsync(relativePath, input);
    }

    public async Task<List<QueryOpenSupplierOrderExtraInfosOutput>> QueryOpenSupplierOrderExtraInfo(params long[] baseOrderIds)
    {
        var relativePath = $"/OpenSupplierOrder/QueryExtraInfo";
        return await PostAsync<long[], List<QueryOpenSupplierOrderExtraInfosOutput>>(relativePath, baseOrderIds);
    }

    #endregion

    #region LineSupplierOrder

    public async Task<SupplierOrderDeliveryOutput> TravelLineSupplierOrderDelivery(SupplierOrderDeliveryInput input)
    {
        var relativePath = "/TravelLineSupplierOrder/Delivery";
        return await PostAsync<SupplierOrderDeliveryInput, SupplierOrderDeliveryOutput>(relativePath, input);
    }

    #endregion


    #region OrderDelayedPay

    public Task OrderDelayedCancel(OrderDelayedCancelInput input)
    {
        var relativePath = "/OrderDelayed/Cancel";
        return PostAsync<OrderDelayedCancelInput>(relativePath, input);
    }

    #endregion

    #region CompensationOrder

    /// <summary>
    /// 获取补差关联订单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<PagingModel<SearchCompensationRelatedOrderOutput, SearchCompensationRelatedOrderExtendData>> SearchCompensationRelatedOrder(
        SearchCompensationRelatedOrderInput input)
    {
        var relativePath = "/CompensationOrder/Search";
        return await PostAsync<SearchCompensationRelatedOrderInput, PagingModel<SearchCompensationRelatedOrderOutput, SearchCompensationRelatedOrderExtendData>>(relativePath, input);
    }

    /// <summary>
    /// 绑定补差单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task BindCompensationOrder(BindCompensationOrderInput input)
    {
        var relativePath = "/CompensationOrder/Bind";
        await PostAsync<BindCompensationOrderInput>(relativePath, input);
    }

    /// <summary>
    /// 绑定补差单前置校验
    /// </summary>
    /// <param name="input"></param>
    public async Task PreBindCompensationOrder(BindCompensationOrderPreCheckInput input)
    {
        var relativePath = "/CompensationOrder/PreBind";
        await PostAsync(relativePath, input);
    }

    public async Task UnBindCompensationOrder(UnBindCompensationOrderInput input)
    {
        var relativePath = "/CompensationOrder/UnBind";
        await PostAsync<UnBindCompensationOrderInput>(relativePath, input);
    }

    /// <summary>
    /// 获取补差关联订单详情
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    public async Task<List<GetCompensationRelatedOrderDetailOutput>> GetCompensationRelatedOrder(long baseOrderId)
    {
        var relativePath = $"/CompensationOrder/Get?baseOrderId={baseOrderId}";
        return await GetAsync<List<GetCompensationRelatedOrderDetailOutput>>(relativePath);
    }

    public async Task SupplementCompensationOrder(SupplementCompensationOrderDataInput input)
    {
        var relativePath = "/CompensationOrder/SupplementOrder";
        await PostAsync(relativePath, input);
    }

    #endregion

    #region OffsetOrderDingtalkApply
    public async Task<PagingModel<OffsetOrderDingtalkApplySearchOutput>> SearchOffsetOrderDingtalkApply(OffsetOrderDingtalkApplySearchInput input)
    {
        var relativePath = "/OffsetOrderDingtalkApply/Search";
        return await PostAsync<OffsetOrderDingtalkApplySearchInput, PagingModel<OffsetOrderDingtalkApplySearchOutput>>(relativePath, input);
    }

    public async Task CallbackOffsetOrderDingtalkApplyResult(CallbackApplyResultInput input)
    {
        var relativePath = "/OffsetOrderDingtalkApply/CallbackApplyResult";
        await PostAsync<CallbackApplyResultInput>(relativePath, input);
    }
    #endregion

    #region OpenChannelRefundApplyOrder

    public async Task CreateChannelRefundApplyOrder(CreateChannelRefundApplyOrderInput input)
    {
        var relativePath = "/OpenChannelRefundApplyOrder/Create";
        await PostAsync(relativePath, input);
    }
    #endregion


    #region GroupBookingAreaSetting
    public async Task SaveGroupBookingAreaSetting(SaveGroupBookingAreaSettingInput input)
    {
        var relativePath = "/GroupBookingAreaSetting/Save";
        await PostAsync<SaveGroupBookingAreaSettingInput>(relativePath, input);
    }

    public async Task<GroupBookingAreaSettingOutput> GetGroupBookingAreaSetting()
    {
        var relativePath = "/GroupBookingAreaSetting/Get";
        return await GetAsync<GroupBookingAreaSettingOutput>(relativePath);
    }
    #endregion

    #region GroupBookingAggregate
    public async Task<GetBasicDataOutput> GetGroupBookingAggregateBasicData(GetBasicDataInput input)
    {
        var relativePath = "/GroupBookingAggregate/GetBasicData";
        return await PostAsync<GetBasicDataInput, GetBasicDataOutput>(relativePath, input);
    }

    public async Task<GetChatDataOutput> GetGroupBookingAggregateChatData(GetChatDataInput input)
    {
        var relativePath = "/GroupBookingAggregate/GetChatData";
        return await PostAsync<GetChatDataInput, GetChatDataOutput>(relativePath, input);
    }
    public async Task<GetProportionOutput> GetGroupBookingAggregateProportion(GetProportionInput input)
    {
        var relativePath = "/GroupBookingAggregate/GetProportion";
        return await PostAsync<GetProportionInput, GetProportionOutput>(relativePath, input);
    }

    public async Task<QueryOperationOutput> QueryGroupBookingOperationData(QueryOperationDataInput input)
    {
        var relativePath = "/GroupBookingAggregate/QueryOperationData";
        return await PostAsync<QueryOperationDataInput, QueryOperationOutput>(relativePath, input);
    }

    public async Task<List<GetStatusStatisticsOutput>> GetGroupBookingAggregateStatusStatistics(GetStatusStatisticsInput input)
    {
        var relativePath = "/GroupBookingAggregate/GetStatusStatistics";
        return await PostAsync<GetStatusStatisticsInput, List<GetStatusStatisticsOutput>>(relativePath, input);
    }

    public async Task<List<ExportStatusStatisticsOutput>> ExportGroupBookingAggregateStatusStatistics(GetStatusStatisticsInput input)
    {
        var relativePath = "/GroupBookingAggregate/ExportStatusStatistics";
        return await PostAsync<GetStatusStatisticsInput, List<ExportStatusStatisticsOutput>>(relativePath, input);
    }

    public async Task<GetSyncTimeOutput> GetGroupBookingAggregateSyncTime()
    {
        var relativePath = "/GroupBookingAggregate/GetSyncTime";
        return await GetAsync<GetSyncTimeOutput>(relativePath);
    }
    #endregion

    #region OrderRemark

    public async Task<List<BaseOrderRemarkOutput>> GetAllOrderRemark(QueryBaseOrderRemarkInput input)
    {
        var relativePath = "/BaseOrderRemark/GetAll";
        return await PostAsync<QueryBaseOrderRemarkInput, List<BaseOrderRemarkOutput>>(relativePath, input);
    }

    #endregion

    #region YouxiaTripOrder
    public async Task YouxiaTripOrderStatusChange(OrderStatusChangeInput input)
    {
        var relativePath = "/YouxiaTripOrder/OrderStatusChange";
        await PostAsync<OrderStatusChangeInput>(relativePath, input);
    }

    public async Task YouxiaTripOrderPriceChange(OrderPriceChangeInput input)
    {
        var relativePath = "/YouxiaTripOrder/OrderPriceChange";
        await PostAsync<OrderPriceChangeInput>(relativePath, input);
    }
    #endregion
}
