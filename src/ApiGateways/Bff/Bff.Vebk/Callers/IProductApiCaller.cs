using Common.Caller;
using Contracts.Common.Order.DTOs.TravelLineOrder;
using Contracts.Common.Order.Messages;
using Contracts.Common.Product.DTOs.AgencyChannelCommissionSettings;
using Contracts.Common.Product.DTOs.AgencyChannelPriceSettings;
using Contracts.Common.Product.DTOs.CarHailingProduct;
using Contracts.Common.Product.DTOs.CarProduct;
using Contracts.Common.Product.DTOs.CarProductSku;
using Contracts.Common.Product.DTOs.CarProductSkuCalendarPrice;
using Contracts.Common.Product.DTOs.CarServiceItem;
using Contracts.Common.Product.DTOs.CarServiceItemCalendarPrices;
using Contracts.Common.Product.DTOs.CarTypeGrade;
using Contracts.Common.Product.DTOs.Disclaimer;
using Contracts.Common.Product.DTOs.Fields;
using Contracts.Common.Product.DTOs.Group;
using Contracts.Common.Product.DTOs.InformationTemplate;
using Contracts.Common.Product.DTOs.LineProduct;
using Contracts.Common.Product.DTOs.LineProductOpenChannelSetting;
using Contracts.Common.Product.DTOs.LineProductOpenSupplierSetting;
using Contracts.Common.Product.DTOs.LineProductSku;
using Contracts.Common.Product.DTOs.LineProductSkuCalendarPrice;
using Contracts.Common.Product.DTOs.LineProductSkuTypeItem;
using Contracts.Common.Product.DTOs.LineProductTimeSlot;
using Contracts.Common.Product.DTOs.OpenChannel;
using Contracts.Common.Product.DTOs.OpenPlatformPricingSyncLog;
using Contracts.Common.Product.DTOs.OpenSupplier;
using Contracts.Common.Product.DTOs.OpenSupplierProductSyncLog;
using Contracts.Common.Product.DTOs.ProductDefaultInformationTemplate;
using Contracts.Common.Product.DTOs.ProductInformationTemplate;
using Contracts.Common.Product.DTOs.ProductSku;
using Contracts.Common.Product.DTOs.Restaurant;
using Contracts.Common.Product.DTOs.Store;
using Contracts.Common.Product.DTOs.TicketProduct;
using Contracts.Common.Scenic.DTOs.OpenSupplier;
using Contracts.Common.Scenic.DTOs.Ticket;
using Contracts.Common.Scenic.DTOs.TicketsCalendarPrice;
using EfCoreExtensions.Abstract;
using TicketInfo = Contracts.Common.Product.DTOs.TicketProduct.TicketInfo;

namespace Bff.Vebk.Callers;

public interface IProductApiCaller : IHttpCallerBase
{
    #region lineproduct

    /// <summary>
    /// 获取线路产品信息
    /// </summary>
    /// <param name="productId"></param>
    /// <returns></returns>
    Task<GetLineProductDto> GetLineProduct(long productId);

    /// <summary>
    /// 获取线路产品信息
    /// (因为 GetLineProduct 返回的对象和底层不一致，而且在订单业务里使用，暂新开一个接口接原来参数)
    /// </summary>
    /// <param name="productId"></param>
    /// <returns></returns>
    Task<GetLineProductOutput> GetLineProductV2(long productId);

    /// <summary>
    /// 获取线路日历价格信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GetLineProductSkuDto> GetLineProductSkuCalendarPriceBySkuId(GetLineProductSkuCalendarPriceInput input);

    /// <summary>
    /// 线路产品分页查询
    /// </summary>
    Task<PagingModel<SearchLineProductOutput, SearchLineProductSummary>> SearchLineProduct(SearchLineProductInput input);

    /// <summary>
    /// 线路产品新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<AddLineProductOutput> AddLineProduct(AddLineProductInput input);

    /// <summary>
    /// 线路产品更新
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<UpdateLineProductOutput> UpdateLineProduct(UpdateLineProductInput input);

    Task<List<GetLineProductSimpleInfoOutput>> GetLineProductDetail(params long[] productIds);

    /// <summary>
    /// 批量移除线路产品
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    Task RemoveLineProducts(IEnumerable<long> ids);

    /// <summary>
    /// 获取线路产品规格信息
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    Task<List<GetLineProductSkuDetailOutput>> GetLineProductSkusByIds(params long[] ids);

    /// <summary>
    /// 更新线路产品供应商模块设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateLineProductSupplierModuleSetting(UpdateLineProductSupplierModuleSettingInput input);

    /// <summary>
    /// 获取线路产品供应商模块设置
    /// </summary>
    /// <param name="lineProductId"></param>
    /// <returns></returns>
    Task<GetLineProductSupplierModuleSettingOutput> GetLineProductSupplierModuleSetting(long lineProductId);


    /// <summary>
    /// 更新线路产品开放渠道设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateLineProductChannelModuleSetting(UpdateLineProductChannelModuleSettingInput input);
    
    /// <summary>
    /// 获取线路产品开放渠道设置
    /// </summary>
    /// <param name="lineProductId"></param>
    /// <returns></returns>
    Task<GetLineProductChannelModuleSettingOutput> GetLineProductChannelModuleSetting(long lineProductId);
    
    /// <summary>
    /// 更新线路产品开放供应商设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateLineProductOpenSupplierSetting(UpdateOpenSupplierSyncSettingInput input);

    /// <summary>
    /// 获取线路产品开放供应商设置
    /// </summary>
    /// <param name="lineProductId"></param>
    /// <returns></returns>
    Task<LineProductOpenSupplierSettingInfo> GetLineProductOpenSupplierSetting(long lineProductId);

    Task<GetLineProductCitiesOutput> GetLineProductCities(bool isCache, bool needAvailable);

    /// <summary>
    /// 更新线路产品开放渠道设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateLineProductOpenChannelSetting(UpdateOpenChannelSyncSettingInput input);
    
    /// <summary>
    /// 获取线路产品开放渠道设置
    /// </summary>
    /// <param name="lineProductId"></param>
    /// <returns></returns>
    Task<List<LineProductOpenChannelSettingInfo>> GetLineProductOpenChannelSetting(long lineProductId);

    /// <summary>
    /// 更新线路产品开放渠道时效设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateLineProductChannelTimelinessSetting(UpdateOpenChannelTimelinessSettingInput input);

    /// <summary>
    /// 获取线路产品开放渠道时效设置
    /// </summary>
    /// <param name="lineProductId"></param>
    /// <returns></returns>
    Task<GetOpenChannelTimelinessSettingOutput> GetLineProductChannelTimelinessSetting(long lineProductId);

    /// <summary>
    /// 线路产品运营模块设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateLineProductOperationModuleSetting(UpdateLineProductOperationModuleSettingInput input);

    /// <summary>
    /// 获取线路产品运营模块设置
    /// </summary>
    /// <param name="lineProductId"></param>
    /// <returns></returns>
    Task<GetLineProductOperationModuleSettingOutput> GetLineProductOperationModuleSetting(long lineProductId);
    
    /// <summary>
    /// 查询补差线路产品
    /// </summary>
    /// <returns></returns>
    Task<GetCompensationLineOutput> GetCompensationLine();
    
    #endregion

    #region LineProductSku
    /// <summary>
    /// 创建线路产品套餐
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<long> AddLineProductSku(AddLineProductSkuInput input);

    /// <summary>
    /// 编辑线路产品套餐
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task EditLineProductSku(EditLineProductSkuInput input);

    /// <summary>
    /// 移除线路产品套餐
    /// </summary>
    /// <param name="id"></param>
    Task DeleteLineProductSku(RemoveProductSkuInput input);

    /// <summary>
    /// 查询线路产品套餐详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<GetLineProductSkuDetailOutput> LineProductSkuDetail(long id);

    /// <summary>
    /// 设置LineProductSku B2B售卖状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SetLineProductSkuB2bSellingStatus(Contracts.Common.Product.DTOs.ProductSku.SetB2bSellingStatusInput input);


    /// <summary>
    /// 查询线路产品详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<GetLineProductSkuDetailOutput> GetLineProductSkuDetail(long id);

    /// <summary>
    /// 通过产品id查询线路套餐
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<List<GetLineProductSkuDetailOutput>> GetLineSkuByProductId(long id);

    /// <summary>
    /// 线路套餐 - 同步API内容信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateLineSkuApiContent(UpdateLineSkuApiContentInput input);
    #endregion

    #region LineProductTimeSlot

    /// <summary>
    /// 检查时间段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<CheckTimeSlotOutput>> CheckLineProductTimeSlot(CheckTimeSlotInput input);

    #endregion

    #region Group

    /// <summary>
    /// 获取分组下产品类型数据统计
    /// </summary>
    /// <param name="groupId"></param>
    /// <returns></returns>
    Task<List<GetProductStatisticsOutput>> GroupGetStatistics(long groupId);

    #endregion

    #region Disclaimer
    /// <summary>
    /// 查询免责声明使用详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetDisclaimerUsedDetailsOutput>> DisclaimerGetUsedDetails(GetDisclaimerUsedDetailsInput input);

    #endregion

    #region AgencyChannelPriceSettings

    /// <summary>
    /// 查询价格分组配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<AgencyChannelPriceSettingOutput>> QueryAgencyChannelPriceSettings(QueryChannelPriceInput input);


    /// <summary>
    /// 配置汇智酒店价格分组配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SetHuiZhiHotelChannelPriceSettings(SetHuiZhiHotelChannelPriceInput input);


    /// <summary>
    /// 汇智酒店渠道价查询
    /// </summary>
    Task<GetHuiZhiHotelChannelPriceOutput> GetHuiZhiHotelChannelPriceSettings(long priceGroupId);

    /// <summary>
    /// 批量配置分销商渠道价格
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    Task SetListAgencyChannelPriceSettings(SetChannelPriceDto dto);

    /// <summary>
    /// 批量移除分销商渠道价格
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    Task RemoveListAgencyChannelPriceSettings(RemoveChannelPriceDto dto);
    #endregion

    #region TicketProduct

    /// <summary>
    /// 券类产品搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchTicketInfo, SearchTicketSummary>> TicketProductSearch(SearchTicketInput input);

    /// <summary>
    /// 检查产品有效性
    /// </summary>
    /// <param name="productIds"></param>
    /// <returns></returns>
    Task<List<CheckProductValidityOutput>> CheckTicketProductValidity(params long[] productIds);


    /// <summary>
    /// 票券产品详情
    /// </summary>
    /// <param name="productId"></param>
    /// <returns></returns>
    Task<TicketInfo> GetTicketProductDetail(long productId);

    /// <summary>
    /// 查询券类规格信息
    /// </summary>
    /// <param name="skuIds"></param>
    /// <returns></returns>
    Task<List<GetProductSkusOutput>> GetTicketSkuByIds(List<long> skuIds);

    /// <summary>
    /// 查询券类产品的规格信息
    /// </summary>
    /// <param name="productId"></param>
    /// <returns></returns>
    Task<List<GetProductSkusOutput>> GetTicketProductSkus(long productId);

    #endregion

    #region ProductSku
    /// <summary>
    /// 设置ProductSku B2B售卖状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SetProductSkuB2bSellingStatus(Contracts.Common.Product.DTOs.ProductSku.SetB2bSellingStatusInput input);

    /// <summary>
    /// 设置ProductSku 上下架
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task ProductSkuSetEnabled(SetEnabledInput input);
    #endregion

    #region LineProductSkuCalendarPrice
    /// <summary>
    /// 批量更新线路产品价格设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task BatchUpdateLineProductSkuCalendarPrice(BatchUpdateInput input);

    /// <summary>
    /// 通过productId获取sku列表和对应的日历价库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetLineProductSkuCalendarPriceOutput>> GetLineProductSkuCalendarPriceByProductId(GetLineProductSkuCalendarPriceInput input);

    /// <summary>
    /// 批量移除线路套餐日历价库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task BatchDeleteLineProductSkuCalendarPrice(BatchDeleteInput input);
    
    /// <summary>
    /// 设置产品冗余数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SetLineProductRedundantData(SetProductRedundantDataInput input);
    
    #endregion

    #region CarHailingProduct
    //接送车产品详情
    Task<GetCarHailingProductOutput> GetCarHailingProduct(long id);
    #endregion

    #region CarProduct
    Task<long> AddCarProduct(AddCarProductInput input);

    Task UpdateCarProduct(UpdateCarProductInput input);

    Task<PagingModel<SearchCarProductOuput, SearchCarProductSummary>> SearchCarProduct(SearchCarProductInput input);

    Task<CarProductDetailOutput> CarProductDetail(long id);

    Task SetCarProductEnabled(SetCarProductEnabledInput input);

    Task<PagingModel<GetCarProductsAndSkuOutput>> GetCarProductsAndSku(GetProductsAndSkuInput input);

    Task<CarProductDetailCalendarPricesOutput> GetCarProductPriceDetails(CarProductDetailCalendarPricesInput input);

    Task<string> CarProductSetMozio(List<long> productIdsInput);

    /// <summary>
    /// 用车 - 查询补差产品
    /// </summary>
    /// <returns></returns>
    Task<GetCompensationCarProductOutput> GetCompensationCarProduct();
    
    #endregion

    #region CarTypeGrade
    Task<long> AddCarTypeGrade(AddCarTypeGradeInput input);

    Task UpdateCarTypeGrade(UpdateCarTypeGradeInput input);

    Task<PagingModel<SearchCarTypeGradeOuput>> SearchCarTypeGrade(SearchCarTypeGradeInput input);

    #endregion

    #region CarServiceItem
    Task<long> AddCarServiceItem(AddCarServiceItemInput input);

    Task UpdateCarServiceItem(UpdateCarServiceItemInput input);

    Task<CarServiceItemOutput> DetailCarServiceItem(long id);

    Task<PagingModel<SearchCarServiceItemOutput>> SearchCarServiceItem(SearchCarServiceItemInput input);

    Task DeleteCarServiceItem(List<long> ids);

    Task SetCarServiceItemB2bSellingStatus(Contracts.Common.Product.DTOs.ProductSku.SetB2bSellingStatusInput input);

    Task SetCarServiceItemEnabled(SetCarServiceItemEnabledInput input);
    #endregion

    #region CarProductSku
    Task<long> AddCarProductSku(AddCarProductSkuInput input);

    Task UpdateCarProductSku(UpdateCarProductSkuInput input);

    Task<CarProductSkuOutput> DetailCarProductSku(long id);

    Task DeleteCarProductSku(List<long> ids);

    Task SetCarProductSkuB2bSellingStatus(Contracts.Common.Product.DTOs.ProductSku.SetB2bSellingStatusInput input);

    Task SetCarProductSkuEnabled(SetCarProductSkuEnabledInput input);

    Task<List<CarProductSkuOutput>> GetCarProductSkDetails(CarProductSkuInput input);

    #endregion

    #region CarServiceItemCalendarPrice
    Task UpdateCarServiceItemCalendarPrice(UpdateCarServiceItemCalendarPriceInput input);

    Task BatchUpdateCarServiceItemCalendarPrice(BatchUpdateCarServiceItemCalendarPriceInput input);

    Task<List<GetCarServiceItemCalendarPriceOutput>> GetCarServiceItemCalendarPrice(GetCarServiceItemCalendarPriceInput input);
    #endregion

    #region CarProductSkuCalendarPrice

    Task BatchUpdateCarProductSkuCalendarPrice(BatchUpdateCarProductSkuCalendarPriceInput input);

    Task<List<GetCarProductSkuCalendarPriceOutput>> GetCarProductSkuCalendarPrice(GetCarProductSkuCalendarPriceInput input);

    #endregion

    #region Fields
    Task<List<FieldsOutput>> FieldsList(SearchFieldsInput input);
    #endregion

    #region InformationTemplate
    Task<long> InformationTemplateSave(SaveInformationTemplateInput input);

    Task<PagingModel<SearchInformationTemplateOutput>> InformationTemplateSearch(SearchInformationTemplateInput input);

    Task<InformationTemplateOutput> InformationTemplateDetail(long id);

    Task InformationTemplateDelete(List<long> ids);
    #endregion

    #region ProductDefaultInformationTemplate
    Task ProductDefaultTempSave(ProductDefaultInformationTemplateInput input);

    Task<List<SearchProductDefaultInformationTemplateOutput>> ProductDefaultTempList(SearchProductDefaultTemplateInput input);
    #endregion

    #region ProductInformationTemplate
    Task ProductInformationTemplateSave(SaveProductInformationTemplateInput input);

    Task<ProductInformationTemplateOutput> ProductInformationTemplateGetProductTemps(SearchProductInformationTemplateInput input);

    /// <summary>
    /// 更小颗粒度上配置的模版优先，套餐层模版设置 > 产品层模版设置 > 默认模版设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GetProductTempFieldsDetailOutput> ProductInformationTemplateGetProductTempFieldsDetail(GetProductTempFieldsDetailInput input);
    #endregion

    #region openchannel

    /// <summary>
    /// 开放平台-渠道产品校验
    /// </summary>
    /// <param name="channelProductSkuId"></param>
    /// <returns></returns>
    Task<List<OpenChannelCheckProductOutput>> CheckOpenChannelProduct(params long[] channelProductSkuIds);

    #endregion

    #region openSupplierProduct

    /// <summary>
    /// 查询供应端产品详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GetOpenSupplierProductDetailOutput> GetOpenSupplierProductDetail(GetOpenSupplierProductDetailInput input);

    /// <summary>
    /// 查询供应端产品内容
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GetOpenSupplierProductContentOutput> GetOpenSupplierProductContent(GetOpenSupplierProductContentInput input);

    /// <summary>
    /// 查询供应端基础产品信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetOpenSupplierBasicProductOutput>> GetOpenSupplierBasicProducts(GetOpenSupplierBasicProductInput input);

    
    /// <summary>
    /// 查询sku附加信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<QueryOpenSupplierSkuExtraInfoOutput>> QueryOpenSupplierSkuExtraInfo(QueryOpenSupplierSkuExtraInfoInput input);


    /// <summary>
    /// 附加信息同步落库
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    Task SyncOpenSupplierExtraInfo(SyncOpenSupplierExtraInfoInput receive);

    /// <summary>
    /// 重试匹配基础产品
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task RetryMatchOpenSupplierBasicProduct(RetryMatchBasicProductInput input);
    #endregion

    #region LineProductSkuTypeItem

    Task<List<GetOpenSupplierBasicProductOutput>> GetLineSkuItemMatchBasicProducts(GetBasicProductInput input);

    Task AddLineSkuItem(AddLineSkuTypeItemInput input);

    Task NotifySyncLineSkuItemPriceInventory(NotifySyncThirdInventoryInput input);

    Task<List<QueryLineSkuTypeItemOutput>> QueryLineSkuTypeItems(QueryLineSkuTypeItemInput input);

    Task<List<OpenSupplierQueryProductStockPollOutput>> QueryLineProductStockPoll(OpenSupplierQueryProductStockPollInput input);

    #endregion

    #region Store
    /// <summary>
    /// 分页查询资源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchStoreOutput>> SearchStore(SearchStoreInput input);

    /// <summary>
    /// 新增资源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AddStore(AddStoreInput input);

    /// <summary>
    /// 修改资源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateStore(UpdateStoreInput input);

    /// <summary>
    /// 查询资源-资源ID
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<GetStoreOutput> GetStore(long id);
    #endregion

    #region Restaurant
    Task<PagingModel<SearchRestaurantOutput>> SearchRestaurant(SearchRestaurantInput input);

    /// <summary>
    /// 查询资源-资源ID
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<GetRestaurantOutput> GetRestaurant(long id);

    /// <summary>
    /// 新增资源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AddRestaurant(AddRestaurantInput input);

    /// <summary>
    /// 修改资源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateRestaurant(UpdateRestaurantInput input);
    #endregion

    #region openplatform

    /// <summary>
    /// 查询开放平台价库同步日志
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchSyncLogOutput>> SearchOpenPlatformPricingSyncLog(SearchSyncLogInput input);

    #endregion

    #region AgencyChannelCommissionSettings
    Task SetAgencyChannelCommissionSettings(SetAgencyChannelCommissionInput input);

    Task<List<QueryAgencyChannelCommissionOutput>> QueryAgencyChannelCommissionSettings(QueryAgencyChannelCommissionInput input); 
    #endregion

    #region OpenSupplierProductSyncLog

    /// <summary>
    /// 添加api产品同步日志
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AddOpenSupplierProductSyncLog(AddProductSyncLogInput input);

    /// <summary>
    /// 查询产品信息同步日志
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchProductSyncLogOutput>> SearchOpenSupplierProductSyncLog(SearchProductSyncLogInput input);

    #endregion
}
