using Contracts.Common.Order.DTOs.ReceiptSettlementOrder;
using Contracts.Common.Order.DTOs.SettlementOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;

namespace Bff.Vebk.Models.OrderPayment;

public class GetOrderFinancialDataOutput
{
    /// <summary>
    /// 【Obsolete paymentInfos中统一展示支付信息】支付信息 
    /// </summary>
    public OrderFinancialPaymentInfo? PaymentInfo { get; set; }

    /// <summary>
    /// 支付信息
    /// </summary>
    public List<OrderFinancialPaymentInfo>? PaymentInfos { get; set; }

    /// <summary>
    /// 退款信息
    /// </summary>
    public List<OrderFinancialPaymentInfo> RefundInfo { get; set; } = new();

    /// <summary>
    /// 入账结算单(收款结算单)
    /// </summary>
    public List<GetReceivableOrderDetailOutput> ReceiptSettlementOrderInfo { get; set; } = new();

    /// <summary>
    /// 退款入账结算单(收款结算单)
    /// </summary>
    public List<GetReceivableOrderDetailOutput> RefundReceiptSettlementOrderInfo { get; set; } = new();

    /// <summary>
    /// 出账结算单
    /// </summary>
    public List<GetPayableOrderDetailOutput> PayableSettlementOrderInfo { get; set; } = new();

    /// <summary>
    /// 退款出账结算单
    /// </summary>
    public List<GetPayableOrderDetailOutput> RefundPayableSettlementOrderInfo { get; set; } = new();

    /// <summary>
    /// 毛利相关信息
    /// </summary>
    public OrderFinancialGrossMarginInfo GrossMarginInfo { get; set; }

    /// <summary>
    /// 抵冲单信息
    /// </summary>
    public List<FinancialOffsetOrderInfo> OffsetOrderInfos { get; set; } = new();

    #region 抵充单汇总信息

    /// <summary>
    /// 总销售信息
    /// </summary>
    public OffsetOrderCollectInfo TotalSalesInfo { get; set; }

    /// <summary>
    /// 总采购信息
    /// </summary>
    public OffsetOrderCollectInfo TotalPurchaseInfo { get; set; }

    /// <summary>
    /// 毛利
    /// </summary>
    public OffsetOrderCollectInfo GrossProfitInfo { get; set; }

    /// <summary>
    /// 渠道平台佣金
    /// </summary>
    public OffsetOrderCollectInfo ChannelCommissionInfo { get; set; }

    /// <summary>
    /// 保险信息
    /// </summary>
    public OffsetOrderCollectInfo InsureRecordInfo { get; set; }
    #endregion

    /// <summary>
    /// 线上可退金额
    /// </summary>
    public OffsetOrderCollectInfo ReceiptRefundableInfo { get; set; }

    /// <summary>
    /// 订单基础数据汇总
    /// </summary>
    public BaseStatisticsInfo BaseStatisticsInfo { get; set; }
}

/// <summary>
/// 基础统计数据
/// </summary>
public class BaseStatisticsInfo
{ 
    /// <summary>
    /// 原采购总价
    /// </summary>
    public decimal OrgPurchaseAmount { get; set; }

    /// <summary>
    /// 实付总价
    /// </summary>
    public decimal OrgPayAmount { get; set; }
}

public class OffsetOrderCollectInfo
{
    public decimal Amount { get; set; }

    /// <summary>
    /// 币种
    /// </summary>
    public string CurrencyCode { get; set; }
}

public class OrderFinancialPaymentInfo
{
    /// <summary>
    /// 订单id/退款单id
    /// </summary>
    public long OrderId { get; set; }

    /// <summary>
    /// 币种
    /// </summary>
    public string CurrencyCode { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal? Amount { get; set; }

    /// <summary>
    /// 供应商应退采购金额
    /// </summary>
    public decimal CostAmount { get; set; }

    /// <summary>
    /// 采购币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 支付类型
    /// </summary>
    public PayType? PaymentType { get; set; }

    /// <summary>
    /// 付款人名称(C端用户名/分销商名称)
    /// </summary>
    public string UserName { get; set; }

    /// <summary>
    /// 付款人类型
    /// </summary>
    public Contracts.Common.Payment.Enums.UserType UserType { get; set; }

    /// <summary>
    /// 流水号
    /// </summary>
    public string UniqueOrderNo { get; set; }
}

public class OrderFinancialGrossMarginInfo
{
    /// <summary>
    /// 支付手续费
    /// </summary>
    public decimal PaymentFee { get; set; }

    /// <summary>
    /// 平台佣金
    /// </summary>
    public decimal PlatformCommission { get; set; }

    /// <summary>
    /// 达人佣金
    /// </summary>
    public decimal DarenBonusAmount { get; set; }

    /// <summary>
    /// 毛利
    /// </summary>
    public decimal? GrossMargin { get; set; }

    public string CurrencyCode { get; set; } = Currency.CNY.ToString();
}

public class FinancialOffsetOrderInfo
{
    /// <summary>
    /// 主订单id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 业务单号，酒店订单Id / 邮寄订单Id / 预约单Id /
    /// </summary>
    public long BusinessOrderId { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 产品Sku名称
    /// </summary>
    public string ProductSkuName { get; set; }

    /// <summary>
    /// 抵冲单列表
    /// </summary>
    public List<FinancialOffsetOrderItem> OffsetOrderItems { get; set; } = new();
}

public class FinancialOffsetOrderItem
{
    /// <summary>
    /// 抵冲单id
    /// </summary>
    public long OffsetOrderId { get; set; }

    /// <summary>
    /// 主订单id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 业务单号，酒店订单Id / 邮寄订单Id / 预约单Id /
    /// </summary>
    public long BusinessOrderId { get; set; }

    /// <summary>
    /// 结算单id
    /// </summary>
    public long? SettlementOrderId { get; set; }

    /// <summary>
    /// 分销商Id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 供应商Id
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 抵冲金额类型
    /// </summary>
    public OffsetOrderMoneyType MoneyType { get; set; }

    /// <summary>
    /// 抵冲金额 绝对值
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 抵冲金额 正负值
    /// </summary>
    public decimal OffsetAmount { get; set; }

    /// <summary>
    /// 抵冲金额币种
    /// </summary>
    public string CurrencyCode { get; set; }

    /// <summary>
    /// 业务类型
    /// </summary>
    public OffsetOrderBusinessType BusinessType { get; set; }

    /// <summary>
    /// 抵冲类型
    /// </summary>
    public OffsetOrderType OffsetType { get; set; }

    /// <summary>
    /// 抵冲单状态
    /// </summary>
    public OffsetOrderStatus Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string Remark { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string CreatorName { get; set; }

    /// <summary>
    /// 更新人名称
    /// </summary>
    public string UpdaterName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 图片
    /// </summary>
    public List<string>? Images { get; set; }

    /// <summary>
    /// 抵冲单审批状态
    /// </summary>
    public OffsetOrderDingtalkApplyAuditStatus? AuditStatus { get; set; }

    /// <summary>
    /// 钉钉审批单号
    /// </summary>
    public string? DingtalkBusinessId { get; set; }

    /// <summary>
    /// 处理状态
    /// 处理中（审核中，审核通过-》触发相关事件；不需要审核时，直接触发相关事件）
    /// 成功（触发相关事件完成），结算时只查询处理成功的    
    /// 失败（审核拒绝，审核撤销，触发事件失败）
    /// </summary>
    public OffsetOrderProcessingStatus? ProcessStatus { get; set; }

    /// <summary>
    /// 处理失败原因
    /// </summary>
    public string? ProcessErrorMsg { get; set; }
}