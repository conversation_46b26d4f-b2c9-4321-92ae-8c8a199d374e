using AutoMapper;
using Bff.Vebk.Models.OpenChannelSyncFailOrder;
using Bff.Vebk.Models.ScenicTicketOrder.OTA;
using Bff.Vebk.Models.TravelLineOrder.OTA;
using Contracts.Common.Order.DTOs.OpenChannelOrder;
using Contracts.Common.Order.DTOs.OrderManualVoucher;
using Contracts.Common.Order.DTOs.ScenicTicketOrder;
using Contracts.Common.Order.DTOs.ScenicTicketOrder.OTA;
using Contracts.Common.Order.DTOs.TravelLineOrder;
using Contracts.Common.Order.Enums;
using EfCoreExtensions.Abstract;
using System.Text.RegularExpressions;

namespace Bff.Vebk.Models.ScenicTicketOrder;

public class ScenicTicketOrderMapProfile : Profile
{
    public ScenicTicketOrderMapProfile()
    {
        //OTA
        CreateMap<CreateScenicOTAOrderBffInput, CreateScenicOTAOrderInput>()
            .ForMember(f => f.ContactsEmail, s => s.MapFrom(m => Regex.Replace(m.ContactsEmail, @"\s", "")));
        CreateMap<CreateScenicOTAOrderResult, CreateScenicOTAOrderBffResult>();
        CreateMap<CreateScenicOTAOrderOutput, CreateScenicOTAOrderBffOutput>();
        CreateMap<ScenicTicketOrderTravelerInfo, ScenicTicketOrderTravelerBffInfo>()
            .ForMember(f => f._birthday, c => c.MapFrom(m => m.Birthday))
            .ForMember(f => f._cardValidDate, c => c.MapFrom(m => m.CardValidDate));
        CreateMap<ScenicTicketBaseOrderInfo, ScenicTicketBaseOrderBffInfo>()
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => MapMixBaseOrderStatus(src.Status)));
        CreateMap<ScenicTicketOrderDetailOutput, ScenicTicketOrderDetailBffOutput>();
        CreateMap<CreateScenicOTAOrderBffInput, CreateScenicOTACombinationOrderInput>();
        CreateMap<ManualDeliveryScenicTicketVoucherBffInput, ManualDeliveryScenicTicketVoucherInput>();
        CreateMap<GetChannelOrderInfoOutput, GetChannelOrderInfoBffOutput>();
        CreateMap<TicketOrderTravelerInfo, ScenicTicketOrderTravelerInfo>();
        CreateMap<CreateScenicOTAOrderBffInput,GetSaveOrderFieldsInputDto>();
        CreateMap<GetSyncFailScenicTicketOrderDetailBffOutput, GetChannelOrderInfoBffOutput>();

        //订单分页查询
        CreateMap<SearchScenicTicketOrderBffInput, SearchScenicTicketOrderInput>()
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => MapBaseOrderStatus(src.Status)));
        CreateMap<ScenicTicketOrderStatusCount, ScenicTicketOrderStatusCountBffOutput>();
        CreateMap<SearchScenicTicketOrderOutput, SearchScenicTicketOrderBffOutput>()
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => MapMixBaseOrderStatus(src.Status)));
        CreateMap<PagingModel<SearchScenicTicketOrderOutput, ScenicTicketOrderStatusCount>,
            PagingModel<SearchScenicTicketOrderBffOutput, ScenicTicketOrderStatusCountBffOutput>>();

        //订单详情查询
        CreateMap<ScenicTicketOrderInfo, ScenicTicketOrderDetailBffInfo>();

        #region 转字段模板
        //异常单详情查询-转字段模板
        CreateMap<ScenicTicketOrderTravelerInfo, OrderTravelerOutput>()
            .ForMember(dest => dest.Phone, opt => opt.MapFrom(src => src.PhoneNumber));
        //ota下单-转字段模板--
        CreateMap<TicketOrderTravelerInfo, OrderTravelerOutput>()
           .ForMember(dest => dest.Phone, opt => opt.MapFrom(src => src.PhoneNumber));
        CreateMap<ScenicTicketOTAOrderContactBffItem, TravelLineOTAOrderContactBffItem>();
        //ota下单-转字段模板-- 
        #endregion
        
        CreateMap<ChannelOrderContactItem, ScenicTicketOTAOrderContactBffItem>();
    }

    /// <summary>
    /// 映射转换订单状态
    /// </summary>
    /// <param name="status"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    private ScenicTicketOrderMixStatus MapMixBaseOrderStatus(BaseOrderStatus status)
    {
        return status switch
        {
            BaseOrderStatus.WaitingForPay => ScenicTicketOrderMixStatus.WaitingForPay,
            BaseOrderStatus.UnFinished => ScenicTicketOrderMixStatus.UnFinished,
            BaseOrderStatus.Finished => ScenicTicketOrderMixStatus.Finished,
            BaseOrderStatus.Closed => ScenicTicketOrderMixStatus.Closed,
            _ => throw new ArgumentOutOfRangeException(nameof(status), status, null)
        };
    }

    private BaseOrderStatus? MapBaseOrderStatus(ScenicTicketOrderMixStatus? status)
    {
        return status switch
        {
            ScenicTicketOrderMixStatus.WaitingForPay => BaseOrderStatus.WaitingForPay,
            ScenicTicketOrderMixStatus.UnFinished => BaseOrderStatus.UnFinished,
            ScenicTicketOrderMixStatus.Finished => BaseOrderStatus.Finished,
            ScenicTicketOrderMixStatus.Closed => BaseOrderStatus.Closed,
            _ => null
        };
    }
}