using Contracts.Common.Order.DTOs.OpenSupplierOrder;
using Contracts.Common.Order.DTOs.OrderFieldInformation;
using Contracts.Common.Order.Enums;
using Contracts.Common.Product.DTOs.ProductInformationTemplate;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.TicketsCombinationOrder;

/// <summary>
/// 创建组合单
/// </summary>
public class CreateCombinationOrderBffInput
{
    #region 订单基础信息

    /// <summary>
    /// 异常单
    /// <remarks>组合异常单id</remarks>
    /// </summary>
    public long? SyncFailOrderId { get; set; }

    /// <summary>
    /// 门票组合id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long TicketsCombinationId { get; set; }
    
    /// <summary>
    /// 门票组合套餐id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long TicketsCombinationPackageId { get; set; }
    
    /// <summary>
    /// 分销商id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long AgencyId { get; set; }
    
    /// <summary>
    /// 来源渠道
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public SellingChannels SellingChannels { get; set; } = SellingChannels.B2b;
    
    /// <summary>
    /// 渠道编码
    /// 组合单只有一个编码
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string ChannelOrderNo { get; set; }
    
    /// <summary>
    /// 订单备注
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string Remark { get; set; }
    
    /// <summary>
    /// 采购备注
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string SupplierOrderRemark { get; set; }
    
    /// <summary>
    /// 出行日期
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public DateTime TravelDate { get; set; } = DateTime.Today;
    
    #endregion

    #region 销售信息

    /// <summary>
    /// 销售信息
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public List<CreateCombinationOrderSalesBffInfo> SalesInfos { get; set; } = new();

    #endregion

    #region 字段模板信息

    /// <summary>
    /// 字段模板信息 - 出行信息
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<CreateCombinationOrderFieldBffInfo> OrderFieldInfos { get; set; } = new();

    #endregion

    #region 联系人信息

    /// <summary>
    /// 联系人
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string ContactName { get; set; }

    /// <summary>
    /// 联系人手机 - 号码
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string ContactPhoneNumber { get; set; }

    /// <summary>
    /// 联系人手机 - 区号
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string ContactAreaCode { get; set; }

    /// <summary>
    /// 联系人邮箱
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string ContactEmail { get; set; }

    /// <summary>
    /// 英文名
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string EnglishName { get; set; }

    /// <summary>
    /// 英文姓
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string EnglishSurname { get; set; }

    #endregion

    #region 附加信息

    /// <summary>
    /// 附加信息
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<CreateCombinationOrderExtraBffInfos> OrderExtraInfos { get; set; } = new();

    #endregion
}

public class CreateCombinationOrderSalesBffInfo
{
    /// <summary>
    /// 门票id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long TicketId { get; set; }
    
    /// <summary>
    /// 时段id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? TimeSlotId { get; set; }
    
    /// <summary>
    /// 购买数量
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public int Quantity { get; set; }

    /// <summary>
    /// 采购单价
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public decimal CostPrice { get; set; }

    /// <summary>
    /// 售卖单价
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public decimal SellingPrice { get; set; }

    /// <summary>
    /// 优惠总额
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// 实收金额 可选参数
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public decimal PaymentAmount { get; set; }
}

public class CreateCombinationOrderFieldBffInfo
{
    /// <summary>
    /// 门票id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long TicketId { get; set; }
    
    /// <summary>
    /// 时段id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? TimeSlotId { get; set; }
    
    /// <summary>
    /// 订单字段信息
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<SaveOrderFieldInformationTypeDto> OrderFields { get; set; } = new();
}

public class CreateCombinationOrderExtraBffInfos
{
    /// <summary>
    /// 门票id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long TicketId { get; set; }
    
    /// <summary>
    /// 时段id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? TimeSlotId { get; set; }
    
    /// <summary>
    /// 供应商 - 订单附加信息
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<AddOpenSupplierOrderExtraInfoItem> OrderExtraInfos { get; set; } = new();
}

/// <summary>
///  组合订单 - 联系人字段模板赋值
/// </summary>
public class CombinationOrderContactToTempFieldDto
{
    public List<ProductInformationTemplateDetail> Templates { get; set; } = new();
    
    #region 联系人信息

    /// <summary>
    /// 联系人
    /// </summary>
    public string ContactName { get; set; }

    /// <summary>
    /// 联系人手机 - 号码
    /// </summary>
    public string ContactPhoneNumber { get; set; }

    /// <summary>
    /// 联系人手机 - 区号
    /// </summary>
    public string ContactAreaCode { get; set; }

    /// <summary>
    /// 联系人邮箱
    /// </summary>
    public string ContactEmail { get; set; }

    /// <summary>
    /// 英文名
    /// </summary>
    public string EnglishName { get; set; }

    /// <summary>
    /// 英文姓
    /// </summary>
    public string EnglishSurname { get; set; }

    #endregion
    
}

public class SyncFailCombinationOrderContentDataDto
{
    public bool NeedConfirmOrder { get; set; }
}