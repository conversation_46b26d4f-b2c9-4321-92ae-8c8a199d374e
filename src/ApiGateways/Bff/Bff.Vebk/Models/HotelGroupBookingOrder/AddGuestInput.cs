using Contracts.Common.Order.Enums;

namespace Bff.Vebk.Models.HotelGroupBookingOrder;

public class AddGuestInput
{
    /// <summary>
    /// 团房单id
    /// </summary>
    public long GroupBookingOrderId { get; set; }

    /// <summary>
    /// 订单项id集合
    /// </summary>
    public long[] GroupBookingOrderItemIds { get; set; }

    /// <summary>
    /// 入住人附件文件路径
    /// </summary>
    public string? FilePath { get; set; }

    /// <summary>
    /// 入住人信息 入住人姓名必填
    /// </summary>
    public List<OrderGuestInfoDto> GuestInfos { get; set; } = new();
}

public class OrderGuestInfoDto
{
    /// <summary>
    /// 入住人房间序号 1,2,3...
    /// </summary>
    public int? RoomNumber { get; set; }

    /// <summary>
    /// 名
    /// </summary>
    public string? FirstName { get; set; }

    /// <summary>
    /// 姓
    /// </summary>
    public string? LastName { get; set; }

    /// <summary>
    /// 入住人姓名
    /// </summary>
    public string? GuestName { get; set; }

    /// <summary>
    /// 入住人类型 默认成人
    /// </summary>
    public HotelOrderGuestType HotelOrderGuestType { get; set; } = HotelOrderGuestType.Adult;
}