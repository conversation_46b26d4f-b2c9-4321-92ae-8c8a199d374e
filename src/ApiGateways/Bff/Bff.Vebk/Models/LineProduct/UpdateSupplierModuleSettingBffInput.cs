using Contracts.Common.Order.DTOs.Insure;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.LineProduct;

public class UpdateSupplierModuleSettingBffInput
{
    /// <summary>
    /// 产品id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long LineProductId { get; set; }
    
    /// <summary>
    /// 供应商
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long SupplierId { get; set; }
    
    /// <summary>
    /// 活动id
    /// <remarks>供应端 - productid</remarks>
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? ActivityId { get; set; }
    
    /// <summary>
    /// 供应商-服务信息Id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? SupplierServiceInfoId { get; set; }
    
    /// <summary>
    /// 供应商-服务信息名称
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? SupplierServiceInfoName { get; set; }

    /// <summary>
    /// 产品关联保险
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public SaveInsureProductRelationsInput? InsureProductRelation { get; set; }
    
    /// <summary>
    /// 采购折扣比例[0-100] 2位小数
    /// <remarks>
    /// <para>如15表示为15%的折扣</para>
    /// <para>API对接直接读取供应端数据</para>
    /// </remarks>
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public decimal CostDiscountRate { get; set; }
}