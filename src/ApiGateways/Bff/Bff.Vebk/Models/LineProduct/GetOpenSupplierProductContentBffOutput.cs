using Bff.Vebk.Models.ScenicTicket;

namespace Bff.Vebk.Models.LineProduct;

public class GetOpenSupplierProductContentBffOutput
{

    /// <summary>
    /// 线路产品内容信息
    /// </summary>
    public GetOpenSupplierLineProductContentItem ProductContentItem { get; set; }

    /// <summary>
    /// 线路套餐内容信息
    /// </summary>
    public List<GetOpenSupplierLineSkuContentItem> SkuContentItems { get; set; } = new();
    
    /// <summary>
    /// 同步字段信息
    /// </summary>
    public List<GetOpenSupplierProductSyncFieldBffOutput> SyncFields { get; set; } = new();
}

/// <summary>
/// 线路产品内容信息
/// </summary>
public class GetOpenSupplierLineProductContentItem
{
    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// 图片
    /// </summary>
    public List<string> ProductPictures { get; set; } = new();
    
    /// <summary>
    /// 亮点(卖点)描述
    /// </summary>
    public string SellPointDescribe { get; set; }
    
    /// <summary>
    /// 产品详情(图文内容)
    /// </summary>
    public string Content { get; set; }
}

/// <summary>
/// 线路套餐内容信息
/// </summary>
public class GetOpenSupplierLineSkuContentItem
{
    /// <summary>
    /// 线路套餐id
    /// </summary>
    public long LineProductSkuId { get; set; }
    
    /// <summary>
    /// 费用包含
    /// </summary>
    public string? FeeNote { get; set; }

    /// <summary>
    /// 费用不含
    /// </summary>
    public string? FeeNotNote { get; set; }
    
    /// <summary>
    /// 有效期描述
    /// </summary>
    public string? ValidityDescription { get; set; }
    
    /// <summary>
    /// 注意事项
    /// </summary>
    public string? Precautions { get; set; }

    /// <summary>
    /// 使用方式
    /// </summary>
    public string? UsageInstructions { get; set; }

    /// <summary>
    /// 其他说明
    /// </summary>
    public string? OtherInstructions { get; set; }
    
    /// <summary>
    /// 取消政策
    /// </summary>
    public string? CancellationPolicy { get; set; }
}