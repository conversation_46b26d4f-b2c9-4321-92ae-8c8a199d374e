using Contracts.Common.Product.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.LineProduct;

/// <summary>
/// 线路 - 获取api供应商产品内容信息
/// </summary>
public class GetOpenSupplierProductContentBffInput
{
    /// <summary>
    /// 线路产品id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long LineProductId { get; set; }
    
    /// <summary>
    /// ActivityId
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string ActivityId { get; set; }

    /// <summary>
    ///供应商id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long SupplierId { get; set; }
    
    /// <summary>
    /// 语言类型
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public OpenSupplierProductLanguageType LanguageType { get; set; } = OpenSupplierProductLanguageType.Chinese;
}