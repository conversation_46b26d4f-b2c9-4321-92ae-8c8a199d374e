using Contracts.Common.Product.DTOs.ProductOperatorUser;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.LineProduct;

public class UpdateOperationModuleSettingBffInput
{
    /// <summary>
    /// 线路产品id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long LineProductId { get; set; }
    
    /// <summary>
    /// 产品人id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? DevelopUserId { get; set; }

    /// <summary>
    /// 平台运营人
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<ProductOperatorUserDto> ProductOperatorUser { get; set; } = new();

    /// <summary>
    /// 海报
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<string> ProductPosters { get; set; } = new();

    /// <summary>
    /// 售前客服
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long PreSaleStaff { get; set; }

    /// <summary>
    /// 售后客服
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long AfterSaleStaff { get; set; }
}