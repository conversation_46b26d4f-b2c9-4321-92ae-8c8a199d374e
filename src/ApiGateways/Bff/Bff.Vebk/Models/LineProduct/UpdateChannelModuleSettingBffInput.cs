using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.LineProduct;

public class UpdateChannelModuleSettingBffInput
{
    /// <summary>
    /// 产品id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long LineProductId { get; set; }

    #region 售卖设置

    /// <summary>
    /// 售卖日期 - 起  Null表示不限
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public DateTime? SellingDateBegin { get; set; }

    /// <summary>
    /// 售卖日期 - 止  Null表示不限
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public DateTime? SellingDateEnd { get; set; }

    /// <summary>
    /// 上架/下架
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public bool Enabled { get; set; } = false;

    #endregion
}