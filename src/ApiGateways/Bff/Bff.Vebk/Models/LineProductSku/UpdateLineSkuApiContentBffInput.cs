using Contracts.Common.Product.DTOs.LineProductSku;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.LineProductSku;

public class UpdateLineSkuApiContentBffInput
{
    /// <summary>
    /// 套餐信息
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public List<UpdateLineSkuApiContentBffItem> SkuContentItems { get; set; } = new();
}

public class UpdateLineSkuApiContentBffItem
{
    /// <summary>
    /// 线路产品id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long LineProductSkuId { get; set; }
    
    /// <summary>
    /// 费用包含
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? FeeNote { get; set; }

    /// <summary>
    /// 费用不含
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? FeeNotNote { get; set; }
    
    /// <summary>
    /// 有效期描述
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? ValidityDescription { get; set; }
    
    /// <summary>
    /// 注意事项
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? Precautions { get; set; }

    /// <summary>
    /// 使用方式
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? UsageInstructions { get; set; }

    /// <summary>
    /// 其他说明
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? OtherInstructions { get; set; }
    
    /// <summary>
    /// 取消政策
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? CancellationPolicy { get; set; }
}