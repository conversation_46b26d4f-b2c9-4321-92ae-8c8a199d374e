using AutoMapper;
using Bff.Vebk.Models.Insure;
using Bff.Vebk.Models.LineProductSku;
using Contracts.Common.Order.DTOs.Insure;
using Contracts.Common.Product.DTOs.LineProduct;
using Contracts.Common.Product.DTOs.LineProductSku;
using Contracts.Common.Product.DTOs.LineProductSkuTypeItem;
using EfCoreExtensions.Abstract;

namespace Bff.Vebk.Models.LineProduct;

public class LineProductSkuMapProfiles : Profile
{
    public LineProductSkuMapProfiles()
    {
        CreateMap<SaveInsureProductSkuBffInput, Contracts.Common.Order.DTOs.Insure.SaveInsureProductSkuRelationInput>();
        CreateMap<GetInsureProductSkuRelationOutput, InsureProductSkuRelationBffOutput>();
        CreateMap<GetLineProductSkuDetailOutput, DetailOutput>();

        // LineProductSkuTypeItem
        CreateMap<QueryLineSkuTypeItemOutput, QuerySkuTypeItemBffOutput>();

        CreateMap<UpdateLineSkuApiContentBffItem, UpdateLineSkuApiContentItem>();
        CreateMap<UpdateLineSkuApiContentBffInput, UpdateLineSkuApiContentInput>();
    }
}