using Bff.Vebk.Models.Insure;
using Contracts.Common.Product.DTOs.LineProduct;
using Contracts.Common.Product.DTOs.LineProductSku;
using Contracts.Common.Product.Enums;

namespace Bff.Vebk.Models.LineProductSku;

public class DetailOutput
{
    public long Id { get; set; }
    
    /// <summary>
    /// 采购来源类型
    /// </summary>
    public LineProductPurchaseSourceType PurchaseSourceType { get; set; }
    
    /// <summary>
    /// 租户id
    /// </summary>
    public long TenantId { get; set; }

    /// <summary>
    /// 上架/下架
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// sku最低日历价
    /// </summary>
    public decimal? MinSellingPrice { get; set; }
    
    /// <summary>
    /// 线路产品Id
    /// </summary>
    public long LineProductId { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 含住宿
    /// </summary>
    public bool IncludedAccommodation { get; set; }

    /// <summary>
    /// 费用包含
    /// </summary>
    public List<FeeIncludeDto> FeeIncludes { get; set; }
    
    /// <summary>
    /// 时段场次名称
    /// </summary>
    public string? TimeSlotName { get; set; }

    /// <summary>
    /// 线路套餐票种信息
    /// </summary>
    public List<LineProductSkuTypeItemInfo> SkuTypeItemInfos { get; set; } = new();
    
    /// <summary>
    /// 采购折扣比例[0-100] 2位小数
    /// <remarks>
    /// <para>如15表示为15%的折扣</para>
    /// <para>API对接直接读取供应端数据</para>
    /// </remarks>
    /// </summary>
    public decimal? CostDiscountRate { get; set; }
    
    /// <summary>
    /// 保险和产品sku配置
    /// </summary>
    public InsureProductSkuRelationBffOutput? InsureProductSku { get; set; }
    
        
    /// <summary>
    /// 费用包含
    /// </summary>
    public string? FeeNote { get; set; }

    /// <summary>
    /// 费用不含
    /// </summary>
    public string? FeeNotNote { get; set; }
    
    /// <summary>
    /// 有效期描述
    /// </summary>
    public string? ValidityDescription { get; set; }
    
    /// <summary>
    /// 注意事项
    /// </summary>
    public string? Precautions { get; set; }

    /// <summary>
    /// 使用方式
    /// </summary>
    public string? UsageInstructions { get; set; }

    /// <summary>
    /// 其他说明
    /// </summary>
    public string? OtherInstructions { get; set; }
    
    /// <summary>
    /// 取消政策
    /// </summary>
    public string? CancellationPolicy { get; set; }
}
