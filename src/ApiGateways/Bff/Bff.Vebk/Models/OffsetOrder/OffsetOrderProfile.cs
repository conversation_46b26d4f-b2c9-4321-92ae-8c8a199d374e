using AutoMapper;
using Contracts.Common.Order.DTOs.OffsetOrder;
using EfCoreExtensions.Abstract;

namespace Bff.Vebk.Models.OffsetOrder;

public class OffsetOrderProfile : Profile
{
    public OffsetOrderProfile()
    {
        CreateMap<SearchOffsetOrderOutput, SearchOffsetOrderPageOutput>();
        CreateMap<PagingModel<SearchOffsetOrderOutput>, PagingModel<SearchOffsetOrderPageOutput>>();
        CreateMap<GetOffsetOrderListOutput, OffsetOrderDetailBffOutput>();
        CreateMap<GetOffsetOrderDetailOutput, OffsetOrderDetailBffOutput>();

        CreateMap<ExportSearchOffsetOrderBffInput,SearchOffsetOrderInput>();
        CreateMap<SearchOffsetOrderOutput,ExportSearchOffsetOrderBffOutput>();
    }
}
