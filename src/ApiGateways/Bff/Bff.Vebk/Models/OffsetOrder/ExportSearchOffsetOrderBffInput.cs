using Contracts.Common.Order.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.OffsetOrder;

public class ExportSearchOffsetOrderBffInput
{
    /// <summary>
    /// 抵冲单id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? Id { get; set; }

    /// <summary>
    /// 主订单id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? BaseOrderId { get; set; }
    
    /// <summary>
    /// 业务单号，酒店订单Id / 邮寄订单Id / 预约单Id /
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? BusinessOrderId { get; set; }
    
    /// <summary>
    /// 分销商Id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? AgencyId { get; set; }

    /// <summary>
    /// 供应商Id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? SupplierId { get; set; }
    
    /// <summary>
    /// 业务类型
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public OffsetOrderBusinessType? BusinessType { get; set; }

    /// <summary>
    /// 抵冲类型
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public OffsetOrderType? OffsetType { get; set; }
    
    /// <summary>
    /// 抵冲单状态
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public OffsetOrderStatus? Status { get; set; }

    /// <summary>
    /// 开始日期
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public DateTime? BeginTime { get; set; }
    
    /// <summary>
    /// 结束日期
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 工单id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? WorkOrderId { get; set; }

    /// <summary>
    /// HOP工单号
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? HopWorkOrderNumber { get; set; }
}