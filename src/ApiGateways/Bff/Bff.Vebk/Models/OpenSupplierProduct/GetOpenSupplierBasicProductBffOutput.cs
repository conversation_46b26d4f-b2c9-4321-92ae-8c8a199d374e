using Contracts.Common.Product.Enums;

namespace Bff.Vebk.Models.OpenSupplierProduct;

/// <summary>
/// 查询供应端产品-维护的基础数据
/// </summary>
public class GetOpenSupplierBasicProductBffOutput
{
    /// <summary>
    /// 供应商id
    /// </summary>
    public long SupplierId { get; set; }
    
    /// <summary>
    /// 供应端- 产品id
    /// </summary>
    public string SupplierProductId { get; set; }

    /// <summary>
    /// 供应端 - 产品名称
    /// </summary>
    public string SupplierProductName { get; set; }

    /// <summary>
    /// 套餐选项列表
    /// </summary>
    public List<OpenSupplierBasicProductOptionItem> OptionItems { get; set; } = new();
}

public class OpenSupplierBasicProductOptionItem
{
    /// <summary>
    /// 供应端 - 套餐选项id
    /// </summary>
    public string? SupplierOptionId { get; set; }
    
    /// <summary>
    /// 供应端 - 套餐选项名称
    /// </summary>
    public string? SupplierOptionName { get; set; }
    
    /// <summary>
    /// 场次选项
    /// </summary>
    public List<OpenSupplierBasicProductTimeSlotItem> TimeSlotItems { get; set; } = new();
}

public class OpenSupplierBasicProductTimeSlotItem
{
    /// <summary>
    /// 时段场次id
    /// </summary>
    public string? SupplierTimeSlotId { get; set; }
    
    /// <summary>
    /// 时段场次名称
    /// </summary>
    public string? SupplierTimeSlotName { get; set; }

    /// <summary>
    /// sku列表
    /// </summary>
    public List<OpenSupplierBasicProductSkuItem> SkuItems { get; set; } = new();
}

public class OpenSupplierBasicProductSkuItem
{
    /// <summary>
    /// saas - 匹配Id
    /// </summary>
    public long? MatchId { get; set; }

    /// <summary>
    /// 匹配状态
    /// </summary>
    public BasicProductMatchStatus MatchStatus { get; set; }
    
    /// <summary>
    /// sku id
    /// </summary>
    public string? SupplierSkuId { get; set; }
    
    /// <summary>
    /// sku name
    /// </summary>
    public string? SupplierSkuName { get; set; }
    
    /// <summary>
    /// 票种类型
    /// </summary>
    public LineSkuPriceType SkuPriceType { get; set; }
}