using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.OpenSupplierProduct;

public class RetryMatchBasicProductBffInput
{
    /// <summary>
    /// 供应商id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long SupplierId { get; set; }

    /// <summary>
    /// 供应商productId
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string SupplierProductId { get; set; }

    /// <summary>
    /// 供应商skuId列表
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public List<string> SupplierSkuIds { get; set; } = new();
}