using AutoMapper;
using Contracts.Common.Resource.DTOs.MaxkbBi;
using Contracts.Common.Resource.DTOs.Scenic;
using Contracts.Common.Tenant.DTOs.Tenant;
using EfCoreExtensions.Abstract;

namespace Bff.Manage.Models.Resource;

public class ResourceProfile : Profile
{
    public ResourceProfile()
    {
        CreateMap<GetScenicSpotDetailOutput, Contracts.Common.Scenic.DTOs.ScenicSpot.SyncScenicSpotInput>()
               .ForMember(x => x.ScenicSpotResourceId, m => m.MapFrom(f => f.Id));

        CreateMap<PromtHotelDto, PromtGroupHotelDto>();
    }
}
