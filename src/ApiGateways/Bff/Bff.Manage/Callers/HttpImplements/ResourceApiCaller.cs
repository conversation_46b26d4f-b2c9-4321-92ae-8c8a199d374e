using Common.Caller;
using Common.ServicesHttpClient;
using Contracts.Common.Resource.DTOs;
using Contracts.Common.Resource.DTOs.AggregateResource;
using Contracts.Common.Resource.DTOs.Airport;
using Contracts.Common.Resource.DTOs.EsTradingArea;
using Contracts.Common.Resource.DTOs.Hotel;
using Contracts.Common.Resource.DTOs.HotelGroupRecommend;
using Contracts.Common.Resource.DTOs.HotelMeeting;
using Contracts.Common.Resource.DTOs.MaxkbBi;
using Contracts.Common.Resource.DTOs.Scenic;
using Contracts.Common.Resource.DTOs.ThirdHotel;
using EfCoreExtensions.Abstract;
using Microsoft.Extensions.Options;

namespace Bff.Manage.Callers.HttpImplements;

public class ResourceApiCaller : HttpCallerBase, IResourceApiCaller
{
    public ResourceApiCaller(IOptions<ServicesAddress> options, IHttpClientFactory httpClientFactory)
        : base(options.Value.Resource, httpClientFactory)
    {
    }

    #region ScenicSpot

    public async Task<GetScenicSpotDetailOutput> Detail(long id)
    {
        string relativePath = $"/scenicspot/detail?id={id}";
        return await GetAsync<GetScenicSpotDetailOutput>(relativePath);
    }

    #endregion

    #region ThirdHotel

    public Task<GetThirdHotelPriceOutput> GetThirdHotelPrice(GetThirdHotelPriceInput input)
    {
        string relativePath = "/ThirdHotel/GetPrice";
        return PostAsync<GetThirdHotelPriceInput, GetThirdHotelPriceOutput>(relativePath, input);
    }

    #endregion

    #region Hotel

    public Task<PagingModel<SearchHotelOutput>> HotelSearch(SearchHotelInput input)
    {
        var relativePath = "/Hotel/Search";
        return PostAsync<SearchHotelInput, PagingModel<SearchHotelOutput>>(relativePath, input);
    }

    public async Task<List<GetRoomsByHotelIdsOutput>> GetRoomsByHotelIds(GetRoomsByHotelIdsInput input)
    {
        var relativePath = "/Hotel/GetRoomsByHotelIds";
        return await PostAsync<GetRoomsByHotelIdsInput, List<GetRoomsByHotelIdsOutput>>(relativePath, input);
    }

    public async Task<HotelPhotosOutput> GetHotelPhotos(long hotelId)
    {
        var relativePath = $"/Hotel/Photos?hotelId={hotelId}";
        return await GetAsync<HotelPhotosOutput>(relativePath);
    }

    public Task<Contracts.Common.Resource.DTOs.HopHotel.SyncHotelInfoOutput> SyncHotelInfo(Contracts.Common.Resource.DTOs.HopHotel.SyncHotelInfoInput input)
    {
        var relativePath = "/HopHotel/SyncHotelInfo";
        return PostAsync<Contracts.Common.Resource.DTOs.HopHotel.SyncHotelInfoInput, Contracts.Common.Resource.DTOs.HopHotel.SyncHotelInfoOutput>(relativePath, input);
    }

    public Task<List<HotelInquiryEmailOutput>> SearchHotelInquiryEmail(SearchHotelInquiryEmailInput input)
    {
        var relativePath = "/Hotel/SearchHotelInquiryEmail";
        return PostAsync<SearchHotelInquiryEmailInput, List<HotelInquiryEmailOutput>>(relativePath, input);
    }

    public async Task<List<GetSimpleInfoOutput>> GetSimpleInfoByResourceHotelIds(params long[] ids)
    {
        var relativePath = "/Hotel/GetByHotelIds";
        return await PostAsync<long[], List<GetSimpleInfoOutput>>(relativePath,ids);
    }

    #endregion

    #region airport
    public async Task<List<AirportListOutput>> AirportGetAll()
    {
        var relativePath = $"/Airport/GetAll";
        return await GetAsync<List<AirportListOutput>>(relativePath);
    }

    /// <summary>
    /// 列表查询-机场资源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<PagingModel<AirportListOutput>> SearchAirport(SearchAirportInput input)
    {
        var relativePath = "/Airport/Search";
        return await PostAsync<SearchAirportInput, PagingModel<AirportListOutput>>(relativePath, input);
    }

    /// <summary>
    /// 机场资源详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<AirportDetailOutput> AirportDetail(long id)
    {
        var relativePath = "/Airport/Detail?id=" + id;
        return await GetAsync<AirportDetailOutput>(relativePath);
    }

    /// <summary>
    /// 创建机场资源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<long> AddAirport(AddAirportInput input)
    {
        var relativePath = "/Airport/Add";
        return await PostAsync<AddAirportInput, long>(relativePath, input);
    }

    /// <summary>
    /// 更新机场资源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> UpdateAirport(UpdateAirportInput input)
    {
        var relativePath = "/Airport/Update";
        return await PostAsync<UpdateAirportInput, bool>(relativePath, input);
    }
    #endregion

    #region Es AggregateResource

    public async Task<DestinationSearchOutput> AggregateResourceSearchDestinations(DestinationSearchInput input)
    {
        var relativePath = "/EsAggregateResource/SearchDestinations";
        return await PostAsync<DestinationSearchInput, DestinationSearchOutput>(relativePath, input);
    }

    #endregion

    #region EsTradingArea

    public async Task<IEnumerable<SearchEsTradingAreaOutput>> SearchEsTradingArea(SearchEsTradingAreaInput input)
    {
        var relativePath = "/EsTradingArea/Search";
        return await PostAsync<SearchEsTradingAreaInput, IEnumerable<SearchEsTradingAreaOutput>>(relativePath, input);
    }
    
    public async Task<IEnumerable<SearchEsTradingAreaOutput>> SearchEsTradingAreaV2(SearchEsTradingAreaInput input)
    {
        var relativePath = "/EsTradingArea/SearchV2";
        return await PostAsync<SearchEsTradingAreaInput, IEnumerable<SearchEsTradingAreaOutput>>(relativePath, input);
    }

    #endregion

    #region HotelMeeting
    public async Task<long> AddHotelMeeting(AddHotelMeetingInput input)
    {
        var relativePath = "/HotelMeeting/Add";
        return await PostAsync<AddHotelMeetingInput, long>(relativePath, input);
    }

    public async Task UpdateHotelMeeting(UpdateHotelMeetingInput input)
    {
        var relativePath = "/HotelMeeting/Update";
        await PostAsync<UpdateHotelMeetingInput>(relativePath, input);
    }

    public async Task<HotelMeetingDetailOutput> DetailHotelMeeting(long id)
    {
        var relativePath = "/HotelMeeting/Detail?id=" + id;
        return await GetAsync<HotelMeetingDetailOutput>(relativePath);
    }

    public async Task<PagingModel<SearchHotelMeetingOuput>> SearchHotelMeeting(SearchHotelMeetingInput input)
    {
        var relativePath = "/HotelMeeting/Search";
        return await PostAsync<SearchHotelMeetingInput, PagingModel<SearchHotelMeetingOuput>>(relativePath, input);
    }

    public async Task DeleteHotelMeeting(List<long> ids)
    {
        var relativePath = "/HotelMeeting/Delete";
        await PostAsync<List<long>>(relativePath, ids);
    }
    #endregion

    #region HotelGroupRecommend

    public Task<List<HotelGroupRecommendSetOutput>> HotelGroupRecommendSet(params HotelGroupRecommendSetInput[] inputs)
    {
        var relativePath = "/HotelGroupRecommend/Set";
        return PostAsync<HotelGroupRecommendSetInput[], List<HotelGroupRecommendSetOutput>>(relativePath, inputs);
    }

    public Task<PagingModel<HotelGroupRecommendOutput>> HotelGroupRecommendSearch(HotelGroupRecommendSearchInput input)
    {
        var relativePath = "/HotelGroupRecommend/Search";
        return PostAsync<HotelGroupRecommendSearchInput, PagingModel<HotelGroupRecommendOutput>>(relativePath, input);
    }

    public Task<List<HotelGroupRecommendStatOutput>> HotelGroupRecommendStat(HotelGroupRecommendQueryDto dto)
    {
        var relativePath = "/HotelGroupRecommend/HotelGroupRecommendStat";
        return PostAsync<HotelGroupRecommendQueryDto, List<HotelGroupRecommendStatOutput>>(relativePath, dto);
    }

    public Task HotelGroupRecommendDelete(HotelGroupRecommendDeleteInput input)
    {
        var relativePath = "/HotelGroupRecommend/Delete";
        return PostAsync<HotelGroupRecommendDeleteInput>(relativePath, input);
    }

    #endregion

    #region Country
    public Task<IEnumerable<CountryOutput>> GetCountries()
    {
        var relativePath = "/Country/GetAll";
        return GetAsync<IEnumerable<CountryOutput>>(relativePath);
    }

    #endregion

    #region MaxkbBi
    public Task<string> ChatMessageByPromt(ChatMessageByPromtInput input)
    {
        var relativePath = "/MaxkbBi/ChatMessageByPromt";
        return PostAsync<ChatMessageByPromtInput, string>(relativePath, input);
    } 
    #endregion
}
