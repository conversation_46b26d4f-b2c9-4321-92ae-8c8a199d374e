using Common.Caller;
using Contracts.Common.Resource.DTOs;
using Contracts.Common.Resource.DTOs.AggregateResource;
using Contracts.Common.Resource.DTOs.Airport;
using Contracts.Common.Resource.DTOs.EsTradingArea;
using Contracts.Common.Resource.DTOs.Hotel;
using Contracts.Common.Resource.DTOs.HotelGroupRecommend;
using Contracts.Common.Resource.DTOs.HotelMeeting;
using Contracts.Common.Resource.DTOs.MaxkbBi;
using Contracts.Common.Resource.DTOs.Scenic;
using Contracts.Common.Resource.DTOs.ThirdHotel;
using EfCoreExtensions.Abstract;

namespace Bff.Manage.Callers;

public interface IResourceApiCaller : IHttpCallerBase
{
    #region ScenicSpot

    /// <summary>
    /// 查询景区详情
    /// </summary>
    Task<GetScenicSpotDetailOutput> Detail(long id);

    #endregion

    #region ThirdHotel

    /// <summary>
    /// 查询第三方平台酒店报价信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GetThirdHotelPriceOutput> GetThirdHotelPrice(GetThirdHotelPriceInput input);

    #endregion

    #region Hotel

    /// <summary>
    /// 资源酒店搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchHotelOutput>> HotelSearch(SearchHotelInput input);

    /// <summary>
    /// 获取资源酒店房型信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetRoomsByHotelIdsOutput>> GetRoomsByHotelIds(GetRoomsByHotelIdsInput input);

    /// <summary>
    /// 获取资源酒店(包括房型)图片
    /// </summary>
    Task<HotelPhotosOutput> GetHotelPhotos(long hotelId);

    /// <summary>
    /// HOP酒店资源信息同步
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<Contracts.Common.Resource.DTOs.HopHotel.SyncHotelInfoOutput> SyncHotelInfo(Contracts.Common.Resource.DTOs.HopHotel.SyncHotelInfoInput input);

    /// <summary>
    /// 查询酒店发单信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<HotelInquiryEmailOutput>> SearchHotelInquiryEmail(SearchHotelInquiryEmailInput input);

    /// <summary>
    /// 获取资源酒店基础详情
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    Task<List<GetSimpleInfoOutput>> GetSimpleInfoByResourceHotelIds(params long[] ids);

    #endregion

    #region airport
    /// <summary>
    /// 获取机场全部信息
    /// </summary>
    /// <returns></returns>
    Task<List<AirportListOutput>> AirportGetAll();

    /// <summary>
    /// 列表查询-机场资源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<AirportListOutput>> SearchAirport(SearchAirportInput input);

    /// <summary>
    /// 机场资源详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<AirportDetailOutput> AirportDetail(long id);

    /// <summary>
    /// 创建机场资源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<long> AddAirport(AddAirportInput input);

    /// <summary>
    /// 更新机场资源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> UpdateAirport(UpdateAirportInput input);
    #endregion

    #region Es AggregateResource

    /// <summary>
    /// 聚合资源-目的地筛选
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<DestinationSearchOutput> AggregateResourceSearchDestinations(DestinationSearchInput input);

    #endregion

    #region EsTradingArea

    /// <summary>
    /// 查询es商圈数据
    /// </summary>
    Task<IEnumerable<SearchEsTradingAreaOutput>> SearchEsTradingArea(SearchEsTradingAreaInput input);
    
    /// <summary>
    /// 查询es商圈数据
    /// </summary>
    Task<IEnumerable<SearchEsTradingAreaOutput>> SearchEsTradingAreaV2(SearchEsTradingAreaInput input);


    #endregion

    #region HotelMeeting
    Task<long> AddHotelMeeting(AddHotelMeetingInput input);

    Task UpdateHotelMeeting(UpdateHotelMeetingInput input);

    Task<HotelMeetingDetailOutput> DetailHotelMeeting(long id);

    Task<PagingModel<SearchHotelMeetingOuput>> SearchHotelMeeting(SearchHotelMeetingInput input);

    Task DeleteHotelMeeting(List<long> ids);
    #endregion

    #region HotelGroupRecommend

    /// <summary>
    /// 设置推荐团房
    /// </summary>
    /// <param name="inputs"></param>
    /// <returns></returns>
    Task<List<HotelGroupRecommendSetOutput>> HotelGroupRecommendSet(params HotelGroupRecommendSetInput[] inputs);

    /// <summary>
    /// 搜索酒店推荐团房
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<HotelGroupRecommendOutput>> HotelGroupRecommendSearch(HotelGroupRecommendSearchInput input);

    /// <summary>
    /// 酒店推荐团房统计
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    Task<List<HotelGroupRecommendStatOutput>> HotelGroupRecommendStat(HotelGroupRecommendQueryDto dto);

    /// <summary>
    /// 删除酒店推荐团房
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task HotelGroupRecommendDelete(HotelGroupRecommendDeleteInput input);

    #endregion

    #region Country

    /// <summary>
    /// 获取国家列表
    /// </summary>
    /// <returns></returns>
    Task<IEnumerable<CountryOutput>> GetCountries();


    #endregion

    #region MaxkbBi
    Task<string> ChatMessageByPromt(ChatMessageByPromtInput input); 
    #endregion
}