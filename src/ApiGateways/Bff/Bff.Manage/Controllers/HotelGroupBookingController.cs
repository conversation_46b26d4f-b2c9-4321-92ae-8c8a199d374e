using AutoMapper;
using Bff.Manage.Callers;
using Bff.Manage.Models.HotelGroupBooking;
using Bff.Manage.Models.HotelGroupBookingInquiry;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.BaseOrderRemark;
using Contracts.Common.Order.DTOs.GroupBooking;
using Contracts.Common.Order.Enums;
using Contracts.Common.Resource.DTOs.Hotel;
using Contracts.Common.Resource.DTOs.MaxkbBi;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text.RegularExpressions;
using System.Web;

namespace Bff.Manage.Controllers;
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class HotelGroupBookingController : ControllerBase
{
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly ITenantCaller _tenantCaller;
    private readonly IUserApiCaller _userApiCaller;
    private readonly IPaymentApiCaller _paymentApiCaller;
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly IMapper _mapper;
    private static readonly string _desKey = "hztravel";
    private readonly ILogger<HotelGroupBookingController> _logger;

    public HotelGroupBookingController(IOrderApiCaller orderApiCaller,
        ITenantCaller tenantCaller,
        IUserApiCaller userApiCaller,
        IPaymentApiCaller paymentApiCaller,
        IResourceApiCaller resourceApiCaller,
        ILogger<HotelGroupBookingController> logger,
        IMapper mapper)
    {
        _orderApiCaller = orderApiCaller;
        _tenantCaller = tenantCaller;
        _userApiCaller = userApiCaller;
        _paymentApiCaller = paymentApiCaller;
        _resourceApiCaller = resourceApiCaller;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(PagingModel<SearchOutput, GroupBookingApplicationFormStatusSupplement[]>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchInput input)
    {
        GroupBookingApplicationFormStatus[] statuses = input.Statuses?.Length is > 0 ?
            input.Statuses :
            Enum.GetValues<GroupBookingApplicationFormStatus>()
                .Where(x => x != GroupBookingApplicationFormStatus.NewApplication)
                .ToArray();
        var applicationFormSearchInput = _mapper.Map<ApplicationFormSearchInput>(input);
        applicationFormSearchInput.Ids = input.Id.HasValue ? new List<long>() { input.Id.Value } : new List<long>();
        applicationFormSearchInput.Statuses = statuses;
        var result = await _orderApiCaller.GroupBookingApplicationFormSearch(applicationFormSearchInput);

        var tenantIds = result.Data.Select(x => x.TenantId).Distinct().ToList();
        var tenantSysConfigs = await _tenantCaller.GetSysConfigByTenantIds(tenantIds);

        var formIds = result.Data.Select(x => x.Id).ToArray();
        var operationLogs = await _orderApiCaller.GetGroupBookingOperationLogs(new GetOperationLogInput
        {
            ApplicationFormIds = formIds
        });

        var ids = result.Data.Where(x => x.OperatorUserId.HasValue).Select(x => x.OperatorUserId!.Value).ToArray();
        var users = ids.Length is > 0 ? (await _userApiCaller.SearchTenantUsers(new Contracts.Common.User.DTOs.TenantUser.SearchTenantUsersInput
        {
            Ids = ids
        })) : new List<Contracts.Common.User.DTOs.UserSearchOuput>();

        PagingModel<SearchOutput, GroupBookingApplicationFormStatusSupplement[]> output = new()
        {
            PageIndex = result.PageIndex,
            PageSize = result.PageSize,
            Total = result.Total,
            Data = result.Data.Select(x =>
            {
                var tenant = tenantSysConfigs.FirstOrDefault(s => s.TenantId == x.TenantId);
                var operatorUserName = users.FirstOrDefault(s => s.Id == x.OperatorUserId)?.Name;
                return new SearchOutput
                {
                    Id = x.Id,
                    TenantId = x.TenantId,
                    TenantFullName = tenant?.FullName,
                    AgencyId = x.AgencyId,
                    AgencyName = x.AgencyName,
                    CountryCode = x.CountryCode,
                    CountryName = x.CountryName,
                    ProvinceCode = x.ProvinceCode,
                    ProvinceName = x.ProvinceName,
                    CityCode = x.CityCode,
                    CityName = x.CityName,
                    HotelName = x.HotelName,
                    CheckInDate = x.CheckInDate,
                    CheckOutDate = x.CheckOutDate,
                    UnitPrice = x.UnitPrice,
                    CurrencyCode = x.CurrencyCode,
                    AdultNum = x.AdultNum,
                    ChildNum = x.ChildNum,
                    BedTypes = x.BedTypes,
                    ContactPhone = x.ContactPhone,
                    OtherRequirements = x.OtherRequirements,
                    ApplicantId = x.ApplicantId,
                    Applicant = x.Applicant,
                    ApplicationTime = x.ApplicationTime,
                    FinishTime = x.FinishTime,
                    Status = x.Status,
                    Assignor = x.Assignor,
                    AssignorId = x.AssignorId,
                    OperatorUserId = x.OperatorUserId,
                    operatorUserName = operatorUserName,
                    GroupBookingFormPreOrderStatus = x.GroupBookingFormPreOrderStatus,
                    OrderStatus = x.OrderStatus,
                    ContractSigned = x.ContractSigned,
                    TeamNatureType = x.TeamNatureType,
                    GroupBookingOrderCount = x.GroupBookingOrderCount,
                    LatestOperationLog = operationLogs.Where(s => s.ApplicationFormId == x.Id)
                       .OrderByDescending(x => x.CreateTime)
                       .FirstOrDefault(),
                    Assignors = x.Assignors,
                };
            }),
            Supplement = result.Supplement.GroupBookingApplicationFormStatusSupplements,
        };
        return Ok(output);
    }

    /// <summary>
    /// 申请单信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(FormOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetApplicationForm(GetApplicationFormInput input)
    {
        var form = await _orderApiCaller.GetApplicationForm(input);
        var output = _mapper.Map<FormOutput>(form);
        if (form.OperatorUserId.HasValue)
        {
            var users = await _userApiCaller.SearchTenantUsers(new Contracts.Common.User.DTOs.TenantUser.SearchTenantUsersInput
            {
                Ids = new long[] { form.OperatorUserId.Value },
            });
            output.OperatorUserName = users.FirstOrDefault(x => x.Id == form.OperatorUserId!.Value)?.Name;
        }
        if (form.Status == GroupBookingApplicationFormStatus.WaitForInquiry)
        {
            var operationLogs = await _orderApiCaller.GetGroupBookingOperationLogs(new GetOperationLogInput
            {
                ApplicationFormIds = new long[] { form.Id },
            });
            var WaitForInquiryTime = operationLogs.FirstOrDefault(s => s.Status == GroupBookingApplicationFormStatus.WaitForInquiry)?.CreateTime;
            output.WaitForInquiryTime = WaitForInquiryTime;
            output.ApplicationConfirmedTime = operationLogs.FirstOrDefault(s => s.OperationType == GrouBookingOperationType.ApplicationConfirmed)?.CreateTime;
        }
        return Ok(output);
    }

    /// <summary>
    /// 目的地酒店搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(List<ApplicationDemandHotelOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GroupBookingDemandHotelSearch(DemandHotelSearchInput input)
    {
        var demandHotels = await _orderApiCaller
            .SetTenantId(input.TenantId)
            .GroupBookingDemandHotelSearch(new ApplicationDemandHotelSearchInput
            {
                ApplicationFormId = input.ApplicationFormId,
            });
        return Ok(demandHotels);
    }

    /// <summary>
    /// 目的地需求酒店发单询价搜索
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(List<DemandHotelInquiryoutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> DemandHotelInquirySearch(DemandHotelInquirySearchInput input)
    {
        var demandsTask = _orderApiCaller
            .SetTenantId(input.TenantId)
            .GetGroupBookingApplicationDemands(input.ApplicationFormId);
        var demandHotelsTask = _orderApiCaller
            .SetTenantId(input.TenantId)
            .GroupBookingDemandHotelSearch(new ApplicationDemandHotelSearchInput
            {
                ApplicationFormId = input.ApplicationFormId,
            });
        var hotelInquiriesTask = _orderApiCaller
            .SetTenantId(input.TenantId)
            .GroupBookingHotelInquirySearch(new GroupBookingHotelInquirySearchInput
            {
                ApplicationFormId = input.ApplicationFormId,
            });
        var hotelQuoteSearchTask = _orderApiCaller
            .SetTenantId(input.TenantId)
            .GroupBookingHotelQuoteSearch(new GroupBookingHotelQuoteSearchInput
            {
                ApplicationFormId = input.ApplicationFormId,
            });
        var demands = await demandsTask;
        var demandHotels = await demandHotelsTask;
        var hotelInquiries = await hotelInquiriesTask;
        var groupBookingHotelQuotes = await hotelQuoteSearchTask;

        var SearchHotelInquiryEmails = await _resourceApiCaller
            .SearchHotelInquiryEmail(new Contracts.Common.Resource.DTOs.Hotel.SearchHotelInquiryEmailInput
            {
                HotelIds = demandHotels.Where(s => s.ResourceHotelId.HasValue).Select(s => s.ResourceHotelId!.Value).ToArray(),
            });

        List<DemandHotelInquiryDto> result = new();
        foreach (var demandHotel in demandHotels)
        {
            var inquiries = hotelInquiries
                .Where(x => x.DemandHoteId == demandHotel.Id && x.ResourceHotelId == demandHotel.ResourceHotelId);
            List<DemandHotelInquiryDto> inquiryDtos = inquiries.Select(s => new DemandHotelInquiryDto
            {
                HotelInquiryId = s.Id,
                ApplicationDemandId = demandHotel.ApplicationDemandId,
                DemandHoteId = demandHotel.Id,
                ResourceHotelId = demandHotel.ResourceHotelId,
                HotelName = demandHotel.HotelName,
                HotelENName = demandHotel.HotelENName,
                IsRecommend = demandHotel.IsRecommend,
                Email = s.Email ?? string.Empty,
                FirstName = s.FirstName,
                Status = s.Status,
                MailStatus = s.MailStatus,
                UnableToQuoteReason = s.UnableToQuoteReason,
                HotelQuotes = groupBookingHotelQuotes.Where(x => x.HotelInquiryId == s.Id).ToList(),
                ExpiredTime = s.ExpiredTime,
                HotelRecoveryTime = s.Status == GroupBookingHotelInquiryStatus.Quoted ? s.UpdateTime : null,
            }).ToList();
            var inquiryEmails = SearchHotelInquiryEmails.Where(x => x.HotelId == demandHotel.ResourceHotelId);
            foreach (var inquiryEmail in inquiryEmails)
            {
                if (string.IsNullOrWhiteSpace(inquiryEmail.Email))
                    continue;
                var existsInquiry = inquiryDtos.Any(x => x.Email.Trim() == inquiryEmail.Email.Trim());
                if (existsInquiry)
                    continue;
                inquiryDtos.Add(new DemandHotelInquiryDto
                {
                    ApplicationDemandId = demandHotel.ApplicationDemandId,
                    DemandHoteId = demandHotel.Id,
                    ResourceHotelId = demandHotel.ResourceHotelId,
                    HotelName = demandHotel.HotelName,
                    HotelENName = demandHotel.HotelENName,
                    IsRecommend = demandHotel.IsRecommend,
                    Email = inquiryEmail.Email,
                    FirstName = inquiryEmail.FirstName,
                    Status = GroupBookingHotelInquiryStatus.Processing,
                    MailStatus = null,
                });
            }
            //无邮箱地址
            if (inquiryDtos.Count == 0)
            {
                inquiryDtos.Add(new DemandHotelInquiryDto
                {
                    ApplicationDemandId = demandHotel.ApplicationDemandId,
                    DemandHoteId = demandHotel.Id,
                    ResourceHotelId = demandHotel.ResourceHotelId,
                    HotelName = demandHotel.HotelName,
                    HotelENName = demandHotel.HotelENName,
                    IsRecommend = demandHotel.IsRecommend,
                    Status = GroupBookingHotelInquiryStatus.Processing,
                    MailStatus = Contracts.Common.Notify.Enums.EmailSmtpStatusCode.NoEmailAddress,
                });
            }
            result.AddRange(inquiryDtos);
        }
        List<DemandHotelInquiryoutput> output = demands
            .Select(demand =>
            {
                var inquires = result.Where(x => x.ApplicationDemandId == demand.Id).ToArray();
                DemandHotelInquiryoutput demandHotelInquiryoutput = new()
                {
                    ApplicationDemandId = demand.Id,
                    CheckInDate = demand.CheckInDate,
                    CheckOutDate = demand.CheckOutDate,
                    CityCode = demand.CityCode,
                    CityName = demand.CityName,
                    CountryCode = demand.CountryCode,
                    CountryName = demand.CountryName,
                    ProvinceCode = demand.ProvinceCode,
                    ProvinceName = demand.ProvinceName,
                    Hotels = demand.Hotels,
                    DemandHotelInquiries = inquires,
                    ExpiredTime = inquires?.Max(x => x.ExpiredTime),
                };
                return demandHotelInquiryoutput;
            }).ToList();

        return Ok(output);
    }

    private static DemandHotelInquiryQuoteDto GetDataByCode(string code)
    {
        try
        {
            //var code = Common.Utils.SecurityUtil.DESEncrypt(data, _desKey);
            code = HttpUtility.UrlDecode(code);
            if (code.Contains(' ')) code = code.Replace(' ', '+');
            var data = Common.Utils.SecurityUtil.DESDecrypt(code, _desKey);
            var result = JsonConvert.DeserializeObject<DemandHotelInquiryQuoteDto>(data);
            return result;
        }
        catch (Exception ex)
        {
            throw new BusinessException("Code invalid.", ex);
        }
    }

    /// <summary>
    /// 查询酒店发单询单信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [AllowAnonymous]
    [ProducesResponseType(typeof(GetDemandHotelInquiryOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetDemandHotelInquiry(string code)
    {
        DemandHotelInquiryQuoteDto input = GetDataByCode(code);
        var hotelInquiries = await _orderApiCaller
              .SetTenantId(input.TenantId)
              .GroupBookingHotelInquirySearch(new GroupBookingHotelInquirySearchInput
              {
                  ApplicationFormId = input.ApplicationFormId,
                  Id = input.HotelInquiryId,
              });
        var hotelInquiry = hotelInquiries.FirstOrDefault();
        if (hotelInquiry is null)
            return Ok();
        var applicationForm = await _orderApiCaller
            .SetTenantId(input.TenantId)
            .GetApplicationForm(new GetApplicationFormInput
            {
                Id = hotelInquiry.ApplicationFormId,
                TenantId = input.TenantId
            });

        var demandHotels = await _orderApiCaller
            .SetTenantId(input.TenantId)
            .GroupBookingDemandHotelSearch(new ApplicationDemandHotelSearchInput
            {
                ApplicationFormId = input.ApplicationFormId,
                DemandHotelId = hotelInquiry.DemandHoteId,
            });

        var hotelInquiriesRecord = await _orderApiCaller
           .SetTenantId(input.TenantId)
           .GetGroupBookingHotelInquiryRecord(hotelInquiry.Id);

        var demandHotel = demandHotels.FirstOrDefault(s => s.Id == hotelInquiry.DemandHoteId);
        var nationalCurrencies = await _paymentApiCaller.NationalCurrencySearch(new Contracts.Common.Payment.DTOs.NationalCurrency.NationalCurrencyInput
        {
            CountryCodes = applicationForm.Demands
         .Where(x => x.CountryCode.HasValue)
         .Select(s => s.CountryCode!.Value).Distinct().ToList()
        });
        GetDemandHotelInquiryOutput output = new()
        {
            ApplicationFormId = hotelInquiry.ApplicationFormId,
            Email = hotelInquiry.Email,
            FirstName = hotelInquiry.FirstName,
            Status = hotelInquiry.Status,
            UnableToQuoteReason = hotelInquiry.UnableToQuoteReason,
            ExpiredTime = hotelInquiry.ExpiredTime,
            OtherRequirements = applicationForm.OtherRequirements,
            ENOtherRequirements = applicationForm.ENOtherRequirements,
            // EnTeamNatureTitle = applicationForm.EnTeamNatureTitle,
            EnTeamNatureTitle = hotelInquiriesRecord?.TeamNatureTitle,
            Hotel = new ApplicationDemandHotelOutDto
            {
                ResourceHotelId = demandHotel.ResourceHotelId,
                HotelName = demandHotel.HotelName,
                HotelENName = demandHotel.HotelENName,
                IsRecommend = demandHotel.IsRecommend,
            },
            Demand = applicationForm.Demands.Where(x => x.Id == demandHotel.ApplicationDemandId)
              .Select(s => new ApplicationDemandOutDto
              {
                  AdultNum = s.AdultNum,
                  BedTypes = s.BedTypes,
                  CheckInDate = s.CheckInDate,
                  CheckOutDate = s.CheckOutDate,
                  ChildDesc = s.ChildDesc,
                  ChildNum = s.ChildNum,
                  CityCode = s.CityCode,
                  CityName = s.CityName,
                  CountryCode = s.CountryCode,
                  CountryName = s.CountryName,
                  NationalCurrencyCode = nationalCurrencies
                    .FirstOrDefault(c => c.CountryCode == s.CountryCode)?.CurrencyCode ?? Contracts.Common.Payment.Enums.Currency.USD.ToString(),
                  MeetingRequirement = s.MeetingRequirement,
                  MeetingsNum = s.MeetingsNum,
                  MeetingTimes = s.MeetingTimes,
                  NeedHotelRoom = s.NeedHotelRoom,
                  NeedMeetingRoom = s.NeedMeetingRoom,
                  OtherRequirement = s.OtherRequirement,
                  ProvinceCode = s.ProvinceCode,
                  ProvinceName = s.ProvinceName,
                  HotelName = demandHotel.HotelName,
                  ResourceHotelId = demandHotel.ResourceHotelId,
                  TravelType = s.TravelType,
                  DemandItems = s.DemandItems,
                  //ENOtherRequirements = s.ENOtherRequirements,
                  //ToHotelMeetingBudget = s.ToHotelMeetingBudget,
                  //ToHotelUnitPrice = s.ToHotelUnitPrice,
                  //CurrencyCode = s.CurrencyCode,
                  ENOtherRequirements = hotelInquiriesRecord?.OtherRequirement,
                  ToHotelMeetingBudget = hotelInquiriesRecord?.MeetingBudget,
                  ToHotelUnitPrice = hotelInquiriesRecord?.UnitPrice,
                  CurrencyCode = hotelInquiriesRecord?.CurrencyCode

              })
              .FirstOrDefault()
        };
        return Ok(output);
    }

    /// <summary>
    /// 酒店发单邮件 模板内容渲染
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(GroupBookingHotelInquiryTemplateRenderOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.GroupBookingHotelInquiryTemplateNotConfig)]
    public async Task<IActionResult> HotelInquiryTemplateRender(GroupBookingHotelInquiryTemplateRenderInput input)
    {
        var result = await _orderApiCaller.GroupBookingHotelInquiryTemplateRender(input);
        return Ok(result);
    }

    /// <summary>
    /// 酒店发单询价 发送邮件/重新发送
    /// status=0 报价中 &&  mailStatus 不传值时，可以发送邮件， status=1 && mailStatus!=0发送中时 可以重发邮件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> HotelInquiry(HotelInquiryInput input)
    {
        await _orderApiCaller
            .SetTenantId(input.TenantId)
            .GroupBookingHotelInquiry(new GroupBookingHotelInquiryInput
            {
                ApplicationFormId = input.ApplicationFormId,
                DemandHoteId = input.DemandHoteId,
                HotelInquiryId = input.HotelInquiryId,
                Email = input.Email,
                FirstName = input.FirstName,
                ValidityPeriod = input.ValidityPeriod,
                Description = input.Description
            });
        return Ok();
    }

    /// <summary>
    /// 酒店报价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [AllowAnonymous]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> HotelQuote(HotelQuoteInput input)
    {
        DemandHotelInquiryQuoteDto demandHotelInquiryInput = GetDataByCode(input.Code);
        await _orderApiCaller
            .SetTenantId(demandHotelInquiryInput.TenantId)
            .GroupBookingHotelQuote(new GroupBookingHotelQuoteInput
            {
                CheckInDate = input.CheckInDate,
                CheckOutDate = input.CheckOutDate,
                ApplicationFormId = demandHotelInquiryInput.ApplicationFormId,
                HotelInquiryId = demandHotelInquiryInput.HotelInquiryId,
                CancellationPolicy = input.CancellationPolicy,
                ChildPolicy = input.ChildPolicy,
                Items = input.Items,
                OfferValidity = input.OfferValidity,
                OtherDescription = input.OtherDescription,
                PaymentPolicy = input.PaymentPolicy,
                Additions = input.Additions,
                UserId = 0,
                UserName = "系统",
                UserType = UserType.Manager

            });
        return Ok();
    }

    /// <summary>
    /// 酒店无法报价
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [AllowAnonymous]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> HotelUnableToQuote(HotelUnableToQuoteInput input)
    {
        DemandHotelInquiryQuoteDto demandHotelInquiryInput = GetDataByCode(input.Code);
        await _orderApiCaller
                .SetTenantId(demandHotelInquiryInput.TenantId)
                .GroupBookingHotelUnableToQuote(new GroupBookingHotelUnableToQuoteInput
                {
                    ApplicationFormId = demandHotelInquiryInput.ApplicationFormId,
                    HotelInquiryId = demandHotelInquiryInput.HotelInquiryId,
                    UnableToQuoteReason = input.UnableToQuoteReason
                });
        return Ok();
    }

    /// <summary>
    /// 获取酒店报价信息
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [AllowAnonymous]
    [ProducesResponseType(typeof(GroupBookingHotelQuoteOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetHotelQuotation(string code)
    {
        DemandHotelInquiryQuoteDto input = GetDataByCode(code);
        var result = await _orderApiCaller
            .SetTenantId(input.TenantId)
            .GroupBookingHotelQuoteSearch(new GroupBookingHotelQuoteSearchInput
            {
                ApplicationFormId = input.ApplicationFormId,
                HotelInquiryId = input.HotelInquiryId,
            });
        var output = result.FirstOrDefault();
        return Ok(output);
    }


    /// <summary>
    /// 酒店发单询价 报价详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(List<GroupBookingHotelQuoteOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> HotelQuoteSearch(HotelQuoteSearchInput input)
    {
        var result = await _orderApiCaller
            .SetTenantId(input.TenantId)
            .GroupBookingHotelQuoteSearch(new GroupBookingHotelQuoteSearchInput
            {
                ApplicationFormId = input.ApplicationFormId,
                HotelInquiryId = input.HotelInquiryId,
            });

        return Ok(result);
    }

    /// <summary>
    /// 确认询价 生成报价单 
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Quotate(FormQuotationInput input)
    {

        var operationUser = GetOperationUser();
        await _orderApiCaller
            .SetTenantId(input.TenantId)
            .GroupBookingQuotation(new QuotationInput
            {
                ApplicationFormId = input.ApplicationFormId,
                OperationUser = operationUser,
                Quotations = input.Quotations
            });
        return Ok();
    }

    /// <summary>
    /// 无法报价 状态-无法报价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> UnableToQuote(UnableQuoteInput input)
    {
        var operationUser = GetOperationUser();
        await _orderApiCaller
            .SetTenantId(input.TenantId)
            .GroupBookingUnableToQuote(new UnableToQuoteInput
            {
                ApplicationFormId = input.ApplicationFormId,
                OperationUser = operationUser,
            });

        await _orderApiCaller
          .SetTenantId(input.TenantId)
          .AddBaseOrderRemarks(new AddBaseOrderRemarkInput
          {
              BaseOrderId = input.ApplicationFormId,
              Imgs = input.Imgs,
              Remark = input.Remark
          });
        return Ok();
    }

    /// <summary>
    /// 查询报价单信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(IEnumerable<QuotationOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetQuotations(GetQuotationsInput input)
    {
        var result = await _orderApiCaller
            .SetTenantId(input.TenantId)
            .GetGroupBookingQuotations(input.ApplicationFormId);
        return Ok(result);
    }

    /// <summary>
    /// 提交/编辑预订单 存在团房单号的预订单不能编辑 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> PreOrder(FormPreOrderInput input)
    {
        var preOrders = input.PreOrders;
        foreach (var preOrder in preOrders)
        {
            var items = preOrder.PreOrderItems;
            foreach (var g in items.OrderByDescending(x => x.RoomCount).GroupBy(x => x.HotelId))
            {
                foreach (var item in g.Skip(1))
                    item.PreOrderAdditions = Enumerable.Empty<PreOrderAdditionDto>();
            }
        }
        var operationUser = GetOperationUser();
        PreOrderInput preOrderInput = new()
        {
            ApplicationFormId = input.ApplicationFormId,
            OperationUser = operationUser,
            PreOrders = preOrders,
        };
        await _orderApiCaller
            .SetTenantId(input.TenantId)
            .GroupBookingPreOrder(preOrderInput);
        return Ok();
    }

    /// <summary>
    /// 查询关联预订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(List<PreOrderOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetPreOrders(GetPreOrdersInput input)
    {
        var preOrders = await _orderApiCaller
            .SetTenantId(input.TenantId)
            .GetGroupBookingPreOrders(input.ApplicationFormId);
        foreach (var preOrder in preOrders)
        {
            var items = preOrder.PreOrderItems;
            foreach (var g in items.GroupBy(x => x.HotelId))
            {
                var adds = g.Where(x => x.PreOrderAdditions?.Any() is true).FirstOrDefault()?.PreOrderAdditions;
                if (adds?.Any() is true)
                {
                    foreach (var item in g)
                    {
                        item.PreOrderAdditions = adds;
                    }
                }
            }
        }
        return Ok(preOrders);
    }

    /// <summary>
    /// 查询操作记录
    /// </summary>
    /// <param name="applicationFormId"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(IEnumerable<OperationLogOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetOperationLogs(GetLogInput input)
    {
        var result = await _orderApiCaller
            .SetTenantId(input.TenantId)
            .GetGroupBookingOperationLogs(new GetOperationLogInput
            {
                ApplicationFormIds = new long[] { input.ApplicationFormId },
                UserTypes = new[] { UserType.Manager, UserType.Merchant, UserType.Agency }
            });
        return Ok(result);
    }

    /// <summary>
    /// 团房申请单 指派
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    public async Task<IActionResult> ApplicationFormAssign(AssignInput input)
    {
        var resourceHotelIds = input.DemandHotelAssigns?
            .Where(s => s.ResourceHotelId is > 0)
            .Select(s => s.ResourceHotelId!.Value)
            .Distinct()
            .ToArray();
        List<GetSimpleInfoOutput> simpleInfos = resourceHotelIds?.Length is > 0 ?
            (await _resourceApiCaller.GetSimpleInfoByResourceHotelIds(resourceHotelIds)) : new List<GetSimpleInfoOutput>();
        ApplicationFormAssignInput applicationFormAssignInput = new()
        {
            Id = input.Id,
            DemandHotelAssigns = input.DemandHotelAssigns?.Select(s =>
            {
                var simpleInfo = simpleInfos.FirstOrDefault(x => x.Id == s.ResourceHotelId);
                return new DemandHotelAssignInput
                {
                    AssignorId = s.AssignorId,
                    Assignor = s.Assignor,
                    DemandHotelId = s.DemandHotelId,
                    ResourceHotelId = s.ResourceHotelId,
                    HotelName = simpleInfo?.ZHName,
                    HotelENName = simpleInfo?.ENName ?? "",
                };
            }).ToList(),
        };
        await _orderApiCaller
            .SetTenantId(input.TenantId)
            .GroupBookingApplicationFormAssign(applicationFormAssignInput);
        return Ok();
    }

    /// <summary>
    /// 添加合同
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> SaveContractContent(SaveContractInput input)
    {
        SaveContractContentInput saveContractContentInput = new()
        {
            ApplicationFormId = input.ApplicationFormId,
            ContractContent = input.ContractContent,
            OperationUser = GetOperationUser()
        };
        await _orderApiCaller.SetTenantId(input.TenantId)
            .SaveContractContent(saveContractContentInput);
        return Ok();
    }

    /// <summary>
    /// 获取所有的城市信息
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(CityOutput), (int)HttpStatusCode.OK)]
    [Authorize]
    public async Task<IActionResult> GetCities()
    {
        var result = await _orderApiCaller.GroupBookingGetCities();
        return Ok(result);
    }

    /// <summary>
    /// 取消申请单 状态不为已取消 && 不存在团房单时 可以操作取消
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Cancel(CancelInput input)
    {
        var operationUser = GetOperationUser();
        await _orderApiCaller
            .SetTenantId(input.TenantId)
            .GroupBookingApplicationFormStatusModify(new ApplicationFormStatusModifyInput
            {
                ApplicationFormId = input.ApplicationFormId,
                LimitStatuses = Enum.GetValues<GroupBookingApplicationFormStatus>()
                .Where(x => x != GroupBookingApplicationFormStatus.Cancellation).ToArray(),
                Status = GroupBookingApplicationFormStatus.Cancellation,
                OperationType = GrouBookingOperationType.Cancellation,
                Remark = input.Remark,
                OperationUser = operationUser,
            });
        return Ok();
    }


    /// <summary>
    /// 获取询价记录申请单与目的地详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(GroupBookingInquiryInformationDto), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default,
        ErrorTypes.User.UserEmailIsEmpty,
        ErrorTypes.User.UserEmailPasswordIsEmpty,
        ErrorTypes.Order.GroupBookingHotelNotAssignor,
        ErrorTypes.Notify.MailBoxAuthenticationError)]
    public async Task<IActionResult> InquiryInformation(BffGroupBookingInquiryInformationInput input)
    {
        var result = await _orderApiCaller.SetTenantId(input.TenantId).GroupBookingInquiryInformation(input);
        return Ok(result);
    }

    /// <summary>
    /// 获取询价记录详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(GroupBookingHotelInquiryRecordDto), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetGroupBookingHotelInquiryRecord(BffGetGroupBookingHotelInquiryRecordInput input)
    {
        var result = await _orderApiCaller.SetTenantId(input.TenantId).GetGroupBookingHotelInquiryRecord(input.GroupBookingHotelInquiryId);
        return Ok(result);
    }

    /// <summary>
    /// 保存询价记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> SaveInquiryInformation(BffSaveInquiryInformationInput input)
    {
        var result = await _orderApiCaller.SetTenantId(input.TenantId).GroupBookingSaveInquiryInformation(input);
        return Ok(result);
    }

    /// <summary>
    /// 保存酒店人工报价信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> SaveGroupHotelManualRecord(BffSaveGroupHotelManualRecordInput input)
    {
        var operationUser = GetOperationUser();

        input.UserType = operationUser.UserType;
        input.UserId = operationUser.UserId;
        input.UserName = operationUser.Name;

        await _orderApiCaller.SetTenantId(input.TenantId).SaveGroupHotelManualRecord(input);
        return Ok();
    }

    /// <summary>
    /// 获取酒店人工报价信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(List<GroupBookingHotelManualRecordDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetGroupBookingHotelManualRecord(BffGetGroupBookingHotelManualRecordInput input)
    {
        var result = await _orderApiCaller.SetTenantId(input.TenantId).GetGroupBookingHotelManualRecord(input);
        return Ok(result);
    }

    /// <summary>
    /// 获取询价记录模版渲染信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(GroupBookingHotelInquiryRecordRenderDto), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetInquiryRecordRender(BffGetInquiryRecordRenderInput input)
    {
        var result = await _orderApiCaller.SetTenantId(input.TenantId).GetInquiryRecordRender(input.GroupBookingHotelInquiryRecordId);
        return Ok(result);
    }

    private OperationUserDto GetOperationUser()
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        OperationUserDto operationUser = new()
        {
            UserType = Contracts.Common.Order.Enums.UserType.Manager,
            UserId = currentUser.UserId,
            Name = currentUser.NickName,
        };
        return operationUser;
    }

    /// <summary>
    /// 更新酒店回复时间
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Order.NoMatchingData)]
    public async Task<IActionResult> UpdateHotelRecoveryTime(BffUpdateHotelRecoveryTimeInput input)
    {
        await _orderApiCaller.SetTenantId(input.TenantId).UpdateGroupBookingHotelRecoveryTime(input);
        return Ok();
    }

    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(GetGroupHotelByPromtOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> ChatGroupHotelByPromt(ChatMessageByPromtInput input)
    {
        input.Message = $"{input.Message}\r\n以json数组返回，json格式：[\r\n{{\r\n\"目的地\": \"福冈\",\r\n\"酒店\": \"\",\r\n\"入住日期\": \"2025-10-12\",\r\n\"离店日期\": \"2025-10-16\",\r\n\"房型名称\": \"\",\r\n\"床型\": \"大床\",\r\n\"房间数量\": 28,\r\n\"早餐情况\": \"含1早\",\r\n\"成人数量\": 28,\r\n\"单夜价格\": 698,\r\n\"单夜价格币种\": \"CNY\",\r\n\"儿童政策\": \"\",\r\n\"付款政策\": \"\",\r\n\"报价有效期\": \"\",\r\n\"取消政策\": \"\",\r\n\"给客户备注\": \"\",\r\n\"其他需求\": \"小孩10人，需要接送，酒店需带有会议室，可以接受分两个距离近的不同的酒店，价格需要比携程外卖价优惠\"\r\n}}\r\n],以上字段都需要返回，把对应值填到对应字段上，并且单夜价格币种返回国际货币英文缩写，单夜价格和价格币种需要分开，有其它需求的话，另外新增字段，多个数据的时候，就多个数组元素，注意：重要说明，只需要返回json数组，不需要其它文字（如推理说明之类）";
        var output = new GetGroupHotelByPromtOutput() { Hotels = new List<PromtGroupHotelDto>() };
        var res = await _resourceApiCaller.ChatMessageByPromt(input);
        if (!string.IsNullOrEmpty(res))
        {
            // 有时候会返回一些推理文字，需要过滤掉
            // 正则表达式匹配JSON数组（处理带换行符的情况）
            var regex = new Regex(@"\[\s*\{.*?\}\s*\]", RegexOptions.Singleline);
            Match match = regex.Match(res);
            if (match.Success)
            {
                // 提取匹配到的JSON字符串
                string jsonString = match.Value;
                var jsonArray = JArray.Parse(jsonString);
                foreach (var item in jsonArray)
                {
                    var json = item.ToString();
                    // 因为价格这些有时候返回字符串
                    // var dto = JsonConvert.DeserializeObject<PromtHotelDto>(json);
                    var info = new PromtGroupHotelDto();
                    var jsonObject = JObject.Parse(json);
                    jsonObject.TryGetValue("目的地", out var city);
                    info.CityName = city?.ToString();
                    jsonObject.TryGetValue("酒店", out var hotelName);
                    info.HotelName = hotelName?.ToString();
                    jsonObject.TryGetValue("入住日期", out var checkInDateStr);
                    if (DateTime.TryParse(checkInDateStr?.ToString(), out var checkInDate))
                        info.CheckInDate = checkInDate;
                    jsonObject.TryGetValue("离店日期", out var checkOutDateStr);
                    if (DateTime.TryParse(checkOutDateStr?.ToString(), out var checkOutDate))
                        info.CheckOutDate = checkOutDate;
                    jsonObject.TryGetValue("早餐情况", out var breakfast);
                    info.Breakfast = breakfast?.ToString();

                    jsonObject.TryGetValue("单夜价格", out var priceStr);
                    if (decimal.TryParse(priceStr?.ToString(), out var price))
                        info.AveragePrice = price;
                    jsonObject.TryGetValue("单夜价格币种", out var currencyCode);
                    info.CurrencyCode = currencyCode?.ToString();

                    jsonObject.TryGetValue("成人数量", out var adultCountStr);
                    if (int.TryParse(adultCountStr?.ToString(), out var adultCount))
                        info.AdultCount = adultCount;

                    jsonObject.TryGetValue("房间数量", out var roomCountStr);
                    if (int.TryParse(roomCountStr?.ToString(), out var roomCount))
                        info.RoomCount = roomCount;

                    jsonObject.TryGetValue("房型名称", out var roomNameStr);
                    info.RoomName = roomNameStr?.ToString();
                    jsonObject.TryGetValue("床型", out var bedTypeNameStr);
                    info.BedTypeName = bedTypeNameStr?.ToString();
                    jsonObject.TryGetValue("儿童政策", out var childPolicyStr);
                    info.ChildPolicy = childPolicyStr?.ToString();
                    jsonObject.TryGetValue("付款政策", out var paymentPolicyStr);
                    info.PaymentPolicy = paymentPolicyStr?.ToString();
                    jsonObject.TryGetValue("报价有效期", out var offerValidityStr);
                    info.OfferValidity = offerValidityStr?.ToString();
                    jsonObject.TryGetValue("取消政策", out var cancellationPolicyStr);
                    info.CancellationPolicy = cancellationPolicyStr?.ToString();
                    jsonObject.TryGetValue("给客户备注", out var customerCommentsStr);
                    info.CustomerComments = customerCommentsStr?.ToString();

                    output.Hotels.Add(info!);
                }
            }
            else
            {
                _logger.LogWarning("ChatGroupHotelByPromt: No valid JSON array found in the response.{@message},{@res}", input.Message, res);
            }
        }
        return Ok(output);
    }
}
