using AutoMapper;
using Bff.Supplier.Callers;
using Bff.Supplier.Models.LineProduct;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Product.DTOs.LineProduct;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Supplier.Controllers;

/// <summary>
/// 线路产品信息
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class LineProductController : ControllerBase
{
    private readonly IMapper _mapper;
    private readonly IProductApiCaller _productApiCaller;
    public LineProductController(
        IMapper mapper,
        IProductApiCaller productApiCaller)
    {
        _mapper = mapper;
        _productApiCaller = productApiCaller;
    }

    
    /// <summary>
    /// 查询分页列表
    /// </summary>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200,typeof(PagingModel<SearchLineProductBffOutput>))]
    public async Task<IActionResult> Search(SearchLineProductBffInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var searchInput = new SearchLineProductInput
        {
            PageIndex = input.PageIndex,
            PageSize = input.PageSize,
            DepartureCityIds = input.DepartureCityIds,
            DestinationCityIds = input.DestinationCityIds,
            Days = input.Days.HasValue ? new List<int> {input.Days.Value} : new List<int>(),
            KeyWord = input.KeyWord,
            SupplierId = currentUser.Provider,
            NeedSummary = false
        };
        var searchResponse = await _productApiCaller.SearchLineProduct(searchInput);
        var result = _mapper.Map<PagingModel<SearchLineProductBffOutput>>(searchResponse);
        return Ok(result);
    }

    /// <summary>
    /// 获取线路产品城市数据
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200,typeof(GetLineProductCitiesBffOutput))]
    public async Task<IActionResult> GetCities()
    {
        var searchResponse = await _productApiCaller.GetLineProductCities();
        var result = _mapper.Map<GetLineProductCitiesBffOutput>(searchResponse);
        return Ok(result);
    }

    /// <summary>
    /// 获取线路产品详情
    /// </summary>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200,typeof(GetLineProductBffOutput))]
    public async Task<IActionResult> Get(long productId)
    {
        var searchResponse = await _productApiCaller.GetLineProductDetail(productId);
        var result = _mapper.Map<GetLineProductBffOutput>(searchResponse);
        return Ok(result);
    }

    /// <summary>
    /// 添加线路产品信息
    /// </summary>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200,typeof(long))]
    [SwaggerResponseExt(default,ErrorTypes.Tenant.SupplierInvalid)]
    [SwaggerResponseExt(default,ErrorTypes.Product.GroupInvalid)]
    public async Task<IActionResult> Add(AddLineProductBffInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var searchInput = _mapper.Map<AddLineProductInput>(input);
        var searchResponse = await _productApiCaller.AddLineProduct(searchInput);
        await _productApiCaller.UpdateLineProductSupplierModuleSetting(new UpdateLineProductSupplierModuleSettingInput
        {
            LineProductId = searchResponse,
            SupplierId = currentUser.Provider,
        });
        await _productApiCaller.UpdateLineProductChannelModuleSetting(new UpdateLineProductChannelModuleSettingInput
        {
            LineProductId = searchResponse,
            Enabled = false
        });
        return Ok(searchResponse);
    }

    /// <summary>
    /// 编辑线路产品信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200,typeof(long))]
    [SwaggerResponseExt(default,ErrorTypes.Tenant.SupplierInvalid)]
    [SwaggerResponseExt(default,ErrorTypes.Product.GroupInvalid)]
    public async Task<IActionResult> Update(UpdateLineProductBffInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var searchInput = _mapper.Map<UpdateLineProductInput>(input);
        await _productApiCaller.UpdateLineProduct(searchInput);
        await _productApiCaller.UpdateLineProductSupplierModuleSetting(new UpdateLineProductSupplierModuleSettingInput
        {
            LineProductId = input.Id,
            SupplierId = currentUser.Provider,
        });
        await _productApiCaller.UpdateLineProductChannelModuleSetting(new UpdateLineProductChannelModuleSettingInput
        {
            LineProductId = input.Id,
            Enabled = false
        });
        return Ok();
    }
}