using Common.Caller;
using Common.ServicesHttpClient;
using Contracts.Common.Product.DTOs.LineProduct;
using Contracts.Common.Product.DTOs.LineProductSku;
using Contracts.Common.Product.DTOs.LineProductSkuCalendarPrice;
using Contracts.Common.Product.DTOs.LineProductSkuItinerary;
using Contracts.Common.Product.DTOs.Restaurant;
using Contracts.Common.Product.DTOs.Store;
using Contracts.Common.Product.DTOs.TicketProduct;
using EfCoreExtensions.Abstract;
using Microsoft.Extensions.Options;

namespace Bff.Supplier.Callers.HttpImplements;

public class ProductApiCaller : HttpCallerBase, IProductApiCaller
{
    public ProductApiCaller(IOptions<ServicesAddress> options, IHttpClientFactory httpClientFactory) 
        : base(options.Value.Product, httpClientFactory)
    {
    }

    #region LineProduct

    public async Task<PagingModel<SearchLineProductOutput, SearchLineProductSummary>> SearchLineProduct(
        SearchLineProductInput input)
    {
        var relativePath = "/LineProduct/Search";
        return await PostAsync<SearchLineProductInput, PagingModel<SearchLineProductOutput, SearchLineProductSummary>>(
            relativePath, input);
    }

    public async Task<GetLineProductCitiesOutput> GetLineProductCities()
    {
        var relativePath = "/LineProduct/GetCities";
        return await GetAsync<GetLineProductCitiesOutput>(relativePath);
    }

    public async Task<GetLineProductOutput> GetLineProductDetail(long productId)
    {
        var relativePath = "/LineProduct/Get?productId=" + productId;
        return await GetAsync<GetLineProductOutput>(relativePath);
    }

    public async Task<long> AddLineProduct(AddLineProductInput input)
    {
        var relativePath = "/LineProduct/Add";
        return await PostAsync<AddLineProductInput,long>(relativePath,input);
    }

    public async Task UpdateLineProduct(UpdateLineProductInput input)
    {
        var relativePath = "/LineProduct/Update";
        await PostAsync(relativePath,input);
    }

    public async Task AddLineProductSku(AddLineProductSkuInput input)
    {
        var relativePath = "/LineProductSku/Add";
        await PostAsync(relativePath, input);
    }

    public async Task EditLineProductSku(EditLineProductSkuInput input)
    {
        var relativePath = "/LineProductSku/Edit";
        await PostAsync(relativePath, input);
    }

    public async Task RemoveLineProductSku(RemoveProductSkuInput input)
    {
        var relativePath = "/LineProductSku/Remove";
        await PostAsync(relativePath, input);
    }

    public async Task<GetLineProductSkuDetailOutput> GetLineProductSkuDetail(long id)
    {
        var relativePath = "/LineProductSku/Detail?id="+id;
        return await GetAsync<GetLineProductSkuDetailOutput>(relativePath);
    }

    public async Task UpdateLineSkuCalendarPrice(UpdateLineProductSkuCalendarPriceInput input)
    {
        var relativePath = "/LineProductSkuCalendarPrice/Update";
        await PostAsync(relativePath, input);
    }

    public async Task<List<GetLineProductSkuCalendarPriceOutput>> GetCalendarPriceByLineProduct(
        GetLineProductSkuCalendarPriceInput input)
    {
        var relativePath = "/LineProductSkuCalendarPrice/GetByProductId";
        return await PostAsync<GetLineProductSkuCalendarPriceInput, List<GetLineProductSkuCalendarPriceOutput>>(
            relativePath, input);
    }

    public async Task UpdateLineSkuItinerary(UpdateLineProductSkuItineraryInput input)
    {
        var relativePath = "/LineProductSkuItinerary/Update";
        await PostAsync(relativePath,input);
    }

    public async Task<List<GetItineraryListOutPut>> GetLineSkuItineraryGroupList(long productSkuId)
    {
        var relativePath = "/LineProductSkuItinerary/DetailByGroup?productSkuId="+productSkuId;
        return await GetAsync<List<GetItineraryListOutPut>>(relativePath);
    }

    public async Task<List<GetSkuWithExistingItineraryOutput>> GetLineProductItineraryList(
        GetSkuWithExistingItineraryInput input)
    {
        var relativePath = "/LineProductSkuItinerary/GetSku";
        return await PostAsync<GetSkuWithExistingItineraryInput, List<GetSkuWithExistingItineraryOutput>>(
            relativePath,input);
    }
    
    public async Task UpdateLineProductSupplierModuleSetting(UpdateLineProductSupplierModuleSettingInput input)
    {
        var relativePath = "/LineProduct/UpdateSupplierModuleSetting";
        await PostAsync(relativePath, input);
    }
    
    public async Task UpdateLineProductChannelModuleSetting(UpdateLineProductChannelModuleSettingInput input)
    {
        var relativePath = "/LineProduct/UpdateChannelModuleSetting";
        await PostAsync(relativePath, input);
    }
    #endregion

    #region TicketProduct

    public async Task<long> AddTicketProduct(AddTicketInput input)
    {
        var relativePath = "/TicketProduct/Add";
        return await PostAsync<AddTicketInput, long>(relativePath,input);
    }

    public async Task UpdateTicketProduct(UpdateTicketInput input)
    {
        var relativePath = "/TicketProduct/Update";
        await PostAsync(relativePath,input);
    }

    public async Task<GetTicketOutput> GetTicketProductDetail(long productId,long supplierId)
    {
        var relativePath = $"/TicketProduct/Get?productId={productId}&supplierId={supplierId}";
        return await GetAsync<GetTicketOutput>(relativePath);
    }

    public async Task<PagingModel<SearchTicketInfo>> SearchTicketProduct(SearchTicketInput input)
    {
        var relativePath = "/TicketProduct/Search";
        return await PostAsync<SearchTicketInput, PagingModel<SearchTicketInfo>>(relativePath,input);
    }
    
    #endregion

    #region SupplySetting

    public async Task<IEnumerable<long>> GetSupplySettingTicketProductIds(long supplierId)
    {
        var relativePath = $"/SupplySetting/GetTicketProductIds?supplierId={supplierId}";
        return await GetAsync<IEnumerable<long>>(relativePath);
    }

    #endregion

    #region Restaurant

    public async Task<List<GetRestaurantByIdsOutPut>> GetRestaurantByIds(GetRestaurantByIdsInput input)
    {
        var relativePath = "/Restaurant/GetRestaurantByIds";
        return await PostAsync<GetRestaurantByIdsInput,List<GetRestaurantByIdsOutPut>>(relativePath,input);
    }

    #endregion

    #region Store

    public async Task<List<GetStoreByIdsOutPut>> GetStoreByIds(GetStoreByIdsInput input)
    {
        var relativePath = "/Store/GetStoreByIds";
        return await PostAsync<GetStoreByIdsInput, List<GetStoreByIdsOutPut>>(relativePath, input);
    }

    #endregion
}