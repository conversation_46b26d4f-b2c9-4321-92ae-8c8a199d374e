using Common.Caller;
using Contracts.Common.Product.DTOs.LineProduct;
using Contracts.Common.Product.DTOs.LineProductSku;
using Contracts.Common.Product.DTOs.LineProductSkuCalendarPrice;
using Contracts.Common.Product.DTOs.LineProductSkuItinerary;
using Contracts.Common.Product.DTOs.Restaurant;
using Contracts.Common.Product.DTOs.Store;
using Contracts.Common.Product.DTOs.SupplySetting;
using Contracts.Common.Product.DTOs.TicketProduct;
using EfCoreExtensions.Abstract;

namespace Bff.Supplier.Callers;

public interface IProductApiCaller : IHttpCallerBase
{
    #region LineProduct

    /// <summary>
    /// 线路产品分页查询
    /// </summary>
    Task<PagingModel<SearchLineProductOutput, SearchLineProductSummary>> SearchLineProduct(SearchLineProductInput input);

    /// <summary>
    /// 线路产品城市数据
    /// </summary>
    Task<GetLineProductCitiesOutput> GetLineProductCities();

    /// <summary>
    /// 获取线路产品详情
    /// </summary>
    Task<GetLineProductOutput> GetLineProductDetail(long productId);

    /// <summary>
    /// 添加线路产品信息
    /// </summary>
    /// <exception cref="ErrorTypes.Tenant.SupplierInvalid"></exception>
    /// <exception cref="ErrorTypes.Product.GroupInvalid"></exception>
    Task<long> AddLineProduct(AddLineProductInput input);
    
    /// <summary>
    /// 编辑线路产品信息
    /// </summary>
    /// <exception cref="ErrorTypes.Tenant.SupplierInvalid"></exception>
    /// <exception cref="ErrorTypes.Product.GroupInvalid"></exception>
    Task UpdateLineProduct(UpdateLineProductInput input);
    
    /// <summary>
    /// 添加线路产品规格信息
    /// </summary>
    Task AddLineProductSku(AddLineProductSkuInput input);
    
    /// <summary>
    /// 修改线路产品规格信息
    /// </summary>
    Task EditLineProductSku(EditLineProductSkuInput input);
    
    /// <summary>
    /// 删除线路产品规格信息
    /// </summary>
    Task RemoveLineProductSku(RemoveProductSkuInput input);
    
    /// <summary>
    /// 获取线路产品规格详情信息
    /// </summary>
    Task<GetLineProductSkuDetailOutput> GetLineProductSkuDetail(long id);

    /// <summary>
    /// 更新线路日历价库存
    /// </summary>
    Task UpdateLineSkuCalendarPrice(UpdateLineProductSkuCalendarPriceInput input);

    /// <summary>
    /// 获取线路产品所有sku的日历价信息
    /// </summary>
    Task<List<GetLineProductSkuCalendarPriceOutput>> GetCalendarPriceByLineProduct(GetLineProductSkuCalendarPriceInput input);

    /// <summary>
    /// 更新sku的行程信息
    /// </summary>
    Task UpdateLineSkuItinerary(UpdateLineProductSkuItineraryInput input);

    /// <summary>
    /// 查询sku的行程信息
    /// </summary>
    Task<List<GetItineraryListOutPut>> GetLineSkuItineraryGroupList(long productSkuId);

    /// <summary>
    /// 查询产品下所有sku的行程信息
    /// </summary>
    Task<List<GetSkuWithExistingItineraryOutput>> GetLineProductItineraryList(GetSkuWithExistingItineraryInput input);

    
    /// <summary>
    /// 更新线路产品供应商模块设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateLineProductSupplierModuleSetting(UpdateLineProductSupplierModuleSettingInput input);
    
    /// <summary>
    /// 更新线路产品开放渠道设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateLineProductChannelModuleSetting(UpdateLineProductChannelModuleSettingInput input);
    #endregion

    #region TicketProduct

    /// <summary>
    /// 创建券类产品
    /// </summary>
    /// <exception cref="ErrorTypes.Tenant.SupplierInvalid"></exception>
    /// <exception cref="ErrorTypes.Common.ResourceInvalid"></exception>
    /// <exception cref="ErrorTypes.Product.GroupInvalid"></exception>
    Task<long> AddTicketProduct(AddTicketInput input);
    
    /// <summary>
    /// 编辑券类产品
    /// </summary>
    /// <exception cref="ErrorTypes.Tenant.SupplierInvalid"></exception>
    /// <exception cref="ErrorTypes.Common.ResourceInvalid"></exception>
    /// <exception cref="ErrorTypes.Product.GroupInvalid"></exception>
    Task UpdateTicketProduct(UpdateTicketInput input);

    /// <summary>
    /// 获取券类产品详情
    /// </summary>
    Task<GetTicketOutput> GetTicketProductDetail(long productId,long supplierId);
    
    /// <summary>
    /// 查询券类产品分页列表
    /// </summary>
    Task<PagingModel<SearchTicketInfo>> SearchTicketProduct(SearchTicketInput input);
    #endregion

    #region SupplySetting

    /// <summary>
    /// 查询供应商供货的券类产品id列表
    /// </summary>
    Task<IEnumerable<long>> GetSupplySettingTicketProductIds(long supplierId);

    #endregion

    #region Restaurant

    /// <summary>
    /// 根据id集合获取餐饮店信息
    /// </summary>
    Task<List<GetRestaurantByIdsOutPut>> GetRestaurantByIds(GetRestaurantByIdsInput input);

    #endregion

    #region Store

    /// <summary>
    /// 根据id集合获取其他门店信息
    /// </summary>
    Task<List<GetStoreByIdsOutPut>> GetStoreByIds(GetStoreByIdsInput input);

    #endregion
}