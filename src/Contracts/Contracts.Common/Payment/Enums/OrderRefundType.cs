using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Payment.Enums;
public enum OrderRefundType
{
    /// <summary>
    /// 订单类退款
    /// </summary>
    Order = 0,

    /// <summary>
    /// 抵冲单退款
    /// </summary>
    OffsetOrder = 1,

    /// <summary>
    /// 预收款
    /// </summary>
    ReceiptPrepayment = 2,

    /// <summary>
    /// 无用的支付单退款 无须要人工审核的退款类型，通常是因为重复支付等情况导致的退款
    /// </summary>
    Useless = 3,
}
