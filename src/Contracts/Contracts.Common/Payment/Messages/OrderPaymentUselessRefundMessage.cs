using Contracts.Common.Payment.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Payment.Messages;
public class OrderPaymentUselessRefundMessage
{
    /// <summary>
    /// 支付单id
    /// </summary>
    public long OrderPaymentId { get; set; }
    /// <summary>
    /// 支付单类型 1-订单 2-预约单
    /// </summary>
    public OrderPaymentType OrderPaymentType { get; set; }
    /// <summary>
    /// 订单id
    /// </summary>
    public long OrderId { get; set; }
}
