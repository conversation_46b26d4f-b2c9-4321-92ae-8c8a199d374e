using Contracts.Common.Payment.Enums;

namespace Contracts.Common.Payment.Messages
{
    public class OrderRefundMessage
    {
        public long TenantId { get; set; }

        public OrderRefundType OrderRefundType { get; set; }

        /// <summary>
        /// 发起退款的订单退款单id
        /// </summary>
        public long RefundOrderId { get; set; }

        /// <summary>
        /// 支付单id
        /// </summary>
        public long? OrderPaymentId { get; set; }

        /// <summary>
        /// 订单id
        /// </summary>
        public long OrderId { get; set; }

        /// <summary>
        /// 限定原支付类型 1-易宝 2-线下
        /// </summary>
        public PayType? PayType { get; set; }

        /// <summary>
        /// 退款金额
        /// </summary>
        public decimal RefundAmount { get; set; }

        /// <summary>
        /// 退款原因
        /// </summary>
        public string Description { get; set; }
    }
}
