using Contracts.Common.Marketing.Enums;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;

namespace Contracts.Common.Payment.DTOs;
public class GetPaymentInfoOutput
{
    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 订单类型
    /// </summary>
    public OrderType OrderType { get; set; }

    /// <summary>
    /// 币种
    /// </summary>
    public string CurrencyCode { get; set; }

    public decimal Amount { get; set; }

    /// <summary>
    /// 支付手续费 需要额外支付的费用 Onerway支付方式存在
    /// </summary>
    public decimal PaymentFee { get; set; }

    public ProductBusinessType? ProductBusinessType { get; set; }

    /// <summary>
    /// 用户id
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// 分销商id 售卖渠道为B2B或OTA
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 联系人邮箱
    /// </summary>
    public string? ContactsEmail { get; set; }

    /// <summary>
    /// 售卖渠道
    /// </summary>
    public SellingChannels SellingChannels { get; set; }

    /// <summary>
    /// 最新支付状态
    /// </summary>
    public PayStatus PayStatus { get; set; }

    /// <summary>
    /// 主单状态 必填项
    /// </summary>
    public OrderStatus OrderStatus { get; set; }


    public BaseOrderStatus BaseOrderStatus { get; set; }
    
    /// <summary>
    /// 下单时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 支付过期时间
    /// </summary>
    public DateTime ExpiredTime { get; set; }
}
