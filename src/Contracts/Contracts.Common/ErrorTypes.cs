using System.ComponentModel;

namespace Contracts.Common;

public static class ErrorTypes
{
    public enum Common
    {
        [Description("不支持操作")]
        NotSupportedOperation,

        [Description("资源无效")]
        ResourceInvalid,

        [Description("产品无效")]
        ProductInvalid,

        [Description("第三方产品参数配置错误")]
        ThirdProductConfigurationError,
    }

    public enum Tenant
    {
        [Description("域名已存在")]
        SubdomainExist,

        [Description("TenantNotExist")]
        SubdomainNotExist,

        [Description("已授信分销商不可再添加")]
        AgencyCreditExist,

        [Description("分销商授信额度不可用")]
        AgencyCreditDisabled,

        [Description("无分销商额度")]
        CreditNotExist,

        [Description("分销商额度不足")]
        CreditNotEnough,

        [Description("额度支付记录不存在")]
        CreditPayRecordNotExist,

        [Description("Api供应商类型已存在")]
        SupplierApiTypeExist,

        [Description("非API供应商")]
        IsNotApiSupplier,

        [Description("非虚拟卡供应商")]
        IsNotVirtualCardSupplier,

        [Description("供应商无效")]
        SupplierInvalid,

        [Description("租户被停用")]
        TenantIsDisabled,

        [Description("分销商无效")]
        AgencyInvalid,

        [Description("当前商户非人民币币种")]
        TenantIsNotCNYCurrency,

        [Description("分销商密钥信息无效")]
        AgencySecretInvalid,

        [Description("API分销商类型已存在")]
        AgencyApiTypeExist,

        [Description("页面组件加载失败")]
        PageComponentFailToLoad,

        [Description("分销商信用代码已存在")]
        AgencyLicenceExist,

        [Description("分销商全称已存在")]
        AgencyFullNameExist,

        [Description("分销商凭证已通过")]
        AgencyCertificationPassed,

        [Description("签约主体名称已存在")]
        SignSubjectNameExist,

        [Description("内容长度超过限制")]
        ContentLengthExceedsTheLimit,

        [Description("退款金额大于该次充值金额")]
        AgencyChargeOverChagreAmount,

        [Description("预收款充值单存在退款订单")]
        ReceiptPrepaymentRefundIsExists,

        [Description("预付款余额不足")]
        ReceiptPrepaymentInsufficientBalance,

        [Description("等级配置无效")]
        AgencyLevelConfigInvalid,

        [Description("请完善区域信息")]
        RegionConfigSaveCheck,

        [Description("国家省份不能重复选择")]
        RegionConfigProvinciesExist,

        [Description("未配置保险供应商")]
        AgencyInsureSettingInvalid,

        #region 专题页
        [Description("专题页不存在")]
        B2BTopicPageNotExist,

        [Description("专题页组件不存在")]
        B2BTopicPageComponentNotExist,

        [Description("专题页标签名重复")]
        B2BTopicPageTagNameExist,
        #endregion

        #region Weiling
        [Description("卫瓴归属人不存在")]
        WeilingVestingNotExist,

        [Description("卫瓴企业创建失败")]
        WeilingCompanyCreateFailed,

        [Description("卫瓴配置不正确")]
        WeilingConfigError,

        [Description("卫瓴企业编辑失败")]
        WeilingCompanyEditFailed,

        [Description("卫瓴企业不存在")]
        WeilingCompanyNotExist,
        #endregion

        [Description("Api账号配置新增失败")]
        CitOpenApiAccountAddError,

        [Description("Api账号配置编辑失败")]
        CitOpenApiAccountUpdateError,

        [Description("非会员分销商")]
        NoVipAgency,

        [Description("分销商等级明细无效")]
        AgencyLevelDetailNotExist,

        [Description("Focussend配置不存在")]
        FocussendConfigNotExists,

        [Description("Focussend配置异常")]
        FocussendConfigError,

        [Description("请先配置补充协议")]
        AgencySideAgreementCheck,

        [Description("该补充协议记录无效")]
        AgencySideAgreementNotExists,

        [Description("分销商信用代码已存在")]
        ExistsLcenceNoCertified,

        [Description("延时支付不可用")]
        DelayedCreditDisabled,

        [Description("延时支付余额不足")]
        DelayedCreditNotEnough,

        [Description("延时支付记录不存在")]
        DelayedCreditPayRecordNotExist,

        [Description("Api字段验证")]
        ApiFieldValidater,

        [Description("等级设置参数错误")]
        LevelSettingError,

        [Description("等级设置成长值保级小于升级")]
        LevelSettingGrowthValueKipLessthanUp,

        [Description("等级设置成长值高级小于低级")]
        LevelSettingGrowthValueLessthanUp,

        [Description("开启预订协议所有等级必填")]
        LevelAgreementEnableLevelRequired,

        [Description("钉钉凭证配置不正确")]
        DintalkConfigError,

        [Description("币种不可变")]
        CurrencyCodeCantChange,

        [Description("分销商手机号码已存在")]
        AgencyContactNumberExist,

        [Description("分销商邮箱已存在")]
        AgencyEmailExist,

        [Description("延时支付已开启")]
        AgencyDelayedCreditExistOpen,

        [Description("授信已开启")]
        AgencyCreditExistOpen,

        [Description("分销商未审核")]
        AgencyNotAudited,

        [Description("部门已停用")]
        TenantDepartmentIsDisable,
    }

    public enum Resource
    {
        [Description("获取令牌凭证失败")]
        GetCredentialsFail,

        [Description("不支持Hop酒店")]
        HopHotelNotSupported,

        [Description("国家已存在")]
        CountryIsExists,

        [Description("国家存在省份信息")]
        CountryProvinceExists,

        [Description("国家省份已存在")]
        ProvinceIsExists,

        [Description("省份存在城市信息")]
        ProvinceCityExists,

        [Description("国家城市已存在")]
        CityIsExists,

        [Description("HOP酒店已存在")]
        HotelIsExists,

        [Description("新添加的HOP酒店请等待系统自动同步")]
        HotelWaitForSync,

        [Description("城市不存在")]
        CityNotExist,

        [Description("文件上传失败")]
        FileUploadFile,

        [Description("机场资源三字码重复")]
        AirportIATARepeat,

        [Description("未查询到HOP酒店信息")]
        HopHotelNotExists,

        [Description("酒店标签已存在")]
        GDSTagIsExists,
        [Description("没有GDS豪华酒店查询权限")]
        GDSHotelRPNoPermission,
    }

    public enum User
    {
        /// <summary>
        /// 账号或密码错误
        /// </summary>
        [Description("账号或密码错误")]
        AccountOrPasswordError,

        /// <summary>
        /// 账号被停用
        /// </summary>
        [Description("账号被停用")]
        AccountIsDisabled,

        /// <summary>
        /// 手机号已绑定其它账号
        /// </summary>
        [Description("手机号已绑定其它账号")]
        PhoneNumberIsDisabled,

        /// <summary>
        /// 账号或密码错误
        /// </summary>
        [Description("账号或密码错误")]
        PasswordError,

        /// <summary>
        /// 账号已存在
        /// </summary>
        [Description("账号已存在")]
        AccountIsExist,

        /// <summary>
        /// 账号未绑定
        /// </summary>
        [Description("账号未绑定")]
        AccountUnbound,

        /// <summary>
        /// 验证码错误
        /// </summary>
        [Description("验证码错误")]
        VerifyCodeError,

        /// <summary>
        /// 验证码获取太频繁
        /// </summary>
        [Description("验证码获取太频繁")]
        GetCodeTooFrequent,

        /// <summary>
        /// 折扣权益重复
        /// </summary>
        [Description("折扣权益只能存在一个")]
        DiscountRightsRepeat,

        /// <summary>
        /// 当前微信已存在绑定关系
        /// </summary>
        [Description("当前微信已经绑定了其它账号")]
        WechatIsBound,

        /// <summary>
        /// 老系统账号迁移
        /// </summary>
        [Description("老系统账号迁移")]
        DataMigration,

        /// <summary>
        /// 分销商不允许直接存在酒店标签
        /// </summary>
        [Description("分销商不允许直接存在酒店标签")]
        AgencyNotAllowedToExistHotelLabelDirect,

        /// <summary>
        /// 联系方式未验证
        /// </summary>
        [Description("联系方式未验证")]
        AccountContactNotVaild,

        /// <summary>
        /// 账号需要进行安全验证
        /// </summary>
        [Description("账号需要进行安全验证")]
        AccountNotSecurity,

        /// <summary>
        /// 安全验证超时
        /// </summary>
        [Description("安全验证超时")]
        AccountLoginSecurityInvalid,

        /// <summary>
        /// 当前邮箱已绑定其他账号
        /// </summary>
        [Description("当前邮箱已绑定其他账号")]
        EmailIsBound,

        /// <summary>
        /// 该账号不存在
        /// </summary>
        [Description("该账号不存在")]
        AccountIsNoFind,

        /// <summary>
        /// 填写用户的钉钉姓名与工号
        /// </summary>
        [Description("在用户详情正确填写的钉钉姓名与工号")]
        DingtalkUserIdIsNoFind,

        /// <summary>
        /// 钉钉绑定失败
        /// </summary>
        [Description("钉钉绑定失败")]
        DingtalkUserIdBindError,

        /// <summary>
        /// 邀请码不能为空/邀请码错误
        /// </summary>
        [Description("邀请码不能为空/邀请码错误")]
        InviteUserError,
        /// <summary>
        /// 该用户已被邀请
        /// </summary>
        [Description("该用户已被邀请")]
        InviteUserExistError,

        /// <summary>
        /// 邮箱已经被注册
        /// </summary>
        [Description("邮箱已经被注册")]
        EmailIsRegistered,

        /// <summary>
        /// 用户邮箱未配置
        /// </summary>
        [Description("用户邮箱未配置")]
        UserEmailIsEmpty,

        /// <summary>
        /// 用户邮箱密码未配置
        /// </summary>
        [Description("用户邮箱密码未配置")]
        UserEmailPasswordIsEmpty,
    }

    public enum Product
    {
        [Description("存在无效分组")]
        GroupInvalid,

        [Description("行程天数不一致")]
        ItineraryDayInconsistent,

        [Description("已存在该接送点")]
        DuplicateCarHailingPoint,

        [Description("免责声明已使用")]
        DisclaimerInUsed,

        [Description("免责声明不存在")]
        DisclaimerNotExist,

        [Description("产品不可售")]
        ProductDisabled,

        [Description("邮费模板不存在")]
        PostageTemplateNotExist,

        [Description("部分产品不支持配送")]
        DeliveryError,

        [Description("同平台产品运营人不可多个")]
        OperatorUserPlatformCannotMultiple,

        [Description("名称已存在")]
        ProductNameExist,

        [Description("模板名称已存在")]
        TempNameExist,

        [Description("默认模版不可删除")]
        DefaultTempUsed,

        [Description("非时段产品")]
        NonTimeSlot,
        
        [Description("供应商不提供中文版信息")]
        SupplierNotProvideChineseInfo,
        
        [Description("供应商不提供英文版信息")]
        SupplierNotProvideEnglishInfo,
    }

    public enum Order
    {
        [Description("部分日期不可下单")]
        SomeDatesAreUnavailable,

        [Description("无匹配数据")]
        NoMatchingData,

        [Description("可用券码不足")]
        TicketCodeNotEnough,

        [Description("券码需预约")]
        TicketCodeNeedReservation,

        [Description("未在使用有效期内")]
        OutOfValidityDate,

        [Description("产品价格有变动")]
        ProductPriceChange,

        [Description("退款份数大于购买数")]
        RefundQuantityInvalid,

        [Description("退款金额高于可退金额")]
        RefundAmountInvalid,

        [Description("出账金额高于订单出账")]
        RefundCostInvalid,

        [Description("未在预约时间段内")]
        OutOfTravelDate,

        [Description("无效券码")]
        TicketCodeInvalid,

        [Description("未选择预订日期")]
        InvalidTravelDate,

        [Description("未到入住日期")]
        CheckInDateNotReached,

        [Description("未到离店日期")]
        CheckOutDateNotReached,

        [Description("未获取到订单")]
        OrderNotFind,

        [Description("订单已存在")]
        OrderIsExist,

        [Description("订单不可退")]
        OrderCannotRefund,

        [Description("当前订单已关联售后单，不可退款，需提交工单处理")]
        OrderHasReceiptOffset,

        [Description("日历价格不可售")]
        CalendarNotEnable,

        [Description("每间房至少需要一个入住人")]
        EachRoomNeedOneGuest,

        [Description("团房最低起订间数不足")]
        NumberOfRoomsNotEnough,

        [Description("产品验证不通过")]
        OrderVerifyFail,

        [Description("购买份数不能减少")]
        BuyQuantityCanNotReduced,

        [Description("优惠总额需小于售卖总额")]
        DiscountAmountMustBeLessThanTotalAmount,

        [Description("出行人信息不完整")]
        TravelerNotCompleted,

        [Description("房间数超出范围")]
        NumberOfRoomsOutOfRange,

        [Description("房差数不正确")]
        RoomDiffQuantityInvalid,

        [Description("上车点未选择或不存在")]
        LineRallyPointNotExists,

        [Description("储值卡档位不存在")]
        StoredValueCardGearNotExist,

        [Description("包含内容失效，请联系客服处理")]
        StoredValueCardGiftNotExist,

        [Description("抵冲单已生成结算单")]
        OffsetOrderInSettlement,

        [Description("已生成结算单")]
        SettlementOrderHasGenerated,

        [Description("出账抵冲单只支持代销产品")]
        PayableOffsetOrderNeedAgencyProduct,

        [Description("价格分组配置有变动")]
        ChannelPriceSettingChange,

        [Description("采购门票已发货")]
        PurchaseVoucherDelivered,

        [Description("酒店订单状态不正确")]
        HotelOrderStatusInvalid,

        [Description("申请HOP工单失败")]
        ApplyHopWorkOrderFail,

        [Description("回复HOP工单失败")]
        ReplyHopWorkOrderFail,

        [Description("采购导入门票已使用")]
        PurchaseTicketAlreadyUsed,

        [Description("OTA订单已发货")]
        OTAOrderDelivered,

        [Description("不支持组合订单的渠道单号")]
        NotSupportOTACombinationOrder,

        [Description("不支持当前订单替换")]
        NotSupportOrderReplace,

        [Description("不支持系统生成的门票替换订单")]
        NotSupportSystemTicketReplace,

        [Description("不支持当前分销商替换订单")]
        NotSupportAgencyReplace,

        [Description("不支持多渠道单号")]
        NotSupportMultiChannelOrderNo,

        [Description("查无凭证信息")]
        VouchersNotFind,

        [Description("存在退款中的退款单")]
        OrderRefundInProcessing,

        [Description("入账抵冲单合计金额不能超过订单原支付金额")]
        ReceiptOffsetOrderRefundOutoffAmount,

        [Description("入账抵冲单合计金额不能超过订单销售金额")]
        ReceiptOffsetOrderRefundOutTotalSaleAmount,

        [Description("总销售金额大于0，不可添加额外赔付金额")]
        ReceiptOffsetOrderCannotCompensated,

        [Description("出账抵冲合计金额不能超过订单原采购金额")]
        PayableOffsetOrderRefundOutoffAmount,

        [Description("合计退款金额小于采购金额，不可添加额外赔付金额")]
        PayableOffsetOrderCannotCompensated,

        [Description("玩乐类订单待确认不能创建抵冲单")]
        OffsetOrderNeedConfirmed,

        [Description("当前订单已关联抵冲单，不可退款，可使用抵冲单平账")]
        RefundRefunseByOffsetOrder,

        [Description("超过最晚可预订时间")]
        ExceededBookingTime,

        [Description("投保失败")]
        InsurePolicyError,

        [Description("投保人信息不完全")]
        InsurePolicyHoldersError,

        [Description("保险信息验证异常")]
        InsureProductVaildError,

        [Description("OTA订单号已存在")]
        OtaOrderNoAlreadyExists,

        [Description("渠道商品未配置商家编码")]
        OtaNotConfiguredCode,

        [Description("非Hop酒店订单")]
        NonHopHotelOrder,

        [Description("有效期错误")]
        ValidityDateError,

        [Description("发票配置不存在")]
        InvoiceConfigNotExists,

        [Description("发票配置是默认")]
        InvoiceConfigIsDefault,

        [Description("发票配置重复")]
        InvoiceConfigIsExists,

        [Description("发票抬头不存在")]
        InvoiceTitleNotExists,

        [Description("订单不能申请发票")]
        CannotInvoice,

        [Description("自动对账excel解析失败")]
        AutoReconciliationExcelError,

        [Description("自动对账excel解析失败,携程渠道单号结构异常")]
        AutoReconciliationChannelOrderNoExcelError,

        [Description("应付款自动对账异常，应付货币与供应商货币不一致")]
        AutoCreateSettlementPayablesCurrencyNotMatch,

        [Description("修改线路订单联系人不能为空")]
        UpdateUpdateTravelLineOrderContactNotNull,

        [Description("修改线路订单出行人不能为空")]
        UpdateTravelLineOrderTravelerNotNull,

        [Description("乘客数超过最大乘客数限制")]
        OverMaxPassengers,

        [Description("行李数超过最大行李数限制")]
        OverMaxBaggages,

        [Description("用车服务项目必选-不少于1件")]
        CarServiceItemMustOneBaggage,

        [Description("用车服务项目必选-不少于乘客数")]
        CarServiceItemMustGreaterThanPassengers,

        [Description("用车服务项目必选-不少于行李数")]
        CarServiceItemMustGreaterThanBaggages,

        [Description("不满足立即投保条件")]
        InsurePolicyImmediately,
        [Description("订单设置不存在")]
        OrderPrintSettingNotExists,

        [Description("出行时间不正确")]
        TravelDateError,

        [Description("出行人数量不匹配")]
        TravelerQuantityError,
        [Description("出行人信息不匹配")]
        TravelerInformationError,

        [Description("存在执行中的批量操作")]
        ExistsOperationTaskProgressing,

        [Description("采购单号为空")]
        SupplierOrderIdIsEmpty,

        [Description("询价结果有效期失效")]
        CarProductQuoteExpire,

        [Description("字段模板验证")]
        OrderFieldValidater,

        [Description("字段模板改变")]
        OrderFieldChange,

        [Description("OP认领次数超额")]
        OPSetTrackingUserExcess,

        [Description("采购导入Excel格式错误")]
        PurchaseOrderExcelError,

        [Description("采购库存盘点Excel格式错误")]
        PurchaseInventoryCheckExcelError,

        [Description("失败订单状态已变更")]
        SyncFailOrderStatusChanged,

        [Description("供应端下单前置校验错误")]
        OpenSupplierOrderPreCheckError,

        [Description("供应端下单前置校验出发地信息错误")]
        OpenSupplierOrderPreCheckDepartureError,

        [Description("供应端下单前置校验目的地信息错误")]
        OpenSupplierOrderPreCheckDestinationError,

        [Description("供应端下单前置校验联系人邮箱信息错误")]
        OpenSupplierOrderPreCheckContactEmailError,

        [Description("供应端下单前置校验附加信息信息错误")]
        OpenSupplierOrderExtraInfoError,

        [Description("订单取消失败")]
        OrderCancelFail,

        [Description("超过最晚修改时间")]
        ExceededUpdateTime,

        [Description("证件号不能为空")]
        IdCardIsRequired,

        [Description("收款结算单多次收款的总实收总额不能超过收款结算单应收总额")]
        ReceivedAmountNotMoreThan,

        [Description("收款结算单多次收款币种不一致")]
        ReceivedAmountCurrencyCodeChanged,

        [Description("下单超时，请重新下单")]
        OrderTimeout,

        [Description("钉钉审批单类型错误")]
        DingtalkApplayMoneyTypeError,

        [Description("钉钉审批单是必须")]
        DingtalkApplayIsRequired,

        [Description("钉钉审批单必填字段数据缺失")]
        DingtalkApplayRequiredFieldNotFind,

        [Description("请先配置当前地区的发单模版")]
        GroupBookingHotelInquiryTemplateNotConfig,

        [Description("发单模版指定国家已存在")]
        GroupBookingHotelInquiryTemplateCountryIsExists,

        [Description("发单模版解析错误，请检查变量是否正确")]
        GroupBookingHotelInquiryTemplateParseError,

        [Description("渠道单号已存在")]
        ChannelOrderNoIsExists,

        [Description("分销商不一致")]
        AgencyIsInconsistent,

        [Description("重复区域配置")]
        DuplicateAreaConfiguration,

        [Description("不支持的指标类型")]
        NotSupportIndicatorsType,

        [Description("区域配置有改变")]
        AreaChange,

        [Description("组合订单售后退款金额错误")]
        CombinationOrderAfterSalseRefundAmountError,

        [Description("组合异常单实收总额错误")]
        CombinationAbnormalOrderTotalAmountError,

        [Description("错误供应商")]
        SupplierApiTypeError,

        [Description("团房酒店没有配置指派人")]
        GroupBookingHotelNotAssignor,

    }

    public enum Marketing
    {
        [Description("存在无效优惠券")]
        ExistCouponInvalid,

        [Description("结束时间无效")]
        EndTimeInvalid,

        [Description("限购活动不在进行中状态")]
        FlashSaleNotInProcess,

        [Description("促销活动产品超过每人限购数量")]
        FlashSaleOverProductLimit,

        [Description("限购活动库存不足")]
        FlashSaleInventoryNotEnough,

        [Description("券码无效")]
        CouponCodeInvalid,

        [Description("券码已使用")]
        CouponCodeHadUsed,

        [Description("券码未在使用有效期内")]
        CouponCodeOutOfValidityDate,

        [Description("券码资源无效")]
        CouponCodeResourceInvalid,

        [Description("优惠券不在有效期内")]
        CouponOutOfValidityDate,

        [Description("优惠券数量不足")]
        CouponQuantityNotEnough,

        [Description("抽奖已开始或结束")]
        LotteryHasStartedOrFinished,

        [Description("抽奖活动未开始")]
        LotteryNotStarted,

        [Description("抽奖活动已结束")]
        LotteryFinished,

        [Description("奖品配置达到限额")]
        LotteryAwardReachingLimit,

        [Description("开始时间大于结束时间")]
        BeginTimeGreaterThanEndTime,

        [Description("可用次数不足")]
        UsableNumberNotEnough,

        [Description("剩余数量不足")]
        RemainingQuantityNotEnough,

        [Description("B2B不支持当前产品类型")]
        B2BNotSupportedProductType,

        [Description("B2B不支持当前优惠券类型")]
        B2BNotSupportedCouponType,

        [Description("优惠券不可用")]
        UserCouponDisabled,

        [Description("创建人手机号缺失")]
        PromoterPhoneNumberMissing,
    }

    public enum Payment
    {
        [Description("入网未完成，无法获取商户配置")]
        YeeMerNotConfig,

        [Description("商户提现限1天最多提现1次")]
        WithdrawalDailyLimit,

        [Description("可提现余额不足")]
        WithdrawalInsufficientAmount,

        [Description("订单已支付，无需重复支付")]
        OrderHasPaid,

        [Description("分销商额度支付失败")]
        CreditPayFail,

        [Description("超过记录条数")]
        Exceedance,

        [Description("支付方式不支持该币种")]
        CurrencyNonsupport,

        [Description("易宝账号不允许修改")]
        YeeAccountCannotBeModified,

        [Description("在线支付账单账期重复")]
        PaymentBillPeriodsRepeated,

        [Description("预收款余额不足")]
        ReceiptPrepaymentInsufficientBalance,

        [Description("供应商预付款流水已存在")]
        SupplierPrepaymentFlowAlreadyExists,

        [Description("供应商名称已存在")]
        SupplierNameExists,

        [Description("银行账户不存在")]
        BankAccountNotExist,

        [Description("银行账户的境外账户配置不存在")]
        AbroadConfigNotExist,

        [Description("银行账户的虚拟卡配置不存在")]
        VccConfigNotExist,

        [Description("虚拟卡银行账户币种不匹配")]
        VccCurrencyNotMatch,

        [Description("虚拟卡采购信息不存在")]
        VccPurchaseNotFound,

        [Description("虚拟卡开卡失败")]
        VccPurchaseCreateFail,

        [Description("虚拟卡开卡金额错误")]
        VccPurchaseAmountError,
    }

    public enum Notify
    {
        [Description("邮箱认证失败")]
        MailBoxAuthenticationError,

        [Description("host or port error")]
        MailBoxConnectError,

        [Description("员工通知配置已存在")]
        NotifySettingExisted,

        [Description("通知功能未开启")]
        NotifyNotOpen,

        [Description("短信签名已存在")]
        SmsHeaderExist,
    }

    public enum Permission
    {
        [Description("套餐存在绑定")]
        PackageHasBind,

        [Description("用户未设置角色")]
        UserRoleNotExist,

        [Description("套餐不存在")]
        PackageNotExist,

        [Description("无查看全部订单权限")]
        OrderViewallDataNotExist,
    }

    public enum Hotel
    {
        [Description("商户未启用Ota运营")]
        TenantOtaIsDisabled,

        [Description("无效酒店")]
        HotelInvalid,

        [Description("无效房型")]
        RoomInvalid,

        [Description("无效价格策略")]
        PriceStrategyInvalid,

        [Description("存在日历价格无采购价")]
        ExistCalendarPriceWithoutCostPrice,

        [Description("日历价格不足")]
        PricesNotEnough,

        [Description("尾房不可售")]
        EndRoomNotBook,

        [Description("钟点房不可售")]
        HourRoomNotBook,

        [Description("连住房不可售")]
        StayLongDiscountNotBook,

        [Description("日历价格不可售")]
        CalendarNotEnable,

        [Description("该酒店未开通OTA渠道")]
        HopOtaIsDisabled,

        [Description("添加任务在进行中")]
        TaskInProgress,

        [Description("酒店正在推送匹配中")]
        HotelMappingIsProcessing,

        [Description("酒店以匹配成功，无需重复操作")]
        HotelMappingHasSuccessed,

        [Description("外部服务请求异常")]
        ExternalServiceRequestException,

        [Description("酒店标签已存在")]
        TagIsExists,

        [Description("酒店组合已存在")]
        HotelCombinationIsExists,

        [Description("酒店组合Sku已存在")]
        HotelCombinationSkuIsExists,

        [Description("酒店组合Sku不足")]
        HotelCombinationSkuNotEnough,

        [Description("专题正在使用中，无法删除")]
        SpecializedHotelInUse,
    }

    public enum Inventory
    {
        [Description("产品库存不足")]
        ProductInventoryNotEnough,

        [Description("获取库存失败")]
        GetInventoryFail,

        [Description("库存停售")]
        StoppedSelling,
    }

    public enum WeChat
    {
        [Description("Code已被使用")]
        CodeBeenUsed,

        [Description("Code无效")]
        CodeInvalid,

        [Description("未配置微信信息")]
        WechatNotConfig,

        [Description("已存在相同版本号的模板")]
        AppletTemplateUserVersionExists,

        [Description("模板类别和服务类别不相同")]
        TemplateAndServiceCategoryNotEquals,

        [Description("模板数量超过限制")]
        TemplateNumExceeds,

        [Description("未授权微信或已取消授权")]
        WechatUnauthorized,

        [Description("获取AuthorizerToken失败")]
        GetAuthorizerTokenFailed,

        [Description("该APPID已存在配置项")]
        WechatAppIdHasConfig,
    }

    public enum Scenic
    {
        [Description("存在重复的日期价格")]
        PriceDuplicationOfDate,

        [Description("只允许指定日期的价格")]
        OnlyAllowDesignatedDatePrice,

        [Description("景区有产品")]
        ScenicSpotHasProducts,

        [Description("景区无效")]
        ScenicSpotInvalid,

        [Description("景区门票无效")]
        ScenicTicketsInvalid,

        [Description("产品组合名称已存在")]
        TicketsCombinationNameIsExists,

        [Description("产品组合无效")]
        TicketsCombinationInvalid,

        [Description("产品组合不支持时段产品")]
        TicketsCombinationNotSupportTimeSlot,

        [Description("非时段产品")]
        NonTimeSlot,

        [Description("查询第三方价库信息失败")]
        QueryThirdPriceInventoryError,

        [Description("第三方门票类型不匹配")]
        ThirdTicketTypeMismatch,

        [Description("无效时段")]
        InvalidTimeSlot,

        [Description("查询第三方产品信息失败")]
        QueryThirdProductDetailError,

        [Description("组合套餐产品数据变动")]
        CombinationPackageProductDataChanged,
    }

}