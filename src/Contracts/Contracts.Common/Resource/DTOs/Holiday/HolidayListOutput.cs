using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Resource.DTOs.Holiday;

public class HolidayDto
{
    public string Name { get; set; }

    public DateTime Date { get; set; }

    /// <summary>
    /// false 的时候是节假日的调休工作日
    /// </summary>
    public bool IsOffDay { get; set; }
}

public class HolidayListOutput
{
    /// <summary>
    /// 年份
    /// </summary>

    public int Year { get; set; }

    /// <summary>
    /// 节假日详情
    /// </summary>

    public List<HolidayDto> Holidays { get; set; } = new List<HolidayDto>();
}
