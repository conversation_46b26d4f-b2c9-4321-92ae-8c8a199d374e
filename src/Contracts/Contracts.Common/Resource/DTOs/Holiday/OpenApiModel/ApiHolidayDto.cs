using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Resource.DTOs.Holiday.OpenApiModel;
public class ApiHolidayDto
{
    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("date")]
    public string Date { get; set; }

    [JsonProperty("isOffDay")]
    public bool IsOffDay { get; set; }
}

public class ApiHolidayResult
{
    [JsonProperty("days")]
    public List<ApiHolidayDto> Days { get; set; }
}
