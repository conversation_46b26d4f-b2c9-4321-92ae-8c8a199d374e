using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Resource.DTOs.MaxkbBi;
public class GetGroupHotelByPromtOutput
{
    public List<PromtGroupHotelDto> Hotels { get; set; } = new();
}

public class PromtGroupHotelDto
{
    /// <summary>
    /// 城市名称
    /// </summary>

    public string? CityName { get; set; }
    /// <summary>
    /// 酒店名称
    /// </summary>

    public string? HotelName { get; set; }

    /// <summary>
    /// 入住时间
    /// </summary>

    public DateTime? CheckInDate { get; set; }

    /// <summary>
    /// 离店时间
    /// </summary>

    public DateTime? CheckOutDate { get; set; }

    /// <summary>
    /// 含早情况
    /// </summary>

    public string? Breakfast { get; set; }

    /// <summary>
    /// 间夜售价
    /// </summary>

    public decimal? AveragePrice { get; set; }


    /// <summary>
    /// 间夜售价币种
    /// </summary>

    public string? CurrencyCode { get; set; }

    /// <summary>
    /// 成人数量
    /// </summary>

    public int? AdultCount { get; set; }

    /// <summary>
    /// 房间数
    /// </summary>

    public int? RoomCount { get; set; }

    /// <summary>
    /// 房型
    /// </summary>

    public string? RoomName { get; set; }

    /// <summary>
    /// 床型
    /// </summary>

    public string? BedTypeName { get; set; }

    /// <summary>
    /// 儿童政策
    /// </summary>

    public string? ChildPolicy { get; set; }

    /// <summary>
    /// 付款政策
    /// </summary>

    public string? PaymentPolicy { get; set; }

    /// <summary>
    /// 报价有效期
    /// </summary>

    public string? OfferValidity { get; set; }
    /// <summary>
    /// 取消政策
    /// </summary>

    public string? CancellationPolicy { get; set; }

    /// <summary>
    /// 给客户备注
    /// </summary>

    public string? CustomerComments { get; set; }
}

public class PromtHotelDto
{
    /// <summary>
    /// 城市名称
    /// </summary>
    [JsonProperty("目的地")]
    public string? CityName { get; set; }
    /// <summary>
    /// 酒店名称
    /// </summary>
    [JsonProperty("酒店")]
    public string? HotelName { get; set; }

    /// <summary>
    /// 入住时间
    /// </summary>
    [JsonProperty("入住日期")]
    public DateTime? CheckInDate { get; set; }

    /// <summary>
    /// 离店时间
    /// </summary>
    [JsonProperty("离店日期")]
    public DateTime? CheckOutDate { get; set; }

    /// <summary>
    /// 含早情况
    /// </summary>
    [JsonProperty("早餐情况")]
    public string? Breakfast { get; set; }

    /// <summary>
    /// 间夜售价
    /// </summary>
    [JsonProperty("单夜价格")]
    public decimal? Price { get; set; }

    /// <summary>
    /// 成人数量
    /// </summary>
    [JsonProperty("成人数量")]
    public int? AdultCount { get; set; }

    /// <summary>
    /// 房间数
    /// </summary>
    [JsonProperty("房间数量")]
    public int? RoomCount { get; set; }

    /// <summary>
    /// 房型
    /// </summary>
    [JsonProperty("房型名称")]
    public string? RoomName { get; set; }

    /// <summary>
    /// 床型
    /// </summary>
    [JsonProperty("床型")]
    public string? BedTypeName { get; set; }

    /// <summary>
    /// 儿童政策
    /// </summary>
    [JsonProperty("儿童政策")]
    public string? ChildPolicy { get; set; }

    /// <summary>
    /// 付款政策
    /// </summary>
    [JsonProperty("付款政策")]
    public string? PaymentPolicy { get; set; }

    /// <summary>
    /// 报价有效期
    /// </summary>
    [JsonProperty("报价有效期")]
    public string? OfferValidity { get; set; }
    /// <summary>
    /// 取消政策
    /// </summary>
    [JsonProperty("取消政策")]
    public string? CancellationPolicy { get; set; }

    /// <summary>
    /// 给客户备注
    /// </summary>
    [JsonProperty("给客户备注")]
    public string? CustomerComments { get; set; }
}
