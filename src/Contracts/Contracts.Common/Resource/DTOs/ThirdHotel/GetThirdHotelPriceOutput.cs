using Contracts.Common.Hotel.Enums;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Tenant.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Resource.DTOs.ThirdHotel;

public class GetThirdHotelPriceOutput
{
    public long ResourceHotelId { get; set; }

    public IEnumerable<ThirdHotelRoomOutput> Rooms { get; set; }
}
public class ThirdHotelPricestrategyOutput
{
    /// <summary>
    /// 第三方平台
    /// </summary>
    public SupplierApiType SupplierApiType { get; set; }

    /// <summary>
    /// 第三方报价计划id
    /// </summary>
    public string PricestrategyId { get; set; }

    #region 报价计划信息

    public string Name { get; set; }

    public string ENName { get; set; }

    /// <summary>
    /// 价格策略类型
    /// </summary>
    public PriceStrategyType PriceStrategyType { get; set; } = PriceStrategyType.StandardRoom;

    /// <summary>
    /// 供应商Id
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 提前几小时预订
    /// </summary>
    public int BookingHoursInAdvance { get; set; }

    /// <summary>
    /// 含早数
    /// </summary>
    [Obsolete]
    public int NumberOfBreakfast { get; set; }

    /// <summary>
    /// 餐食类型
    /// </summary>
    public BoardCodeType BoardCodeType { get; set; }

    /// <summary>
    /// 餐食数量
    /// </summary>
    public int BoardCount { get; set; }

    /// <summary>
    /// 是否自动确认
    /// </summary>
    public bool IsAutoConfirm { get; set; }

    /// <summary>
    /// 非立即确认报价预计确认时长，单位：分钟
    /// </summary>
    public int? ConfirmByMins { get; set; }

    /// <summary>
    /// 是否上架
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 采购价币种
    /// </summary>
    public string CostCurrencyCode { get; set; } = Currency.CNY.ToString();

    /// <summary>
    /// 售价币种
    /// </summary>
    public string SaleCurrencyCode { get; set; } = Currency.CNY.ToString();

    /// <summary>
    /// 国籍限制，空数组表示不限制
    /// </summary>
    public IEnumerable<string> NationalNames { get; set; } = Enumerable.Empty<string>();

    /// <summary>
    /// 该价格仅适用于nationalityCodes的客人 空数组表示不限制
    /// </summary>
    public IEnumerable<string>? NationalityCodes { get; set; }

    /// <summary>
    /// 该价格不适用于nationalityCodes的客人 空数组表示不限制
    /// </summary>
    public IEnumerable<string>? NonNationalityCodes { get; set; }

    #region 连住策略单独部分

    /// <summary>
    /// 连住晚数及以上
    /// </summary>
    public int NumberOfNights { get; set; }

    /// <summary>
    /// 限制最大连住天数
    /// </summary>
    public int? LimitNumberOfNights { get; set; }

    #endregion

    #region 团房策略单独部分

    /// <summary>
    /// 最小预订间数
    /// </summary>
    public int NumberOfRooms { get; set; }

    #endregion


    /// <summary>
    /// 最大入住人数
    /// </summary>
    public int MaxOccupancy { get; set; }

    #endregion

    /// <summary>
    /// 取消规则
    /// </summary>
    [Obsolete]
    public ThirdPriceStrategyCancelRuleOutput CancelRule => CancelRules?.FirstOrDefault() ?? new ThirdPriceStrategyCancelRuleOutput { CancelRulesType = CancelRulesType.CannotCancel };

    public List<ThirdPriceStrategyCancelRuleOutput> CancelRules { get; set; }

    /// <summary>
    /// 日历价格
    /// </summary>
    public IEnumerable<ThirdPriceStrategyCalendarPriceOutput> Calendars { get; set; }

    /// <summary>
    /// 总采购价
    /// </summary>
    public decimal? TotalCost { get; set; }

    /// <summary>
    /// 是否直采
    /// </summary>
    public bool IsDirect { get; set; }

    /// <summary>
    /// 敏感度类型 
    /// </summary>
    public SellHotelTag? Tag { get; set; }

    /// <summary>
    /// 预订礼遇
    /// </summary>
    public string? BookingBenefits { get; set; }

    /// <summary>
    /// 供应商渠道类型 1-直采 2-EBK 3-CM渠道 4-直连
    /// </summary>
    public ApiHotelChannelType ChannelType { get; set; }

    /// <summary>
    /// 报价计划类型 0-日历房 1-商旅 2-高德 3-团房 4-直销
    /// </summary>
    public ApiHotelSaleType? SaleType { get; set; }

    /// <summary>
    /// 预订代码 汇智酒店特有 
    /// </summary>
    public string? PreBookingCode { get; set; }
}

public class ThirdPriceStrategyCancelRuleOutput
{
    /// <summary>
    /// 取消策略类型
    /// </summary>
    public CancelRulesType CancelRulesType { get; set; }

    /// <summary>
    /// 入住日前x天
    /// </summary>
    public int BeforeCheckInDays { get; set; }

    /// <summary>
    /// 入住日前x天时间 如14:00
    /// </summary>
    public TimeSpan? BeforeCheckInTime { get; set; }

    /// <summary>
    /// 入住当日 - 时间 如14:00
    /// </summary>
    public TimeSpan CheckInDateTime { get; set; }

    /// <summary>
    /// 取消收费类型
    /// </summary>
    public CancelChargeType CancelChargeType { get; set; }

    /// <summary>
    /// 收费值
    /// </summary>
    public int ChargeValue { get; set; }
}


public class ThirdPriceStrategyCalendarPriceOutput
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 成本价/采购价
    /// </summary>
    public decimal CostPrice { get; set; }

    /// <summary>
    /// 房量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 是否可超卖
    /// </summary>
    public bool OverSaleable { get; set; }

    /// <summary>
    /// 房态
    /// </summary>
    public bool Enabled { get; set; }
}



public class ThirdHotelRoomOutput
{
    public long ResourceRoomId { get; set; }

    #region 房型信息

    /// <summary>
    /// 第三方房型id
    /// </summary>
    public string RoomId { get; set; }

    public string ENName { get; set; }

    /// <summary>
    /// 中文名称
    /// </summary>
    public string ZHName { get; set; }

    /// <summary>
    /// 窗户
    /// </summary>
    public WindowType WindowType { get; set; }

    /// <summary>
    /// 面积 - 最小
    /// </summary>
    public decimal AreaMin { get; set; }

    /// <summary>
    /// 面积 - 最大
    /// </summary>
    public decimal AreaMax { get; set; }

    /// <summary>
    /// 楼层 - 最低
    /// </summary>
    public int FloorMin { get; set; }

    /// <summary>
    /// 楼层 - 最高
    /// </summary>
    public int FloorMax { get; set; }

    /// <summary>
    /// 最大入住人数
    /// </summary>
    public int MaximumOccupancy { get; set; }

    /// <summary>
    /// 房间数量
    /// </summary>
    public int RoomQuantity { get; set; }

    public string BedType { get; set; }


    #endregion

    public IEnumerable<ThirdHotelPricestrategyOutput> Pricestrategies { get; set; }
}