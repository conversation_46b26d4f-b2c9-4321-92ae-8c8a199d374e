
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Resource.Enums;

namespace Contracts.Common.Resource.DTOs.ThirdHotel;
public class CheckAvailabilityOutput
{
    public CheckPriceStrategySaleCode CheckCode { get; set; }
    public string Msg { get; set; }

    /// <summary>
    /// bookingCode，试单接口返回的bookingCode 汇智酒店V3接口需要传入
    /// </summary>
    public string? BookingCode { get; set; }

    /// <summary>
    /// 第三方平台酒店id
    /// </summary>
    public string HotelId { get; set; }

    /// <summary>
    /// 第三方平台酒店房型id
    /// </summary>
    public string RoomId { get; set; }

    /// <summary>
    /// 第三方平台酒店报价计划id
    /// </summary>
    public string PricestrategyId { get; set; }
    public string Name { get; set; }

    public string ENName { get; set; }

    /// <summary>
    /// 房型信息
    /// </summary>
    public ThirdHotelRoomOutput? Room { get; set; }

    /// <summary>
    /// 供应商Id
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 是否自动确认 立即确认
    /// </summary>
    public bool IsAutoConfirm { get; set; }

    /// <summary>
    /// 非立即确认报价预计确认时长，单位：分钟
    /// </summary>
    public int? ConfirmByMins { get; set; }

    [Obsolete]
    public ThirdPriceStrategyCancelRuleOutput CancelRule => CancelRules?.FirstOrDefault() ?? new ThirdPriceStrategyCancelRuleOutput { CancelRulesType = CancelRulesType.CannotCancel };

    public List<ThirdPriceStrategyCancelRuleOutput> CancelRules { get; set; }

    public int MaxOccupancy { get; set; }

    [Obsolete]
    public int BreakfastCount { get; set; }

    /// <summary>
    /// 餐食类型
    /// </summary>
    public BoardCodeType? BoardCodeType { get; set; }

    /// <summary>
    /// 餐食数量
    /// </summary>
    public int BoardCount { get; set; }

    /// <summary>
    /// 采购价货币
    /// </summary>
    public string CostCurrencyCode { get; set; } = Currency.CNY.ToString();

    /// <summary>
    /// 采购总价
    /// </summary>
    public decimal TotalPrice { get; set; }

    public IEnumerable<ThirdPriceStrategyCalendarPriceOutput> CalendarPrices { get; set; } = Enumerable.Empty<ThirdPriceStrategyCalendarPriceOutput>();

    /// <summary>
    /// 税费提醒
    /// </summary>
    public string? TaxDescription { get; set; }

    /// <summary>
    /// 税费说明
    /// </summary>
    public CheckAvailabilityArrivalTaxFeeOutput[]? ArrivalTaxFees { get; set; }
}

public class CheckAvailabilityHotelOutput
{
    public long ResourceHotelId { get; set; }

    /// <summary>
    /// 第三方平台酒店id
    /// </summary>
    public string HotelId { get; set; }

    /// <summary>
    /// 酒店名称
    /// </summary>
    public string? HotelName { get; set; }
}

public class CheckAvailabilityRoomOutput
{
    public long ResourceRoomId { get; set; }

    /// <summary>
    /// 第三方平台房型id
    /// </summary>
    public string RoomId { get; set; }

    public string ZHName { get; set; }
}

public class CheckAvailabilityArrivalTaxFeeOutput
{
    /// <summary>
    /// 费用类型
    /// </summary>
    public HotelFeeType Type { get; set; }

    /// <summary>
    /// 是否强制，某些酒店可能允许客人选择是否支付度假村费
    /// </summary>
    public bool IsMandatory { get; set; }

    /// <summary>
    /// 费用的具体金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 币种
    /// </summary>
    public string Currency { get; set; }
}

