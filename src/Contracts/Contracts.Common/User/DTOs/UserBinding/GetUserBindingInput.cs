using Contracts.Common.User.Enums;

namespace Contracts.Common.User.DTOs.UserBinding;
public class GetUserBindingInput
{
    public long[] UserIds { get; set; }

    /// <summary>
    /// 第三方平台
    /// </summary>
    public UserBindPlatformType? PlatformType { get; set; }

    public int? SysRole { get; set; }
}


public class GetUserBindingOutput
{
    public long UserId { get; set; }

    /// <summary>
    /// 第三方平台code, 微信openid/小程序openid等等
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// 第三方平台
    /// </summary>
    public UserBindPlatformType PlatformType { get; set; }

}