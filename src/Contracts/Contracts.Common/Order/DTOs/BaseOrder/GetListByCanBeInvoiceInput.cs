using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;

namespace Contracts.Common.Order.DTOs.BaseOrder;
public class GetListByCanBeInvoiceInput : PagingInput
{
    /// <summary>
    /// 订单号
    /// </summary>
    public long? BaseOrderId { get; set; }

    /// <summary>
    /// 多个订单号
    /// </summary>
    public List<long> BaseOrderIds { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 订单类型
    /// </summary>
    public OrderType? OrderType { get; set; }

    /// <summary>
    /// 完成开始时间
    /// </summary>
    public DateTime? FinishBeginTime { get; set; }

    /// <summary>
    /// 完成结束时间
    /// </summary>
    public DateTime? FinishEndTime { get; set; }

    /// <summary>
    /// 支付方式
    /// </summary>
    public PayType[] PaymentType { get; set; } = Array.Empty<PayType>();

    /// <summary>
    /// 是否未开票
    /// </summary>
    public bool IsToBeInvoiced { get; set; }

    /// <summary>
    /// 发票状态
    /// </summary>
    public InvoiceStatus[] InvoiceStatus { get; set; } = Array.Empty<InvoiceStatus>();

    /// <summary>
    /// 开票来源
    /// </summary>
    public InvoiceSourceChannel SourceChannel { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public long? UserId { get; set; }

    /// <summary>
    /// 分销商ID
    /// </summary>
    public long? AgencyId { get; set; }
}
