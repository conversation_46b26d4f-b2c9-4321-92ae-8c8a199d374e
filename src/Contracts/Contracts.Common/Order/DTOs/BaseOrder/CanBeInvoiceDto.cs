using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;

namespace Contracts.Common.Order.DTOs.BaseOrder;
public class CanBeInvoiceDto
{
    public long? BaseOrderId { get; set; }
    public long? ReceiptSettlementOrderId { get; set; }

    public long? ReceiptSettlementOrderDetailsId { get; set; }

    /// <summary>
    /// 资源名称
    /// </summary>
    public string? ResourceName { get; set; }

    public string ProductName { get; set; }

    public string ProductSkuName { get; set; }
    /// <summary>
    /// 付款方式
    /// </summary>
    public PayType PaymentType { get; set; }
    /// <summary>
    /// 订单金额
    /// </summary>
    public decimal OrderAmount { get; set; }

    /// <summary>
    /// 发票状态
    /// </summary>
    public InvoiceStatus? InvoiceStatus { get; set; }
    /// <summary>
    /// 发票流水号
    /// </summary>
    public string InvoiceSerialNo { get; set; }

    /// <summary>
    /// 是否能开票
    /// </summary>
    public bool IsCanInvoice { get; set; } = true;
}
