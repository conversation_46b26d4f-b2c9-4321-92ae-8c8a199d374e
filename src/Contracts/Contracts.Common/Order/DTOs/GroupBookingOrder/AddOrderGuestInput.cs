using Contracts.Common.Order.DTOs.HotelOrder;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.GroupBookingOrder;
public class AddOrderGuestInput
{
    /// <summary>
    /// 分销商id
    /// </summary>
    public long? AgencyId { get; set; }

    public long? UserId{ get; set; }

    /// <summary>
    /// 团房单id
    /// </summary>
    public long GroupBookingOrderId { get; set; }

    /// <summary>
    /// 入住人信息
    /// </summary>
    public OrderGuestInput[] OrderGuests { get; set; } = Array.Empty<OrderGuestInput>();

}

public class OrderGuestInput 
{
    /// <summary>
    /// 团房单子项id
    /// </summary>
    public long GroupBookingOrderItemId { get; set; }

    /// <summary>
    /// 入住人附件文件路径
    /// </summary>
    public string? FilePath { get; set; }

    /// <summary>
    /// 入住人信息
    /// </summary>
    public IEnumerable<HotelOrderGuestInfo> GuestInfos { get; set; } = Enumerable.Empty<HotelOrderGuestInfo>();
}