using Contracts.Common.Order.DTOs.GroupBookingOrder;
using Contracts.Common.Order.DTOs.HotelOrder;
using Contracts.Common.Tenant.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.ExportPdf;
public class ExportGroupBookingOrderPdfInput: ExportHotelOrderPdfBaseInput
{
    /// <summary>
    /// 团房单号
    /// </summary>
    public long? GroupBookingOrderId { get; set; }

    /// <summary>
    /// 申请单id
    /// </summary>
    public long? GroupBookingApplicationFormId { get; set; }

    /// <summary>
    /// 团房订单详情
    /// </summary>
    public List<ExportGroupBookingOrderOutput> Hotels { get; set; }
}

public class ExportGroupBookingOrderOutput
{
      /// <summary>
    /// 酒店Id
    /// </summary>
    public long HotelId { get; set; }
    /// <summary>
    /// 酒店名称
    /// </summary>
    public string HotelName { get; set; }

    public string? HotelEnName { get; set; }

    /// <summary>
    /// 酒店地址
    /// </summary>
    public string? HotelAddress { get; set; }


    /// <summary>
    /// 酒店地址
    /// </summary>
    public string? HotelEnAddress { get; set; }
    /// <summary>
    /// 酒店联系电话
    /// </summary>
    public string? HotelTelePhone { get; set; }

    /// <summary>
    /// 订单详情
    /// </summary>
    public List<ExportGroupBookingOrderItemOutput> OrderItems { get; set; }
}

public class ExportGroupBookingOrderItemOutput
{
    public long Id { get; set; }

    /// <summary>
    /// 主单id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 酒店Id
    /// </summary>
    public long HotelId { get; set; }

    /// <summary>
    /// 酒店单id
    /// </summary>
    public long HotelOrderId { get; set; }

    /// <summary>
    /// 入住日期
    /// </summary>
    public DateTime CheckInDate { get; set; }

    /// <summary>
    /// 离店日期
    /// </summary>
    public DateTime CheckOutDate { get; set; }

    /// <summary>
    /// 间数
    /// </summary>
    public int PriceStrategyRoomsCount { get; set; }

    /// <summary>
    /// 夜数
    /// </summary>
    public int PriceStrategyNightsCount { get; set; }

    /// <summary>
    /// 含早数
    /// </summary>
    public int PriceStrategyNumberOfBreakfast { get; set; }
    /// <summary>
    /// 酒店房间Id
    /// </summary>
    public long HotelRoomId { get; set; }
    /// <summary>
    /// 酒店房间名
    /// </summary>
    public string? HotelRoomName { get; set; }
    /// <summary>
    /// 酒店英文名
    /// </summary>
    public string? HotelRoomEnName { get; set; }
    /// <summary>
    /// 确认号
    /// </summary>
    public string? ConfirmCode { get; set; }

    /// <summary>
    /// 入住人信息
    /// </summary>
    public IEnumerable<HotelOrderGuestInfo> HotelOrderGuests { get; set; } = Enumerable.Empty<HotelOrderGuestInfo>();
}