using Contracts.Common.Order.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.GroupBookingAggregate;
public class MapToStatusDto
{
    public StatisticsFunnelStatus Status { get; set; }

    public IEnumerable<StatisticsFunnelStatusDto> MappedItems { get; set; }
}


public class StatisticsFunnelStatusDto
{
    public long ApplicationFormId { get; set; }
    public int? CountryCode { get; set; }

    public string? CountryName { get; set; }
    public int? ProvinceCode { get; set; }

    public string? ProvinceName { get; set; }

    public int? CityCode { get; set; }

    public string? CityName { get; set; }

    public long? AreaId { get; set; }

    public string? AreaName { get; set; }

    public StatisticsFunnelStatus Status { get; set; }
    public decimal Time { get; set; }
    public decimal CancelTime { get; set; }

    /// <summary>
    /// 工作时长
    /// </summary>
    public decimal WorkTime { get; set; }

    public GroupBookingApplicationFormStatus FormStatus { get; set; }

    /// <summary>
    /// 跟进运营id 商户员工id
    /// </summary>
    public long? OperatorUserId { get; set; }

    public bool IsWork { get; set; } = false;
}

public class BasicData
{
    /// <summary>
    /// 申请数量
    /// </summary>
    public int Count { get; set; }

    /// <summary>
    /// GMV
    /// </summary>
    public decimal GMV { get; set; }

    /// <summary>
    /// 首付单数
    /// </summary>
    public int DownPaymentCount { get; set; }

    /// <summary>
    /// 处理时长
    /// </summary>
    public double TotalPayTimes { get; set; }

    /// <summary>
    /// 询单客户
    /// </summary>
    public int AgencyCount { get; set; }
}

public class BasicDataItem
{

    public long AreaId { get; set; }

    /// <summary>
    /// 申请数量
    /// </summary>
    public int FormCount { get; set; }

    /// <summary>
    /// GMV
    /// </summary>
    public decimal GMV { get; set; }

    /// <summary>
    /// 首付单数
    /// </summary>
    public int DownPaymentCount { get; set; }

    /// <summary>
    /// 处理时长
    /// </summary>
    public double TotalTimes { get; set; }

    /// <summary>
    /// 询单客户
    /// </summary>
    public List<long?> AgencyIds { get; set; }
}

public class GMVDataItem
{
    public DateTime Date { get; set; }
    /// <summary>
    /// 申请单id
    /// </summary>
    public long ApplicationFormId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public long BaseOrderId { get; set; }

    public int? CountryCode { get; set; }

    public int? ProvinceCode { get; set; }

    public decimal GMV { get; set; }

    public GroupBookingSourceType SourceType { get; set; }
}

public class ChatDataGroup
{
    public int? CountryCode { get; set; }
    public int? ProvinceCode { get; set; }
    public DateTime Date { get; set; }
    /// <summary>
    /// 申请单id
    /// </summary>
    public long ApplicationFormId { get; set; }

    public GroupBookingSourceType? SourceType { get; set; }
}

public class AreaDto
{
    public long Id { get; set; }
    public string Name { get; set; }
    public GroupBookingAreaSettingType AreaSettingType { get; set; }

    public int? CountryCode { get; set; }
    public int? ProvinceCode { get; set; }
}

public class GetAreaDataDto
{
    public List<int> CountryCodes { get; set; } = new();
    public List<int> ProvinceCodes { get; set; } = new();
    public List<AreaDto> AreaDatas { get; set; } = new();

    public bool NoOther { get; set; } = true;

    /// <summary>
    /// 是否全部区域或者国家/地区
    /// </summary>
    public bool IsAllArea { get; set; } = false;

    public int ChinaCountryCode { get; set; } = 10;

    public StatisticalDimensionType DimensionType { get; set; }

    public List<MixedRegionCountryDto> MixedCountrys { get; set; }
}

public class MixedRegionCountryDto
{
    public int MixedRegionCountryCode { get; set; }

    public string MixedRegionCountryName { get; set; }

    public List<int> CountryCodes { get; set; } = new();
    public List<int> ProvinceCodes { get; set; } = new();
}