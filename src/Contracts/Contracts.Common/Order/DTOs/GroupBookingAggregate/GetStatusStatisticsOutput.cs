using Contracts.Common.Order.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.GroupBookingAggregate;
public class GetStatusStatisticsOutput
{
    public decimal? ConversionRate { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public StatisticsFunnelStatus Status { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int Count { get; set; }

    /// <summary>
    /// 平均处理时间（分钟）
    /// </summary>
    public decimal? AvgTime { get; set; }

    /// <summary>
    /// 平均取消时间（分钟）
    /// </summary>
    public decimal? AvgCancelTime { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int CancelCount { get; set; }

    public List<CancelDetailDto> CancelDetails { get; set; } = new();

    /// <summary>
    /// 待处理订单数
    /// </summary>
    public int WaitCount { get; set; }

    /// <summary>
    /// 平均工作处理时间（分钟）
    /// 只计算工作日
    /// </summary>
    public decimal? AvgWorkTime { get; set; }
}

public class CancelDetailDto
{
    /// <summary>
    /// 跟进运营id 商户员工id
    /// </summary>
    public long? OperatorUserId { get; set; }

    public string OperatorUserName { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 操作时间
    /// </summary>
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// GroupBookingOperationReasonType 中的赋值 
    /// [{一级},{二级}]
    /// </summary>
    public List<GroupBookingOperationReasonType> OperationReasonType { get; set; }

    public int? CountryCode { get; set; }

    public string? CountryName { get; set; }
    public int? ProvinceCode { get; set; }

    public string? ProvinceName { get; set; }

    public int? CityCode { get; set; }

    public string? CityName { get; set; }

    public long? AreaId { get; set; }

    public string? AreaName { get; set; }

    /// <summary>
    /// 申请单id
    /// </summary>
    public long ApplicationFormId { get; set; }
}