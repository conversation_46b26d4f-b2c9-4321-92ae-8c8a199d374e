using Contracts.Common.Order.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.GroupBookingAggregate;

public class ExportStatusStatisticsOutput
{
    public string? AreaName { get; set; }

    public long? AreaId { get; set; }
    /// <summary>
    /// 跟进运营id 商户员工id
    /// </summary>
    public long? OperatorUserId { get; set; }

    /// <summary>
    /// 跟进运营
    /// </summary>
    public string? OperatorUserName { get; set; }

    public decimal? ConversionRate { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public StatisticsFunnelStatus Status { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int Count { get; set; }

    /// <summary>
    /// 平均处理时间（h）
    /// </summary>
    public decimal? AvgTime { get; set; }

    /// <summary>
    /// 平均取消时间（h）
    /// </summary>
    public decimal? AvgCancelTime { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int CancelCount { get; set; }

    /// <summary>
    /// 平均工作处理时间（h）
    /// </summary>
    public decimal? AvgWorkTime { get; set; }
}

