using Contracts.Common.Order.Enums;

namespace Contracts.Common.Order.DTOs.OffsetOrder;

public class SearchOffsetOrderOutput
{
    /// <summary>
    /// 抵冲单id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 主订单id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 业务单号，酒店订单Id / 邮寄订单Id / 预约单Id /
    /// </summary>
    public long BusinessOrderId { get; set; }

    /// <summary>
    /// 分销商Id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 供应商Id
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 关联工单id
    /// </summary>
    public long? WorkOrderId { get; set; }

    /// <summary>
    /// HOP工单号
    /// </summary>
    public string? HopWorkOrderNumber { get; set; }

    /// <summary>
    /// 抵冲金额类型 1-加收 2-退款 3-赔付 类型为退款/赔付时 抵冲金额为负数
    /// </summary>
    public OffsetOrderMoneyType OffsetAmountType { get; set; }

    /// <summary>
    /// 抵冲金额(追加和退款 用正负区分)
    /// </summary>
    public decimal Amount { get; set; }
    
    /// <summary>
    /// 抵冲金额币种
    /// </summary>
    public string CurrencyCode { get; set; } 

    #region 产品信息

    /// <summary>
    /// 产品Id
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 产品SkuId
    /// </summary>
    public long ProductSkuId { get; set; }

    /// <summary>
    /// 产品Sku名称
    /// </summary>
    public string ProductSkuName { get; set; }

    #endregion

    /// <summary>
    /// 业务类型
    /// </summary>
    public OffsetOrderBusinessType BusinessType { get; set; }

    /// <summary>
    /// 抵冲类型
    /// </summary>
    public OffsetOrderType OffsetType { get; set; }
    
    /// <summary>
    /// 抵冲单状态
    /// </summary>
    public OffsetOrderStatus Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string Remark { get; set; }
    
    /// <summary>
    /// 创建人Id
    /// </summary>
    public long CreatorId { get; set; }
    
    /// <summary>
    /// 创建人
    /// </summary>
    public string? CreatorName { get; set; }

    /// <summary>
    /// 更新人id
    /// </summary>
    public long UpdaterId { get; set; }
    
    /// <summary>
    /// 更新人名称
    /// </summary>
    public string? UpdaterName { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    
    /// <summary>
    /// 抵冲单审批状态
    /// </summary>
    public OffsetOrderDingtalkApplyAuditStatus? AuditStatus { get; set; }

    /// <summary>
    /// 钉钉审批单号
    /// </summary>
    public string? DingtalkBusinessId { get; set; }

    /// <summary>
    /// 处理状态
    /// 处理中（审核中，审核通过-》触发相关事件；不需要审核时，直接触发相关事件）
    /// 成功（触发相关事件完成），结算时只查询处理成功的    
    /// 失败（审核拒绝，审核撤销，触发事件失败）
    /// </summary>
    public OffsetOrderProcessingStatus? ProcessStatus { get; set; }

    /// <summary>
    /// 处理失败原因
    /// </summary>
    public string? ProcessErrorMsg { get; set; }
}