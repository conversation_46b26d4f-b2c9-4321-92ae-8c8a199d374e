using Contracts.Common.Order.Enums;
using Contracts.Common.Resource.Enums;

namespace Contracts.Common.Order.DTOs.MessageNotify;

public class HotelCreateOrderNotifyDto
{
    /// <summary>
    /// 酒店名称
    /// </summary>
    public string HotelName { get; set; }

    /// <summary>
    /// 酒店房型
    /// </summary>
    public string HotelRoomName { get; set; }

    /// <summary>
    /// 间数
    /// </summary>
    public int RoomsCount { get; set; }

    /// <summary>
    /// 晚数
    /// </summary>
    public int NightsCount { get; set; }

    /// <summary>
    /// 入住日期
    /// </summary>
    public DateTime CheckInDate { get; set; }

    /// <summary>
    /// 离店日期
    /// </summary>
    public DateTime CheckOutDate { get; set; }

    /// <summary>
    /// 含早数
    /// </summary>
    public int PriceStrategyNumberOfBreakfast { get; set; }

    public HotelOrderStatus Status { get; set; }

    public string[] Guests { get; set; }

    public long PriceStrategySupplierId { get; set; }

    /// <summary>
    /// 餐食类型
    /// </summary>
    public BoardCodeType? BoardCodeType { get; set; }

    /// <summary>
    /// 餐食数量
    /// </summary>
    public int BoardCount { get; set; }
}

public class HotelRefundSucceededNotifyDto
{
    public string HotelName { get; set; }
    public string HotelRoomName { get; set; }
    public decimal RefundAmount { get; set; }
    public DateTime CheckInDate { get; set; }
    public DateTime CheckOutDate { get; set; }
    public int PriceStrategyRoomsCount { get; set; }
    public int PriceStrategyNumberOfBreakfast { get; set; }
    public long PriceStrategySupplierId { get; set; }
    public string[] Guests { get; set; }

    /// <summary>
    /// 餐食类型
    /// </summary>
    public BoardCodeType? BoardCodeType { get; set; }

    /// <summary>
    /// 餐食数量
    /// </summary>
    public int BoardCount { get; set; }
}

public class HotelOrderConfirmNotifyDto
{
    public string HotelName { get; set; }
    public string HotelRoomName { get; set; }
    public string SkuName { get; set; }
    public int NumberOfBreakfast { get; set; }
    public int RoomsCount { get; set; }
    public int NightsCount { get; set; }
    public DateTime CheckInDate { get; set; }
    public DateTime CheckOutDate { get; set; }
    public string[] Guests { get; set; }
    /// <summary>
    /// 是否团房订单
    /// </summary>
    public bool IsGroupBooking { get; set; }

    /// <summary>
    /// 确认号
    /// </summary>
    public string ConfirmCode { get; set; }

    /// <summary>
    /// 餐食类型
    /// </summary>
    public BoardCodeType? BoardCodeType { get; set; }

    /// <summary>
    /// 餐食数量
    /// </summary>
    public int BoardCount { get; set; }
}

public class HotelCheckInTimeReminderNotifyDto
{
    public string HotelName { get; set; }
    public string RoomName { get; set; }
    public string SkuName { get; set; }
    public DateTime CheckInDate { get; set; }
    public DateTime CheckOutDate { get; set; }
}