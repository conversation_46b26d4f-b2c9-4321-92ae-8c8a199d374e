using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.Enums;
using Contracts.Common.Tenant.Enums;

namespace Contracts.Common.Order.DTOs.ReceiptSettlementOrder;

#region 酒店应收订单

public class ReceivablesHotelOrderOutput : ReceivablesInfo
{
    /// <summary>
    /// 入住日期
    /// </summary>
    public DateTime CheckInDate { get; set; }

    /// <summary>
    /// 离店日期
    /// </summary>
    public DateTime CheckOutDate { get; set; }

    /// <summary>
    /// 酒店名称
    /// </summary>
    public string HotelName { get; set; }

    /// <summary>
    /// 确认号
    /// </summary>
    public string ConfirmCode { get; set; }

    /// <summary>
    /// 团号
    /// </summary>
    public string? GroupNo { get; set; }

    /// <summary>
    /// 晚数
    /// </summary>
    public int NightsCount { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public HotelOrderStatus Status { get; set; }

    /// <summary>
    /// 酒店入住人姓名,多个以逗号分隔
    /// </summary>
    public string HotelGuestNames { get; set; }

    /// <summary>
    /// 是否参与统计
    /// </summary>
    public bool IsStatistics { get; set; } = true;

    /// <summary>
    /// 订单类型（订单价格源）
    /// </summary>
    public HotelPriceSourceType? PriceSourceType { get; set; }
}

#endregion

#region 券类应收订单(无需预约)

public class ReceivablesTicketOrderOutput : ReceivablesInfo
{
    /// <summary>
    /// 券类产品二级分类
    /// </summary>
    public TicketBusinessType TicketBusinessType { get; set; }

    /// <summary>
    /// 主订单状态
    /// </summary>
    public BaseOrderStatus Status { get; set; }

    /// <summary>
    /// 完结日期
    /// </summary>
    public DateTime? FinishDate
        => Status is BaseOrderStatus.Finished or BaseOrderStatus.Closed ? UpdateTime : null;
}

#endregion

#region 券类应收款订单(需要预约)

public class ReceivablesReservationOrderOutput : ReceivablesInfo
{
    /// <summary>
    /// 券类产品二级分类
    /// </summary>
    public TicketBusinessType TicketBusinessType { get; set; }

    /// <summary>
    /// 主订单状态
    /// </summary>
    public ReservationStatus Status { get; set; }

    /// <summary>
    /// 确认号
    /// </summary>
    public string ConfirmCode { get; set; }

    /// <summary>
    /// 完结日期
    /// </summary>
    public DateTime? FinishDate
        => Status is ReservationStatus.Finished ? UpdateTime : null;
}

#endregion

#region 门票应收款订单

public class ReceivablesScenicOrderOutput : ReceivablesInfo
{
    /// <summary>
    /// 联系人
    /// </summary>
    public string ContactsName { get; set; }

    /// <summary>
    /// 资源名称
    /// </summary>
    public string ResourceName { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public BaseOrderStatus Status { get; set; }

    /// <summary>
    /// 完结日期
    /// </summary>
    public DateTime? FinishDate
        => Status is BaseOrderStatus.Finished or BaseOrderStatus.Closed ? UpdateTime : null;
}

#endregion


#region 线路应收款订单

public class ReceivablesLineOrderOutput : ReceivablesInfo
{
    /// <summary>
    /// 下单联系人
    /// </summary>
    public string ContactsName { get; set; }

    public BaseOrderStatus Status { get; set; }
    
    /// <summary>
    /// 预订日期
    /// </summary>
    public DateTime TravelBeginDate { get; set; }

    /// <summary>
    /// 预订结束日期
    /// </summary>
    public DateTime TravelEndDate { get; set; }


    /// <summary>
    /// 完结日期
    /// </summary>
    public DateTime? FinishDate
        => Status == BaseOrderStatus.Finished || Status == BaseOrderStatus.Closed ? UpdateTime : null;
}

#endregion

#region 用车应收款订单
public class ReceivablesCarProductOrderOutput : ReceivablesInfo
{
    /// <summary>
    /// 下单联系人
    /// </summary>
    public string ContactsName { get; set; }

    public BaseOrderStatus Status { get; set; }

    /// <summary>
    /// 出行时间
    /// </summary>
    public DateTime TravelDate { get; set; }

    /// <summary>
    /// 完结日期
    /// </summary>
    public DateTime? FinishDate
        => Status == BaseOrderStatus.Finished || Status == BaseOrderStatus.Closed ? UpdateTime : null;
}

#endregion

#region 退款应收订单

public class ReceivablesRefundOrderOutput : ReceivablesInfo
{
    /// <summary>
    /// 退款单关联的业务类型
    /// </summary>
    public RefundOrderType OrderType { get; set; }

    /// <summary>
    /// 完结日期
    /// </summary>
    public DateTime? FinishDate => UpdateTime;
}

#endregion

public class ReceivablesOffsetOrderOutput : ReceivablesInfo
{
    /// <summary>
    /// 业务类型
    /// </summary>
    public OffsetOrderBusinessType OffsetOrderBusinessType { get; set; }
        
    /// <summary>
    /// 创建人
    /// </summary>
    public string? CreatorName { get; set; }

    /// <summary>
    /// 更新人名称
    /// </summary>
    public string? UpdaterName { get; set; }
}

public class ReceivablesAgencyCreditRechargeOutput : ReceivablesInfo
{
    /// <summary>
    /// 充值状态
    /// </summary>
    public AgencyCreditChargeStatus ChargeStatus { get; set; }
    
    /// <summary>
    /// 支付方式
    /// </summary>
    public PayType PayType { get; set; }
    
    /// <summary>
    /// 支付渠道 1-微信 2-支付宝 5-企业网银
    /// </summary>
    public PayChannel? PayChannel { get; set; }
    
    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? FinishTime { get; set; }
}