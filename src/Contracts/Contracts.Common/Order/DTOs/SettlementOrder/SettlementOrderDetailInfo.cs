using Contracts.Common.Order.Enums;

namespace Contracts.Common.Order.DTOs.SettlementOrder;

public class SettlementOrderDetailInfo
{
    public long SettlementOrderId { get; set; }

    public long BaseOrderId { get; set; }

    /// <summary>
    /// 业务单号，HotelOrder.Id / MailOrder.Id / 预约单Id / 退款单Id
    /// <para>利用唯一索引控制并发写</para>
    /// </summary>
    public long BusinessOrderId { get; set; }

    /// <summary>
    /// 供应商Id
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 结算业务类型
    /// </summary>
    public SettlementBusinessType SettlementBusinessType { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 应付总额 / 采购价 * 数量 * （100 - 采购折扣）/ 100 或 采购价 * 数量 - baseOrder.costDiscountAmount
    /// </summary>
    public decimal TotalAmount { get; set; }
    
    /// <summary>
    /// 成本币种 =>供应商币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 业务订单结算日期类型对应日期时间
    /// </summary>
    public DateTime OrderSettlementDateTypeTime { get; set; }

    /// <summary>
    /// 采购单号
    /// </summary>
    public string? SupplierOrderId { get; set; }

    /// <summary>
    /// 订单类别
    /// </summary>
    public OrderType? OrderType { get; set; }

    /// <summary>
    /// 采购总价 / 采购价 * 数量
    /// </summary>
    public decimal? CostTotalAmount { get; set; }
}