
using Contracts.Common.Resource.Enums;
using Contracts.Common.Tenant.Enums;

namespace Contracts.Common.Order.DTOs.HotelOrder;
public class HotelAgencyChannelPriceInput
{
    /// <summary>
    /// 试单查价code
    /// </summary>
    public Hotel.Enums.CheckPriceStrategySaleCode Code { get; set; }
    /// <summary>
    /// 分销商id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 产品类型
    /// </summary>
    public Product.Enums.ChannelProductType ChannelProductType { get; set; }

    /// <summary>
    /// 产品id
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// 规格id
    /// </summary>
    public long SkuId { get; set; }

    /// <summary>
    /// 采购价币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 售价币种
    /// </summary>
    public string SaleCurrencyCode { get; set; }

    /// <summary>
    /// 支付币种
    /// </summary>
    public string PaymentCurrencyCode { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public IEnumerable<HotelDatePriceDto> DatePrices { get; set; }

    /// <summary>
    /// 试单查价价格 变价
    /// </summary>
    public IEnumerable<HotelDatePriceDto>? CheckDatePrices { get; set; }

    public SupplierApiType SupplierApiType { get; set; } = SupplierApiType.None;
    
    /// <summary>
    /// 0-非主推  1-主推
    /// </summary>
    public bool StaffTag { get; set; }

    /// <summary>
    /// 敏感度类型 
    /// </summary>
    public SellHotelTag? Tag { get; set; }

    /// <summary>
    /// 团房
    /// </summary>
    public bool IsReunionRoomTag { get; set; }
}

public class HotelDatePriceDto
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 售价
    /// </summary>
    public decimal SalePrice { get; set; }

    /// <summary>
    /// 采购价
    /// </summary>
    public decimal? CostPrice { get; set; }

    /// <summary>
    /// 库存
    /// </summary>
    public int Stock { get; set; }
}