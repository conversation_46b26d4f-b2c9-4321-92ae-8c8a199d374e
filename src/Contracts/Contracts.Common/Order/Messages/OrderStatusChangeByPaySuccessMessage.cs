using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;

namespace Contracts.Common.Order.Messages
{
    public class OrderStatusChangeByPaySuccessMessage
    {
        /// <summary>
        /// 支付单id
        /// </summary>
        public long OrderPaymentId { get; set; }

        /// <summary>
        /// 支付单类型 1-订单 2-预约单
        /// </summary>
        public OrderPaymentType OrderPaymentType { get; set; }

        /// <summary>
        /// 订单id
        /// </summary>
        public long OrderId { get; set; }

        /// <summary>
        /// 支付类型
        /// </summary>
        public PayType PaymentType { get; set; }

        /// <summary>
        /// 支付渠道
        /// </summary>
        public string PaymentChannel { get; set; }

        /// <summary>
        /// 支付方式  微信h5、微信小程序、微信扫码等
        /// </summary>
        public string PaymentMode { get; set; }

        /// <summary>
        /// 外部支付单号
        /// </summary>
        public string PaymentExternalNo { get; set; }

        /// <summary>
        /// 支付时间
        /// </summary>
        public string? PayTime { get; set; }
    }
}
