using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Tenant.DTOs.DingtalkApi;

public class DingtalkCallbackBody
{
    public string Encrypt { get; set; }

}

public class DingtalkCallbackInput
{
    public string ProcessInstanceId { get; set; }

    public string EventId { get; set; }

    public string CorpId { get; set; }

    public string Resource { get; set; }

    public string EventType { get; set; }

    public string BusinessId { get; set; }

    public string Title { get; set; }

    public string Type { get; set; }

    public string Url { get; set; }

    public string ProcessCode { get; set; }

    public string CreateTime { get; set; }

    public string BizCategoryId { get; set; }

    public string StaffId { get; set; }

    public List<string> UserId { get; set; }
}
