namespace Contracts.Common;

public static class CapTopics
{
    public static class User
    {
        /// <summary>
        /// 添加租户后台用户
        /// </summary>
        public const string AddTenantUser = "Cap.User.AddTenantUser";

        /// <summary>
        /// 添加客户Vip等级记录
        /// </summary>
        public const string AddCustomerVip = "Cap.User.AddCustomerVip";

        /// <summary>
        /// 奖金预发放
        /// </summary>
        public const string BonusPreRelease = "Cap.User.BonusPreRelease";

        /// <summary>
        /// 奖金发放
        /// </summary>
        public const string BonusRelease = "Cap.User.BonusRelease";

        /// <summary>
        /// 绑定下级
        /// </summary>
        public const string BindUnderling = "Cap.User.BindUnderling";

        /// <summary>
        /// 提现结果
        /// </summary>
        public const string WithdrawalApplyResult = "Cap.User.WithdrawalApplyResult";

        /// <summary>
        /// 移除产品已配置的佣金设置
        /// </summary>
        public const string RemoveProductCommission = "Cap.User.RemoveProductCommission";

        /// <summary>
        /// 添加分销商用户
        /// </summary>
        public const string AddAgencyUser = "Cap.User..AddAgencyUser";

        /// <summary>
        /// 同步冗余用户角色数据
        /// </summary>
        public const string SyncTenantUserRole = "Cap.User.SyncTenantUserRole";

        /// <summary>
        /// 添加日志
        /// </summary>
        public const string AddOperationLog = "Cap.User.AddOperationLog";

        /// <summary>
        /// 同步冗余用户角色数据
        /// </summary>
        public const string SyncManageUserRole = "Cap.User.SyncManageUserRole";

        /// <summary>
        /// 订阅-查询钉钉用户
        /// </summary>
        public const string SyncSearchDingtalkUser = "Cap.User.Dingtalk.SyncSearchDingtalkUser";

        /// <summary>
        /// 订阅-同步钉钉用户
        /// </summary>
        public const string SyncDingtalkUser = "Cap.User.Dingtalk.SyncDingtalkUser";

    }

    public static class Tenant
    {
        public const string SetProductSupportStaff = "Cap.Tenant.SetProductSupportStaff";

        public const string AgencyCreditOrderCancelRecord = "Cap.Tenant.AgencyCredit.OrderCancelRecord";

        /// <summary>
        /// 订阅 - 分销商延迟支付 订单取消/订单实付 额度记录恢复
        /// </summary>
        public const string AgencyDelayedCreditOrderCancelRecord = "Cap.Tenant.AgencyDelayedCredit.OrderCancelRecord";

        /// <summary>
        /// 订阅 - (入账)应收结算单结算付款 分销商额度流水记录结算
        /// </summary>
        public const string AgencyCreditReceiptSettlementOrderPay = "Cap.Tenant.AgencyCredit.ReceiptSettlementOrderPay";

        /// <summary>
        /// 订阅 - 收款结算单生成 对账中状态 分销商额度流水记录结算
        /// </summary>
        public const string AgencyCreditReceiptSettlementOrderCheckingRecord = "Cap.Tenant.AgencyCredit.AgencyCreditReceiptSettlementOrderCheckingRecord";

        /// <summary>
        /// 订阅 - 分销商额度充值单状态变更(支付成功)
        /// </summary>
        public const string AgencyChargeStatusChangeByPaySuccess = "Cap.Tenant.AgencyChargeStatusChangeByPaySuccess";

        /// <summary>
        /// 订阅 - 分销商额度充值单退款
        /// </summary>
        public const string AgencyChargeStatusChangeRefundResult = "Cap.Tenant.AgencyChargeStatusChangeRefundResult";

        /// <summary>
        /// 订阅 - 分销商重新核算等级
        /// </summary>
        public const string ReAgencyLevelCalculate = "Cap.Tenant.ReAgencyLevelCalculate";

        /// <summary>
        /// 订阅 - 周期分销商核算等级
        /// </summary>
        public const string CycleAgencyLevelCalculate = "Cap.Tenant.CycleAgencyLevelCalculate";

        /// <summary>
        /// 订阅 - 分销商等级开启初始化
        /// </summary>
        public const string InitAgencyLevel = "Cap.Tenant.InitAgencyLevel";

        /// <summary>
        /// 订阅 - 增加分销商会员积分值
        /// </summary>
        public const string IncreaseAgencyGrowUpValue = "Cap.Tenant.IncreaseAgencyGrowUpValue";

        /// <summary>
        /// 订阅 - 分销商订单转换成长值
        /// </summary>
        public const string SyncOrderChangeGrowUpValue = "Cap.Tenant.SyncOrderChangeGrowUpValue";

        /// <summary>
        /// 订阅 - 新增分销商初始化分销商等级明细
        /// </summary>
        public const string SyncAddAgencyInitAgencyLevelDetail = "Cap.Tenant.SyncAddAgencyInitAgencyLevelDetail";

        /// <summary>
        /// 订阅 - 认证分销商成功改变分销商等级明细
        /// </summary>
        public const string SyncAgencyLevelDetailByCertified = "Cap.Tenant.SyncAgencyLevelDetailByCertified";

        /// <summary>
        /// 订阅-供应商充值单状态变更
        /// </summary>
        public const string SupplierCreditOrderCharge = "Cap.Tenant.SupplierCreditOrderCharge";

        /// <summary>
        /// 订阅 - 分销商等级价格分组变化
        /// </summary>
        public const string AgencyLevelPriceGroupIdChange = "Cap.Tenant.AgencyLevelPriceGroupIdChange";

        /// <summary>
        /// 订阅 - Cit Open 租户账号开通
        /// </summary>
        public const string CitOpenTenantAppAccountCreate = "Cap.Tenant.CitOpenTenantAppAccountCreate";

        /// <summary>
        /// 订阅 - 创建企业（分销商）
        /// </summary>
        public const string WeilingCreateCompany = "Cap.Tenant.WeilingCreateCompany";

        /// <summary>
        /// 订阅 - 更新企业（分销商）
        /// </summary>
        public const string WeilingUpdateCompany = "Cap.Tenant.WeilingUpdateCompany";

        /// <summary>
        /// 停用员工，删除员工的通知
        /// </summary>
        public const string DisableTenantUser = "Cap.Tenant.DisableTenantUser";

        /// <summary>
        /// 启用分销商额度
        /// </summary>
        public const string EnableAgencyCredit = "Cap.Tenant.EnableAgencyCredit";

        /// <summary>
        /// Focussend创建更新联系人(分销商)
        /// </summary>
        public const string FocussendCreateUpdateContactByAgency = "Cap.Tenant.FocussendCreateUpdateContactByAgency";

        /// <summary>
        /// Focussend创建更新联系人(汇智酒店)
        /// </summary>
        public const string FocussendCreateUpdateContactByApiHotel = "Cap.Tenant.FocussendCreateUpdateContactByApiHotel";
        /// <summary>
        /// 同步B2B酒店数据
        /// </summary>
        public const string B2BSyncHotel = "Cap.Tenant.B2BSyncHotel";

        /// <summary>
        /// 订阅 - 钉钉审批申请提交
        /// </summary>
        public const string DingtalkApplySubmit = "Cap.Tenant.Dingtalk.DingtalkApplySubmit";

        /// <summary>
        /// 同步钉钉部门
        /// </summary>
        public const string SyncDingtalkDepartment = "Cap.Tenant.Dingtalk.SyncDingtalkDepartment";

        /// <summary>
        /// 订阅 - 钉钉审批申请回调通知查询详情
        /// </summary>
        public const string SyncDingtalkApplyCallback = "Cap.Tenant.Dingtalk.SyncDingtalkApplyCallback";

        /// <summary>
        /// 订阅 - 钉钉用户查询
        /// </summary>
        public const string SyncDingtalkSearchUserInfo = "Cap.Tenant.Dingtalk.DingtalkSearchUserInfo";

        /// <summary>
        /// 订阅 - 预收款充值 记录流水id信息
        /// </summary>
        public const string ReceiptPrepaymentRechargeFlow = "Cap.Tenant.ReceiptPrepaymentRechargeFlow";
    }

    public static class Notify
    {
        public const string SmsHeaderSign = "Cap.Notify.SmsHeaderSign";

        public const string SendSMS = "Cap.Notify.SendSMS";

        /// <summary>
        /// 邮件发送
        /// </summary>
        public const string SendEmail = "Cap.Notify.SendEmail";

        /// <summary>
        /// 通知消息处理
        /// </summary>

        public const string MessageProcess = "Cap.Notify.MessageProcess";

        /// <summary>
        /// 停用员工，删除员工的通知
        /// </summary>
        public const string DisableTenantUser = "Cap.Notify.DisableTenantUser";
    }

    public static class Hotel
    {
        public const string SetTenantOtaSetting = "Cap.Hotel.SetTenantOtaSetting";

        public const string SetPriceStrategyDisabled = "Cap.Hotel.SetPriceStrategyDisabled";

        /// <summary>
        /// 设置酒店达人佣金设置
        /// </summary>
        public const string SetHotelHasCommission = "Cap.Hotel.SetHotelHasCommission";

        public const string SetHotelMappingInfo = "Cap.Hotel.SetHotelMappingInfo";

        public const string HotelMappingPush = "Cap.Hotel.HotelMappingPush";
        public const string HotelRoomMappingPush = "Cap.Hotel.HotelRoomMappingPush";
        public const string HotelPriceStrategyMappingPush = "Cap.Hotel.HotelPriceStrategyMappingPush";

        public const string PriceStrategyCalendarPricePush = "Cap.Hotel.PriceStrategyCalendarPricePush";
        public const string PriceStrategyCalendarInventoryPush = "Cap.Hotel.PriceStrategyCalendarInventoryPush";

        /// <summary>
        /// 初始化加载HOP酒店报价每日价格
        /// </summary>
        public const string InitHopHotelNightlyPrices = "Cap.Hotel.InitHopHotelNightlyPrices";
        public const string ClearUnrecitedHopHotelNightlyPrices = "Cap.Hotel.ClearUnrecitedHopHotelNightlyPrices";
    }

    public static class Product
    {
        public const string SetProductDisabled = "Cap.Product.SetProductDisabled";

        /// <summary>
        /// 设置产品达人佣金设置
        /// </summary>
        public const string SetProductHasCommission = "Cap.Product.SetProductHasCommission";

        public const string SetProductRedundantSubscribe = "Cap.Product.SetProductRedundantSubscribe";

        public const string ShoppingCartProductRemove = "Cap.Product.ShoppingCart.RemoveProductSubscribe";

        public const string UpdatePriceChannelSettingMinPrice = "Cap.Product.UpdatePriceChannelSettingMinPrice";

        /// <summary>
        /// 更新无效产品的价格配置
        /// </summary>
        public const string UpdateInvalidProductPriceSettings = "Cap.Product.UpdateInvalidProductPriceSettings";

        public const string BytePlusItemAttributesUpdate = "Cap.Product.BytePlusItemAttributesUpdate";

        public const string LineOrderTriggerScheduleChannelSync = "Cap.Product.LineOrderTriggerScheduleChannelSync";
    }

    public static class Permission
    {
        public const string SetTenantPackage = "Cap.Permission.SetTenantPackage";

    }

    public static class Scenic
    {
        /// <summary>
        /// 设置供应商不可用，下架关联景区门票产品
        /// </summary>
        public const string SetScenicTicketDisabled = "Cap.Hotel.SetScenicTicketDisabled";

        /// <summary>
        /// 设置景区门票佣金设置
        /// </summary>
        public const string SetScenicSpotHasCommission = "Cap.Scenic.SetScenicSpotHasCommission";

        /// <summary>
        /// 设置景区预订门票最低价冗余
        /// </summary>
        public const string SetScenicTicketMinPrice = "Cap.Scenic.SetScenicTicketMinPrice";

        /// <summary>
        /// 同步渠道库存价格
        /// </summary>
        public const string SyncChannelPriceStock = "Cap.Scenic.SyncChannelPriceStock";

        /// <summary>
        /// 
        /// </summary>
        public const string SyncByCalendarPriceData = "Cap.Scenic.SyncByCalendarPriceData";

        /// <summary>
        /// 下单后-同步OTA分销端
        /// </summary>
        public const string TriggerOpenChannelSync = "Cap.Scenic.TriggerOpenChannelSync";
    }

    public static class Payment
    {
        /// <summary>
        /// 达人提现申请
        /// </summary>
        public const string DarenWithdrawalApply = "Cap.Payment.DarenWithdrawalApply";

        /// <summary>
        /// 订单退款
        /// </summary>
        public const string OrderRefund = "Cap.Payment.OrderRefund.Refund";

        /// <summary>
        /// 订单关闭/重复支付 发起退款处理
        /// </summary>
        public const string OrderPaymentUselessRefund = "Cap.Payment.OrderPaymentUseless.Refund";

        /// <summary>
        /// 供应商结算单付款下单
        /// </summary>
        public const string SettlementOrderPayOrder = "Cap.Payment.SettlementOrder.PayOrder";

        /// <summary>
        /// 【线下收款单】支付成功
        /// </summary>
        public const string OfflineReceiptOrderPaySuccess = "Cap.Payment.OfflineReceiptOrderPaySuccess";

        /// <summary>
        /// 【线下收款单】退款结果
        /// </summary>
        public const string OfflineReceiptOrderRefundResult = "Cap.Payment.OfflineReceiptOrderRefundResult";

        /// <summary>
        /// 订阅 - 易宝微信配置
        /// </summary>
        public const string YeeWechatConfig = "Cap.Payment.Yee.Wechat.Config";

        /// <summary>
        /// 订阅 - 易宝实名认证状态查询
        /// </summary>
        public const string YeeWechatAuthQuery = "Cap.Payment.YeeWechat.WechatAuthQuery";

        /// <summary>
        /// 订阅 - 申请分账
        /// </summary>
        public const string ProfitDivideApply = "Cap.Payment.ProfitDivide.Apply";

        /// <summary>
        /// 订阅 - 分账查询
        /// </summary>
        public const string ProfitDivideQuery = "Cap.Payment.ProfitDivide.Query";

        /// <summary>
        /// 订阅 - 完结分账
        /// </summary>
        public const string ProfitDivideComplete = "Cap.Payment.ProfitDivide.Complete";

        /// <summary>
        /// 订阅 - 分账回退
        /// </summary>
        public const string ProfitDivideBackApply = "Cap.Payment.ProfitDivideBack.Apply";

        /// <summary>
        /// 订阅 - 易宝分账回退
        /// </summary>
        public const string YeeDivideBackApply = "Cap.Payment.YeeDivideBack.Apply";

        /// <summary>
        /// 订阅 - 分账回退查询
        /// </summary>
        public const string ProfitDivideBackQuery = "Cap.Payment.ProfitDivideBack.Query";

        /// <summary>
        /// 订阅 - 付款下单
        /// </summary>
        public const string AccountPayOrder = "Cap.Payment.AccountPayOrder";

        /// <summary>
        /// 订阅 - 子商户转账平台商下单
        /// </summary>
        public const string AccountTransferOrder = "Cap.Payment.AccountTransferOrder";

        /// <summary>
        /// 订阅 - 平台转账子商户下单
        /// </summary>
        public const string AccountTransferOrderToMer = "Cap.Payment.AccountTransferOrderToMer";

        /// <summary>
        /// 订阅 - 达人提现付款结果处理
        /// </summary>
        public const string DarenWithdrawalResult = "Cap.Payment.DarenWithdrawalResult";

        /// <summary>
        /// 订阅 - 【线下收款】预付转账结果处理
        /// </summary>
        public const string OfflineReceiptTransferOrderResult = "Cap.Payment.OfflineReceipt.TransferOrderResult";

        /// <summary>
        /// 订阅 - 【线下收款】提现付款结果处理
        /// </summary>
        public const string OfflineReceiptOrderWithdrawalResult = "Cap.Payment.OfflineReceiptOrderWithdrawalResult";

        /// <summary>
        /// 订阅 - 【商户提现】预转账主商户结果处理
        /// </summary>
        public const string WithdrawOrderTransferOrderResult = "Cap.Payment.WithdrawOrder.TransferOrderResult";

        /// <summary>
        /// 订阅 - 【商户提现】商户提现付款结果处理
        /// </summary>
        public const string WithdrawOrderAccountPayOrderResult = "Cap.Payment.WithdrawOrder.AccountPayOrderResult";

        /// <summary>
        /// 订阅 - 【结算单付款】预转账主商户结果处理
        /// </summary>
        public const string SettlementOrderTransferOrderResult = "Cap.Payment.SettlementOrder.TransferOrderResult";

        /// <summary>
        /// 订阅 - 【结算单付款】付款结果处理
        /// </summary>
        public const string SettlementOrderAccountPayOrderResult = "Cap.Payment.SettlementOrder.AccountPayOrderResult";

        /// <summary>
        /// 订阅 - 【账单流水】记录转账产生的账单流水
        /// </summary>
        public const string AccountTransferBillFlow = "Cap.Payment.AccountTransfer.BillFlow";

        /// <summary>
        /// 订阅 - 【账单流水】记录付款产生的账单流水
        /// </summary>
        public const string AccountPayOrderBillFlow = "Cap.Payment.AccountPayOrder.BillFlow";

        /// <summary>
        /// 订阅 - 【账单流水】记录分账产生的账单流水
        /// </summary>
        public const string ProfitDivideBillFlow = "Cap.Payment.ProfitDivide.BillFlow";

        /// <summary>
        /// 订阅 - 【账单流水】记录分账回退产生的账单流水
        /// </summary>
        public const string ProfitDivideBackBillFlow = "Cap.Payment.ProfitDivideBack.BillFlow";

        /// <summary>
        /// 订阅 - 【分销额度】订单支付记录结果通知
        /// </summary>
        public const string AgencyCreditOrderPayRecordResult = "Cap.Payment.AgencyCredit.OrderPayRecord.Result";

        /// <summary>
        /// 订阅 - 【分销额度】订单编辑记录结果处理
        /// </summary>
        public const string AgencyCreditOrderEditPayRecordResult = "Cap.Payment.AgencyCredit.OrderEditPayRecord.Result";

        /// <summary>
        /// 订阅 - 账户交易明细记录
        /// </summary>
        public const string AccountInfoDetailRecord = "Cap.Payment.AccountInfoDetailRecord";

        /// <summary>
        /// 订阅 -【预收款】退款结果
        /// </summary>
        public const string ReceiptPrepaymentRefundResult = "Cap.Payment.ReceiptPrepaymentRefundResult";

        /// <summary>
        /// 订阅 - 预收款充值
        /// </summary>
        public const string ReceiptPrepaymentRecharge = "Cap.Payment.ReceiptPrepaymentRecharge";

        /// <summary>
        /// 订阅-预付款充值
        /// </summary>
        public const string SupplierPrepaymentCharge = "Cap.Payment.SupplierPrepaymentCharge";

        /// <summary>
        /// 订阅-预付款支付
        /// </summary>
        public const string SupplierPrepaymentPay = "Cap.Payment.SupplierPrepaymentPay";

        /// <summary>
        /// 订阅 - 商户收款流水记录
        /// </summary>
        public const string TenantReceiptFlow = "Cap.Payment.TenantReceiptFlow";

        /// <summary>
        /// 订阅-更新虚拟卡采购状态
        /// </summary>
        public const string UpdateVccPurchaseStatus = "Cap.Payment.UpdateVccPurchaseStatus";
    }

    public static class Resource
    {
        public const string ResourceHotelPhotosUpdate = "Cap.Resource.ResourceHotelPhotosUpdate";

        public const string ReadPdfFirstPage = "Cap.Resource.ReadPdfFirstPage";

        /// <summary>
        /// 订阅-pdf转单张图片
        /// </summary>
        public const string ConvertPdfToOneImage = "Cap.Resource.ConvertPdfToOneImage";

        /// <summary>
        /// 订阅-单张图片转pdf
        /// </summary>
        public const string CreatePdfFromSingleImage = "Cap.Resource.CreatePdfFromSingleImage";
    }

    public static class Marketing
    {
        public const string CouponActivityByRegistration = "Cap.Marketing.CouponActivityByRegistration";

        /// <summary>
        /// 设置电子卡达人佣金设置
        /// </summary>
        public const string SetCardHasCommission = "Cap.Marketing.SetCardHasCommission";

        /// <summary>
        /// 优惠券发放
        /// </summary>
        public const string GiveUserCoupon = "Cap.Marketing.GiveUserCoupon";

        /// <summary>
        /// 批量优惠券发放
        /// </summary>
        public const string GiveUserCoupons = "Cap.Marketing.GiveUserCoupons";

        /// <summary>
        /// 消费赠送优惠券活动领取
        /// </summary>
        public const string CouponActivityByConsumption = "Cap.Marketing.CouponActivityByConsumption";

        /// <summary>
        /// 指定用户优惠券活动领取
        /// </summary>
        public const string CouponActivityBySpecifiedUser = "Cap.Marketing.CouponActivityBySpecifiedUser";

        /// <summary>
        /// 用户优惠券使用
        /// </summary>
        public const string UserCouponUsed = "Cap.Marketing.UserCouponUsed";

        /// <summary>
        /// 用户优惠券返还
        /// </summary>
        public const string UserCouponReturn = "Cap.Marketing.UserCouponReturn";

        /// <summary>
        /// 创建用户储值卡
        /// </summary>
        public const string CreateUserStoredValueCard = "Cap.Marketing.CreateUserStoredValueCard";

        /// <summary>
        /// 移除用户储值卡
        /// </summary>
        public const string RemoveUserStoredValueCard = "Cap.Marketing.RemoveUserStoredValueCard";

        /// <summary>
        /// 取消限时抢购顶大记录 恢复可用抢购库存
        /// </summary>
        public const string FlashSaleOrderCancel = "Cap.Marketing.FlashSale.OrderCancel";

        /// <summary>
        /// 优惠券发放 - 抽奖活动
        /// </summary>
        public const string GiveUserCouponByLottey = "Cap.Marketing.GiveUserCouponByLottey";

        /// <summary>
        /// 抽奖活动 - 优惠券发放结果
        /// </summary>
        public const string LotteyAwardResult = "Cap.Marketing.LotteyAwardResult";

        /// <summary>
        /// 跟踪日志 - 推送
        /// </summary>
        public const string PushPromotionTraceRecord = "Cap.Marketing.PushPromotionTraceRecord";

        /// <summary>
        /// 订阅-分销商等级权益优惠券发放
        /// </summary>
        public const string GiveAgencyLevelEquityCoupon = "Cap.Marketing.GiveAgencyLevelEquityCoupon";
    }

    public static class Inventory
    {
        /// <summary>
        /// 扣除日历库存【无需支付】
        /// </summary>
        public const string DeductCalendarInventory = "Cap.Inventory.Deduct.CalendarInventory";

        /// <summary>
        /// 扣减库存
        /// </summary>
        public const string DeductInventory = "Cap.Inventory.Deduct.Inventory";

        /// <summary>
        /// 恢复库存
        /// </summary>
        public const string RenewInventory = "Cap.Inventory.Renew.Inventory";

        /// <summary>
        /// 订阅 - 冻结日历库存【下单】
        /// </summary>
        public const string FrozenCalendarInventory = "Cap.Inventory.Frozen.CalendarInventory";

        /// <summary>
        /// 订阅 - 冻结总库存【下单】
        /// </summary>
        public const string FrozenGeneralInventory = "Cap.Inventory.Frozen.GeneralInventory";

        /// <summary>
        /// 订阅 - 冻结时段日历库存【下单】
        /// </summary>
        public const string FrozenTimeSlotInventory = "Cap.Inventory.Frozen.TimeSlotInventory";

        /// <summary>
        /// 订阅 - 解冻库存【未支付取消】
        /// </summary>
        public const string UnfrozenInventory = "Cap.Inventory.Unfrozen.Inventory";

        /// <summary>
        /// 订阅 - 更新产品日历库存
        /// </summary>
        public const string UpdateCalendarInventory = "Cap.Inventory.UpdateCalendarInventory";

        /// <summary>
        /// 订阅 - 批量更新产品日历库存
        /// </summary>
        public const string BatchUpdateCalendarInventory = "Cap.Inventory.BatchUpdateCalendarInventory";

        /// <summary>
        /// 订阅 - 批量更新产品日历库存状态,可选callback:Cap_Inventory_BatchUpdateCalendarInventory(批量更新产品日历库存)
        /// </summary>
        public const string BatchUpdateCalendarInventoryStatus = "Cap.Inventory.BatchUpdateCalendarInventoryStatus";

        /// <summary>
        /// 订阅 - 更新产品可用库存
        /// </summary>
        public const string UpdateAvailableQuantity = "Cap.Inventory.UpdateAvailableQuantity";

        /// <summary>
        /// 订阅 - 新增产品库存配置
        /// </summary>
        public const string AddConfig = "Cap.Inventory.AddConfig";

        /// <summary>
        /// 订阅 - 更新产品库存配置
        /// </summary>
        public const string UpdateConfig = "Cap.Inventory.UpdateConfig";

        /// <summary>
        /// 订阅 - 更新房型库存 、库存房间数
        /// </summary>
        public const string ConfigTotalQuantity = "Cap.Inventory.ConfigTotalQuantity";

        /// <summary>
        /// 订阅-批量同步更新第三方时段日历价
        /// </summary>
        public const string BatchSyncThirdTimeSlotInventory = "Cap.Inventory.BatchSyncThirdTimeSlotInventory";

        /// <summary>
        /// 订阅-批量同步更新第三方日历价
        /// </summary>
        public const string BatchSyncThirdCalendarInventory = "Cap.Inventory.BatchSyncThirdCalendarInventory";

        /// <summary>
        /// 订阅-批量移除时段日历价
        /// </summary>
        public const string BatchDeleteTimeSlotInventory = "Cap.Inventory.BatchDeleteTimeSlotInventory";

        /// <summary>
        /// 订阅-批量移除日历价
        /// </summary>
        public const string BatchDeleteCalendarInventory = "Cap.Inventory.BatchDeleteCalendarInventory";
    }

    public static class Order
    {
        /// <summary>
        /// 订阅 - 订单状态变更(支付成功)
        /// </summary>
        public const string StatusChangeByPaySuccess = "Cap.Order.StatusChangeByPaySuccess";

        /// <summary>
        /// 订阅 - 预约单支付成功
        /// </summary>
        public const string ReservationOrderPaySuccess = "Cap.Order.ReservationOrderPaySuccess";

        /// <summary>
        /// 订阅 - 团房单支付成功
        /// </summary>
        public const string GroupBookingOrderStatusChangeByPaySuccess = "Cap.Order.GroupBookingOrderStatusChangeByPaySuccess";

        /// <summary>
        /// 订阅 - 订单退款结果
        /// </summary>
        public const string RefundResult = "Cap.Order.RefundResult";

        /// <summary>
        /// 订阅 - 抵冲单退款结果
        /// </summary>
        public const string OffsetOrderRefundResult = "Cap.Order.OffsetOrderRefundResult";

        /// <summary>
        /// 订阅 - 申请订单退款
        /// </summary>
        public const string OrderRefundApply = "Cap.Payment.OrderRefund.Apply";

        /// <summary>
        /// 订阅 - 结算单线上转账结果处理
        /// </summary>
        public const string SettlementOrderTransferResult = "Cap.Order.SettlementOrderTransferResult";

        /// <summary>
        /// 订阅 - 创建第三方平台酒店订单
        /// </summary>
        public const string CreateHotelApiOrder = "Cap.Order.CreateHotelApiOrder";

        /// <summary>
        /// 订阅 - 更新汇智酒店订单入住人信息
        /// </summary>
        public const string UpdateHotelApiOrderGuest = "Cap.Order.UpdateHotelApiOrderGuest";

        /// <summary>
        /// 订阅 - 调用航天开票接口
        /// </summary>
        public const string AisinogzInvEli = "Cap.Order.AisinogzInvEli";

        /// <summary>
        /// 订阅-客路订单发货
        /// </summary>
        public const string CreateKloorder = "Cap.Order.CreateKloorder";

        /// <summary>
        /// 订阅-OTA门票订单发货
        /// </summary>
        public const string ScenicOTAOrderDeliver = "Cap.Order.ScenicOTAOrderDeliver";

        /// <summary>
        /// 订阅 - 金蝶 应收单推送
        /// </summary>
        public const string KingdeeARReceivablePush = "Cap.Order.Kingdee.ARReceivablePush";

        /// <summary>
        /// 订阅 - 金蝶 应付单推送
        /// </summary>
        public const string KingdeeAPPayablePush = "Cap.Order.Kingdee.APPayablePush";

        /// <summary>
        /// 订阅 - 金蝶 供应商添加
        /// </summary>
        public const string KingdeeSupplierAdd = "Cap.Order.Kingdee.SupplierAdd";

        /// <summary>
        /// 订阅 - 金蝶 客户添加
        /// </summary>
        public const string KingdeeCustomerAdd = "Cap.Order.Kingdee.CustomerAdd";

        /// <summary>
        /// 订阅-开放平台供货方订单创建
        /// </summary>
        public const string ScenicTicketSupplierOrderCreate = "Cap.Order.OpenSupplier.Create";

        /// <summary>
        /// 订阅-开放平台组合产品供货方订单创建
        /// </summary>
        public const string ScenicTicketCombinationSupplierOrderCreate = "Cap.Order.OpenSupplier.TicketCombination.Create";

        /// <summary>
        /// 订阅-开放平台供货方订单信息更新
        /// </summary>
        public const string UpdateSupplierOrderInformation = "Cap.Order.OpenSupplier.UpdateInformation";

        /// <summary>
        /// 创建订单和保险产品关系
        /// </summary>
        public const string CreateInsureOrderRelation = "Cap.Order.Insure.InsureOrderRelation.Create";

        /// <summary>
        /// 订阅-开放平台分销端同步失败订单状态更新
        /// </summary>
        public const string OpenChannelSyncFailOrderStatusUpdate = "Cap.Order.OpenChannel.SyncFailOrderStatusUpdate";

        /// <summary>
        /// 订阅-更新门票采购单未使用库存
        /// </summary>
        public const string UpdateScenicPurchaseOrderUnUsedQuantity = "Cap.Order.ScenicPurchaseOrder.UpdateUnUsedQuantity";

        /// <summary>
        /// 订阅-更新订单的虚拟卡付款状态
        /// </summary>
        public const string UpdateVccPaymentStatus = "Cap.Order.UpdateVccPaymentStatus";

        /// <summary>
        /// 订阅-创建虚拟卡结算订单
        /// </summary>
        public const string CreateVccSettlementOrder = "Cap.Order.CreateVccSettlementOrder";

        /// <summary>
        /// 订阅-更新虚拟卡结算订单
        /// </summary>
        public const string UpdateVccSettlementOrder = "Cap.Order.UpdateVccSettlementOrder";

        /// <summary>
        /// 订阅-线路订单同步OTA确认
        /// </summary>
        public const string TravelLineOrderSyncOtaConfirm = "Cap.Order.TravelLineOrderSyncOtaConfirm";

        /// <summary>
        /// 订阅 - 开放平台-供应商用车询价
        /// </summary>
        public const string CarProductSupplierOrderInitiateQuote = "Cap.Order.OpenSupplier.CarInitiateQuote";

        /// <summary>
        /// 订阅 - 开放平台用车订单ota发货
        /// </summary>
        public const string CarProductOtaSyncDelivery = "Cap.Order.OpenChannel.CarDelievry";

        /// <summary>
        /// 订阅 - 开放平台-供应商用车订单取消
        /// </summary>
        public const string CarProductSupplierOrderCancel = "Cap.Order.OpenSupplier.CarCancel";

        /// <summary>
        /// 订阅 - 开放平台-供应商用车订单预订
        /// </summary>
        public const string CarSupplierOrderReservationMessage = "Cap.Order.OpenSupplier.InitiateReservation";

        /// <summary>
        /// 订阅 - 批量设置对象访问权限
        /// </summary>
        public const string BatchSetObjectAcl = "Cap.Order.BatchSetObjectAcl";

        /// <summary>
        /// 订阅-日游开放平台供货方订单创建
        /// </summary>
        public const string TravelLineSupplierOrderCreate = "Cap.Order.OpenSupplier.TravelLine.Create";

        /// <summary>
        /// 订阅 - 开放平台-GDS订单预订
        /// </summary>
        public const string HotelSupplierOrderCreate = "Cap.Order.OpenSupplier.GDS.Create";

        /// <summary>
        /// 订阅 - 酒店发单邮件通知消息处理结果回调
        /// </summary>
        public const string HotelInquiryMailMessageCallback = "Cap.Notify.HotelInquiryMailMessageCallback";

        /// <summary>
        /// 订阅 - 开放平台-渠道及时性订单同步
        /// </summary>
        public const string ChannelTimelinessVoucherSync = "Cap.Order.OpenChannel.TimelinessVocherSync";

        /// <summary>
        /// 订阅 - 开放平台-渠道订单标记修改
        /// </summary>
        public const string ChannelOrderFlagModify = "Cap.Order.OpenChannel.ChannelOrderFlagModify";

        /// <summary>
        /// 订阅 - 订阅钉钉审批单提交结果
        /// </summary>
        public const string SyncOffsetOrderDingtalk = "Cap.Order.Dingtalk.SyncOffsetOrderDingtalk";

        /// <summary>
        /// 订阅 - 收款结算单发送账单
        /// </summary>
        public const string SendEmailReceiptSettlementOrder = "Cap.Order.ReceiptSettlementOrder.SendEmail";

        /// <summary>
        /// 订阅 - 补差单退款
        /// </summary>
        public const string CompensationOrderRefund = "Cap.Order.CompensationOrderRefundMessage";

        /// <summary>
        /// 订阅  - 渠道退款申请审核处理
        /// </summary>
        public const string OpenChannelRefundApplyAudit = "Cap.Order.OpenChannelRefundApplyAudit";

        /// <summary>
        /// 订阅 - 补差单订单信息同步
        /// </summary>
        public const string CompensationOrderDataUpdate = "Cap.Order.CompensationOrderDataUpdate";

        /// <summary>
        /// 订阅 - 更新API门票订单价格
        /// </summary>
        public const string ScenicSupplierOrderPriceUpdate = "Cap.Order.ScenicSupplierOrderPriceUpdate";
    }

    public static class Wechat
    {
        /// <summary>
        /// 小程序发版前置申请
        /// </summary>
        public const string WechatAppletPreApply = "Cap.WechatApplet.PreApply";

        /// <summary>
        /// 小程序提交代码
        /// </summary>
        public const string WechatAppletCommitCode = "Cap.Wechat.WechatAppletCommitCode";

        /// <summary>
        /// 小程序审核版本
        /// </summary>
        public const string WechatAppletAuditVersion = "Cap.Wechat.WechatAppletAuditVersion";

        /// <summary>
        /// 小程序发布版本
        /// </summary>
        public const string WechatAppletReleaseVersion = "Cap.Wechat.WechatAppletReleaseVersion";

    }
}
