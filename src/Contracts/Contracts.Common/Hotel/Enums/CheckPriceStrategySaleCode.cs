using System.ComponentModel;

namespace Contracts.Common.Hotel.Enums
{
    public enum CheckPriceStrategySaleCode
    {
        [Description("成功")]
        Success = 0,

        //变价
        [Description("变价")]
        PriceChanged = 1,

        [Description("无效酒店")]
        HotelInvalid = 1001,

        [Description("无效房型")]
        RoomInvalid = 1002,

        [Description("无效价格策略")]
        PriceStrategyInvalid = 1003,

        [Description("渠道不可售")]
        ChannelNotSale = 1004,

        [Description("尾房不可售")]
        EndRoomNotBook = 1005,

        [Description("钟点房不可售")]
        HourRoomNotBook = 1006,

        [Description("连住房不可售")]
        StayLongDiscountNotBook = 1007,

        [Description("日历价格不足")]
        PricesNotEnough = 1008,

        [Description("库存不足")]
        InventoryNotEnough = 1009,

        [Description("日历价格不可售")]
        CalendarNotEnable = 1010,

        [Description("团房最低起订间数不足")]
        NumberOfRoomsNotEnough = 1011,

        [Description("提前预订时长不足")]
        AdvanceHoursNotEnough = 1012,

        /// <summary>
        /// 超过最大入住人数
        /// </summary>
        [Description("超过最大入住人数")]
        OverMaxOccupancy = 1013,

        /// <summary>
        /// 超过最大连住数量
        /// </summary>
        [Description("超过最大连住数量")]
        OverMaxNumberOfNights=1014,

        /// <summary>
        /// 不满足最小连住日期
        /// </summary>
        [Description("不满足最小连住日期")]
        LessMinNumberOfNights = 1014,
    }
}
