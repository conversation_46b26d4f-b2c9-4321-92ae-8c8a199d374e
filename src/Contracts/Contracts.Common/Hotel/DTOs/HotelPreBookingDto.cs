using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Hotel.DTOs;
public class HotelPreBookingDto
{
    /// <summary>
    /// 价格策略类型
    /// </summary>
    public Contracts.Common.Hotel.Enums.PriceStrategyType PriceStrategyType { get; set; }

    #region 连住策略单独部分

    /// <summary>
    /// 连住晚数及以上
    /// </summary>
    public int NumberOfNights { get; set; }

    /// <summary>
    /// 限制最大连住天数
    /// </summary>
    public int? LimitNumberOfNights { get; set; }

    #endregion

    #region 团房策略单独部分

    /// <summary>
    /// 最小预订间数
    /// </summary>
    public int NumberOfRooms { get; set; }

    #endregion

    /// <summary>
    /// 是否直采
    /// </summary>
    public bool IsDirect { get; set; }

    /// <summary>
    /// 敏感度类型 
    /// </summary>
    public Resource.Enums.SellHotelTag? Tag { get; set; }

    /// <summary>
    /// 预订礼遇
    /// </summary>
    public string? BookingBenefits { get; set; }

    /// <summary>
    /// 总价 本地酒店总售价/第三方酒店总采购价
    /// </summary>
    public decimal TotalPrice { get; set; }

    /// <summary>
    /// 每日价格
    /// </summary>
    public HotelPreBookingDatePriceDto[] DatePrices { get; set; }
}


public class HotelPreBookingDatePriceDto
{
    public DateTime Date { get; set; }
    public decimal Price { get; set; }
    public decimal Cost { get; set; }
}