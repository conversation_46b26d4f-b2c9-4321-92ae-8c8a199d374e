using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Hotel.DTOs.Hotel;
public class SyncHotelInfosInput
{
    public long Id { get; set; }

    /// Hop系统Id
    public int HopId { get; set; }

    public OriginInfo OriginInfo { get; set; }

    /// <summary>
    /// 中文名称
    /// </summary>
    public string ZHName { get; set; }

    /// <summary>
    /// 英文名称
    /// </summary>
    public string? ENName { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    public int CountryCode { get; set; }

    public string CountryName { get; set; }

    /// <summary>
    /// 省
    /// </summary>
    public int ProvinceCode { get; set; }

    public string ProvinceName { get; set; }

    /// <summary>
    /// 市
    /// </summary>
    public int CityCode { get; set; }

    public string CityName { get; set; }

    /// <summary>
    /// 详细地址
    /// </summary>
    public string Address { get; set; }
    
    /// <summary>
    /// 英文地址
    /// </summary>
    public string? ENAddress { get; set; }

    /// <summary>
    /// 经纬度坐标类型
    /// </summary>
    public Resource.Enums.CoordinateType CoordinateType { get; set; }

    public double Longitude { get; set; }

    public double Latitude { get; set; }

    /// <summary>
    /// 星级
    /// </summary>
    public decimal StarLevel { get; set; }

    /// <summary>
    /// 简介
    /// </summary>
    public string? Intro { get; set; }

    /// <summary>
    /// 周边设施
    /// </summary>
    public string? SurroundingFacilities { get; set; }

    /// <summary>
    /// 入住政策
    /// </summary>
    public string? CheckinPolicy { get; set; }

    /// <summary>
    /// 注意事项
    /// </summary>
    public string? ImportantNotices { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    public string? Telephone { get; set; }

    /// <summary>
    /// 联系传真
    /// </summary>
    public string? Telefax { get; set; }

    /// <summary>
    /// 发单邮箱
    /// </summary>
    public string? ReceiptEmail { get; set; }

    /// <summary>
    /// 0-非主推  1-主推
    /// </summary>
    public bool? StaffTag { get; set; }

    /// <summary>
    /// 团房数量
    /// </summary>
    public int? ReunionRoom { get; set; }
}

public record OriginInfo(string? Intro, string? SurroundingFacilities, string? CheckinPolicy, string? ImportantNotices);
