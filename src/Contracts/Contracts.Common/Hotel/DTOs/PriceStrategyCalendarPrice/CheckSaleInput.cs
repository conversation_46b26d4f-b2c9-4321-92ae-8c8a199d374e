using Contracts.Common.Order.Enums;
using Contracts.Common.Tenant.Enums;

namespace Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;

public class CheckSaleInput
{
    public long HotelId { get; set; }
    public long RoomId { get; set; }
    public string PriceStrategyId { get; set; }

    /// <summary>
    /// 预订编码 试单传参
    /// </summary>
    public string? PreBookingCode { get; set; }
    public DateTime BeginDate { get; set; }
    public DateTime EndDate { get; set; }
    public int Quantity { get; set; }
    /// <summary>
    /// 成人数
    /// </summary>
    public int AdultNum { get; set; } = 1;

    /// <summary>
    /// 儿童年龄集合
    /// </summary>
    public IEnumerable<int>? ChildrenAges { get; set; }
    public SupplierApiType SupplierApiType { get; set; }
    public SellingChannels? SalesChannel { get; set; }

    /// <summary>
    /// 是否是团房申请单下单
    /// </summary>
    public bool IsGroupBooking { get; set; }
}