using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Hotel.DTOs.PriceStrategy;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Tenant.Enums;
using Newtonsoft.Json;

namespace Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;

public class CheckSaleOutput
{
    public CheckPriceStrategySaleCode Code { get; set; }
    public string Message { get; set; }
    public CheckSaleData Data { get; set; }
}

public class CheckSaleData
{
    public long HotelId { get; set; }

    /// <summary>
    /// 资源酒店id
    /// </summary>
    public long ResourceHotelId { get; set; }

    public string HotelName { get; set; }

    public string? HotelEnName { get; set; }

    /// <summary>
    /// 0-非主推  1-主推
    /// </summary>
    public bool StaffTag { get; set; }

    /// <summary>
    /// 是否自动确认房态
    /// </summary>
    public bool IsAutoConfirmRoomStatus { get; set; }

    /// <summary>
    /// 运营模式
    /// </summary>
    public OperatingModel OperatingModel { get; set; }
    
    public CheckSale_Room Room { get; set; }

    public CheckSale_PriceStrategy PriceStrategy { get; set; }
}

public class CheckSale_Room
{
    public long Id { get; set; }

    public long ResourceRoomId { get; set; }

    /// <summary>
    /// 房型名称
    /// </summary>
    public string Name { get; set; }

    public string? EnName { get; set; }

    /// <summary>
    /// 最大入住人数
    /// </summary>
    public int MaximumOccupancy { get; set; }

    /// <summary>
    /// 床型
    /// </summary>
    public string BedType { get; set; }

    /// <summary>
    /// 床型
    /// </summary>
    public List<BedType> BedTypes { get; set; }

    /// <summary>
    /// 面积 - 最小
    /// </summary>
    public decimal AreaMin { get; set; }

    /// <summary>
    /// 面积 - 最大
    /// </summary>
    public decimal AreaMax { get; set; }

    /// <summary>
    /// 窗户
    /// </summary>
    public WindowType WindowType { get; set; }

    /// <summary>
    /// 房型图片
    /// </summary>
    public string HotelRoomImgPath { get; set; }

    /// <summary>
    /// 是否显示
    /// </summary>
    public bool Viewable { get; set; }
}

public class CheckSale_PriceStrategy
{
    /// <summary>
    /// 第三方平台
    /// </summary>
    public SupplierApiType SupplierApiType { get; set; }
    
    public string Id { get; set; }


    /// <summary>
    /// 第三方平台酒店id
    /// </summary>
    public string? HotelId { get; set; }

    /// <summary>
    /// 第三方平台酒店房型id
    /// </summary>
    public string? RoomId { get; set; }

    /// <summary>
    /// 策略名称
    /// </summary>
    public string Name { get; set; }

    public string? ENName { get; set; }

    /// <summary>
    /// 价格策略类型
    /// </summary>
    public PriceStrategyType PriceStrategyType { get; set; }

    /// <summary>
    /// 连住晚数及以上
    /// </summary>
    public int NumberOfNights { get; set; }

    /// <summary>
    /// 含早数
    /// </summary>
    public int NumberOfBreakfast { get; set; }

    /// <summary>
    /// 餐食类型
    /// </summary>
    public BoardCodeType? BoardCodeType { get; set; }

    /// <summary>
    /// 餐食数量
    /// </summary>
    public int BoardCount { get; set; }

    /// <summary>
    /// 是否自动确认
    /// </summary>
    public bool PriceStrategyIsAutoConfirm { get; set; }

    /// <summary>
    /// 是否可以超卖
    /// </summary>
    public bool OverSaleable { get; set; }

    /// <summary>
    /// 供应商Id
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 是否自动确认
    /// </summary>
    public bool IsAutoConfirm { get; set; }

    /// <summary>
    /// 非立即确认报价预计确认时长，单位：分钟
    /// </summary>
    public int? ConfirmByMins { get; set; }

    /// <summary>
    /// 每日价格
    /// </summary>
    public List<DatePrice> DatePrice { get; set; } = new();

    /// <summary>
    /// 试单请求价格
    /// </summary>
    public List<DatePrice> CheckDatePrice { get; set; } = new();

    public CancelRule CancelRule { get; set; }

    /// <summary>
    /// 最小预订间数
    /// </summary>
    public int NumberOfRooms { get; set; }

    /// <summary>
    /// 采购价币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 售价币种
    /// </summary>
    public string SaleCurrencyCode { get; set; }
    
    /// <summary>
    /// 价格策略最大可入住人数
    /// </summary>
    public int MaximumOccupancy  { get; set; }
    
    /// <summary>
    /// 是否直采
    /// </summary>
    public bool IsDirect { get; set; }

    /// <summary>
    /// 敏感度类型 
    /// </summary>
    public SellHotelTag? Tag { get; set; }

    /// <summary>
    /// 预订礼遇
    /// </summary>
    public string? BookingBenefits { get; set; }

    /// <summary>
    /// 税费提醒
    /// </summary>
    public string? TaxDescription { get; set; }

    /// <summary>
    /// 税费说明
    /// </summary>
    public Resource.DTOs.ThirdHotel.CheckAvailabilityArrivalTaxFeeOutput[]? ArrivalTaxFees { get; set; }
}