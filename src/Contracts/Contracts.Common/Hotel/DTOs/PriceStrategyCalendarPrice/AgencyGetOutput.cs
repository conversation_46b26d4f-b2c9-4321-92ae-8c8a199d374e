using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Hotel.DTOs.PriceStrategy;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Resource.Enums;

namespace Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;

public class AgencyGetOutput
{
    /// <summary>
    /// 房型id
    /// </summary>
    public long RoomId { get; set; }

    /// <summary>
    /// 中文名称
    /// </summary>
    public string RoomZHName { get; set; }

    /// <summary>
    /// 英文名称
    /// </summary>
    public string RoomENName { get; set; }

    /// <summary>
    /// 窗户
    /// </summary>
    public WindowType WindowType { get; set; }
    
    /// <summary>
    /// 房间数
    /// </summary>
    public int RoomQuantity { get; set; }

    /// <summary>
    /// 最大入住人数
    /// </summary>
    public int MaximumOccupancy { get; set; }

    /// <summary>
    /// 面积 - 最小
    /// </summary>
    public decimal AreaMin { get; set; }

    /// <summary>
    /// 面积 - 最大
    /// </summary>
    public decimal AreaMax { get; set; }
        
    /// <summary>
    /// 楼层 - 最低
    /// </summary>
    public int FloorMin { get; set; }

    /// <summary>
    /// 楼层 - 最高
    /// </summary>
    public int FloorMax { get; set; }

    /// <summary>
    /// 首图
    /// </summary>
    public string FirstPhoto { get; set; }

    /// <summary>
    /// 床型json
    /// </summary>
    public List<BedType> BedType { get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }
    
    /// <summary>
    /// 资源库房型Id
    /// </summary>
    public long ResourceRoomId { get; set; }

    /// <summary>
    /// 是否容许儿童入住
    /// </summary>
    public bool? IsHasChildren { get; set; }

    /// <summary>
    /// 加床费
    /// </summary>
    public decimal? ExtraBedFee { get; set; }

    /// <summary>
    /// 提供哪种加床
    /// </summary>
    public HotelExtraBedType? ExtraBedType { get; set; }

    /// <summary>
    /// 共用床
    /// </summary>
    public bool? IsHasChildrenExistingBed { get; set; }

    /// <summary>
    /// 加床费货币
    /// </summary>
    public string? ExtraBedFeeCurrency { get; set; }

    /// <summary>
    /// 房型价格策略
    /// </summary>
    public List<AgencyGetPriceStrategyItem> PriceStrategies { get; set; } = new();

    /// <summary>
    /// 房型说明
    /// </summary>
    public string? Description { get; set; }
}

public class AgencyGetPriceStrategyItem
{
    /// <summary>
    /// 价格策略id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 酒店Id
    /// </summary>
    public long HotelId { get; set; }

    /// <summary>
    /// 策略名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 价格策略类型
    /// </summary>
    public PriceStrategyType PriceStrategyType { get; set; }

    /// <summary>
    /// 含早数
    /// </summary>
    public int NumberOfBreakfast { get; set; }

    /// <summary>
    /// 取消策略类型
    /// </summary>
    public CancelRulesType CancelRulesType { get; set; }

    /// <summary>
    /// 是否可超售
    /// </summary>
    public bool OverSaleable { get; set; }

    /// <summary>
    /// 连住晚数及以上
    /// </summary>
    public int NumberOfNights { get; set; }

    /// <summary>
    /// 最小预订间数
    /// </summary>
    public int NumberOfRooms { get; set; }

    /// <summary>
    /// 供应商id
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 采购价币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 售价币种
    /// </summary>
    public string SaleCurrencyCode { get; set; }

    /// <summary>
    /// 提前几小时预订
    /// </summary>
    public int BookingHoursInAdvance { get; set; }

    /// <summary>
    /// 是否自动确认
    /// </summary>
    public bool IsAutoConfirm { get; set; }

    /// <summary>
    /// 策略日历价
    /// </summary>
    public List<AgencyGetPriceStrategyCalendarPriceItem> CalendarPrices { get; set; } = new();

    /// <summary>
    /// 取消政策
    /// </summary>
    public CancelRule CancelRule { get; set; }

    /// <summary>
    /// 限制国籍
    /// </summary>
    public IEnumerable<NationalityDto> Nationalities { get; set; } = Enumerable.Empty<NationalityDto>();

    /// <summary>
    /// 最大入住人数
    /// </summary>
    public int MaximumOccupancy { get; set; }

    /// <summary>
    /// 是否可售
    /// </summary>
    public bool IsSale { get; set; }

    /// <summary>
    /// 试单码
    /// </summary>
    public string PreBookingCode { get; set; }
}

public class AgencyGetPriceStrategyCalendarPriceItem
{
    public DateTime Date { get; set; }

    public bool Enabled { get; set; }

    /// <summary>
    /// 可用库存
    /// </summary>
    public int AvailableQuantity { get; set; }

    /// <summary>
    /// 总库存
    /// </summary>
    public int TotalQuantity { get; set; }

    /// <summary>
    /// 成本价
    /// </summary>
    public decimal? CostPrice { get; set; }

    /// <summary>
    /// 售价
    /// </summary>
    public decimal? SalePrice { get; set; }


}