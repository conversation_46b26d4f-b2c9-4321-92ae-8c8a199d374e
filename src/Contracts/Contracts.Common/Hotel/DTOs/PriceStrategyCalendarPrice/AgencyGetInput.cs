namespace Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;

public class AgencyGetInput
{
    /// <summary>
    /// 酒店id
    /// </summary>
    public long HotelId { get; set; }

    /// <summary>
    /// 价格分组配置规格id
    /// </summary>
    public IEnumerable<long> PriceStrategyIds { get; set; } = Enumerable.Empty<long>();

    /// <summary>
    /// 入住日期
    /// </summary>
    public DateTime LiveDate { get; set; }

    /// <summary>
    /// 离店日期
    /// </summary>
    public DateTime LeaveDate { get; set; }

    /// <summary>
    /// 房间数(默认1)
    /// </summary>
    public int RoomNum { get; set; } = 1;

    /// <summary>
    /// 校验价格策略是否可售
    /// </summary>
    public bool VerifySale { get; set; }
}