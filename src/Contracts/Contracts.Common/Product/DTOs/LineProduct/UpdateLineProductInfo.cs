using Contracts.Common.Product.Enums;
using Contracts.Common.Resource.Enums;

namespace Contracts.Common.Product.DTOs.LineProduct;

public class UpdateLineProductInfo
{
    #region 基础信息

    /// <summary>
    /// 采购来源类型
    /// </summary>
    public LineProductPurchaseSourceType PurchaseSourceType { get; set; }
    
    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// 英文标题
    /// </summary>
    public string EnTitle { get; set; }

    /// <summary>
    /// 卖点描述
    /// </summary>
    public string SellPointDescribe { get; set; }

    /// <summary>
    /// 出发国家
    /// </summary>
    public int DepartureCountryId { get; set; }

    public string DepartureCountryName { get; set; } = string.Empty;

    /// <summary>
    /// 出发地城市Id
    /// </summary>
    public int DepartureCityId { get; set; }

    /// <summary>
    /// 出发地城市名称
    /// </summary>
    public string DepartureCityName { get; set; } = string.Empty;

    /// <summary>
    /// 目的地国家
    /// </summary>
    public int DestinationCountryId { get; set; }
    public string DestinationCountryName { get; set; } = string.Empty;

    /// <summary>
    /// 目的地城市Id
    /// </summary>
    public int DestinationCityId { get; set; }

    /// <summary>
    /// 目的地城市名称
    /// </summary>
    public string DestinationCityName { get; set; } = string.Empty;
    
    /// <summary>
    /// 目的地城市经纬度坐标类型
    /// </summary>
    public CoordinateType? DestinationCoordinateType { get; set; }

    /// <summary>
    /// 目的地城市经度
    /// </summary>
    public double? DestinationLongitude { get; set; }

    /// <summary>
    /// 目的地城市纬度
    /// </summary>
    public double? DestinationLatitude { get; set; }

    /// <summary>
    /// 目的地城市Google位置Id
    /// </summary>
    public string? DestinationGooglePalceId { get; set; }
    
    /// <summary>
    /// 图片
    /// </summary>
    public List<string> ProductPictures { get; set; }

    /// <summary>
    /// 视频
    /// </summary>
    public List<string> ProductVideos { get; set; } = new();
    
    /// <summary>
    /// 上车点
    /// </summary>
    public List<RallyPointItem> RallyPoints { get; set; } = new List<RallyPointItem>();
    
    /// <summary>
    /// 产品分组
    /// </summary>
    public List<long> GroupIds { get; set; }
    
    /// <summary>
    /// 成人标准说明
    /// </summary>
    public string AdultsStandard { get; set; }

    /// <summary>
    /// 儿童标准说明
    /// </summary>
    public string ChildrenStandard { get; set; }
    
    /// <summary>
    /// 婴儿标准说明
    /// </summary>
    public string BabyStandard { get; set; }
    
    /// <summary>
    /// 长者标准说明
    /// </summary>
    public string ElderlyStandard { get; set; }

    #endregion

    #region 购买须知

    /// <summary>
    /// 费用包含
    /// </summary>
    public string? FeeNote { get; set; }

    /// <summary>
    /// 费用不含
    /// </summary>
    public string? FeeNotNote { get; set; }

    /// <summary>
    /// 预订须知
    /// </summary>
    public string? OtherNote { get; set; }
    
    #region 预订规则

    /// <summary>
    /// 提前几天预订
    /// </summary>
    public int ReservationDaysInAdvance { get; set; }

    /// <summary>
    /// 提前预订时间点
    /// </summary>
    public TimeSpan ReservationTimeInAdvance { get; set; }
    
    /// <summary>
    /// 是否系统自动确认  true 是系统自动确认
    /// </summary>
    public bool AutoConfirm { get; set; }

    #endregion

    #region 退款规则

    /// <summary>
    /// 是否支持退款
    /// </summary>
    public bool IsSupportRefund { get; set; }

    /// <summary>
    /// 退款比例
    /// </summary>
    public decimal? RefundRate { get; set; }

    /// <summary>
    /// 退款-预订日期截止前多少天
    /// </summary>
    public int? RefundBeforeTravelDateDay { get; set; }

    /// <summary>
    /// 退款-预订当日 - 时间 如14:00
    /// </summary>
    public TimeSpan? RefundTravelDateTime { get; set; }

    #endregion

    #endregion

    #region 产品详情内容

    /// <summary>
    /// 产品详情内容
    /// </summary>
    public string Content { get; set; }

    #endregion
}