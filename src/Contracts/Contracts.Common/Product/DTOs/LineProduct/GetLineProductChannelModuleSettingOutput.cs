namespace Contracts.Common.Product.DTOs.LineProduct;

public class GetLineProductChannelModuleSettingOutput
{
    /// <summary>
    /// 产品id
    /// </summary>
    public long LineProductId { get; set; }

    #region 售卖设置

    /// <summary>
    /// 售卖日期 - 起  Null表示不限
    /// </summary>
    public DateTime? SellingDateBegin { get; set; }

    /// <summary>
    /// 售卖日期 - 止  Null表示不限
    /// </summary>
    public DateTime? SellingDateEnd { get; set; }

    /// <summary>
    /// 上架/下架
    /// </summary>
    public bool Enabled { get; set; }

    #endregion
}