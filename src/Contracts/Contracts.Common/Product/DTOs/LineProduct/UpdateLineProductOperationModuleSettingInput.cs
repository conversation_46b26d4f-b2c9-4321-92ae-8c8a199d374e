using Contracts.Common.Product.DTOs.ProductOperatorUser;

namespace Contracts.Common.Product.DTOs.LineProduct;

public class UpdateLineProductOperationModuleSettingInput
{
    /// <summary>
    /// 线路产品id
    /// </summary>
    public long LineProductId { get; set; }
    
    /// <summary>
    /// 产品人id
    /// </summary>
    public long? DevelopUserId { get; set; }

    /// <summary>
    /// 平台运营人
    /// </summary>
    public List<ProductOperatorUserDto> ProductOperatorUser { get; set; } = new();

    /// <summary>
    /// 海报
    /// </summary>
    public List<string> ProductPosters { get; set; } = new();

    /// <summary>
    /// 售前客服
    /// </summary>
    public long PreSaleStaff { get; set; }

    /// <summary>
    /// 售后客服
    /// </summary>
    public long AfterSaleStaff { get; set; }
}