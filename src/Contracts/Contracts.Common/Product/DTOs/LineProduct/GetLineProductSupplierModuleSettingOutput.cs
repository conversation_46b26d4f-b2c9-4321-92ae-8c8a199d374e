using Contracts.Common.Order.DTOs.Insure;
using Contracts.Common.Product.DTOs.LineProductOpenSupplierSetting;
using Contracts.Common.Product.Enums;

namespace Contracts.Common.Product.DTOs.LineProduct;

/// <summary>
/// 获取线路产品-供应商模块设置输出
/// </summary>
public class GetLineProductSupplierModuleSettingOutput
{
    /// <summary>
    /// 产品id
    /// </summary>
    public long LineProductId { get; set; }
    
    /// <summary>
    /// 供应商
    /// </summary>
    public long SupplierId { get; set; }
    
    /// <summary>
    /// 活动id
    /// <remarks>供应端 - productid</remarks>
    /// </summary>
    public string? ActivityId { get; set; }
    
    /// <summary>
    /// 供应商-服务信息Id
    /// </summary>
    public long? SupplierServiceInfoId { get; set; }
    
    /// <summary>
    /// 供应商-服务信息名称
    /// </summary>
    public string? SupplierServiceInfoName { get; set; }
    
    /// <summary>
    /// 采购来源类型
    /// </summary>
    public LineProductPurchaseSourceType PurchaseSourceType { get; set; }
    
    /// <summary>
    /// 采购折扣比例[0-100] 2位小数
    /// <remarks>
    /// <para>如15表示为15%的折扣</para>
    /// <para>API对接直接读取供应端数据</para>
    /// </remarks>
    /// </summary>
    public decimal CostDiscountRate { get; set; }
}