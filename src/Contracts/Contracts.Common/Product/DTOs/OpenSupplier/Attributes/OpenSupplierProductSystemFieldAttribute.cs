using Contracts.Common.Product.Enums;

namespace Contracts.Common.Product.DTOs.OpenSupplier.Attributes;

/// <summary>
/// 开发平台产品系统字段特性
/// </summary>
[AttributeUsage(AttributeTargets.Field , AllowMultiple = true)]
public class OpenSupplierProductSystemFieldAttribute : Attribute
{
    public ProductType ProductType { get; set; }

    /// <summary>
    /// 字段key
    /// <remarks>匹配对应产品的saas字段名称</remarks>
    /// </summary>
    public string FieldKey { get; set; }
    
    public OpenSupplierProductSystemFieldAttribute(ProductType productType, string fieldKey)
        => (ProductType, FieldKey) = (productType, fieldKey);
}