namespace Contracts.Common.Product.DTOs.LineProductSku;

public class UpdateLineSkuApiContentInput
{
    /// <summary>
    /// 套餐信息
    /// </summary>
    public List<UpdateLineSkuApiContentItem> SkuContentItems { get; set; } = new();
}

public class UpdateLineSkuApiContentItem
{
    /// <summary>
    /// 线路产品id
    /// </summary>
    public long LineProductSkuId { get; set; }
    
    /// <summary>
    /// 费用包含
    /// </summary>
    public string? FeeNote { get; set; }

    /// <summary>
    /// 费用不含
    /// </summary>
    public string? FeeNotNote { get; set; }
    
    /// <summary>
    /// 有效期描述
    /// </summary>
    public string? ValidityDescription { get; set; }
    
    /// <summary>
    /// 注意事项
    /// </summary>
    public string? Precautions { get; set; }

    /// <summary>
    /// 使用方式
    /// </summary>
    public string? UsageInstructions { get; set; }

    /// <summary>
    /// 其他说明
    /// </summary>
    public string? OtherInstructions { get; set; }
    
    /// <summary>
    /// 取消政策
    /// </summary>
    public string? CancellationPolicy { get; set; }
}