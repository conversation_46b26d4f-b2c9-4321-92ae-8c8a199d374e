using Contracts.Common.Product.Enums;

namespace Contracts.Common.Product.DTOs.LineProductSku;

public class GetLineProductSkuDetailOutput : LineProductSkuInfo
{
    public long Id { get; set; }

    /// <summary>
    /// 租户id
    /// </summary>
    public long TenantId { get; set; }

    /// <summary>
    /// 上架/下架
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// sku最低日历价
    /// </summary>
    public decimal? MinSellingPrice { get; set; }
    
    /// <summary>
    /// 采购来源类型
    /// </summary>
    public LineProductPurchaseSourceType PurchaseSourceType { get; set; }

    /// <summary>
    /// 活动id
    /// <remarks>供应端 - productid</remarks>
    /// </summary>
    public string? ActivityId { get; set; }

    /// <summary>
    /// 套餐包id
    /// <remarks>供应端 - optionId</remarks>
    /// </summary>
    public string? PackageId { get; set; }
}