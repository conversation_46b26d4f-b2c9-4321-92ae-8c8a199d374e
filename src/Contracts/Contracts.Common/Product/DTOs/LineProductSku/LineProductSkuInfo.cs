using Contracts.Common.Product.DTOs.LineProduct;
using Contracts.Common.Product.Enums;

namespace Contracts.Common.Product.DTOs.LineProductSku;

public class LineProductSkuInfo
{
    /// <summary>
    /// 线路产品Id
    /// </summary>
    public long LineProductId { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 含住宿
    /// </summary>
    public bool IncludedAccommodation { get; set; }

    /// <summary>
    /// 费用包含
    /// </summary>
    public List<FeeIncludeDto> FeeIncludes { get; set; }
    
    /// <summary>
    /// 时段场次名称
    /// </summary>
    public string? TimeSlotName { get; set; }

    /// <summary>
    /// 线路套餐票种信息
    /// </summary>
    public List<LineProductSkuTypeItemInfo> SkuTypeItemInfos { get; set; } = new();
    
    /// <summary>
    /// 采购折扣比例[0-100] 2位小数
    /// <remarks>
    /// <para>如15表示为15%的折扣</para>
    /// <para>API对接直接读取供应端数据</para>
    /// </remarks>
    /// </summary>
    public decimal CostDiscountRate { get; set; }
    
        
    /// <summary>
    /// 费用包含
    /// </summary>
    public string? FeeNote { get; set; }

    /// <summary>
    /// 费用不含
    /// </summary>
    public string? FeeNotNote { get; set; }
    
    /// <summary>
    /// 有效期描述
    /// </summary>
    public string? ValidityDescription { get; set; }
    
    /// <summary>
    /// 注意事项
    /// </summary>
    public string? Precautions { get; set; }

    /// <summary>
    /// 使用方式
    /// </summary>
    public string? UsageInstructions { get; set; }

    /// <summary>
    /// 其他说明
    /// </summary>
    public string? OtherInstructions { get; set; }
    
    /// <summary>
    /// 取消政策
    /// </summary>
    public string? CancellationPolicy { get; set; }
}

public class LineProductSkuTypeItemInfo
{
    /// <summary>
    /// 票种年龄段类型
    /// </summary>
    public LineSkuPriceType SkuPriceType { get; set; }

    /// <summary>
    /// 线路套餐票种id
    /// </summary>
    public long? SkuTypeItemId { get; set; }

    /// <summary>
    /// 线路套餐票种名称
    /// </summary>
    public string SkuTypeItemName { get; set; }
}