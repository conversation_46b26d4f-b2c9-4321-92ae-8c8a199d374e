using Contracts.Common.Product.DTOs.OpenSupplier.Attributes;
using System.ComponentModel;

namespace Contracts.Common.Product.Enums;

/// <summary>
/// 开发平台类型
/// </summary>
public enum OpenPlatformType
{
    OpenSupplier =1,
    OpenChannel =2
}


/// <summary>
/// 开发平台产品模块类型
/// </summary>
public enum OpenSupplierProductModuleType
{
    [Description("产品介绍")]
    ProductIntroduction = 1,
    
    [Description("价格说明")]
    PriceExplanation = 2,
    
    [Description("预定须知")]
    BookingKnowledge = 3,
    
    [Description("政策")]
    Policy = 4
}

/// <summary>
/// 开发平台产品系统字段
/// </summary>
public enum OpenSupplierProductSystemFieldType
{
    [Description("产品图片")]
    [OpenSupplierProductSystemField(ProductType.Line,"ProductPictures")]
    Pictures = 1,
    
    [Description("名称")]
    [OpenSupplierProductSystemField(ProductType.Line,"Title")]
    Name = 2,
    
    [Description("亮点")]
    [OpenSupplierProductSystemField(ProductType.Line,"SellPointDescribe")]
    Highlights = 3,
    
    [Description("营业时间")]
    BusinessHours = 4,
    
    [Description("景点公告")]
    ScenicNotice = 5,
    
    [Description("地址")]
    Address = 6,
    
    [Description("产品详情")]
    [OpenSupplierProductSystemField(ProductType.Line,"Content")]
    ProductDetails = 7,
    
    [Description("价格包含")]
    [OpenSupplierProductSystemField(ProductType.Line,"FeeNote")]
    FeeNote = 8,
    
    [Description("价格不包含")]
    [OpenSupplierProductSystemField(ProductType.Line,"FeeNotNote")]
    FeeNotNote = 9,
    
    [Description("有效期")]
    [OpenSupplierProductSystemField(ProductType.Line,"ValidityDescription")]
    ValidityDescription = 10,
    
    [Description("注意事项")]
    [OpenSupplierProductSystemField(ProductType.Line,"Precautions")]
    Precautions = 11,
    
    [Description("如何使用")]
    [OpenSupplierProductSystemField(ProductType.Line,"UsageInstructions")]
    UsageInstructions = 12,
    
    [Description("其他")]
    [OpenSupplierProductSystemField(ProductType.Line,"OtherInstructions")]
    OtherInstructions = 13,
    
    [Description("取消政策")]
    [OpenSupplierProductSystemField(ProductType.Line,"CancellationPolicy")]
    CancellationPolicy = 14
}