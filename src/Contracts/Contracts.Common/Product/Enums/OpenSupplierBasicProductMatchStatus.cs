using System.ComponentModel;

namespace Contracts.Common.Product.Enums;

/// <summary>
/// 供应端 - 基础产品匹配状态
/// </summary>
public enum OpenSupplierBasicProductMatchStatus
{
    /// <summary>
    /// 等待匹配(未进行场次匹配)
    /// </summary>
    [Description("等待匹配")]
    WaitingMatch = 0,

    /// <summary>
    /// 匹配成功
    /// </summary>
    [Description("匹配成功")]
    MatchSuccess = 1,

    /// <summary>
    /// 匹配失败
    /// </summary>
    [Description("匹配失败")]
    MatchFailed = 2,
}


public enum BasicProductMatchStatus
{
    /// <summary>
    /// 未匹配.等待初始化
    /// </summary>
    [Description("等待初始化")]
    WaitingInit = 0,

    /// <summary>
    /// 已添加
    /// </summary>
    [Description("已添加")]
    Added = 1,
    
    /// <summary>
    /// 匹配失败(无匹配)
    /// </summary>
    [Description("匹配失败(无匹配)")]
    MatchFailed = 2,
    
    /// <summary>
    /// 新增(可以进行添加)
    /// </summary>
    [Description("新增匹配")]
    New = 3,
}