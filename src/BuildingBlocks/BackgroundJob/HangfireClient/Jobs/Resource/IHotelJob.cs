using Hangfire;
using System.Threading;
using System.Threading.Tasks;

namespace HangfireClient.Jobs.Resource;

public interface IHotelJob
{
    /// <summary>
    /// 批量获取hop酒店信息
    /// </summary>
    /// <returns></returns>
    Task<IJobResult> BatchHopHotelInfo();

    /// <summary>
    /// HOP资源酒店信息每天同步 RecurringJob
    /// </summary>
    /// <returns></returns>
    Task<IJobResult> SyncHotels();

}
