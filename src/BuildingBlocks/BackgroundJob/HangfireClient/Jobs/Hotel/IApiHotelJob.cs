using System.Threading.Tasks;

namespace HangfireClient.Jobs.Hotel;
public interface IApiHotelJob
{
    /// <summary>
    /// 添加第三方酒店
    /// </summary>
    /// <param name="apiHotelHositoryId"></param>
    /// <returns></returns>
    Task<IJobResult> AddApiHotel(long apiHotelHositoryId, long tenantId);

    /// <summary>
    /// 同步 ApiHotel Tags 
    /// </summary>
    /// <returns></returns>
    Task<IJobResult> SyncHopApiHotelTags();

}
