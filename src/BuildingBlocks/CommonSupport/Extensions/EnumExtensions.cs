using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Extensions
{
    public static class EnumExtensions
    {
        /// <summary>
        /// 获取Description
        /// </summary>
        /// <param name="enum"></param>
        /// <returns></returns>
        public static string GetDescription(this Enum @enum)
        {
            FieldInfo fi = @enum.GetType().GetField(@enum.ToString());
            if (fi == null)
            {
                return string.Empty;
            }
            DescriptionAttribute[] attributes = (DescriptionAttribute[])fi.GetCustomAttributes(typeof(DescriptionAttribute), false);
            return attributes.FirstOrDefault()?.Description ?? @enum.ToString();
        }

        /// <summary>
        /// 将描述转成枚举
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="desc"></param>
        /// <returns></returns>
        public static T EnumConvertByDescription<T>(string desc)
        {
            var fields = typeof(T).GetFields(BindingFlags.Static | BindingFlags.Public);
            var field = fields.FirstOrDefault(w => (w.GetCustomAttribute(typeof(DescriptionAttribute)) as DescriptionAttribute)?.Description == desc);
            if (field == null)
                return default(T);
            return (T)Enum.Parse(typeof(T), field.Name);
        }

        /// <summary>
        /// 获取所有枚举值和描述
        /// </summary>
        /// <param name="enum"></param>
        /// <returns></returns>
        public static IEnumerable<KeyValuePair<int, string>> GetAllDescription<T>() where T : Enum
        {
            var result = Enumerable.Empty<KeyValuePair<int, string>>();
            var type = typeof(T);
            var fields = type.GetFields();
            foreach (FieldInfo field in fields)
            {
                if (field.FieldType != type)
                    continue;
                object[] attr = field.GetCustomAttributes(typeof(DescriptionAttribute), false);
                string description = attr.Length == 0 ? field.Name : ((DescriptionAttribute)attr[0]).Description;
                var key = (int)Enum.Parse(type, field.Name);
                result = result.Append(new KeyValuePair<int, string>(key, description));
            }
            return result;
        }
        
        /// <summary>
        /// 通用枚举属性获取扩展
        /// </summary>
        public static TAttribute GetEnumAttribute<TAttribute>(this Enum @enum) where TAttribute : Attribute
        {
            var field = @enum.GetType().GetField(@enum.ToString());
            return field?.GetCustomAttribute<TAttribute>();
        }
        
        /// <summary>
        /// 获取枚举字段的所有指定类型属性
        /// </summary>
        /// <typeparam name="TAttribute">属性类型</typeparam>
        /// <param name="enum">枚举值</param>
        /// <returns>所有匹配的属性列表</returns>
        public static IEnumerable<TAttribute> GetEnumAttributes<TAttribute>(this Enum @enum) where TAttribute : Attribute
        {
            var field = @enum.GetType().GetField(@enum.ToString());
            return field?.GetCustomAttributes<TAttribute>() ?? Enumerable.Empty<TAttribute>();
        }
    }
}
