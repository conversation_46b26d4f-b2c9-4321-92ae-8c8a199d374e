using Common.ServicesHttpClient;
using Contracts.Common.Hotel.DTOs.ApiHotel;
using Contracts.Common.Hotel.DTOs.ApiHotel.HopApiHotel;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Resource.DTOs.HopHotel;
using Hangfire.Server.Extensions;
using Hangfire.Server.Filters;
using HangfireClient.Jobs;
using HangfireClient.Jobs.Hotel;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Hangfire.Server.Jobs.Hotel;

public class ApiHotelJob : IApiHotelJob
{
    private readonly IOptions<ServicesAddress> _servicesAddress;
    private readonly IHttpClientFactory _httpClientFactory;

    public ApiHotelJob(
        IOptions<ServicesAddress> servicesAddress,
        IHttpClientFactory httpClientFactory)
    {
        _servicesAddress = servicesAddress;
        _httpClientFactory = httpClientFactory;
    }

    /// <summary>
    /// 添加第三方酒店
    /// </summary>
    /// <param name="apiHotelHositoryId"></param>
    /// <returns></returns>
    [Obsolete]
    public async Task<IJobResult> AddApiHotel(long apiHotelHositoryId, long tenantId)
    {
        var headers = new List<KeyValuePair<string, string>> { new("tenant", tenantId.ToString()) };
        var apiHotelHositoryDto = await _httpClientFactory.InternalGetAsync<ApiHotelHositoryDto>(
            _servicesAddress.Value.Hotel_BindApiHotel(apiHotelHositoryId),
            headers: headers);

        if (apiHotelHositoryDto.AddApiHotelStatus.Equals(AddApiHotelStatus.InProgress))
            return await AddApiHotel(apiHotelHositoryId, tenantId);

        return new JobResult { Data = apiHotelHositoryDto };
    }

    /// <summary>
    /// 更新 ApiHotel Tag敏感度信息
    /// </summary>
    /// <returns></returns>
    public async Task<SyncHopApiHotelTagsDto> SyncApiHotelTags()
    {
        var requestUrl = _servicesAddress.Value.Hotel_SyncApiHotelTags();
        //var headers = new List<KeyValuePair<string, string>> { new("tenant", tenantId.ToString()) };
        return await _httpClientFactory.InternalGetAsync<SyncHopApiHotelTagsDto>(requestUri: requestUrl);
    }

    [RecurringJob("Hotel.同步 ApiHotel 在售标签", "0 0 3 * * ?")]
    public async Task<IJobResult> SyncHopApiHotelTags()
    {
        var run = true;
        var result = new SyncHopApiHotelTagsDto();
        while (run)
        {
            var hotelInfo = await SyncApiHotelTags();

            result.Count += hotelInfo.Count;
            result.UpdateCount += hotelInfo.UpdateCount;
            result.MaxId = hotelInfo.MaxId;

            if (hotelInfo.Done)
                run = false;
        }
        return new JobResult { Data = result };
    }
}
