using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common.Resource.DTOs.HopHotel;
using Hangfire.Server.ConfigModel;
using Hangfire.Server.Extensions;
using Hangfire.Server.Filters;
using HangfireClient.Jobs;
using HangfireClient.Jobs.Resource;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Threading;

namespace Hangfire.Server.Jobs.Resource
{
    public class HotelJob : IHotelJob
    {
        private readonly IOptions<ServicesAddress> _servicesAddress;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IOptionsMonitor<HuiZhiAsyncHotelConfig> _huiZhiAsyncHotelConfig;

        public HotelJob(IOptions<ServicesAddress> servicesAddress,
            IHttpClientFactory httpClientFactory,
            IOptionsMonitor<HuiZhiAsyncHotelConfig> huiZhiAsyncHotelConfig)
        {
            _servicesAddress = servicesAddress;
            _httpClientFactory = httpClientFactory;
            _huiZhiAsyncHotelConfig = huiZhiAsyncHotelConfig;
        }

        [RecurringJob("Resource.批量同步HOP酒店信息", "0 13 * * * ?")]
        public async Task<IJobResult> BatchHopHotelInfo()
        {
            var requestUrl = _servicesAddress.Value.Resource_BatchHOPHotelInfo();
            var response = await _httpClientFactory.InternalPostAsync<DownloadHopHotelDataResult>(requestUrl);
            return new JobResult { Message = response.Message, Data = response };
        }

        #region HOP直采资源酒店信息同步

        [RecurringJob("Resource.HOP资源酒店信息每天同步", "0 0 3 * * ?")]
        public async Task<IJobResult> SyncHotels()
        {
            var hotelCount = 0;
            int? minHid = _huiZhiAsyncHotelConfig.CurrentValue.MinHid;
            int count = _huiZhiAsyncHotelConfig.CurrentValue.Count;
            DateTime? lastUpdateTime = _huiZhiAsyncHotelConfig.CurrentValue.LastUpdateTime;//默认更新昨天更新过的HOP酒店
            
            var client = _httpClientFactory.CreateClient();
            client.Timeout = TimeSpan.FromMinutes(10);
            CancellationTokenSource cancellationTokenSource = new(_huiZhiAsyncHotelConfig.CurrentValue.CancellationTokenDelay);
            var cancellationToken = cancellationTokenSource.Token;
            while (cancellationToken.IsCancellationRequested is false)
            {
                try
                {
                    var requestUrl = _servicesAddress.Value.Resource_SyncHotelInfo();
                    SyncHotelInfoInput input = new() { MinHid = minHid, Count = count, LastUpdateTime = lastUpdateTime };
                    var httpContent = new StringContent(JsonConvert.SerializeObject(input), System.Text.Encoding.UTF8, "application/json");
                    var responseMessage = await client.PostAsync(requestUrl, httpContent, cancellationToken);
                    responseMessage.EnsureSuccessOrThrowException();
                    var responseContent = await responseMessage.Content.ReadAsStringAsync();
                    var response = JsonConvert.DeserializeObject<SyncHotelInfoOutput>(responseContent);
                    hotelCount += response.Count;
                    minHid = response.MaxId;
                    if (response.IsFinished)
                    {
                        cancellationTokenSource.Cancel();
                        break;
                    }
                }
                catch
                {
                    cancellationTokenSource.Cancel();
                    throw;
                }
            }
            return new JobResult { Message = $"本次更新酒店资源数:hotelCount:{hotelCount},LastHOPId:{minHid}" };
        }

        #endregion
    }
}
