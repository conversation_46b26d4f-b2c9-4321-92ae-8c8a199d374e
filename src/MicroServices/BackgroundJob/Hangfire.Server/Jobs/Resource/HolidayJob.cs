using Common.ServicesHttpClient;
using Hangfire.Server.Extensions;
using Hangfire.Server.Filters;
using HangfireClient.Jobs;
using HangfireClient.Jobs.Resource;
using Microsoft.Extensions.Options;

namespace Hangfire.Server.Jobs.Resource;

public class HolidayJob : IHolidayJob
{
    private readonly IOptions<ServicesAddress> _servicesAddress;
    private readonly IHttpClientFactory _httpClientFactory;

    public HolidayJob(IOptions<ServicesAddress> servicesAddress,
        IHttpClientFactory httpClientFactory)
    {
        _servicesAddress = servicesAddress;
        _httpClientFactory = httpClientFactory;
    }

    /// <summary>
    /// 同步节假日信息,每月15,20,25号0点执行
    /// 因为有些节假日数据是跨年的，12-30、12-31有时候会跨年，放在下一年数据中了
    /// </summary>
    /// <returns></returns>
    [RecurringJob("Resource.同步节假日信息", "0 0 0 15,20,25 12 *")]
    public async Task<IJobResult> SyncHolidayList()
    {
        var requestUrl = _servicesAddress.Value.Resource_SyncHolidayList();
        var response = await _httpClientFactory.InternalGetAsync<string>(requestUrl);
        return new JobResult { Message = response, Data = response };
    }
}
