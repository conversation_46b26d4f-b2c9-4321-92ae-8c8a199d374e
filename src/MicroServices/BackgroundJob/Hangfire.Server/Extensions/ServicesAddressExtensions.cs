using Common.ServicesHttpClient;

namespace Hangfire.Server.Extensions;

public static class ServicesAddressExtensions
{
    #region Resource

    public static string Resource_BatchHOPHotelInfo(this ServicesAddress address)
          => $"{address.Resource}/HopHotel/BatchHotelInfo";

    public static string Resource_SyncHotelInfo(this ServicesAddress address)
     => $"{address.Resource}/HopHotel/SyncHotelInfo";

    /// <summary>
    /// 取消 第三方平台酒店订单
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Resource_ThirdHotelCancelOrder(this ServicesAddress address)
        => $"{address.Resource}/ThirdHotel/CancelOrder";

    /// <summary>
    /// 同步节假日信息
    /// </summary>
    public static string Resource_SyncHolidayList(this ServicesAddress address)
               => $"{address.Resource}/Holiday/SyncHolidayList";
    #endregion

    #region Order

    /// <summary>
    /// 关闭超时订单
    /// </summary>
    /// <param name="address"></param>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    public static string Order_CloseTimeoutOrder(this ServicesAddress address, long baseOrderId)
           => $"{address.Order}/BaseOrder/CancelByJob?baseOrderId={baseOrderId}";

    /// <summary>
    /// 延时支付订单超时未实付取消订单
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_OrderDelayedTimeoutCancel(this ServicesAddress address)
       => $"{address.Order}/OrderDelayed/Cancel";

    /// <summary>
    /// 延时未实付订单 付款提醒
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_OrderDelayedPayNotify(this ServicesAddress address)
       => $"{address.Order}/OrderDelayed/DelayedPayNotify";

    /// <summary>
    /// 关闭超时订单
    /// </summary>
    /// <param name="address"></param>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    public static string Order_CloseTimeOutReservationOrder(this ServicesAddress address, long reservationOrderId)
           => $"{address.Order}/ReservationOrder/CloseTimeOut?reservationOrderId={reservationOrderId}";

    /// <summary>
    /// 关闭超时团房单
    /// </summary>
    /// <param name="address"></param>
    /// <param name="groupBookingOrderId"></param>
    /// <returns></returns>
    public static string Order_CloseTimeoutGroupBookingOrder(this ServicesAddress address, long groupBookingOrderId)
          => $"{address.Order}/HotelGroupBookingOrder/Close?groupBookingOrderId={groupBookingOrderId}";

    /// <summary>
    /// 组合订单发货
    /// </summary>
    /// <param name="address"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    public static string Order_ScenicTicketCombinationOrderDelivery(this ServicesAddress address, long id)
        => $"{address.Order}/TicketsCombinationOrder/Delivery?id={id}";

    /// <summary>
    /// 完成电商邮寄订单
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_CompletedMailOrder(this ServicesAddress address)
           => $"{address.Order}/MailOrder/CompletedMailOrder";

    /// <summary>
    /// 券类订单过期自动完结
    /// </summary>
    public static string Order_FinishExpirationTicketOrder(this ServicesAddress address)
        => $"{address.Order}/TicketOrder/FinishExpiration";

    /// <summary>
    /// 券类订单自动时间提醒
    /// </summary>
    public static string Order_TicketOrderAutoTimeReminder(this ServicesAddress address)
        => $"{address.Order}/TicketOrder/AutoTimeReminder";

    /// <summary>
    /// 预约单过期自动完结(仅预约产品的预约订单)
    /// </summary>
    public static string Order_FinishExpirationReservationOrder(this ServicesAddress address)
        => $"{address.Order}/ReservationOrder/FinishExpiration";

    /// <summary>
    /// 券类预约单自动时间提醒
    /// </summary>
    public static string Order_ReservationOrderAutoTimeReminder(this ServicesAddress address)
        => $"{address.Order}/ReservationOrder/AutoTimeReminder";

    public static string Order_HotelOrderCheckIn(this ServicesAddress address)
          => $"{address.Order}/HotelOrder/CheckInByJob";

    public static string Order_HotelOrderCheckOut(this ServicesAddress address)
         => $"{address.Order}/HotelOrder/CheckOutByJob";

    public static string Order_HotelOrderCheckInTimeReminder(this ServicesAddress address)
            => $"{address.Order}/HotelOrder/CheckInTimeReminder";

    public static string Order_GDSHotelOrderConfirmed(this ServicesAddress address)
        => $"{address.Order}/GDSOrder/GetBookingConfirmed";

    /// <summary>
    /// 查询api订单并处理更新订单
    /// </summary>
    public static string Order_HotelApiOrderQueryAndHandleResult(this ServicesAddress address)
        => $"{address.Order}/HotelApiOrder/QueryAndHandleResult";

    /// <summary>
    /// 取消酒店api订单
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_HotelApiOrderCancel(this ServicesAddress address)
        => $"{address.Order}/HotelApiOrder/Cancel";

    /// <summary>
    /// 景点门票过期自动退[定时任务]
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_ScenicTicketOrderAutoRefundAfterExpiration(this ServicesAddress address)
       => $"{address.Order}/ScenicTicketOrder/AutoRefundAfterExpiration";

    /// <summary>
    /// 景点门票自动时间提醒
    /// </summary>
    public static string Order_ScenicTicketOrderAutoTimeReminder(this ServicesAddress address)
        => $"{address.Order}/ScenicTicketOrder/AutoTimeReminder";

    /// <summary>
    /// 线路出行时间提醒
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_TravelLineOrderAutoTimeReminder(this ServicesAddress address)
      => $"{address.Order}/TravelLineOrder/AutoTimeReminder";

    public static string Order_CarHailingOrderAutoFinish(this ServicesAddress address)
      => $"{address.Order}/CarHailingOrder/AutoFinish";

    /// <summary>
    /// 自动完成发票状态
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_InvoiceRecordAutoFinished(this ServicesAddress address)
      => $"{address.Order}/InvoiceRecord/AutoFinished";

    /// <summary>
    /// 金蝶 - 执行单据推送
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_KingdeeBillPushExecute(this ServicesAddress address)
        => $"{address.Order}/Kingdee/KingdeeBillPushExecute";

    /// <summary>
    /// 金蝶 应收单推送
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_KingdeeBillPush(this ServicesAddress address)
        => $"{address.Order}/Kingdee/BillPush";

    public static string Order_KingdeeBillPushResultNotify(this ServicesAddress address)
        => $"{address.Order}/Kingdee/BillPushResultNotify";

    /// <summary>
    /// 批量更新base订单的连续单号
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_BatchUpdateBaseOrderSeriesNumber(this ServicesAddress address)
      => $"{address.Order}/BaseOrderSeriesNumber/BatchUpdateOrderSeriesNumber";

    /// <summary>
    /// 获取分销商最近购买时间
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_SearchAgencyOrderRecencyTime(this ServicesAddress address)
      => $"{address.Order}/BaseOrder/SearchAgencyOrderRecencyTime";

    /// <summary>
    /// 更新希望保保险产品
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_UpateThirdInsureProduct(this ServicesAddress address)
      => $"{address.Order}/ThirdInsureProduct/SyncProduct";

    /// <summary>
    /// 查询未完成保单详情，更新保单状态信息
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_InsurePurchaseRecordStatusFlowProcess(this ServicesAddress address)
      => $"{address.Order}/InsurePurchaseRecord/InsureStatusFlowProcessByJob";

    /// <summary>
    /// 查询未完成保单列表
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_GetInsureStatusDatas(this ServicesAddress address)
      => $"{address.Order}/InsurePurchaseRecord/GetInsureStatusDatas";

    /// <summary>
    /// 更新保单状态信息
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_UpdateInsureStatusByOnce(this ServicesAddress address)
      => $"{address.Order}/InsurePurchaseRecord/UpdateInsureStatusByOnce";

    /// <summary>
    /// 批量投保
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_BatchInsurePolicy(this ServicesAddress address)
      => $"{address.Order}/Insure/BatchInsurePolicy";

    /// <summary>
    /// 根据单个订单投保
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_InsureByOrder(this ServicesAddress address)
      => $"{address.Order}/Insure/InsureByOrder";

    /// <summary>
    /// 批量获取投保订单
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_GetNeedInsureOrders(this ServicesAddress address)
      => $"{address.Order}/Insure/GetNeedInsureOrders";

    /// <summary>
    /// 团房付款提醒
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_HotelGroupPaymentReminder(this ServicesAddress address)
      => $"{address.Order}/HotelGroupBooking/PaymentReminder";

    /// <summary>
    /// 入住人提醒
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_HotelGroupGuestsReminder(this ServicesAddress address)
      => $"{address.Order}/HotelGroupBooking/GuestsReminder";

    /// <summary>
    /// 团房申请单搜索
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_HotelGroupBookingSearch(this ServicesAddress address)
      => $"{address.Order}/HotelGroupBooking/Search";

    /// <summary>
    /// 团房推荐目的地需求酒店
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_GroupBookingRecommendDemandHotel(this ServicesAddress address)
        => $"{address.Order}/HotelGroupBooking/RecommendDemandHotel";

    /// <summary>
    /// 团房 酒店邮件发单询价
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_HotelGroupBookingHotelInquiry(this ServicesAddress address)
     => $"{address.Order}/HotelGroupBooking/HotelInquiry";

    /// <summary>
    /// 团房酒店 询价发单 通知
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_HotelGroupBookingDemandHotelInquiryHandle(this ServicesAddress address)
     => $"{address.Order}/HotelGroupBooking/DemandHotelInquiryHandle";

    /// <summary>
    /// 修改团房申请单状态
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_HotelGroupBookingApplicationFormStatusModify(this ServicesAddress address)
      => $"{address.Order}/HotelGroupBooking/ApplicationFormStatusModify";

    /// <summary>
    /// 执行批量操作
    /// </summary>
    /// <param name="address"></param>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    public static string Order_OrderOperationTaskRun(this ServicesAddress address, long taskId)
          => $"{address.Order}/OrderOperationTask/OrderOperationTaskRun?taskId={taskId}";

    /// <summary>
    /// 收款结算单-获取账单提醒数据
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Order_ReceiptSettlementOrderGetDisableAgency(this ServicesAddress address)
          => $"{address.Order}/ReceiptSettlementOrder/GetDisableAgency";

    /// <summary>
    ///  定时更新WorkOrder的Hop最新信息
    /// </summary>
    /// <param name="address"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    public static string Ordert_UpdateHopForWorkOrder(this ServicesAddress address)
          => $"{address.Order}/WorkOrder/UpdateHopForWorkOrderByJob";

    /// <summary>
    ///  自动生成账单
    /// </summary>
    /// <param name="address"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    public static string Ordert_ReceiptOrderAutoCreate(this ServicesAddress address)
          => $"{address.Order}/ReceiptOrder/AutoCreate";

    /// <summary>
    ///  获取自动生成账单配置
    /// </summary>
    /// <param name="address"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    public static string Ordert_ReceiptOrderSetting(this ServicesAddress address)
          => $"{address.Order}/ReceiptOrder/GetSetting";

    public static string Order_GroupBookingAggregateSyncByJob(this ServicesAddress address)
         => $"{address.Order}/GroupBookingAggregate/SyncByJob";
    #endregion

    #region Notify

    public static string Notify_UpdateSignApplyStatus(this ServicesAddress address)
          => $"{address.Notify}/ShortMessage/UpdateSignApplyStatus";

    #endregion

    #region Payment

    public static string Payment_YeeWechatAuthQuery(this ServicesAddress address)
          => $"{address.Payment}/YeeWechat/Query";

    public static string Payment_PullDailyExchangeRate(this ServicesAddress address)
        => $"{address.Payment}/CurrencyExchangeRate/PullDailyExchangeRate";

    public static string Payment_YeeWechatConfigQuery(this ServicesAddress address)
      => $"{address.Payment}/YeeWechat/WechatConfigQuery";


    #endregion

    #region Marketing

    public static string Marketing_OpenOrCloseFlashSale(this ServicesAddress address)
          => $"{address.Marketing}/FlashSale/OpenOrClose";

    public static string Marketing_UpdateLotteryStatus(this ServicesAddress address)
          => $"{address.Marketing}/Lottery/UpdateLotteryStatus";

    public static string Marketing_AddTraceDailyStatistics(this ServicesAddress address)
        => $"{address.Marketing}/PromotionTraceRecord/AddTraceDailyStatistics";

    public static string Marketing_AgencyCouponsExpireRemind(this ServicesAddress address)
        => $"{address.Marketing}/UserCoupon/AgencyCouponsExpireRemind";

    #endregion

    #region Hotel

    /// <summary>
    /// 绑定第三方酒店
    /// </summary>
    /// <param name="address"></param>
    /// <param name="apiHotelHository"></param>
    /// <returns></returns>
    public static string Hotel_BindApiHotel(this ServicesAddress address, long apiHotelHositoryId)
           => $"{address.Hotel}/ApiHotel/Bind?apiHotelHositoryId={apiHotelHositoryId}";

    /// <summary>
    /// 批量设置汇智酒店标志
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Hotel_UpdateTags(this ServicesAddress address)
           => $"{address.Hotel}/ApiHotel/UpdateTags";

    /// <summary>
    ///批量更新 ApiHotel 售卖Tags
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Hotel_SyncApiHotelTags(this ServicesAddress address)
           => $"{address.Hotel}/ApiHotel/SyncApiHotelTags";

    /// <summary>
    /// 处理酒店匹配结果
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Hotel_HotelMappingPushQueryHandle(this ServicesAddress address)
       => $"{address.Hotel}/HotelPush/HotelMappingPushQueryHandle";

    /// <summary>
    /// 查询处理中的匹配酒店
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Hotel_GetProcessMappingHotels(this ServicesAddress address)
       => $"{address.Hotel}/HotelPush/GetProcessMappingHotels";

    #endregion

    #region Tenant

    /// <summary>
    /// 关闭分销商额度充值单
    /// </summary>
    /// <param name="address"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    public static string Order_CloseAgencyCreditChargeOrder(this ServicesAddress address, long id)
          => $"{address.Tenant}/AgencyCreditCharge/Close?id={id}";

    /// <summary>
    /// 关闭分销商额度充值单
    /// </summary>
    /// <param name="address"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    public static string Agency_SyncAgencyRecencyOrder(this ServicesAddress address)
          => $"{address.Tenant}/Agency/SyncAgencyRecencyOrder";

    /// <summary>
    /// 获取额度支付的分销商
    /// </summary>
    /// <param name="address"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    public static string Tenant_GetAgenciesByReceiptOrder(this ServicesAddress address)
          => $"{address.Tenant}/Agency/GetAgenciesByReceiptOrder";

    /// <summary>
    /// 等级变化重新核算分销商等级
    /// </summary>
    /// <param name="address"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    public static string Agency_RunReAgencyLevelCalculate(this ServicesAddress address)
          => $"{address.Tenant}/AgencyLevelConfig/RunAgencyLevelCalculateTaskMq";

    /// <summary>
    /// 周期核算分销商等级
    /// </summary>
    /// <param name="address"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    public static string Agency_RunCycleAgencyLevelCalculate(this ServicesAddress address)
          => $"{address.Tenant}/AgencyLevelConfig/RunCycleAgencyLevelCalculateTaskMq";

    /// <summary>
    /// 获取Api供应商配置
    /// </summary>
    /// <param name="address"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    public static string Tenant_GetSupplierApiSettingInfos(this ServicesAddress address)
          => $"{address.Tenant}/SupplierApiSetting/GetApiSettingInfos";

    /// <summary>
    /// 每月月初 财务月报 生成数据
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Tenant_AgencyMonthlyReport(this ServicesAddress address)
      => $"{address.Tenant}/AgencyMonthlyReport/Report";

    /// <summary>
    /// 同步B2B首页酒店权重
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Tenant_SyncB2BIndexPageHotel(this ServicesAddress address)
      => $"{address.Tenant}/B2BIndexPage/SyncB2BIndexPageHotel";

    /// <summary>
    /// B2b首页获取组件列表
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Tenant_GetB2BIndexPageComponents(this ServicesAddress address)
      => $"{address.Tenant}/B2BIndexPage/GetComponents";

    /// <summary>
    /// B2b专题页获取组件列表
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Tenant_SearchB2BTopicPageComponentsByJob(this ServicesAddress address)
      => $"{address.Tenant}/B2BTopicPage/SearchByJob";

    /// <summary>
    /// 更新hop热门酒店
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Tenant_UpdateB2BHotHotel(this ServicesAddress address)
      => $"{address.Tenant}/B2BHotHotel/Update";

    /// <summary>
    /// 更新hop热门酒店
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public static string Tenant_SyncB2BIndexHotel(this ServicesAddress address)
      => $"{address.Tenant}/B2BIndexHotel/Sync";

    /// <summary>
    /// 关闭分销商额度充值单
    /// </summary>
    /// <param name="address"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    public static string Tenant_AgencyCreditSetEnable(this ServicesAddress address)
          => $"{address.Tenant}/AgencyCredit/SetEnable";

    #endregion
}
