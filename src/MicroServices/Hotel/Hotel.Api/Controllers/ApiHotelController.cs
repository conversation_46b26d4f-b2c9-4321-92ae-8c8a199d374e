using Cit.Storage.Redis;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Hotel.DTOs.ApiHotel;
using Contracts.Common.Hotel.DTOs.ApiHotel.HopApiHotel;
using EfCoreExtensions.Abstract;
using Hotel.Api.HostedService;
using Hotel.Api.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace Hotel.Api.Controllers;
[Route("[controller]/[action]")]
[ApiController]
public class ApiHotelController : ControllerBase
{
    private readonly IApiHotelService _apiHotelService;
    private readonly IRedisClient _redisClient;
    private readonly IConfiguration _configuration;

    public ApiHotelController(
        IApiHotelService apiHotelService,
        IRedisClient redisClient,
        IConfiguration configuration)
    {
        _apiHotelService = apiHotelService;
        _redisClient = redisClient;
        _configuration = configuration;
    }

    /// <summary>
    /// 查询第三方酒店
    /// </summary>
    [Obsolete]
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchInput input)
    {
        var result = await _apiHotelService.Search(input);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchV2(SearchInput input)
    {
        long? tenantId = HttpContext.GetTenantId();
        if (tenantId <= 0) tenantId = null;
        var result = await _apiHotelService.SearchV2(input, tenantId);
        return Ok(result);
    }

    /// <summary>
    /// 添加第三方酒店
    /// </summary>
    [Obsolete]
    [HttpPost]
    [SwaggerResponseExt(default, ErrorTypes.Tenant.IsNotApiSupplier)]
    public async Task<IActionResult> Add(AddInput input)
    {
        await _apiHotelService.Add(input);
        return Ok();
    }

    /// <summary>
    /// 添加所有满足条件的第三方酒店
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Obsolete]
    [HttpPost]
    [SwaggerResponseExt(default, ErrorTypes.Tenant.IsNotApiSupplier)]
    [SwaggerResponseExt(default, ErrorTypes.Hotel.TaskInProgress)]
    public async Task<IActionResult> AddAll(AddAllApiHotelInput input)
    {
        await _apiHotelService.AddAll(input);
        return Ok();
    }

    /// <summary>
    /// 删除第三方酒店
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Obsolete]
    [HttpPost]
    public async Task<IActionResult> Delete(DeleteInput input)
    {
        await _apiHotelService.Delete(input);
        return Ok();
    }

    /// <summary>
    /// 绑定第三方酒店
    /// </summary>
    /// <param name="ApiHotelHositoryId"></param>
    /// <returns></returns>
    [Obsolete]
    [HttpGet]
    [ProducesResponseType(typeof(ApiHotelHositoryDto), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Bind(long apiHotelHositoryId)
    {
        var result = await _apiHotelService.Bind(apiHotelHositoryId);
        return Ok(result);
    }

    /// <summary>
    /// 根据资源酒店Id检查酒店是否已经添加
    /// </summary>
    [Obsolete]
    [HttpPost]
    [ProducesResponseType(typeof(List<CheckRefOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> CheckRefByResourceHotelIds(CheckRefInput input)
    {
        var result = await _apiHotelService.CheckRefByResourceHotelIds(input);
        return Ok(result);
    }

    /// <summary>
    /// 设置是否置顶
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> SetOnTop(SetOnTopInput input)
    {
        await _apiHotelService.SetOnTopV2(input);
        return Ok();
    }

    /// <summary>
    /// 批量设置标志
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> UpdateTags(List<UpdateTagInput> input)
    {
        await _apiHotelService.UpdateTags(input);
        return Ok();
    }

    /// <summary>
    /// 批量设置酒店敏感度标签 Tag
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(SyncHopApiHotelTagsDto), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SyncApiHotelTags()
    {
        var result = await _apiHotelService.SyncApiHotelTags();
        return Ok(result);
    }

    /// <summary>
    /// 推送酒店权重值
    /// </summary>
    /// <param name="inputs"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> PushWeightValue(PushWeightValueInput[] inputs)
    {
        string weightValueListKey = ApiHotelWeightHostedService.WeightValueListKey;
        var result = await _redisClient.ListLeftPushAsync<PushWeightValueInput>(weightValueListKey, inputs);
        return Ok();
    }

    /// <summary>
    /// api酒店详情
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<GetApiHotelDetailOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Detail(params long[] ids)
    {
        var result = await _apiHotelService.Detail(new GetApiHotelDetailsInput
        {
            Ids = ids.ToList(),
        });
        return Ok(result);
    }

    /// <summary>
    /// api酒店详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<GetApiHotelDetailOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Details(GetApiHotelDetailsInput input)
    {
        var result = await _apiHotelService.Detail(input);
        return Ok(result);
    }
}
