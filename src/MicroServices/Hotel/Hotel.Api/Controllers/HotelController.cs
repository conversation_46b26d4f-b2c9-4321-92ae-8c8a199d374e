using AutoMapper;
using Common.GlobalException;
using Common.Jwt;
using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Hotel.DTOs.HotelExtend;
using Contracts.Common.Resource.DTOs.GDSHotel;
using EfCoreExtensions.Abstract;
using Hotel.Api.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace Hotel.Api.Controllers
{
    [Route("[controller]/[action]")]
    [ApiController]
    public class HotelController : ControllerBase
    {
        private readonly IHotelService _hotelService;
        private readonly IApiHotelService _apiHotelService;
        private readonly IMapper _mapper;

        public HotelController(IHotelService hotelService,
            IApiHotelService apiHotelService,
            IMapper mapper)
        {
            _mapper = mapper;
            _hotelService = hotelService;
            _apiHotelService = apiHotelService;
        }

        /// <summary>
        /// 获取日历房酒店列表(带分页)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(PagingModel<SearchHotelsOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> Search(SearchHotelsInput input)
        {
            var result = await _hotelService.Search(input);
            return Ok(result);
        }

        /// <summary>
        /// 通过城市Code分页获取酒店信息
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(PagingModel<long>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetByCityCode(GetByCityCodeInput input)
        {
            var result = await _hotelService.GetByCityCode(input);
            return Ok(result);
        }

        /// <summary>
        /// 修改日历房酒店上架状态
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> UpdateHotelEnabled(UpdateHotelEnabledInput input)
        {
            await _hotelService.UpdateHotelEnabled(input);
            return Ok();
        }


        /// <summary>
        /// 根据酒店ID获取酒店列表
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(List<GetHotelByIdsOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetByIds(GetHotelByIdsInput input)
        {
            var result = await _hotelService.GetByIds(input);
            return Ok(result);
        }


        /// <summary>
        /// 根据资源酒店Id检查酒店是否已经添加
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(List<CheckRefOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> CheckRefByResourceHotelIds(CheckRefInput input)
        {
            var result = await _hotelService.CheckRefByResourceHotelIds(input);
            return Ok(result);
        }

        /// <summary>
        /// 添加酒店
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> Add(AddHotelInput input)
        {
            await _hotelService.Add(input);
            return Ok();
        }

        /// <summary>
        /// 获取酒店详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(GetHotelDetailsOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> Detail(long hotelId)
        {
            var hotel = await _hotelService.Detail(hotelId);
          
            return Ok(hotel);
        }

        /// <summary>
        /// 修改酒店详情
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> Update(UpdateHotelDetailsInput input)
        {
            await _hotelService.Update(input);
            return Ok();
        }

        /// <summary>
        /// 获取酒店图片列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(List<GetHotelPhotosOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetHotelPhotos(long hotelId)
        {
            var result = await _hotelService.GetHotelPhotos(hotelId);
            return Ok(result);
        }

        /// <summary>
        /// 修改酒店图片
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> UpdateHotelPhotos(UpdateHotelPhotosInput input)
        {
            await _hotelService.UpdateHotelPhotos(input);
            return Ok();
        }

        /// <summary>
        /// 获取酒店房型列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GetHotelRoomsOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetHotelRooms(GetHotelRoomsInput input)
        {
            var result = await _hotelService.GetHotelRooms(input);
            return Ok(result);
        }

        /// <summary>
        /// 获取房型详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(GetHotelRoomDetailsOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetHotelRoomDetails(long roomId)
        {
            var roomIds = new List<long> { roomId };
            var rooms = await _hotelService.GetHotelRoomDetails(roomIds);
            var result = rooms.FirstOrDefault();
            return Ok(result);
        }

        /// <summary>
        /// 修改房型
        /// 床型 需要保存json格式
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> UpdateHotelRoom(UpdateHotelRoomInput input)
        {
            await _hotelService.UpdateHotelRoom(input);
            return Ok();
        }

        /// <summary>
        /// 修改房型排序
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> UpdateHotelRoomSort(UpdateHotelRoomSortInput input)
        {
            await _hotelService.UpdateHotelRoomSort(input);
            return Ok();
        }

        /// <summary>
        /// 获取酒店营业信息
        /// </summary>
        /// <param name="hotelId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(GetOperateInfoOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetOperateInfo(long hotelId)
        {
            var result = await _hotelService.GetOperateInfo(hotelId);
            return Ok(result);
        }

        /// <summary>
        /// 获取商户全部酒店（按城市分组）
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(IEnumerable<GetAllGroupbyCityOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetAllGroupbyCity()
        {
            var result = await _hotelService.GetAllGroupbyCity();
            return Ok(result);
        }

        /// <summary>
        /// 根据酒店id校验酒店是否有效
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<CheckIsEnabledOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> CheckHotelsEnabled(CheckIsEnabledInput input)
        {
            var result = await _hotelService.CheckIsEnabled(input);
            return Ok(result);
        }


        /// <summary>
        /// 修改是否自动确认房态(入住/退房)
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> UpdateAutoConfirmRoomStatus(UpdateAutoConfirmRoomStatusInput input)
        {
            await _hotelService.UpdateAutoConfirmRoomStatus(input);
            return Ok();
        }


        /// <summary>
        /// 删除酒店
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> DeleteHotel(DeleteHotelInput input)
        {
            await _hotelService.DeleteHotel(input);
            return Ok();
        }

        /// <summary>
        /// 获取酒店城市
        /// </summary>
        /// <param name="isCache">是否取缓存</param>
        /// <param name="needAvailable">是否取上架酒店</param>
        [HttpGet]
        [ProducesResponseType(typeof(List<GetCitiesOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetCitiese(bool? isCache, bool? needAvailable)
        {
            if (!isCache.HasValue) isCache = true;
            if (!needAvailable.HasValue) needAvailable = false;

            var result = await _hotelService.GetCities(HttpContext.GetTenantId(), isCache.Value, needAvailable.Value);
            return Ok(result);
        }

        /// <summary>
        /// 获取所有酒店
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(List<GetAllHotelOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetAllHotel()
        {
            var result = await _hotelService.GetAllHotel();
            return Ok(result);
        }


        /// <summary>
        /// 通过酒店Ids查询酒店基础设施
        /// </summary>
        /// <param name="hotelIds"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<HotelFacilities>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetFacilitiesByIds(List<long> input)
        {
            var result = await _hotelService.GetFacilitiesByIds(input);
            return Ok(result);
        }

        /// <summary>
        /// 通过酒店id查询酒店基础信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<Model.Hotel>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetHotelsModelByIds(List<long> input)
        {
            var result = await _hotelService.GetHotelsModelByIds(input);
            return Ok(result);
        }

        /// <summary>
        /// 查询房型图片
        /// </summary>
        /// <param name="roomIds"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<GetRoomPhotoOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetRoomPhoto(GetRoomPhotoInput input)
        {
            var result = await _hotelService.GetRoomPhoto(input);
            return Ok(result);
        }

        /// <summary>
        /// 设置房型显示状态
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> SetRoomViewable(SetHotelRoomEnabledInput input)
        {
            await _hotelService.SetRoomViewable(input);
            return Ok();
        }

        /// <summary>
        /// 批量获取酒店首图
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(IEnumerable<GetHotelFirstPhotoOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetHotelFirstPhoto(long[] ids)
        {
            var result = await _hotelService.GetHotelFirstPhoto(ids);
            return Ok(result);
        }

        /// <summary>
        /// 根据资源酒店id批量获取酒店首图
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(IEnumerable<GetHotelFirstPhotoOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetHotelFirstPhotoByResourceHotelIds(long[] ids)
        {
            var result = await _hotelService.GetHotelFirstPhoto(ids);
            return Ok(result);
        }

        [HttpPost]
        [ProducesResponseType(typeof(List<long>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetIncludeGroupRoomHotel(List<long> strategyIds)
        {
            var result = await _hotelService.GetIncludeGroupRoomHotel(strategyIds);
            return Ok(result);
        }

        /// <summary>
        /// 同步资源酒店关联酒店房型信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType((int)HttpStatusCode.OK)]
        public async Task<IActionResult> SyncHotelRooms(SyncHotelRoomsInput input)
        {
            await _hotelService.SyncHotelRooms(input);
            return Ok();
        }

        #region 分销商web

        /// <summary>
        /// 分销商web-酒店分页
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(PagingModel<AgencySearchOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> AgencySearch(AgencySearchInput input)
        {
            var result = await _hotelService.AgencySearch(input);
            return Ok(result);
        }

        #endregion

        #region OpenApi

        /// <summary>
        /// 通过房型Id列表查询房型列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<GetHotelRoomDetailsOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetRoomsByRoomIds(List<long> roomIds)
        {
            var result = await _hotelService.GetHotelRoomDetails(roomIds);
            return Ok(result);
        }

        /// <summary>
        /// 查询房型列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<GetHotelRoomDetailsOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetRoomDetails(GetHotelRoomDetailInput input)
        {
            var result = await _hotelService.GetHotelRoomDetails(input);
            return Ok(result);
        }

        #endregion


        /// <summary>
        /// 同步更新商户酒店信息
        /// </summary>
        /// <param name="receive"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> SyncHotelInfos(SyncHotelInfosInput receive)
        {
            await _apiHotelService.SyncApiHotelDetails(receive);
            await _hotelService.SyncHotelInfo(receive);
            return Ok();
        }

        /// <summary>
        /// 根据资源酒店id获取酒店id
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GetHotelIdsByResourceHotelIds(List<long> ids)
        {
            var result = await _hotelService.GetHotelIdsByResourceHotelIds(ids);
            return Ok(result);
        }

        /// <summary>
        /// 获取酒店扩展信息（基础扩展信息，酒店政策）
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(HotelPolicyOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> DetailPolicy(HotelPolicyDetailInput input)
        {
            var result = await _hotelService.DetailPolicy(input);
            return Ok(result);
        }

        /// <summary>
        /// 获取酒店房间加床政策
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<HotelRoomExtraBedPolicyOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetHotelRoomExtraBedPolicy(GetHotelRoomExtraBedPolicyInput input)
        {
            var result = await _hotelService.GetHotelRoomExtraBedPolicy(input);
            return Ok(result);
        }

        /// <summary>
        /// 根据资源酒店id获取酒店id
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(IEnumerable<GetHotelIdsByResourceHotelIdsOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetResourceHotelIdByHotelIds(List<long> ids)
        {
            var result = await _hotelService.GetResourceHotelIdByHotelIds(ids);
            return Ok(result);
        }
    }
}
