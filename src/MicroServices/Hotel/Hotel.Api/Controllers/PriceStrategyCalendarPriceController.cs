using AutoMapper;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Hotel.DTOs;
using Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Order.Enums;
using Contracts.Common.Tenant.Enums;
using Hotel.Api.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace Hotel.Api.Controllers
{
    [Route("[controller]/[action]")]
    [ApiController]
    public class PriceStrategyCalendarPriceController : ControllerBase
    {
        private readonly IPriceStrategyService _priceStrategyService;
        private readonly IPriceStrategyCalendarPriceService _priceStrategyCalendarPriceService;
        private readonly ISupplySettingService _supplySettingService;
        private readonly IHotelService _hotelService;
        private readonly IMapper _mapper;
        public PriceStrategyCalendarPriceController(
            IPriceStrategyService priceStrategyService,
            IPriceStrategyCalendarPriceService priceStrategyCalendarPriceService,
            ISupplySettingService supplySettingService,
            IHotelService hotelService,
            IMapper mapper)
        {
            _priceStrategyService = priceStrategyService;
            _priceStrategyCalendarPriceService = priceStrategyCalendarPriceService;
            _supplySettingService = supplySettingService;
            _hotelService = hotelService;
            _mapper = mapper;
        }
        /// <summary>
        /// 修改基础价格
        /// </summary>
        [HttpPost]
        [SwaggerResponseExt(default,
            ErrorTypes.Hotel.PriceStrategyInvalid,
            ErrorTypes.Hotel.ExistCalendarPriceWithoutCostPrice)]
        [SwaggerResponseExt(default,ErrorTypes.Common.NotSupportedOperation)]
        public async Task<IActionResult> UpdateBasisPrice(UpdateBasisPriceInput input)
        {
            await _priceStrategyCalendarPriceService.UpdateBasisPrice(input);
            return Ok();
        }

        /// <summary>
        /// 获取单个报价日历价格
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(GetByPriceStrategyOutput), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Inventory.GetInventoryFail)]
        public async Task<IActionResult> GetByPriceStrategy(GetByPriceStrategyInput input)
        {
            var result = await _priceStrategyCalendarPriceService.GetByPriceStrategy(input);
            return Ok(result);
        }

        /// <summary>
        /// 获取酒店日历价格
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(List<GetByHotelOutput>), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Inventory.GetInventoryFail)]
        public async Task<IActionResult> GetByHotel(GetByHotelInput input)
        {
            var result = await _priceStrategyCalendarPriceService.GetByHotel(input);
            return Ok(result);
        }

        /// <summary>
        /// 获取可售报价(微商城)
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(GetSaleByHotelOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetSaleByHotel(GetSaleByHotelInput input)
        {
            input.SalesChannel = SellingChannels.WechatMall;
            var result = await _priceStrategyCalendarPriceService.GetSaleByHotel(input);
            return Ok(result);
        }

        /// <summary>
        /// 通过酒店Id、价格策略Id、时间区间 获取价格，库存，房态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType((typeof(GetPriceInventoryStatusOutput)), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Inventory.GetInventoryFail)]
        public async Task<IActionResult> GetPriceInventoryStatus(
            GetPriceInventoryStatusInput input)
        {
            var result = await _priceStrategyCalendarPriceService.GetPriceInventoryStatus(input);
            return Ok(result);
        }

        /// <summary>
        /// 获取最低价
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(List<GetMinPriceOutput>), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Inventory.GetInventoryFail)]
        public async Task<IActionResult> GetMinPrice(GetMinPriceInput input)
        {
            var salePrices = await _priceStrategyCalendarPriceService.SalePriceStrategyPrice(input.HotelIds,
                input.BeginDate, input.EndDate, input.SalesChannel);

            var result = salePrices.GroupBy(a => a.HotelId)
                .Select(a => new GetMinPriceOutput
                {
                    HotelId = a.Key,
                    PriceStrategyId = a.MinBy(x => x.MinPrice).PriceStrategyId,
                    SaleCurrencyCode = a.MinBy(x => x.MinPrice).SaleCurrencyCode,
                    Price = a.Min(x => x.MinPrice)
                }).ToList();

            return Ok(result);
        }

        /// <summary>
        /// 获取酒店可售价格
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(List<GetHotelsSalePriceOutput>), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Inventory.GetInventoryFail)]
        public async Task<IActionResult> GetHotelsSalePrice(GetHotelsSalePriceInput input)
        {
            var salePrices = await _priceStrategyCalendarPriceService.SalePriceStrategyPrice(input.HotelIds,
                input.BeginDate, input.EndDate, input.SalesChannel);

            var result = salePrices.Select(a => new GetHotelsSalePriceOutput
            {
                HotelId = a.HotelId,
                PriceStrategyId = a.PriceStrategyId,
                Price = a.Price,
                SaleCurrencyCode = a.SaleCurrencyCode
            }).ToList();
            return Ok(result);
        }

        /// <summary>
        /// 获取价格策略指定日期所有售卖渠道价格
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(List<GetSaleChannelsPriceOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetSaleChannelsPrice(GetSaleChannelsPriceInput input)
        {
            var result = await _priceStrategyCalendarPriceService.GetSaleChannelsPrice(input);
            return Ok(result);
        }

        /// <summary>
        /// 检验价格策略是否可售-微商城
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(CheckSaleOutput), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Inventory.GetInventoryFail)]
        public async Task<IActionResult> CheckPriceStrategySaleWechatMall(CheckSaleInput input)
        {
            var tenantId = HttpContext.GetTenantId();
            input.SalesChannel = SellingChannels.WechatMall;
            var result = new CheckSaleOutput
            {
                Data = new CheckSaleData()
            };
            if (input.SupplierApiType == SupplierApiType.None)
            {//本地酒店价格策略校验
                var priceStrategyId = long.Parse(input.PriceStrategyId);
                var localHotelCheckResult = await _priceStrategyCalendarPriceService.CheckPriceStrategySale(
                    new CheckPriceStrategySaleInput
                    {
                        HotelId = input.HotelId,
                        PriceStrategyId = priceStrategyId,
                        SalesChannel = input.SalesChannel,
                        BeginDate = input.BeginDate,
                        EndDate = input.EndDate,
                        Quantity = input.Quantity
                    });

                result.Code = localHotelCheckResult.Code;
                result.Message = localHotelCheckResult.Message;
                result.Data.HotelId = localHotelCheckResult.Data.HotelId;
                result.Data.HotelName = localHotelCheckResult.Data.HotelName;
                result.Data.HotelEnName = localHotelCheckResult.Data.HotelEnName;
                result.Data.OperatingModel = localHotelCheckResult.Data.OperatingModel;
                result.Data.IsAutoConfirmRoomStatus = localHotelCheckResult.Data.IsAutoConfirmRoomStatus;
                result.Data.Room = _mapper.Map<CheckSale_Room>(localHotelCheckResult.Data.Room);
                result.Data.PriceStrategy = _mapper.Map<CheckSale_PriceStrategy>(localHotelCheckResult.Data.PriceStrategy);
                if (result.Data.PriceStrategy?.NumberOfBreakfast is > 0)
                {
                    result.Data.PriceStrategy.BoardCodeType = Contracts.Common.Resource.Enums.BoardCodeType.BB;
                    result.Data.PriceStrategy.BoardCount = result.Data.PriceStrategy.NumberOfBreakfast;
                }
            }
            else
            {
                result = await _priceStrategyCalendarPriceService.CheckSaleByThirdPartyHotel(input,tenantId);
            }

            result.Data?.PriceStrategy?.DatePrice.ForEach(x => x.Cost = null);

            return Ok(result);
        }

        /// <summary>
        /// 检验价格策略是否可售
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(CheckPriceStrategySaleOutput), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Inventory.GetInventoryFail)]
        public async Task<IActionResult> CheckPriceStrategySaleHasCost(CheckPriceStrategySaleInput input)
        {
            var result = await _priceStrategyCalendarPriceService.CheckPriceStrategySale(input);
            return Ok(result);
        }

        /// <summary>
        /// 下单前校验酒店价格策略(本地酒店和第三方酒店)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(CheckSaleOutput), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Inventory.GetInventoryFail)]
        public async Task<IActionResult> PreOrderCheckSale(CheckSaleInput input)
        {
            var tenantId = HttpContext.GetTenantId();

            if (input.SalesChannel is SellingChannels.B2b)
                input.SalesChannel = null;

            CheckSaleOutput? result;
            if (input.SupplierApiType == SupplierApiType.None)
            {
                //本地酒店价格策略校验
                result = await _priceStrategyCalendarPriceService.CheckSaleByLocalHotel(input);
            }
            else
            {
                result = await _priceStrategyCalendarPriceService.CheckSaleByThirdPartyHotel(input, tenantId);
            }
            return Ok(result);
        }

        [HttpPost]
        [ProducesResponseType(typeof(List<GetOTAPriceOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetOTAPrice(GetOTAPriceInput input)
        {
            //获取可售价格策略
            var resourceHotelIds = new List<long>() { input.ResourceHotelId };
            var priceStrategies = await _priceStrategyService.GetByResourceHotelIds(resourceHotelIds);
            if (input.ResultFormat == 0)
            {
                //移除不符合连住要求的价格计划
                priceStrategies.RemoveAll(x => x.PriceStrategyType == PriceStrategyType.StayLongDiscount && x.NumberOfNights > input.StayNight);
            }
            if (!priceStrategies.Any())
                return Ok(new List<GetOTAPriceOutput>());

            var result = await _priceStrategyCalendarPriceService.GetOTAPrice(priceStrategies, input.BeginDate, input.EndDate);
            if (input.ResultFormat == 0)
            {
                //移除无价格库存的价格计划
                result.RemoveAll(x => x.DatePrices.Any(w => !w.Status || w.Quantity == 0 || w.Price == 0));
            }
            return Ok(result);
        }

        /// <summary>
        /// 手工单价格策略库存校验
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(CheckSaleOutput), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Inventory.GetInventoryFail)]
        public async Task<IActionResult> CheckPriceStrategySaleByManual(
            CheckSaleInput input)
        {
            var tenantId = HttpContext.GetTenantId();
            CheckSaleOutput result;
            if (input.SupplierApiType == SupplierApiType.None)
            {//本地酒店手工单库存校验
                result = await _priceStrategyCalendarPriceService.CheckPriceStrategySaleByManual(input);
            }
            else
            {//第三方酒店库存校验
                result = await _priceStrategyCalendarPriceService.CheckSaleByThirdPartyHotel(input,tenantId);
            }
            return Ok(result);
        }

        #region 供应商

        /// <summary>
        /// 供应商修改基础价格
        /// </summary>
        [HttpPost, ActionName("supplier/updatebasisprice")]
        [ProducesResponseType((int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default,
            ErrorTypes.Hotel.PriceStrategyInvalid,
            ErrorTypes.Hotel.ExistCalendarPriceWithoutCostPrice)]
        [SwaggerResponseExt(default,ErrorTypes.Common.NotSupportedOperation)]
        public async Task<IActionResult> UpdateBasisPriceBySupplier(UpdateBasisPriceInput input)
        {
            var supplierId = HttpContext.GetCurrentUser().GetSupplierId();
            //供应商有运营权正常操作,无营运权则基础售价不可填写，但会根据供应商供货管理中配置的默认基础售价进行修改
            var hasOperationRight = await _supplySettingService.HasOperationRight(supplierId);
            input.HasOperationRight = hasOperationRight;
            await _priceStrategyCalendarPriceService.UpdateBasisPrice(input);
            return Ok();
        }


        /// <summary>
        /// 供应商获取单个报价日历价格
        /// </summary>
        [HttpPost, ActionName("supplier/get")]
        [ProducesResponseType(typeof(GetByPriceStrategyOutput), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Inventory.GetInventoryFail)]
        public async Task<IActionResult> GetBySupplierPriceStrategy(GetByPriceStrategyInput input)
        {
            var supplierId = HttpContext.GetCurrentUser().GetSupplierId();
            var result = await _priceStrategyCalendarPriceService.GetByPriceStrategy(input);

            //供应商无运营权页面上不展示基础售价、渠道加价
            var hasOperationRight = await _supplySettingService.HasOperationRight(supplierId);
            if (hasOperationRight)
                return Ok(result);

            foreach (var item in result.Prices)
            {
                item.ChannelPrice = null;
            }

            return Ok(result);
        }



        /// <summary>
        /// 供应商获取酒店日历价格
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost, ActionName("supplier/getbyhotel")]
        [ProducesResponseType(typeof(List<GetByHotelOutput>), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Inventory.GetInventoryFail)]
        public async Task<IActionResult> GetBySupplierHotel(GetByHotelInput input)
        {
            input.SupplierId = HttpContext.GetCurrentUser().GetSupplierId();
            var result = await _priceStrategyCalendarPriceService.GetByHotel(input);
            if (result.Any() is false) return Ok(result);

            //供应商无运营权页面上不展示基础售价、渠道加价
            var hasOperationRight = await _supplySettingService.HasOperationRight(input.SupplierId.Value);
            if (hasOperationRight)
                return Ok(result);

            foreach (var price in from item in result
                                  from priceStrategy in item.PriceStrategies
                                  from price in priceStrategy.Prices
                                  select price)
            {
                price.ChannelPrice = null;
            }

            return Ok(result);
        }


        /// <summary>
        /// 获取价格策略指定日期所有售卖渠道价格
        /// </summary>
        [HttpPost, ActionName("supplier/getsalechannelsprice")]
        [ProducesResponseType(typeof(List<GetSaleChannelsPriceOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetSaleChannelsPriceBySupplier(GetSaleChannelsPriceInput input)
        {
            var supplierId = HttpContext.GetCurrentUser().GetSupplierId();
            var result = await _priceStrategyCalendarPriceService.GetSaleChannelsPrice(input);
            if (result.Any() is false)
                return Ok(result);

            //供应商无运营权页面上不展示基础售价、渠道加价
            var hasOperationRight = await _supplySettingService.HasOperationRight(supplierId);
            if (hasOperationRight)
                return Ok(result);

            foreach (var item in result)
            {
                item.Price = null;
            }

            return Ok(result);
        }

        #endregion

        /// <summary>
        /// 获取30天最高最低价
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(List<GetRecentPriceOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetRecentMinOrMaxPrice(
            (List<long> hotelIds, RecentPriceType recentPriceType) input)
        {
            var result =
                await _priceStrategyCalendarPriceService.GetRecentMinOrMaxPrice(input.hotelIds, input.recentPriceType);
            return Ok(result);
        }

        #region 分销商web

        /// <summary>
        /// 酒店详情策略
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<AgencyGetOutput>), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default,
            ErrorTypes.Hotel.PricesNotEnough,
            ErrorTypes.Hotel.EndRoomNotBook,
            ErrorTypes.Hotel.HourRoomNotBook,
            ErrorTypes.Hotel.StayLongDiscountNotBook,
            ErrorTypes.Hotel.CalendarNotEnable)]
        [SwaggerResponseExt(default,ErrorTypes.Inventory.GetInventoryFail)]
        public async Task<IActionResult> AgencyGet(AgencyGetInput input)
        {
            var result = await _priceStrategyCalendarPriceService.AgencyGet(input);
            return Ok(result);
        }

        #endregion

        /// <summary>
        /// 日历酒店的价格策略按可预订最近日期
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetSaleableOutput> GetSaleable(GetSaleableInput input)
        {
            var result = await _priceStrategyCalendarPriceService.GetSaleable(input);
            return result;
        }

    }
}
