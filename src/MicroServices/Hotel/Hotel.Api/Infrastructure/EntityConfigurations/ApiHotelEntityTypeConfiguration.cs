using Contracts.Common.Resource.Enums;
using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Hotel.Api.Infrastructure.EntityConfigurations;

public class ApiHotelEntityTypeConfiguration : KeyBaseConfiguration<ApiHotel>, IEntityTypeConfiguration<ApiHotel>
{
    public void Configure(EntityTypeBuilder<ApiHotel> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.ResourceHotelId)
            .HasColumnType("bigint");

        builder.Property(s => s.HopId)
            .HasColumnType("int");

        builder.Property(s => s.StarLevel)
            .HasColumnType("decimal(2,1)");

        builder.Property(s => s.ZHName)
            .HasColumnType("varchar(100)")
            .IsRequired();

        builder.Property(s => s.ENName)
            .HasColumnType("varchar(200)");

        builder.Property(s => s.CountryCode)
            .HasColumnType("int");

        builder.Property(s => s.CountryName)
            .HasColumnType("varchar(50)")
            .IsRequired();

        builder.Property(s => s.ProvinceCode)
            .HasColumnType("int");

        builder.Property(s => s.ProvinceName)
            .HasColumnType("varchar(50)")
            .IsRequired();

        builder.Property(s => s.CityCode)
            .HasColumnType("int");

        builder.Property(s => s.CityName)
            .HasColumnType("varchar(50)")
            .IsRequired();

        builder.Property(s => s.Location)
            .HasColumnType("point");

        builder.Property(s => s.StaffTag)
            .HasColumnType("tinyint(1)");

        builder.Property(s => s.ReunionRoom)
            .HasDefaultValue(0)
            .HasColumnType("int");

        builder.Property(s => s.CreateTime)
            .HasColumnType("datetime");

        builder.Property(s => s.UpdateTime)
            .HasColumnType("datetime");

        builder.Property(s => s.CoordinateType)
            .HasColumnType("int")
            .HasDefaultValue(CoordinateType.BD09);

        builder.Property(s => s.Tags)
            .HasColumnType("int");

        builder.Property(s => s.SaleFlag)
            .HasColumnType("tinyint(1)");
        
        builder.Property(s => s.Address)
            .HasColumnType("varchar(200)")
            .IsRequired();

        builder.Property(s => s.ENAddress)
            .HasColumnType("varchar(200)");

        //索引
        builder.HasIndex(s => s.ResourceHotelId).IsUnique();
    }
}
