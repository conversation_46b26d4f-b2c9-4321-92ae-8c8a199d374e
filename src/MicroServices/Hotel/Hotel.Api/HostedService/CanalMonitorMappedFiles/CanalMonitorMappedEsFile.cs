using Contracts.Common.Resource.Enums;
using Nest;

namespace Hotel.Api.HostedService.CanalMonitorMappedFiles;

/// <summary>
/// canal监听数据数据表到Es的映射文件
/// <value>处理es文档字段定义与数据库字段的出入过大的情况</value>
/// </summary>
public class CanalMonitorMappedEsFile
{
    
}

/// <summary>
/// 资源国家
/// <value>对应数据库表的字段名</value>
/// </summary>
public class CanalMonitorMappedCountryFile
{
    public long Id { get; set; }
    
    /// <summary>
    /// 国家编码
    /// </summary>
    public int CountryCode { get; set; }

    /// <summary>
    /// 国家中文名称
    /// </summary>
    public string ZHName { get; set; }

    /// <summary>
    /// 国家英文名称
    /// </summary>
    public string ENName { get; set; }
}

/// <summary>
/// 资源省份
/// <value>对应数据库表的字段名</value>
/// </summary>
public class CanalMonitorMappedProvinceFile
{
    public long Id { get; set; }
    
    /// <summary>
    /// 国家编码
    /// </summary>
    public int CountryCode { get; set; }

    /// <summary>
    /// 省份编码
    /// </summary>
    public int ProvinceCode { get; set; }

    /// <summary>
    /// 省份名称
    /// </summary>
    public string ZHName { get; set; }

    /// <summary>
    /// 省份英文名称
    /// </summary>
    public string ENName { get; set; }
}

/// <summary>
/// 资源城市
/// <value>对应数据库表的字段名</value>
/// </summary>
public class CanalMonitorMappedCityFile
{
    public long Id { get; set; }
    
    /// <summary>
    /// 国家编码
    /// </summary>
    public int CountryCode { get; set; }

    /// <summary>
    /// 省份编码
    /// </summary>
    public int ProvinceCode { get; set; }

    /// <summary>
    /// 城市编码
    /// </summary>
    public int CityCode { get; set; }

    /// <summary>
    /// 城市中文名称
    /// </summary>
    public string ZHName { get; set; }

    /// <summary>
    /// 城市英文名称
    /// </summary>
    public string ENName { get; set; }
    
    /// <summary>
    /// 地理坐标点
    /// </summary>
    public GeoLocation? Location { get; set; }
    
    /// <summary>
    /// Google位置Id
    /// </summary>
    public string? GooglePalceId { get; set; }
}

/// <summary>
/// 资源机场
/// <value>对应数据库表的字段名</value>
/// </summary>
public class CanalMonitorMappedAirportFile
{
    public long Id { get; set; }
    
    public int CountryCode { get; set; }

    public int CityCode { get; set; }
    
    /// <summary>
    /// 三字码
    /// </summary>
    public string IATA { get; set; }

    /// <summary>
    /// 四字码
    /// </summary>
    public string ICAO { get; set; }
    
    /// <summary>
    /// 机场名称 - 中文
    /// </summary>
    public string AirportZHName { get; set; }

    /// <summary>
    /// 机场名称 - 英文
    /// </summary>
    public string AirportENName { get; set; }
    
    /// <summary>
    /// 地理坐标点
    /// </summary>
    public GeoLocation Location { get; set; }
    
    /// <summary>
    /// Google位置Id
    /// </summary>
    public string GooglePalceId { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string Address { get; set; }
}

/// <summary>
/// 租户商圈数据
/// <value>对应数据库表的字段名</value>
/// </summary>
public class CanalMonitorMappedTradingAreaFile
{
    /// <summary>
    /// id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 城市code
    /// </summary>
    public int CityCode { get; set; }

    /// <summary>
    /// 商圈类型
    /// </summary>
    public TradingAreaType TradingAreaType { get; set; }

    /// <summary>
    /// 商圈名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 商圈地址
    /// </summary>
    public string Address { get; set; }
    
    /// <summary>
    /// 地理坐标点
    /// </summary>
    public GeoLocation Location { get; set; }
}

/// <summary>
/// 租户景区数据
/// <value>对应数据库表的字段名</value>
/// </summary>
public class CanalMonitorMappedScenicSpotFile
{
    public long Id { get; set; }
    
    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; }
    
    /// <summary>
    /// 英文名
    /// </summary>
    public string ENName { get; set; }
    
    /// <summary>
    /// 国家
    /// </summary>
    public int CountryCode { get; set; }

    public string CountryName { get; set; }

    /// <summary>
    /// 省
    /// </summary>
    public int ProvinceCode { get; set; }

    public string ProvinceName { get; set; }

    /// <summary>
    /// 市
    /// </summary>
    public int CityCode { get; set; }

    public string CityName { get; set; }
    
    /// <summary>
    /// 地理坐标点
    /// </summary>
    public GeoLocation Location { get; set; }
    
    /// <summary>
    /// 是否上架
    /// </summary>
    public bool Enabled { get; set; }
    
    /// <summary>
    /// 详细地址
    /// </summary>
    public string Address { get; set; }
}

/// <summary>
/// 租户旅游线路数据
/// <value>对应数据库表的字段名</value>
/// </summary>
public class CanalMonitorMappedTravelLineFile
{
    public long Id { get; set; }
    
    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; }
    
    /// <summary>
    /// 目的地国家
    /// </summary>
    public int DestinationCountryId { get; set; }
    public string? DestinationCountryName { get; set; }
    
    /// <summary>
    /// 目的地城市Id
    /// </summary>
    public int DestinationCityId { get; set; }

    /// <summary>
    /// 目的地城市名称
    /// </summary>
    public string? DestinationCityName { get; set; }
    
    /// <summary>
    /// 出发国家
    /// </summary>
    public int DepartureCountryId { get; set; }
    public string? DepartureCountryName { get; set; }
    
    /// <summary>
    /// 出发地城市Id
    /// </summary>
    public int DepartureCityId { get; set; }

    /// <summary>
    /// 出发地城市名称
    /// </summary>
    public string? DepartureCityName { get; set; }

    /// <summary>
    /// 上架/下架
    /// </summary>
    public bool Enabled { get; set; }
    
    /// <summary>
    /// 是否已删除
    /// </summary>
    public bool IsDeleted { get; set; }
}

/// <summary>
/// 租户用车数据
/// <value>对应数据库表的字段名</value>
/// </summary>
public class CanalMonitorMappedCarProductFile
{
    public long Id { get; set; }
    
    /// <summary>
    /// 产品标题
    /// </summary>
    public string Title { get; set; }
    
    /// <summary>
    /// 国家
    /// </summary>
    public int CountryCode { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    public string CountryName { get; set; }

    /// <summary>
    /// 城市 接送机为机场所在城市
    /// </summary>
    public int CityCode { get; set; }

    public string CityName { get; set; }
    
    /// <summary>
    /// 上架/下架
    /// </summary>
    public bool Enabled { get; set; }
}

/// <summary>
/// 资源 - HopOpenHotel
/// </summary>
public class CanalMonitorMappedHopOpenHotelFile
{
    /// <summary>
    /// hopId
    /// </summary>
    public int HopId { get; set; }
    
    /// <summary>
    /// 0-非主推  1-主推
    /// </summary>
    public int StaffTag { get; set; }

    /// <summary>
    /// 团房数量
    /// </summary>
    public int ReunionRoom { get; set; }

    /// <summary>
    /// 敏感度标签 英文逗号分隔
    /// </summary>
    public string? Tags { get; set; }
}

/// <summary>
/// api hotel tenant config
/// </summary>
public class CanalMonitorMappedApiHotelTenantConfigFile
{
    /// <summary>
    /// api hotel id
    /// </summary>
    public long ApiHotelId { get; set; }
    
    /// <summary>
    /// 第三方资源酒店ID
    /// </summary>
    public long ResourceHotelId { get; set; }
    
    /// <summary>
    /// 是否置顶
    /// </summary>
    public bool OnTop { get; set; }

    /// <summary>
    /// 权重值
    /// </summary>
    public long WeightValue { get; set; }

    /// <summary>
    /// 租户id
    /// </summary>
    public long TenantId { get; set; }
}


public class CanalMonitorMappedApiHotelEsFile
{ 
    /// <summary>
    /// apiHotelId
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// resourceHotel id
    /// </summary>
    public long ResourceHotelId { get; set; }
    
    /// <summary>
    /// 0-非主推  1-主推
    /// </summary>
    public bool StaffTag { get; set; }
    
    /// <summary>
    /// 团房数量
    /// </summary>
    public int ReunionRoom { get; set; }

    /// <summary>
    /// 售卖标签
    /// </summary>
    public List<SellHotelTag> Tags { get; set; } = new();
}

/// <summary>
/// 资源酒店基础设施
/// </summary>
public class CanalMonitorResourceHotelFacilitiesMappedEsFile
{ 
    /// <summary>
    /// 酒店Id
    /// </summary>
    public long HotelId { get; set; }

    /// <summary>
    /// 设施Id
    /// </summary>
    public long FacilityId { get; set; }
}

/// <summary>
/// 资源-景区数据
/// <value>对应数据库表的字段名</value>
/// </summary>
public class CanalMonitorMappedResourceScenicSpotFile
{
    public long Id { get; set; }
    
    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; }
    
    /// <summary>
    /// 英文名
    /// </summary>
    public string ENName { get; set; }
    
    /// <summary>
    /// 国家
    /// </summary>
    public int CountryCode { get; set; }

    public string CountryName { get; set; }

    /// <summary>
    /// 省
    /// </summary>
    public int ProvinceCode { get; set; }

    public string ProvinceName { get; set; }

    /// <summary>
    /// 市
    /// </summary>
    public int CityCode { get; set; }

    public string CityName { get; set; }
    
    /// <summary>
    /// 地理坐标点
    /// </summary>
    public GeoLocation Location { get; set; }
    
    /// <summary>
    /// 详细地址
    /// </summary>
    public string Address { get; set; }
}