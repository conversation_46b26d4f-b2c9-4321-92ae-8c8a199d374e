using AutoMapper;
using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Hotel.EsDocuments;
using Contracts.Common.Product.Enums;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Tenant.DTOs.TenantApiSupplierConfig;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.Abstract;
using Hotel.Api.EsDocument;
using Hotel.Api.Services.Interfaces;
using Nest;

namespace Hotel.Api.Services;

/// <summary>
/// es酒店
/// <value>汇智酒店移除租户概念.</value>
/// </summary>
public class EsHotelV2Service : IEsHotelV2Service
{
    private readonly IBaseHotelService _baseHotelService;
    private readonly IElasticClient _elasticClient;
    private readonly IMapper _mapper;
    
    private const string _resourceHotelIndexNameV2 = "resource-hotel-v2"; // 资源酒店索引名称
    private const string _tenantHotelIndexNamePrefix = "hotel-";
    private const string _tenantChannelPriceSettingIndexNamePrefix = "channelpricesetting-";
    
    public EsHotelV2Service(
        IBaseHotelService baseHotelService,
        IElasticClient elasticClient,
        IMapper mapper)
    {
        _baseHotelService = baseHotelService;
        _elasticClient = elasticClient;
        _mapper = mapper;
    }

    public async Task<PagingModel<SearchEsHotelOutput>> Search(EsHotelSearchInput input, long tenantId)
    {
        var result = new PagingModel<SearchEsHotelOutput>();
        var huiZhiSpecifiedHotelSettings = await CovHuizhiSpecifiedHotelSettingData(input,tenantId); // 查询汇智配置
        var localHotelDocs = await GetLocalHotel(tenantId,input.AgencyLocalHotelId.ToArray()); // 查询本地酒店
        var localHotelRelatedResourceId = new List<long>();
        if (input.AgencyLocalHotelId.Any())
        {
            localHotelRelatedResourceId = localHotelDocs.Select(x => x.ResourceHotelId).ToList();
        }
        var saasHopHotelConfig = await _baseHotelService.GetTenantSaasApiSupplierConfig(new GetTenantApiSupplierConfigDetailInput
        {
            TenantId = tenantId,
            SupplierApiType = SupplierApiType.Hop
        });
        var isQueryApiHotel = input.PriceGroupId > 0 ? input.AgencyThirdHotel : true; // 是否支持查询api酒店
        if (saasHopHotelConfig.Enabled is false || (input.SupplierApiType.HasValue && input.SupplierApiType != SupplierApiType.Hop))
        {
            isQueryApiHotel = false;
            input.AgencyThirdHotelId = new List<long>();
        }
        // 汇智酒店禁用,并且无指定查询api酒店,允许查询本地酒店(非价格分组查询 或者 价格分组配置了本地酒店数据)
        if (input.SupplierApiType is SupplierApiType.None || isQueryApiHotel is false)
        {
            // 只查询本地酒店
            if (input.PriceGroupId <= 0 || (input.PriceGroupId > 0 && input.AgencyLocalHotelId.Any()))
                localHotelRelatedResourceId = localHotelDocs.Select(x => x.ResourceHotelId).ToList();
        }
        if (localHotelRelatedResourceId.Any() is false && isQueryApiHotel is false)
            return result;
        
        var filterResourceIds = new List<long>();
        //汇智指定酒店id过滤
        if (isQueryApiHotel && input.AgencyThirdHotelId.Any())
        {
            //赋值到HotelIds字段进行统一的酒店id查询过滤
            input.HotelIds.AddRange(input.AgencyThirdHotelId);
            
            if(localHotelRelatedResourceId.Any())
                filterResourceIds.AddRange(localHotelRelatedResourceId);
        }
        if (input.HotelIds.Any()) // 混合酒店id过滤
        {
            var mixedHotelIds = input.HotelIds.ToArray(); 
            var mixHotelDocs = await GetMixHotel(mixedHotelIds,tenantId); // 混合酒店id换取资源酒店id
            var mixHotelRelatedResourceId = mixHotelDocs.Select(x => x.Id).Distinct().ToList();
            filterResourceIds.AddRange(mixHotelRelatedResourceId);
        }

        if (saasHopHotelConfig is { Enabled: true, IsAllHotel: false })
        {
            // saas 配置指定酒店 过滤
            if(localHotelRelatedResourceId.Any())
                filterResourceIds.AddRange(localHotelRelatedResourceId);
            
            filterResourceIds.AddRange(saasHopHotelConfig.HotelIds);
        }
        filterResourceIds = filterResourceIds.Where(x => x > 0).Distinct().ToList();

        #region shouldClauses

        var shouldClauses = new List<Func<QueryContainerDescriptor<ResourceHotelDocumentV2>, QueryContainer>>();
        //本地酒店过滤should
        if (localHotelRelatedResourceId.Any())
        {
            var localHotelFilterShouldClauses = new Func<QueryContainerDescriptor<ResourceHotelDocumentV2>, QueryContainer>(
                sq => sq.Bool(ib => ib
                    .Must(sm =>
                    {
                        //本地酒店过滤
                        var conditions =
                            new List<Func<QueryContainerDescriptor<ResourceHotelDocumentV2>, QueryContainer>>();
                        conditions.Add(c => c
                            .Terms(t => t
                                .Field(f => f.Id)
                                .Terms(localHotelRelatedResourceId)));
                        return sm.Bool(bd => bd.Must(conditions));
                    })));
            shouldClauses.Add(localHotelFilterShouldClauses);
        }
        
        // 汇智酒店全局过滤
        if (isQueryApiHotel)
        {
            var hopHotelFilterShouldClauses = new Func<QueryContainerDescriptor<ResourceHotelDocumentV2>, QueryContainer>(
                sq => sq.Bool(ib=>ib
                    .Must(sm =>
                    {
                        var conditions =
                            new List<Func<QueryContainerDescriptor<ResourceHotelDocumentV2>, QueryContainer>>();

                        conditions.Add(c => c
                            .Range(r => r
                                .Field(f => f.ApiHotelId)
                                .GreaterThan(0)));
                        
                        //默认酒店不配置则不过滤
                        if (input.DefaultIsStaffTag || input.DefaultTags.Any())
                        {
                            if (input.ShowOnlyGroupRoom)
                            {
                                //团房策略数量过滤
                                conditions.Add(qc => qc
                                    .Range(r => r
                                        .Field(f => f.ReunionRoom)
                                        .GreaterThan(0)));
                            }

                            conditions.Add(c => c
                                .Bool(b => b
                                    .Should(
                                        s => s.Bool(bb => bb
                                            .Must(m =>
                                            {
                                                //主推标签过滤
                                                if (!input.DefaultIsStaffTag || input.DefaultIsReunionRoomTag)
                                                    return null;

                                                return m.Term(t => t
                                                    .Field(f => f.StaffTag)
                                                    .Value(1));
                                            })),
                                        s => s.Bool(bb => bb
                                            .Must(m =>
                                            {
                                                //主推团房标签过滤
                                                if (!input.DefaultIsReunionRoomTag)
                                                    return null;

                                                var reunionRoomConditions =
                                                    new List<Func<QueryContainerDescriptor<ResourceHotelDocumentV2>,
                                                        QueryContainer>>();
                                                reunionRoomConditions.Add(r => r
                                                    .Term(t => t
                                                        .Field(f => f.StaffTag)
                                                        .Value(1)));
                                                reunionRoomConditions.Add(r => r
                                                    .Range(r => r
                                                        .Field(f => f.ReunionRoom)
                                                        .GreaterThan(0)));
                                                return m.Bool(b => b.Must(reunionRoomConditions));
                                            })),
                                        s => s.Bool(bb => bb
                                            .Must(m =>
                                            {
                                                //售卖标签过滤
                                                if (input.DefaultTags.Any() is false)
                                                    return null;

                                                return m.Terms(t => t
                                                    .Field(f => f.Tags)
                                                    .Terms(input.DefaultTags));
                                            }))
                                    )));
                        }
                        
                        return sm.Bool(bd => bd.Must(conditions));
                    })));
            shouldClauses.Add(hopHotelFilterShouldClauses);

            if (huiZhiSpecifiedHotelSettings.Any())
            {
                var huiZhiSpecifiedHotelShouldClauses = huiZhiSpecifiedHotelSettings.Select(x =>
                        (Func<QueryContainerDescriptor<ResourceHotelDocumentV2>, QueryContainer>)(s => s
                            .Bool(b => b
                                .Must(m =>
                                {
                                    var conditions =
                                        new List<Func<QueryContainerDescriptor<ResourceHotelDocumentV2>, QueryContainer>>();

                                    conditions.Add(c => c
                                        .Term(t => t
                                            .Field(f => f.ApiHotelId)
                                            .Value(x.ApiHotelId)));

                                        if (input.ShowOnlyGroupRoom)
                                        {
                                            //团房策略数量过滤
                                            conditions.Add(qc => qc
                                                .Range(r => r
                                                    .Field(f => f.ReunionRoom)
                                                    .GreaterThan(0)));
                                        }

                                        var isStaffTag = x.IsStaffTag;
                                        var tags = x.Tags;
                                        var isReunionRoomTag = x.IsReunionRoomTag;
                                        if (isStaffTag || tags.Any())
                                        {
                                            conditions.Add(c => c
                                                .Bool(b => b
                                                    .Should(
                                                        s => s.Bool(bb => bb
                                                            .Must(m =>
                                                            {
                                                                //主推标签过滤
                                                                if (!isStaffTag || isReunionRoomTag)
                                                                    return null;

                                                                return m.Term(t => t
                                                                    .Field(f => f.StaffTag)
                                                                    .Value(1));
                                                            })),
                                                        s => s.Bool(bb => bb
                                                            .Must(m =>
                                                            {
                                                                //主推团房标签过滤
                                                                if (!isReunionRoomTag)
                                                                    return null;

                                                                var reunionRoomConditions =
                                                                    new List<Func<QueryContainerDescriptor<
                                                                            ResourceHotelDocumentV2>,
                                                                        QueryContainer>>();
                                                                reunionRoomConditions.Add(r => r
                                                                    .Term(t => t
                                                                        .Field(f => f.StaffTag)
                                                                        .Value(1)));
                                                                reunionRoomConditions.Add(r => r
                                                                    .Range(r => r
                                                                        .Field(f => f.ReunionRoom)
                                                                        .GreaterThan(0)));
                                                                return m.Bool(b => b.Must(reunionRoomConditions));
                                                            })),
                                                        s => s.Bool(bb => bb
                                                            .Must(m =>
                                                            {
                                                                //售卖标签过滤
                                                                if (tags.Any() is false)
                                                                    return null;

                                                                return m.Terms(t => t
                                                                    .Field(f => f.Tags)
                                                                    .Terms(tags));
                                                            }))
                                                    )));
                                        }

                                    return m.Bool(b => b.Must(conditions));
                                }))
                        ))
                    .ToList();
                shouldClauses.AddRange(huiZhiSpecifiedHotelShouldClauses);
            }
        }

        #endregion
        
        var distanceScriptFieldName = "distance_to_location";
        var searchDescriptor = new SearchDescriptor<ResourceHotelDocumentV2>()
            .Index(_resourceHotelIndexNameV2)
            .Source()
            .Query(q => q
                .Bool(b => b
                    .Must(m =>
                    {
                        var conditions = new List<Func<QueryContainerDescriptor<ResourceHotelDocumentV2>, QueryContainer>>();

                        // 创建主要查询条件
                        var mainConditions = new List<Func<QueryContainerDescriptor<ResourceHotelDocumentV2>, QueryContainer>>();

                        if (!string.IsNullOrEmpty(input.KeyWord))
                        {
                            // 多场景查询匹配
                            mainConditions.Add(q => q
                                .DisMax(d => d
                                    .Queries(
                                        qz => qz.Bool(qb => qb
                                            .Should(
                                                // `zhName`精准匹配 
                                                s => s.MatchPhrase(sm => sm
                                                    .Field(sf => sf.ZHName)
                                                    .Query(input.KeyWord))

                                                // 跨字段匹配 `CrossFields` 
                                                , s => s.MultiMatch(sm => sm
                                                    .Type(TextQueryType.CrossFields)
                                                    .Operator(Operator.And)
                                                    .Fields(sf => sf
                                                        .Field(ff => ff.ZHName)
                                                        .Field(ff => ff.CityName))
                                                    .Query(input.KeyWord))

                                                // 模糊匹配兜底
                                                , s => s.Match(sm => sm
                                                    .Field(sf => sf.ZHName)
                                                    .Query(input.KeyWord))
                                            )
                                        ),
                                        qe => qe.Bool(qb => qb
                                            .Should(
                                                // `eNName`精准匹配 
                                                s => s.MatchPhrase(sm => sm
                                                    .Field(sf => sf.ENName)
                                                    .Query(input.KeyWord))

                                                // 跨字段匹配 `CrossFields` 
                                                , s => s.MultiMatch(sm => sm
                                                    .Type(TextQueryType.CrossFields)
                                                    .Operator(Operator.And)
                                                    .Fields(sf => sf
                                                        .Field(ff => ff.ENName)
                                                        .Field(ff => ff.CityName))
                                                    .Query(input.KeyWord))

                                                // 模糊匹配兜底
                                                , s => s.Match(sm => sm
                                                    .Field(sf => sf.ENName)
                                                    .Query(input.KeyWord))
                                            )
                                        )
                                    )));
                        }

                        if (input.CityCode > 0)
                        {
                            mainConditions.Add(qc => qc
                                .Term(qm => qm
                                    .Field(f => f.CityCode)
                                    .Value(input.CityCode)));
                        }

                        if (input.StarLevel > 0)
                        {
                            mainConditions.Add(qc => qc
                                .Term(qm => qm
                                    .Field(f => f.StarLevel)
                                    .Value(input.StarLevel)));
                        }

                        if (filterResourceIds.Any())
                        {
                            mainConditions.Add(qc => qc
                                .Terms(t => t
                                    .Field(f => f.Id)
                                    .Terms(filterResourceIds)));
                        }

                        if (input.FacilityIds.Any())
                        {
                            mainConditions.Add(qc=>qc
                                .Terms(t=>t
                                    .Field(f=>f.FacilityId)
                                    .Terms(input.FacilityIds)));
                        }

                        // should：主要查询条件 OR 距离过滤
                        var shouldConditions = new List<Func<QueryContainerDescriptor<ResourceHotelDocumentV2>, QueryContainer>>();
                        
                        // 主要查询条件
                        if (mainConditions.Any())
                        {
                            shouldConditions.Add(q => q.Bool(bq => bq.Must(mainConditions)));
                        }
                        
                        // 距离过滤条件（与主要查询条件形成OR关系）
                        if (input is {TargetLatitude: not null, TargetLongitude: not null, TargetDistance: > 0})
                        {
                            shouldConditions.Add(qc => qc
                                .GeoDistance(g => g
                                    .Field(f => f.Location)
                                    .Distance($"{input.TargetDistance}km")
                                    .Location(input.TargetLatitude.Value,
                                        input.TargetLongitude.Value)));
                        }

                        // 如果有should条件，使用should；否则使用must
                        if (shouldConditions.Count > 1)
                        {
                            conditions.Add(q => q.Bool(bq => bq.Should(shouldConditions)));
                        }
                        else if (shouldConditions.Count == 1)
                        {
                            conditions.AddRange(shouldConditions);
                        }

                        return m.Bool(sq => sq.Must(conditions));
                    })
                    .Filter(ft => ft
                        .Bool(fb => fb
                            .Should(shouldClauses.ToArray())))
                ))
            .ScriptFields(sf => sf
                .ScriptField(distanceScriptFieldName, s => s
                    .Source(
                        "if(params.lat != null && params.lon !=null) {doc['location'].arcDistance(params.lat,params.lon)}")
                    .Params(p => p
                        .Add("lat", input.TargetLatitude)
                        .Add("lon", input.TargetLongitude))));

        // 存在经纬度信息.默认排序为距离排序
        if (input is { TargetLatitude: not null, TargetLongitude: not null })
        {
            input.SortType = EsHotelSortType.Distance;
        }
        switch (input.SortType)
        {
            case EsHotelSortType.Default:
                //默认排序如下：优先搜索得分降序=>包含本地酒店=>HOP根据置顶或者权重值排序
                searchDescriptor
                    .Sort(s => s
                        .Descending("_score")
                        .Field(f => f
                            .Field("tenantConfig.includeLocalHotel")
                            .Nested(n => n
                                .Path(p => p.TenantConfig)
                                .Filter(fq => fq
                                    .Term(t => t
                                        .Field("tenantConfig.tenantId")
                                        .Value(tenantId)
                                    )
                                )
                            )
                            .Order(SortOrder.Descending)
                        )
                        .Field(f => f
                            .Field("tenantConfig.sortValue")
                            .Nested(n => n
                                .Path(p => p.TenantConfig)
                                .Filter(fq => fq
                                    .Term(t => t
                                        .Field("tenantConfig.tenantId")
                                        .Value(tenantId)
                                    )
                                )
                            )
                            .Order(SortOrder.Descending)
                        )
                    );
                break;
            case EsHotelSortType.Distance:
                //距离排序(由近到远)
                if (input is {TargetLatitude: not null, TargetLongitude: not null})
                {
                    searchDescriptor
                        .Sort(s => s
                            .GeoDistance(gd => gd
                                .Field(f => f.Location)
                                .Order(SortOrder.Ascending)
                                .Unit(DistanceUnit.Kilometers)
                                .Mode(SortMode.Min)
                                .Points(new GeoLocation(input.TargetLatitude.Value, input.TargetLongitude.Value)))
                            .Descending("_score")
                        );
                }
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }
        
        searchDescriptor
            .From((input.PageIndex - 1) * input.PageSize)
            .Size(input.PageSize);
        
        var search = await _elasticClient.SearchAsync<ResourceHotelDocumentV2>(searchDescriptor);
        result.PageIndex = input.PageIndex;
        result.PageSize = input.PageSize;
        result.Total = Convert.ToInt32(search.Total);
        var resultData = new List<SearchEsHotelOutput>();
        foreach (var docItem in search.Documents)
        {
            var resultItem = _mapper.Map<SearchEsHotelOutput>(docItem);
            resultItem.HotelIdList = new List<HotelIdNested>();
            resultItem.HotelIdList.Add(new HotelIdNested
            {
                SupplierApiType = SupplierApiType.Hop,
                FacilityId = docItem.FacilityId,
                HotelId = docItem.ApiHotelId
            });
            var includeLocalHotel = localHotelRelatedResourceId.Contains(docItem.Id);
            if (includeLocalHotel)
            {
                // 换取 本地酒店id
                var localHotel = localHotelDocs.FirstOrDefault(x => x.ResourceHotelId == docItem.Id)?.HotelIdList
                    .FirstOrDefault(x => x.SupplierApiType == SupplierApiType.None);
                if (localHotel is not null)
                {
                    resultItem.Id = localHotel.HotelId;
                    resultItem.HotelIdList.Add(new HotelIdNested
                    {
                        FacilityId = localHotel.FacilityId,
                        HotelId = localHotel.HotelId,
                        SupplierApiType = SupplierApiType.None
                    });
                }
            }
            
            var hitId = docItem.Id.ToString();
            var searchHits = search.Hits.FirstOrDefault(x => x.Id == hitId);
            var distance = searchHits.Fields.Value<double?>(distanceScriptFieldName);
            if (distance.HasValue)
            {
                resultItem.Distance = Math.Round(Convert.ToDecimal(distance.Value), 2);
            }
            resultData.Add(resultItem);
        }
        result.Data = resultData;
        return result;
    }

    public async Task<IEnumerable<SearchEsHotelOutput>> Get(long tenantId,params long[] mixHotelIds)
    {
        var result = new List<SearchEsHotelOutput>();
        var saasHopHotelConfig = await _baseHotelService.GetTenantSaasApiSupplierConfig(new GetTenantApiSupplierConfigDetailInput
        {
            TenantId = tenantId,
            SupplierApiType = SupplierApiType.Hop
        });
        var localHotelDocs = await GetLocalHotel(tenantId); // 查询全部本地酒店
        var localHotelRelatedResourceId = localHotelDocs.Select(x => x.ResourceHotelId).ToList(); // 本地所有资源酒店id
        
        var exchangeResourceHotelIds = new List<long>();

        #region 换取
        
        var relatedHotelDoc = await GetMixHotel(mixHotelIds,tenantId);
        var relateLocalResourceHotelIds = relatedHotelDoc
            .Where(x => x.TenantConfig != null)
            .Where(x => x.TenantConfig.Any(t =>
                t.TenantId == tenantId && (t.LocalHotelId.HasValue && mixHotelIds.Contains(t.LocalHotelId!.Value))))
            .Select(x => x.Id)
            .ToList();
            
        var relateApiHotelResourceHotelIds =
            relatedHotelDoc.Where(x => mixHotelIds.Contains(x.ApiHotelId)).Select(x => x.Id).ToList();
        
        var localIntersectIds = localHotelRelatedResourceId.Intersect(relateApiHotelResourceHotelIds).ToList(); //apihotel资源id换取本地
        relateLocalResourceHotelIds.AddRange(localIntersectIds);

        if (saasHopHotelConfig.Enabled == false)
        {
            relateApiHotelResourceHotelIds = new List<long>();
        }
        // 租户指定汇智酒店过滤
        if (saasHopHotelConfig.IsAllHotel == false)
        {
            relateApiHotelResourceHotelIds = saasHopHotelConfig.HotelIds.Intersect(relateApiHotelResourceHotelIds).ToList();
        }

        exchangeResourceHotelIds = relateLocalResourceHotelIds
            .Concat(relateApiHotelResourceHotelIds)
            .Distinct()
            .ToList();
        #endregion
        

        if (exchangeResourceHotelIds.Any() is false)
            return result;
        
        var count = mixHotelIds.Length;
        var searchDescriptor = new SearchDescriptor<ResourceHotelDocumentV2>()
            .Index(_resourceHotelIndexNameV2)
            .Query(q => q
                .Bool(b => b
                    .Must(m => m
                        .Terms(t => t
                            .Field(f => f.Id)
                            .Terms(exchangeResourceHotelIds)))));
        
        searchDescriptor
            .From(0)
            .Size(count);
        var searchResponse = await _elasticClient.SearchAsync<ResourceHotelDocumentV2>(searchDescriptor);
        foreach (var docItem in searchResponse.Documents)
        {
            var resultItem = _mapper.Map<SearchEsHotelOutput>(docItem);
            resultItem.HotelIdList = new List<HotelIdNested>();
            if (saasHopHotelConfig.IsAllHotel == true || (saasHopHotelConfig.IsAllHotel == false && saasHopHotelConfig.HotelIds.Contains(docItem.Id)))
            {
                resultItem.HotelIdList.Add(new HotelIdNested
                {
                    SupplierApiType = SupplierApiType.Hop,
                    FacilityId = docItem.FacilityId,
                    HotelId = docItem.ApiHotelId
                });
            }
            var includeLocalHotel = localHotelRelatedResourceId.Contains(docItem.Id);
            if (includeLocalHotel)
            {
                // 换取 本地酒店id
                var localHotel = localHotelDocs.FirstOrDefault(x => x.ResourceHotelId == docItem.Id)?.HotelIdList
                    .FirstOrDefault(x => x.SupplierApiType == SupplierApiType.None);
                if (localHotel is not null)
                {
                    resultItem.Id = localHotel.HotelId;
                    resultItem.HotelIdList.Add(new HotelIdNested
                    {
                        FacilityId = localHotel.FacilityId,
                        HotelId = localHotel.HotelId,
                        SupplierApiType = SupplierApiType.None
                    });
                }
            }
            
            // 没有汇智酒店和本地酒店.跳过
            if(resultItem.HotelIdList.Any() is false) continue;
            
            result.Add(resultItem);
        }

        return result;
    }

    #region private

    private static Func<ScriptSortDescriptor<ResourceHotelDocumentV2>, IScriptSort> SortFunc(long tenantId)
    {
        var sortFunc = new Func<ScriptSortDescriptor<ResourceHotelDocumentV2>, IScriptSort>(sd => sd
            .Type("number")
            .Script(scr => scr
                .Source(@"
                            long tenantId = params.tenantId;
                            def tenantIds = doc['tenantConfig.tenantId'];
                            if (tenantIds.isEmpty()) return 0;
                            
                            def onTops = doc['tenantConfig.onTop'];
                            def weightValues = doc['tenantConfig.weightValue'];
                            
                            for (int i = 0; i < tenantIds.length; i++) {
                              if (tenantIds[i] == tenantId) {
                                int onTop = onTops[i];
                                int weight = weightValues[i];
                                return (onTop == 1 ? 1000000000 : 0) + weight;
                              }
                            }
                            return 0;
                    ")
                .Params(p => p
                    .Add("tenantId", tenantId)
                )
                .Lang("painless")
            )
            .Order(SortOrder.Descending)
        );
        return sortFunc;
    }
    
    /// <summary>
    /// 查询本地酒店
    /// </summary>
    /// <param name="tenantId"></param>
    /// <param name="localHotelIds"></param>
    /// <returns></returns>
    private async Task<List<SearchEsHotelOutput>> GetLocalHotel(long tenantId,params long[] localHotelIds)
    {
        var indexName = $"{_tenantHotelIndexNamePrefix}{tenantId}";
        var count = 5000;
        var searchDescriptor = new SearchDescriptor<HotelDocument>()
            .Index(indexName)
            .Query(q => q
                .Nested(n => n
                    .Path(p => p.HotelIdList)
                    .Query(q => q
                        .Bool(b=>b
                            .Must(m =>
                            {
                                var conditions = new List<Func<QueryContainerDescriptor<HotelDocument>, QueryContainer>>();
                                
                                conditions.Add(qc=>qc
                                    .Term(t=>t
                                        .Field(f=>f.HotelIdList.Select(x=>x.SupplierApiType))
                                        .Value(SupplierApiType.None)));

                                if (localHotelIds.Any())
                                {
                                    conditions.Add(qc=>qc
                                        .Terms(t=>t
                                            .Field(f=>f.HotelIdList.Select(x=>x.HotelId))
                                            .Terms(localHotelIds)));
                                }
                                
                                return m.Bool(sq => sq.Must(conditions));
                            }))
                    )
                )
            );

        searchDescriptor
            .From(0)
            .Size(count);
        var searchResponse = await _elasticClient.SearchAsync<HotelDocument>(searchDescriptor);
        var result = _mapper.Map<List<SearchEsHotelOutput>>(searchResponse.Documents);
        return result;
    }

    
    private async Task<List<ResourceHotelDocumentV2>> GetMixHotel(long[] mixHotelIds,long tenantId)
    {
        if (mixHotelIds.Any() is false) return new List<ResourceHotelDocumentV2>();
        
        mixHotelIds = mixHotelIds.Where(x => x > 0).ToArray();
        var count = mixHotelIds.Length;
        var searchDescriptor = new SearchDescriptor<ResourceHotelDocumentV2>()
            .Index(_resourceHotelIndexNameV2)
            .Query(q => q
                .Bool(b => b
                    .Should(
                        s => s.Terms(t => t
                            .Field(f => f.ApiHotelId)
                            .Terms(mixHotelIds)),
                        s => s.Nested(n => n
                            .Path(p => p.TenantConfig)
                            .Query(q => q
                                .Bool(b => b
                                    .Must(
                                        m => m.Term(t => t
                                            .Field(f => f.TenantConfig.Select(x => x.TenantId))
                                            .Value(tenantId)),
                                        m => m.Terms(t => t
                                            .Field(f => f.TenantConfig.Select(x => x.LocalHotelId))
                                            .Terms(mixHotelIds))
                                    )
                                )
                            )
                        )
                    )
                ));
        searchDescriptor
            .From(0)
            .Size(count);
        var searchResponse = await _elasticClient.SearchAsync<ResourceHotelDocumentV2>(searchDescriptor);
        var result = _mapper.Map<List<ResourceHotelDocumentV2>>(searchResponse.Documents);
        return result;
    }
    
    /// <summary>
    /// 渠道价格设置
    /// <remarks>主要用来查询汇智酒店</remarks>
    /// </summary>
    /// <param name="priceGroupId"></param>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    private async Task<List<AgencyChannelPriceSettingsDocument>> SearchHopHotelChannelPriceSettings(
        long priceGroupId, long tenantId)
    {
        var indexName = $"{_tenantChannelPriceSettingIndexNamePrefix}{tenantId}";
        var searchResponse = await _elasticClient
            .SearchAsync<AgencyChannelPriceSettingsDocument>(i=>i
                .Index(indexName)
                .Query(q=>q
                    .Bool(b=>b
                        .Must(m=>m
                            .Term(t=>t
                                .Field(f=>f.PriceGroupId)
                                .Value(priceGroupId))))));
        var result = searchResponse.Documents.ToList();
        return result;
    }


    /// <summary>
    /// 转换hop汇智酒店价格分组渠道配置
    /// </summary>
    /// <param name="ApiHotelId"></param>
    /// <param name="IsStaffTag"></param>
    /// <param name="IsReunionRoomTag"></param>
    /// <param name="Tags"></param>
    record HopSpecifiedHotelSettingData(long ApiHotelId, bool IsStaffTag, bool IsReunionRoomTag, List<SellHotelTag> Tags);
    private async Task<List<HopSpecifiedHotelSettingData>> CovHuizhiSpecifiedHotelSettingData(EsHotelSearchInput input,
        long tenantId)
    {
        var result = new List<HopSpecifiedHotelSettingData>();
        if (input is { AgencyThirdHotel: true, PriceGroupId: > 0 })
        {
            var huiZhiChannelPriceSettings = await SearchHopHotelChannelPriceSettings(
                input.PriceGroupId,
                tenantId);
            var specifiedHotelIds = huiZhiChannelPriceSettings.Select(x => x.ProductId)
                .Distinct()
                .ToList();
            foreach (var hotelId in specifiedHotelIds)
            {
                var settings = huiZhiChannelPriceSettings
                    .Where(x => x.ProductId == hotelId)
                    .ToList();

                //主推酒店优先级高于主推酒店团房
                //主推酒店
                var isStaffTag = settings
                    .Any(x => x.HotelSaleType == ChannelHotelSaleType.IsStaffTag);

                //主推酒店团房
                var isReunionRoomTag = settings
                    .Any(x => x.HotelSaleType == ChannelHotelSaleType.IsReunionRoomTag);

                switch (isStaffTag)
                {
                    //主推酒店团房(优先级比主推酒店散房低)
                    case true:
                        isReunionRoomTag = false;
                        break;
                    case false when isReunionRoomTag:
                        isStaffTag = true;
                        break;
                }

                var sellHotelTags = settings
                    .Where(x => ChannelHotelSaleTypeToSellTagMapping.ContainsKey(x.HotelSaleType))
                    .Select(x => ChannelHotelSaleTypeToSellTagMapping[x.HotelSaleType])
                    .ToList();
                result.Add(new HopSpecifiedHotelSettingData(
                    ApiHotelId: hotelId,
                    IsStaffTag: isStaffTag,
                    IsReunionRoomTag: isReunionRoomTag,
                    Tags: sellHotelTags));
            }

            //指定酒店配置的id和传入过滤的酒店id需要进行过滤.
            if (input.AgencyThirdHotelId.Any())
            {
                //获取存在配置酒店id
                result = result
                    .Where(x => input.AgencyThirdHotelId.Contains(x.ApiHotelId))
                    .ToList();
            }
        }
        return result;
    }
    
    private Dictionary<ChannelHotelSaleType, SellHotelTag> ChannelHotelSaleTypeToSellTagMapping =>
        new Dictionary<ChannelHotelSaleType, SellHotelTag>
        {
            {ChannelHotelSaleType.SaleTagA, SellHotelTag.A},
            {ChannelHotelSaleType.SaleTagB, SellHotelTag.B},
            {ChannelHotelSaleType.SaleTagC, SellHotelTag.C},
            {ChannelHotelSaleType.SaleTagD, SellHotelTag.D},
            {ChannelHotelSaleType.SaleTagE, SellHotelTag.E},
            {ChannelHotelSaleType.SaleTagO, SellHotelTag.O},
        };

    #endregion
}