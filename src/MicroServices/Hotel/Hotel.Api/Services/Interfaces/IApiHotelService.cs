using Contracts.Common.Hotel.DTOs.ApiHotel;
using Contracts.Common.Hotel.DTOs.ApiHotel.HopApiHotel;
using EfCoreExtensions.Abstract;
using Hotel.Api.EsDocument;

namespace Hotel.Api.Services.Interfaces;

public interface IApiHotelService
{
    /// <summary>
    /// 查询汇智酒店
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchOutput>> Search(SearchInput input);

    Task<PagingModel<SearchOutput>> SearchV2(SearchInput input, long? tenantId);

    /// <summary>
    /// 添加第三方酒店
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task Add(AddInput input);

    Task AddV2(ResourceHotelDocumentV2 input);
    Task Refresh(List<long> resourceHotelIds);

    /// <summary>
    /// 添加所有满足条件的第三方酒店
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AddAll(AddAllApiHotelInput input);

    /// <summary>
    /// 删除汇智酒店
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task Delete(DeleteInput input);

    Task<List<(long ResourceId, long ApiHotelId)>> GetByResourceIds(params long[] resourceIds);

    /// <summary>
    /// 绑定第三方酒店
    /// </summary>
    /// <param name="apiHotelHositoryId"></param>
    /// <returns></returns>
    Task<ApiHotelHositoryDto> Bind(long apiHotelHositoryId);

    /// <summary>
    /// 根据资源酒店Id检查酒店是否已经添加
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<CheckRefOutput>> CheckRefByResourceHotelIds(CheckRefInput input);

    /// <summary>
    /// 设置是否置顶
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SetOnTop(SetOnTopInput input);

    Task SetOnTopV2(SetOnTopInput input);

    /// <summary>
    /// 设置权重
    /// </summary>
    /// <param name="inputs"></param>
    /// <returns></returns>
    Task SetWeightValues(IEnumerable<HotelWeightValueInput> inputs);
    Task SetWeightValuesV2(IEnumerable<HotelWeightValueInput> inputs);

    /// <summary>
    /// 根据资源酒店信息同步汇智API酒店详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SyncApiHotelDetails(Contracts.Common.Hotel.DTOs.Hotel.SyncHotelInfosInput input);

    /// <summary>
    /// 批量设置是否主推
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateTags(List<UpdateTagInput> input);

    /// <summary>
    /// api酒店详情
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    Task<List<GetApiHotelDetailOutput>> Detail(GetApiHotelDetailsInput input);

    /// <summary>
    /// 批量更新 ApiHotel 售卖标签
    /// </summary>
    /// <returns></returns>
    Task<SyncHopApiHotelTagsDto> SyncApiHotelTags();
}
