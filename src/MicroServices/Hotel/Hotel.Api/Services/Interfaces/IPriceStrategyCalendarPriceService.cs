using Contracts.Common.Hotel.DTOs;
using Contracts.Common.Hotel.DTOs.PriceStrategy;
using Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Order.Enums;

namespace Hotel.Api.Services.Interfaces
{
    public interface IPriceStrategyCalendarPriceService
    {
        /// <summary>
        /// 修改基础价格
        /// </summary>
        Task UpdateBasisPrice(UpdateBasisPriceInput input);
        /// <summary>
        /// 获取单个报价日历价格
        /// </summary>
        Task<GetByPriceStrategyOutput> GetByPriceStrategy(GetByPriceStrategyInput input);
        /// <summary>
        /// 获取酒店日历价格
        /// </summary>
        Task<List<GetByHotelOutput>> GetByHotel(GetByHotelInput input);
        /// <summary>
        /// 获取可售报价
        /// </summary>
        Task<GetSaleByHotelOutput> GetSaleByHotel(GetSaleByHotelInput input);

        /// <summary>
        /// 通过酒店Id、价格策略Id、时间区间 获取价格，库存，房态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetPriceInventoryStatusOutput> GetPriceInventoryStatus(GetPriceInventoryStatusInput input);

        /// <summary>
        /// 获取酒店可售价格
        /// </summary>
        Task<List<SalePriceStrategyPrice>> SalePriceStrategyPrice(List<long> hotelIds, DateTime beginDate, DateTime endDate, SellingChannels salesChannel,
            bool needZeroAvailableQuantity = false);
        /// <summary>
        /// 获取价格策略指定日期所有售卖渠道价格
        /// </summary>
        Task<List<GetSaleChannelsPriceOutput>> GetSaleChannelsPrice(GetSaleChannelsPriceInput input);

        ValueTask<IEnumerable<QuerySaleChanelsPriceOutput>> QuerySaleChanelsPrice(QuerySaleChanelsPriceInput input);

        /// <summary>
        /// 检验价格策略是否可售
        /// </summary>
        Task<CheckPriceStrategySaleOutput> CheckPriceStrategySale(CheckPriceStrategySaleInput input);
        /// <summary>
        /// 获取30天最低价或最高价
        /// </summary>
        /// <param name="type">max:最高价，min:最低价</param>
        Task<List<GetRecentPriceOutput>> GetRecentMinOrMaxPrice(List<long> hotelIds, RecentPriceType type);
        /// <summary>
        /// 获取OTA售价
        /// </summary>
        Task<List<GetOTAPriceOutput>> GetOTAPrice(List<GetByResourceHotelIdsOutput> priceStrategies, DateTime beginDate, DateTime endDate);

        /// <summary>
        /// 手工单价格策略库存校验
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<CheckSaleOutput> CheckPriceStrategySaleByManual(CheckSaleInput input);

        /// <summary>
        /// 分销商web-酒店详情策略
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<AgencyGetOutput>> AgencyGet(AgencyGetInput input);

        /// <summary>
        /// 本地酒店价格策略查询校验
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<CheckSaleOutput> CheckSaleByLocalHotel(CheckSaleInput input);

        /// <summary>
        /// 第三方酒店价格策略查询校验
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<CheckSaleOutput> CheckSaleByThirdPartyHotel(CheckSaleInput input,long tenantId);

        /// <summary>
        /// 日历酒店的价格策略按可预订最近日期
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetSaleableOutput> GetSaleable(GetSaleableInput input);
    }
}
