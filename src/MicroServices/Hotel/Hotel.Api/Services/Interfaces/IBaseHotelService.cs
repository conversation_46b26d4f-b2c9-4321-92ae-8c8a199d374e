using Contracts.Common.Payment.DTOs.Currency;
using Contracts.Common.Resource.DTOs.GDSHotel;
using Contracts.Common.Resource.DTOs.ThirdHotel;
using Contracts.Common.Tenant.DTOs.Supplier;
using Contracts.Common.Tenant.DTOs.Tenant;
using Contracts.Common.Tenant.DTOs.TenantApiSupplierConfig;

namespace Hotel.Api.Services.Interfaces;

public interface IBaseHotelService
{
    Task<GetTenantSysConfigOutput> GetTenantSysConfig();
    Task<GetSupplierOutput> GetSupplierInfo(long supplierId);
    Task<List<GetExchangeRateOutput>> GetCurrencyRate(List<GetExchangeRatesInput> input);
    Task<List<GetExchangeRateOutput>> GetCurrencyExchangeRateList(List<GetExchangeRatesInput> input);
    Task<GetThirdHotelPriceOutput> GetPriceByThirdPartyHotel(GetThirdHotelPriceInput request);
    Task<List<GDSHotelFacilitiesOutput>> GetGDSFacilities(params long[] hotelIds);

    /// <summary>
    /// 第三方酒店 试单接口
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    Task<CheckAvailabilityOutput> ThirdPartyHotelCheckAvailability(CheckAvailabilityInput request);

    /// <summary>
    /// 获取租户saasapi供应商配置
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    Task<GetTenantApiSupplierConfigDetailOutput> GetTenantSaasApiSupplierConfig(GetTenantApiSupplierConfigDetailInput request);

    /// <summary>
    /// 创建酒店试单码
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    string CreatePreBookingCode(Contracts.Common.Hotel.DTOs.HotelPreBookingDto dto);

    /// <summary>
    /// 获取酒店试单码数据
    /// </summary>
    /// <param name="preBookingCode"></param>
    /// <returns></returns>
    Contracts.Common.Hotel.DTOs.HotelPreBookingDto? GetPreBookingByCode(string? preBookingCode);

    /// <summary>
    /// 获取酒店试单配置
    /// </summary>
    /// <returns></returns>
    ConfigModel.HotelPreBookingConfig GetHotelPreBookingConfig();
}