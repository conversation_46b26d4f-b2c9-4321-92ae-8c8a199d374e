using Contracts.Common.Resource.Enums;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hotel.Api.Services.Utils;
public static class HotelPreBookingHelper
{
    private const string _aesKey = "HopOpenApiHotels";
    private const string _aesIV = "huitravel.tenant";
    private static string CreatePreBookingCode(CreatePreBookingDto dto)
    {
        var data = JsonConvert.SerializeObject(dto);
        var code = Common.Utils.SecurityUtil.AESEncrypt(data, _aesKey, _aesIV);
        return code;
    }

    private static CreatePreBookingDto? GetPreBookingByCode(string? preBookingCode)
    {
        if (string.IsNullOrWhiteSpace(preBookingCode))
            return null;
        var data = Common.Utils.SecurityUtil.AESDecrypt(preBookingCode, _aesKey, _aesIV);
        var result = JsonConvert.DeserializeObject<CreatePreBookingDto>(data);
        return result;
    }
}

public record CreatePreBookingDto
{
    /// <summary>
    /// 价格策略类型
    /// </summary>
    public Contracts.Common.Hotel.Enums.PriceStrategyType PriceStrategyType { get; set; }

    #region 连住策略单独部分

    /// <summary>
    /// 连住晚数及以上
    /// </summary>
    public int NumberOfNights { get; set; }

    /// <summary>
    /// 限制最大连住天数
    /// </summary>
    public int? LimitNumberOfNights { get; set; }

    #endregion

    #region 团房策略单独部分

    /// <summary>
    /// 最小预订间数
    /// </summary>
    public int NumberOfRooms { get; set; }

    #endregion

    /// <summary>
    /// 是否直采
    /// </summary>
    public bool IsDirect { get; set; }

    /// <summary>
    /// 敏感度类型 
    /// </summary>
    public SellHotelTag? Tag { get; set; }

    /// <summary>
    /// 预订礼遇
    /// </summary>
    public string? BookingBenefits { get; set; }

    /// <summary>
    /// 采购总价
    /// </summary>
    public decimal TotalPrice { get; set; }

    /// <summary>
    /// 每日价格
    /// </summary>
    public decimal[] Cost { get; set; }
}