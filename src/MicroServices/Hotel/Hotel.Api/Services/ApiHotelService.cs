using AutoMapper;
using Cit.Storage.Redis;
using Common.GlobalException;
using Common.Jwt;
using Common.ServicesHttpClient;
using Contracts.Common.Hotel.DTOs.ApiHotel;
using Contracts.Common.Hotel.DTOs.ApiHotel.HopApiHotel;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Product.Messages;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Tenant.DTOs.Focussend;
using Contracts.Common.Tenant.DTOs.SupplierApiFields;
using Contracts.Common.Tenant.DTOs.SupplierApiSetting;
using Contracts.Common.Tenant.DTOs.TenantApiSupplierConfig;
using Contracts.Common.Tenant.Enums;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Hangfire;
using HangfireClient.Jobs.Hotel;
using Hotel.Api.ConfigModel;
using Hotel.Api.EsDocument;
using Hotel.Api.Extensions;
using Hotel.Api.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Nest;
using Newtonsoft.Json;
using System.Text.RegularExpressions;
using SearchInput = Contracts.Common.Hotel.DTOs.ApiHotel.SearchInput;

namespace Hotel.Api.Services;

public class ApiHotelService : IApiHotelService
{
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ServicesAddress _servicesAddress;
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ICapPublisher _capPublisher;
    private readonly IElasticClient _elasticClient;
    private readonly IBaseHotelService _baseHotelService;
    private readonly HuiZhiHotelConfig _huiZhiHotelConfig;
    private readonly IRedisClient _redisClient;

    private const string _resourceHotelIndexNameV2 = "resource-hotel-v2"; // 资源酒店索引名称
    private const string _apiHotelTagUpdateKey = "hotel:apihotel:updatehoteltags";

    public ApiHotelService(
        CustomDbContext dbContext,
        IMapper mapper,
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> options,
        IBackgroundJobClient backgroundJobClient,
        IHttpContextAccessor httpContextAccessor,
        ICapPublisher capPublisher,
        IElasticClient elasticClient,
        IBaseHotelService baseHotelService,
        IRedisClient redisClient,
        IOptionsMonitor<HuiZhiHotelConfig> huiZhiHotelConfig)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _httpClientFactory = httpClientFactory;
        _servicesAddress = options.Value;
        _backgroundJobClient = backgroundJobClient;
        _httpContextAccessor = httpContextAccessor;
        _capPublisher = capPublisher;
        _elasticClient = elasticClient;
        _baseHotelService = baseHotelService;
        _redisClient = redisClient;
        _huiZhiHotelConfig = huiZhiHotelConfig.CurrentValue;
    }

    /// <summary>
    /// 查询第三方酒店
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Obsolete]
    public async Task<PagingModel<SearchOutput>> Search(SearchInput input)
    {
        var linq = _dbContext.ApiHotels.AsNoTracking();
        //根据资源酒店id查询
        if (input.ResourceHotelIds.Any())
            linq = linq.IgnoreQueryFilters();
        var query = linq
            .WhereIF(!string.IsNullOrWhiteSpace(input.KeyWord),
                x => x.ZHName.Contains(input.KeyWord) || x.ENName.Contains(input.KeyWord))
            .WhereIF(input.CityCode is not null, x => x.CityCode.Equals(input.CityCode))
            .WhereIF(input.ApiHotelIds?.Any() is true, x => input.ApiHotelIds.Contains(x.Id))
            .WhereIF(input.StarLevel is not null, x => x.StarLevel < input.StarLevel + 1 && x.StarLevel > input.StarLevel - 1)
            .WhereIF(input.StaffTag is not null, x => x.StaffTag.Equals(input.StaffTag));


        var apiHotelIds = new List<long>();
        //标签筛选
        if (input.HotelTagId is not null && input.HotelTagId > 0)
        {
            apiHotelIds = await _dbContext.HotelTags.AsNoTracking()
                .Where(x => x.TagId.Equals(input.HotelTagId))
                .Select(x => x.ApiHotelId)
                .ToListAsync();

            query = query.Where(x => apiHotelIds.Contains(x.Id));
        }

        if (input.HotelFilter != null)
        {
            //查询指定酒店的配置是否在对应酒店的售卖类型中.
            var includeHotelIds = new List<long>();
            var filterIds = input.HotelFilter.IncludeHotelFilters
                .Select(x => x.HotelId)
                .ToList();
            if (filterIds.Any())
            {
                var filterHotels = await _dbContext.ApiHotels.AsNoTracking()
                    .Where(x => filterIds.Contains(x.Id))
                    .ToListAsync();
                foreach (var apiHotel in filterHotels)
                {
                    var includeHotelFilter = input.HotelFilter.IncludeHotelFilters
                        .FirstOrDefault(x => x.HotelId == apiHotel.Id);
                    var apiHotelTypes = new ApiHotelTag();
                    if (includeHotelFilter.ApiHotelTags.Any())
                        apiHotelTypes = includeHotelFilter.ApiHotelTags
                            .Aggregate((current, next) => current | next);

                    var isStaffTag = includeHotelFilter.IsStaffTag && apiHotel.StaffTag;
                    if (includeHotelFilter.IsReunionRoomTag)
                    {
                        isStaffTag = isStaffTag && apiHotel.ReunionRoom > 0;
                    }

                    //判断配置的售卖渠道是否有包含
                    var isInclude = isStaffTag
                                    || (apiHotel.Tags.HasValue && ((apiHotelTypes & apiHotel.Tags) != 0));
                    if (!isInclude)
                        continue;
                    includeHotelIds.Add(apiHotel.Id);
                }
            }

            //过滤默认配置不包含的酒店
            var defaultApiHotelTags = new ApiHotelTag();
            if (input.HotelFilter.ApiHotelTags.Any())
                defaultApiHotelTags = input.HotelFilter.ApiHotelTags
                    .Aggregate((current, next) => current | next);

            if (includeHotelIds.Any())
            {
                query = query
                    .Where(x => includeHotelIds.Contains(x.Id)
                                || ((input.HotelFilter.IsStaffTag && x.StaffTag &&
                                     (!input.HotelFilter.IsReunionRoomTag ||
                                      (input.HotelFilter.IsReunionRoomTag && x.ReunionRoom > 0)))
                                    || (x.Tags.HasValue && ((defaultApiHotelTags & x.Tags) != 0)))
                    );
            }
            else
            {
                query = query
                    .Where(x => (input.HotelFilter.IsStaffTag && x.StaffTag && (!input.HotelFilter.IsReunionRoomTag ||
                                    (input.HotelFilter.IsReunionRoomTag && x.ReunionRoom > 0)))
                                || (x.Tags.HasValue && ((defaultApiHotelTags & x.Tags) != 0))
                    );
            }
        }


        if (input.HopId.HasValue)
        {
            var hopLinq = _dbContext.ApiHotelExtends
                .AsNoTracking()
                .Where(x => x.ApiHotelSourceType == ApiHotelSourceType.Hop && x.ThirdHotelId == input.HopId);

            query = query.Where(x => hopLinq.Any(h => h.ResourceHotelId == x.ResourceHotelId));
        }

        if (input.HopIds != null && input.HopIds.Any())
        {
            var hopLinq = _dbContext.ApiHotelExtends
                .AsNoTracking()
                .Where(x => x.ApiHotelSourceType == ApiHotelSourceType.Hop && input.HopIds.Contains(x.ThirdHotelId));

            query = query.Where(x => hopLinq.Any(h => h.ResourceHotelId == x.ResourceHotelId));
        }

        // query = query.OrderByDescending(x => x.OnTop)
        //              .ThenByDescending(x => x.WeightValue)
        //              .ThenByDescending(x => x.Id);


        var pagingModel = await query.PagingAsync(input.PageIndex, input.PageSize);

        var result = _mapper.Map<PagingModel<SearchOutput>>(pagingModel);

        // hopid setting
        if (result?.Data != null)
        {
            var apiHotelResoureHotelIds = result.Data.Select(x => x.ResourceHotelId);

            if (apiHotelResoureHotelIds != null && apiHotelResoureHotelIds.Any())
            {

                var apiHotelExtends = await _dbContext.ApiHotelExtends.AsNoTracking()
                    .Where(x => apiHotelResoureHotelIds.Contains(x.ResourceHotelId) && x.ApiHotelSourceType == ApiHotelSourceType.Hop)
                    .Select(x => new { x.ResourceHotelId, x.ThirdHotelId })
                    .ToListAsync();

                foreach (var item in result.Data)
                {
                    item.HopId = apiHotelExtends.FirstOrDefault(x => x.ResourceHotelId == item.ResourceHotelId)?.ThirdHotelId ?? -1;
                }
            }
        }

        return result;
    }

    public async Task<PagingModel<SearchOutput>> SearchV2(SearchInput input, long? tenantId)
    {
        var result = new PagingModel<SearchOutput>
        {
            PageIndex = input.PageIndex,
            PageSize = input.PageSize
        };
        var apiHotelIds = input.ApiHotelIds;
        var resourceHotelIds = input.ResourceHotelIds;
        var hopIds = input.HopIds ?? new List<long>();
        if (input.HopId.HasValue) hopIds.Add(input.HopId.Value);

        #region 查询saas租户配置

        if (tenantId.HasValue)
        {
            var saasHopHotelConfig = await _baseHotelService.GetTenantSaasApiSupplierConfig(new GetTenantApiSupplierConfigDetailInput
            {
                TenantId = tenantId.Value,
                SupplierApiType = SupplierApiType.Hop
            });
            if (saasHopHotelConfig == null || saasHopHotelConfig.Enabled == false)
            {
                return result;
            }

            if (saasHopHotelConfig.IsAllHotel == false)
            {
                resourceHotelIds.AddRange(saasHopHotelConfig.HotelIds);
            }
        }

        #endregion

        #region 标签筛选

        if (input.HotelTagId is not null && input.HotelTagId > 0 && tenantId.HasValue)
        {
            var tagApiHotelIds = await _dbContext.HotelTags
                .AsNoTracking()
                .Where(x => x.TagId.Equals(input.HotelTagId))
                .Select(x => x.ApiHotelId)
                .ToListAsync();
            apiHotelIds.AddRange(tagApiHotelIds);
        }

        #endregion

        #region shouldClauses

        var shouldClauses = new List<Func<QueryContainerDescriptor<ResourceHotelDocumentV2>, QueryContainer>>();
        if (input.HotelFilter != null)
        {
            var hopHotelFilterShouldClauses =
                new Func<QueryContainerDescriptor<ResourceHotelDocumentV2>, QueryContainer>(sq => sq.Bool(ib => ib
                    .Must(sm =>
                    {
                        var conditions =
                            new List<Func<QueryContainerDescriptor<ResourceHotelDocumentV2>, QueryContainer>>();

                        //默认酒店不配置则不过滤
                        if (input.HotelFilter.IsStaffTag || input.HotelFilter.ApiHotelTags.Any())
                        {
                            var saleTags = MappingSellHotelTag(input.HotelFilter.ApiHotelTags);

                            conditions.Add(c => c
                                .Bool(b => b
                                    .Should(
                                        s => s.Bool(bb => bb
                                            .Must(m =>
                                            {
                                                //主推标签过滤
                                                if (!input.HotelFilter.IsStaffTag || input.HotelFilter.IsReunionRoomTag)
                                                    return null;

                                                return m.Term(t => t
                                                    .Field(f => f.StaffTag)
                                                    .Value(1));
                                            })),
                                        s => s.Bool(bb => bb
                                            .Must(m =>
                                            {
                                                //主推团房标签过滤
                                                if (!input.HotelFilter.IsReunionRoomTag)
                                                    return null;

                                                var reunionRoomConditions =
                                                    new List<Func<QueryContainerDescriptor<ResourceHotelDocumentV2>,
                                                        QueryContainer>>();
                                                reunionRoomConditions.Add(r => r
                                                    .Term(t => t
                                                        .Field(f => f.StaffTag)
                                                        .Value(1)));
                                                reunionRoomConditions.Add(r => r
                                                    .Range(r => r
                                                        .Field(f => f.ReunionRoom)
                                                        .GreaterThan(0)));
                                                return m.Bool(b => b.Must(reunionRoomConditions));
                                            })),
                                        s => s.Bool(bb => bb
                                            .Must(m =>
                                            {
                                                //售卖标签过滤
                                                if (saleTags.Any() is false)
                                                    return null;

                                                return m.Terms(t => t
                                                    .Field(f => f.Tags)
                                                    .Terms(saleTags));
                                            }))
                                    )));
                        }

                        return sm.Bool(bd => bd.Must(conditions));
                    })));
            shouldClauses.Add(hopHotelFilterShouldClauses);

            if (input.HotelFilter.IncludeHotelFilters.Any())
            {
                var huiZhiSpecifiedHotelShouldClauses = input.HotelFilter.IncludeHotelFilters.Select(x =>
                        (Func<QueryContainerDescriptor<ResourceHotelDocumentV2>, QueryContainer>)(s => s
                            .Bool(b => b
                                .Must(m =>
                                {
                                    var conditions =
                                        new List<
                                            Func<QueryContainerDescriptor<ResourceHotelDocumentV2>, QueryContainer>>();

                                    conditions.Add(c => c
                                        .Term(t => t
                                            .Field(f => f.ApiHotelId)
                                            .Value(x.HotelId)));

                                    var isStaffTag = x.IsStaffTag;
                                    var tags = MappingSellHotelTag(x.ApiHotelTags);
                                    var isReunionRoomTag = x.IsReunionRoomTag;
                                    if (isStaffTag || tags.Any())
                                    {
                                        conditions.Add(c => c
                                            .Bool(b => b
                                                .Should(
                                                    s => s.Bool(bb => bb
                                                        .Must(m =>
                                                        {
                                                            //主推标签过滤
                                                            if (!isStaffTag || isReunionRoomTag)
                                                                return null;

                                                            return m.Term(t => t
                                                                .Field(f => f.StaffTag)
                                                                .Value(1));
                                                        })),
                                                    s => s.Bool(bb => bb
                                                        .Must(m =>
                                                        {
                                                            //主推团房标签过滤
                                                            if (!isReunionRoomTag)
                                                                return null;

                                                            var reunionRoomConditions =
                                                                new List<Func<QueryContainerDescriptor<
                                                                        ResourceHotelDocumentV2>,
                                                                    QueryContainer>>();
                                                            reunionRoomConditions.Add(r => r
                                                                .Term(t => t
                                                                    .Field(f => f.StaffTag)
                                                                    .Value(1)));
                                                            reunionRoomConditions.Add(r => r
                                                                .Range(r => r
                                                                    .Field(f => f.ReunionRoom)
                                                                    .GreaterThan(0)));
                                                            return m.Bool(b => b.Must(reunionRoomConditions));
                                                        })),
                                                    s => s.Bool(bb => bb
                                                        .Must(m =>
                                                        {
                                                            //售卖标签过滤
                                                            if (tags.Any() is false)
                                                                return null;

                                                            return m.Terms(t => t
                                                                .Field(f => f.Tags)
                                                                .Terms(tags));
                                                        }))
                                                )));
                                    }

                                    return m.Bool(b => b.Must(conditions));
                                }))
                        ))
                    .ToList();
                shouldClauses.AddRange(huiZhiSpecifiedHotelShouldClauses);
            }
        }

        #endregion

        var searchDescriptor = new SearchDescriptor<ResourceHotelDocumentV2>()
            .Index(_resourceHotelIndexNameV2)
            .Source()
            .Query(q => q
                .Bool(b => b
                    .Must(m =>
                    {
                        var conditions = new List<Func<QueryContainerDescriptor<ResourceHotelDocumentV2>, QueryContainer>>();

                        conditions.Add(q => q
                            .LongRange(l => l
                                .Field(f => f.ApiHotelId)
                                .GreaterThan(0)));

                        if (resourceHotelIds.Any())
                        {
                            conditions.Add(q => q
                                .Terms(t => t
                                    .Field(f => f.Id)
                                    .Terms(resourceHotelIds)));
                        }

                        if (apiHotelIds.Any())
                        {
                            conditions.Add(q => q
                                .Terms(t => t
                                    .Field(f => f.ApiHotelId)
                                    .Terms(apiHotelIds)));
                        }

                        if (hopIds.Any())
                        {
                            conditions.Add(q => q
                                .Terms(t => t
                                    .Field(f => f.HopId)
                                    .Terms(hopIds)));
                        }

                        if (!string.IsNullOrWhiteSpace(input.KeyWord))
                        {
                            conditions.Add(q => q
                                .MultiMatch(mm => mm
                                    .Query(input.KeyWord)
                                    .Fields(fs => fs
                                        .Field(f => f.ZHName)
                                        .Field(f => f.ENName))
                                    .Type(TextQueryType.Phrase)));
                        }

                        if (input.CityCode.HasValue)
                        {
                            conditions.Add(q => q
                                .Term(t => t
                                    .Field(f => f.CityCode)
                                    .Value(input.CityCode.Value)));
                        }

                        // 星级过滤逻辑 StarLevel-1 < StarLevel < StarLevel +1
                        if (input.StarLevel.HasValue)
                        {
                            var doubleMin = Convert.ToDouble(input.StarLevel.Value - 1);
                            var doubleMax = Convert.ToDouble(input.StarLevel.Value + 1);
                            conditions.Add(q => q
                                .Range(r => r
                                    .Field(f => f.StarLevel)
                                    .GreaterThan(doubleMin)
                                    .LessThan(doubleMax)));
                        }

                        if (input.StaffTag.HasValue)
                        {
                            var staffTag = input.StaffTag.Value ? 1 : 0;
                            conditions.Add(qc => qc
                                .Term(qm => qm
                                    .Field(f => f.StaffTag)
                                    .Value(staffTag)));
                        }


                        return m.Bool(sq => sq.Must(conditions));
                    })
                    .Filter(ft => ft
                        .Bool(fb => fb
                            .Should(shouldClauses.ToArray())))));

        if (tenantId.HasValue)
        {
            searchDescriptor
                .Sort(s => s
                    .Descending("_score")
                    .Field(f => f
                        .Field("tenantConfig.sortValue") // 第二排序：嵌套字段
                        .Nested(n => n
                            .Path(p => p.TenantConfig)
                            .Filter(fq => fq
                                .Term(t => t
                                    .Field("tenantConfig.tenantId")
                                    .Value(tenantId.Value)
                                )
                            )
                        )
                        .Order(SortOrder.Descending)
                    )
                    .Descending(d => d.ApiHotelId)
                );
        }
        else
        {
            searchDescriptor
                .Sort(s => s
                    .Descending("_score")
                    .Descending(d => d.ApiHotelId)
                );
        }

        searchDescriptor
            .From((input.PageIndex - 1) * input.PageSize)
            .Size(input.PageSize);
        var search = await _elasticClient.SearchAsync<ResourceHotelDocumentV2>(searchDescriptor);
        result.PageIndex = input.PageIndex;
        result.PageSize = input.PageSize;
        result.Total = Convert.ToInt32(search.Total);
        var resultData = new List<SearchOutput>();
        foreach (var document in search.Documents)
        {
            var resultItem = _mapper.Map<SearchOutput>(document);
            resultItem.SaleFlag = true;
            var tenantConfig = document.TenantConfig?.FirstOrDefault(x => x.TenantId == tenantId);
            if (tenantConfig != null)
            {
                resultItem.OnTop = tenantConfig.OnTop == 1;
                resultItem.WeightValue = tenantConfig.WeightValue ?? 0;
            }
            resultData.Add(resultItem);
        }

        result.Data = resultData;
        return result;
    }

    /// <summary>
    /// 添加第三方酒店
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Obsolete]
    public async Task Add(AddInput input)
    {
        #region 判断是否Api供应商
        if (!await CheckIsApiSupplier()) throw new BusinessException(ErrorTypes.Tenant.IsNotApiSupplier);
        #endregion

        if (_dbContext.ApiHotels.AsNoTracking()
            .Any(x => x.ResourceHotelId == input.ResourceHotelId))
            return;

        //不可售状态不能添加
        var headers = new List<KeyValuePair<string, string>> { new("tenant", string.Empty) };
        var body = new
        {
            pageIndex = 1,
            pageSize = 1,
            name = input.ZHName,
            input.CityCode,
        };
        using var httpContent = new StringContent(JsonConvert.SerializeObject(body), Encoding.UTF8, "application/json");

        var thirdHotels = await _httpClientFactory.InternalPostAsync<PagingModel<Contracts.Common.Resource.DTOs.ThirdHotel.SearchOutput>>(
            _servicesAddress.Resource_SearchThirdHotels(),
            headers,
            httpContent: httpContent);
        var thirdHotel = thirdHotels.Data.FirstOrDefault();
        if (!thirdHotel.IsEnabled) return;

        //数据补充
        input.ENAddress ??= thirdHotel?.ENAddress;

        var tags = CreateMappingTag(input.Tags);
        var apiHotel = _mapper.Map<ApiHotel>(input);
        apiHotel.Tags = tags;
        apiHotel.SetLocation(input.Longitude, input.Latitude);
        await _dbContext.AddAsync(apiHotel);

        var apiHotelExtend = await _dbContext.ApiHotelExtends
            .FirstOrDefaultAsync(x => x.ResourceHotelId == apiHotel.ResourceHotelId && x.ApiHotelSourceType == ApiHotelSourceType.Hop);

        if (apiHotelExtend == null)
        {
            await _dbContext.ApiHotelExtends.AddAsync(new ApiHotelExtend(apiHotel.ResourceHotelId, input.HopId));
        }
        else
        {
            apiHotelExtend.Set(apiHotel.ResourceHotelId, input.HopId);
        }

        await _dbContext.SaveChangesAsync();

        if (thirdHotel is not null && apiHotel is not null)
        {
            await CreateUpdateContact(new List<Contracts.Common.Resource.DTOs.ThirdHotel.SearchOutput> { thirdHotel }, new List<ApiHotel> { apiHotel });
        }
    }

    public async Task AddV2(ResourceHotelDocumentV2 input)
    {
        var dbApiHotel = await _dbContext.ApiHotels.FirstOrDefaultAsync(x => x.ResourceHotelId == input.Id);
        if (dbApiHotel != null)
        {
            // 触发更新
            dbApiHotel.UpdateTime = DateTime.Now;
        }
        else
        {
            // 新增
            var apiHotel = new ApiHotel
            {
                ResourceHotelId = input.Id,
                ZHName = input.ZHName,
                ENName = input.ENName,
                StarLevel = input.StarLevel,
                CountryCode = input.CountryCode,
                CountryName = input.CountryName,
                ProvinceCode = input.ProvinceCode,
                ProvinceName = input.ProvinceName,
                CityCode = input.CityCode,
                CityName = input.CityName,
                CoordinateType = input.CoordinateType,
                Address = input.Address,
                ENAddress = input.ENAddress
            };
            apiHotel.SetLocation(input.Location.Longitude, input.Location.Latitude);
            await _dbContext.AddAsync(apiHotel);
        }
        await _dbContext.SaveChangesAsync();
    }

    public async Task Refresh(List<long> resourceHotelIds)
    {
        var dbApiHotels = await _dbContext.ApiHotels.Where(x => resourceHotelIds.Contains(x.ResourceHotelId))
            .ToListAsync();
        foreach (var dbApiHotel in dbApiHotels)
        {
            // 触发更新
            dbApiHotel.UpdateTime = DateTime.Now;
        }
        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 添加所有满足条件的第三方酒店
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Obsolete]
    public async Task AddAll(AddAllApiHotelInput input)
    {
        throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        #region 判断是否Api供应商
        if (!await CheckIsApiSupplier()) throw new BusinessException(ErrorTypes.Tenant.IsNotApiSupplier);
        #endregion

        #region 查询之前是否有进行中的添加
        if (await _dbContext.ApiHotelHositorys
            .AnyAsync(x => x.AddApiHotelStatus.Equals(AddApiHotelStatus.InProgress)))
        {
            throw new BusinessException(ErrorTypes.Hotel.TaskInProgress);
        }
        #endregion

        #region 添加任务记录
        var huiZhiHotelHository = new ApiHotelHository
        {
            Name = input.Name,
            Code = input.Code,
            CodeType = input.CodeType,
            AddApiHotelStatus = AddApiHotelStatus.InProgress
        };
        await _dbContext.ApiHotelHositorys.AddAsync(huiZhiHotelHository);
        await _dbContext.SaveChangesAsync();
        #endregion

        #region 任务调度分页取数据
        _backgroundJobClient.Schedule<IApiHotelJob>(
                    x => x.AddApiHotel(huiZhiHotelHository.Id, huiZhiHotelHository.TenantId), DateTime.Now);
        #endregion
    }

    /// <summary>
    /// 删除汇智酒店
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Obsolete]
    [UnitOfWork]
    public async Task Delete(DeleteInput input)
    {
        throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        //更新无效汇智酒店的价格分组配置
        var updatePriceSettingsMessage = new UpdateInvalidProductPriceSettingsMessage();

        if (input.Codes is not null && input.Codes.Any())
        {
            var apiHotels = await _dbContext.ApiHotels
                .WhereIF(input.CodeType is ThirdHotelSearchCodeType.City, x => input.Codes.Contains(x.CityCode))
                .WhereIF(input.CodeType is ThirdHotelSearchCodeType.Province, x => input.Codes.Contains(x.ProvinceCode))
                .WhereIF(input.CodeType is ThirdHotelSearchCodeType.Country, x => input.Codes.Contains(x.CountryCode))
                .ToListAsync();
            if (!apiHotels.Any()) return;

            _dbContext.ApiHotels.RemoveRange(apiHotels);
            //updatePriceSettingsMessage.TenantId = apiHotels.First().TenantId;
            updatePriceSettingsMessage.ProductIds = apiHotels.Select(x => x.Id).ToList();
        }
        else
        {
            var apiHotel = await _dbContext.ApiHotels.FindAsync(input.Id);
            if (apiHotel is null) return;

            _dbContext.ApiHotels.Remove(apiHotel);
            //updatePriceSettingsMessage.TenantId = apiHotel.TenantId;
            updatePriceSettingsMessage.ProductIds = new List<long> { apiHotel.Id };
        }

        if (updatePriceSettingsMessage.ProductIds.Any() && updatePriceSettingsMessage.TenantId > 0)
        {
            //更新无效的汇智酒店价格配置
            await _capPublisher.PublishAsync(CapTopics.Product.UpdateInvalidProductPriceSettings,
                updatePriceSettingsMessage);
        }
        await _dbContext.SaveChangesAsync();
    }

    public async Task<List<(long ResourceId, long ApiHotelId)>> GetByResourceIds(params long[] resourceIds)
    {
        var apiHotels = await _dbContext.ApiHotels
            .Where(x => resourceIds.Contains(x.ResourceHotelId))
            .Select(x => new ValueTuple<long, long>(x.ResourceHotelId, x.Id))
            .ToListAsync();
        return apiHotels;
    }

    /// <summary>
    /// 绑定第三方酒店
    /// </summary>
    /// <param name="apiHotelHositoryId"></param>
    /// <returns></returns>
    [Obsolete]
    public async Task<ApiHotelHositoryDto> Bind(long apiHotelHositoryId)
    {
        throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        #region 根据任务记录查询搜索条件
        var apiHotelHository = await _dbContext.ApiHotelHositorys
            .IgnoreQueryFilters()
            .Where(x => x.Id.Equals(apiHotelHositoryId))
            .FirstOrDefaultAsync();

        if (apiHotelHository.AddApiHotelStatus.Equals(AddApiHotelStatus.Finished))
            return _mapper.Map<ApiHotelHositoryDto>(apiHotelHository);
        #endregion

        var body = new
        {
            apiHotelHository.Name,
            apiHotelHository.Code,
            apiHotelHository.CodeType,
            PageIndex = ++apiHotelHository.PageIndex,
            PageSize = 1000
        };
        using var httpContent = new StringContent(JsonConvert.SerializeObject(body), Encoding.UTF8, "application/json");
        var resourceThirdHotels = await _httpClientFactory.InternalPostAsync<PagingModel<Contracts.Common.Resource.DTOs.ThirdHotel.SearchOutput>>(
            _servicesAddress.Resource_SearchThirdHotels(),
            httpContent: httpContent);

        #region 待添加第三方酒店列表去除已添加/不可售的数据
        var thirdHotels = resourceThirdHotels.Data.Where(x => x.IsEnabled).ToList();
        var thirdHotelIds = thirdHotels.Select(x => x.Id).ToList();

        //第三方
        var existApiResourceHotelIds = await _dbContext.ApiHotels
            .Where(x => thirdHotelIds.Contains(x.ResourceHotelId))
            .Select(x => x.ResourceHotelId)
            .ToListAsync();
        thirdHotels.RemoveAll(x => existApiResourceHotelIds.Contains(x.Id));
        #endregion

        //apiHotelExtend

        var apiHotelExtends = await _dbContext.ApiHotelExtends
         .Where(x => thirdHotelIds.Contains(x.ResourceHotelId) && x.ApiHotelSourceType == ApiHotelSourceType.Hop)
         .ToListAsync();

        var insertApiHotelExtend = new List<ApiHotelExtend>();

        var apiHotels = _mapper.Map<List<ApiHotel>>(thirdHotels);
        foreach (var item in apiHotels)
        {
            var thirdHotel = thirdHotels.Where(x => x.Id.Equals(item.ResourceHotelId)).FirstOrDefault();
            item.SetLocation(thirdHotel.Longitude, thirdHotel.Latitude);
            item.Tags = CreateMappingTag(thirdHotel.Tags);

            //add or update apiHotelExtend
            var apiHotelExtend = apiHotelExtends.Find(x => x.ResourceHotelId == thirdHotel.Id);
            if (apiHotelExtend != null)
            {
                apiHotelExtend.Set(thirdHotel.Id, thirdHotel.HopId);
            }
            else
            {
                insertApiHotelExtend.Add(new ApiHotelExtend(thirdHotel.Id, thirdHotel.HopId));
            }
        }
        //更新记录
        if (apiHotelHository.TotalAddQuantity == 0)
            apiHotelHository.TotalAddQuantity = resourceThirdHotels.Total;
        if (apiHotelHository.PageCount == 0)
            apiHotelHository.PageCount = resourceThirdHotels.PageCount;
        apiHotelHository.AddedQuantity += thirdHotels.Count;
        if (apiHotelHository.PageIndex >= resourceThirdHotels.PageCount)
            apiHotelHository.AddApiHotelStatus = AddApiHotelStatus.Finished;


        if (insertApiHotelExtend.Count > 0)
        {
            await _dbContext.ApiHotelExtends.AddRangeAsync(insertApiHotelExtend);
        }

        await _dbContext.AddRangeAsync(apiHotels);
        await _dbContext.SaveChangesAsync();
        await CreateUpdateContact(thirdHotels, apiHotels);
        return _mapper.Map<ApiHotelHositoryDto>(apiHotelHository);
    }

    /// <summary>
    /// 根据资源酒店Id检查酒店是否已经添加
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<List<CheckRefOutput>> CheckRefByResourceHotelIds(CheckRefInput input)
    {
        var apiHotelds = await _dbContext.ApiHotels.AsNoTracking()
            .Where(x => input.HotelIds.Contains(x.ResourceHotelId))
            .Select(x => new { x.Id, x.ResourceHotelId })
            .ToListAsync();

        var resourcehotel = new List<CheckRefOutput>();
        input.HotelIds.ForEach(x =>
        {
            var apiHotel = apiHotelds.FirstOrDefault(a => a.ResourceHotelId.Equals(x));
            resourcehotel.Add(new CheckRefOutput
            {
                ResourceHotelId = x,
                RefStatus = apiHotel is not null ?
                ApiHotelRefStatus.Added : ApiHotelRefStatus.CanAdd,
                HotelId = apiHotel?.Id
            });
        });
        return resourcehotel;
    }

    [Obsolete]
    public async Task SetOnTop(SetOnTopInput input)
    {
        var apiHotel = await _dbContext.ApiHotels.FindAsync(input.Id);
        if (apiHotel is null) return;
        //apiHotel.OnTop = input.OnTop;
        await _dbContext.SaveChangesAsync();
    }

    [UnitOfWork]
    public async Task SetOnTopV2(SetOnTopInput input)
    {
        var apiHotelConfig = await _dbContext.ApiHotelTenantConfigs
            .FirstOrDefaultAsync(x => x.ApiHotelId == input.Id);
        if (apiHotelConfig is null)
        {
            var apiHotel = await _dbContext.ApiHotels.AsNoTracking().FirstOrDefaultAsync(x => x.Id == input.Id);
            apiHotelConfig = new ApiHotelTenantConfig
            {
                ApiHotelId = apiHotel.Id,
                ResourceHotelId = apiHotel.ResourceHotelId,
                OnTop = input.OnTop,
                WeightValue = 0
            };
            await _dbContext.AddAsync(apiHotelConfig);
        }
        else
        {
            apiHotelConfig.OnTop = input.OnTop;
        }
    }

    public async Task SyncApiHotelDetails(Contracts.Common.Hotel.DTOs.Hotel.SyncHotelInfosInput input)
    {
        var apiHotel = await _dbContext.ApiHotels
                    .IgnoreQueryFilters()
                    .Where(x => x.ResourceHotelId == input.Id)
                    .FirstOrDefaultAsync();
        if (apiHotel is not null)
        {
            apiHotel.HopId = input.HopId;
            apiHotel.ZHName = input.ZHName;
            apiHotel.ENName = input.ENName;
            apiHotel.CountryCode = input.CountryCode;
            apiHotel.CountryName = input.CountryName;
            apiHotel.ProvinceCode = input.ProvinceCode;
            apiHotel.ProvinceName = input.ProvinceName;
            apiHotel.CityCode = input.CityCode;
            apiHotel.CityName = input.CityName;
            apiHotel.StarLevel = input.StarLevel;
            apiHotel.Address = input.Address;
            apiHotel.ENAddress = input.ENAddress;
            apiHotel.CoordinateType = input.CoordinateType;
            apiHotel.SetLocation(input.Longitude, input.Latitude);
            apiHotel.UpdateTime = DateTime.Now;
        }
        else
        {
            apiHotel = new ApiHotel
            {
                ResourceHotelId = input.Id,
                HopId = input.HopId,
                ZHName = input.ZHName,
                ENName = input.ENName,
                CountryCode = input.CountryCode,
                CountryName = input.CountryName,
                ProvinceCode = input.ProvinceCode,
                ProvinceName = input.ProvinceName,
                CityCode = input.CityCode,
                CityName = input.CityName,
                StarLevel = input.StarLevel,
                Address = input.Address,
                ENAddress = input.ENAddress,
                CoordinateType = input.CoordinateType,
                UpdateTime = DateTime.Now
            };
            apiHotel.SetLocation(input.Longitude, input.Latitude);
            // 获取hop tags标签 仅新增时获取
            var hopHotelTags = await GetHopHotelTagsResponse(new GetHopHotelTagsInput
            {
                hids = new List<int> { input.HopId }
            });
            if (hopHotelTags?.Any() is true)
            {
                apiHotel.Tags = CreateMappingTag(hopHotelTags.First().tags);
            }
            await _dbContext.ApiHotels.AddAsync(apiHotel);
        }
        if (input.StaffTag.HasValue)
            apiHotel.StaffTag = input.StaffTag.Value;
        if (input.ReunionRoom.HasValue)
            apiHotel.ReunionRoom = input.ReunionRoom.Value;

        await _dbContext.SaveChangesAsync();
    }

    public async Task UpdateTags(List<UpdateTagInput> input)
    {
        var resourceHotelIds = input.Select(x => x.ResourceHotelId).ToList();
        var apiHotels = await _dbContext.ApiHotels
            .Where(x => resourceHotelIds.Contains(x.ResourceHotelId))
            .ToListAsync();
        foreach (var apiHotel in apiHotels)
        {
            var updateTagInput = input.Last(x => x.ResourceHotelId.Equals(apiHotel.ResourceHotelId));
            if (updateTagInput is null)
                continue;

            apiHotel.StaffTag = updateTagInput.StaffTag;
            apiHotel.ReunionRoom = updateTagInput.ReunionRoom;
            apiHotel.Tags = CreateMappingTag(updateTagInput.Tags);
            apiHotel.SaleFlag = updateTagInput.SaleFlag;
        }
        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 批量同步ApiHotel 售卖标签
    /// </summary>
    /// <returns></returns>
    public async Task<SyncHopApiHotelTagsDto> SyncApiHotelTags()
    {
        var result = new SyncHopApiHotelTagsDto();
        string hotelIndexKey = $"{_apiHotelTagUpdateKey}:index";

        var index = await _redisClient.StringGetAsync<long>(hotelIndexKey);

        int changeHotelCount = 0;

        long maxId = await _dbContext.ApiHotels
            .IgnoreQueryFilters()
            .AsNoTracking()
            .MaxAsync(x => x.Id);

        result.MaxId = maxId;

        if (index >= maxId)
        {
            await SyncHopApiHotelTagsDone(result, hotelIndexKey);
            return result;
        }

        var apiHotels = await _dbContext.ApiHotels
           .IgnoreQueryFilters()
           .AsNoTracking()
           .Where(x => x.Id > index)
           .Take(200)
           .OrderBy(x => x.Id)
           .ToListAsync();

        if (apiHotels?.Any() is not true)
        {
            await SyncHopApiHotelTagsDone(result, hotelIndexKey);
            return result;
        }

        // 取当前批次的最大Id，作为下一次的起点,立马缓存
        index = apiHotels.Max(x => x.Id);
        var timeSpan = TimeSpan.FromHours(1);
        await _redisClient.StringSetAsync(hotelIndexKey, index, timeSpan);
        result.Count = apiHotels.Count;

        var hopIds = apiHotels.Select(x => x.HopId).Distinct().ToList();

        var tagsResult = await GetHopHotelTagsResponse(new GetHopHotelTagsInput
        {
            hids = hopIds
        });

        if (tagsResult is null || tagsResult.Any() is not true)
            return result;

        //关联hop结果与apihotel对应的资源酒店id
        var tags = tagsResult.Join(apiHotels, a => a.hid, b => b.HopId,
            (a, b) => new UpdateApiHotelTagsInput
            {
                ResourceHotelId = b.ResourceHotelId,
                Tags = a.tags
            })
            .Distinct()
            .ToList();

        if (tags is null)
            return result;

        result.UpdateCount = UpdateApiHotelTags(apiHotels, tags);
        if (result.UpdateCount > 0)
            await _dbContext.SaveChangesAsync();

        return result;

    }

    private async Task SyncHopApiHotelTagsDone(SyncHopApiHotelTagsDto result, string hotelIndexKey)
    {
        result.Done = true;
        //删除该批次的缓存，下次从头开始
        await _redisClient.KeyDeleteAsync(hotelIndexKey);
    }
    public long UpdateApiHotelTags(IEnumerable<ApiHotel> apiHotels, IEnumerable<UpdateApiHotelTagsInput> input)
    {
        long count = 0;
        var resourceHotelIds = input.Select(x => x.ResourceHotelId).ToList();

        foreach (var apiHotel in apiHotels)
        {
            var updateTagInput = input.FirstOrDefault(x => x.ResourceHotelId == apiHotel.ResourceHotelId);

            if (updateTagInput is null)
                continue;
            if (updateTagInput.Tags?.Any() is not true)
                continue;

            apiHotel.Tags = CreateMappingTag(updateTagInput.Tags);
            apiHotel.UpdateTime = DateTime.Now;
            count++;
        }
        return count;
    }

    private async Task<bool> CheckIsApiSupplier()
    {
        var tenantId = _httpContextAccessor.HttpContext.GetTenantId();
        var body = new
        {
            TenantId = tenantId
        };
        using var httpContent = new StringContent(JsonConvert.SerializeObject(body), Encoding.UTF8, "application/json");

        var result = await _httpClientFactory.InternalPostAsync<List<SupplierApiSettingDto>>(
            _servicesAddress.Tenant_GetApiSettingInfos(),
            null,
            httpContent: httpContent);
        bool resultFlag = false;
        foreach (var item in result.Where(x => x.SupplierApiType.Equals(SupplierApiType.Hop)))
        {
            var appKey = item.SupplierApiSettingFields
                              .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.HopCode.HopKey)?.FieldValue;
            if (!string.IsNullOrWhiteSpace(appKey))
            {
                resultFlag = true;
                break;
            }
        }
        return resultFlag;
    }

    [Obsolete]
    public async Task SetWeightValues(IEnumerable<HotelWeightValueInput> inputs)
    {
        var hotelIds = inputs.Select(x => x.HotelId).Distinct();

        var apiHotels = await _dbContext.ApiHotels
            .IgnoreQueryFilters()
            .Where(x => hotelIds.Contains(x.Id))
            .ToListAsync();

        foreach (var apiHotel in apiHotels)
        {
            var hotelWeight = inputs.FirstOrDefault(x => x.HotelId == apiHotel.Id);
            //apiHotel.WeightValue += hotelWeight.Value;
        }
        await _dbContext.SaveChangesAsync();
    }

    [UnitOfWork]
    public async Task SetWeightValuesV2(IEnumerable<HotelWeightValueInput> inputs)
    {
        var hotelIds = inputs.Select(x => x.HotelId).Distinct();
        var tenantIds = inputs.Select(x => x.TenantId).Distinct();

        var apiHotels = await _dbContext.ApiHotels.AsNoTracking()
            .Where(x => hotelIds.Contains(x.Id))
            .ToListAsync();
        var apiHotelConfigs = await _dbContext.ApiHotelTenantConfigs
            .IgnoreQueryFilters()
            .Where(x => tenantIds.Contains(x.TenantId))
            .Where(x => hotelIds.Contains(x.ApiHotelId))
            .ToListAsync();

        foreach (var tenantId in tenantIds)
        {
            foreach (var apiHotel in apiHotels)
            {
                var hotelWeight = inputs.FirstOrDefault(x => x.HotelId == apiHotel.Id && x.TenantId == tenantId);
                var apiHotelConfig = apiHotelConfigs.FirstOrDefault(x => x.TenantId == tenantId && x.ApiHotelId == apiHotel.Id);
                if (apiHotelConfig == null)
                {
                    apiHotelConfig = new ApiHotelTenantConfig
                    {
                        ApiHotelId = apiHotel.Id,
                        ResourceHotelId = apiHotel.ResourceHotelId,
                        OnTop = false,
                        WeightValue = hotelWeight.Value
                    };
                    apiHotelConfig.SetTenantId(tenantId);
                    await _dbContext.AddAsync(apiHotelConfig);
                }
                else
                {
                    apiHotelConfig.WeightValue += hotelWeight.Value;
                }
            }
        }
    }

    public async Task<List<GetApiHotelDetailOutput>> Detail(GetApiHotelDetailsInput input)
    {
        var result = new List<GetApiHotelDetailOutput>();
        if (input.Ids.Any() is false && input.HopIds.Any() is false)
            return result;
        var query = _dbContext.ApiHotels
            .AsNoTracking()
            .WhereIF(input.Ids.Any(), x => input.Ids.Contains(x.Id))
            .Select(x => x);

        if (input.HopIds.Any())
        {
            var hopLinq = _dbContext.ApiHotelExtends
                .AsNoTracking()
                .Where(x => x.ApiHotelSourceType == ApiHotelSourceType.Hop && input.HopIds.Contains(x.ThirdHotelId));

            query = query.Where(x => hopLinq.Any(h => h.ResourceHotelId == x.ResourceHotelId));
        }

        var apiHotels = await query
            .ToListAsync();

        result = _mapper.Map<List<GetApiHotelDetailOutput>>(apiHotels);
        // hopid setting
        if (result.Any())
        {
            var apiHotelResoureHotelIds = result.Select(x => x.ResourceHotelId);

            if (apiHotelResoureHotelIds != null && apiHotelResoureHotelIds.Any())
            {

                var apiHotelExtends = await _dbContext.ApiHotelExtends.AsNoTracking()
                    .Where(x => apiHotelResoureHotelIds.Contains(x.ResourceHotelId) && x.ApiHotelSourceType == ApiHotelSourceType.Hop)
                    .Select(x => new { x.ResourceHotelId, x.ThirdHotelId })
                    .ToListAsync();
                foreach (var item in result)
                {
                    item.HopId = apiHotelExtends.FirstOrDefault(x => x.ResourceHotelId == item.ResourceHotelId)?.ThirdHotelId ?? -1;
                }
            }
        }
        return result;
    }

    private static ApiHotelTag? CreateMappingTag(string? hopTags)
    {
        if (string.IsNullOrWhiteSpace(hopTags)) return null;
        var hopTagArrs = hopTags.Split(",", StringSplitOptions.RemoveEmptyEntries);
        int tags = 0;
        foreach (var item in hopTagArrs)
        {
            var sellHotelTag = (SellHotelTag)Enum.Parse(typeof(SellHotelTag), item);
            tags |= (int)MappingTag(sellHotelTag);
        }

        if (tags.Equals(0))
            return null;

        return (ApiHotelTag)tags;
    }

    private static ApiHotelTag? CreateMappingTag(List<int> apiTags)
    {
        if (apiTags is null)
            return null;

        int tags = 0;
        foreach (var apiTag in apiTags)
        {
            tags |= (int)MappingTag((SellHotelTag)apiTag);
        }

        if (tags.Equals(0))
            return null;

        return (ApiHotelTag)tags;
    }

    static ApiHotelTag MappingTag(SellHotelTag tag) => tag switch
    {
        SellHotelTag.O => ApiHotelTag.O,
        SellHotelTag.A => ApiHotelTag.A,
        SellHotelTag.B => ApiHotelTag.B,
        SellHotelTag.C => ApiHotelTag.C,
        SellHotelTag.D => ApiHotelTag.D,
        SellHotelTag.E => ApiHotelTag.E,
        _ => throw new NotImplementedException(),
    };

    private static List<SellHotelTag> MappingSellHotelTag(List<ApiHotelTag> apiHotelTags)
    {
        return apiHotelTags.Select(apiHotelTag => apiHotelTag switch
            {
                ApiHotelTag.A => SellHotelTag.A,
                ApiHotelTag.B => SellHotelTag.B,
                ApiHotelTag.C => SellHotelTag.C,
                ApiHotelTag.D => SellHotelTag.D,
                ApiHotelTag.E => SellHotelTag.E,
                ApiHotelTag.O => SellHotelTag.O,
                _ => throw new ArgumentOutOfRangeException()
            })
            .ToList();
    }

    /// <summary>
    /// 创建或更新 FS联系人(汇智酒店)
    /// </summary>
    /// <param name="thirdHotels"></param>
    /// <param name="apiHotels"></param>
    /// <returns></returns>
    private async Task CreateUpdateContact(List<Contracts.Common.Resource.DTOs.ThirdHotel.SearchOutput>? thirdHotels, List<ApiHotel>? apiHotels)
    {

        Regex regexEmail = new Regex("^\\s*([A-Za-z0-9_-]+(\\.\\w+)*@(\\w+\\.)+\\w{2,40})\\s*$");

        if (thirdHotels is not null && thirdHotels.Any()
            && apiHotels is not null && apiHotels.Any())
        {
            thirdHotels = thirdHotels.Where(x => !string.IsNullOrWhiteSpace(x.ReceiptEmail)).ToList();
            var Message = new List<CreateUpdateContactByApiHotelMessage>();
            foreach (var thirdHotel in thirdHotels)
            {

                var receiptEmail = string.Empty;

                var mutilEamil = thirdHotel.ReceiptEmail.Split('|');

                foreach (var email in mutilEamil)
                {
                    string e = email.Trim();
                    if (regexEmail.IsMatch(e))
                    {
                        receiptEmail = e;
                        break;
                    }
                }
                if (string.IsNullOrEmpty(receiptEmail))
                {
                    continue;
                }

                Message.Add(new CreateUpdateContactByApiHotelMessage
                {
                    ENName = thirdHotel.ENName,
                    StarLevel = thirdHotel.StarLevel,
                    HotelType = thirdHotel.HotelType,
                    Telephone = thirdHotel.Telephone,
                    Address = thirdHotel.Address,
                    CountryName = thirdHotel.CountryName,
                    CityName = thirdHotel.CityName,
                    Id = apiHotels.Where(x => x.ResourceHotelId.Equals(thirdHotel.Id)).Select(x => x.Id).FirstOrDefault(),
                    ZHName = thirdHotel.ZHName,
                    ReceiptEmail = receiptEmail,
                });
            }
            if (Message.Count > 0)
            {
                await _capPublisher.PublishAsync(CapTopics.Tenant.FocussendCreateUpdateContactByApiHotel, Message);
            }

        }
    }


    private async Task<List<HopHotelTagsResponse>> GetHopHotelTagsResponse(GetHopHotelTagsInput input)
    {
        var requestUri = new Uri(_huiZhiHotelConfig.BaseUrl + _huiZhiHotelConfig.GetHotelTags);

        var client = _httpClientFactory.CreateClient();
        var request = JsonConvert.SerializeObject(input);
        HttpContent content = new StringContent(request, Encoding.UTF8, "application/json");
        //请求HOP酒店信息
        var response = await client.PostAsync(requestUri: requestUri, content: content);
        response.EnsureSuccessStatusCode();
        var hopHotelResponse = JsonConvert.DeserializeObject<List<HopHotelTagsResponse>>(await response.Content.ReadAsStringAsync());
        return hopHotelResponse;
    }
}
