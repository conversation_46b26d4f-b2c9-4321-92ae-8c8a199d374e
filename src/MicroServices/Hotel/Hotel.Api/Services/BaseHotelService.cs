using Common.ServicesHttpClient;
using Contracts.Common.Hotel.DTOs;
using Contracts.Common.Payment.DTOs.Currency;
using Contracts.Common.Resource.DTOs.GDSHotel;
using Contracts.Common.Resource.DTOs.ThirdHotel;
using Contracts.Common.Tenant.DTOs.Supplier;
using Contracts.Common.Tenant.DTOs.Tenant;
using Contracts.Common.Tenant.DTOs.TenantApiSupplierConfig;
using Hotel.Api.ConfigModel;
using Hotel.Api.Extensions;
using Hotel.Api.Services.Interfaces;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Hotel.Api.Services;

public class BaseHotelService : IBaseHotelService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly HotelPreBookingConfig _hotelPreBookingConfig;
    private readonly ServicesAddress _servicesAddress;

    public BaseHotelService(
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> options,
        IOptions<ConfigModel.HotelPreBookingConfig> hotelPreBookingConfig)
    {
        _httpClientFactory = httpClientFactory;
        _hotelPreBookingConfig = hotelPreBookingConfig.Value;
        _servicesAddress = options.Value;
    }

    /// <summary>
    /// 查汇率
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<List<GetExchangeRateOutput>> GetCurrencyExchangeRateList(List<GetExchangeRatesInput> input)
    {
        var result = new List<GetExchangeRateOutput>();

        //相同币种之前的汇率
        var sameCurrencyExchangeRateList =
            (from item in input
             let baseEqualsTarget =
                 item.BaseCurrencyCode.Equals(item.TargetCurrencyCode, StringComparison.OrdinalIgnoreCase)
             where baseEqualsTarget
             select new GetExchangeRateOutput
             {
                 BaseCurrencyCode = item.BaseCurrencyCode,
                 TargetCurrencyCode = item.TargetCurrencyCode,
                 ExchangeRate = 1
             }).ToList();
        result.AddRange(sameCurrencyExchangeRateList);

        //不同币种之前的汇率
        var differentCurrencyInput =
            (from item in input
             let baseEqualsTarget =
                 item.BaseCurrencyCode.Equals(item.TargetCurrencyCode, StringComparison.OrdinalIgnoreCase)
             where !baseEqualsTarget
             select item).ToList();
        if (differentCurrencyInput.Any())
        {
            var diffCurrencyExchangeRateList = await GetCurrencyRate(differentCurrencyInput);
            result.AddRange(diffCurrencyExchangeRateList);
        }

        return result;
    }

    /// <summary>
    /// 获取租户配置
    /// </summary>
    public async Task<GetTenantSysConfigOutput> GetTenantSysConfig()
    {
        var tenantSysConfig =
            await _httpClientFactory.InternalGetAsync<GetTenantSysConfigOutput>(
                _servicesAddress.Tenant_GetTenantSysConfig());

        return tenantSysConfig;
    }

    /// <summary>
    /// 获取供应商配置
    /// </summary>
    /// <param name="supplierId"></param>
    /// <returns></returns>
    public async Task<GetSupplierOutput> GetSupplierInfo(long supplierId)
    {
        var request = await _httpClientFactory.InternalGetAsync<GetSupplierOutput>(
            requestUri: _servicesAddress.Tenant_GetSupplierInfo(supplierId));
        return request;
    }

    /// <summary>
    /// 获取汇率
    /// </summary>
    public async Task<List<GetExchangeRateOutput>> GetCurrencyRate(List<GetExchangeRatesInput> input)
    {
        var httpContent = new StringContent(JsonConvert.SerializeObject(input),
            Encoding.UTF8,
            "application/json");
        var request = await _httpClientFactory.InternalPostAsync<List<GetExchangeRateOutput>>(
            requestUri: _servicesAddress.Payment_GetCurrencyRate(),
            httpContent: httpContent);
        return request;
    }

    /// <summary>
    /// 获取第三方酒店价格策略信息
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public async Task<GetThirdHotelPriceOutput> GetPriceByThirdPartyHotel(GetThirdHotelPriceInput request)
    {
        var content = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
        var data = await _httpClientFactory.InternalPostAsync<GetThirdHotelPriceOutput>(
            requestUri: _servicesAddress.Resource_ThirdHotel_GetPrice(),
            httpContent: content);
        return data;
    }

    /// <summary>
    /// 批量获取GDS酒店设施
    /// </summary>
    public async Task<List<GDSHotelFacilitiesOutput>> GetGDSFacilities(params long[] hotelIds)
    {
        var content = new StringContent(JsonConvert.SerializeObject(hotelIds), Encoding.UTF8, "application/json");
        var data = await _httpClientFactory.InternalPostAsync<List<GDSHotelFacilitiesOutput>>(
            requestUri: _servicesAddress.Resource_GDS_GetFacilitiesByHotelIds(),
            httpContent: content);
        return data;
    }

    /// <summary>
    /// 第三方酒店 试单
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public async Task<CheckAvailabilityOutput> ThirdPartyHotelCheckAvailability(CheckAvailabilityInput request)
    {
        var content = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
        var data = await _httpClientFactory.InternalPostAsync<CheckAvailabilityOutput>(
            requestUri: _servicesAddress.Resource_ThirdHotel_CheckAvailability(),
            httpContent: content);
        return data;
    }

    public async Task<GetTenantApiSupplierConfigDetailOutput> GetTenantSaasApiSupplierConfig(GetTenantApiSupplierConfigDetailInput request)
    {
        var data = await _httpClientFactory.InternalPostAsync<GetTenantApiSupplierConfigDetailOutput>(
            requestUri: _servicesAddress.Tenant_GetSaasApiSupplierConfig(),
            httpContent: new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json"));
        return data;
    }

    public string CreatePreBookingCode(HotelPreBookingDto dto)
    {
        var data = JsonConvert.SerializeObject(dto);
        var code = Common.Utils.SecurityUtil.AESEncrypt(data, _hotelPreBookingConfig.Key, _hotelPreBookingConfig.IV);
        return code;
    }

    public HotelPreBookingDto? GetPreBookingByCode(string? preBookingCode)
    {
        if (string.IsNullOrWhiteSpace(preBookingCode))
            return null;
        var data = Common.Utils.SecurityUtil.AESDecrypt(preBookingCode, _hotelPreBookingConfig.Key, _hotelPreBookingConfig.IV);
        var result = JsonConvert.DeserializeObject<HotelPreBookingDto>(data);
        return result;
    }

    public HotelPreBookingConfig GetHotelPreBookingConfig()
    {
        return _hotelPreBookingConfig;
    }
}