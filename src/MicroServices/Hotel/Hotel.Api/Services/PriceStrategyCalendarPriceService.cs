using AutoMapper;
using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common.Hotel.DTOs;
using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Hotel.DTOs.PriceStrategy;
using Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
using Contracts.Common.Hotel.DTOs.SupplySetting;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Inventory.DTOs;
using Contracts.Common.Inventory.Enums;
using Contracts.Common.Inventory.Messages;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.DTOs.Currency;
using Contracts.Common.Product.Messages;
using Contracts.Common.Resource.DTOs.ThirdHotel;
using DotNetCore.CAP;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Hotel.Api.Extensions;
using Hotel.Api.Services.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using JsonConvert = Newtonsoft.Json.JsonConvert;

namespace Hotel.Api.Services
{
    public class PriceStrategyCalendarPriceService : IPriceStrategyCalendarPriceService
    {
        private readonly CustomDbContext _dbContext;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ICapPublisher _capPublisher;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ServicesAddress _servicesAddress;
        private readonly IMapper _mapper;
        private readonly IMediator _mediator;
        private readonly ISupplySettingService _supplySettingService;
        private readonly IBaseHotelService _baseHotelService;
        private readonly ILogger<PriceStrategyCalendarPriceService> _logger;

        public PriceStrategyCalendarPriceService(
            CustomDbContext dbContext,
            IHttpContextAccessor httpContextAccessor,
            ICapPublisher capPublisher,
            IHttpClientFactory httpClientFactory,
            IOptions<ServicesAddress> options,
            IMapper mapper,
            IMediator mediator,
            ISupplySettingService supplySettingService,
            IBaseHotelService baseHotelService,
            ILogger<PriceStrategyCalendarPriceService> logger)
        {
            _dbContext = dbContext;
            _httpContextAccessor = httpContextAccessor;
            _capPublisher = capPublisher;
            _httpClientFactory = httpClientFactory;
            _servicesAddress = options.Value;
            _mapper = mapper;
            _mediator = mediator;
            _supplySettingService = supplySettingService;
            _baseHotelService = baseHotelService;
            _logger = logger;
        }

        /// <summary>
        /// 修改基础价格
        /// </summary>
        [UnitOfWork]
        public async Task UpdateBasisPrice(UpdateBasisPriceInput input)
        {
            if (input.CostPriceUpdateType == CostPriceUpdateType.NoChange
                && input.SalePriceUpdateType == SalePriceUpdateType.NoChange
                && !input.TotalQuantity.HasValue
                && !input.Enabled.HasValue)
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

            var priceStrategy = await _dbContext.PriceStrategies.FindAsync(input.PriceStrategyId);
            if (priceStrategy is null)
                throw new BusinessException(ErrorTypes.Hotel.PriceStrategyInvalid);

            if (input.CostPriceUpdateType != CostPriceUpdateType.NoChange
                || input.SalePriceUpdateType != SalePriceUpdateType.NoChange)
            {
                var operatingModel = (await _dbContext.Hotels.FindAsync(priceStrategy.HotelId)).OperatingModel;

                var calendarPrices = await _dbContext.PriceStrategyCalendarPrices
                    .Where(a => a.PriceStrategyId == input.PriceStrategyId
                       && a.Date >= input.BeginDate
                       && a.Date <= input.EndDate
                       && input.ApplicableWeek.Contains(a.Date.DayOfWeek))
                    .ToListAsync();

                //代销酒店 判断是否存在未设置采购价的日历价格
                if (operatingModel == OperatingModel.Agency &&
                    (input.CostPriceUpdateType == CostPriceUpdateType.NoChange
                        || input.SalePriceUpdateType == SalePriceUpdateType.NoChange))
                {
                    var beginDate = input.BeginDate;
                    //计算修改总天数
                    var totalDays = 0;
                    while (beginDate <= input.EndDate)
                    {
                        if (input.ApplicableWeek.Contains(beginDate.DayOfWeek))
                            totalDays++;
                        beginDate = beginDate.AddDays(1);
                    }

                    if (calendarPrices.Count != totalDays)
                        throw new BusinessException(ErrorTypes.Hotel.ExistCalendarPriceWithoutCostPrice);
                }

                //查询供货配置
                var supplySetting = new GetSupplySettingOutput();
                if (input.HasOperationRight.HasValue && !input.HasOperationRight.Value)
                {
                    var supplySettings = (await _supplySettingService.Get(new GetSupplySettingInput
                    {
                        SupplierId = priceStrategy.SupplierId,
                        HotelIds = new List<long>() { priceStrategy.HotelId },
                        IsSupply = true
                    })).ToList();
                    supplySetting = supplySettings.FirstOrDefault();
                }

                //查询供应商货币转商户货币的汇率
                decimal saleExchangeRate = 1;
                if (priceStrategy.SupplierId is > 0)
                {
                    var exchangeRateInfo = await _baseHotelService.GetCurrencyExchangeRateList(new List<GetExchangeRatesInput>
                    {
                        new GetExchangeRatesInput
                        {
                            BaseCurrencyCode = priceStrategy.CostCurrencyCode,
                            TargetCurrencyCode = priceStrategy.SaleCurrencyCode
                        }
                    });
                    saleExchangeRate = exchangeRateInfo.First().ExchangeRate;
                }


                var list = new List<PriceStrategyCalendarPrice>();
                var date = input.BeginDate;
                while (date <= input.EndDate)
                {
                    if (!input.ApplicableWeek.Contains(date.DayOfWeek))
                    {
                        date = date.AddDays(1);
                        continue;
                    }

                    var calendarPrice = calendarPrices.FirstOrDefault(x => x.Date == date);
                    if (calendarPrice is null)
                    {
                        var addCalendarPrice = new PriceStrategyCalendarPrice()
                        {
                            Date = date,
                            HotelId = priceStrategy.HotelId,
                            PriceStrategyId = priceStrategy.Id,
                            CostPrice = input.CostPrice
                        };
                        if (input.HasOperationRight.HasValue && !input.HasOperationRight.Value)
                        {
                            if (supplySetting is not null)
                            {
                                //无运营权供应商需要计算基础售价
                                var supplySettingCalc = CalculateSupplyPrice(new CalcSupplyMarkupValueInput
                                {
                                    MarkupType = supplySetting.MarkupType,
                                    Value = supplySetting.Value,
                                    CostPrice = addCalendarPrice.CostPrice,
                                    SalePrice = addCalendarPrice.SalePrice,
                                    SaleExchangeRate = saleExchangeRate
                                });
                                addCalendarPrice.SalePrice = supplySettingCalc;
                            }
                        }
                        else
                        {
                            if (input.SalePriceUpdateType == SalePriceUpdateType.FixedPrice)
                                addCalendarPrice.SalePrice = input.SalePrice;
                            if (input.SalePriceUpdateType == SalePriceUpdateType.AutoPrice)
                                addCalendarPrice.SalePrice = CalculateSalePrice(addCalendarPrice.CostPrice, input.MarkupType, input.MarkupValue, saleExchangeRate);
                        }
                        list.Add(addCalendarPrice);
                    }
                    else
                    {
                        if (input.CostPriceUpdateType == CostPriceUpdateType.FixedPrice)
                            calendarPrice.CostPrice = input.CostPrice;

                        if (input.HasOperationRight.HasValue && !input.HasOperationRight.Value)
                        {
                            if (supplySetting is not null)
                            {
                                //无运营权供应商需要计算基础售价
                                var supplySettingCalc = CalculateSupplyPrice(new CalcSupplyMarkupValueInput
                                {
                                    MarkupType = supplySetting.MarkupType,
                                    Value = supplySetting.Value,
                                    CostPrice = calendarPrice.CostPrice,
                                    SalePrice = calendarPrice.SalePrice,
                                    SaleExchangeRate = saleExchangeRate
                                });
                                calendarPrice.SalePrice = supplySettingCalc;
                            }
                        }
                        else
                        {
                            if (input.SalePriceUpdateType == SalePriceUpdateType.FixedPrice)
                                calendarPrice.SalePrice = input.SalePrice;
                            if (input.SalePriceUpdateType == SalePriceUpdateType.AutoPrice)
                                calendarPrice.SalePrice = CalculateSalePrice(calendarPrice.CostPrice, input.MarkupType, input.MarkupValue, saleExchangeRate);
                        }
                    }

                    date = date.AddDays(1);
                }
                await _dbContext.PriceStrategyCalendarPrices.AddRangeAsync(list);
            }

            //修改库存
            if (input.TotalQuantity.HasValue || input.Enabled.HasValue)
            {
                var tenantId = priceStrategy.TenantId;
                var command = new BatchUpdateCalendarInventoryMessage()
                {
                    ProductId = priceStrategy.HotelRoomId,
                    StartDate = input.BeginDate,
                    EndDate = input.EndDate,
                    ValidWeeks = input.ApplicableWeek,
                    ItemInfos = new List<ProductItemInfo>()
                    {
                        new  ProductItemInfo()
                        {
                            ItemId = priceStrategy.Id,
                            TotalQuantity = input.TotalQuantity,
                            Enabled = input.Enabled
                        }
                    },
                    TenantId = tenantId
                };

                var httpContent = new StringContent(JsonConvert.SerializeObject(command),
                    Encoding.UTF8, "application/json");
                await _httpClientFactory.InternalPostAsync<string>(
                    _servicesAddress.Inventory_CalendarBatchUpdateInventory(),
                    httpContent: httpContent);

                await _mediator.Publish(new Notification.InventoryChangeNotification
                {
                    TenantId = priceStrategy.TenantId,
                    HotelId = priceStrategy.HotelId,
                    BeginDate = input.BeginDate,
                    EndDate = input.EndDate,
                    Strategies = new[] { priceStrategy.Id }
                });
            }

            await _dbContext.SaveChangesAsync();
            await UpdatePriceChannelSettingMinPrice(priceStrategy.Id, input.BeginDate, input.EndDate);

            //价格策略价格变更通知
            await _mediator.Publish(new Notification.PriceChangeNotification
            {
                TenantId = priceStrategy.TenantId,
                HotelId = priceStrategy.HotelId,
                Strategies = new Notification.StrategyPriceChange[] {
                    new Notification.StrategyPriceChange{ StrategyId = priceStrategy.Id, BeginDate = input.BeginDate, EndDate = input.EndDate }
                }
            });
        }

        /// <summary>
        /// 获取单个报价日历价格
        /// </summary>
        public async Task<GetByPriceStrategyOutput> GetByPriceStrategy(GetByPriceStrategyInput input)
        {
            //获取报价策略
            var priceStrategy = await _dbContext.PriceStrategies.FindAsync(input.PriceStrategyId);
            var result = _mapper.Map<GetByPriceStrategyOutput>(priceStrategy);

            //OTA设置
            var otaEnable = await _dbContext.OtaSettings.AsNoTracking()
                .AnyAsync(a => a.HotelId == priceStrategy.HotelId && a.Enabled);
            //渠道加价(同时获取价格策略可售渠道)
            var channelMarkups = await _dbContext.ChannelMarkups.AsNoTracking()
                .Where(a => a.PriceStrategyId == input.PriceStrategyId && a.Enabled)
                .WhereIF(!otaEnable, a => a.ChannelType <= SellingChannels.TheWorld)
                .ToListAsync();
            result.SaleChannels = channelMarkups.Select(a => a.ChannelType);//可售渠道

            var roomPriceStrategies = new List<TempRoomPriceStrategy>()
            {
                new TempRoomPriceStrategy()
                {
                    HotelRoomId = priceStrategy.HotelRoomId,
                    PriceStrategyId = priceStrategy.Id,
                    CostCurrencyCode = priceStrategy.CostCurrencyCode,
                    SaleCurrencyCode = priceStrategy.SaleCurrencyCode,
                    SupplierId = priceStrategy.SupplierId
                }
            };
            var prices = await GetPriceStrategyPrices(false, roomPriceStrategies,
                input.BeginDate, input.EndDate, input.SalesChannel);

            var price = prices.First();
            result.ProductInventoryType = price.ProductInventoryType;
            if (!price.CalendarPrices.Any())
            {
                var stayDays = input.EndDate.Subtract(input.BeginDate).Days;
                for (int i = 0; i < stayDays; i++)
                {
                    result.Prices.Add(new CalendarPriceItem()
                    {
                        Date = input.BeginDate.AddDays(i)
                    });
                }
            }
            else
            {
                result.Prices = price.CalendarPrices
                    .Select(x => new CalendarPriceItem()
                    {
                        Date = x.Date,
                        AvailableQuantity = x.AvailableQuantity,
                        TotalQuantity = x.TotalQuantity,
                        ChannelPrice = x.ChannelPrice,
                        CostPrice = x.CostPrice,
                        Enabled = x.Enabled,
                        CostCurrencyCode = x.CostCurrencyCode,
                        SaleCurrencyCode = x.SaleCurrencyCode
                    }).ToList();
            }

            return result;
        }

        /// <summary>
        /// 获取酒店日历价格
        /// </summary>
        public async Task<List<GetByHotelOutput>> GetByHotel(GetByHotelInput input)
        {
            //获取房型
            var result = await _dbContext.HotelRooms.AsNoTracking()
                .Where(a => a.HotelId == input.HotelId)
                .OrderBy(a => a.Sort)
                .Select(a => new GetByHotelOutput
                {
                    RoomId = a.Id,
                    RoomName = a.ZHName,
                    RoomQuantity = a.RoomQuantity,
                    MaximumOccupancy = a.MaximumOccupancy,
                    BedType = a.BedType,
                    Viewable = a.Viewable,
                    Description = a.Description
                }).ToListAsync();
            if (!result.Any())
                return result;

            result.ForEach(x =>
            {
                //床型设置
                var bedEnumerable = JsonConvert.DeserializeObject<List<BedType>>(x.BedType)?
                    .Select(x => x.main) ?? new List<string>();
                x.BedType = string.Join(",", bedEnumerable);
            });

            //获取报价策略
            var priceStrategies = await _dbContext.PriceStrategies.AsNoTracking()
                .Where(a => a.HotelId == input.HotelId)
                .WhereIF(input.Enabled.HasValue, a => a.Enabled == input.Enabled.Value)
                .WhereIF(input.SupplierId is > 0, a => a.SupplierId == input.SupplierId.Value)
                .ToListAsync();
            if (!priceStrategies.Any())
                return result;

            //OTA设置
            var otaEnable = await _dbContext.OtaSettings.AsNoTracking()
                .AnyAsync(a => a.HotelId == input.HotelId && a.Enabled);
            //渠道加价(同时获取价格策略可售渠道)
            var channelMarkups = await _dbContext.ChannelMarkups.AsNoTracking()
                .Where(a => a.HotelId == input.HotelId && a.Enabled)
                .WhereIF(!otaEnable, a => a.ChannelType <= SellingChannels.TheWorld)
                .ToListAsync();

            var roomPriceStrategies = priceStrategies.Select(x => new TempRoomPriceStrategy()
            {
                HotelRoomId = x.HotelRoomId,
                PriceStrategyId = x.Id,
                CostCurrencyCode = x.CostCurrencyCode,
                SaleCurrencyCode = x.SaleCurrencyCode,
                SupplierId = x.SupplierId
            }).ToList();
            var prices = await GetPriceStrategyPrices(false, roomPriceStrategies,
                input.BeginDate, input.EndDate, input.SalesChannel);

            var stayDays = input.EndDate.Subtract(input.BeginDate).Days;
            foreach (var item in result)
            {
                //获取房型下的报价策略
                item.PriceStrategies = priceStrategies.Where(a => a.HotelRoomId == item.RoomId)
                    .OrderBy(a => a.PriceStrategyType)
                    .Select(a => _mapper.Map<PriceStrategyItem>(a))
                    .ToList();

                foreach (var priceStrategyItem in item.PriceStrategies)
                {
                    var markups = channelMarkups.Where(a => a.PriceStrategyId == priceStrategyItem.Id).ToList();
                    priceStrategyItem.SaleChannels = markups.Select(a => a.ChannelType);//可售渠道

                    var price = prices.First(x => x.PriceStrategyId == priceStrategyItem.Id);
                    priceStrategyItem.ProductInventoryType = price.ProductInventoryType;
                    if (!price.CalendarPrices.Any())
                    {
                        for (int i = 0; i < stayDays; i++)
                        {
                            priceStrategyItem.Prices.Add(new CalendarPriceItem()
                            {
                                Date = input.BeginDate.AddDays(i)
                            });
                        }
                    }
                    else
                    {
                        priceStrategyItem.Prices = price.CalendarPrices
                            .Select(x => new CalendarPriceItem()
                            {
                                Date = x.Date,
                                AvailableQuantity = x.AvailableQuantity,
                                TotalQuantity = x.TotalQuantity,
                                ChannelPrice = x.ChannelPrice,
                                CostPrice = x.CostPrice,
                                Enabled = x.Enabled,
                                CostCurrencyCode = x.CostCurrencyCode,
                                SaleCurrencyCode = x.SaleCurrencyCode
                            }).ToList();
                    }
                }
            }

            result = result.OrderByDescending(a => a.PriceStrategies.Count).ToList();
            return result;
        }

        /// <summary>
        /// 获取可售报价(微商城)
        /// </summary>
        public async Task<GetSaleByHotelOutput> GetSaleByHotel(GetSaleByHotelInput input)
        {
            var result = new GetSaleByHotelOutput();
            //酒店房型
            var rooms = await _dbContext.HotelRooms.AsNoTracking()
                  .Where(a => a.HotelId == input.HotelId)
                  .OrderBy(x => x.Sort)
                  .Select(r => new StandardRoom
                  {
                      RoomId = r.Id,
                      RoomName = r.ZHName,
                      RoomQuantity = r.RoomQuantity,
                      RoomMaximumOccupancy = r.MaximumOccupancy,
                      BedType = r.BedType,
                      AreaMax = r.AreaMax,
                      AreaMin = r.AreaMin,
                      FloorMax = r.FloorMax,
                      FloorMin = r.FloorMin,
                      WindowType = r.WindowType,
                      ResourceRoomId = r.ResourceRoomId,
                      Description = r.Description
                  })
                  .ToListAsync();
            if (!rooms.Any())
                return result;

            //房型图片 床型
            var roomPhotos = await _dbContext.HotelPhotos.AsNoTracking()
                .Where(a => a.HotelId == input.HotelId && a.Sort == 0 && a.Enabled)
                .Select(a => new { a.HotelRoomId, a.Path })
                .ToListAsync();
            rooms.ForEach(x =>
            {
                var convertBedType = ConvertBedType(x.BedType);
                x.BedType = convertBedType.mainBedType;
                x.BedTypes = convertBedType.bedTypes;
                x.Photo = roomPhotos.FirstOrDefault(w => w.HotelRoomId == x.RoomId)?.Path ?? "";
            });

            //获取可售价格策略
            var hotelIds = new List<long>() { input.HotelId };
            var salePriceStrategies = await SalePriceStrategyPrice(hotelIds, input.BeginDate, input.EndDate, input.SalesChannel, true);
            if (!salePriceStrategies.Any())
                return result;

            //取消政策
            var priceStrategyIds = salePriceStrategies.Select(x => x.PriceStrategyId).ToList();
            var cancelRules = await _dbContext.PriceStrategyCancelRules.AsNoTracking()
                .Where(a => priceStrategyIds.Contains(a.PriceStrategyId))
                .ToListAsync();
            salePriceStrategies.ForEach(x =>
            {
                var rules = cancelRules.FirstOrDefault(a => a.PriceStrategyId == x.PriceStrategyId);
                x.PriceStrategy.CancelRule = _mapper.Map<CancelRule>(rules) ?? new CancelRule();
            });

            foreach (var room in rooms)
            {
                var roomPriceStrategy = salePriceStrategies.Where(x => x.RoomId == room.RoomId)
                    .Select(x => x.PriceStrategy)
                    .ToList();

                foreach (var strategy in roomPriceStrategy)
                {
                    switch (strategy.PriceStrategyType)
                    {
                        case PriceStrategyType.HourRoom:
                            {
                                //钟点房
                                var hourRoom = _mapper.Map<HourRoomPriceStrategy>(strategy);
                                hourRoom.Photo = room.Photo;
                                hourRoom.AreaMax = room.AreaMax;
                                hourRoom.AreaMin = room.AreaMin;
                                hourRoom.BedType = room.BedType;
                                hourRoom.WindowType = room.WindowType;
                                hourRoom.RoomId = room.RoomId;
                                hourRoom.RoomName = room.RoomName;
                                hourRoom.RoomQuantity = room.RoomQuantity;
                                hourRoom.FloorMin = room.FloorMin;
                                hourRoom.FloorMax = room.FloorMax;
                                hourRoom.RoomMaximumOccupancy = room.RoomMaximumOccupancy;
                                hourRoom.BedTypes = room.BedTypes;
                                hourRoom.DatePrices = hourRoom.DatePrices.Where(x => x.Date == input.BeginDate).ToList();
                                hourRoom.ResourceRoomId = room.ResourceRoomId;
                                hourRoom.Description = room.Description;
                                result.HourRooms.Add(hourRoom);
                            }
                            break;
                        case PriceStrategyType.StayLongDiscount:
                            {
                                //连住房
                                var stayLongDiscountRoom = _mapper.Map<StayLongDiscountRoomPriceStrategy>(strategy);
                                stayLongDiscountRoom.Photo = room.Photo;
                                stayLongDiscountRoom.AreaMax = room.AreaMax;
                                stayLongDiscountRoom.AreaMin = room.AreaMin;
                                stayLongDiscountRoom.BedType = room.BedType;
                                stayLongDiscountRoom.WindowType = room.WindowType;
                                stayLongDiscountRoom.RoomId = room.RoomId;
                                stayLongDiscountRoom.RoomName = room.RoomName;
                                stayLongDiscountRoom.RoomQuantity = room.RoomQuantity;
                                stayLongDiscountRoom.FloorMin = room.FloorMin;
                                stayLongDiscountRoom.FloorMax = room.FloorMax;
                                stayLongDiscountRoom.RoomMaximumOccupancy = room.RoomMaximumOccupancy;
                                stayLongDiscountRoom.BedTypes = room.BedTypes;
                                stayLongDiscountRoom.ResourceRoomId = room.ResourceRoomId;
                                stayLongDiscountRoom.Description = room.Description;
                                result.StayLongDiscountRooms.Add(stayLongDiscountRoom);
                            }
                            break;
                        case PriceStrategyType.GroupRoom:
                            {
                                //团房
                                var groupRoom = _mapper.Map<GroupRoomPriceStrategy>(strategy);
                                groupRoom.Photo = room.Photo;
                                groupRoom.AreaMax = room.AreaMax;
                                groupRoom.AreaMin = room.AreaMin;
                                groupRoom.BedType = room.BedType;
                                groupRoom.WindowType = room.WindowType;
                                groupRoom.RoomId = room.RoomId;
                                groupRoom.RoomName = room.RoomName;
                                groupRoom.RoomQuantity = room.RoomQuantity;
                                groupRoom.FloorMin = room.FloorMin;
                                groupRoom.FloorMax = room.FloorMax;
                                groupRoom.RoomMaximumOccupancy = room.RoomMaximumOccupancy;
                                groupRoom.BedTypes = room.BedTypes;
                                groupRoom.ResourceRoomId = room.ResourceRoomId;
                                groupRoom.Description = room.Description;
                                result.GroupRooms.Add(groupRoom);
                            }
                            break;
                        default:
                            {
                                //标准房
                                var standardRoomPriceStrategy = _mapper.Map<StandardRoomPriceStrategy>(strategy);
                                room.PriceStrategies.Add(standardRoomPriceStrategy);
                            }
                            break;
                    }
                }

                if (room.PriceStrategies.Count == 0)
                    continue;

                room.Price = room.PriceStrategies.Min(a => a.Amount);
                room.SaleCurrencyCode = room.PriceStrategies.First().SaleCurrencyCode;
                result.Rooms.Add(room);
            }

            result.Rooms = result.Rooms.Where(a => a.Price > 0).ToList();
            return result;
        }

        /// <summary>
        /// 通过酒店Id、价格策略Id、时间区间 获取价格，库存，房态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetPriceInventoryStatusOutput> GetPriceInventoryStatus(GetPriceInventoryStatusInput input)
        {
            //查询酒店信息
            var infoData = await _dbContext.Hotels.AsNoTracking()
                .Where(x => x.Id == input.HotelId && x.Enabled)
                .Select(x => new GetPriceInventoryStatusOutput { HotelId = x.Id, HotelName = x.ZHName ?? "" })
                .FirstOrDefaultAsync();
            if (infoData is null) return new GetPriceInventoryStatusOutput();

            //查询房型信息
            var roomList = await _dbContext.HotelRooms.AsNoTracking()
                .Where(x => x.HotelId == input.HotelId)
                .Select(x => new PriceInventoryStatusRoomsInfo
                {
                    RoomId = x.Id,
                    RoomName = x.ZHName ?? ""
                }).ToListAsync();
            if (!roomList.Any()) return infoData;
            infoData.Rooms = roomList;

            //获取报价策略只出标准房
            var priceStrategies = await _dbContext.PriceStrategies.AsNoTracking()
                .Where(a => a.HotelId == input.HotelId && a.PriceStrategyType == PriceStrategyType.StandardRoom)
                .WhereIF(input.PriceStrategyIds != null && input.PriceStrategyIds.Any(),
                    x => input.PriceStrategyIds.Contains(x.Id))
                .ToListAsync();
            if (!priceStrategies.Any())
                return infoData;

            //获取价格策略日历价格
            var roomPriceStrategies = priceStrategies.Select(x => new TempRoomPriceStrategy()
            {
                HotelRoomId = x.HotelRoomId,
                PriceStrategyId = x.Id,
                CostCurrencyCode = x.CostCurrencyCode,
                SaleCurrencyCode = x.SaleCurrencyCode,
                SupplierId = x.SupplierId
            }).ToList();
            var prices = await GetPriceStrategyPrices(input.IsSale, roomPriceStrategies,
                input.BeginDate, input.EndDate, input.SalesChannel);

            var stayDays = input.EndDate.Subtract(input.BeginDate).Days;
            //遍历房型
            foreach (var item in infoData.Rooms)
            {
                //获取房型下的报价策略
                var priceStrategyItems = priceStrategies.Where(a => a.HotelRoomId == item.RoomId)
                    .OrderBy(a => a.PriceStrategyType)
                    .Select(a => _mapper.Map<PriceInventoryStatusBaseInfo>(a))
                    .ToList();

                //遍历房型下的报价策略
                foreach (var strategyItem in priceStrategyItems)
                {
                    //获取对应的日历价格
                    var price = prices.FirstOrDefault(x => x.PriceStrategyId == strategyItem.PriceStrategyId);
                    if (!price.CalendarPrices.Any())
                    {
                        for (int i = 0; i < stayDays; i++)
                        {
                            strategyItem.DatePrices.Add(new DatePriceInventoryStatus()
                            {
                                Date = input.BeginDate.AddDays(i)
                            });
                        }
                    }
                    else
                    {
                        if (input.IsSale)
                        {
                            //是否存在价格为0或者null的日历价格。存在则表示该策略不可售
                            if (price.CalendarPrices.Exists(x => x.ChannelPrice is null or <= 0))
                            {
                                strategyItem.DatePrices = new List<DatePriceInventoryStatus>();
                                continue;
                            }
                        }

                        strategyItem.DatePrices = price.CalendarPrices.Select(x => new DatePriceInventoryStatus()
                        {
                            Date = x.Date,
                            Quantity = x.AvailableQuantity,
                            Price = x.ChannelPrice ?? 0,
                            Status = x.Enabled,
                            SaleCurrencyCode = x.SaleCurrencyCode
                        }).ToList();
                    }
                    item.PriceStrategies.Add(strategyItem);
                }
            }
            return infoData;
        }

        /// <summary>
        /// 获取价格策略指定日期所有售卖渠道价格
        /// </summary>
        public async Task<List<GetSaleChannelsPriceOutput>> GetSaleChannelsPrice(GetSaleChannelsPriceInput input)
        {
            var list = new List<GetSaleChannelsPriceOutput>();
            //日历价格
            var calendarPrice = await _dbContext.PriceStrategyCalendarPrices.AsNoTracking()
                .FirstOrDefaultAsync(x => x.PriceStrategyId == input.PriceStrategyId
                    && x.Date == input.Date);
            if (calendarPrice is null)
                return list;

            //查询汇率
            var strategyAndRate = await GetPriceStrategyAndExchangeRate(new List<long> { input.PriceStrategyId });

            if (strategyAndRate.priceStrategyList.Any() is false)
                return list;
            var priceStrategy = strategyAndRate.priceStrategyList.First();

            decimal saleExchangeRate = 1;
            //代销酒店展示采购价.查询汇率
            var operatingModel = (await _dbContext.Hotels.FindAsync(input.HotelId)).OperatingModel;
            if (operatingModel is OperatingModel.Agency && priceStrategy.SupplierId > 0)
            {
                //查询汇率
                saleExchangeRate = strategyAndRate.exchangeRateList.First().ExchangeRate;
            }

            //OTA设置
            var otaSetting = await _dbContext.OtaSettings.AsNoTracking()
                .FirstOrDefaultAsync(x => x.HotelId == input.HotelId);
            var values = Enum.GetValues<SellingChannels>().ToList();
            var myEnum = SellingChannels.B2b | SellingChannels.WechatMall | SellingChannels.TheWorld;
            if (otaSetting is null || !otaSetting.Enabled)
                values = values.Where(s => (myEnum & s) == s).ToList();

            //加价数据
            var markups = await _dbContext.ChannelMarkups.AsNoTracking()
                .Where(x => x.PriceStrategyId == input.PriceStrategyId
                    && x.Enabled
                    && values.Contains(x.ChannelType))
                .ToListAsync();

            foreach (var item in markups)
            {
                var model = new GetSaleChannelsPriceOutput
                {
                    SaleChannel = item.ChannelType,
                    Price = CalculateChannelPrice(item, calendarPrice.SalePrice, calendarPrice.CostPrice, saleExchangeRate),
                    PriceCurrencyCode = priceStrategy.SaleCurrencyCode
                };

                list.Add(model);
            }

            return list;
        }

        public async ValueTask<IEnumerable<QuerySaleChanelsPriceOutput>> QuerySaleChanelsPrice(QuerySaleChanelsPriceInput input)
        {
            var result = new List<QuerySaleChanelsPriceOutput>();
            var calendarPrices = await _dbContext.PriceStrategyCalendarPrices
                .Where(x => x.HotelId == input.HotelId && input.PriceStartegyIds.Contains(x.PriceStrategyId)
                && x.Date >= input.BeginDate && x.Date <= input.EndDate)
                .ToListAsync();
            if (!calendarPrices.Any()) return result;
            var strategyAndRates = await GetPriceStrategyAndExchangeRate(input.PriceStartegyIds);
            decimal saleExchangeRate = 1;
            //代销酒店展示采购价.查询汇率
            var operatingModel = (await _dbContext.Hotels.FindAsync(input.HotelId)).OperatingModel;

            var channelMarkups = await _dbContext.ChannelMarkups.AsNoTracking()
                .Where(x => input.PriceStartegyIds.Contains(x.PriceStrategyId)
                    && x.Enabled
                    && input.SellingChannels.Contains(x.ChannelType))
                .ToListAsync();
            var days = input.EndDate.Subtract(input.BeginDate).TotalDays;
            foreach (var priceStartegyId in input.PriceStartegyIds)
            {
                var priceStrategy = strategyAndRates.priceStrategyList.First(x => x.Id == priceStartegyId);
                if (operatingModel is OperatingModel.Agency && priceStrategy.SupplierId > 0)
                {
                    saleExchangeRate = strategyAndRates.exchangeRateList
                        .Where(x => x.BaseCurrencyCode == priceStrategy.CostCurrencyCode
                            && x.TargetCurrencyCode == priceStrategy.SaleCurrencyCode)
                        .First()
                        .ExchangeRate;
                }
                var prices = calendarPrices.Where(x => x.PriceStrategyId == priceStartegyId);
                var markups = channelMarkups.Where(x => x.PriceStrategyId == priceStartegyId);
                List<SaleChanelsPriceOutput> saleChanelsPrices = new();
                foreach (var item in markups)
                {
                    for (int i = 0; i < days; i++)
                    {
                        var date = input.BeginDate.AddDays(i);
                        var calendarPrice = prices.FirstOrDefault(x => x.Date == date);
                        SaleChanelsPriceOutput priceOutput = new()
                        {
                            Date = date,
                            CurrencyCode = priceStrategy.SaleCurrencyCode,
                            Price = CalculateChannelPrice(item, calendarPrice.SalePrice, calendarPrice.CostPrice, saleExchangeRate),
                            SellingChannels = item.ChannelType
                        };
                        saleChanelsPrices.Add(priceOutput);
                    }
                }
                QuerySaleChanelsPriceOutput querySaleChanelsPriceOutput = new QuerySaleChanelsPriceOutput()
                {
                    PriceStrategyId = priceStartegyId,
                    SaleChanelsPrices = saleChanelsPrices
                };
                result.Add(querySaleChanelsPriceOutput);
            }
            return result;
        }

        /// <summary>
        /// 获取近30天的最低价或最高价
        /// </summary>
        /// <param name="type">max:最高价，min:最低价</param>
        public async Task<List<GetRecentPriceOutput>> GetRecentMinOrMaxPrice(List<long> hotelIds, RecentPriceType type)
        {
            var result = new List<GetRecentPriceOutput>();

            var prices = await _dbContext.PriceStrategyCalendarPrices.AsNoTracking()
              .Where(x => hotelIds.Contains(x.HotelId)
                  && x.Date >= DateTime.Today
                  && x.Date <= DateTime.Today.AddDays(30)
                  && x.SalePrice > 0)
              .GroupBy(x => x.PriceStrategyId)
              .Select(x => new
              {
                  PriceStrategyId = x.Key,
                  CostPrice = type == RecentPriceType.Max ? x.Max(w => w.CostPrice) : x.Min(w => w.CostPrice),
                  SalePrice = type == RecentPriceType.Max ? x.Max(w => w.SalePrice) : x.Min(w => w.SalePrice)
              })
              .ToListAsync();
            if (!prices.Any())
                return result;

            var markups = await _dbContext.ChannelMarkups.AsNoTracking()
                .Where(x => hotelIds.Contains(x.HotelId) && x.Enabled)
                .ToListAsync();

            var priceStrategyIds = prices.Select(x => x.PriceStrategyId).ToList();

            //查询汇率
            var strategyAndRate = await GetPriceStrategyAndExchangeRate(priceStrategyIds);

            foreach (var item in prices)
            {
                var markup = markups.FirstOrDefault(x => x.PriceStrategyId == item.PriceStrategyId);

                if (markup is null)
                    continue;

                //查询当前价格策略
                var priceStrategyItem = strategyAndRate.priceStrategyList.FirstOrDefault(x => x.Id == item.PriceStrategyId);
                if (priceStrategyItem is null)
                    continue;

                //获取当前价格策略汇率
                decimal exchangeRate = 1;
                if (priceStrategyItem.SupplierId > 0)
                {
                    exchangeRate = strategyAndRate.exchangeRateList.First(x =>
                        x.BaseCurrencyCode == priceStrategyItem.CostCurrencyCode
                        && x.TargetCurrencyCode == priceStrategyItem.SaleCurrencyCode).ExchangeRate;
                }

                result.Add(new GetRecentPriceOutput()
                {
                    HotelId = markup.HotelId,
                    PriceStrategyId = item.PriceStrategyId,
                    Price = CalculateChannelPrice(markup, item.SalePrice, item.CostPrice, exchangeRate),
                    SaleCurrencyCode = priceStrategyItem.SaleCurrencyCode
                });
            }

            return result;
        }

        /// <summary>
        /// 批量获取酒店可售价格
        /// </summary>
        public async Task<List<SalePriceStrategyPrice>> SalePriceStrategyPrice(
            List<long> hotelIds,
            DateTime beginDate,
            DateTime endDate,
            SellingChannels salesChannel,
            bool needZeroAvailableQuantity = false)
        {
            var list = new List<SalePriceStrategyPrice>();

            //获取酒店信息
            var hotels = await _dbContext.Hotels.AsNoTracking()
                .Where(x => hotelIds.Contains(x.Id) && x.Enabled)
                .Select(x => new
                {
                    x.Id,
                    x.ResourceHotelId,
                    x.ServiceTimeBegin,
                    x.ServiceTimeEnd,
                    x.ServiceEndtimeInNextDay
                })
                .ToListAsync();
            if (!hotels.Any())
                return list;

            var stayDays = endDate.Subtract(beginDate).Days;
            //获取可售价格策略
            var priceStrategies = await _dbContext.PriceStrategies.AsNoTracking()
                .Where(a => hotelIds.Contains(a.HotelId) && a.Enabled)
                .ToListAsync();
            //移除不符合提前预订时间的价格策略
            var nowTime = DateTime.Now;
            priceStrategies.RemoveAll(x => x.BookingHoursInAdvance > 0
                && beginDate.AddHours(24 - x.BookingHoursInAdvance) < nowTime);
            if (!priceStrategies.Any())
                return list;
            var priceStrategyIds = priceStrategies.Select(x => x.Id);
            var nationalities = await _dbContext.PriceStrategyNationalities.AsNoTracking()
                .Where(x => priceStrategyIds.Contains(x.PriceStrategyId))
                .ToListAsync();
            var roomPriceStrategies = priceStrategies.Select(x => new TempRoomPriceStrategy()
            {
                HotelRoomId = x.HotelRoomId,
                PriceStrategyId = x.Id,
                CostCurrencyCode = x.CostCurrencyCode,
                SaleCurrencyCode = x.SaleCurrencyCode,
                SupplierId = x.SupplierId
            }).ToList();
            var prices = await GetPriceStrategyPrices(true, roomPriceStrategies,
                beginDate, endDate, salesChannel);

            foreach (var hotel in hotels)
            {
                //酒店营业结束时间
                var serviceTime = hotel.ServiceEndtimeInNextDay
                    ? beginDate.AddDays(1).Add(hotel.ServiceTimeEnd)
                    : beginDate.Add(hotel.ServiceTimeEnd);

                var tempPriceStrategies = priceStrategies.Where(x => x.HotelId == hotel.Id);
                foreach (var priceStrategy in tempPriceStrategies)
                {
                    //尾房售卖时间处理
                    if (priceStrategy.PriceStrategyType == PriceStrategyType.EndRoom)
                    {
                        var start = beginDate.Add(priceStrategy.EndRoomBookingTimeBegin);
                        if (priceStrategy.EndRoomBookingDateBegin == 1)
                            start = start.AddDays(1);
                        var end = beginDate.Add(priceStrategy.EndRoomBookingTimeEnd);
                        if (priceStrategy.EndRoomBookingDateEnd == 1)
                            end = end.AddDays(1);

                        if (end > serviceTime)
                            end = serviceTime;

                        if (start > DateTime.Now || end < DateTime.Now)
                            continue;
                    }

                    var price = prices.First(x => x.PriceStrategyId == priceStrategy.Id);
                    if (price.Code != GetPriceErrorCode.Success)
                        continue;

                    var enabledDays = price.CalendarPrices.Count(x => x.Enabled && x.ChannelPrice > 0
                        && (price.OverSaleable || (!price.OverSaleable && (x.AvailableQuantity > 0 || needZeroAvailableQuantity == true))));
                    if (enabledDays != stayDays)
                        continue;

                    var datePrices = price.CalendarPrices.Select(x => new DatePrice()
                    {
                        Date = x.Date,
                        Price = x.ChannelPrice.Value,
                        Stock = x.AvailableQuantity,
                        CostCurrencyCode = x.CostCurrencyCode,
                        SaleCurrencyCode = x.SaleCurrencyCode
                    }).ToList();
                    var saleCurrencyCode = datePrices.First().SaleCurrencyCode;
                    var roomPriceStrategy = _mapper.Map<RoomPriceStrategy>(priceStrategy);
                    roomPriceStrategy.DatePrices = datePrices;
                    roomPriceStrategy.SaleCurrencyCode = saleCurrencyCode;
                    roomPriceStrategy.Amount = datePrices.Sum(x => x.Price);
                    roomPriceStrategy.OverSaleable = price.OverSaleable;
                    roomPriceStrategy.NationalNames = nationalities.Where(n => n.PriceStrategyId == priceStrategy.Id)
                        .Select(x => x.ZHName);

                    list.Add(new SalePriceStrategyPrice()
                    {
                        HotelId = priceStrategy.HotelId,
                        RoomId = priceStrategy.HotelRoomId,
                        PriceStrategyId = priceStrategy.Id,
                        Price = datePrices.First().Price,
                        MinPrice = datePrices.Min(m => m.Price),
                        SaleCurrencyCode = saleCurrencyCode,
                        PriceStrategy = roomPriceStrategy
                    });
                }
            }

            list = list.OrderBy(x => x.PriceStrategy.Amount).ToList();
            return list;
        }

        /// <summary>
        /// 检验价格策略是否可售
        /// </summary>
        public async Task<CheckPriceStrategySaleOutput> CheckPriceStrategySale(CheckPriceStrategySaleInput input)
        {
            var data = new CheckPriceStrategySaleData();
            var result = new CheckPriceStrategySaleOutput() { Data = data };

            var stayDays = (int)input.EndDate.Subtract(input.BeginDate).TotalDays;
            var priceStrategy = await _dbContext.PriceStrategies.AsNoTracking()
                .FirstOrDefaultAsync(a => a.Id == input.PriceStrategyId && a.Enabled);
            if (priceStrategy is null)
            {
                result.Code = CheckPriceStrategySaleCode.PriceStrategyInvalid;
                result.Message = "房型不可售";
                return result;
            }

            if (priceStrategy.BookingHoursInAdvance > 0
                && input.BeginDate.AddHours(24 - priceStrategy.BookingHoursInAdvance) < DateTime.Now)
            {
                result.Code = CheckPriceStrategySaleCode.AdvanceHoursNotEnough;
                result.Message = $"房型需提前{priceStrategy.BookingHoursInAdvance}小时预订";
                return result;
            }

            var hotel = await _dbContext.Hotels.AsNoTracking()
                .FirstOrDefaultAsync(a => a.Id == priceStrategy.HotelId && a.Enabled);
            if (hotel is null)
            {
                result.Code = CheckPriceStrategySaleCode.HotelInvalid;
                result.Message = "房型不可售";
                return result;
            }
            data.HotelId = hotel.Id;
            data.HotelName = hotel.ZHName;
            data.HotelEnName = hotel.ENName;
            data.IsAutoConfirmRoomStatus = hotel.IsAutoConfirmRoomStatus;
            data.OperatingModel = hotel.OperatingModel;

            switch (priceStrategy.PriceStrategyType)
            {
                //尾房、钟点房时间判断
                case PriceStrategyType.EndRoom:
                    {
                        //营业结束时间
                        var serviceTime = hotel.ServiceEndtimeInNextDay
                            ? input.BeginDate.AddDays(1).Add(hotel.ServiceTimeEnd)
                            : input.BeginDate.Add(hotel.ServiceTimeEnd);

                        var start = input.BeginDate.Add(priceStrategy.EndRoomBookingTimeBegin);
                        if (priceStrategy.EndRoomBookingDateBegin == 1)
                            start = start.AddDays(1);
                        var end = input.BeginDate.Add(priceStrategy.EndRoomBookingTimeEnd);
                        if (priceStrategy.EndRoomBookingDateEnd == 1)
                            end = end.AddDays(1);

                        if (end > serviceTime)
                            end = serviceTime;

                        if (start >= DateTime.Now || end <= DateTime.Now)
                        {
                            result.Code = CheckPriceStrategySaleCode.EndRoomNotBook;
                            result.Message = "房型不可售";
                            return result;
                        }

                        break;
                    }
                case PriceStrategyType.HourRoom when stayDays != 1:
                    result.Code = CheckPriceStrategySaleCode.HourRoomNotBook;
                    result.Message = "房型不可售";
                    return result;
                case PriceStrategyType.StayLongDiscount when stayDays < priceStrategy.NumberOfNights:
                    result.Code = CheckPriceStrategySaleCode.StayLongDiscountNotBook;
                    result.Message = "房型不可售";
                    return result;
                case PriceStrategyType.GroupRoom when input.Quantity < priceStrategy.NumberOfRooms:
                    result.Code = CheckPriceStrategySaleCode.NumberOfRoomsNotEnough;
                    result.Message = "最低起订间数不足";
                    return result;
            }

            var room = await _dbContext.HotelRooms.AsNoTracking()
                .FirstOrDefaultAsync(a => a.Id == priceStrategy.HotelRoomId);
            if (room is null)
            {
                result.Code = CheckPriceStrategySaleCode.RoomInvalid;
                result.Message = "房型不可售";
                return result;
            }

            var convertBedType = ConvertBedType(room.BedType);
            room.BedType = convertBedType.mainBedType;

            data.Room = _mapper.Map<CheckPriceStrategySale_Room>(room);
            data.Room.Name = room.ZHName;
            data.Room.EnName = room.ENName;
            data.Room.BedTypes = convertBedType.bedTypes;
            //房型图片
            data.Room.HotelRoomImgPath = await _dbContext.HotelPhotos
                .Where(x => x.HotelRoomId == room.Id && x.HotelId == room.HotelId)
                .Select(x => x.Path)
                .FirstOrDefaultAsync();

            #region 库存价格判断
            var roomPriceStrategies = new List<TempRoomPriceStrategy>()
            {
                new TempRoomPriceStrategy()
                {
                    HotelRoomId = priceStrategy.HotelRoomId,
                    PriceStrategyId = priceStrategy.Id,
                    CostCurrencyCode = priceStrategy.CostCurrencyCode,
                    SaleCurrencyCode = priceStrategy.SaleCurrencyCode,
                    SupplierId = priceStrategy.SupplierId
                }
            };
            var prices = await GetPriceStrategyPrices(true, roomPriceStrategies,
                input.BeginDate, input.EndDate, input.SalesChannel);
            var price = prices.First();
            if (price.Code == GetPriceErrorCode.ChannelNotSale)
            {
                result.Code = CheckPriceStrategySaleCode.ChannelNotSale;
                result.Message = "房型不可售";
                return result;
            }
            else if (price.Code == GetPriceErrorCode.InventoryNotSetup)
            {
                result.Code = CheckPriceStrategySaleCode.InventoryNotEnough;
                result.Message = "房量不足";
                return result;
            }
            else if (price.Code == GetPriceErrorCode.PriceNotSetup)
            {
                result.Code = CheckPriceStrategySaleCode.PricesNotEnough;
                result.Message = "房量不足";
                return result;
            }

            if (price.CalendarPrices.Any(x => x.ChannelPrice <= 0))
            {
                result.Code = CheckPriceStrategySaleCode.PricesNotEnough;
                result.Message = "房量不足";
                return result;
            }
            if (price.CalendarPrices.Any(x => !x.Enabled || (!price.OverSaleable && x.AvailableQuantity < input.Quantity)))
            {
                result.Code = CheckPriceStrategySaleCode.InventoryNotEnough;
                result.Message = "房量不足";
                return result;
            }
            #endregion

            var salePriceStrategy = _mapper.Map<CheckPriceStrategySale_PriceStrategy>(priceStrategy);
            salePriceStrategy.OverSaleable = price.OverSaleable;
            //取消政策
            var cancelRule = await _dbContext.PriceStrategyCancelRules.AsNoTracking()
                .Where(a => priceStrategy.Id == a.PriceStrategyId)
                .FirstOrDefaultAsync();
            salePriceStrategy.CancelRule = _mapper.Map<CancelRule>(cancelRule) ?? new CancelRule();
            salePriceStrategy.DatePrice = price.CalendarPrices.Select(x => new DatePrice()
            {
                Date = x.Date,
                Cost = x.CostPrice,
                Price = x.ChannelPrice.Value,
                Stock = x.AvailableQuantity,
                SaleCurrencyCode = x.SaleCurrencyCode,
                CostCurrencyCode = x.CostCurrencyCode
            }).ToList();
            data.PriceStrategy = salePriceStrategy;
            return result;
        }

        public async Task<CheckSaleOutput> CheckSaleByLocalHotel(CheckSaleInput input)
        {
            var priceStrategyId = long.Parse(input.PriceStrategyId);
            var localHotelCheckResult = await CheckPriceStrategySale(
                new CheckPriceStrategySaleInput
                {
                    HotelId = input.HotelId,
                    PriceStrategyId = priceStrategyId,
                    SalesChannel = input.SalesChannel,
                    BeginDate = input.BeginDate,
                    EndDate = input.EndDate,
                    Quantity = input.Quantity
                });
            CheckSaleOutput result = new()
            {
                Code = localHotelCheckResult.Code,
                Message = localHotelCheckResult.Message,
                Data = new CheckSaleData()
            };
            result.Data.HotelId = localHotelCheckResult.Data.HotelId;
            result.Data.HotelName = localHotelCheckResult.Data.HotelName;
            result.Data.HotelEnName = localHotelCheckResult.Data.HotelEnName;
            result.Data.OperatingModel = localHotelCheckResult.Data.OperatingModel;
            result.Data.IsAutoConfirmRoomStatus = localHotelCheckResult.Data.IsAutoConfirmRoomStatus;
            result.Data.Room = _mapper.Map<CheckSale_Room>(localHotelCheckResult.Data.Room);
            result.Data.PriceStrategy = _mapper.Map<CheckSale_PriceStrategy>(localHotelCheckResult.Data.PriceStrategy);
            if (result.Code == CheckPriceStrategySaleCode.Success)
            {
                var hotelPreBooking = _baseHotelService.GetPreBookingByCode(input.PreBookingCode);
                if (hotelPreBooking?.DatePrices?.Length is > 0)
                {
                    var datePrices = result.Data.PriceStrategy.DatePrice;
                    //试单变价允许差额 
                    var totalPrice = datePrices.Sum(s => s.Price) * input.Quantity;
                    var hotelPreBookingConfig = _baseHotelService.GetHotelPreBookingConfig();
                    var diff = Math.Abs(hotelPreBookingConfig.Difference);
                    if (totalPrice > hotelPreBooking.TotalPrice + diff)
                    {
                        result.Code = CheckPriceStrategySaleCode.PriceChanged;
                    }
                    result.Data.PriceStrategy.CheckDatePrice = hotelPreBooking.DatePrices
                        .Select(x =>
                        {
                            var price = datePrices.FirstOrDefault(p => p.Date == x.Date);
                            return new DatePrice
                            {
                                Date = x.Date,
                                Price = x.Price,
                                Stock = price.Stock,
                                SaleCurrencyCode = price.SaleCurrencyCode,
                                CostCurrencyCode = price.CostCurrencyCode
                            };
                        }).ToList();
                }
            }
            return result;
        }

        /// <summary>
        /// 第三方酒店价格策略查询校验
        /// </summary>
        public async Task<CheckSaleOutput> CheckSaleByThirdPartyHotel(CheckSaleInput input, long tenantId)
        {
            var result = new CheckSaleOutput
            {
                Data = new CheckSaleData()
            };

            #region 查询第三方酒店渠道配置信息

            ApiHotelSetting? hotelChannelSettings;
            if (input.SalesChannel.HasValue)
            {
                hotelChannelSettings = await _dbContext.ApiHotelSettings.AsNoTracking()
                    .Where(x => x.ChannelType == input.SalesChannel
                                && x.Enabled)
                    .FirstOrDefaultAsync();
                if (hotelChannelSettings == null)
                {
                    result.Code = CheckPriceStrategySaleCode.ChannelNotSale;
                    result.Message = "房型不可售";
                    _logger.LogWarning("[汇智酒店]下单前校验失败:{@Msg},{@Input}", "查无渠道配置", input);
                    return result;
                }
            }
            else
            {
                hotelChannelSettings = null;
            }

            #endregion

            var hotelPreBooking = _baseHotelService.GetPreBookingByCode(input.PreBookingCode);

            #region 第三方酒店价格策略查询

            var thirdPartyHotel = await _dbContext.ApiHotels.AsNoTracking()
                .Where(x => x.Id == input.HotelId)
                .Select(x => new
                {
                    x.Id,
                    x.ResourceHotelId,
                    x.ZHName,
                    x.ENName,
                    x.StaffTag
                })
                .FirstOrDefaultAsync();
            if (thirdPartyHotel is null)
            {
                result.Code = CheckPriceStrategySaleCode.HotelInvalid;
                result.Message = "房型不可售";
                _logger.LogWarning("[汇智酒店]下单前校验失败:{@Msg},{@Input}", "查无第三方酒店信息", input);
                return result;
            }

            var checkAvailabilityInput = new CheckAvailabilityInput
            {
                SupplierApiType = input.SupplierApiType,
                ResourceHotelId = thirdPartyHotel.ResourceHotelId,
                CheckIn = input.BeginDate,
                CheckOut = input.EndDate,
                TenantId = tenantId,
                PricestrategyId = input.PriceStrategyId,
                RoomNum = input.Quantity,
                AdultNum = input.AdultNum,
                ChildrenAges = input.ChildrenAges,
                ResourceRoomId = input.RoomId,
                TotalCost = hotelPreBooking.TotalPrice,
                PreBookingCode = input.PreBookingCode,
            };
            var checkAvailability = await _baseHotelService.ThirdPartyHotelCheckAvailability(checkAvailabilityInput);

            if (checkAvailability.CheckCode != CheckPriceStrategySaleCode.Success)
            {
                result.Code = CheckPriceStrategySaleCode.PriceStrategyInvalid;
                result.Message = "房型不可预订";
                return result;
            }

            #endregion

            var hotelPreBookingConfig = _baseHotelService.GetHotelPreBookingConfig();
            var diff = Math.Abs(hotelPreBookingConfig.Difference);
            //试单变价允许差额 
            if (checkAvailability.TotalPrice > hotelPreBooking.TotalPrice + diff)
            {
                result.Code = CheckPriceStrategySaleCode.PriceChanged;
            }

            #region 获取指定房型信息和判断

            var roomInfo = checkAvailability.Room;
            if (roomInfo is null)
            {
                result.Code = CheckPriceStrategySaleCode.ChannelNotSale;
                result.Message = "房型不可售";
                _logger.LogWarning("[汇智酒店]下单前校验失败:{@Msg},{@Input}", "查无房型信息", input);
                return result;
            }

            result.Data.Room = new CheckSale_Room
            {
                Id = roomInfo.ResourceRoomId,
                ResourceRoomId = roomInfo.ResourceRoomId,
                Name = roomInfo.ZHName,
                EnName = roomInfo.ENName,
                MaximumOccupancy = roomInfo.MaximumOccupancy,
                AreaMin = roomInfo.AreaMin,
                AreaMax = roomInfo.AreaMax,
                WindowType = roomInfo.WindowType,
            };

            var convertBedType = ConvertBedType(roomInfo.BedType);
            result.Data.Room.BedTypes = convertBedType.bedTypes;
            result.Data.Room.BedType = convertBedType.mainBedType;

            #endregion

            #region 获取指定价格策略信息和判断

            var stayDays = input.EndDate.Subtract(input.BeginDate).Days;
            var priceStrategyInfo = checkAvailability;

            //价格库存判断
            if (priceStrategyInfo.CalendarPrices.Any() is false)
            {
                result.Code = CheckPriceStrategySaleCode.PricesNotEnough;
                result.Message = "房量不足";
                _logger.LogWarning("[汇智酒店]下单前校验失败:{@Msg},{@Input}", "日历价格不足", input);
                return result;
            }

            if (priceStrategyInfo.CalendarPrices.Count() != stayDays)
            {
                result.Code = CheckPriceStrategySaleCode.PricesNotEnough;
                result.Message = "房量不足";
                _logger.LogWarning("[汇智酒店]下单前校验失败:{@Msg},{@Input}", "日历价格不足", input);
                return result;
            }

            if (priceStrategyInfo.CalendarPrices.Any(x => !x.Enabled
                                                     || (!x.OverSaleable && x.Quantity < input.Quantity)))
            {
                result.Code = CheckPriceStrategySaleCode.InventoryNotEnough;
                result.Message = "房量不足";
                _logger.LogWarning("[汇智酒店]下单前校验失败:{@Msg},{@Input}", "日历库存不足", input);
                return result;
            }

            //查询汇率
            var tenantConfig = await _baseHotelService.GetTenantSysConfig();
            var saleCurrencyCode = tenantConfig.CurrencyCode;
            var getExchangeRateInput = new List<GetExchangeRatesInput>
            {
                new GetExchangeRatesInput
                {
                    BaseCurrencyCode = priceStrategyInfo.CostCurrencyCode,
                    TargetCurrencyCode = saleCurrencyCode
                }
            };

            var costToSaleExchangeRate = (await _baseHotelService.GetCurrencyExchangeRateList(getExchangeRateInput)).FirstOrDefault();
            var overSaleable = priceStrategyInfo.CalendarPrices.First().OverSaleable;
            result.Data.PriceStrategy = new CheckSale_PriceStrategy
            {
                SupplierApiType = input.SupplierApiType,
                Id = priceStrategyInfo.PricestrategyId,
                RoomId = priceStrategyInfo.RoomId,
                HotelId = priceStrategyInfo.HotelId,
                Name = priceStrategyInfo.Name,
                ENName = priceStrategyInfo.ENName,
                NumberOfBreakfast = priceStrategyInfo.BreakfastCount,
                BoardCodeType = priceStrategyInfo.BoardCodeType,
                BoardCount = priceStrategyInfo.BoardCount,
                OverSaleable = overSaleable,
                SupplierId = priceStrategyInfo.SupplierId,
                PriceStrategyIsAutoConfirm = priceStrategyInfo.IsAutoConfirm,
                IsAutoConfirm = priceStrategyInfo.IsAutoConfirm,
                ConfirmByMins = priceStrategyInfo.ConfirmByMins,
                CancelRule = new CancelRule
                {
                    CancelRulesType = priceStrategyInfo.CancelRule.CancelRulesType,
                    BeforeCheckInDays = priceStrategyInfo.CancelRule.BeforeCheckInDays,
                    BeforeCheckInTime = priceStrategyInfo.CancelRule.BeforeCheckInTime,
                    CheckInDateTime = priceStrategyInfo.CancelRule.CheckInDateTime,
                    CancelChargeType = priceStrategyInfo.CancelRule.CancelChargeType,
                    ChargeValue = priceStrategyInfo.CancelRule.ChargeValue
                },
                CostCurrencyCode = priceStrategyInfo.CostCurrencyCode,
                SaleCurrencyCode = saleCurrencyCode,
                MaximumOccupancy = priceStrategyInfo.MaxOccupancy,
                ArrivalTaxFees = priceStrategyInfo.ArrivalTaxFees,
                TaxDescription = priceStrategyInfo.TaxDescription,
                NumberOfNights = hotelPreBooking.NumberOfNights,
                NumberOfRooms = hotelPreBooking.NumberOfRooms,
                PriceStrategyType = hotelPreBooking.PriceStrategyType,
                IsDirect = hotelPreBooking.IsDirect,
                Tag = hotelPreBooking.Tag,
                BookingBenefits = hotelPreBooking.BookingBenefits,
            };

            //渠道加价 汇智酒店不适用
            ChannelMarkup channelMarkup = new() { MarkupType = MarkupType.AddValue, PriceType = MarkupPriceType.SalePrice, Value = 0 };
            if (hotelChannelSettings is not null)
            {
                channelMarkup = new ChannelMarkup
                {
                    MarkupType = hotelChannelSettings.MarkupType,
                    PriceType = hotelChannelSettings.PriceType,
                    Value = hotelChannelSettings.Value
                };
            }

            foreach (var calendar in priceStrategyInfo.CalendarPrices)
            {
                decimal channelPrice = CalculateChannelPrice(channelMarkup, calendar.CostPrice, calendar.CostPrice, costToSaleExchangeRate.ExchangeRate);

                result.Data.PriceStrategy.DatePrice.Add(new DatePrice
                {
                    Date = calendar.Date,
                    Price = channelPrice,
                    Cost = calendar.CostPrice,
                    Stock = calendar.Quantity,
                    CostCurrencyCode = priceStrategyInfo.CostCurrencyCode,
                    SaleCurrencyCode = saleCurrencyCode
                });

                var checkDatePrice = hotelPreBooking.DatePrices?.FirstOrDefault(x => x.Date == calendar.Date);
                if (checkDatePrice is not null)
                {
                    result.Data.PriceStrategy.CheckDatePrice.Add(new DatePrice
                    {
                        Date = checkDatePrice.Date,
                        Price = checkDatePrice.Price,
                        Cost = checkDatePrice.Cost,
                        Stock = calendar.Quantity,
                        CostCurrencyCode = priceStrategyInfo.CostCurrencyCode,
                        SaleCurrencyCode = saleCurrencyCode
                    });
                }
            }

            #endregion

            result.Data.HotelId = input.HotelId;
            result.Data.ResourceHotelId = thirdPartyHotel.ResourceHotelId;
            result.Data.HotelName = thirdPartyHotel.ZHName;
            result.Data.HotelEnName = thirdPartyHotel.ENName;
            result.Data.StaffTag = thirdPartyHotel.StaffTag;
            result.Data.OperatingModel = OperatingModel.Agency;
            result.Data.IsAutoConfirmRoomStatus = true;//自动确认房态
            return result;
        }

        /// <summary>
        /// 手工单价格策略库存校验
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<CheckSaleOutput> CheckPriceStrategySaleByManual(CheckSaleInput input)
        {
            var data = new CheckSaleData();
            var result = new CheckSaleOutput() { Data = data };

            #region 基础信息

            var priceStrategyId = long.Parse(input.PriceStrategyId);
            var priceStrategy = await _dbContext.PriceStrategies.AsNoTracking()
                .FirstOrDefaultAsync(a => a.Id == priceStrategyId);
            if (priceStrategy is null)
            {
                result.Code = CheckPriceStrategySaleCode.PriceStrategyInvalid;
                result.Message = "房型不可售";
                return result;
            }

            //团房小于起订量
            if (priceStrategy.PriceStrategyType == PriceStrategyType.GroupRoom
                && input.Quantity < priceStrategy.NumberOfRooms)
            {
                result.Code = CheckPriceStrategySaleCode.NumberOfRoomsNotEnough;
                result.Message = "团房最低起订间数不足";
                return result;
            }

            var hotel = await _dbContext.Hotels.AsNoTracking()
                .FirstOrDefaultAsync(a => a.Id == priceStrategy.HotelId);
            if (hotel is null)
            {
                result.Code = CheckPriceStrategySaleCode.HotelInvalid;
                result.Message = "房型不可售";
                return result;
            }

            data.HotelId = hotel.Id;
            data.HotelName = hotel.ZHName;
            data.HotelEnName = hotel.ENName;
            data.IsAutoConfirmRoomStatus = hotel.IsAutoConfirmRoomStatus;
            data.OperatingModel = hotel.OperatingModel;

            var room = await _dbContext.HotelRooms.AsNoTracking()
                .FirstOrDefaultAsync(a => a.Id == priceStrategy.HotelRoomId);
            if (room is null)
            {
                result.Code = CheckPriceStrategySaleCode.RoomInvalid;
                result.Message = "房型不可售";
                return result;
            }

            var convertBedTypes = ConvertBedType(room.BedType);
            room.BedType = convertBedTypes.mainBedType;

            data.Room = _mapper.Map<CheckSale_Room>(room);
            data.Room.BedTypes = convertBedTypes.bedTypes;
            //房型图片
            data.Room.HotelRoomImgPath = await _dbContext.HotelPhotos
                .Where(x => x.HotelRoomId == room.Id && x.HotelId == room.HotelId)
                .Select(x => x.Path)
                .FirstOrDefaultAsync();

            //取消政策
            var salePriceStrategy = _mapper.Map<CheckSale_PriceStrategy>(priceStrategy);
            var cancelRule = await _dbContext.PriceStrategyCancelRules.AsNoTracking()
                .Where(a => priceStrategy.Id == a.PriceStrategyId)
                .FirstOrDefaultAsync();
            salePriceStrategy.CancelRule = _mapper.Map<CancelRule>(cancelRule) ?? new CancelRule();
            salePriceStrategy.DatePrice = new List<DatePrice>();
            data.PriceStrategy = salePriceStrategy;

            #endregion

            #region 判断库存

            var stayDays = input.EndDate.Subtract(input.BeginDate).Days;
            var inventoriesRequest = new GetCalendarInventoryInput()
            {
                StartDate = input.BeginDate,
                EndDate = input.EndDate.AddDays(-1),
                CalendarProducts = new List<CalendarProduct>
                {
                    new CalendarProduct
                    {
                        ProductId = room.Id,
                        ItemIds = new List<long>(){ priceStrategy.Id }
                    }
                }
            };
            var inventories = await GetInventories(inventoriesRequest);
            if (inventories.Any() is false)
            {
                result.Code = CheckPriceStrategySaleCode.InventoryNotEnough;
                result.Message = "房量不足";
                return result;
            }

            var inventory = inventories.FirstOrDefault();
            for (var i = 0; i < stayDays; i++)
            {
                var date = input.BeginDate.AddDays(i);
                var inventoryItem = inventory?.Inventories?.FirstOrDefault(x => x.Date == date);
                if (inventoryItem is null)
                {
                    result.Code = CheckPriceStrategySaleCode.InventoryNotEnough;
                    result.Message = "房量不足";
                    return result;
                }

                if (!inventoryItem.Enabled)
                {
                    result.Code = CheckPriceStrategySaleCode.CalendarNotEnable;
                    result.Message = "日历价格不可售";
                    return result;
                }

                if (inventory.OverSaleable || inventoryItem.AvailableQuantity >= input.Quantity)
                {
                    continue;
                }

                result.Code = CheckPriceStrategySaleCode.InventoryNotEnough;
                result.Message = "房量不足";
                return result;
            }

            #endregion

            return result;
        }

        /// <summary>
        /// 获取价格策略日历价格
        /// </summary>
        public async Task<List<GetPriceStrategyPriceInfo>> GetPriceStrategyPrices(bool isSale, List<TempRoomPriceStrategy> priceStrategies,
            DateTime beginDate, DateTime endDate, SellingChannels? salesChannel)
        {
            var list = priceStrategies.Select(x => new GetPriceStrategyPriceInfo()
            {
                PriceStrategyId = x.PriceStrategyId,
                CostCurrencyCode = x.CostCurrencyCode,
                SaleCurrencyCode = x.SaleCurrencyCode,
                SupplierId = x.SupplierId,
                CalendarPrices = new List<CalendarPriceItem>()
            }).ToList();

            var priceStrategyIds = priceStrategies.Select(x => x.PriceStrategyId).ToList();

            //获取渠道加价配置
            var channelMarkups = new List<ChannelMarkup>();
            if (salesChannel.HasValue)
            {
                channelMarkups = await _dbContext.ChannelMarkups.AsNoTracking()
                    .Where(a => priceStrategyIds.Contains(a.PriceStrategyId)
                                && a.Enabled
                                && a.ChannelType == salesChannel)
                    .ToListAsync();
                if (isSale && !channelMarkups.Any())
                {
                    list.ForEach(x => x.Code = GetPriceErrorCode.ChannelNotSale);
                    return list;
                }
            }

            //获取日历价格
            var calendarPrices = await _dbContext.PriceStrategyCalendarPrices.AsNoTracking()
                .Where(a => priceStrategyIds.Contains(a.PriceStrategyId)
                    && a.Date >= beginDate
                    && a.Date < endDate)
                .ToListAsync();
            if (isSale && !calendarPrices.Any())
            {
                list.ForEach(x => x.Code = GetPriceErrorCode.PriceNotSetup);
                return list;
            }

            //计算有供应商的价格策略汇率
            var getExchangeRateInput = priceStrategies
                .Where(x => x.SupplierId > 0)
                .GroupBy(x => new { x.SaleCurrencyCode, x.CostCurrencyCode })
                .Select(x => new GetExchangeRatesInput
                {
                    BaseCurrencyCode = x.Key.CostCurrencyCode,
                    TargetCurrencyCode = x.Key.SaleCurrencyCode
                })
                .ToList();
            var saleExchangeRateList = await _baseHotelService.GetCurrencyExchangeRateList(getExchangeRateInput);

            //获取库存
            var inventoriesRequest = new GetCalendarInventoryInput()
            {
                StartDate = beginDate,
                EndDate = endDate.AddDays(-1),
                CalendarProducts = priceStrategies
                    .GroupBy(a => a.HotelRoomId)
                    .Select(a => new CalendarProduct
                    {
                        ProductId = a.Key,
                        ItemIds = a.Select(c => c.PriceStrategyId).ToList()
                    }).ToList()
            };
            var inventories = await GetInventories(inventoriesRequest);
            if (isSale && !inventories.Any())
            {
                list.ForEach(x => x.Code = GetPriceErrorCode.InventoryNotSetup);
                return list;
            }

            var stayDays = endDate.Subtract(beginDate).Days;
            foreach (var item in list)
            {
                var markup = channelMarkups.FirstOrDefault(a => a.PriceStrategyId == item.PriceStrategyId);
                if (salesChannel.HasValue && isSale && markup is null)
                {
                    item.Code = GetPriceErrorCode.ChannelNotSale;
                    continue;
                }

                var itemInventory = inventories.FirstOrDefault(a => a.ItemId == item.PriceStrategyId);
                if (isSale && itemInventory is null)
                {
                    item.Code = GetPriceErrorCode.InventoryNotSetup;
                    continue;
                }

                var itemCalendarPrices = calendarPrices.Where(a => a.PriceStrategyId == item.PriceStrategyId);
                if (isSale && itemCalendarPrices.Count() != stayDays)
                {
                    item.Code = GetPriceErrorCode.PriceNotSetup;
                    continue;
                }

                item.OverSaleable = itemInventory?.OverSaleable ?? false;
                item.ProductInventoryType = itemInventory?.ProductInventoryType ?? ProductInventoryType.Single;

                //渠道售价汇率
                decimal saleExchangeRate = 1;
                if (item.SupplierId > 0)
                {
                    saleExchangeRate = saleExchangeRateList
                        .First(x => x.BaseCurrencyCode == item.CostCurrencyCode
                                             && x.TargetCurrencyCode == item.SaleCurrencyCode).ExchangeRate;
                }

                for (int i = 0; i < stayDays; i++)
                {
                    var date = beginDate.AddDays(i);
                    var priceItem = new CalendarPriceItem()
                    {
                        Date = date
                    };

                    var dateInventory = itemInventory?.Inventories?.FirstOrDefault(a => a.Date == date);
                    priceItem.Enabled = dateInventory?.Enabled ?? false;
                    priceItem.AvailableQuantity = dateInventory?.AvailableQuantity ?? 0;
                    priceItem.TotalQuantity = dateInventory?.TotalQuantity ?? 0;

                    var calendarPrice = itemCalendarPrices.FirstOrDefault(a => a.Date == date);
                    priceItem.CostPrice = calendarPrice?.CostPrice;
                    priceItem.CostCurrencyCode = item.CostCurrencyCode;
                    priceItem.SaleCurrencyCode = item.SaleCurrencyCode;

                    if (salesChannel.HasValue)
                    {
                        if (markup != null && calendarPrice != null)
                        {
                            priceItem.ChannelPrice = CalculateChannelPrice(markup, calendarPrice.SalePrice,
                                calendarPrice.CostPrice, saleExchangeRate);
                        }
                    }
                    else
                    {
                        priceItem.ChannelPrice = calendarPrice?.SalePrice;
                    }

                    item.CalendarPrices.Add(priceItem);
                }
            }

            return list;
        }

        /// <summary>
        /// 获取库存
        /// </summary>
        public async Task<List<CalendarInventoryOutput>> GetInventories(GetCalendarInventoryInput request)
        {
            var searchHttpContent = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
            var inventories = await _httpClientFactory.InternalPostAsync<List<CalendarInventoryOutput>>(
                requestUri: _servicesAddress.Inventory_Inventories(),
                httpContent: searchHttpContent);
            return inventories is null ? throw new BusinessException(ErrorTypes.Inventory.GetInventoryFail) : inventories;
        }

        /// <summary>
        /// 计算基础售价
        /// </summary>
        private static decimal CalculateSalePrice(decimal costPrice, MarkupType markupType, decimal value, decimal saleExchangeRate)
        {
            var salePrice = 0m;
            //汇率换算采购价
            var calcCostPrice = Math.Round(costPrice * saleExchangeRate, 2);
            switch (markupType)
            {
                case MarkupType.AddValue:
                    salePrice = Math.Round(calcCostPrice + value, 2);
                    break;
                case MarkupType.SubtractValue:
                    salePrice = Math.Round(calcCostPrice - value, 2);
                    break;
                case MarkupType.AddRate:
                    salePrice = Math.Round(calcCostPrice * (1 + value * 0.01m), 2);
                    break;
                case MarkupType.SubtractRate:
                    salePrice = Math.Round(calcCostPrice * (1 - value * 0.01m), 2);
                    break;
            }
            return salePrice;
        }

        /// <summary>
        /// 计算渠道售价
        /// </summary>
        private static decimal CalculateChannelPrice(ChannelMarkup markup, decimal salePrice, decimal costPrice,
            decimal saleExchangeRate)
        {
            //汇率
            var orgExchangeRate = markup.PriceType == MarkupPriceType.CostPrice
                ? saleExchangeRate
                : 1;

            //汇率换算采购价
            var orgPrice = markup.PriceType == MarkupPriceType.CostPrice
                ? Math.Round(costPrice * orgExchangeRate, 2)
                : salePrice;

            var channelPrice = 0m;
            switch (markup.MarkupType)
            {
                case MarkupType.AddValue:
                    channelPrice = Math.Round(orgPrice + markup.Value, 2);
                    break;
                case MarkupType.SubtractValue:
                    channelPrice = Math.Round(orgPrice - markup.Value, 2);
                    break;
                case MarkupType.AddRate:
                    channelPrice = Math.Round(orgPrice * (1 + markup.Value * 0.01m), 2);
                    break;
                case MarkupType.SubtractRate:
                    channelPrice = Math.Round(orgPrice * (1 - markup.Value * 0.01m), 2);
                    break;
            }
            return channelPrice;
        }

        /// <summary>
        /// OTA价格
        /// </summary>
        public async Task<List<GetOTAPriceOutput>> GetOTAPrice(List<GetByResourceHotelIdsOutput> priceStrategies, DateTime beginDate, DateTime endDate)
        {
            var priceStrategyIds = priceStrategies.Select(x => x.PriceStrategyId).ToList();

            //获取价格
            var getCalendarPricesTask = _dbContext.PriceStrategyCalendarPrices
                .IgnoreQueryFilters()
                .Where(x => priceStrategyIds.Contains(x.PriceStrategyId)
                    && x.Date >= beginDate
                    && x.Date < endDate)
                .ToListAsync();

            //获取库存
            var inventoriesRequest = new GetCalendarInventoryInput()
            {
                StartDate = beginDate,
                EndDate = endDate.AddDays(-1),
                CalendarProducts = priceStrategies
                    .GroupBy(a => a.HotelRoomId)
                    .Select(a => new CalendarProduct
                    {
                        ProductId = a.Key,
                        ItemIds = a.Select(c => c.PriceStrategyId).ToList()
                    }).ToList()
            };
            var searchHttpContent = new StringContent(JsonConvert.SerializeObject(inventoriesRequest), Encoding.UTF8, "application/json");
            var getInventoriesTask = _httpClientFactory.InternalPostAsync<List<CalendarInventoryOutput>>(
                requestUri: _servicesAddress.Inventory_InventoriesIgnoreTenant(),
                httpContent: searchHttpContent);

            Task.WaitAll(getCalendarPricesTask, getInventoriesTask);
            var calendarPrices = getCalendarPricesTask.Result;
            var inventories = getInventoriesTask.Result;

            //查询汇率
            var strategyAndRate = await GetPriceStrategyAndExchangeRate(priceStrategyIds);


            var list = new List<GetOTAPriceOutput>();
            var stayDays = endDate.Subtract(beginDate).Days;
            foreach (var item in priceStrategies)
            {
                var bookingHoursInAdvance = DateTime.Now.AddHours(item.BookingHoursInAdvance);
                var itemCalendarPrices = calendarPrices.Where(x => x.PriceStrategyId == item.PriceStrategyId);
                var itemInventory = inventories.FirstOrDefault(a => a.ItemId == item.PriceStrategyId);
                var itemPriceStrategy = strategyAndRate.priceStrategyList.FirstOrDefault(x => x.Id == item.PriceStrategyId);

                //汇率
                decimal exchangeRate = 1;
                if (itemPriceStrategy.SupplierId > 0)
                {
                    exchangeRate = strategyAndRate.exchangeRateList
                        .First(x => x.BaseCurrencyCode == itemPriceStrategy.CostCurrencyCode
                                             && x.TargetCurrencyCode == itemPriceStrategy.SaleCurrencyCode).ExchangeRate;
                }

                var output = new GetOTAPriceOutput()
                {
                    ResourceRoomId = item.ResourceRoomId,
                    PriceStrategyName = item.Name,
                    PriceStrategyId = item.PriceStrategyId,
                    NumberOfBreakfast = item.NumberOfBreakfast,
                    DatePrices = new List<GetOTAPrice_DatePrice>()
                };

                for (var i = 0; i < stayDays; i++)
                {
                    var date = beginDate.AddDays(i);

                    if (item.BookingHoursInAdvance > 1 && date.AddDays(1) < bookingHoursInAdvance)
                    {
                        output.DatePrices.Add(new GetOTAPrice_DatePrice()
                        {
                            Date = date,
                            Price = 0,
                            Status = false,
                            Quantity = 0
                        });
                        continue;
                    }

                    var calendarPrice = itemCalendarPrices.FirstOrDefault(x => x.Date == date);
                    var inventory = itemInventory.Inventories.FirstOrDefault(x => x.Date == date);

                    output.DatePrices.Add(new GetOTAPrice_DatePrice()
                    {
                        Date = date,
                        Price = calendarPrice is null
                            ? 0
                            : CalculateChannelPrice(_mapper.Map<ChannelMarkup>(item.Markup),
                                calendarPrice.SalePrice, calendarPrice.CostPrice, exchangeRate),
                        Status = inventory?.Enabled ?? false,
                        Quantity = inventory?.AvailableQuantity ?? 0
                    });
                }
                list.Add(output);
            }

            return list;
        }

        /// <summary>
        /// 计算无营运权供应商的基础售价
        /// 供货配置-不变==>销售值不变
        /// 供货配置-增加==>采购价基础上增加
        /// 供货配置-增幅==>采购价基础上增幅
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private decimal CalculateSupplyPrice(CalcSupplyMarkupValueInput input)
        {
            var calcData = 0m;
            //汇率换算采购价
            var costPrice = Math.Round(input.CostPrice * input.SaleExchangeRate, 2);
            switch (input.MarkupType)
            {
                case SupplyMarkupType.None:
                    calcData = input.SalePrice;
                    break;
                case SupplyMarkupType.AddValue:
                    calcData = Math.Round(costPrice + input.Value, 2);
                    break;
                case SupplyMarkupType.AddRate:
                    calcData = Math.Round(costPrice * (1 + input.Value * 0.01m), 2);
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
            return calcData;
        }

        /// <summary>
        /// 更新价格分组冗余最低价
        /// </summary>
        /// <param name="priceStrategyId"></param>
        private async Task UpdatePriceChannelSettingMinPrice(long priceStrategyId, DateTime beginDate, DateTime endDate)
        {
            var calendarPrices = await _dbContext.PriceStrategyCalendarPrices.AsNoTracking()
                .Where(a => a.PriceStrategyId == priceStrategyId
                            && a.Date >= DateTime.Now
                            && a.Date <= DateTime.Now.AddDays(30)
                            && a.SalePrice > 0)
                .Select(x => new
                {
                    x.SalePrice,
                    x.TenantId,
                    x.HotelId
                })
                .ToListAsync();
            if (calendarPrices.Any())
            {
                var minPrice = calendarPrices.Min(x => x.SalePrice);
                await _capPublisher.PublishAsync(CapTopics.Product.UpdatePriceChannelSettingMinPrice, new UpdatePriceChannelMinPriceMessage
                {
                    HotelId = calendarPrices.First().HotelId,
                    SkuId = priceStrategyId,
                    PriceStrategyId = priceStrategyId,
                    RedundantMinPrice = minPrice,
                    TenantId = calendarPrices.First().TenantId,
                    BeginDate = beginDate,
                    EndDate = endDate,
                });
            }
        }

        /// <summary>
        /// 通过价格策略id查询价格策略和对应汇率
        /// </summary>
        /// <param name="priceStrategyIds"></param>
        private async Task<(List<PriceStrategy> priceStrategyList, List<GetExchangeRateOutput> exchangeRateList)>
            GetPriceStrategyAndExchangeRate(List<long> priceStrategyIds)
        {
            var priceStrategyList = await _dbContext.PriceStrategies.AsNoTracking()
                .Where(x => priceStrategyIds.Contains(x.Id))
                .ToListAsync();

            if (priceStrategyList.Any() is false)
                return new ValueTuple<List<PriceStrategy>, List<GetExchangeRateOutput>>();

            //查询策略对应汇率
            var getExchangeRateInput = priceStrategyList
                .Where(x => x.SupplierId > 0)
                .Select(x => new GetExchangeRatesInput
                {
                    BaseCurrencyCode = x.CostCurrencyCode,
                    TargetCurrencyCode = x.SaleCurrencyCode
                })
                .ToList();

            if (getExchangeRateInput.Any() is false)
                return new ValueTuple<List<PriceStrategy>, List<GetExchangeRateOutput>>(priceStrategyList, new());

            var exchangeRateList = await _baseHotelService.GetCurrencyExchangeRateList(getExchangeRateInput);
            return new ValueTuple<List<PriceStrategy>, List<GetExchangeRateOutput>>(priceStrategyList,
                exchangeRateList);
        }

        /// <summary>
        /// 转换床型数据
        /// </summary>
        private static (List<BedType>? bedTypes, string mainBedType) ConvertBedType(string bedTypeStr)
        {
            if (string.IsNullOrEmpty(bedTypeStr))
                return new ValueTuple<List<BedType>?, string>();
            var bedTypes = JsonConvert.DeserializeObject<List<BedType>>(bedTypeStr);
            var mainBedTypes = bedTypes?.Select(x => x.main) ?? new List<string>();
            var mainBedType = string.Join(",", mainBedTypes);
            return new ValueTuple<List<BedType>?, string>(bedTypes, mainBedType);
        }

        #region 分销商web

        /// <summary>
        /// 酒店详情策略
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<AgencyGetOutput>> AgencyGet(AgencyGetInput input)
        {
            var result = new List<AgencyGetOutput>();

            #region 价格策略信息

            //日历价格
            var calendarPrices = await _dbContext.PriceStrategyCalendarPrices.AsNoTracking()
                .Where(x => x.HotelId == input.HotelId
                            && x.Date >= input.LiveDate
                            && x.Date < input.LeaveDate
                            && x.SalePrice > 0)
                .WhereIF(input.PriceStrategyIds.Any(), x => input.PriceStrategyIds.Contains(x.PriceStrategyId))
                .ToListAsync();
            if (calendarPrices.Any() is false) return result;

            //价格策略信息
            var priceStrategies = await _dbContext.PriceStrategies.AsNoTracking()
                .Where(x => input.PriceStrategyIds.Contains(x.Id)
                            && x.Enabled)
                .ToListAsync();
            if (priceStrategies.Any() is false) return result;

            //取消政策
            var cancelRules = await _dbContext.PriceStrategyCancelRules.AsNoTracking()
                .Where(x => input.PriceStrategyIds.Contains(x.PriceStrategyId))
                .Select(x => x)
                .ToListAsync();

            //国籍限制
            var nationalities = await _dbContext.PriceStrategyNationalities.AsNoTracking()
                .Where(x => input.PriceStrategyIds.Contains(x.PriceStrategyId))
                .ToListAsync();

            //酒店信息
            var hotel = await _dbContext.Hotels.AsNoTracking()
                .Where(x => x.Id == input.HotelId)
                .Select(x => new
                {
                    x.ServiceTimeBegin,
                    x.ServiceTimeEnd,
                    x.ServiceEndtimeInNextDay
                })
                .FirstOrDefaultAsync();
            if (hotel is null) return result;

            //酒店营业结束时间
            var serviceTime = hotel.ServiceEndtimeInNextDay
                ? input.LiveDate.AddDays(1).Add(hotel.ServiceTimeEnd)
                : input.LeaveDate.Add(hotel.ServiceTimeEnd);


            var groupByRoomIds = priceStrategies.GroupBy(x => x.HotelRoomId)
                .Select(x =>
                    new CalendarProduct
                    {
                        ProductId = x.Key,
                        ItemIds = x.Select(m => m.Id).ToList()
                    })
                .ToList();

            //获取库存
            var inventoriesRequest = new GetCalendarInventoryInput()
            {
                StartDate = input.LiveDate,
                EndDate = input.LeaveDate.AddDays(-1),
                CalendarProducts = groupByRoomIds
            };
            var inventories = await GetInventories(inventoriesRequest);
            var newStrategyIds = priceStrategies.Select(x => x.Id);
            //入住天数
            var stayDays = input.LeaveDate.Subtract(input.LiveDate).Days;
            //处理日历价&库存
            var resultStrategies = new List<AgencyGetPriceStrategyItem>();
            foreach (var item in newStrategyIds)
            {
                //是否可售-用于兼容openapi接口
                bool isSale = true;
                //过滤日历价格和入住天数不匹配
                var calendarPrice = calendarPrices.Where(x => x.PriceStrategyId == item);
                if (calendarPrice.Count() != stayDays)
                {
                    if (input.VerifySale)
                    {
                        throw new BusinessException(ErrorTypes.Hotel.PricesNotEnough);
                    }
                    isSale = false;
                    continue;
                }

                var inv = inventories.FirstOrDefault(x => x.ItemId == item);
                var strategy = priceStrategies.First(x => x.Id == item);
                var cancelRuleItem = cancelRules.FirstOrDefault(x => x.PriceStrategyId == strategy.Id);

                //判断策略预订时间
                if (strategy.BookingHoursInAdvance > 0
                    && input.LiveDate.AddHours(24 - strategy.BookingHoursInAdvance) < DateTime.Now)
                {
                    if (input.VerifySale)
                        throw new BusinessException(ErrorTypes.Hotel.PricesNotEnough);
                    isSale = false;
                }

                #region 处理尾房

                if (strategy.PriceStrategyType == PriceStrategyType.EndRoom)
                {
                    var start = input.LiveDate.Add(strategy.EndRoomBookingTimeBegin);
                    if (strategy.EndRoomBookingDateBegin == 1)
                        start = start.AddDays(1);
                    var end = input.LiveDate.Add(strategy.EndRoomBookingTimeEnd);
                    if (strategy.EndRoomBookingDateEnd == 1)
                        end = end.AddDays(1);

                    if (end > serviceTime)
                        end = serviceTime;

                    if (start > DateTime.Now || end < DateTime.Now)
                    {
                        if (input.VerifySale)
                        {
                            throw new BusinessException(ErrorTypes.Hotel.EndRoomNotBook);
                        }
                        isSale = false;
                        continue;
                    }
                }

                #endregion

                #region 处理钟点房和连住房

                ErrorTypes.Hotel? errorType = null;
                switch (strategy.PriceStrategyType)
                {
                    case PriceStrategyType.HourRoom when stayDays != 1:
                        errorType = ErrorTypes.Hotel.HourRoomNotBook;
                        break;
                    case PriceStrategyType.StayLongDiscount when stayDays < strategy.NumberOfNights:
                        errorType = ErrorTypes.Hotel.StayLongDiscountNotBook;
                        break;
                }
                if (errorType.HasValue)
                {
                    isSale = false;
                    if (input.VerifySale)
                        throw new BusinessException(errorType);
                }
                #endregion

                var resultItemInfo = new AgencyGetPriceStrategyItem
                {
                    Id = strategy.Id,
                    HotelId = strategy.HotelId,
                    Name = strategy.Name,
                    PriceStrategyType = strategy.PriceStrategyType,
                    NumberOfBreakfast = strategy.NumberOfBreakfast,
                    NumberOfNights = strategy.NumberOfNights,
                    NumberOfRooms = strategy.NumberOfRooms,
                    OverSaleable = inv?.OverSaleable ?? false,
                    CancelRulesType = cancelRuleItem?.CancelRulesType ?? CancelRulesType.FreeCancel,
                    SupplierId = strategy.SupplierId,
                    CostCurrencyCode = strategy.CostCurrencyCode,
                    SaleCurrencyCode = strategy.SaleCurrencyCode,
                    BookingHoursInAdvance = strategy.BookingHoursInAdvance,
                    IsAutoConfirm = strategy.IsAutoConfirm,
                    CancelRule = new CancelRule
                    {
                        CancelRuleId = cancelRuleItem.Id,
                        CancelChargeType = cancelRuleItem.CancelChargeType,
                        CancelRulesType = cancelRuleItem.CancelRulesType,
                        BeforeCheckInDays = cancelRuleItem.BeforeCheckInDays,
                        CheckInDateTime = cancelRuleItem.CheckInDateTime,
                        ChargeValue = cancelRuleItem.ChargeValue
                    },
                    Nationalities = nationalities.Where(x => x.PriceStrategyId == strategy.Id)
                    .Select(x => new NationalityDto
                    {
                        CountryCode = x.CountryCode,
                        ZHName = x.ZHName
                    }),
                    MaximumOccupancy = strategy.MaximumOccupancy,
                    IsSale = isSale,
                };

                for (int i = 0; i < stayDays; i++)
                {
                    var date = input.LiveDate.AddDays(i);
                    var priceItem = new AgencyGetPriceStrategyCalendarPriceItem()
                    {
                        Date = date
                    };

                    var dateInventory = inv?.Inventories?.FirstOrDefault(a => a.Date == date);
                    if (!(dateInventory?.Enabled ?? false))
                    {
                        if (input.VerifySale)
                            throw new BusinessException(ErrorTypes.Hotel.CalendarNotEnable);
                    }

                    var dateCalendarPrice = calendarPrice.First(x => x.Date == date);
                    priceItem.Enabled = dateInventory?.Enabled ?? false;
                    priceItem.AvailableQuantity = dateInventory?.AvailableQuantity ?? 0;
                    priceItem.TotalQuantity = dateInventory?.TotalQuantity ?? 0;
                    priceItem.CostPrice = dateCalendarPrice?.CostPrice;
                    priceItem.SalePrice = dateCalendarPrice?.SalePrice;
                    resultItemInfo.CalendarPrices.Add(priceItem);
                }
                //报价 试单码
                resultItemInfo.PreBookingCode = _baseHotelService.CreatePreBookingCode(new HotelPreBookingDto
                {
                    DatePrices = resultItemInfo.CalendarPrices.Select(p => new HotelPreBookingDatePriceDto
                    {
                        Date = p.Date,
                        Price = p.SalePrice ?? 0,
                        Cost = p.CostPrice ?? 0
                    }).ToArray(),
                    TotalPrice = resultItemInfo.CalendarPrices.Sum(p => p.SalePrice ?? 0) * input.RoomNum
                });
                resultStrategies.Add(resultItemInfo);
            }

            #endregion

            #region 房型信息

            var roomIds = priceStrategies.Select(x => x.HotelRoomId);
            var roomInfo = await _dbContext.HotelRooms.AsNoTracking()
                .Where(x => roomIds.Contains(x.Id))
                .OrderBy(x => x.Sort)
                .Select(x => new
                {
                    RoomId = x.Id,
                    RoomZHName = x.ZHName,
                    RoomENName = x.ENName,
                    x.WindowType,
                    x.BedType,
                    x.RoomQuantity,
                    x.MaximumOccupancy,
                    x.AreaMin,
                    x.AreaMax,
                    x.FloorMin,
                    x.FloorMax,
                    x.Sort,
                    x.ResourceRoomId,
                    x.IsHasChildren,
                    x.ExtraBedFee,
                    x.ExtraBedType,
                    x.IsHasChildrenExistingBed,
                    x.ExtraBedFeeCurrency,
                    x.Description
                }).ToListAsync();

            //房型首图
            var roomPhotos = await _dbContext.HotelPhotos.AsNoTracking()
                .Where(x => x.HotelId == input.HotelId
                            && roomIds.Contains(x.HotelRoomId)
                            && x.Enabled
                            && x.Sort == 0)
                .Select(x => new
                {
                    x.HotelRoomId,
                    x.Path
                })
                .ToListAsync();

            #endregion

            return (from item in groupByRoomIds
                    let room = roomInfo.First(r => r.RoomId == item.ProductId)
                    let roomPhoto = roomPhotos.FirstOrDefault(p => p.HotelRoomId == item.ProductId)?.Path
                    let strategies = resultStrategies.Where(p => item.ItemIds.Contains(p.Id))
                        .OrderBy(p => p.PriceStrategyType)
                        .ToList()
                    where strategies.Any() is true
                    select new AgencyGetOutput
                    {
                        RoomId = room.RoomId,
                        RoomZHName = room.RoomZHName,
                        RoomENName = room.RoomENName,
                        WindowType = room.WindowType,
                        RoomQuantity = room.RoomQuantity,
                        MaximumOccupancy = room.MaximumOccupancy,
                        AreaMin = room.AreaMin,
                        AreaMax = room.AreaMax,
                        FloorMax = room.FloorMax,
                        FloorMin = room.FloorMin,
                        FirstPhoto = roomPhoto,
                        BedType = !string.IsNullOrWhiteSpace(room.BedType)
                            ? JsonConvert.DeserializeObject<List<BedType>>(room.BedType)
                            : new List<BedType>(),
                        Sort = room.Sort,
                        ResourceRoomId = room.ResourceRoomId,
                        PriceStrategies = strategies,
                        IsHasChildren = room.IsHasChildren,
                        ExtraBedFee = room.ExtraBedFee,
                        ExtraBedType = room.ExtraBedType,
                        IsHasChildrenExistingBed = room.IsHasChildrenExistingBed,
                        ExtraBedFeeCurrency = room.ExtraBedFeeCurrency,
                        Description = room.Description
                    }).ToList();
        }

        #endregion

        #region 日历酒店的价格策略按可预订最近日期

        public async Task<GetSaleableOutput> GetSaleable(GetSaleableInput input)
        {
            //获取可售价格策略
            var salePriceStrategies = await SaleablePriceStrategy(input.HotelId, input.BeginDate, input.SalesChannel);

            GetSaleableOutput result = new()
            {
                BeginDate = salePriceStrategies.Count > 0 ? salePriceStrategies.Min(s => s.Date) : null,
                SaleablePriceStrategies = salePriceStrategies
            };
            return result;
        }
        public async Task<List<SaleablePriceStrategyOutput>> SaleablePriceStrategy(
           long hotelId,
           DateTime beginDate,
           SellingChannels salesChannel)
        {
            //获取酒店信息
            var hotel = await _dbContext.Hotels.AsNoTracking()
                .Where(x => x.Id == hotelId && x.Enabled)
                .Select(x => new
                {
                    x.Id,
                    x.TenantId,
                    x.ResourceHotelId,
                    x.ServiceTimeBegin,
                    x.ServiceTimeEnd,
                    x.ServiceEndtimeInNextDay
                })
                .FirstOrDefaultAsync();

            //获取可售价格策略
            var priceStrategies = await _dbContext.PriceStrategies.AsNoTracking()
                .Where(a => a.HotelId == hotelId && a.Enabled)
                .ToListAsync();
            ////移除不符合提前预订时间的价格策略
            //var nowTime = DateTime.Now;
            //priceStrategies.RemoveAll(x => x.BookingHoursInAdvance > 0
            //    && beginDate.AddHours(24 - x.BookingHoursInAdvance) < nowTime);

            var priceStrategyIds = priceStrategies.Select(x => x.Id);

            var roomPriceStrategies = priceStrategies.Select(x => new TempRoomPriceStrategy()
            {
                HotelRoomId = x.HotelRoomId,
                PriceStrategyId = x.Id,
                CostCurrencyCode = x.CostCurrencyCode,
                SaleCurrencyCode = x.SaleCurrencyCode,
                SupplierId = x.SupplierId
            }).ToList();

            var channelMarkups = await _dbContext.ChannelMarkups.AsNoTracking()
                  .Where(a => priceStrategyIds.Contains(a.PriceStrategyId)
                              && a.Enabled
                              && a.ChannelType == salesChannel)
                  .ToListAsync();

            //酒店营业结束时间
            var serviceTime = hotel.ServiceEndtimeInNextDay
                ? beginDate.AddDays(1).Add(hotel.ServiceTimeEnd)
                : beginDate.Add(hotel.ServiceTimeEnd);

            ProductCalendarInventoryValidInfoInput infoInput = new()
            {
                BeginDate = beginDate,
                ProductIds = priceStrategies.Select(x => x.HotelRoomId).ToArray(),
                TenantId = hotel.TenantId
            };
            var httpContent = new StringContent(JsonConvert.SerializeObject(infoInput),
                  Encoding.UTF8, "application/json");
            var validInfoOutputs = await _httpClientFactory.InternalPostAsync<IEnumerable<ProductCalendarInventoryValidInfoOutput>>(
                _servicesAddress.Inventory_CalendarInventoryValidInfos(),
                httpContent: httpContent);
            var list = new List<SaleablePriceStrategyOutput>();
            foreach (var priceStrategy in priceStrategies)
            {


                var channelMarkup = channelMarkups.FirstOrDefault(s => s.PriceStrategyId == priceStrategy.Id);
                if (channelMarkup?.Enabled is not true)
                    continue;

                var itemInventory = validInfoOutputs
                    .FirstOrDefault(a => a.ProductId == priceStrategy.HotelRoomId
                                         && a.ItemId == priceStrategy.Id);
                if (itemInventory is null)
                    continue;

                if (itemInventory.Date == beginDate)
                {
                    //尾房售卖时间处理
                    if (priceStrategy.PriceStrategyType == PriceStrategyType.EndRoom)
                    {
                        var start = beginDate.Add(priceStrategy.EndRoomBookingTimeBegin);
                        if (priceStrategy.EndRoomBookingDateBegin == 1)
                            start = start.AddDays(1);
                        var end = beginDate.Add(priceStrategy.EndRoomBookingTimeEnd);
                        if (priceStrategy.EndRoomBookingDateEnd == 1)
                            end = end.AddDays(1);

                        if (end > serviceTime)
                            end = serviceTime;

                        if (start > DateTime.Now || end < DateTime.Now)
                            continue;
                    }
                }

                list.Add(new SaleablePriceStrategyOutput
                {
                    HotelRoomId = priceStrategy.HotelRoomId,
                    PriceStrategyId = priceStrategy.Id,
                    Date = itemInventory.Date
                });
            }
            return list;
        }

        #endregion
    }

    public class TempRoomPriceStrategy
    {
        public long HotelRoomId { get; set; }
        public long PriceStrategyId { get; set; }

        /// <summary>
        /// 采购价币种
        /// </summary>
        public string CostCurrencyCode { get; set; }

        /// <summary>
        /// 售价币种
        /// </summary>
        public string SaleCurrencyCode { get; set; }

        public long SupplierId { get; set; }
    }
}
