using CanalSharp.Protocol;
using Common.ServicesHttpClient;
using Hotel.Api.EsDocument;
using Hotel.Api.Extensions;
using Hotel.Api.Services.Interfaces;
using Microsoft.Extensions.Options;
using Nest;
using Newtonsoft.Json;

namespace Hotel.Api.Services.EsSyncProcessing;

/// <summary>
/// 资源库 - 酒店同步处理服务
/// </summary>
public class ResourceHotelSyncProcessService : IEsSyncProcessingService
{
    private readonly IElasticClient _elasticClient;
    private readonly IEsCreateIndexService _esCreateIndexService;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ServicesAddress _servicesAddress;
    private readonly ILogger<ResourceHotelSyncProcessService> _logger;
    private readonly IApiHotelService _apiHotelService;
    
    // 索引名称
    private const string _resourceHotelIndexName = "resource-hotel";
    private const string _resourceHotelIndexNameV2 = "resource-hotel-v2";
    
    public ResourceHotelSyncProcessService(
        IElasticClient elasticClient,
        IEsCreateIndexService esCreateIndexService,
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> options,
        ILogger<ResourceHotelSyncProcessService> logger,
        IApiHotelService apiHotelService)
    {
        _elasticClient = elasticClient;
        _httpClientFactory = httpClientFactory;
        _servicesAddress = options.Value;
        _esCreateIndexService = esCreateIndexService;
        _logger = logger;
        _apiHotelService = apiHotelService;
    }
    
    public string SchemaName => "Resource";
    public string TableName => "Hotel";
    public object Document => new ResourceHotelDocumentV2();
    public async Task Sync(List<(object document, long tenantId, EventType eventType)> input)
    {
        try
        {
            #region V1

            //索引初始化
            await IndicesInit();
            //执行批量操作
            await PerformBulkCrudOperations(input);

            #endregion

            #region V2

            //索引初始化
            await IndicesInitV2();
            //执行批量操作
            await PerformBulkCrudOperationsV2(input);

            #endregion
        }
        catch (Exception ex)
        {
            _logger.LogError("ResourceHotelSyncProcessing Error,Message:{@Message}", ex.Message);
        }
    }

    #region V1

    /// <summary>
    /// 索引初始化
    /// </summary>
    private async Task IndicesInit()
    {
        var indexExistsResponse = await _elasticClient.Indices.ExistsAsync(_resourceHotelIndexName);
        if (indexExistsResponse.Exists is false)
        {
            var createResponse = await _esCreateIndexService.CreateResourceHotelIndex(_resourceHotelIndexName);
        }
    }

    /// <summary>
    /// 执行批量操作
    /// </summary>
    /// <param name="syncData"></param>
    private async Task PerformBulkCrudOperations(List<(object document, long tenantId, EventType eventType)> syncData)
    {
        var bulkDescriptor = new BulkDescriptor();
        foreach (var syncItem in syncData)
        {
            var documentV2 = syncItem.document as ResourceHotelDocumentV2;
            var syncDocument = new ResourceHotelDocument
            {
                Id = documentV2.Id,
                HopId = documentV2.HopId,
                ZHName = documentV2.ZHName,
                ENName = documentV2.ENName,
                CountryCode = documentV2.CountryCode,
                ProvinceCode = documentV2.ProvinceCode,
                CityCode = documentV2.CityCode,
                Enabled = documentV2.Enabled
            };
            switch (syncItem.eventType)
            {
                case EventType.Insert:

                    bulkDescriptor.Index<ResourceHotelDocument>(i => i
                        .Index(_resourceHotelIndexName)
                        .Id(syncDocument.Id)
                        .Document(syncDocument));

                    break;
                case EventType.Update:

                    bulkDescriptor.Update<ResourceHotelDocument>(i => i
                        .Index(_resourceHotelIndexName)
                        .Id(syncDocument.Id)
                        .Doc(syncDocument)
                        .Upsert(syncDocument));

                    break;
                case EventType.Delete:

                    bulkDescriptor.Delete<ResourceHotelDocument>(i => i
                        .Index(_resourceHotelIndexName)
                        .Id(syncDocument.Id));

                    break;
            }
        }
        var response = await _elasticClient.BulkAsync(bulkDescriptor);
    }

    #endregion

    #region V2

    /// <summary>
    /// 索引初始化
    /// </summary>
    private async Task IndicesInitV2()
    {
        var indexExistsResponse = await _elasticClient.Indices.ExistsAsync(_resourceHotelIndexNameV2);
        if (indexExistsResponse.Exists is false)
        {
            var createResponse = await _esCreateIndexService.CreateResourceHotelIndexV2(_resourceHotelIndexNameV2);
        }
    }
    
    /// <summary>
    /// 执行批量操作
    /// </summary>
    /// <param name="syncData"></param>
    private async Task PerformBulkCrudOperationsV2(List<(object document, long tenantId, EventType eventType)> syncData)
    {
        var hotelDocSyncData = syncData
            .Select(x => x.document as ResourceHotelDocumentV2)
            .ToList();
        var syncResourceHotelIds = hotelDocSyncData
            .Select(x => x.Id)
            .Distinct()
            .ToList();
        var dataSupplements = await DataSupplementV2(syncResourceHotelIds);
        
        /*
         * 判断是否需要查询基础设施数据
         * 排除掉dataSupplements存在FacilityId的id
         */
        var esExistFacilityHotelIds = dataSupplements.Where(x => x.FacilityId != null && x.FacilityId.Any())
            .Select(x => x.Id)
            .Distinct()
            .ToList(); //es 存在的酒店数据
        var needFacilityHotelIds = syncResourceHotelIds.Except(esExistFacilityHotelIds).ToList(); // 排除掉存在基础设施的酒店数据
        var facilitySupplement = await GetResourceHotelFacility(needFacilityHotelIds); // 基础设施数据查询
        
        var bulkDescriptor = new BulkDescriptor();
        var refreshApiHotelList = new List<long>();
        foreach (var syncItem in syncData)
        {
            var syncDocument = syncItem.document as ResourceHotelDocumentV2;
            var esDocument = dataSupplements.FirstOrDefault(x => x.Id == syncDocument.Id); // es 文档数据
            var facilityIds = esDocument?.FacilityId ?? new List<long>();
            if (facilityIds.Any() is false)
            {
                facilityIds = facilitySupplement
                    .Where(x => x.hotelId == syncDocument.Id)
                    .Select(x => x.facilityId)
                    .ToList();
            }
            
            syncDocument.FacilityId = facilityIds;
            
            switch (syncItem.eventType)
            {
                case EventType.Insert:

                    syncDocument.TenantConfig = new List<TenantResourceHotelConfigNested>();//初始化
                    bulkDescriptor.Index<ResourceHotelDocumentV2>(i => i
                        .Index(_resourceHotelIndexNameV2)
                        .Id(syncDocument.Id)
                        .Document(syncDocument));

                    break;
                case EventType.Update:

                    var updateDto = new ResourceHotelDocumentUpdateDto
                    {
                        Id = syncDocument.Id,
                        HopId = syncDocument.HopId,
                        ZHName = syncDocument.ZHName,
                        ENName = syncDocument.ENName,
                        CountryCode = syncDocument.CountryCode,
                        CountryName = syncDocument.CountryName,
                        ProvinceCode = syncDocument.ProvinceCode,
                        ProvinceName = syncDocument.ProvinceName,
                        CityCode = syncDocument.CityCode,
                        CityName = syncDocument.CityName,
                        Address = syncDocument.Address,
                        ENAddress = syncDocument.ENAddress,
                        StarLevel = syncDocument.StarLevel,
                        Location = syncDocument.Location,
                        CoordinateType = syncDocument.CoordinateType,
                        Floors = syncDocument.Floors,
                        Rooms = syncDocument.Rooms,
                        WeightValue = syncDocument.WeightValue,
                        Enabled = syncDocument.Enabled,
                        FacilityId = syncDocument.FacilityId,
                        CreateTime = syncDocument.CreateTime
                    };
                    
                    bulkDescriptor.Update<ResourceHotelDocumentV2,ResourceHotelDocumentUpdateDto>(i => i
                        .Index(_resourceHotelIndexNameV2)
                        .Id(syncDocument.Id)
                        .Doc(updateDto)
                        .Upsert(syncDocument));

                    break;
                case EventType.Delete:

                    bulkDescriptor.Delete<ResourceHotelDocumentV2>(i => i
                        .Index(_resourceHotelIndexNameV2)
                        .Id(syncDocument.Id));

                    break;
            }
            if (syncDocument.ApiHotelId <= 0 && syncItem.eventType != EventType.Delete)
            {
                refreshApiHotelList.Add(syncDocument.Id);
            }
        }
        
        var response = await _elasticClient.BulkAsync(bulkDescriptor);
        if(refreshApiHotelList.Any())
        {
            try
            {
                await _apiHotelService.Refresh(refreshApiHotelList);
            }
            catch (Exception e)
            {
                _logger.LogError("ResourceHotelSyncProcessing 同步ApiHotel {@Message},{@ResourceHotelId}",e.Message,refreshApiHotelList);
            }
        }
    }

    private async Task<List<(long hotelId, long facilityId)>> GetResourceHotelFacility(List<long> hotelIds)
    {
        if (hotelIds.Any() is false) return new List<(long hotelId, long facilityId)>();
        var content = new StringContent(JsonConvert.SerializeObject(hotelIds), Encoding.UTF8, "application/json");
        var response = await _httpClientFactory.InternalPostAsync<List<(long hotelId, long facilityId)>>(
            httpContent: content,
            requestUri: _servicesAddress.Resource_HotelFacility());
        return response;
    }
    
    private async Task<List<ResourceHotelDocumentV2>> DataSupplementV2(List<long> hotelIdList)
    {
        //查询es doc
        var count = hotelIdList.Count;
        var searchResponse = await _elasticClient.SearchAsync<ResourceHotelDocumentV2>(x => x
            .Index(_resourceHotelIndexNameV2)
            .From(0)
            .Size(count)
            .Query(q => q
                .Bool(b => b
                    .Must(m => m
                        .Terms(t => t
                            .Field(f => f.Id)
                            .Terms(hotelIdList))))));

        var searchResponseList = searchResponse.Documents.ToList();
        return searchResponseList;
    }
    #endregion
}