using Nest;
using CanalSharp.Protocol;
using Contracts.Common.Resource.Enums;
using Hotel.Api.EsDocument;
using Hotel.Api.HostedService.CanalMonitorMappedFiles;
using Hotel.Api.Services.Interfaces;

namespace Hotel.Api.Services.EsSyncProcessing;

/// <summary>
/// 第三方酒店数据同步
/// </summary>
public class ThirdPartyHotelSyncProcessingService : IEsSyncProcessingService
{
    private readonly ILogger<ThirdPartyHotelSyncProcessingService> _logger;
    private readonly IElasticClient _elasticClient;
    
    private const string _resourceHotelIndexNameV2 = "resource-hotel-v2";
    public ThirdPartyHotelSyncProcessingService(
        ILogger<ThirdPartyHotelSyncProcessingService> logger,
        IElasticClient elasticClient)
    {
        _logger = logger;
        _elasticClient = elasticClient;
    }
    
    public string SchemaName => "Hotel";
    public string TableName => nameof(Model.ApiHotel);
    
    public object Document =>  new CanalMonitorMappedApiHotelEsFile();
    public async Task Sync(List<(object document,long tenantId,EventType eventType)> input)
    {
        try
        {
            // 同步id到资源酒店索引
            await PerformBulkCrudOperationsV2(input);
        }
        catch (Exception ex)
        {
            _logger.LogError("ThirdPartyHotelSyncProcessing Error,Message:{@Message}", ex.Message);
        }
    }
    
    #region 同步id到资源酒店索引

    /// <summary>
    /// 执行批量操作
    /// <value>查询到对应文档才更新</value>
    /// </summary>
    /// <param name="syncData"></param>
    private async Task PerformBulkCrudOperationsV2(List<(object document, long tenantId, EventType eventType)> syncData)
    {
        //取出同步数据
        var hotelIdList = syncData.Select(x => x.document as CanalMonitorMappedApiHotelEsFile)
            .Select(x => x.ResourceHotelId)
            .Distinct()
            .ToList();
        //查询es数据
        var dataSupplement = await DataSupplementV2(hotelIdList);
        
        var bulkDescriptor = new BulkDescriptor();
        foreach (var syncItem in syncData)
        {
            var syncDocument = syncItem.document as CanalMonitorMappedApiHotelEsFile;
            var resourceHotelDocument = dataSupplement.FirstOrDefault(x => x.Id == syncDocument.ResourceHotelId);
            if(resourceHotelDocument == null) continue;
            
            //更新资源酒店文档数据
            var updateDto = new ResourceHotelDocumentApiHotelUpdateDto();
            if (syncItem.eventType == EventType.Delete)
            {
                updateDto.ApiHotelId = 0;
                updateDto.StaffTag = 0;
                updateDto.ReunionRoom = 0;
                updateDto.Tags = new List<SellHotelTag>();
            }
            else
            {
                updateDto.ApiHotelId = syncDocument.Id;
                updateDto.StaffTag = syncDocument.StaffTag ? 1 : 0;
                updateDto.ReunionRoom = syncDocument.ReunionRoom;
                updateDto.Tags = syncDocument.Tags;
            }
            
            bulkDescriptor.Update<ResourceHotelDocumentV2,ResourceHotelDocumentApiHotelUpdateDto>(i=>i
                .Index(_resourceHotelIndexNameV2)
                .Id(resourceHotelDocument.Id)
                .Doc(updateDto)
                .RetriesOnConflict(3));
        }
        var response = await _elasticClient.BulkAsync(bulkDescriptor);
    }
    
    private async Task<List<ResourceHotelDocumentV2>> DataSupplementV2(List<long> hotelIdList)
    {
        //查询es doc
        var count = hotelIdList.Count;
        var searchResponse = await _elasticClient.SearchAsync<ResourceHotelDocumentV2>(x => x
            .Index(_resourceHotelIndexNameV2)
            .From(0)
            .Size(count)
            .Query(q => q
                .Bool(b => b
                    .Must(m => m
                        .Terms(t => t
                            .Field(f => f.Id)
                            .Terms(hotelIdList))))));

        var searchResponseList = searchResponse.Documents.ToList();
        return searchResponseList;
    }


    #endregion
}