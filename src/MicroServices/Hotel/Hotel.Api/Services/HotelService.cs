using AutoMapper;
using Cit.Storage.Redis;
using Common.GlobalException;
using Common.Jwt;
using Common.ServicesHttpClient;
using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Hotel.DTOs.HotelExtend;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Inventory.Messages;
using Contracts.Common.Product.Enums;
using Contracts.Common.Resource.DTOs;
using Contracts.Common.Resource.Messages;
using Contracts.Common.Tenant.DTOs.SupplierApiSetting;
using Contracts.Common.User.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Hotel.Api.ConfigModel;
using Hotel.Api.Extensions;
using Hotel.Api.Notification;
using Hotel.Api.Services.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using NetTopologySuite.Geometries;
using Newtonsoft.Json;
using System.Data;
using System.Security.Cryptography.Xml;

namespace Hotel.Api.Services;

public class HotelService : IHotelService
{
    private readonly IMapper _mapper;
    private readonly CustomDbContext _dbContext;
    private readonly IMediator _mediator;
    private readonly ICapPublisher _capPublisher;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ServicesAddress _servicesAddress;
    private readonly IRedisClient _redisClient;
    private readonly IOptions<HuiZhiHotelConfig> _huiZhiHotelConfig;

    public HotelService(
        ICapPublisher capPublisher,
        IMapper mapper,
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> options,
        IHttpContextAccessor httpContextAccessor,
        CustomDbContext dbContext,
        IMediator mediator,
        IRedisClient redisClient,
        IOptions<HuiZhiHotelConfig> huiZhiHotelConfig)
    {
        _mapper = mapper;
        _dbContext = dbContext;
        _mediator = mediator;
        _capPublisher = capPublisher;
        _httpContextAccessor = httpContextAccessor;
        _httpClientFactory = httpClientFactory;
        _servicesAddress = options.Value;
        _redisClient = redisClient;
        _huiZhiHotelConfig = huiZhiHotelConfig;
    }

    public async Task<PagingModel<SearchHotelsOutput>> Search(SearchHotelsInput input)
    {
        Point? targetLocation = null;
        if (input.TargetLatitude.HasValue && input.TargetLongitude.HasValue)
        {
            var geometryFactory = NetTopologySuite.NtsGeometryServices.Instance.CreateGeometryFactory(srid: 4326);
            targetLocation = geometryFactory.CreatePoint(new Coordinate(input.TargetLongitude!.Value, input.TargetLatitude!.Value));
        }

        var hotels = await _dbContext.Hotels.AsNoTracking()
            .GroupJoin(_dbContext.HotelRedundantDatas.AsNoTracking(),
                     hotel => hotel.Id, data => data.HotelId, (hotel, data) => new { hotel, data })
            .SelectMany(x => x.data.DefaultIfEmpty(), (x, y) => new { x.hotel, data = y })
            .WhereIF(input.HotelIds != null && input.HotelIds.Any(), x => input.HotelIds.Contains(x.hotel.Id))
            .WhereIF(input.CountryCode is > 0, x => x.hotel.CountryCode == input.CountryCode!.Value)
            .WhereIF(input.CityCode.HasValue && input.CityCode > 0, x => x.hotel.CityCode == input.CityCode!.Value)
            .WhereIF(input.Enabled.HasValue, x => x.hotel.Enabled == input.Enabled!.Value)
            .WhereIF(input.OperatingModel.HasValue, x => x.hotel.OperatingModel == input.OperatingModel!.Value)
            .WhereIF(input.HasCommission.HasValue, x => x.data.HasCommission == input.HasCommission!.Value)
            .WhereIF(!string.IsNullOrWhiteSpace(input.KeyWord),
            x => x.hotel.ZHName.Contains(input.KeyWord) || x.hotel.ENName.Contains(input.KeyWord))
            .WhereIF(input.StarLevel.HasValue, x => x.hotel.StarLevel == input.StarLevel.Value)
            .WhereIF(input.TargetDistance.HasValue && targetLocation != null,
                    x => x.hotel.Location.IsWithinDistance(targetLocation,
                    input.TargetDistance!.Value * 1000))
            .OrderByDescending(s => s.hotel.UpdateTime)
            .ThenByDescending(s => s.hotel.Id)
            .PagingAsync(input.PageIndex, input.PageSize, x => x.hotel);

        var result = _mapper.Map<PagingModel<SearchHotelsOutput>>(hotels);

        if (result.Data.Any())
        {
            var hotelIds = result.Data.Select(x => x.Id);
            var hotelPhotoList = await _dbContext.HotelPhotos.AsNoTracking()
                .Where(x => hotelIds.Contains(x.HotelId)
                    && x.HotelRoomId == 0
                    && x.Enabled)
                .OrderBy(x => x.Sort)
                .Select(x => new
                {
                    x.HotelId,
                    x.Path
                })
                .ToListAsync();

            foreach (var item in result.Data)
            {
                var orgHotel = hotels.Data.FirstOrDefault(x => x.Id == item.Id);
                item.HotelPicture = hotelPhotoList.FirstOrDefault(x => x.HotelId == item.Id)?.Path ?? "";
                item.Latitude = orgHotel.Location.Y;
                item.Longitude = orgHotel.Location.X;
            }
        }

        return result;
    }

    public async Task<PagingModel<long>> GetByCityCode(GetByCityCodeInput input)
    {
        return await _dbContext.Hotels.AsNoTracking()
            .Where(x => x.Enabled)
            .WhereIF(input.CityCode.HasValue, x => x.CityCode == input.CityCode)
            .WhereIF(input.LastHotelId.HasValue, x => x.Id > input.LastHotelId.Value)
            .OrderBy(x => x.Id)
            .PagingAsync(input.PageIndex, input.PageSize, x => x.Id);
    }

    [UnitOfWork]
    public async Task Add(AddHotelInput input)
    {
        if (_dbContext.Hotels.AsNoTracking().Any(x => x.ResourceHotelId == input.ResourceHotelId))
            throw new BusinessException("该酒店已存在");

        #region 酒店基本信息

        var hotel = new Model.Hotel
        {
            OperatingModel = input.OperatingModel,
            Enabled = input.Enabled,
            HopId = input.HopId,
            HotelType = input.HotelType,
            ZHName = input.Name ?? string.Empty,
            ENName = input.EnName ?? string.Empty,
            CountryCode = input.CountryCode,
            CountryName = input.CountryName ?? string.Empty,
            ProvinceCode = input.ProvinceCode,
            ProvinceName = input.ProvinceName ?? string.Empty,
            CityCode = input.CityCode,
            CityName = input.CityName ?? string.Empty,
            DistrictCode = input.DistrictCode,
            DistrictName = input.DistrictName ?? string.Empty,
            Address = input.Address,
            ENAddress = input.ENAddress,
            StarLevel = input.StarLevel,
            OpeningDate = input.OpeningDate,
            DecorateDate = input.DecorateDate,
            ServiceTimeBegin = input.ServiceTimeBegin,
            ServiceTimeEnd = input.ServiceTimeEnd,
            ServiceEndtimeInNextDay = input.ServiceEndtimeInNextDay,
            Floors = input.Floors,
            Rooms = input.Rooms,
            Telephone = input.Telephone,
            Telefax = input.Telefax,
            Intro = input.Intro,
            ENIntro = input.ENIntro,
            SurroundingFacilities = input.SurroundingFacilities,
            ENSurroundingFacilities = input.ENSurroundingFacilities,
            CheckinPolicy = input.CheckinPolicy,
            ENCheckinPolicy = input.ENCheckinPolicy,
            ImportantNotices = input.ImportantNotices,
            ResourceHotelId = input.ResourceHotelId,
            CoordinateType = input.CoordinateType,
            RoomVoltage = input.RoomVoltage,
            NonSmokingRoomsOrFloors = input.NonSmokingRoomsOrFloors
        };
        if (input.Longitude.HasValue && input.Latitude.HasValue)
            hotel.SetLocation(input.Longitude.Value, input.Latitude.Value);
        var tenantId = _httpContextAccessor.HttpContext.GetTenantId();
        //汇智商户 记录发单邮箱
        if (!string.IsNullOrWhiteSpace(input.ReceiptEmail) && tenantId == _huiZhiHotelConfig?.Value.TenantId)
        {
            hotel.ReceiptEmail = input.ReceiptEmail;
        }
        #endregion

        #region 酒店图片

        var hotelPhotos = input.HotelRoomPhotos
            .Where(s => s.ResourceRoomId == 0)
            .SelectMany(s => s.Photos
                .Where(d => !string.IsNullOrWhiteSpace(d.Path))
                .Select(d => new HotelPhotos
                {
                    Enabled = true,
                    HotelId = hotel.Id,
                    HotelRoomId = 0,
                    Path = d.Path
                }))
            .ToList();

        #endregion

        #region 房型信息和图片
        var roomsByHotelIdsInput = new GetRoomsByHotelIdsInput()
        {
            HotelIds = new List<long> { input.ResourceHotelId },
            IsStandardRoom = true
        };
        using HttpContent httpContent = new StringContent(JsonConvert.SerializeObject(roomsByHotelIdsInput), Encoding.UTF8, "application/json");
        var resourceRoomsResponses = await _httpClientFactory.InternalPostAsync<List<GetRoomsByHotelIdsOutput>>(
            _servicesAddress.Resource_GetHotelRoomDetails(),
            httpContent: httpContent);
        var resourceRooms = resourceRoomsResponses.Select(x => x.Rooms).FirstOrDefault();
        var hotelRooms = resourceRooms
            .Select(x => new HotelRoom
            {
                HotelId = hotel.Id,
                ResourceRoomId = x.Id,
                AreaMax = x.AreaMax,
                AreaMin = x.AreaMin,
                BedType = x.BedType,
                FloorMax = x.FloorMax,
                FloorMin = x.FloorMin,
                MaximumOccupancy = x.MaximumOccupancy,
                RoomQuantity = x.RoomQuantity,
                Sort = x.Sort,
                WindowType = x.WindowType,
                ZHName = x.ZHName,
                ENName = x.ENName
            }).ToList();
        foreach (var hotelRoom in hotelRooms)
        {
            var photots = input.HotelRoomPhotos
                .Where(x => x.ResourceRoomId == hotelRoom.ResourceRoomId)
                .SelectMany(s => s.Photos
                    .Where(d => !string.IsNullOrWhiteSpace(d.Path))
                    .Select(d => new HotelPhotos
                    {
                        Enabled = true,
                        HotelId = hotelRoom.HotelId,
                        HotelRoomId = hotelRoom.Id,
                        Path = d.Path
                    }));
            hotelPhotos.AddRange(photots);
        }

        #endregion

        #region 酒店配套设施

        var hotelFacilities = input.FacilitiesList
             .Select(x => new HotelFacilities
             {
                 HotelId = hotel.Id,
                 FacilityId = x
             });

        #endregion

        await _dbContext.AddAsync(hotel);
        await _dbContext.AddRangeAsync(hotelRooms);
        await _dbContext.AddRangeAsync(hotelPhotos);
        await _dbContext.AddRangeAsync(hotelFacilities);

        //酒店冗余信息
        var redundantData = new HotelRedundantData()
        {
            HotelId = hotel.Id
        };
        await _dbContext.HotelRedundantDatas.AddAsync(redundantData);

        //设置客服
        var command = new Contracts.Common.Tenant.Messages.SetProductSupportStaffMessage
        {
            TenantId = _httpContextAccessor.HttpContext.GetTenantId(),
            ProductId = hotel.Id,
            AfterSaleStaff = input.AfterSaleStaff,
            PreSaleStaff = input.PreSaleStaff,
            ProductType = ProductType.Hotel
        };

        await _capPublisher.PublishAsync(CapTopics.Tenant.SetProductSupportStaff, command);

        //更新资源库酒店图片路径
        var resourceHotelPhotos = input.HotelRoomPhotos
         .SelectMany(x => x.Photos
         .Where(x => !string.IsNullOrWhiteSpace(x.OrgPath) && x.OrgPath != x.Path)
         .Select(x => new ResourceHotelPhoto
         {
             OrgPath = x.OrgPath,
             Path = x.Path
         }));
        if (resourceHotelPhotos.Any())
        {
            ResourceHotelPhotoUpdateMessage photoUpdateCommand = new()
            {
                ResourceHotelId = input.ResourceHotelId,
                Photos = resourceHotelPhotos
            };
            await _capPublisher.PublishAsync(CapTopics.Resource.ResourceHotelPhotosUpdate, photoUpdateCommand);
        }
        await _dbContext.SaveChangesAsync();

        //酒店信息变更通知
        await _mediator.Publish(new HotelChangeNotification
        {
            TenantId = hotel.TenantId,
            HotelId = hotel.Id,
            Type = 1
        });
    }

    public async Task<GetHotelDetailsOutput> Detail(long hotelId)
    {
        var hotel = _dbContext.Hotels.AsNoTracking()
            .FirstOrDefault(x => x.Id == hotelId);
        if (hotel is null) throw new BusinessException(ErrorTypes.Hotel.HotelInvalid);

        var facilities = await _dbContext.HotelFacilities.AsNoTracking()
            .Where(x => x.HotelId == hotelId)
            .Select(x => x.FacilityId)
            .ToListAsync();

        var result = _mapper.Map<GetHotelDetailsOutput>(hotel);
        //非汇智商户不展示发单邮箱
        if (hotel.TenantId != _huiZhiHotelConfig.Value.TenantId)
            hotel.ReceiptEmail = null;
        result.Latitude = hotel.Location.Y;
        result.Longitude = hotel.Location.X;
        result.FacilitiesList = facilities;

        var cities = await Resource_City_Query(new QueryInput() { CityCodes = new int[] { result.CityCode } });
        var city = cities.FirstOrDefault();
        result.EnCityName = city?.ENName;
        result.EnProvinceName = city?.ProvinceEnName;
        result.EnCountryName = city?.CountryEnName;

        return result;
    }

    public async Task Update(UpdateHotelDetailsInput input)
    {
        var entity = await _dbContext.Hotels.FindAsync(input.Id);
        if (entity is null) throw new BusinessException("无数据");
        entity.UpdateTime = DateTime.Now;
        if (input.Longitude.HasValue && input.Latitude.HasValue)
            entity.SetLocation(input.Longitude.Value, input.Latitude.Value);
        entity.UpdateTime = DateTime.Now;
        entity.CountryCode = input.CountryCode;
        entity.CountryName = input.CountryName;
        entity.ProvinceCode = input.ProvinceCode;
        entity.ProvinceName = input.ProvinceName;
        entity.CityCode = input.CityCode;
        entity.CityName = input.CityName;
        entity.DistrictCode = input.DistrictCode;
        entity.DistrictName = input.DistrictName;
        entity.OperatingModel = input.OperatingModel;
        entity.HotelType = input.HotelType;
        entity.Address = input.Address;
        entity.ENAddress = input.ENAddress;
        entity.StarLevel = input.StarLevel;
        entity.OpeningDate = input.OpeningDate;
        entity.DecorateDate = input.DecorateDate;
        entity.ServiceTimeBegin = input.ServiceTimeBegin;
        entity.ServiceTimeEnd = input.ServiceTimeEnd;
        entity.ServiceEndtimeInNextDay = input.ServiceEndtimeInNextDay;
        entity.Floors = input.Floors;
        entity.Rooms = input.Rooms;
        entity.Telephone = input.Telephone;
        entity.Telefax = input.Telefax;
        entity.Intro = input.Intro;
        entity.ENIntro = input.ENIntro;
        entity.SurroundingFacilities = input.SurroundingFacilities;
        entity.ENSurroundingFacilities = input.ENSurroundingFacilities;
        entity.CheckinPolicy = input.CheckinPolicy;
        entity.ENCheckinPolicy = input.ENCheckinPolicy;
        entity.ImportantNotices = input.ImportantNotices;
        entity.RoomVoltage = input.RoomVoltage;
        entity.NonSmokingRoomsOrFloors = input.NonSmokingRoomsOrFloors;
        entity.CoordinateType = input.CoordinateType;
        if (input.FacilitiesList.Any())
        {
            var removes = await _dbContext.HotelFacilities
                .Where(x => x.HotelId == input.Id)
                .ToListAsync();
            _dbContext.RemoveRange(removes);
            var hotelFacilities = new List<HotelFacilities>();
            input.FacilitiesList.ForEach(facilitiesId =>
            {
                var facilities = new HotelFacilities()
                {
                    HotelId = input.Id,
                    FacilityId = facilitiesId
                };
                hotelFacilities.Add(facilities);
            });
            await _dbContext.AddRangeAsync(hotelFacilities);
        }

        await _dbContext.SaveChangesAsync();
        //酒店信息变更通知
        await _mediator.Publish(new HotelChangeNotification
        {
            TenantId = entity.TenantId,
            HotelId = entity.Id,
            Type = 1
        });
    }

    public async Task<List<CheckRefOutput>> CheckRefByResourceHotelIds(CheckRefInput input)
    {
        var hotelds = await _dbContext.Hotels.AsNoTracking()
            .Where(x => input.HotelIds.Contains(x.ResourceHotelId))
            .Select(x => x.ResourceHotelId)
            .ToListAsync();

        var resourcehotel = new List<CheckRefOutput>();
        input.HotelIds.ForEach(x =>
        {
            resourcehotel.Add(new CheckRefOutput()
            {
                resourceHotelId = x,
                isExists = hotelds.Any(a => a.Equals(x))
            });
        });
        return resourcehotel;
    }

    public async Task<List<GetHotelByIdsOutput>> GetByIds(GetHotelByIdsInput input)
    {
        var hotels = await _dbContext.Hotels.AsNoTracking()
            .Where(x => input.HotelIds.Contains(x.Id))
            .WhereIF(input.Enabled, x => x.Enabled)
            .ToListAsync();
        if (!hotels.Any())
            return new List<GetHotelByIdsOutput>();

        var result = _mapper.Map<List<GetHotelByIdsOutput>>(hotels);
        var hotelPhotos = await _dbContext.HotelPhotos.AsNoTracking()
            .Where(x => input.HotelIds.Contains(x.HotelId)
                && x.HotelRoomId == 0 && x.Enabled && x.Sort == 0)
            .Select(s => new
            {
                s.HotelId,
                s.Path
            })
            .ToListAsync();

        var facilities = await GetFacilitiesByIds(input.HotelIds);

        foreach (var item in result)
        {
            item.HotelPicture = hotelPhotos.FirstOrDefault(x => x.HotelId == item.Id)?.Path ?? "";
            if (input.IsSearchAllPhotos)
                item.HotelPictures = hotelPhotos.Where(x => x.HotelId == item.Id).Select(x => x.Path);
            var hotelItem = hotels.FirstOrDefault(x => x.Id == item.Id);
            if (hotelItem != null)
            {
                item.Latitude = hotelItem.Location.Y;
                item.Longitude = hotelItem.Location.X;
                item.CoordinateType = hotelItem.CoordinateType;
            }
            item.FacilitiesIds = facilities.Where(x => x.HotelId == item.Id)
                .Select(x => x.FacilityId)
                .ToList();
        }

        return result;
    }

    public async Task UpdateHotelEnabled(UpdateHotelEnabledInput input)
    {
        var hotel = await _dbContext.Hotels.FindAsync(input.Id);
        if (hotel is null) throw new BusinessException("无数据");
        hotel.Enabled = input.Enabled;
        hotel.UpdateTime = DateTime.Now;
        await _dbContext.SaveChangesAsync();
        //酒店上下架变更通知
        await _mediator.Publish(new HotelChangeNotification
        {
            TenantId = hotel.TenantId,
            HotelId = hotel.Id,
            Type = (byte)(input.Enabled ? 2 : 3)
        });
    }

    public async Task<GetHotelPhotosOutput> GetHotelPhotos(long hotelId)
    {
        var result = await _dbContext.Hotels.AsNoTracking()
            .Where(s => s.Id == hotelId)
            .Select(s => new GetHotelPhotosOutput()
            {
                Id = s.Id,
                Name = s.ZHName
            })
            .FirstOrDefaultAsync();
        if (result is null)
            return new GetHotelPhotosOutput();

        var rooms = await _dbContext.HotelRooms.AsNoTracking()
            .Where(a => a.HotelId == hotelId)
            .ToListAsync();
        var photos = await _dbContext.HotelPhotos.AsNoTracking()
            .Where(a => a.HotelId == hotelId && a.Enabled)
            .ToListAsync();
        //过滤mp4
        photos = photos
            .Where(x => x.HotelPhotoType != Contracts.Common.Resource.Enums.HotelPhotoType.Video &&
                !x.Path.EndsWith(".mp4", StringComparison.OrdinalIgnoreCase))
            .ToList();
        var hotelPhotos = new Contracts.Common.Hotel.DTOs.Hotel.Room()
        {
            Name = result.Name,//id=0时，记录酒店名称
            Id = 0
        };
        hotelPhotos.Photos = photos.Where(a => a.HotelRoomId == 0)
            .OrderBy(a => a.Sort)
            .Select(a => new Contracts.Common.Hotel.DTOs.Hotel.Photo
            {
                Id = a.Id,
                Url = a.Path
            }).ToList();
        result.Rooms.Add(hotelPhotos);
        foreach (var item in rooms)
        {
            var room = new Contracts.Common.Hotel.DTOs.Hotel.Room()
            {
                Name = item.ZHName,
                Id = item.Id,
                ResourceRoomId = item.ResourceRoomId
            };
            room.Photos = photos.Where(a => a.HotelRoomId == item.Id)
                .OrderBy(a => a.Sort)
                .Select(a => new Contracts.Common.Hotel.DTOs.Hotel.Photo
                {
                    Id = a.Id,
                    Url = a.Path
                }).ToList();
            result.Rooms.Add(room);
        }

        return result;
    }

    public async Task UpdateHotelPhotos(UpdateHotelPhotosInput input)
    {
        var dbPhotos = await _dbContext.HotelPhotos
            .Where(a => a.Enabled && a.HotelId == input.Id).ToListAsync();
        var addList = new List<HotelPhotos>();
        var all = new List<long>();
        foreach (var rooms in input.Rooms)
        {
            for (int i = 0; i < rooms.Photos.Count; i++)
            {
                var photo = rooms.Photos[i];
                var dbPhoto = dbPhotos.FirstOrDefault(a => a.Id == photo.Id);
                if (dbPhoto is null)
                {
                    //新增
                    addList.Add(new HotelPhotos()
                    {
                        Enabled = true,
                        Path = photo.Url,
                        Sort = i,
                        HotelId = input.Id,
                        HotelRoomId = rooms.Id
                    });
                    continue;
                }

                all.Add(photo.Id);
                if (dbPhoto.Sort != i)
                {
                    //排序
                    dbPhoto.Sort = i;
                    dbPhoto.UpdateTime = DateTime.Now;
                }
            }
        }

        await _dbContext.AddRangeAsync(addList);

        //删除
        var deleteList = dbPhotos.Where(a => !all.Contains(a.Id)).ToList();
        deleteList.ForEach(a => a.Enabled = false);

        await _dbContext.SaveChangesAsync();
    }

    public async Task<GetHotelRoomsOutput> GetHotelRooms(GetHotelRoomsInput input)
    {
        var hotelName = _dbContext.Hotels.AsNoTracking()
            .FirstOrDefault(x => x.Id == input.HotelId)?.ZHName ?? "";
        var roomlist = await _dbContext.HotelRooms.AsNoTracking()
            .Where(x => x.HotelId == input.HotelId)
            .OrderBy(x => x.Sort).ToListAsync();

        var pricelist = await _dbContext.PriceStrategies.AsNoTracking()
            .Where(s => roomlist.Select(x => x.Id).Contains(s.HotelRoomId))
            .Select(x => x.HotelRoomId).ToListAsync();

        var roomPhotos = await GetRoomPhoto(new GetRoomPhotoInput
        {
            HotelId = input.HotelId
        });

        var hotelRoom = new List<HotelRoomsOutput>();
        roomlist.ForEach(x =>
        {
            var entity = new HotelRoomsOutput()
            {
                Id = x.Id,
                AreaMax = x.AreaMax,
                AreaMin = x.AreaMin,
                FloorMax = x.FloorMax,
                FloorMin = x.FloorMin,
                MaximumOccupancy = x.MaximumOccupancy,
                RoomQuantity = x.RoomQuantity,
                BedType = !string.IsNullOrWhiteSpace(x.BedType) ? JsonConvert.DeserializeObject<List<BedType>>(x.BedType) : null,
                Sort = x.Sort,
                WindowType = x.WindowType,
                ZHName = x.ZHName,
                ENName = x.ENName,
                PriceStrategyCount = pricelist.Count(s => s == x.Id),
                Photos = roomPhotos.FirstOrDefault(r => r.RoomId == x.Id)?.Photos,
                Viewable = x.Viewable,
                ResourceRoomId = x.ResourceRoomId,
                Description = x.Description
            };
            hotelRoom.Add(entity);
        });
        return new GetHotelRoomsOutput
        {
            HotelName = hotelName,
            HotelRooms = hotelRoom
        };
    }

    public async Task UpdateHotelRoomSort(UpdateHotelRoomSortInput input)
    {
        var dbRooms = await _dbContext.HotelRooms
            .Where(a => input.Id.Contains(a.Id))
            .ToListAsync();

        for (int i = 0; i < input.Id.Count; i++)
        {
            var model = dbRooms.FirstOrDefault(a => a.Id == input.Id[i]);
            if (model != null && model.Sort != i)
            {
                model.Sort = i;
                model.UpdateTime = DateTime.Now;
            }
        }

        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 通过房型Id列表查询房型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<List<GetHotelRoomDetailsOutput>> GetHotelRoomDetails(List<long> roomIds)
    {
        var result = await _dbContext.HotelRooms.AsNoTracking()
            .Select(x => new GetHotelRoomDetailsOutput
            {
                Id = x.Id,
                AreaMax = x.AreaMax,
                AreaMin = x.AreaMin,
                FloorMax = x.FloorMax,
                FloorMin = x.FloorMin,
                HotelId = x.HotelId,
                MaximumOccupancy = x.MaximumOccupancy,
                RoomQuantity = x.RoomQuantity,
                Sort = x.Sort,
                WindowType = x.WindowType,
                ZHName = x.ZHName,
                ENName = x.ENName,
                BedType = JsonConvert.DeserializeObject<List<BedType>>(x.BedType) ?? new List<BedType>(),
                ResourceRoomId = x.ResourceRoomId,
                Viewable = x.Viewable,
                Description = x.Description
            })
            .Where(x => roomIds.Contains(x.Id))
            .ToListAsync();

        var roomPhotos = await GetRoomPhoto(new GetRoomPhotoInput
        {
            RoomIds = roomIds
        });

        foreach (var item in result)
        {
            item.Photos = roomPhotos.FirstOrDefault(x => x.RoomId == item.Id)?.Photos;
        }

        return result;
    }

    public async Task<List<GetHotelRoomDetailsOutput>> GetHotelRoomDetails(GetHotelRoomDetailInput input)
    {
        var result = await _dbContext.HotelRooms.AsNoTracking()
            .Select(x => new GetHotelRoomDetailsOutput
            {
                Id = x.Id,
                AreaMax = x.AreaMax,
                AreaMin = x.AreaMin,
                FloorMax = x.FloorMax,
                FloorMin = x.FloorMin,
                HotelId = x.HotelId,
                MaximumOccupancy = x.MaximumOccupancy,
                RoomQuantity = x.RoomQuantity,
                Sort = x.Sort,
                WindowType = x.WindowType,
                ZHName = x.ZHName,
                ENName = x.ENName,
                BedType = JsonConvert.DeserializeObject<List<BedType>>(x.BedType) ?? new List<BedType>(),
                ResourceRoomId = x.ResourceRoomId,
                Viewable = x.Viewable,
                Description = x.Description
            })
            .WhereIF(input.HotelIds.Any(), x => input.HotelIds.Contains(x.HotelId))
            .WhereIF(input.RoomIds.Any(), x => input.RoomIds.Contains(x.Id))
            .ToListAsync();

        var roomPhotos = await GetRoomPhoto(new GetRoomPhotoInput
        {
            RoomIds = input.RoomIds
        });

        foreach (var item in result)
        {
            item.Photos = roomPhotos.FirstOrDefault(x => x.RoomId == item.Id)?.Photos;
        }

        return result;
    }

    [UnitOfWork]
    public async Task UpdateHotelRoom(UpdateHotelRoomInput input)
    {
        var room = await _dbContext.HotelRooms.FindAsync(input.Id);

        room.AreaMax = input.AreaMax;
        room.AreaMin = input.AreaMin;
        room.FloorMax = input.FloorMax;
        room.FloorMin = input.FloorMin;
        //room.HotelId = input.HotelId;
        room.MaximumOccupancy = input.MaximumOccupancy;
        room.RoomQuantity = input.RoomQuantity;
        room.WindowType = input.WindowType;
        room.ZHName = input.ZHName;
        room.UpdateTime = DateTime.Now;
        room.BedType = JsonConvert.SerializeObject(input.BedType);
        room.Description = input.Description;

        var tenantId = _httpContextAccessor.HttpContext.GetTenantId();
        var command = new UpdateTotalQuantityMessage
        {
            TenantId = tenantId,
            ProductId = input.Id,
            TotalQuantity = input.RoomQuantity
        };


        await _capPublisher.PublishAsync(CapTopics.Inventory.ConfigTotalQuantity, command);
        await _dbContext.SaveChangesAsync();
        //房型变更通知
        await _mediator.Publish(new RoomChangeNotification { TenantId = room.TenantId, HotelId = room.HotelId, RoomId = room.Id });
    }

    public async Task<GetOperateInfoOutput> GetOperateInfo(long hotelId)
    {
        var hotel = await _dbContext.Hotels.AsNoTracking()
            .Where(s => s.Id == hotelId)
            .Select(s => new GetOperateInfoOutput
            {
                OperatingModel = s.OperatingModel,
                ZHName = s.ZHName,
                ENName = s.ENName,
                CountryName = s.CountryName,
                CityName = s.CityName,
                ServiceTimeBegin = s.ServiceTimeBegin,
                ServiceTimeEnd = s.ServiceTimeEnd,
                ServiceEndtimeInNextDay = s.ServiceEndtimeInNextDay,
                CityCode = s.CityCode,
            })
            .FirstOrDefaultAsync();

        if (hotel is null)
            throw new BusinessException("无数据");
        var cities = await Resource_City_Query(new QueryInput() { CityCodes = new int[] { hotel.CityCode } });
        var city = cities.FirstOrDefault();
        hotel.EnCityName = city?.ENName;
        hotel.EnCountryName = city?.CountryEnName;

        return hotel;
    }

    public async Task<IEnumerable<GetAllGroupbyCityOutput>> GetAllGroupbyCity()
    {
        var hotels = await _dbContext.Hotels.AsNoTracking()
            .Where(s => s.Enabled)
            .Select(s => new
            {
                s.CityCode,
                s.CityName,
                s.Id,
                s.ZHName,
                s.ENName,
                s.Address
            }).ToListAsync();

        var result = hotels.GroupBy(x => new { x.CityCode, x.CityName })
            .Select(x => new GetAllGroupbyCityOutput()
            {
                CityCode = x.Key.CityCode,
                CityName = x.Key.CityName,
                Hotels = x.Select(h => new CityHotel()
                {
                    Id = h.Id,
                    ZHName = h.ZHName,
                    ENName = h.ENName,
                    Address = h.Address
                })
            });

        return result;
    }

    public async Task<IEnumerable<CheckIsEnabledOutput>> CheckIsEnabled(CheckIsEnabledInput input)
    {
        var hotels = await _dbContext.Hotels.AsNoTracking()
            .Where(s => input.HotelIds.Contains(s.Id))
            .Select(s => new
            {
                s.Id,
                s.ZHName,
                s.OperatingModel,
                s.CountryCode,
                s.CountryName,
                s.ProvinceCode,
                s.ProvinceName,
                s.CityCode,
                s.CityName,
                s.ServiceTimeBegin,
                s.ServiceTimeEnd,
                s.Address,
                s.Location,
                s.Enabled,
                s.CoordinateType,
                s.ResourceHotelId
            })
            .ToListAsync();

        var result = input.HotelIds
            .Select(s =>
            {
                var hotel = hotels.FirstOrDefault(x => x.Id == s);
                if (hotel is null)
                    return new CheckIsEnabledOutput()
                    {
                        Id = s,
                        Enabled = false,
                        Exists = false
                    };

                return new CheckIsEnabledOutput
                {
                    Id = s,
                    Name = hotel.ZHName,
                    Enabled = hotel.Enabled,
                    OperatingModel = hotel.OperatingModel,
                    CountryCode = hotel.CountryCode,
                    CountryName = hotel.CountryName,
                    ProvinceCode = hotel.ProvinceCode,
                    ProvinceName = hotel.ProvinceName,
                    CityCode = hotel.CityCode,
                    CityName = hotel.CityName,
                    Address = hotel.Address,
                    Longitude = hotel.Location.X,
                    Latitude = hotel.Location.Y,
                    ServiceTimeBegin = hotel.ServiceTimeBegin,
                    ServiceTimeEnd = hotel.ServiceTimeEnd,
                    CoordinateType = hotel.CoordinateType,
                    ResourceHotelId = hotel.ResourceHotelId
                };
            });

        return result;
    }

    public async Task UpdateAutoConfirmRoomStatus(UpdateAutoConfirmRoomStatusInput input)
    {
        var hotel = await _dbContext.Hotels.FirstOrDefaultAsync(a => a.Id == input.HotelId);
        if (hotel is null) throw new BusinessException("无数据");

        hotel.IsAutoConfirmRoomStatus = input.IsAutoConfirmRoomStatus;
        hotel.UpdateTime = DateTime.Now;
        await _dbContext.SaveChangesAsync();
    }

    [UnitOfWork]
    public async Task DeleteHotel(DeleteHotelInput input)
    {
        //删除酒店
        var hotel = await _dbContext.Hotels.FirstOrDefaultAsync(a => a.Id == input.HotelId);
        var tenantId = hotel.TenantId;

        _dbContext.Hotels.Remove(hotel);
        //删除酒店设施
        var facilitiesList = await _dbContext.HotelFacilities.Where(a => a.HotelId == input.HotelId).ToListAsync();
        _dbContext.HotelFacilities.RemoveRange(facilitiesList);
        //删除酒店和房型图片
        var photosList = await _dbContext.HotelPhotos.Where(a => a.HotelId == input.HotelId).ToListAsync();
        _dbContext.HotelPhotos.RemoveRange(photosList);
        //删除房型
        var rooms = await _dbContext.HotelRooms.Where(a => a.HotelId == input.HotelId).ToListAsync();
        _dbContext.HotelRooms.RemoveRange(rooms);
        //删除价格计划
        var strategies = await _dbContext.PriceStrategies.Where(a => a.HotelId == input.HotelId).ToListAsync();
        _dbContext.PriceStrategies.RemoveRange(strategies);
        //删除代运营设置
        var otaSettings = await _dbContext.OtaSettings.Where(a => a.HotelId == input.HotelId).ToListAsync();
        _dbContext.RemoveRange(otaSettings);
        //删除渠道设置
        var markups = await _dbContext.ChannelMarkups.Where(a => a.HotelId == input.HotelId).ToListAsync();
        _dbContext.RemoveRange(markups);
        //删除日历价格
        var calendarPrices = await _dbContext.PriceStrategyCalendarPrices.Where(a => a.HotelId == input.HotelId).ToListAsync();
        _dbContext.RemoveRange(calendarPrices);
        //删除酒店冗余信息
        var redundantDatas = await _dbContext.HotelRedundantDatas.Where(a => a.HotelId == input.HotelId).ToListAsync();
        _dbContext.RemoveRange(redundantDatas);

        //删除已配置的佣金设置
        var command = new RemoveProductCommissionMessage
        {
            TenantId = tenantId,
            ProductId = input.HotelId,
            ProductType = ProductType.Hotel
        };
        await _capPublisher.PublishAsync(CapTopics.User.RemoveProductCommission, command);

        //酒店下架通知
        await _mediator.Publish(new HotelChangeNotification
        {
            TenantId = _httpContextAccessor.HttpContext.GetTenantId(),
            HotelId = input.HotelId,
            Type = 3
        });
    }

    public async Task<List<GetAllHotelOutput>> GetAllHotel()
    {
        return await _dbContext.Hotels.AsNoTracking()
            .Where(x => x.Enabled)
            .OrderByDescending(x => x.UpdateTime)
            .Select(x => new GetAllHotelOutput
            {
                Id = x.Id,
                Name = x.ZHName
            })
            .ToListAsync();
    }

    public async Task<List<GetCitiesOutput>> GetCities(long tenantId, bool isCache, bool needAvailable)
    {
        if (!isCache)
            return await GetCities(needAvailable);

        var cacheKey = $"hotel:hotelcities:{tenantId}:{(needAvailable ? 1 : 0)}";
        var cities = await _redisClient.StringGetAsync<List<GetCitiesOutput>>(cacheKey);

        if (cities != null)
            return cities;

        cities = await GetCities(needAvailable);
        await _redisClient.StringSetAsync(cacheKey, cities, TimeSpan.FromMinutes(10));
        return cities;
    }

    private async Task<List<GetCitiesOutput>> GetCities(bool needAvailable)
    {
        var cities = await _dbContext.Hotels.AsNoTracking()
            .WhereIF(needAvailable, x => x.Enabled)
            .GroupBy(x => new
            {
                x.CityCode,
                x.CityName,
                x.CountryCode,
                x.CountryName
            })
            .Select(x => new GetCitiesOutput()
            {
                CityCode = x.Key.CityCode,
                CityName = x.Key.CityName,
                CountryCode = x.Key.CountryCode,
                CountryName = x.Key.CountryName
            })
            .ToListAsync();

        return cities.OrderBy(x => x.CityCode).ToList();
    }

    /// <summary>
    /// 通过酒店id返回设施model列表
    /// </summary>
    /// <param name="hotelIds"></param>
    /// <returns></returns>
    public async Task<List<HotelFacilities>> GetFacilitiesByIds(List<long> hotelIds)
    {
        var infoData = await _dbContext.HotelFacilities.AsNoTracking()
            .Where(x => hotelIds.Contains(x.HotelId))
            .ToListAsync();
        return infoData;
    }

    /// <summary>
    /// 通过酒店Id返回酒店model列表
    /// </summary>
    /// <param name="hotelIds"></param>
    /// <returns></returns>
    public async Task<List<Model.Hotel>> GetHotelsModelByIds(List<long> hotelIds)
    {
        var infoData = await _dbContext.Hotels.AsNoTracking()
            .Where(x => hotelIds.Contains(x.Id))
            .ToListAsync();
        return infoData;
    }

    /// <summary>
    /// 查询房型图片
    /// </summary>
    /// <param name="roomIds"></param>
    /// <returns></returns>
    public async Task<List<GetRoomPhotoOutput>> GetRoomPhoto(GetRoomPhotoInput input)
    {
        var photos = await _dbContext.HotelPhotos.AsNoTracking()
            .Where(x => x.Enabled)
            .WhereIF(input.HotelId > 0, x => x.HotelId == input.HotelId)
            .WhereIF(input.RoomIds.Any(), x => input.RoomIds.Contains(x.HotelRoomId))
            .OrderBy(x => x.Sort)
            .Select(x => new { x.HotelRoomId, x.Path })
            .ToListAsync();

        var result = photos
            .GroupBy(x => x.HotelRoomId)
            .Select(x => new GetRoomPhotoOutput
            {
                RoomId = x.Key,
                Photos = x.Select(g => g.Path)
            }).ToList();
        return result;
    }

    /// <summary>
    /// 设置房型显示状态
    /// </summary>
    public async Task SetRoomViewable(SetHotelRoomEnabledInput input)
    {
        var room = await _dbContext.HotelRooms
            .FirstOrDefaultAsync(x => x.Id == input.HotelRoomId);
        room.Viewable = input.Viewable;
        room.UpdateTime = DateTime.Now;
        await _dbContext.SaveChangesAsync();
    }

    #region 同步新增酒店房型

    public async Task SyncHotelRooms(SyncHotelRoomsInput input)
    {
        using var httpContent = new StringContent(JsonConvert.SerializeObject(new GetApiSettingInfosInput
        {
            SupplierApiType = Contracts.Common.Tenant.Enums.SupplierApiType.Hop,
            SupplierEnabled = true,
        }), Encoding.UTF8, "application/json");
        var supplierApiSettings = await _httpClientFactory.InternalPostAsync<List<SupplierApiSettingDto>>(
            _servicesAddress.Tenant_GetApiSettingInfos(),
            null,
            httpContent: httpContent);
        var tenantIds = supplierApiSettings.Select(s => s.TenantId).ToArray();
        if (tenantIds.Any() is not true)
            return;
        //获取所有引用的商户酒店
        var hotels = await _dbContext.Hotels
            .IgnoreQueryFilters()
            .Where(x => x.ResourceHotelId == input.HotelId && tenantIds.Contains(x.TenantId))
            .Select(x => new
            {

                x.Id,
                x.TenantId
            })
            .ToListAsync();

        if (!hotels.Any())
            return;

        var hotelIds = hotels.Select(x => x.Id).ToArray();

        var resHotelRooms = await _dbContext.HotelRooms.IgnoreQueryFilters()
            .Where(x => hotelIds.Contains(x.HotelId))
            .ToListAsync();
        var resHotelPhotos = await _dbContext.HotelPhotos.IgnoreQueryFilters()
            .Where(x => hotelIds.Contains(x.HotelId) && x.HotelRoomId > 0 && x.Enabled)
            .ToListAsync();

        var configs = await _dbContext.HotelOperationConfigs
           .IgnoreQueryFilters()
           .Where(x => hotelIds.Contains(x.HotelId))
           .Select(x => new { x.HotelId, x.AllowSelfMaintainRoom })
           .ToListAsync();

        foreach (var hotel in hotels)
        {
            var resRooms = resHotelRooms.Where(x => x.HotelId == hotel.Id).ToList();
            foreach (var room in input.Rooms)
            {
                var resHotelRoom = resRooms.FirstOrDefault(r => r.ResourceRoomId == room.Id);
                var photos = input.Photos.Where(p => p.HotelRoomId == room.Id);
                if (resHotelRoom is null)
                {
                    var hotelRoom = new HotelRoom()
                    {
                        HotelId = hotel.Id,
                        ResourceRoomId = room.Id,
                        ZHName = room.ZHName,
                        ENName = room.ENName,
                        MaximumOccupancy = room.MaximumOccupancy,
                        RoomQuantity = room.RoomQuantity,
                        BedType = room.BedType,
                        WindowType = room.WindowType,
                        AreaMin = room.AreaMin,
                        AreaMax = room.AreaMax,
                        FloorMin = room.FloorMin,
                        FloorMax = room.FloorMax,
                        Sort = 0
                    };
                    hotelRoom.SetTenantId(hotel.TenantId);
                    var hotelPhotos = photos
                        .Select((t, i) => new HotelPhotos()
                        {
                            HotelId = hotel.Id,
                            HotelRoomId = hotelRoom.Id,
                            Path = t.Path,
                            Sort = i,
                            Enabled = true
                        })
                        .ToList();
                    foreach (var hotelPhoto in hotelPhotos)
                        hotelPhoto.SetTenantId(hotel.TenantId);
                    await _dbContext.HotelRooms.AddAsync(hotelRoom);
                    await _dbContext.HotelPhotos.AddRangeAsync(hotelPhotos);
                }
                else
                {
                    var config = configs.FirstOrDefault(x => x.HotelId == resHotelRoom.HotelId);
                    if (config?.AllowSelfMaintainRoom is true)//自行维护房型信息
                        continue;
                    resHotelRoom.ZHName = room.ZHName;
                    resHotelRoom.ENName = room.ENName;
                    resHotelRoom.MaximumOccupancy = room.MaximumOccupancy;
                    resHotelRoom.RoomQuantity = room.RoomQuantity;
                    resHotelRoom.BedType = room.BedType;
                    resHotelRoom.WindowType = room.WindowType;
                    if (room.AreaMin > 0)
                        resHotelRoom.AreaMin = room.AreaMin;
                    if (room.AreaMax > 0)
                        resHotelRoom.AreaMax = room.AreaMax;
                    if (room.FloorMin > 0)
                        resHotelRoom.FloorMin = room.FloorMin;
                    if (room.FloorMax > 0)
                        resHotelRoom.FloorMax = room.FloorMax;

                    var resPhotoPaths = resHotelPhotos.Where(x => x.HotelRoomId == resHotelRoom.Id).Select(x => x.Path).ToArray();
                    var hotelPhotos = photos
                        .Where(x => resPhotoPaths.Contains(x.Path) is false)
                        .Select((t, i) => new HotelPhotos()
                        {
                            HotelId = hotel.Id,
                            HotelRoomId = resHotelRoom.Id,
                            Path = t.Path,
                            Sort = i,
                            Enabled = true
                        })
                        .ToList();
                    foreach (var hotelPhoto in hotelPhotos)
                        hotelPhoto.SetTenantId(hotel.TenantId);
                    await _dbContext.AddRangeAsync(hotelPhotos);
                }
            }
        }
        await _dbContext.SaveChangesAsync();
    }

    public async Task SyncHotelInfo(SyncHotelInfosInput receive)
    {
        using var httpContent = new StringContent(JsonConvert.SerializeObject(new GetApiSettingInfosInput
        {
            SupplierApiType = Contracts.Common.Tenant.Enums.SupplierApiType.Hop,
            SupplierEnabled = true,
        }), Encoding.UTF8, "application/json");
        var supplierApiSettings = await _httpClientFactory.InternalPostAsync<List<SupplierApiSettingDto>>(
            _servicesAddress.Tenant_GetApiSettingInfos(),
            null,
            httpContent: httpContent);
        var tenantIds = supplierApiSettings.Select(s => s.TenantId).ToArray();
        if (tenantIds.Any() is not true)
            return;

        //获取所有引用的商户酒店
        var hotels = await _dbContext.Hotels
            .IgnoreQueryFilters()
            .Where(x => x.ResourceHotelId == receive.Id && tenantIds.Contains(x.TenantId))
            .ToListAsync();
        var hotelIds = hotels.Select(s => s.Id).ToArray();
        var configs = await _dbContext.HotelOperationConfigs
            .IgnoreQueryFilters()
            .Where(x => hotelIds.Contains(x.HotelId))
            .Select(x => new { x.HotelId, x.AllowSelfMaintainHotel })
            .ToListAsync();

        foreach (var hotel in hotels)
        {
            var config = configs.FirstOrDefault(x => x.HotelId == hotel.Id);
            if (config?.AllowSelfMaintainHotel is true)//自行维护酒店信息
                continue;
            hotel.ZHName = receive.ZHName;
            hotel.ENName = receive.ENName;
            hotel.CountryCode = receive.CountryCode;
            hotel.CountryName = receive.CountryName;
            hotel.ProvinceCode = receive.ProvinceCode;
            hotel.ProvinceName = receive.ProvinceName;
            hotel.CityCode = receive.CityCode;
            hotel.CityName = receive.CityName;
            hotel.StarLevel = receive.StarLevel;
            hotel.Address = receive.Address;
            hotel.ENAddress = receive.ENAddress;
            hotel.CoordinateType = receive.CoordinateType;
            hotel.SetLocation(receive.Longitude, receive.Latitude);
            hotel.Telephone = receive.Telephone;
            hotel.Telefax = receive.Telefax;
            //仅汇智商户需要同步发单邮箱
            if (hotel.TenantId == _huiZhiHotelConfig?.Value.TenantId)
                hotel.ReceiptEmail = receive.ReceiptEmail;
            if (receive.OriginInfo is not null)
            {
                if (hotel.Intro == receive.OriginInfo.Intro)
                    hotel.Intro = receive.Intro;
                if (hotel.ImportantNotices == receive.OriginInfo.ImportantNotices)
                    hotel.ImportantNotices = receive.ImportantNotices;
                if (hotel.SurroundingFacilities == receive.OriginInfo.SurroundingFacilities)
                    hotel.SurroundingFacilities = receive.SurroundingFacilities;
                if (hotel.CheckinPolicy == receive.OriginInfo.CheckinPolicy)
                    hotel.CheckinPolicy = receive.CheckinPolicy;
            }
            hotel.UpdateTime = DateTime.Now;
        }
        await _dbContext.SaveChangesAsync();
    }

    #endregion

    public async Task<IEnumerable<GetHotelFirstPhotoOutput>> GetHotelFirstPhoto(long[] ids)
    {
        var hotelPhotos = await _dbContext.HotelPhotos.AsNoTracking()
            .Where(x => ids.Contains(x.HotelId)
                        && x.HotelRoomId == 0
                        && x.Enabled
                        && x.Sort == 0
                        && x.HotelPhotoType != Contracts.Common.Resource.Enums.HotelPhotoType.Video)
            .Select(x => new GetHotelFirstPhotoOutput
            {
                HotelId = x.HotelId,
                Path = x.Path
            })
            .ToListAsync();

        //过滤mp4
        hotelPhotos = hotelPhotos
            .Where(x => !x.Path.EndsWith(".mp4", StringComparison.OrdinalIgnoreCase))
            .ToList();

        var result = hotelPhotos.GroupBy(x => x.HotelId)
            .Select(x => new GetHotelFirstPhotoOutput
            {
                HotelId = x.Key,
                Path = x.FirstOrDefault()?.Path
            });
        return result;
    }

    #region 分销商web

    /// <summary>
    /// 查询酒店分页
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<PagingModel<AgencySearchOutput>> AgencySearch(AgencySearchInput input)
    {
        var result = new PagingModel<AgencySearchOutput>();

        //查询价格策略数据
        var priceStrategyList = await _dbContext.PriceStrategies.AsNoTracking()
            .Where(x => x.Enabled)
            .Where(x => input.PriceStrategyId.Contains(x.Id))
            .WhereIF(input.MaximumOccupancy.HasValue, x => x.MaximumOccupancy >= input.MaximumOccupancy!.Value)
            .Select(x => new
            {
                x.Id,
                x.HotelId,
                x.CostCurrencyCode,
                x.SaleCurrencyCode
            })
            .ToListAsync();
        if (priceStrategyList.Any() is false) return result;
        input.PriceStrategyId = priceStrategyList.Select(x => x.Id).ToList();

        var compare = DateTime.Compare(input.LeaveDate, DateTime.Now.AddDays(30));
        var endDate = compare > 0
            ? input.LeaveDate
            : DateTime.Now.AddDays(30);
        var priceStrategyCalendarPrices = await _dbContext.PriceStrategyCalendarPrices.AsNoTracking()
            .Where(a => input.PriceStrategyId.Contains(a.PriceStrategyId)
                        && a.Date >= input.LiveDate
                        && a.Date <= endDate
                        && a.SalePrice > 0)
            .Select(x => new
            {
                x.SalePrice,
                x.CostPrice,
                x.HotelId,
                x.PriceStrategyId,
                x.Date
            })
            .ToListAsync();

        //查询对应入离日期有价格的酒店id
        var hotelIds = priceStrategyCalendarPrices
            .Where(x => x.Date >= input.LiveDate
                        && x.Date <= input.LeaveDate
                        && x.SalePrice > 0)
            .GroupBy(x => x.HotelId)
            .Select(x => x.Key)
            .ToList();
        if (hotelIds.Any() is false) return result;

        var pageData = await _dbContext.Hotels.AsNoTracking()
            .Where(x => x.Enabled)
            .WhereIF(hotelIds.Any(), x => hotelIds.Contains(x.Id))
            .WhereIF(input.CityCode > 0, x => x.CityCode == input.CityCode)
            .WhereIF(input.StarLevel > 0, x => x.StarLevel == input.StarLevel)
            .WhereIF(!string.IsNullOrEmpty(input.Name), x => x.ZHName.Contains(input.Name))
            .OrderByDescending(x => x.UpdateTime)
            .PagingAsync(input.PageIndex, input.PageSize, x => new AgencySearchOutput
            {
                HotelId = x.Id,
                ZHName = x.ZHName,
                ENName = x.ENName,
                CityCode = x.CityCode,
                CityName = x.CityName,
                CountryCode = x.CountryCode,
                CountryName = x.CountryName,
                ProvinceCode = x.ProvinceCode,
                ProvinceName = x.ProvinceName,
                DistrictCode = x.DistrictCode,
                DistrictName = x.DistrictName,
                Address = x.Address,
                StarLevel = x.StarLevel,
                Latitude = x.Location.Y,
                Longitude = x.Location.X
            });

        //查询酒店设施id
        var ids = pageData.Data.Select(x => x.HotelId).ToList();
        var facilities = await GetFacilitiesByIds(ids);
        //图片
        var photos = await _dbContext.HotelPhotos.AsNoTracking()
            .Where(x => input.HotelId.Contains(x.HotelId) && x.Enabled && x.HotelRoomId == 0)
            .OrderBy(x => x.Sort)
            .Select(x => new
            {
                x.HotelId,
                x.Path
            }).ToListAsync();

        //30天最低价
        var calendarPrices = priceStrategyCalendarPrices
            .Where(a => a.Date >= input.LiveDate
                        && a.Date <= endDate
                        && a.SalePrice > 0)
            .Select(x => new
            {
                x.SalePrice,
                x.CostPrice,
                x.HotelId,
                x.PriceStrategyId
            })
            .GroupBy(x => x.HotelId)
            .Select(x => new
            {
                HotelId = x.Key,
                MinSalePrice = x.Min(m => m.SalePrice),
                MinCostPrice = x.Min(m => m.CostPrice),
                MaxSalePrice = x.Max(m => m.SalePrice),
                MaxCostPrice = x.Max(m => m.CostPrice),
                MinSalePriceStrategyId = x.MinBy(m => m.SalePrice).PriceStrategyId,
                MinCostPriceStrategyId = x.MinBy(m => m.CostPrice).PriceStrategyId,
            })
            .ToList();

        foreach (var item in pageData.Data)
        {
            item.FacilitiesList = facilities.Where(x => x.HotelId == item.HotelId)
                .Select(x => x.FacilityId).ToList();

            item.FirstPhoto = photos.FirstOrDefault(x => x.HotelId == item.HotelId)?.Path;
            var calendarPrice = calendarPrices.First(x => x.HotelId == item.HotelId);
            item.MinSalePrice = calendarPrice.MinSalePrice;
            item.MinCostPrice = calendarPrice.MinCostPrice;
            item.MaxSalePrice = calendarPrice.MaxSalePrice;
            item.MaxCostPrice = calendarPrice.MaxCostPrice;
            //最低售价策略的采购价币种
            item.MinSalePriceCurrencyCode =
                priceStrategyList.FirstOrDefault(x => x.Id == calendarPrice.MinSalePriceStrategyId)?.CostCurrencyCode;
            //最低采购价策略的采购价币种
            item.MinCostPriceCurrencyCode =
                priceStrategyList.FirstOrDefault(x => x.Id == calendarPrice.MinCostPriceStrategyId)?.CostCurrencyCode;

            //酒店价格策略数据
            foreach (var strategyItem in priceStrategyList.Where(x => x.HotelId == item.HotelId))
            {
                var strategyCalenderPrice = priceStrategyCalendarPrices
                    .Where(x => x.PriceStrategyId == strategyItem.Id)
                    .ToList();

                if (strategyCalenderPrice.Any() is false) continue;

                item.StrategyMinPrices.Add(new AgencyLocalHotelStrategyMinPriceDto
                {
                    PriceStrategyId = strategyItem.Id,
                    MinCostPriceCurrencyCode = strategyItem.CostCurrencyCode,
                    MinSalePriceCurrencyCode = strategyItem.SaleCurrencyCode,
                    MinSalePrice = strategyCalenderPrice.Min(m => m.SalePrice),
                    MinCostPrice = strategyCalenderPrice.Min(m => m.CostPrice),
                    MaxSalePrice = strategyCalenderPrice.Max(m => m.SalePrice),
                    MaxCostPrice = strategyCalenderPrice.Max(m => m.CostPrice),
                });
            }
        }

        return pageData;
    }

    #endregion

    public async Task<List<(long hotelId, long facilityId)>> SyncEsSearchFacility(List<long> hotelIds, long tenantId)
    {
        var facilities = await _dbContext.HotelFacilities.IgnoreQueryFilters()
            .Where(x => x.TenantId == tenantId)
            .Where(x => hotelIds.Contains(x.HotelId))
            .Select(x => new ValueTuple<long, long>(x.HotelId, x.FacilityId))
            .ToListAsync();
        return facilities;
    }

    public async Task<List<long>> GetIncludeGroupRoomHotel(List<long> strategyIds)
    {
        var strategyHotelIds = await _dbContext.PriceStrategies.AsNoTracking()
            .Where(x => strategyIds.Contains(x.Id)
                        && x.PriceStrategyType == PriceStrategyType.GroupRoom
                        && x.Enabled)
            .Select(x => x.HotelId)
            .ToListAsync();
        var groupRoomHotelIds = strategyHotelIds.Distinct().ToList();
        return groupRoomHotelIds;
    }

    public async Task<List<GetHotelIdsByResourceHotelIdsOutput>> GetHotelIdsByResourceHotelIds(List<long> ids)
    {
        var result = await _dbContext.Hotels.AsNoTracking()
                .Where(x => ids.Contains(x.ResourceHotelId))
                .Select(x => new GetHotelIdsByResourceHotelIdsOutput
                {
                    HotelId = x.Id,
                    ResourceHotelId = x.ResourceHotelId,
                    ZHName = x.ZHName,
                    ENName = x.ENName,
                })
                .ToListAsync();

        return result;
    }

    public async Task<List<GetHotelIdsByResourceHotelIdsOutput>> GetResourceHotelIdByHotelIds(List<long> ids)
    {
        var result = await _dbContext.Hotels.AsNoTracking()
                .Where(x => ids.Contains(x.Id))
                .Select(x => new GetHotelIdsByResourceHotelIdsOutput
                {
                    HotelId = x.Id,
                    ResourceHotelId = x.ResourceHotelId
                })
                .ToListAsync();

        return result;
    }

    public async Task<HotelPolicyOutput> DetailPolicy(HotelPolicyDetailInput input)
    {
        var result = new HotelPolicyOutput();
        var hotelExtend = await _dbContext.HotelExtend.AsNoTracking()
            .Where(x => x.HotelId == input.Id)
            .FirstOrDefaultAsync();

        var hotelExtraBedPolicies = await _dbContext.HotelExtraBedPolicy.AsNoTracking()
            .Where(x => x.HotelId == input.Id)
            .ToListAsync();

        var hotelMealChildPolicies = await _dbContext.HotelMealChildPolicy.AsNoTracking()
            .Where(x => x.HotelId == input.Id)
            .ToListAsync();

        var hotelMealPolicite = await _dbContext.HotelMealPolicy.AsNoTracking()
            .Where(x => x.HotelId == input.Id)
            .ToListAsync();

        var hotelPolicyExtends = await _dbContext.HotelPolicyExtend.AsNoTracking()
            .Where(x => x.HotelId == input.Id)
            .WhereIF(!string.IsNullOrEmpty(input.Language), x => x.Language == input.Language)
            .ToListAsync();

        result.HotelExtend = _mapper.Map<HotelExtendOutput>(hotelExtend);
        result.HotelExtraBedPolicies = _mapper.Map<List<HotelExtraBedPolicyOutput>>(hotelExtraBedPolicies);
        result.HotelMealChildPolicies = _mapper.Map<List<HotelMealChildPolicyOutput>>(hotelMealChildPolicies);
        result.HotelMealPolicies = _mapper.Map<List<HotelMealPolicyOutput>>(hotelMealPolicite);
        result.HotelPolicyExtends = _mapper.Map<List<HotelPolicyExtendOutput>>(hotelPolicyExtends);

        return result;
    }

    public async Task<List<HotelRoomExtraBedPolicyOutput>> GetHotelRoomExtraBedPolicy(GetHotelRoomExtraBedPolicyInput input)
    {
        var HotelRoomExtraBedPolicies = await _dbContext.HotelRoomExtraBedPolicy.AsNoTracking()
            .Where(x => x.HotelId == input.HotelId)
            .WhereIF(input.HotelRoomIds.Any(), x => input.HotelRoomIds.Contains(x.HotelRoomId))
            .ToListAsync();

        return _mapper.Map<List<HotelRoomExtraBedPolicyOutput>>(HotelRoomExtraBedPolicies);
    }


    private async Task<List<CityOutput>> Resource_City_Query(QueryInput input)
    {
        var httpContent = new StringContent(JsonConvert.SerializeObject(input), Encoding.UTF8, "application/json");
        var output = await _httpClientFactory.InternalPostAsync<List<CityOutput>>(
            requestUri: _servicesAddress.Resource_City_Query(), httpContent: httpContent);
        return output;
    }
}