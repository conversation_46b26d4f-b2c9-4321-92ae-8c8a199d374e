using Contracts.Common.Hotel.Enums;
using EfCoreExtensions.EntityBase;

namespace Hotel.Api.Model;

/// <summary>
/// Api酒店关联第三方酒店信息
/// </summary>
[Obsolete("ApiHotel取HopId,标记过期")]
public class ApiHotelExtend : TenantBase
{
    public ApiHotelExtend()
    {

    }

    public ApiHotelExtend(long resourceHotelId, long thirdHotelId, ApiHotelSourceType apiHotelSourceType = ApiHotelSourceType.Hop)
    {
        ResourceHotelId = resourceHotelId;
        ThirdHotelId = thirdHotelId;
        ApiHotelSourceType = apiHotelSourceType;
    }

    public void Set(long resourceHotelId, long thirdHotelId, ApiHotelSourceType apiHotelSourceType = ApiHotelSourceType.Hop)
    {
        ResourceHotelId = resourceHotelId;
        ThirdHotelId = thirdHotelId;
        ApiHotelSourceType = apiHotelSourceType;
    }
    /// <summary>
    /// 资源酒店ID
    /// </summary>
    public long ResourceHotelId { get; set; }

    /// <summary>
    /// 酒店来源
    /// </summary>
    public ApiHotelSourceType ApiHotelSourceType { get; set; }

    /// <summary>
    /// 第三方酒店Id
    /// </summary>
    public long ThirdHotelId { get; set; }
}
