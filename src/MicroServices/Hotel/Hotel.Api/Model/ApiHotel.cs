using Contracts.Common.Hotel.Enums;
using Contracts.Common.Resource.Enums;
using EfCoreExtensions.EntityBase;
using NetTopologySuite.Geometries;
using System.Text.Json.Serialization;

namespace Hotel.Api.Model;

public class ApiHotel : KeyBase
{
    /// <summary>
    /// 第三方资源酒店ID
    /// </summary>
    public long ResourceHotelId { get; set; }

    /// <summary>
    /// 汇智酒店ID
    /// </summary>
    public int HopId { get; set; }

    /// <summary>
    /// 中文名称
    /// </summary>
    public string ZHName { get; set; }

    /// <summary>
    /// 英文名称
    /// </summary>
    public string? ENName { get; set; }

    /// <summary>
    /// 星级
    /// </summary>
    public decimal StarLevel { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    public int CountryCode { get; set; }

    public string CountryName { get; set; }

    /// <summary>
    /// 省份
    /// </summary>
    public int ProvinceCode { get; set; }

    public string ProvinceName { get; set; }

    /// <summary>
    /// 市
    /// </summary>
    public int CityCode { get; set; }

    public string CityName { get; set; }

    /// <summary>
    /// 经纬度坐标
    /// </summary>
    [JsonIgnore]
    public Point? Location { get; private set; }

    /// <summary>
    /// 经纬度坐标类型
    /// </summary>
    public CoordinateType CoordinateType { get; set; }

    /// <summary>
    /// 0-非主推  1-主推
    /// </summary>
    public bool StaffTag { get; set; }

    /// <summary>
    /// 团房数量
    /// </summary>
    public int ReunionRoom { get; set; }

    /// <summary>
    /// 售卖标签
    /// </summary>
    public ApiHotelTag? Tags { get; set; }

    /// <summary>
    /// 是否可售
    /// </summary>
    public bool SaleFlag { get; set; } = true;
    
    /// <summary>
    /// 详细地址
    /// </summary>
    public string Address { get; set; }

    /// <summary>
    /// 英文地址
    /// </summary>
    public string? ENAddress { get; set; }

    /// <summary>
    /// 设置经纬度
    /// </summary>
    /// <param name="lon">经度</param>
    /// <param name="lat">纬度</param>
    public void SetLocation(double lon, double lat)
    {
        var geometryFactory = NetTopologySuite.NtsGeometryServices.Instance.CreateGeometryFactory(srid: 4326);
        Location = geometryFactory.CreatePoint(new Coordinate(lon, lat));
    }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; } = DateTime.Now;
}
