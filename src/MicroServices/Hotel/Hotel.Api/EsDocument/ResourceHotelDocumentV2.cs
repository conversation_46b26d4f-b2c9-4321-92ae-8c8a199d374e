using Contracts.Common.Resource.Enums;
using Nest;

namespace Hotel.Api.EsDocument;

/// <summary>
/// 资源库 - 酒店文档V2
/// </summary>
public class ResourceHotelDocumentV2
{
    /// <summary>
    /// 酒店Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// HopId
    /// </summary>
    public int HopId { get; set; }

    /// <summary>
    /// 中文名称
    /// </summary>
    public string ZHName { get; set; }

    /// <summary>
    /// 英文名称
    /// </summary>
    public string ENName { get; set; }

    /// <summary>
    /// 国家编码
    /// </summary>
    public int CountryCode { get; set; }

    public string CountryName { get; set; }

    /// <summary>
    /// 省份代码
    /// </summary>
    public int ProvinceCode { get; set; }

    public string ProvinceName { get; set; }

    /// <summary>
    /// 城市代码
    /// </summary>
    public int CityCode { get; set; }

    public string CityName { get; set; }

    /// <summary>
    /// 详细地址
    /// </summary>
    public string Address { get; set; }

    /// <summary>
    /// 英文地址
    /// </summary>
    public string? ENAddress { get; set; }

    /// <summary>
    /// 星级
    /// </summary>
    public decimal StarLevel { get; set; }

    /// <summary>
    /// 地理坐标点
    /// </summary>
    public GeoLocation Location { get; set; }

    /// <summary>
    /// 经纬度坐标类型
    /// </summary>
    public CoordinateType CoordinateType { get; set; }

    /// <summary>
    /// 楼层数
    /// </summary>
    public int Floors { get; set; }

    /// <summary>
    /// 房间数 
    /// </summary>
    public int Rooms { get; set; }

    /// <summary>
    /// 权重值
    /// </summary>
    public long WeightValue { get; set; }
    
    /// <summary>
    /// 是否上架
    /// </summary>
    public bool Enabled { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }
    
    #region 冗余 - 基础设施id

    /// <summary>
    /// 基础设施id
    /// </summary>
    public List<long> FacilityId { get; set; }

    #endregion

    #region 冗余 HopOpenHotel

    /// <summary>
    /// 0-非主推  1-主推
    /// </summary>
    public int StaffTag { get; set; }

    /// <summary>
    /// 团房数量
    /// </summary>
    public int ReunionRoom { get; set; }

    /// <summary>
    /// 售卖标签
    /// </summary>
    public List<SellHotelTag> Tags { get; set; }

    #endregion

    #region 冗余-apiHotel

    /// <summary>
    /// 冗余-apiHotel表Id
    /// </summary>
    public long ApiHotelId { get; set; }

    #endregion

    #region 冗余商户排序配置

    /// <summary>
    /// 冗余商户排序配置
    /// </summary>
    [Nested]
    public List<TenantResourceHotelConfigNested> TenantConfig { get; set; }

    #endregion
}

public class TenantResourceHotelConfigNested
{
    /// <summary>
    /// 租户id
    /// </summary>
    public long TenantId { get; set; }

    #region api hotel 租户配置

    /// <summary>
    /// 是否置顶
    /// </summary>
    public int? OnTop { get; set; } // 0=非置顶, 1=置顶
    
    /// <summary>
    /// 权重值
    /// </summary>
    public long? WeightValue { get; set; }

    /// <summary>
    /// 排序值
    /// </summary>
    public long? SortValue { get; set; }

    #endregion

    #region 本地酒店

    /// <summary>
    /// 本地酒店id
    /// </summary>
    public long? LocalHotelId { get; set; }

    /// <summary>
    /// 是否包含本地酒店
    /// 0=不包含, 1=包含
    /// </summary>
    public int? IncludeLocalHotel { get; set; }

    #endregion
}

/// <summary>
/// 资源酒店更新字段
/// </summary>
public class ResourceHotelDocumentUpdateDto
{
    /// <summary>
    /// 酒店Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// HopId
    /// </summary>
    public int HopId { get; set; }

    /// <summary>
    /// 中文名称
    /// </summary>
    public string ZHName { get; set; }

    /// <summary>
    /// 英文名称
    /// </summary>
    public string ENName { get; set; }

    /// <summary>
    /// 国家编码
    /// </summary>
    public int CountryCode { get; set; }

    public string CountryName { get; set; }

    /// <summary>
    /// 省份代码
    /// </summary>
    public int ProvinceCode { get; set; }

    public string ProvinceName { get; set; }

    /// <summary>
    /// 城市代码
    /// </summary>
    public int CityCode { get; set; }

    public string CityName { get; set; }

    /// <summary>
    /// 详细地址
    /// </summary>
    public string Address { get; set; }

    /// <summary>
    /// 英文地址
    /// </summary>
    public string? ENAddress { get; set; }

    /// <summary>
    /// 星级
    /// </summary>
    public decimal StarLevel { get; set; }

    /// <summary>
    /// 地理坐标点
    /// </summary>
    public GeoLocation Location { get; set; }

    /// <summary>
    /// 经纬度坐标类型
    /// </summary>
    public CoordinateType CoordinateType { get; set; }

    /// <summary>
    /// 楼层数
    /// </summary>
    public int Floors { get; set; }

    /// <summary>
    /// 房间数 
    /// </summary>
    public int Rooms { get; set; }

    /// <summary>
    /// 权重值
    /// </summary>
    public long WeightValue { get; set; }
    
    /// <summary>
    /// 是否上架
    /// </summary>
    public bool Enabled { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }
    
    #region 冗余 - 基础设施id

    /// <summary>
    /// 基础设施id
    /// </summary>
    public List<long> FacilityId { get; set; }

    #endregion
}

/// <summary>
/// hop open 更新字段
/// </summary>
public class ResourceHotelDocumentHopOpenUpdateDto
{
    /// <summary>
    /// 0-非主推  1-主推
    /// </summary>
    public int StaffTag { get; set; }

    /// <summary>
    /// 团房数量
    /// </summary>
    public int ReunionRoom { get; set; }

    /// <summary>
    /// 售卖标签
    /// </summary>
    public List<SellHotelTag> Tags { get; set; }
    
    /// <summary>
    /// 冗余-apiHotel表Id
    /// </summary>
    public long ApiHotelId { get; set; }
}

/// <summary>
/// apihotel - 更新字段
/// </summary>
public class ResourceHotelDocumentApiHotelUpdateDto
{
    /// <summary>
    /// 冗余-apiHotel表Id
    /// </summary>
    public long ApiHotelId { get; set; }
    
    /// <summary>
    /// 0-非主推  1-主推
    /// </summary>
    public int StaffTag { get; set; }

    /// <summary>
    /// 团房数量
    /// </summary>
    public int ReunionRoom { get; set; }

    /// <summary>
    /// 售卖标签
    /// </summary>
    public List<SellHotelTag> Tags { get; set; }
}

/// <summary>
/// HotelFacilities - 更新字段
/// </summary>
public class ResourceHotelDocumentHotelFacilityUpdateDto
{
    /// <summary>
    /// 基础设施id
    /// </summary>
    public List<long> FacilityId { get; set; }
}