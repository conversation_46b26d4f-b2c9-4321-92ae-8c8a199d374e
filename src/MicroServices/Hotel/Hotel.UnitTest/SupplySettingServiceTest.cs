using AutoMapper;
using Common.ServicesHttpClient;
using Contracts.Common.Hotel.DTOs.SupplySetting;
using Contracts.Common.Hotel.Enums;
using Hotel.Api.ConfigModel;
using Hotel.Api.Infrastructure;
using Hotel.Api.Model;
using Hotel.Api.Services;
using Hotel.Api.Services.Interfaces;
using Hotel.Api.Services.MappingProfiles;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Hotel.UnitTest;

public class SupplySettingServiceTest : TestBase<CustomDbContext>
{
    private readonly static long _tenantId = 1;
    
    public SupplySettingService CreateService(
        CustomDbContext dbContext,
        IMapper mapper = null,
        IHttpClientFactory httpClientFactory = null,
        IOptions<ServicesAddress> options = null,
        IBaseHotelService baseHotelService = null)
    {
        options ??= Options.Create(new ServicesAddress());
        
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile<SupplySettingProfiles>();
        });
        mapper = mapperConfig.CreateMapper();
        return new SupplySettingService(mapper,dbContext,httpClientFactory,options,baseHotelService);
    }

    [Fact(DisplayName = "新增供货配置成功")]
    public async Task Add_SupplySetting_Success()
    {
        #region arrange

        var dbContext = GetNewDbContext(_tenantId);
        var httpClientFactory = GetHttpClientFactoryMock(new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.OK,
            Content = new StringContent("null",Encoding.UTF8,"application/json")
        });
        var options = Options.Create(new ServicesAddress{ Tenant = "http://127.0.0.1/"});
        var optionConfig = Options.Create(new HotelPreBookingConfig());
        var baseHotelService = new BaseHotelService(httpClientFactory,options,optionConfig);
        var service = CreateService(dbContext,
            httpClientFactory: httpClientFactory,
            options: options,
            baseHotelService: baseHotelService);

        #endregion

        #region fake data

        var supplySettingInfo = new SupplySettingInfo
        {
            HotelId = 22,
            IsSupply = true,
            MarkupType = SupplyMarkupType.AddValue, 
            Value = 100
        };
        
        var fakeDataInput = new UpdateSupplySettingInput
        {
            SupplierId = 1,
            SupplySettingInfos = new List<SupplySettingInfo>
            {
                supplySettingInfo
            }
        };

        #endregion
        
        //act
        await service.Update(fakeDataInput);
        
        //assert
        Assert.True(dbContext.SupplySettings.Any(x=>x.HotelId == supplySettingInfo.HotelId));
    }

    [Fact(DisplayName = "更新供货配置成功")]
    public async Task Update_SupplySetting_Success()
    {
        #region arrange

        var dbContext = GetNewDbContext(_tenantId);
        var service = CreateService(dbContext);

        #endregion

        #region fake data

        var dbData = new SupplySetting
        {
            SupplierId = 1,
            HotelId = 22,
            IsSupply = false,
            MarkupType = SupplyMarkupType.AddRate,
            Value = 0.03m
        };
        await dbContext.AddAsync(dbData);
        await dbContext.SaveChangesAsync();
        
        var supplySettingInfo = new SupplySettingInfo
        {
            HotelId = 22,
            IsSupply = true,
            MarkupType = SupplyMarkupType.AddValue, 
            Value = 100
        };
        
        var fakeDataInput = new UpdateSupplySettingInput
        {
            SupplierId = 1,
            SupplySettingInfos = new List<SupplySettingInfo>
            {
                supplySettingInfo
            }
        };

        #endregion
        
        //act
        await service.Update(fakeDataInput);
        var result = await dbContext.SupplySettings.FirstOrDefaultAsync(x => x.HotelId == supplySettingInfo.HotelId);
        
        //assert
        Assert.True(result is not null);
        Assert.True(result.IsSupply==supplySettingInfo.IsSupply);
        Assert.True(result.MarkupType==supplySettingInfo.MarkupType);
        Assert.True(result.Value==supplySettingInfo.Value);
    }
}