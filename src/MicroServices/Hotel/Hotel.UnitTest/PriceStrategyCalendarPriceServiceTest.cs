using AutoMapper;
using Common.GlobalException;
using Common.Jwt;
using Common.ServicesHttpClient;
using Contracts.Common;
using Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Payment.DTOs.Currency;
using Contracts.Common.Payment.Enums;
using DotNetCore.CAP;
using Hotel.Api.ConfigModel;
using Hotel.Api.Infrastructure;
using Hotel.Api.Model;
using Hotel.Api.Services;
using Hotel.Api.Services.Interfaces;
using Hotel.Api.Services.MappingProfiles;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Hotel.UnitTest
{
    public class PriceStrategyCalendarPriceServiceTest : TestBase<CustomDbContext>
    {
        private readonly static long _tenantId = 1;
        public PriceStrategyCalendarPriceService CreateService(
            CustomDbContext dbContext = null,
            IHttpContextAccessor httpContextAccessor = null,
            ICapPublisher capPublisher = null,
            IHttpClientFactory httpClientFactory = null,
            IOptions<ServicesAddress> options = null,
            IMapper mapper = null,
            MediatR.IMediator mediator = null,
            ISupplySettingService supplySettingService = null,
            IBaseHotelService baseHotelService = null,
            ILogger<PriceStrategyCalendarPriceService> logger = null)
        {
            if (options is null)
                options = Options.Create(new ServicesAddress());

            return new PriceStrategyCalendarPriceService(
                dbContext,
                httpContextAccessor,
                capPublisher,
                httpClientFactory,
                options,
                mapper,
                mediator,
                supplySettingService,
                baseHotelService,
                logger);
        }

        #region 修改基础售价

        [Fact(DisplayName = "修改自营酒店基础售价_成功")]
        public async Task UpdateBasisPrice_SelfSupportHotel_Success()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var httpContextAccessor = GetHttpContextAccessor(new CurrentUser()
            {
                userid = 89757,
                nickname = "测试",
                tenant = _tenantId
            });
            var httpClientFactory = GetUpdateBasisHttpClientFactory();

            var capPublisher = GetCapPublisher();
            var mediator = new Mock<MediatR.IMediator>();
            mediator.Setup(s => s.Publish(It.IsAny<MediatR.INotification>(), default))
                .Returns(Task.CompletedTask);

            var options = Options.Create(new ServicesAddress { Payment = "http://127.0.0.1/" });
            var optioinConfig = Options.Create(new HotelPreBookingConfig());
            var baseHotelService = new BaseHotelService(httpClientFactory, options,optioinConfig);

            var service = CreateService(
                dbContext: dbContext,
                httpContextAccessor: httpContextAccessor,
                capPublisher: capPublisher,
                mediator: mediator.Object,
                baseHotelService: baseHotelService);

            #region fake data
            var hotel = new Api.Model.Hotel()
            {
                ZHName = "自营酒店",
                OperatingModel = OperatingModel.SelfSupport,
                CountryCode = 0,
                CountryName = "",
                ProvinceCode = 0,
                ProvinceName = "",
                CityCode = 0,
                CityName = "",
                Address = ""
            };
            hotel.SetLocation(0, 0);
            await dbContext.Hotels.AddAsync(hotel);
            var priceStrategy = new PriceStrategy()
            {
                HotelId = hotel.Id,
                Name = "测试报价策略",
                SaleCurrencyCode = Currency.CNY.ToString()
            };
            await dbContext.PriceStrategies.AddAsync(priceStrategy);
            var priceStrategyCalendarPrices = new List<PriceStrategyCalendarPrice>()
            {
                new PriceStrategyCalendarPrice(){ HotelId = hotel.Id, PriceStrategyId = priceStrategy.Id, Date = new DateTime(2022, 03, 01), SalePrice = 120 },
                new PriceStrategyCalendarPrice(){ HotelId = hotel.Id, PriceStrategyId = priceStrategy.Id, Date = new DateTime(2022, 03, 02), SalePrice = 120 },
                new PriceStrategyCalendarPrice(){ HotelId = hotel.Id, PriceStrategyId = priceStrategy.Id, Date = new DateTime(2022, 03, 03), SalePrice = 120 }
            };
            await dbContext.PriceStrategyCalendarPrices.AddRangeAsync(priceStrategyCalendarPrices);

            await dbContext.SaveChangesAsync();
            #endregion

            await service.UpdateBasisPrice(new UpdateBasisPriceInput()
            {
                PriceStrategyId = priceStrategy.Id,
                BeginDate = new DateTime(2022, 03, 01),
                EndDate = new DateTime(2022, 03, 07),
                ApplicableWeek = new List<DayOfWeek>()
                {
                    DayOfWeek.Monday,
                    DayOfWeek.Wednesday,
                    DayOfWeek.Friday
                },
                SalePriceUpdateType = SalePriceUpdateType.FixedPrice,
                SalePrice = 100
            });

            var result = await dbContext.PriceStrategyCalendarPrices.AsNoTracking()
                .Where(x => x.PriceStrategyId == priceStrategy.Id)
                .ToListAsync();
            Assert.True(result.Count == 5);
        }

        [Fact(DisplayName = "修改代销酒店基础售价_失败无采购价")]
        public async Task UpdateBasisPrice_AgencyHotel_FailNotCostPrice()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var httpContextAccessor = GetHttpContextAccessor(new CurrentUser()
            {
                userid = 89757,
                nickname = "测试",
                tenant = _tenantId
            });
            var httpClientFactory = GetUpdateBasisHttpClientFactory();
            var options = Options.Create(new ServicesAddress { Payment = "http://127.0.0.1/" });
            var optionConfig = Options.Create(new HotelPreBookingConfig());
            var baseHotelService = new BaseHotelService(httpClientFactory, options,optionConfig);

            var capPublisher = GetCapPublisher();

            var service = CreateService(
                dbContext: dbContext,
                httpContextAccessor: httpContextAccessor,
                capPublisher: capPublisher,
                baseHotelService: baseHotelService);

            #region fake data
            var hotel = new Api.Model.Hotel()
            {
                ZHName = "代销酒店",
                OperatingModel = OperatingModel.Agency,
                CountryCode = 0,
                CountryName = "",
                ProvinceCode = 0,
                ProvinceName = "",
                CityCode = 0,
                CityName = "",
                Address = ""
            };
            hotel.SetLocation(0, 0);
            await dbContext.Hotels.AddAsync(hotel);
            var priceStrategy = new PriceStrategy()
            {
                HotelId = hotel.Id,
                Name = "测试报价策略",
                SupplierId = 1,
                CostCurrencyCode = Currency.USD.ToString(),
                SaleCurrencyCode = Currency.CNY.ToString()
            };
            await dbContext.PriceStrategies.AddAsync(priceStrategy);
            var priceStrategyCalendarPrices = new List<PriceStrategyCalendarPrice>()
            {
                new PriceStrategyCalendarPrice(){ HotelId = hotel.Id, PriceStrategyId = priceStrategy.Id, Date = new DateTime(2022, 03, 01), SalePrice = 120 },
                new PriceStrategyCalendarPrice(){ HotelId = hotel.Id, PriceStrategyId = priceStrategy.Id, Date = new DateTime(2022, 03, 02), SalePrice = 120 },
                new PriceStrategyCalendarPrice(){ HotelId = hotel.Id, PriceStrategyId = priceStrategy.Id, Date = new DateTime(2022, 03, 03), SalePrice = 120 }
            };
            await dbContext.PriceStrategyCalendarPrices.AddRangeAsync(priceStrategyCalendarPrices);

            await dbContext.SaveChangesAsync();
            #endregion

            try
            {
                await service.UpdateBasisPrice(new UpdateBasisPriceInput()
                {
                    PriceStrategyId = priceStrategy.Id,
                    BeginDate = new DateTime(2022, 03, 01),
                    EndDate = new DateTime(2022, 03, 07),
                    ApplicableWeek = new List<DayOfWeek>()
                    {
                        DayOfWeek.Monday,
                        DayOfWeek.Wednesday,
                        DayOfWeek.Friday
                    },
                    SalePriceUpdateType = SalePriceUpdateType.FixedPrice,
                    SalePrice = 100
                });
            }
            catch (BusinessException ex)
            {
                Assert.Equal(ErrorTypes.Hotel.ExistCalendarPriceWithoutCostPrice.ToString(), ex.BusinessErrorType);
            }

        }

        [Fact(DisplayName = "修改代销酒店基础售价_只修改采购价_成功")]
        public async Task UpdateBasisPrice_UpdateCostPrice_Success()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var httpContextAccessor = GetHttpContextAccessor(new CurrentUser()
            {
                userid = 89757,
                nickname = "测试",
                tenant = _tenantId
            });
            var mediator = new Mock<MediatR.IMediator>();
            mediator.Setup(s => s.Publish(It.IsAny<MediatR.INotification>(), default))
                .Returns(Task.CompletedTask);
            var capPublisher = GetCapPublisher();
            var mapperConfig = new MapperConfiguration(mc =>
            {
                mc.AddProfile<SupplySettingProfiles>();
            });
            var supplyMapper = mapperConfig.CreateMapper();
            var options = Options.Create(new ServicesAddress
            {
                Tenant = "http://127.0.0.1/",
                Payment = "http://127.0.0.1/"
            });
            var optionConfig = Options.Create(new HotelPreBookingConfig());
            var httpClientFactory = GetUpdateBasisHttpClientFactory();
            var baseHotelService = new BaseHotelService(httpClientFactory, options,optionConfig);
            var supplySettingService = new SupplySettingService(supplyMapper, dbContext, new Mock<IHttpClientFactory>().Object, options, baseHotelService);

            var service = CreateService(
                dbContext: dbContext,
                httpContextAccessor: httpContextAccessor,
                capPublisher: capPublisher,
                mediator: mediator.Object,
                supplySettingService: supplySettingService,
                baseHotelService: baseHotelService);

            #region fake data
            var hotel = new Api.Model.Hotel()
            {
                ZHName = "代销酒店",
                OperatingModel = OperatingModel.Agency,
                CountryCode = 0,
                CountryName = "",
                ProvinceCode = 0,
                ProvinceName = "",
                CityCode = 0,
                CityName = "",
                Address = ""
            };
            hotel.SetLocation(0, 0);
            await dbContext.Hotels.AddAsync(hotel);
            var priceStrategy = new PriceStrategy()
            {
                HotelId = hotel.Id,
                Name = "测试报价策略",
                SupplierId = 112,
                CostCurrencyCode = Currency.USD.ToString(),
                SaleCurrencyCode = Currency.CNY.ToString()
            };
            await dbContext.PriceStrategies.AddAsync(priceStrategy);
            var priceStrategyCalendarPrices = new List<PriceStrategyCalendarPrice>()
            {
                new PriceStrategyCalendarPrice(){ HotelId = hotel.Id, PriceStrategyId = priceStrategy.Id, Date = new DateTime(2022, 03, 01), CostPrice = 80, SalePrice = 100 },
                new PriceStrategyCalendarPrice(){ HotelId = hotel.Id, PriceStrategyId = priceStrategy.Id, Date = new DateTime(2022, 03, 02), CostPrice = 80, SalePrice = 100 },
                new PriceStrategyCalendarPrice(){ HotelId = hotel.Id, PriceStrategyId = priceStrategy.Id, Date = new DateTime(2022, 03, 04), CostPrice = 90, SalePrice = 110 },
                new PriceStrategyCalendarPrice(){ HotelId = hotel.Id, PriceStrategyId = priceStrategy.Id, Date = new DateTime(2022, 03, 07), CostPrice = 100, SalePrice = 120 }
            };
            await dbContext.PriceStrategyCalendarPrices.AddRangeAsync(priceStrategyCalendarPrices);

            var supplySetting = new SupplySetting
            {
                SupplierId = priceStrategy.SupplierId,
                HotelId = priceStrategy.HotelId,
                IsSupply = true,
                MarkupType = SupplyMarkupType.AddRate,
                Value = 2
            };
            await dbContext.AddAsync(supplySetting);
            await dbContext.SaveChangesAsync();

            var inputData = new UpdateBasisPriceInput()
            {
                PriceStrategyId = priceStrategy.Id,
                BeginDate = new DateTime(2022, 03, 01),
                EndDate = new DateTime(2022, 03, 07),
                ApplicableWeek = new List<DayOfWeek>() { DayOfWeek.Monday, DayOfWeek.Wednesday, DayOfWeek.Friday },
                CostPriceUpdateType = CostPriceUpdateType.FixedPrice,
                CostPrice = 100,
                HasOperationRight = false
            };
            #endregion

            await service.UpdateBasisPrice(inputData);

            var result = await dbContext.PriceStrategyCalendarPrices.AsNoTracking()
                .Where(x => x.PriceStrategyId == priceStrategy.Id && x.CostPrice == 100)
                .ToListAsync();
            Assert.True(result.Count == 3);
            if (!inputData.HasOperationRight.Value)
                Assert.Contains(result, x => x.SalePrice == 704.33m);
        }

        [Fact(DisplayName = "修改代销酒店基础售价_只修改售价_成功")]
        public async Task UpdateBasisPrice_UpdateSalePrice_Success()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var httpContextAccessor = GetHttpContextAccessor(new CurrentUser()
            {
                userid = 89757,
                nickname = "测试",
                tenant = _tenantId
            });
            var mediator = new Mock<MediatR.IMediator>();
            mediator.Setup(s => s.Publish(It.IsAny<MediatR.INotification>(), default))
                .Returns(Task.CompletedTask);
            var capPublisher = GetCapPublisher();

            var httpClientFactory = GetUpdateBasisHttpClientFactory();
            var options = Options.Create(new ServicesAddress { Payment = "http://127.0.0.1/" });
            var optionConfig = Options.Create(new HotelPreBookingConfig());
            var baseHotelService = new BaseHotelService(httpClientFactory, options,optionConfig);

            var service = CreateService(
                dbContext: dbContext,
                httpContextAccessor: httpContextAccessor,
                capPublisher: capPublisher,
                mediator: mediator.Object,
                baseHotelService: baseHotelService);

            #region fake data
            var hotel = new Api.Model.Hotel()
            {
                ZHName = "代销酒店",
                OperatingModel = OperatingModel.Agency,
                CountryCode = 0,
                CountryName = "",
                ProvinceCode = 0,
                ProvinceName = "",
                CityCode = 0,
                CityName = "",
                Address = ""
            };
            hotel.SetLocation(0, 0);
            await dbContext.Hotels.AddAsync(hotel);
            var priceStrategy = new PriceStrategy()
            {
                HotelId = hotel.Id,
                Name = "测试报价策略",
                SupplierId = 1,
                CostCurrencyCode = Currency.USD.ToString(),
                SaleCurrencyCode = Currency.CNY.ToString()
            };
            await dbContext.PriceStrategies.AddAsync(priceStrategy);
            var priceStrategyCalendarPrices = new List<PriceStrategyCalendarPrice>()
            {
                new PriceStrategyCalendarPrice(){ HotelId = hotel.Id, PriceStrategyId = priceStrategy.Id, Date = new DateTime(2022, 03, 01), CostPrice = 80, SalePrice = 100 },
                new PriceStrategyCalendarPrice(){ HotelId = hotel.Id, PriceStrategyId = priceStrategy.Id, Date = new DateTime(2022, 03, 02), CostPrice = 80, SalePrice = 100 },
                new PriceStrategyCalendarPrice(){ HotelId = hotel.Id, PriceStrategyId = priceStrategy.Id, Date = new DateTime(2022, 03, 04), CostPrice = 90, SalePrice = 110 },
                new PriceStrategyCalendarPrice(){ HotelId = hotel.Id, PriceStrategyId = priceStrategy.Id, Date = new DateTime(2022, 03, 07), CostPrice = 100, SalePrice = 120 }
            };
            await dbContext.PriceStrategyCalendarPrices.AddRangeAsync(priceStrategyCalendarPrices);

            await dbContext.SaveChangesAsync();
            #endregion

            await service.UpdateBasisPrice(new UpdateBasisPriceInput()
            {
                PriceStrategyId = priceStrategy.Id,
                BeginDate = new DateTime(2022, 03, 01),
                EndDate = new DateTime(2022, 03, 07),
                ApplicableWeek = new List<DayOfWeek>()
                {
                    DayOfWeek.Monday,
                    DayOfWeek.Wednesday,
                    DayOfWeek.Friday
                },
                SalePriceUpdateType = SalePriceUpdateType.AutoPrice,
                MarkupType = MarkupType.AddValue,
                MarkupValue = 10
            });

            var result = await dbContext.PriceStrategyCalendarPrices.AsNoTracking()
                .Where(x => x.PriceStrategyId == priceStrategy.Id && x.SalePrice == 700.52m)
                .ToListAsync();
            Assert.True(result.Count == 1);
        }

        [Fact(DisplayName = "修改代销酒店基础售价_修改采购价和售价_成功")]
        public async Task UpdateBasisPrice_UpdateCostPriceAndSalePrice_Success()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var httpContextAccessor = GetHttpContextAccessor(new CurrentUser()
            {
                userid = 89757,
                nickname = "测试",
                tenant = _tenantId
            });
            var mediator = new Mock<MediatR.IMediator>();
            mediator.Setup(s => s.Publish(It.IsAny<MediatR.INotification>(), default))
                .Returns(Task.CompletedTask);
            var capPublisher = GetCapPublisher();

            var httpClientFactory = GetUpdateBasisHttpClientFactory();
            var options = Options.Create(new ServicesAddress { Payment = "http://127.0.0.1/" });
            var optionConfig = Options.Create(new HotelPreBookingConfig());
            var baseHotelService = new BaseHotelService(httpClientFactory, options,optionConfig);

            var service = CreateService(
                dbContext: dbContext,
                httpContextAccessor: httpContextAccessor,
                capPublisher: capPublisher,
                mediator: mediator.Object,
                baseHotelService: baseHotelService);

            #region fake data
            var hotel = new Api.Model.Hotel()
            {
                ZHName = "代销酒店",
                OperatingModel = OperatingModel.Agency,
                CountryCode = 0,
                CountryName = "",
                ProvinceCode = 0,
                ProvinceName = "",
                CityCode = 0,
                CityName = "",
                Address = ""
            };
            hotel.SetLocation(0, 0);
            await dbContext.Hotels.AddAsync(hotel);
            var priceStrategy = new PriceStrategy()
            {
                HotelId = hotel.Id,
                Name = "测试报价策略",
                SupplierId = 12,
                CostCurrencyCode = Currency.USD.ToString(),
                SaleCurrencyCode = Currency.CNY.ToString()
            };
            await dbContext.PriceStrategies.AddAsync(priceStrategy);
            var priceStrategyCalendarPrices = new List<PriceStrategyCalendarPrice>()
            {
                new PriceStrategyCalendarPrice(){ HotelId = hotel.Id, PriceStrategyId = priceStrategy.Id, Date = new DateTime(2022, 03, 01), CostPrice = 80, SalePrice = 100 },
                new PriceStrategyCalendarPrice(){ HotelId = hotel.Id, PriceStrategyId = priceStrategy.Id, Date = new DateTime(2022, 03, 02), CostPrice = 80, SalePrice = 100 },
                new PriceStrategyCalendarPrice(){ HotelId = hotel.Id, PriceStrategyId = priceStrategy.Id, Date = new DateTime(2022, 03, 04), CostPrice = 90, SalePrice = 110 }
            };
            await dbContext.PriceStrategyCalendarPrices.AddRangeAsync(priceStrategyCalendarPrices);

            await dbContext.SaveChangesAsync();
            #endregion

            await service.UpdateBasisPrice(new UpdateBasisPriceInput()
            {
                PriceStrategyId = priceStrategy.Id,
                BeginDate = new DateTime(2022, 03, 01),
                EndDate = new DateTime(2022, 03, 07),
                ApplicableWeek = new List<DayOfWeek>()
                {
                    DayOfWeek.Monday,
                    DayOfWeek.Wednesday,
                    DayOfWeek.Friday
                },
                CostPriceUpdateType = CostPriceUpdateType.FixedPrice,
                CostPrice = 100,
                SalePriceUpdateType = SalePriceUpdateType.AutoPrice,
                MarkupType = MarkupType.AddValue,
                MarkupValue = 10
            });

            var result = await dbContext.PriceStrategyCalendarPrices.AsNoTracking()
                .Where(x => x.PriceStrategyId == priceStrategy.Id && x.SalePrice == 700.52m)
                .ToListAsync();
            Assert.True(result.Count == 3);
        }

        #endregion

        private IHttpClientFactory GetUpdateBasisHttpClientFactory()
        {
            var httpClientFactory = GetHttpClientFactoryMock(
                new HttpResponseMessage
                {
                    Content = new StringContent(JsonConvert.SerializeObject(new List<GetExchangeRateOutput>
                    {
                        new GetExchangeRateOutput
                        {
                            BaseCurrencyCode = Currency.USD.ToString(),
                            TargetCurrencyCode = Currency.CNY.ToString(),
                            ExchangeRate = 6.905219m
                        },
                        new GetExchangeRateOutput
                        {
                            BaseCurrencyCode = Currency.CNY.ToString(),
                            TargetCurrencyCode = Currency.USD.ToString(),
                            ExchangeRate = 0.144818m
                        }
                    }))
                });

            return httpClientFactory;
        }
    }
}
