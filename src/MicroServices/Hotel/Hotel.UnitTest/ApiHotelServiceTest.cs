using AutoMapper;
using Cit.Storage.Redis;
using Common.ServicesHttpClient;
using Contracts.Common.Hotel.DTOs.ApiHotel;
using Contracts.Common.Hotel.Enums;
using DotNetCore.CAP;
using Hangfire;
using Hotel.Api.ConfigModel;
using Hotel.Api.Infrastructure;
using Hotel.Api.Services;
using Hotel.Api.Services.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Nest;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Hotel.UnitTest;
public class ApiHotelServiceTest : TestBase<CustomDbContext>
{
    public ApiHotelService CreateService(CustomDbContext dbContext = null,
        IMapper mapper = null,
        IHttpClientFactory httpClientFactory = null,
        IOptions<ServicesAddress> options = null,
        IBackgroundJobClient backgroundJobClient = null,
        IHttpContextAccessor httpContextAccessor = null,
        ICapPublisher capPublisher = null,
        IElasticClient elasticClient = null,
        IBaseHotelService baseHotelService = null,
        IRedisClient redisClient = null,
        IOptionsMonitor<HuiZhiHotelConfig> huiZhiHotelConfig = null)
    {
        options ??= Options.Create(new ServicesAddress());

        return new ApiHotelService(
            dbContext,
            mapper,
            httpClientFactory,
            options,
            backgroundJobClient,
            httpContextAccessor,
            capPublisher,
            elasticClient,
            baseHotelService,
            redisClient,
            huiZhiHotelConfig
        );
    }

    [Fact(DisplayName = "根据资源酒店Id检查酒店是否已经添加")]
    public async Task CheckRefByResourceHotelIds()
    {
        //arrange
        var tenantId = 1;
        var dbContext = GetNewDbContext(tenantId);
        var service = CreateService(dbContext: dbContext);

        //fake data
        var hotels = new List<Api.Model.ApiHotel>()
        {
            new (){ },
            new (){ }
        };
        hotels.ForEach(x =>
        {
            x.ZHName = default;
            x.StarLevel = default;
            x.CountryName = default;
            x.CountryCode = default;
            x.CityName = default;
            x.CityCode = default;
        });
        await dbContext.AddRangeAsync(hotels);
        await dbContext.SaveChangesAsync();

        //act
        var hotelIds = hotels.Select(x => x.Id).ToList();
        var result = await service.CheckRefByResourceHotelIds(new CheckRefInput() { HotelIds = hotelIds });

        //assert
        Assert.True(result.Count(x => x.RefStatus.Equals(ApiHotelRefStatus.CanAdd)) == 2);
    }
}
