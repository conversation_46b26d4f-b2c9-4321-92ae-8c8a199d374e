using Common.ServicesHttpClient;
using Contracts.Common.Hotel.Messages;
using Contracts.Common.Order.Enums;
using Hotel.Api.ConfigModel;
using Hotel.Api.Infrastructure;
using Hotel.Api.Model;
using Hotel.Api.Services;
using Hotel.Api.Services.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Moq;
using Moq.Protected;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Hotel.UnitTest;

public class OtaSettingServiceTest : TestBase<CustomDbContext>
{
    private OtaSettingService CreateService(
        IHttpClientFactory httpClientFactory = null,
        IOptions<ServicesAddress> options = null,
        CustomDbContext dbContext = null,
        IHttpContextAccessor httpContextAccessor = null,
        IBaseHotelService baseHotelService = null)
    {
        if (options is null)
            options = Options.Create(new ServicesAddress());

        return new OtaSettingService(
            httpClientFactory, options,
            httpContextAccessor, dbContext,baseHotelService);
    }

    [Fact(DisplayName = "获取OTA代运营配置")]
    public async Task Get()
    {
        var tenantId = 1;
        var dbContext =  GetNewDbContext(tenantId);
        var httpClientFactory = GetHttpClientFactory(JsonConvert.SerializeObject(new { AgencyOperation = 1 }));
        var optioin = Options.Create(new ServicesAddress() { Tenant = "http://127.0.0.1/" });
        var optionConfig = Options.Create(new HotelPreBookingConfig());

        var otaSetting = new OtaSetting()
        {
            HotelId = 1,
            Enabled = true
        };
        await dbContext.AddAsync(otaSetting);
        await dbContext.SaveChangesAsync();

        var baseHotelService = new BaseHotelService(httpClientFactory,optioin,optionConfig);
        
        var service = CreateService(
            dbContext: dbContext,
            httpClientFactory: httpClientFactory,
            options: optioin,
            baseHotelService: baseHotelService);

        var result = await service.Get(1);

        Assert.True(result.Enabled);
    }

    #region [订阅]修改商户Ota代运营设置

    [Fact(DisplayName = "[订阅]修改商户Ota代运营设置_新增_成功")]
    public async Task SetTenantOtaSetting_Add_Success()
    {
        var tenantId = 1;
        //arrange
        var dbContext = GetNewDbContext();
        var service = CreateService(
            dbContext: dbContext);

        var hotels = new List<Api.Model.Hotel>
        {
            new()
            {
                ZHName = "1",
                CountryName = "1",
                ProvinceName = "1",
                CityName = "1",
                Address = "1"
            },
            new()
            {
                ZHName = "1",
                CountryName = "1",
                ProvinceName = "1",
                CityName = "1",
                Address = "1"
            }
        };

        foreach (var hotel in hotels) hotel.SetLocation(0, 0);

        await dbContext.AddRangeAsync(hotels);
        await dbContext.SetTenantId(tenantId).SaveChangesAsync();

        //act
        await service.SetTenantOtaSettingSubscribe(new SetTenantOtaSettingMessage
        {
            Enabled = true,
            TenantId = tenantId
        });

        //assert
        var oatSettingCount = dbContext.OtaSettings.IgnoreQueryFilters().AsNoTracking()
            .Count(s => s.TenantId == tenantId
                        && s.Enabled
                        && hotels.Select(d => d.Id).Contains(s.HotelId));
        Assert.True(oatSettingCount == hotels.Count);
    }

    [Fact(DisplayName = "[订阅]修改商户Ota代运营设置_修改_关闭_成功")]
    public async Task SetTenantOtaSetting_Update_Close_Success()
    {
        //arrange
        var tenantId = 1;
        var dbContext =  GetNewDbContext();
        var service = CreateService(dbContext: dbContext);

        #region fake data
            
        //hotels
        var hotels = new List<Api.Model.Hotel>
        {
            new()
            {
                ZHName = "1",
                CountryName = "1",
                ProvinceName = "1",
                CityName = "1",
                Address = "1"
            },
            new()
            {
                ZHName = "1",
                CountryName = "1",
                ProvinceName = "1",
                CityName = "1",
                Address = "1"
            }
        };

        foreach (var hotel in hotels) hotel.SetLocation(0, 0);

        await dbContext.SetTenantId(tenantId).AddRangeAsync(hotels);

        //otaSettings
        var otaSettings = hotels.Select(s =>
        {
            var item = new OtaSetting
            {
                HotelId = s.Id,
                Enabled = true
            };
            return item;
        });
        await dbContext.AddRangeAsync(otaSettings);

        //channels
        var channels = new List<ChannelMarkup>();
        foreach (var hotel in hotels)
        {
            var wechatMall = new ChannelMarkup
            {
                ChannelType = SellingChannels.WechatMall,
                Enabled = true,
                HotelId = hotel.Id
            };
            channels.Add(wechatMall);

            var ota = new ChannelMarkup
            {
                ChannelType = SellingChannels.OTA,
                Enabled = true,
                HotelId = hotel.Id
            };
            channels.Add(ota);
        }

        await dbContext.AddRangeAsync(channels);
        await dbContext.SaveChangesAsync();

        #endregion

        //act
        await service.SetTenantOtaSettingSubscribe(new SetTenantOtaSettingMessage
        {
            Enabled = false,
            TenantId = tenantId
        });

        //assert
        //普通渠道不会被关闭
        var generalChannelCount = await dbContext.ChannelMarkups.IgnoreQueryFilters().AsNoTracking()
            .CountAsync(s => s.TenantId == tenantId
                             && s.Enabled
                             && s.ChannelType == SellingChannels.WechatMall);
        Assert.True(generalChannelCount == hotels.Count);

        //OTA渠道已全部关闭
        var otaChannels = SellingChannels.OTA;

        var otaChannelHasOpenning = await dbContext.ChannelMarkups.IgnoreQueryFilters().AsNoTracking()
            .AnyAsync(s => s.TenantId == tenantId
                           && s.Enabled
                           && (otaChannels & s.ChannelType) == s.ChannelType);

        Assert.True(otaChannelHasOpenning == false);
    }

    #endregion

    /// <summary>
    /// Mock IHttpClientFactory
    /// </summary>
    public static IHttpClientFactory GetHttpClientFactory(string response)
    {
        var mockHttpMessageHandler = new Mock<HttpMessageHandler>();
        mockHttpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<System.Threading.CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage()
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                Content = new StringContent(response, System.Text.Encoding.UTF8)
            });
        var client = new HttpClient(mockHttpMessageHandler.Object);

        var mock = new Mock<IHttpClientFactory>();
        mock.Setup(s => s.CreateClient(MicroserviceHttpClientExtensions.MICRO_SERVICE)).Returns(client);

        return mock.Object;
    }
}