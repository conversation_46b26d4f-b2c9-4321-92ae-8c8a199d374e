using Contracts.Common.Resource.Enums;

namespace Notify.Api.Services.MessageCenter;

public static class HotelOrderValueHelper
{
    public static HotelOrderBoard GetHotelOrderBoard(BoardCodeType? boardCodeType, int boardCount)
    {
        return boardCodeType switch
        {
            BoardCodeType.BB => new HotelOrderBoard($"早餐 * {boardCount}", $"Breakfast Only * {boardCount}"),
            BoardCodeType.HB => new HotelOrderBoard($"半餐（早餐+晚餐） * {boardCount}", $"Half Board (Breakfast + Dinner) * {boardCount}"),
            BoardCodeType.FB => new HotelOrderBoard($"全餐（早餐+午餐+晚餐） * {boardCount}", $"Full Board (Breakfast + Lunch + Dinner) * {boardCount}"),
            BoardCodeType.BL => new HotelOrderBoard($"午餐 * {boardCount}", $"Lunch Only * {boardCount}"),
            BoardCodeType.BD => new HotelOrderBoard($"晚餐 * {boardCount}", $"Dinner Only * {boardCount}"),
            BoardCodeType.HBBL => new HotelOrderBoard($"早餐+午餐 * {boardCount}", $"Breakfast + Lunch * {boardCount}"),
            BoardCodeType.HBLD => new HotelOrderBoard($"午餐+晚餐 * {boardCount}", $"Lunch + Dinner * {boardCount}"),
            BoardCodeType.AI => new HotelOrderBoard($"全包 * {boardCount}", $"All Inclusive * {boardCount}"),
            _ => new HotelOrderBoard($"不含早", $"No Breakfast"),
        };
    }
}


public record HotelOrderBoard(string ZhName, string EnName);
