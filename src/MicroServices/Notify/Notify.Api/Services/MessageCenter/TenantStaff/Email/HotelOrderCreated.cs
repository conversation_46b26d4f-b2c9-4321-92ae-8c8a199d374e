using Contracts.Common.Notify.Enums;
using Contracts.Common.Resource.Enums;
using System.Text.Json;

namespace Notify.Api.Services.MessageCenter.TenantStaff.Email;

//日历酒店 - 下单成功
[MessageModelMatching(SendToTheRole.TenantStaff, NotifyEventSubType.Hotel_CreateOrder, NotifyMode.Email)]
public class HotelOrderCreated : EmailMessageModelBase
{
    #region Values

    private struct HotelOrderCreatedValues
    {

        public string Addressee { get; set; }
        public long OrderId { get; set; }
        public string HotelName { get; set; }
        public string RoomName { get; set; }
        public int NumberOfBreakfast { get; set; }
        public DateTime CheckInDate { get; set; }
        public DateTime CheckOutDate { get; set; }
        public int Quantity { get; set; }
        public string[] RoomGuests { get; set; }
        public string OrderContact { get; set; }
        public string ChannelOrderNo { get; set; }
        public string Remark { get; set; }
        public decimal PaymentAmount { get; set; }
        /// <summary>
        /// 订单支付币种
        /// </summary>
        public string PaymentCurrencyCode { get; set; }

        /// <summary>
        /// 餐食类型
        /// </summary>
        public BoardCodeType? BoardCodeType { get; set; }

        /// <summary>
        /// 餐食数量
        /// </summary>
        public int BoardCount { get; set; }
    }

    #endregion

    public HotelOrderCreated(
        JsonElement variables,
        IParamsSupportService paramsSupportService,
        long tenantId) : base(tenantId)
    {
        var item = JsonSerializer.Deserialize<HotelOrderCreatedValues>(variables, JsonSerializerHelper.serializerOptions);
        Addressee = item.Addressee;
        OrderId = item.OrderId.ToString();
        HotelName = item.HotelName;
        RoomName = item.RoomName;
        NumberOfBreakfast = item.NumberOfBreakfast;
        Nights = (item.CheckOutDate - item.CheckInDate).Days.ToString();
        //TODO: docker镜像种未安装zh-CN语言包，此处输出的格式为英文日期格式 2022.11.18 Firday
        CheckInDate = item.CheckInDate.ToString("yyyy.MM.dd dddd");//, CultureInfo.GetCultureInfo("zh-CN"));
        CheckOutDate = item.CheckOutDate.ToString("yyyy.MM.dd dddd");//, CultureInfo.GetCultureInfo("zh-CN"));
        Quantity = item.Quantity.ToString();
        RoomGuests = string.Join(',', item.RoomGuests);
        OrderContact = item.OrderContact;
        ChannelOrderNo = item.ChannelOrderNo;
        Remark = item.Remark;


        // PaymentAmount = $"{item.PaymentAmount.ToString("0.00")}{item.PaymentCurrencyCode}";
        PaymentAmount = item.PaymentAmount.ToString("0.00");
        // ￥ 500 CNY
        PaymentAmount = $"{GetCurrencySymbol(item.PaymentCurrencyCode)} {PaymentAmount} {item.PaymentCurrencyCode}";

        var sysTenantConfig = paramsSupportService.GetTenantSysConfig(tenantId)
                                                  .GetAwaiter()
                                                  .GetResult();
        var sysSetting = paramsSupportService.GetSysSetting();
        OrderUrl = $"http://{sysTenantConfig.Subdomain}{sysSetting.Vebk}#/order/orderList?baseOrderId={OrderId}";

        var board = HotelOrderValueHelper.GetHotelOrderBoard(item.BoardCodeType, item.BoardCount);
        BreakfastOptions = board.ZhName;
        BreakfastOptionsEn = board.EnName;
    }

    /// <summary>
    /// 中文含餐信息
    /// </summary>
    public string BreakfastOptions { get; set; }

    /// <summary>
    /// 英文含餐信息
    /// </summary>
    public string BreakfastOptionsEn { get; set; }

    public override string Addressee { get; protected set; }

    public string OrderUrl { get; private set; }

    public string OrderId { get; private set; }

    public string HotelName { get; private set; }

    public string RoomName { get; private set; }

    public int NumberOfBreakfast { get; private set; }

    public string CheckInDate { get; private set; }

    public string CheckOutDate { get; private set; }

    public string Nights { get; private set; }

    public string Quantity { get; private set; }

    public string RoomGuests { get; private set; }

    public string OrderContact { get; private set; }

    public string ChannelOrderNo { get; private set; }

    public string Remark { get; private set; }

    public string PaymentAmount { get; private set; }
}
