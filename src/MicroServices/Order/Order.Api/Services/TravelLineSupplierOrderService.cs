using AutoMapper;
using Cit.Storage.Aliyun.Oss;
using Common.GlobalException;
using Common.ServicesHttpClient;
using Common.Utils;
using Contracts.Common.Notify.Enums;
using Contracts.Common.Notify.Messages;
using Contracts.Common.Order.DTOs.OpenSupplierOrder;
using Contracts.Common.Order.DTOs.OrderFieldInformation;
using Contracts.Common.Order.DTOs.ScenicTicketSupplierOrder;
using Contracts.Common.Order.DTOs.TravelLineOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Product.DTOs.Fields.Group;
using Contracts.Common.Product.Enums;
using Contracts.Common.Product.Messages;
using Contracts.Common.Scenic.Enums;
using Contracts.Common.Tenant.DTOs.Supplier;
using DotNetCore.CAP;
using EfCoreExtensions.UOW;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.International.Converters.PinYinConverter;
using Newtonsoft.Json;
using Order.Api.ConfigModel;
using Order.Api.Extensions;
using Order.Api.Services.Interfaces;
using Order.Api.Services.OpenPlatform.Contracts.Supplier;
using Order.Api.Services.OpenPlatform.Interfaces;
using System.Globalization;

namespace Order.Api.Services;

/// <summary>
/// 开放平台-线路供应端下单服务
/// </summary>
public class TravelLineSupplierOrderService : ITravelLineSupplierOrderService
{
    private readonly CustomDbContext _dbContext;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ServicesAddress _servicesAddress;
    private readonly OssConfig _ossConfig;
    private readonly IAliyunOssObject _aliyunOssObject;
    private readonly ILogger<TravelLineSupplierOrderService> _logger;
    private readonly IOpenSupplierService _openSupplierService;
    private readonly ICapPublisher _capPublisher;
    private readonly IMapper _mapper;
    private readonly IOpenPlatformBaseService _openPlatformBaseService;
    private readonly ITravelLineVoucherService _travelLineVoucherService;
    private readonly ITravelLineOrderMessageService _travelLineOrderMessageService;
    
    private const int _successCode = 200;

    public TravelLineSupplierOrderService(
        CustomDbContext dbContext,
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> servicesAddressOptions,
        IOptions<OssConfig> ossConfigOptions,
        IAliyunOssObject aliyunOssObject,
        ILogger<TravelLineSupplierOrderService> logger,
        IOpenSupplierService openSupplierService,
        ICapPublisher capPublisher,
        IOpenPlatformBaseService openPlatformBaseService,
        IMapper mapper,
        ITravelLineVoucherService travelLineVoucherService,
        ITravelLineOrderMessageService travelLineOrderMessageService)
    {
        _dbContext = dbContext;
        _httpClientFactory = httpClientFactory;
        _servicesAddress = servicesAddressOptions.Value;
        _ossConfig = ossConfigOptions.Value;
        _aliyunOssObject = aliyunOssObject;
        _logger = logger;
        _openSupplierService = openSupplierService;
        _capPublisher = capPublisher;
        _mapper = mapper;
        _openPlatformBaseService = openPlatformBaseService;
        _travelLineVoucherService = travelLineVoucherService;
        _travelLineOrderMessageService = travelLineOrderMessageService;
    }

    [UnitOfWork]
    public async Task<CreateSupplierOrderOutput> OrderProcess(CreateSupplierApiOrderMessage receive)
    {
        var result = new CreateSupplierOrderOutput();
        var baseOrder = await _dbContext.BaseOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.Id == receive.BaseOrderId);
        var travelLineOrder = await _dbContext.TravelLineOrder.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == receive.BaseOrderId);
        var lineSkuTypeItems = await _dbContext.TravelLineOrderSkuTypeItems.IgnoreQueryFilters()
            .Where(x => x.BaseOrderId == baseOrder.Id)
            .ToListAsync();

        //判断sass订单状态是否是待完成
        if (baseOrder.Status != BaseOrderStatus.UnFinished)
        {
            _logger.LogError("[OpenSupplier]OrderProcess:{@Input},{@Message}", receive, "线路订单状态已完成");
            result.Msg = "订单状态已完成";
            return result;
        }

        //查无下单票种信息
        if (lineSkuTypeItems.Any() is false)
        {
            _logger.LogError("[OpenSupplier]OrderProcess:{@Input},{@Message}", receive, "查无下单票种信息");
            result.Msg = "查无下单票种信息";
            return result;
        }

        //判断是否已经存在供应端订单数据.如果创单失败,允许重新下单
        var isNewSupplierOrder = false;
        var supplierOrder = await _dbContext.TravelLineSupplierOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == receive.BaseOrderId);
        if (supplierOrder != null)
        {
            if (supplierOrder.OrderStatus != LineProductSupplierOrderStatus.CreateFail)
            {
                _logger.LogError("[OpenSupplier]OrderProcess:{@Input},{@Message}",receive,"供应端订单已存在");
                result.Msg = "供应端订单已存在";
                return result;
            }
        }
        else
        {
            //初始化供应端订单
            supplierOrder = new TravelLineSupplierOrder();
            supplierOrder.SetTenantId(baseOrder.TenantId);
            isNewSupplierOrder = true;
        }

        #region 查询供应商信息

        //查询API供应商配置信息
        var tenantId = baseOrder.TenantId;
        var header = new List<KeyValuePair<string, string>> {new("tenant", tenantId.ToString())};
        var supplier = await _httpClientFactory.InternalGetAsync<GetSupplierOutput>(
            requestUri: _servicesAddress.Tenant_GetSupplier(travelLineOrder.SupplierId),
            headers: header);

        #endregion
        
        //开放平台供应商配置信息
        var openSupplierType = travelLineOrder.OpenSupplierType.ToString().ToLowerInvariant();
        var activeId = travelLineOrder.SupplierActivityId ?? string.Empty;
        var initTravelDate = travelLineOrder.TravelBeginDate;
        var timeSlotName = travelLineOrder.TimeSlotName;//时段场次名称
        if (TimeSpan.TryParse(timeSlotName, out var timeSlot))
        {
            initTravelDate = initTravelDate.Add(timeSlot);
        }
        var travelDate = initTravelDate.ToString("yyyy-MM-dd HH:mm:ss");;//出行日期

        //订单价格信息
        var orderPrices = await _dbContext.OrderPrices.IgnoreQueryFilters()
            .Where(x => x.BaseOrderId == baseOrder.Id)
            .ToListAsync();


        #region 订单字段模板信息

        //订单字段模板信息
        var orderFieldTypeInfos = await GetOrderFieldInfo(baseOrder.Id);
        var contactInfo = ContactInfoDataSupplement(orderFieldTypeInfos.ContactsFieldInfo);
        var travelInfo = TravelerInfoDataSupplement(
            travelFieldInfo: orderFieldTypeInfos.TravelFieldInfo);
        var locationInfo = OrderCommonSupplement(orderFieldTypeInfos.TravelFieldInfo);

        #endregion

        #region 数据处理和下单前校验

        var extraInfoCheck = true;
        var supplierProduct = await _openSupplierService.SupplierProductDetail(
            new SupplierProductDetailRequest
            {
                SupplierType = openSupplierType,
                OutProductId = activeId
            }, tenantId);
        if (supplierProduct?.Data is null || supplierProduct.Data?.SkuList.Any() is false)
        {
            _logger.LogError("[OpenSupplier]OrderProcess:{@Input},{@Message}", receive, "供应商产品信息为空");
            result.Msg = "供应商产品信息为空";
            return result;
        }

        // 查询订单的附加信息
        var orderExtraInfos = await _dbContext.OpenSupplierOrderExtraInfos.IgnoreQueryFilters()
            .AsNoTracking()
            .Where(x => x.BaseOrderId == receive.BaseOrderId)
            .ToListAsync();

        var orderItems = new List<SupplierOrderCreateOrderItem>();
        foreach (var item in lineSkuTypeItems)
        {
            var supplierSku = supplierProduct.Data.SkuList.FirstOrDefault(x => x.OutSkuId == item.SkuId);
            var extraInfos = supplierSku.ExtraInfos;
            
            //附加信息处理
            var itemExtraInfos = new List<SupplierOrderCreateOrderExtraItem>();
            if (orderExtraInfos.Any())
            {
                itemExtraInfos = orderExtraInfos.Select(x => new SupplierOrderCreateOrderExtraItem
                {
                    Key = x.DataType.ToString(),
                    Value = x.OptionKey ?? x.OptionValue
                })
                .ToList();
            }
            else
            {
                itemExtraInfos = ExtraInfoCheck(extraInfos,locationInfo);
                if (extraInfos.Any())
                {
                    // 判断是否缺失附加信息
                    foreach (var extraInfoItem in extraInfos.GroupBy(x=>x.Key))
                    {
                        // 此处为字段模板换算校验.非必填不做校验
                        if(extraInfoItem.First().Required == false) continue;
                        
                        var extraInfoValue = itemExtraInfos.FirstOrDefault(x => x.Key == extraInfoItem.Key)?.Value ?? string.Empty;
                        if (string.IsNullOrEmpty(extraInfoValue))
                        {
                            extraInfoCheck = false;
                        }
                    }
                }
            }
            
            //价格数量校验判断
            var itemOrderCount = orderPrices.Where(x => x.OrderSubItemId == item.SkuTypeItemId).Sum(x=>x.Quantity);

            // 订单项数据处理
            orderItems.Add(new SupplierOrderCreateOrderItem
            {
                OutProductId = activeId,
                OutOptionId = item.PackageId,
                OutSkuId = item.SkuId,
                StartTime = travelDate,
                Timeslot = timeSlotName,
                Count = itemOrderCount,
                ExtraInfos = itemExtraInfos
            });
            
            // 出行人信息分配票种数据
            var relatedTravelInfos = travelInfo.Where(x => string.IsNullOrEmpty(x.OutSkuId))
                .Where(x => x.LineSkuPriceType == item.SkuPriceType)
                .ToList();
            foreach (var relatedTravelInfoItem in relatedTravelInfos.Take(itemOrderCount))
            {
                relatedTravelInfoItem.OutSkuId = item.SkuId;
            }
        }

        #endregion

        #region supplierOder 初始化数据

        supplierOrder.BaseOrderId = baseOrder.Id;
        supplierOrder.LineProductOrderId = travelLineOrder.Id;
        supplierOrder.SupplierId = travelLineOrder.SupplierId;
        supplierOrder.Currency = supplier.CurrencyCode;
        supplierOrder.SupplierType = travelLineOrder.OpenSupplierType!.Value;
        supplierOrder.TimeSlotName = travelLineOrder.TimeSlotName;
        supplierOrder.OrderStatus = LineProductSupplierOrderStatus.CreateFail;

        #endregion
        
        //校验通过下单
        var checkPass = (orderItems.Count == lineSkuTypeItems.Count) && extraInfoCheck;
        if (checkPass)
        {
            // 查询订单的`供应商备注`
            var supplierRemark = await _dbContext.BaseOrderRemarks.IgnoreQueryFilters()
                .AsNoTracking()
                .Where(x => x.BaseOrderId == receive.BaseOrderId)
                .Where(x => x.RemarkType == BaseOrderRemarkType.SupplierOrderRemark)
                .OrderByDescending(x => x.Id)
                .FirstOrDefaultAsync();
            
            #region 创建订单
            
            var createRequest = new SupplierOrderCreateOrderRequest
            {
                OrderId = baseOrder.Id.ToString(),
                BookingNotes = supplierRemark?.Remark ?? string.Empty,
                SupplierType = openSupplierType,
                ContactInfo = contactInfo,
                TravelerInfos = travelInfo,
                Items = orderItems
            };
            
            //创建订单
            var createResponse = await _openSupplierService.CreateOrder(createRequest,tenantId);
            var createIsSuccess = createResponse.Code == _successCode;

            //关联订单状态更新
            supplierOrder.SupplierOrderId = createResponse.Data?.OutOrderId; //采购单号更新
            supplierOrder.CommissionRate = createResponse.Data?.CommissionRate ?? 0m; //产品折扣（佣金）更新
            supplierOrder.OrderStatus = createIsSuccess switch
            {
                true => LineProductSupplierOrderStatus.WaitingForPay,//创单成功待支付
                false => createResponse.IsGatewayTimeOut switch
                {
                    true => LineProductSupplierOrderStatus.Purchasing, //超时等待采购通知
                    false => LineProductSupplierOrderStatus.CreateFail //非超时直接创单失败
                }
            };
            
            travelLineOrder.SupplierOrderId = supplierOrder.SupplierOrderId;//采购单号更新
            
            //创单记录
            var createRecord = new TravelLineSupplierOrderRecord()
            {
                BaseOrderId = baseOrder.Id,
                SupplierOrderId = supplierOrder.SupplierOrderId,
                RecordType = LineProductSupplierOrderRecordType.Create,
                IsSuccess = createIsSuccess,
                ErrorMsg = createResponse.Msg,
                ErrorCode = createResponse.Code
            };
            createRecord.SetTenantId(tenantId);
            await _dbContext.AddAsync(createRecord);

            #endregion

            #region 支付订单

            if (createIsSuccess)
            {
                await OrderPayBaseProcess(
                    baseOrder: baseOrder,
                    travelLineOrder: travelLineOrder,
                    supplierOrder: supplierOrder,
                    lineSkuTypeItems : lineSkuTypeItems,
                    orderPrices: orderPrices,
                    openSupplierType: openSupplierType);
            }

            #endregion
            
            result.IsSuccess = createIsSuccess;
            result.Msg = createResponse.Msg;
        }
        else
        {
            var errorMsg = "订单年龄段信息与供应端不匹配";
            if (!extraInfoCheck)
                errorMsg = "附加信息不匹配";
            
            //创建失败
            var createFailRecord = new TravelLineSupplierOrderRecord
            {
                BaseOrderId = baseOrder.Id,
                SupplierOrderId = supplierOrder.SupplierOrderId,
                RecordType = LineProductSupplierOrderRecordType.Create,
                IsSuccess = false,
                ErrorMsg = errorMsg
            };
            createFailRecord.SetTenantId(receive.TenantId);
            await _dbContext.AddAsync(createFailRecord);
            
            result.Msg = errorMsg;
        }
        
        if (isNewSupplierOrder)
        {
            //新增供应商订单
            await _dbContext.AddAsync(supplierOrder);
        }

        #region 触发价库更新同步

        foreach (var item in lineSkuTypeItems)
        {
            var message = new LineOrderTriggerScheduleChannelSyncMessage
            {
                OpenSupplierType = travelLineOrder.OpenSupplierType!.Value,
                TravelDate = travelLineOrder.TravelBeginDate,
                TimeSlotName = travelLineOrder.TimeSlotName,
                LineProductId = travelLineOrder.LineProductId,
                LineProductSkuId = travelLineOrder.LineProductSkuId,
                LineProductSkuTypeItemId = item.SkuTypeItemId,
                OutProductId = item.ActivityId,
                OutProductOptionId = item.PackageId,
                OutSkuId = item.SkuId
            };
            await _capPublisher.PublishAsync(CapTopics.Product.LineOrderTriggerScheduleChannelSync, message);
        }

        #endregion
        
        return result;
    }

    [UnitOfWork]
    public async Task<SupplierOrderDeliveryOutput> OrderDelivery(SupplierOrderDeliveryInput input)
    {
        var result = new SupplierOrderDeliveryOutput {Code = _successCode};
        var baseOrder = await _dbContext.BaseOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.Id == input.BaseOrderId);
        var travelLineOrder = await _dbContext.TravelLineOrder.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        var supplierOrder = await _dbContext.TravelLineSupplierOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId
                                      && x.LineProductOrderId == travelLineOrder.Id
                                      && x.SupplierOrderId == input.SupplierOrderId);

        var deliveryStatus = new List<LineProductSupplierOrderStatus>
        {
            LineProductSupplierOrderStatus.WaitingForDeliver,
            LineProductSupplierOrderStatus.DeliveryFail,
            LineProductSupplierOrderStatus.Delivered
        };
        if (!deliveryStatus.Contains(supplierOrder.OrderStatus))
        {
            result.Code = -1;
            result.Msg = "订单状态无法发货";
            return result;
        }
        
        var oldVouchers = await _dbContext.TravelLineSupplierOrderVouchers
            .IgnoreQueryFilters()
            .Where(x => x.BaseOrderId == input.BaseOrderId)
            .ToListAsync();
        if (oldVouchers.Any())
        {
            _dbContext.RemoveRange(oldVouchers);//因为允许多次发货,所以需要删除之前的凭证数据
        }

        //供货方订单状态-已发货
        supplierOrder.OrderStatus = LineProductSupplierOrderStatus.Delivered;
        supplierOrder.UpdateTime = DateTime.Now;

        //供货方订单发货记录
        var deliveryRecord = new TravelLineSupplierOrderRecord();
        if (input.Vouchers.Any())
        {
            //记录供货方凭证数据
            var vouchers = new List<TravelLineSupplierOrderVoucher>();
            foreach (var voucher in input.Vouchers.Select(voucherItem => new TravelLineSupplierOrderVoucher
                     {
                         BaseOrderId = baseOrder.Id,
                         FilePath = voucherItem.FilePath,
                         Thumbnail = voucherItem.ThumbnailPath,
                         SourcePath = voucherItem.SourcePath,
                         ImageSourcePath = voucherItem.ImageSourcePath,
                         PdfSourcePath = voucherItem.PdfSourcePath
                     }))
            {
                voucher.SetTenantId(baseOrder.TenantId);
                vouchers.Add(voucher);
            }

            if (vouchers.Any())
                await _dbContext.AddRangeAsync(vouchers);

            //OTA发货
            var syncVouchers = vouchers.Select(x => new SupplierOrderVoucherItem
            {
                FilePath = x.FilePath,
                ThumbnailPath = x.Thumbnail,
                SourcePath = x.SourcePath,
                ImageSourcePath = x.ImageSourcePath,
                PdfSourcePath = x.PdfSourcePath
            }).ToList();
            deliveryRecord = await _travelLineVoucherService.BaseDelivery(
                baseOrder: baseOrder,
                travelLineOrder: travelLineOrder,
                vouchers: syncVouchers,
                ignoreEmailSend: false);
        }
        else
        {
            //开放平台同步凭证失败或Saas处理图片失败.记录发货失败结果
            deliveryRecord = new TravelLineSupplierOrderRecord
            {
                BaseOrderId = baseOrder.Id,
                SupplierOrderId = supplierOrder.SupplierOrderId,
                RecordType = LineProductSupplierOrderRecordType.Delivery,
                IsSuccess = false,
                ErrorMsg = input.Msg,
                ErrorCode = input.ErrorCode
            };
            
            await _travelLineOrderMessageService.SendConfirmedMessageAsync(
                new TravelLineOrderSendConfirmedMessageInput()
                {
                    BaseOrderId = baseOrder.Id
                });
        }

        deliveryRecord.SetTenantId(baseOrder.TenantId);
        await _dbContext.AddAsync(deliveryRecord);
        
        
        // 更新订单价格
        await UpdatePriceAfterDelivery(
            supplierOrderId: input.SupplierOrderId,
            baseOrder: baseOrder,
            supplierOrder: supplierOrder);
        
        return result;
    }
    
    [UnitOfWork]
    public async Task SupplierOrderStatusNotify(SupplierOrderStatusNotifyInput input)
    {
        if (input.OpenSupplierType == OpenSupplierType.System)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        //订单状态通知枚举映射
        if (_orderStatusNotifyMap.TryGetValue(input.OpenSupplierOrderStatus,
                out OrderStatusNotifyMapEnum? statusNotifyMapEnum))
        {
            var supplierOrder = await _dbContext.TravelLineSupplierOrders
                .Where(x => x.BaseOrderId == input.BaseOrderId)
                .FirstOrDefaultAsync();

            if (supplierOrder is null)
            {
                throw new BusinessException(ErrorTypes.Order.OrderNotFind);
            }

            //特殊处理-发货失败
            if (input.OpenSupplierOrderStatus == OpenSupplierOrderStatus.DeliverFailed)
            {
                //判断供应端原始连接是否有值
                if (!string.IsNullOrEmpty(input.OriginalVoucherUrl))
                {
                    //供应商原始凭证地址有值表示供应端发货成功
                    statusNotifyMapEnum = statusNotifyMapEnum with
                    {
                        SupplierOrderStatus = LineProductSupplierOrderStatus.Delivered
                    };
                }
            }

            supplierOrder.OrderStatus = statusNotifyMapEnum.SupplierOrderStatus;
            supplierOrder.UpdateTime = DateTime.Now;

            var supplierOrderRecord = new TravelLineSupplierOrderRecord()
            {
                BaseOrderId = input.BaseOrderId,
                SupplierOrderId = supplierOrder.SupplierOrderId,
                RecordType = statusNotifyMapEnum.RecordType,
                IsSuccess = statusNotifyMapEnum.RecordTypeResult,
                ErrorCode = statusNotifyMapEnum.ErrorCode,
                ErrorMsg = input.ErrorMsg
            };

            await _dbContext.AddAsync(supplierOrderRecord);
        }
    }
    
    [UnitOfWork]
    public async Task<ResyncSupplierOrderDataOutput> ResyncOrderData(ResyncSupplierOrderDataInput input)
    {
        var result = new ResyncSupplierOrderDataOutput();
        var baseOrder = await _dbContext.BaseOrders
            .FirstOrDefaultAsync(x => x.Id == input.BaseOrderId);
        var lineOrder = await _dbContext.TravelLineOrder
            .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        var supplierOrder = await _dbContext.TravelLineSupplierOrders
            .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        var lineSkuTypeItems = await _dbContext.TravelLineOrderSkuTypeItems
            .Where(x => x.BaseOrderId == input.BaseOrderId)
            .ToListAsync();

        if (baseOrder is null || supplierOrder is null || lineOrder is null)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        if (string.IsNullOrEmpty(supplierOrder.SupplierOrderId))
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        
        var orderPrices = await _dbContext.OrderPrices.IgnoreQueryFilters()
            .Where(x => x.BaseOrderId == input.BaseOrderId)
            .ToListAsync();

        var processResult =  await OrderDetailProcess(
            baseOrder: baseOrder,
            supplierOrder: supplierOrder,
            lineSkuTypeItems:lineSkuTypeItems,
            orderPrices: orderPrices);

        result.IsSuccess = processResult.IsSuccess;
        result.Msg = processResult.Msg;
        result.NeedPay = processResult.NeedPay;
        result.NeedDelivery = processResult.NeedDelivery;
        result.OrderDetailStatus = processResult.OpenSupplierOrderDetailStatus;
        
        return result;
    }

    [UnitOfWork]
    public async Task<SupplierOrderRetryPayOutput> RetryPay(SupplierOrderRetryPayInput input)
    {
        var result = new SupplierOrderRetryPayOutput();
        var baseOrder = await _dbContext.BaseOrders
            .FirstOrDefaultAsync(x => x.Id == input.BaseOrderId);
        var lineOrder = await _dbContext.TravelLineOrder
            .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        var supplierOrder = await _dbContext.TravelLineSupplierOrders
            .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);

        if (baseOrder is null || supplierOrder is null || lineOrder is null)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        if (supplierOrder.OrderStatus != LineProductSupplierOrderStatus.WaitingForPay)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        if (string.IsNullOrEmpty(supplierOrder.SupplierOrderId))
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        //查询订单价格
        var orderPrices = await _dbContext.OrderPrices
            .Where(x => x.BaseOrderId == input.BaseOrderId)
            .ToListAsync();

        var lineSkuItems = await _dbContext.TravelLineOrderSkuTypeItems
            .Where(x => x.BaseOrderId == input.BaseOrderId)
            .ToListAsync();

        //获取开放平台供应商类型
        var openSupplierType = supplierOrder.SupplierType.ToString().ToLowerInvariant();
        var payIsSuccess = await OrderPayBaseProcess(
            baseOrder: baseOrder,
            travelLineOrder: lineOrder,
            supplierOrder: supplierOrder,
            lineSkuTypeItems: lineSkuItems,
            orderPrices: orderPrices,
            openSupplierType: openSupplierType
        );
        result.IsSuccess = payIsSuccess;
        return result;
    }
    
    #region 私有

    private async Task<bool> OrderPayBaseProcess(
        BaseOrder baseOrder,
        TravelLineOrder travelLineOrder,
        TravelLineSupplierOrder supplierOrder,
        List<TravelLineOrderSkuTypeItem> lineSkuTypeItems,
        List<OrderPrice> orderPrices,
        string openSupplierType)
    {
        //发起支付请求
        var payRequest = new SupplierOrderPayBalanceRequest
        {
            OutOrderId = supplierOrder.SupplierOrderId,
            OrderId = supplierOrder.BaseOrderId.ToString(),
            SupplierType = openSupplierType
        };
        var tenantId = supplierOrder.TenantId;
        var payResponse = await _openSupplierService.PayOrder(payRequest,tenantId);
        var payIsSuccess = payResponse.Code == _successCode;
        if (payIsSuccess)
        {
            //更新供应商订单状态
            var transactionStatus = OpenSupplierOrderTransactionStatus.Fail;
            if (!string.IsNullOrEmpty(payResponse.Data.TransactionStatus))
            {
                transactionStatus = _openPlatformBaseService.GetSaasEnumValue<OpenSupplierOrderTransactionStatus>(payResponse.Data.TransactionStatus);
            }
            supplierOrder.OrderStatus = transactionStatus switch
            {
                OpenSupplierOrderTransactionStatus.Success => LineProductSupplierOrderStatus.WaitingForDeliver,
                OpenSupplierOrderTransactionStatus.Processing => LineProductSupplierOrderStatus.PayProcessing,
                OpenSupplierOrderTransactionStatus.Fail => LineProductSupplierOrderStatus.WaitingForPay,
                _ => throw new ArgumentOutOfRangeException(nameof(transactionStatus), transactionStatus, null)
            };
            
            //查询订单详情
            await OrderDetailProcess(
                baseOrder: baseOrder,
                supplierOrder: supplierOrder,
                orderPrices: orderPrices,
                lineSkuTypeItems: lineSkuTypeItems);

            if (supplierOrder.OrderStatus == LineProductSupplierOrderStatus.WaitingForDeliver)
            {
                #region 采购成功.saas线路订单状态变更

                travelLineOrder.Status = TravelLineOrderStatus.Confirmed;
                travelLineOrder.UpdateTime = DateTime.Now;
                
                var confirmedLog = new OrderLogs
                {
                    OperationType = OrderOperationType.Confirmed,
                    OrderLogType = OrderLogType.TravelLine,
                    OrderId = baseOrder.Id,
                    UserId = 0,
                    UserName = "System",
                    OperationRole = UserType.None,
                };
                confirmedLog.SetTenantId(tenantId);
                await _dbContext.AddAsync(confirmedLog);

                await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess, new NotifyMessageProcessMessage
                {
                    NotifyEventSubType = NotifyEventSubType.TravelLine_Confirmed,
                    NotifyMode = NotifyMode.SiteMessage,
                    SendToTheRole = SendToTheRole.AgencyStaff,
                    TenantId = baseOrder.TenantId,
                    Variables = new
                    {
                        BaseOrderId = baseOrder.Id,
                        AgencyId = baseOrder.AgencyId,
                        UserId = baseOrder.UserId,
                        baseOrder.ProductName,
                        baseOrder.ResourceName,
                        baseOrder.ProductSkuName,
                    }
                });
                
                #endregion

                if (travelLineOrder is { IsChannelTimeliness: true, TimelinessChannelTypes: not null })
                {
                    //时效订单.需要发送时效凭证到渠道(不影响正常流程)
                    await _capPublisher.PublishAsync(CapTopics.Order.ChannelTimelinessVoucherSync,
                        new ChannelTimelinessVoucherSyncMessage
                        {
                            TenantId = tenantId,
                            BaseOrderId = baseOrder.Id,
                            SubOrderId = travelLineOrder.Id,
                            OrderType = baseOrder.OrderType,
                            ChannelOrderNo = baseOrder.ChannelOrderNo,
                            SellingPlatform = baseOrder.SellingPlatform,
                            AgencyId = baseOrder.AgencyId,
                            TimelinessChannelTypes = travelLineOrder.IsChannelTimeliness ? travelLineOrder.TimelinessChannelTypes : null,
                            NeedConfirm = true,
                            ProductId = travelLineOrder.LineProductId,
                            TimelinessTriggerType = OpenChannelTimelinessTriggerType.SupplierCreatedOrder
                        });
                }
                
                /*
                 * Saas订单状态为已确认/完成，提示飞猪渠道插旗为红色
                 */
                await _capPublisher.PublishAsync(CapTopics.Order.ChannelOrderFlagModify, new ChannelOrderFlagModifyMessage
                {
                    ChannelOrderNo = baseOrder.ChannelOrderNo,
                    SellingPlatform = baseOrder.SellingPlatform,
                    FlagType = OpenChannelOrderFlagType.Red,
                    AgencyId = baseOrder.AgencyId,
                    TenantId = baseOrder.TenantId
                });
            }
        }
        
        //支付记录
        payIsSuccess = supplierOrder.OrderStatus != LineProductSupplierOrderStatus.WaitingForPay;//还在待支付状态 =>支付失败
        var payRecord = new TravelLineSupplierOrderRecord()
        {
            BaseOrderId = baseOrder.Id,
            SupplierOrderId = supplierOrder.SupplierOrderId,
            RecordType = LineProductSupplierOrderRecordType.Payment,
            IsSuccess = payIsSuccess,
            ErrorMsg = payResponse.Msg,
            ErrorCode = payResponse.Code
        };
        payRecord.SetTenantId(baseOrder.TenantId);
        await _dbContext.AddAsync(payRecord);
        
        return payIsSuccess;
    }

    /// <summary>
    /// 订单详情处理
    /// </summary>
    /// <param name="baseOrder"></param>
    /// <param name="supplierOrder"></param>
    /// <param name="lineSkuTypeItems"></param>
    /// <param name="orderPrices"></param>
    /// <returns></returns>
    private async Task<OpenSupplierOrderDetailProcessOutput> OrderDetailProcess(
        BaseOrder baseOrder,
        TravelLineSupplierOrder supplierOrder,
        List<TravelLineOrderSkuTypeItem> lineSkuTypeItems,
        List<OrderPrice> orderPrices)
    {
        var result = new OpenSupplierOrderDetailProcessOutput();
        var tenantId = supplierOrder.TenantId;
        var openSupplierType = supplierOrder.SupplierType.ToString().ToLowerInvariant();
        //查询订单详情
        var orderDetailResponse = await _openSupplierService.GetOrderDetail(
            new SupplierOrderDetailRequest
            {
                OutOrderId = supplierOrder.SupplierOrderId,
                SupplierType = openSupplierType,
                OrderId = supplierOrder.BaseOrderId.ToString()
            }, tenantId);
        var isSuccess = orderDetailResponse.Code == _successCode;
        OpenSupplierOrderDetailStatus? orderStatus = null;
        if (isSuccess)
        {
            var orderDetail = orderDetailResponse.Data;
            // 价格更新处理
            await UpdateCostAndCommission(
                orderDetail: orderDetail,
                baseOrder: baseOrder,
                supplierOrder: supplierOrder,
                orderPrices: orderPrices,
                lineSkuTypeItems: lineSkuTypeItems
            );
            
            //订单状态补充
            orderStatus =
                _openPlatformBaseService.GetSaasEnumValue<OpenSupplierOrderDetailStatus>(orderDetail.OrderStatus);

            switch (orderStatus.Value)
            {
                case OpenSupplierOrderDetailStatus.Ordered
                    when supplierOrder.OrderStatus == LineProductSupplierOrderStatus.Purchasing: //采购中 => 待支付
                case OpenSupplierOrderDetailStatus.Payed
                    when supplierOrder.OrderStatus is LineProductSupplierOrderStatus.WaitingForPay or LineProductSupplierOrderStatus.PayProcessing: //待支付 or 支付中 => 已支付
                case OpenSupplierOrderDetailStatus.Shipped
                    when supplierOrder.OrderStatus is LineProductSupplierOrderStatus.WaitingForPay or LineProductSupplierOrderStatus.PayProcessing: //已发货,但是saas状态还卡在支付的状态
                case OpenSupplierOrderDetailStatus.Shipped
                    when supplierOrder.OrderStatus is LineProductSupplierOrderStatus.DeliveryFail 
                        or LineProductSupplierOrderStatus.Delivered
                        or LineProductSupplierOrderStatus.WaitingForDeliver : //发货失败 or 待发货 or  已发货
                case OpenSupplierOrderDetailStatus.Cancelled: //已取消
                    if (_orderDetailStatusMap.TryGetValue(orderStatus.Value, out OrderDetailStatusMapEnum? detailStatusMapEnum))
                    {
                        //发货成功or失败不需要新增操作记录
                        if (supplierOrder.OrderStatus != LineProductSupplierOrderStatus.DeliveryFail &&
                            supplierOrder.OrderStatus != LineProductSupplierOrderStatus.Delivered)
                        {
                            supplierOrder.OrderStatus = detailStatusMapEnum.SupplierOrderStatus;
                            supplierOrder.UpdateTime = DateTime.Now;

                            var supplierOrderRecord = new TravelLineSupplierOrderRecord()
                            {
                                BaseOrderId = baseOrder.Id,
                                SupplierOrderId = supplierOrder.SupplierOrderId,
                                RecordType = detailStatusMapEnum.RecordType,
                                IsSuccess = detailStatusMapEnum.RecordTypeResult,
                                ErrorCode = detailStatusMapEnum.ErrorCode
                            };
                            await _dbContext.AddAsync(supplierOrderRecord);
                        }
                        
                        //待支付
                        result.NeedPay = detailStatusMapEnum.SupplierOrderStatus == LineProductSupplierOrderStatus.WaitingForPay;
                        //待发货
                        result.NeedDelivery = detailStatusMapEnum.SupplierOrderStatus == LineProductSupplierOrderStatus.WaitingForDeliver;
                        
                                                
                        #region 重复发货校验

                        if (result.NeedDelivery &&
                            supplierOrder.OrderStatus == LineProductSupplierOrderStatus.Delivered)
                        {
                            // 限制[凭证转换失败]订单
                            var checkSupplierRecord = await _dbContext.TravelLineSupplierOrderRecords.AsNoTracking()
                                .Where(x => x.BaseOrderId == baseOrder.Id)
                                .OrderByDescending(x => x.Id)
                                .FirstOrDefaultAsync();
                            if (checkSupplierRecord is { ErrorCode: (int)OrderBusinessErrorCodeType.DeliverFailed })
                            {
                                var checkOrderTicket = orderDetail.Tickets.FirstOrDefault();
                                if (checkOrderTicket?.Vouchers.Any() is false) //订单详情正常不返回凭证.不做发货处理
                                {
                                    result.NeedDelivery = false; // 不做发货处理
                                }
                            }
                        }

                        #endregion
                    }
                    break;
            }
        }

        result.IsSuccess = isSuccess;
        result.Msg = orderDetailResponse.Msg;
        result.OpenSupplierOrderDetailStatus = orderStatus;
        return result;
    }
    
    /// <summary>
    /// 订单状态通知枚举映射
    /// <param name="RecordTypeResult">表示对应操作的结果</param>
    /// </summary>
    record OrderStatusNotifyMapEnum(
        LineProductSupplierOrderStatus SupplierOrderStatus,
        LineProductSupplierOrderRecordType RecordType,
        int? ErrorCode,
        bool RecordTypeResult);

    private static readonly Dictionary<OpenSupplierOrderStatus, OrderStatusNotifyMapEnum> _orderStatusNotifyMap = new()
    {
        // 订单被拒绝
        {
            OpenSupplierOrderStatus.Reject, new OrderStatusNotifyMapEnum(LineProductSupplierOrderStatus.Reject,
                LineProductSupplierOrderRecordType.Reject, (int)OrderBusinessErrorCodeType.Rejected,true)
        },
        // 订单退款
        {
            OpenSupplierOrderStatus.Cancelled, new OrderStatusNotifyMapEnum(LineProductSupplierOrderStatus.Refunded,
                LineProductSupplierOrderRecordType.Refund, (int)OrderBusinessErrorCodeType.Refunded,true)
        },
        // 发货失败
        {
            OpenSupplierOrderStatus.DeliverFailed, new OrderStatusNotifyMapEnum(LineProductSupplierOrderStatus.DeliveryFail,
                LineProductSupplierOrderRecordType.Delivery, (int)OrderBusinessErrorCodeType.DeliverFailed,false)
        },
        // 创建订单成功
        {
            OpenSupplierOrderStatus.Created, new OrderStatusNotifyMapEnum(LineProductSupplierOrderStatus.WaitingForPay,
                LineProductSupplierOrderRecordType.Create, null,true)
        },
        // 订单支付成功
        {
            OpenSupplierOrderStatus.PaySuccess, new OrderStatusNotifyMapEnum(LineProductSupplierOrderStatus.WaitingForDeliver,
                LineProductSupplierOrderRecordType.Payment, null,true)
        },
        // 订单支付失败
        {
            OpenSupplierOrderStatus.PayFailed, new OrderStatusNotifyMapEnum(LineProductSupplierOrderStatus.WaitingForPay,
                LineProductSupplierOrderRecordType.Payment, null,false)
        }
    };

    /// <summary>
    /// 订单详情状态枚举映射
    /// </summary>
    /// <param name="SupplierOrderStatus"></param>
    /// <param name="RecordType"></param>
    /// <param name="ErrorCode"></param>
    /// <param name="RecordTypeResult"></param>
    record OrderDetailStatusMapEnum(LineProductSupplierOrderStatus SupplierOrderStatus,
        LineProductSupplierOrderRecordType RecordType,
        int? ErrorCode,
        bool RecordTypeResult);
    private static readonly Dictionary<OpenSupplierOrderDetailStatus, OrderDetailStatusMapEnum> _orderDetailStatusMap =
        new()
        {
            //创单成功
            {
                OpenSupplierOrderDetailStatus.Ordered, new OrderDetailStatusMapEnum(
                    LineProductSupplierOrderStatus.WaitingForPay, LineProductSupplierOrderRecordType.Create, null,
                    true)
            },
            
            //支付成功
            {
                OpenSupplierOrderDetailStatus.Payed, new OrderDetailStatusMapEnum(
                    LineProductSupplierOrderStatus.WaitingForDeliver, LineProductSupplierOrderRecordType.Payment,
                    null, true)
            },
            
            //已发货,不直接更新为已发货,因为订单详情状态为已发货时,且需要发货的时候会触发订单发货流程
            {
                OpenSupplierOrderDetailStatus.Shipped, new OrderDetailStatusMapEnum(
                    LineProductSupplierOrderStatus.WaitingForDeliver, LineProductSupplierOrderRecordType.Payment, null, true)
            },

            //已取消
            {
                OpenSupplierOrderDetailStatus.Cancelled, new OrderDetailStatusMapEnum(
                    LineProductSupplierOrderStatus.Refunded,
                    LineProductSupplierOrderRecordType.Refund, (int)OrderBusinessErrorCodeType.Refunded, true)
            }
        };
    
    /// <summary>
    /// 联系人信息数据补全
    /// </summary>
    /// <param name="contactsFieldInfo"></param>
    /// <returns></returns>
    private SupplierOrderCreateOrderContactInfo ContactInfoDataSupplement(
        List<OrderFieldInformationTypeOutput> contactFieldInfo)
    {
        //获取联系人字段模板信息
        var contactsOrderFieldInfos = contactFieldInfo
            .SelectMany(x => x.Fields)
            .ToList();
        var personSupplementData = PersonDataSupplement(contactsOrderFieldInfos, TemplateType.Contacts);//处理联系人名称数据
        var formattingContactName = personSupplementData.SplicedEnName;
        var formattingContactEmail = contactsOrderFieldInfos
            .FirstOrDefault(x => x.FieldCode == "ContactEmail")?.FieldValue;
        var formattingPhoneInfoJson = contactsOrderFieldInfos
            .FirstOrDefault(x => x.FieldCode == "ContactPhone")?.FieldValue;
        var formattingPhoneInfo = new PhoneDto();
        if (!string.IsNullOrEmpty(formattingPhoneInfoJson))
        {
            formattingPhoneInfo = JsonConvert.DeserializeObject<PhoneDto>(formattingPhoneInfoJson);
            //目前只做简单的区号处理
            if (!string.IsNullOrEmpty(formattingPhoneInfo.Type) && formattingPhoneInfo.Type.StartsWith("[") && formattingPhoneInfo.Type.Contains(']'))
            {
                var endOfAreaCodeIndex = formattingPhoneInfo.Type.IndexOf(']');
                formattingPhoneInfo.Type = formattingPhoneInfo.Type.Substring(1, endOfAreaCodeIndex - 1); // 提取区号
            }
            else
            {
                formattingPhoneInfo.Type = "+86";
            }
        }
        

        var contactInfo = new SupplierOrderCreateOrderContactInfo
        {
            Email = formattingContactEmail,
            MobilePrefix = formattingPhoneInfo.Type,
            Mobile = formattingPhoneInfo.Value,
            EnName = formattingContactName,
            IdCard = "string", //目前硬编码默认值
            IdCardType = "string", //目前硬编码默认值,
            FirstName = personSupplementData.FirstName,
            LastName = personSupplementData.LastName
        };
        
        return contactInfo;
    }

    /// <summary>
    /// 订单出行人信息数据补全,分配对应的skuId
    /// </summary>
    /// <returns></returns>
    private List<SupplierOrderCreateOrderTravelerInfo> TravelerInfoDataSupplement(
        List<OrderFieldInformationTypeOutput> travelFieldInfo)
    {
        var travelerInfos = new List<SupplierOrderCreateOrderTravelerInfo>();
        var productTemplateTypes = new List<ProductTemplateType>
        {
            ProductTemplateType.EachAdult,
            ProductTemplateType.EachChild,
            ProductTemplateType.EachBaby,
            ProductTemplateType.EachElderly,
            ProductTemplateType.EachOther
        };
        //获取出行人字段模板信息
        var travelOrderFieldInfos = travelFieldInfo
            .Where(x => productTemplateTypes.Contains(x.ProductTemplateType))
            .ToList();
        
        foreach (var fieldInfoItem in travelOrderFieldInfos)
        {
            var currentFieldIs = fieldInfoItem.Fields;
            var personSupplementData = PersonDataSupplement(currentFieldIs, TemplateType.Travel);//处理出行人人名称数据
            var formattingContactName = personSupplementData.SplicedEnName;
            
            // 处理出行人邮箱
            var formattingContactEmail = currentFieldIs
                .FirstOrDefault(x => x.FieldCode == "ContactEmail")?.FieldValue;
            
            // 处理出行人电话
            var formattingPhoneInfoJson = currentFieldIs
                .FirstOrDefault(x => x.FieldCode == "ContactPhone")?.FieldValue;
            var formattingPhoneInfo = new PhoneDto();
            if (!string.IsNullOrEmpty(formattingPhoneInfoJson))
            {
                formattingPhoneInfo = JsonConvert.DeserializeObject<PhoneDto>(formattingPhoneInfoJson);
                //目前只做简单的区号处理
                if (!string.IsNullOrEmpty(formattingPhoneInfo.Type) && formattingPhoneInfo.Type.StartsWith("[") && formattingPhoneInfo.Type.Contains(']'))
                {
                    var endOfAreaCodeIndex = formattingPhoneInfo.Type.IndexOf(']');
                    formattingPhoneInfo.Type = formattingPhoneInfo.Type.Substring(1, endOfAreaCodeIndex - 1); // 提取区号
                }
                else
                {
                    formattingPhoneInfo.Type = "+86";
                }
            }

            // 处理出行人证件信息
            var formattingCardInfoJson = currentFieldIs
                .FirstOrDefault(x => x.FieldCode == "Card")?.FieldValue;
            var formattingCardInfo = new CardDto();
            if(!string.IsNullOrEmpty(formattingCardInfoJson))
            {
                formattingCardInfo = JsonConvert.DeserializeObject<CardDto>(formattingCardInfoJson);
            }
            
            // 处理出行人年龄信息
            var formattingBrithDateStringValue = currentFieldIs
                .FirstOrDefault(x => x.FieldCode == "BirthDate")?.FieldValue;
            int? formattingAgeValue = null;
            if (DateTime.TryParse(formattingBrithDateStringValue, out var brithDate))
            {
                formattingAgeValue = _openPlatformBaseService.CalculateAge(brithDate);
            }
            
            //处理出行人体重信息
            var formattingWeightStringValue = currentFieldIs
                .FirstOrDefault(x => x.FieldCode == "Weight")?.FieldValue;
            int? formattingWeightValue = null;
            if (int.TryParse(formattingWeightStringValue, out var weight))
            {
                formattingWeightValue = weight;
            }
            
            //处理出行人性别信息
            var formattingGenderStringValue = currentFieldIs
                .FirstOrDefault(x => x.FieldCode == "Gender")?.FieldValue;
            int formattingGenderValue = 0;
            if (int.TryParse(formattingGenderStringValue, out var gender))
            {
                formattingGenderValue = gender;
            }
            
            #region 根据年龄段分配
            
            LineSkuPriceType ? travelSkuPriceType = null;
            switch (fieldInfoItem.ProductTemplateType)
            {
                case ProductTemplateType.EachAdult:
                    travelSkuPriceType = LineSkuPriceType.Adult;
                    break;
                case ProductTemplateType.EachChild:
                    travelSkuPriceType = LineSkuPriceType.Child;
                    break;
                case ProductTemplateType.EachBaby:
                    travelSkuPriceType = LineSkuPriceType.Baby;
                    break;
                case ProductTemplateType.EachElderly:
                    travelSkuPriceType = LineSkuPriceType.Elderly;
                    break;
                case ProductTemplateType.EachOther:
                    travelSkuPriceType = LineSkuPriceType.Other;
                    break;
            }

            #endregion
            
            
            travelerInfos.Add(new SupplierOrderCreateOrderTravelerInfo
            {
                EnName = formattingContactName,
                IdCard = formattingCardInfo.Value,
                Email = formattingContactEmail,
                MobilePrefix = formattingPhoneInfo.Type,
                Mobile = formattingPhoneInfo.Value,
                Age = formattingAgeValue,
                Weight = formattingWeightValue,
                Gender = formattingGenderValue,
                LineSkuPriceType = travelSkuPriceType
            });
        }

        return travelerInfos;
    }

    /// <summary>
    /// 订单通用信息数据补全
    /// </summary>
    /// <param name="DepartureValue">出发地</param>
    /// <param name="DestinationValue">目的地</param>
    record OrderCommonData(string DepartureValue,string DestinationValue,
        string DepartureAddressValue,string DestinationAddressValue);
    private OrderCommonData OrderCommonSupplement(List<OrderFieldInformationTypeOutput> travelFieldInfo)
    {
        var travelOrderFieldInfos = travelFieldInfo
            .Where(x => x.ProductTemplateType == ProductTemplateType.OrderCommon)
            .SelectMany(x => x.Fields)
            .ToList();

        var departureValue = string.Empty;
        var departureAddressValue = string.Empty;
        var destinationValue = string.Empty;
        var destinationAddressValue = string.Empty;
        
        var departureJson = travelOrderFieldInfos.FirstOrDefault(x => x.FieldCode == "DepartureJson")
            ?.FieldValue;
        if (!string.IsNullOrEmpty(departureJson))
        {
            var departureData = JsonConvert.DeserializeObject<AddressDto>(departureJson);
            //合并地点名称和详细地址(一致取其中一个)
            departureValue = departureData?.Detail.ResourceZhName ?? string.Empty;
            departureAddressValue = departureData?.Address ?? string.Empty;
        }
        
        var destinationJson = travelOrderFieldInfos.FirstOrDefault(x => x.FieldCode == "DestinationJson")
            ?.FieldValue;
        if (!string.IsNullOrEmpty(destinationJson))
        {
            var destinationData = JsonConvert.DeserializeObject<AddressDto>(destinationJson);
            //合并地点名称和详细地址
            destinationValue = destinationData?.Detail.ResourceZhName ?? string.Empty;
            destinationAddressValue = destinationData?.Address ?? string.Empty;
        }

        return new OrderCommonData(departureValue,destinationValue,
            DepartureAddressValue: departureAddressValue,
            DestinationAddressValue: destinationAddressValue);
    }
    
    record OrderFieldData(
        List<OrderFieldInformationTypeOutput> TravelFieldInfo,
        List<OrderFieldInformationTypeOutput> ContactsFieldInfo,
        List<OrderFieldInformationTypeOutput> OrderConfirmFieldInfo
    );
    /// <summary>
    /// 查询订单的字段信息
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    private async Task<OrderFieldData> GetOrderFieldInfo(long baseOrderId)
    {
        var orderFieldTypes = await _dbContext.OrderFieldInformationType
            .IgnoreQueryFilters()
            .AsNoTracking()
            .Where(x => x.BaseOrderId == baseOrderId)
            .ToListAsync();
        var orderFieldTypesOut = _mapper.Map<List<OrderFieldInformationTypeOutput>>(orderFieldTypes);
        var orderFieldTypeIds = orderFieldTypes.Select(x => x.Id).ToList();
        var orderFields = await _dbContext.OrderFieldInformations
            .IgnoreQueryFilters()
            .AsNoTracking()
            .Where(x => orderFieldTypeIds.Contains(x.OrderFieldInformationTypeId))
            .ToListAsync();
        var orderFieldsOut = _mapper.Map<List<OrderFieldInformationOutput>>(orderFields);
        orderFieldTypesOut.ForEach(type =>
        {
            var fields = orderFieldsOut.Where(x => x.OrderFieldInformationTypeId == type.Id).OrderBy(x => x.Sort)
                .ToList();
            type.Fields = fields;
        });

        var travelFieldInfo = orderFieldTypesOut.Where(x => x.TemplateType == TemplateType.Travel)
            .ToList();
        var contactsFieldInfo = orderFieldTypesOut.Where(x => x.TemplateType == TemplateType.Contacts)
            .ToList();
        var orderConfirmFieldInfo = orderFieldTypesOut.Where(x => x.TemplateType == TemplateType.Order)
            .ToList();

        return new OrderFieldData(
            TravelFieldInfo: travelFieldInfo,
            ContactsFieldInfo: contactsFieldInfo,
            OrderConfirmFieldInfo: orderConfirmFieldInfo);
    }

    /// <summary>
    /// 联系人信息补充
    /// </summary>
    record PersonData(string SplicedEnName,string FirstName,string LastName);

    private PersonData PersonDataSupplement(List<OrderFieldInformationOutput> orderFieldInfos,TemplateType templateType)
    {
        //英文名
        var firstNameFieldValue = orderFieldInfos
            .FirstOrDefault(x => x.FieldCode == "FirstName")?.FieldValue;
        var firstName = firstNameFieldValue ?? string.Empty;

        //英文姓
        var lastNameFieldValue = orderFieldInfos
            .FirstOrDefault(x => x.FieldCode == "LastName")?.FieldValue;
        var lastName = lastNameFieldValue ?? string.Empty;

        var splicedEnName = string.Empty;
        if (!string.IsNullOrEmpty(firstName) && !string.IsNullOrEmpty(lastName))
        {
            //拼接(姓在前名在后): 英文姓 英文名
            splicedEnName = $"{lastName} {firstName}";
        }
        // 如果英文姓名不全，则从中文姓名转换
        else
        {
            var chinesNameFieldCode = templateType == TemplateType.Contacts ? "ContactName" : "Name";
            var chineseName = orderFieldInfos
                .FirstOrDefault(x => x.FieldCode == chinesNameFieldCode)?.FieldValue;

            if (!string.IsNullOrEmpty(chineseName))
            {
                var nameInfo = ParseChineseName(chineseName);
                if (string.IsNullOrEmpty(firstName))
                    firstName = nameInfo.FirstName;
                if (string.IsNullOrEmpty(lastName))
                    lastName = nameInfo.LastName;
                splicedEnName = $"{lastName} {firstName}".Trim();
            }
        }

        return new PersonData(splicedEnName,firstName,lastName);
    }

    /// <summary>
    /// 解析中文姓名并转换为拼音
    /// </summary>
    private (string FirstName, string LastName) ParseChineseName(string chineseName)
    {
        try
        {
            return ChinesePinyinUtil.ParseChineseName(chineseName);
        }
        catch (Exception e)
        {
            _logger.LogError(e,"[OpenSupplier]解析中文姓名:{@Input}", chineseName);
            return (string.Empty, string.Empty);
        }
    }

    /// <summary>
    /// 年龄段价格数量校验
    /// </summary>
    /// <param name="orderPrices"></param>
    /// <param name="supplierSkuType"></param>
    /// <returns>
    /// </returns>
    private (int count,int adultCount, int childCount, int babyCount, bool priceCheckSuccess) PriceAndQuantityPreCheck(
        List<OrderPrice> orderPrices, string supplierSkuType)
    {
        var (count, adultCount, childCount, babyCount, priceCheckSuccess) = (0, 0, 0, 0, true);
        var skuType = _openPlatformBaseService.GetSaasEnumValue<SupplierProductSkuType>(supplierSkuType);
        var orderPriceSubTypeCount = orderPrices.GroupBy(x => x.OrderSubType).Count(); //需要判断是否与产品侧的年龄段类型不一致
        switch (skuType)
        {
            case SupplierProductSkuType.Adult:
                priceCheckSuccess = orderPriceSubTypeCount == 1; //存在其他年龄段价格.创单失败
                adultCount = GetQuantityByOrderSubType(orderPrices, LineSkuPriceType.Adult);
                count = adultCount;
                break;
            case SupplierProductSkuType.Child:
                priceCheckSuccess = orderPriceSubTypeCount == 1; //存在其他年龄段价格.创单失败
                childCount = GetQuantityByOrderSubType(orderPrices, LineSkuPriceType.Child);
                count = childCount;
                break;
            case SupplierProductSkuType.Baby:
                priceCheckSuccess = orderPriceSubTypeCount == 1; //存在其他年龄段价格.创单失败
                babyCount = GetQuantityByOrderSubType(orderPrices, LineSkuPriceType.Baby);
                count = babyCount;
                break;
            case SupplierProductSkuType.Senior:
                (adultCount, childCount, babyCount) = GetCountsByOrderSubTypes(orderPrices);
                count = adultCount + childCount + babyCount;
                break;
            case SupplierProductSkuType.Person:
                count = orderPrices.Sum(x => x.Quantity);
                break;
        }

        return (count, adultCount, childCount, babyCount, priceCheckSuccess);
    }
    
    private int GetQuantityByOrderSubType(List<OrderPrice> orderPrices, LineSkuPriceType subType)
    {
        return orderPrices.Where(x => x.OrderSubType == (int)subType).Sum(x => x.Quantity);
    }

    private (int adultCount, int childCount, int babyCount) GetCountsByOrderSubTypes(List<OrderPrice> orderPrices)
    {
        var adultCount = GetQuantityByOrderSubType(orderPrices, LineSkuPriceType.Adult);
        var childCount = GetQuantityByOrderSubType(orderPrices, LineSkuPriceType.Child);
        var babyCount = GetQuantityByOrderSubType(orderPrices, LineSkuPriceType.Baby);
        return (adultCount, childCount, babyCount);
    }

    /// <summary>
    /// 订单额外信息数据检查处理
    /// </summary>
    /// <param name="extraInfos"></param>
    /// <param name="locationInfo"></param>
    /// <returns></returns>
    private List<SupplierOrderCreateOrderExtraItem> ExtraInfoCheck(List<SupplierProductDetailExtraInfoItem> extraInfos,
        OrderCommonData locationInfo)
    {
        var itemExtraInfos = new List<SupplierOrderCreateOrderExtraItem>();
        var pickUpLocationValue = new List<(string key, string optionKey, string optionValue)>(); //供应端出发点信息数据
        var deliveryLocationValue = new List<(string key, string optionKey, string optionValue)>(); //供应端到达地点信息数据
        var hasPickUpLocation = false; //有上车点取上车点,没有再取出发酒店
        var extraInfoItemList = new List<(ApiSkuExtraInfoDataType dataType,ApiSkuExtraInfoValueType valueType)>(); //包含所有扩展信息
        foreach (var extraInfoItem in extraInfos)
        {
            var extraInfoKeyType =
                _openPlatformBaseService.GetSaasEnumValue<ApiSkuExtraInfoDataType>(extraInfoItem.Key);
            var extraInfoValueType = 
                _openPlatformBaseService.GetSaasEnumValue<ApiSkuExtraInfoValueType>(extraInfoItem.ValueType);
            extraInfoItemList.Add(new (extraInfoKeyType,extraInfoValueType));
            var optionsValueList = extraInfoItem.ValueOptions.Select(x => (extraInfoItem.Key, x.Key, x.Value)).ToList();
            if (optionsValueList.Any())
            {
                switch (extraInfoKeyType)
                {
                    case ApiSkuExtraInfoDataType.PickUpLocation:
                        pickUpLocationValue = optionsValueList;
                        hasPickUpLocation = true;
                        break;
                    case ApiSkuExtraInfoDataType.DepartureHotel:
                        if (!hasPickUpLocation)
                        {
                            pickUpLocationValue = optionsValueList;
                        }

                        break;
                    case ApiSkuExtraInfoDataType.DeliveryLocation:
                        deliveryLocationValue = optionsValueList;
                        break;
                }
            }
        }

        //产品详情扩展信息如果只有一条(key,value)数据,直接拿该条数据下单
        foreach (var item in extraInfoItemList)
        {
            var itemExtraInfo = new SupplierOrderCreateOrderExtraItem
            {
                Key = item.dataType.ToString(),
            };
            switch (item.dataType)
            {
                case ApiSkuExtraInfoDataType.PickUpLocation:
                case ApiSkuExtraInfoDataType.DepartureHotel:
                    
                    if (item.valueType == ApiSkuExtraInfoValueType.Select)
                    {
                        //1：供应端要求选则的，则只需要用下拉框里面的内容去匹配下单
                        var pickUpLocationValueItem = pickUpLocationValue.Count == 1
                            ? pickUpLocationValue.FirstOrDefault()
                            : pickUpLocationValue.FirstOrDefault(x => x.optionValue == locationInfo.DepartureValue);
                        itemExtraInfo.Value = pickUpLocationValueItem.optionKey;
                    }
                    else
                    {
                        //2：供应端要求是输入的，则需要拼接详细地址传过去下单(一致去重)
                        itemExtraInfo.Value =
                            string.Equals(locationInfo.DepartureValue, locationInfo.DepartureAddressValue)
                                ? locationInfo.DepartureValue
                                : $"{locationInfo.DepartureValue}{locationInfo.DepartureAddressValue}";
                    }
                    
                    break;
                case ApiSkuExtraInfoDataType.DeliveryLocation:

                    if (item.valueType == ApiSkuExtraInfoValueType.Select)
                    {
                        //1：供应端要求选则的，则只需要用下拉框里面的内容去匹配下单
                        var deliveryLocationValueItem = deliveryLocationValue.Count == 1
                            ? deliveryLocationValue.FirstOrDefault()
                            : deliveryLocationValue.FirstOrDefault(x => x.optionValue == locationInfo.DestinationValue);
                        itemExtraInfo.Value = deliveryLocationValueItem.optionKey;
                    }
                    else
                    {
                        //2：供应端要求是输入的，则需要拼接详细地址传过去下单(一致去重)
                        itemExtraInfo.Value = string.Equals(locationInfo.DestinationValue, locationInfo.DestinationAddressValue)
                            ? locationInfo.DestinationValue
                            : $"{locationInfo.DestinationValue}{locationInfo.DestinationAddressValue}";
                    }
                    
                    break;
            }
            
            itemExtraInfos.Add(itemExtraInfo);
        }
        
        return itemExtraInfos;
    }

    /// <summary>
    /// 价格更新更新逻辑
    /// </summary>
    /// <param name="orderDetail"></param>
    /// <param name="baseOrder"></param>
    /// <param name="supplierOrder"></param>
    /// <param name="orderPrices"></param>
    /// <param name="lineSkuTypeItems"></param>
    private async Task UpdateCostAndCommission(
        SupplierOrderDetailResponse orderDetail,
        BaseOrder baseOrder, 
        TravelLineSupplierOrder supplierOrder,
        List<OrderPrice> orderPrices,
        List<TravelLineOrderSkuTypeItem> lineSkuTypeItems)
    {
        decimal calcCostDiscountAmount = 0;
        foreach (var item in orderPrices)
        {
            var lineSkuTypeItem =
                lineSkuTypeItems.FirstOrDefault(x => x.SkuTypeItemId == item.OrderSubItemId);
            if (lineSkuTypeItem == null) continue;
            var orderDetailItem =
                orderDetail.Items.FirstOrDefault(x => x.OutSkuId == lineSkuTypeItem.SkuId);
            if (orderDetailItem == null) continue;

            item.CostPrice = orderDetailItem.Price;
            item.CostDiscountRate = orderDetailItem.CommissionRate; //采购折扣比例
            calcCostDiscountAmount += orderDetailItem.CommissionAmount; //采购折扣金额(累加)
        }

        baseOrder.CostDiscountAmount = calcCostDiscountAmount;

        supplierOrder.Amount =
            orderDetail.Amount ?? orderPrices.Sum(x => x.CostPrice * x.Quantity); //供应商订单详情没有返回订单总额.手动计算
        supplierOrder.CommissionRate = orderPrices.Max(x => x.CostDiscountRate); //采购折扣比例
        
        await Task.CompletedTask;
    }
    
    /// <summary>
    /// 发货后 - 更新价格
    /// </summary>
    /// <param name="supplierOrderId"></param>
    /// <param name="baseOrder"></param>
    /// <param name="supplierOrder"></param>
    private async Task UpdatePriceAfterDelivery(
        string supplierOrderId,
        BaseOrder baseOrder, 
        TravelLineSupplierOrder supplierOrder)
    {
        try
        {
            var tenantId = baseOrder.TenantId;
            var openSupplierType = supplierOrder.SupplierType.ToString().ToLowerInvariant();
            var orderPrices = await _dbContext.OrderPrices.IgnoreQueryFilters()
                .Where(x => x.BaseOrderId == baseOrder.Id)
                .ToListAsync();
            var lineSkuTypeItems = await _dbContext.TravelLineOrderSkuTypeItems.IgnoreQueryFilters()
                .Where(x => x.BaseOrderId == baseOrder.Id)
                .ToListAsync();
            
            //查询订单详情
            var orderDetailResponse = await _openSupplierService.GetOrderDetail(
                new SupplierOrderDetailRequest
                {
                    OutOrderId = supplierOrderId,
                    SupplierType = openSupplierType,
                    OrderId = supplierOrder.BaseOrderId.ToString()
                }, tenantId);
            var isSuccess = orderDetailResponse.Code == _successCode;
            if (isSuccess)
            {
                var orderDetail = orderDetailResponse.Data;
                await UpdateCostAndCommission(
                    orderDetail: orderDetail,
                    baseOrder: baseOrder,
                    supplierOrder: supplierOrder,
                    orderPrices: orderPrices,
                    lineSkuTypeItems: lineSkuTypeItems
                    );
            }
        }
        catch (Exception e)
        {
            // ignore
        }
    }
    #endregion
}