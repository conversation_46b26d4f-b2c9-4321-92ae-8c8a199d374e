using Common.ServicesHttpClient;
using Contracts.Common.Order.DTOs.ExportPdf;
using Contracts.Common.Order.DTOs.GroupBooking;
using Contracts.Common.Order.DTOs.HotelOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Tenant.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using NPOI.SS.Formula.Functions;
using Order.Api.Extensions;
using Order.Api.Services.Interfaces;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;

namespace Order.Api.Services;

/// <summary>
/// pdf导出业务
/// </summary>
public class PDFExportService : IPDFExportService
{
    private readonly CustomDbContext _dbContext;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ServicesAddress _servicesAddress;

    public PDFExportService(IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> options,
        CustomDbContext dbContext)
    {
        _httpClientFactory = httpClientFactory;
        _servicesAddress = options.Value;
        _dbContext = dbContext;
    }

    #region 酒店
    public async Task<byte[]> ExportHotelOrderPdf(ExportHotelOrderPdfInput input)
    {
        var orderPrintSetting = await _dbContext.GroupBookingPreOrderSettings.AsNoTracking()
            .FirstOrDefaultAsync();
        if (orderPrintSetting != null
            && !string.IsNullOrEmpty(orderPrintSetting.ElectronicSeal))
        {
            var client = _httpClientFactory.CreateClient();
            var response =
                await client.GetAsync(_servicesAddress.Resource_FileDownloadOssObject(orderPrintSetting.ElectronicSeal));
            var data = await response.Content.ReadAsByteArrayAsync();
            input.ImpressionImage = data;
        }
        if (input.ExportHotelOrderType == ExportHotelOrderType.Confirm)
        {
            var offsetOrders = await _dbContext.OffsetOrders.AsNoTracking()
                .Where(x => x.BaseOrderId == input.OrderDetail.BaseOrderId && x.OffsetType == OffsetOrderType.Receipt)
                .ToListAsync();
            if (offsetOrders.Any())
                input.OrderDetail.TotalAmount = input.OrderDetail.TotalAmount + offsetOrders.Sum(x => x.Amount);
        }
        if(input.Language == "zh")
            return GenerateHotelOrderPdf(input);
        else
            return GenerateHotelOrderPdfByEn(input);
    }

    /// <summary>
    /// 酒店入住单pdf-中英文
    /// </summary>
    /// <returns></returns>
    private byte[] GenerateHotelOrderPdf(ExportHotelOrderPdfInput detail)
    {
        var gdsSupplierApiTypes = new List<SupplierApiType>() { 
            SupplierApiType.GDS,
            SupplierApiType.Youxia
        };
        using var memoryStream = new MemoryStream();
        Document.Create(container =>
        {
            container.Page(page =>
            {
                var fontColor = Color.FromHex("#333333");
                page.Size(PageSizes.A4);
                page.MarginLeft(24);
                page.MarginRight(24);
                page.PageColor(Colors.White);
                page.DefaultTextStyle(x => x.FontSize(10F).FontFamily("msyh"));

                page.Content()
                    .Padding(10)
                    .Column(column =>
                    {
                        var subHeadStyle = TextStyle
                            .Default
                            .FontSize(11)
                            .ExtraBold()
                            .FontColor(fontColor);

                        var diamondStyle = TextStyle
                            .Default
                            .FontSize(11)
                            .FontColor(Colors.LightBlue.Medium);

                        var reminderStyle = TextStyle
                            .Default
                            .FontSize(9)
                            .FontColor(fontColor);

                        var orderHeadStyle = TextStyle
                          .Default
                          .FontSize(10)
                          .FontColor(Color.FromRGB(90, 90, 90));

                        var orderDetailStyle = TextStyle
                          .Default
                          .FontSize(9)
                          .FontColor(fontColor);

                        column.Item()
                            .Element(HeadingStyle)
                            .PaddingTop(10)
                            .PaddingBottom(10)
                            .Text(detail.Title)
                            .FontSize(15)
                            .SemiBold();

                        #region 酒店信息
                        column.Item()
                            .Text(text =>
                            {
                                text.Span("◆").Style(diamondStyle);
                                text.Span("酒店信息（hotel information）").Style(subHeadStyle);
                            });

                        AddLine(column.Item());

                        column.Item()
                            .PaddingLeft(5)
                            .PaddingBottom(10)
                            .Table(table =>
                            {
                                table.ColumnsDefinition(columns =>
                                {
                                    columns.RelativeColumn();
                                    columns.RelativeColumn();
                                    columns.RelativeColumn();
                                });

                                table.Cell().Row(1).Column(1).Element(HotelInfoLabelStyle).Text("酒店名称(hotel name):");
                                var hotelName = detail.OrderDetail.HotelName +
                                    (string.IsNullOrEmpty(detail.OrderDetail.HotelEnName) ? string.Empty : $"•{detail.OrderDetail.HotelEnName}");
                                table.Cell().Row(1).Column(2).Element(HotelInfoStyle).Text(hotelName);
                                table.Cell().Row(2).Column(1).Element(HotelInfoLabelStyle).Text("电话(telephone):");
                                table.Cell().Row(2).Column(2).Element(HotelInfoStyle).Text(detail.OrderDetail.HotelTelePhone ?? "-");
                                table.Cell().Row(3).Column(1).Element(HotelInfoLabelStyle).Text("地址(address):");
                                table.Cell().Row(3).Column(2).Element(HotelInfoStyle).Text(detail.OrderDetail.HotelAddress ?? "-");


                            });
                        #endregion

                        #region 订单信息
                        column.Item()
                            .PaddingTop(5)
                            .Text(text =>
                            {
                                text.Span("◆").Style(diamondStyle);
                                text.Span("订单信息（order information）").Style(subHeadStyle);
                            });
                        AddLine(column.Item());
                        //入住单
                        if (detail.ExportHotelOrderType == ExportHotelOrderType.CheckIn)
                        {
                            column.Item()
                                .PaddingBottom(10)
                                .Table(table =>
                                {

                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn();

                                        columns.ConstantColumn(200);
                                    });

                                    table.Header(header =>
                                    {
                                        table.Cell().Row(1).Column(1).Element(OrderHeadTopStyle).Text("订单号").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(1).Element(OrderHeadBottonStyle).Text("Reference Number").Style(orderHeadStyle);
                                        table.Cell().Row(1).Column(2).Element(OrderHeadTopStyle).Text("入离日期").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(2).Element(OrderHeadBottonStyle).Text("Travel Date").Style(orderHeadStyle);
                                    });

                                    var checkInDate = $"{detail.OrderDetail.CheckInDate.ToString("yyyy-MM-dd")}~{detail.OrderDetail.CheckOutDate.ToString("yyyy-MM-dd")}"
                                        + $"（{detail.OrderDetail.PriceStrategyNightsCount}晚）";
                                    table.Cell().Row(3).Column(1).Element(OrderDetailStyle).Text(detail.OrderDetail.BaseOrderId).Style(orderDetailStyle);
                                    table.Cell().Row(3).Column(2).Element(OrderDetailStyle).Text(checkInDate).Style(orderDetailStyle);
                                });
                        }
                        else //确认单
                        {
                            column.Item()
                                .PaddingBottom(10)
                                .Table(table =>
                                {

                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn();

                                        columns.ConstantColumn(200);
                                        columns.ConstantColumn(200);
                                    });

                                    table.Header(header =>
                                    {
                                        table.Cell().Row(1).Column(1).Element(OrderHeadTopStyle).Text("订单总价").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(1).Element(OrderHeadBottonStyle).Text("Total Cost").Style(orderHeadStyle);
                                        table.Cell().Row(1).Column(2).Element(OrderHeadTopStyle).Text("订单号").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(2).Element(OrderHeadBottonStyle).Text("Reference Number").Style(orderHeadStyle);
                                        table.Cell().Row(1).Column(3).Element(OrderHeadTopStyle).Text("入离日期").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(3).Element(OrderHeadBottonStyle).Text("Travel Date").Style(orderHeadStyle);
                                    });

                                    var checkInDate = $"{detail.OrderDetail.CheckInDate.ToString("yyyy-MM-dd")}~{detail.OrderDetail.CheckOutDate.ToString("yyyy-MM-dd")}"
                                        + $"（{detail.OrderDetail.PriceStrategyNightsCount}晚）";
                                    table.Cell().Row(3).Column(1).Element(OrderDetailStyle)
                                        .Text($"{detail.OrderDetail.TotalAmount} {detail.OrderDetail.PaymentCurrencyCode}").Style(orderDetailStyle);
                                    table.Cell().Row(3).Column(2).Element(OrderDetailStyle).Text(detail.OrderDetail.BaseOrderId).Style(orderDetailStyle);
                                    table.Cell().Row(3).Column(3).Element(OrderDetailStyle).Text(checkInDate).Style(orderDetailStyle);
                                });
                        }

                        column.Item()
                            .PaddingBottom(10)
                            .Table(table =>
                            {

                                table.ColumnsDefinition(columns =>
                                {
                                    columns.RelativeColumn();

                                    columns.ConstantColumn(60);
                                    columns.ConstantColumn(60);
                                    columns.ConstantColumn(60);
                                    columns.ConstantColumn(120);
                                    columns.ConstantColumn(100);
                                });

                                table.Header(header =>
                                {
                                    table.Cell().Row(1).Column(1).Element(OrderHeadTopStyle).Text("房型").Style(orderHeadStyle);
                                    table.Cell().Row(2).Column(1).Element(OrderHeadBottonStyle).Text("room type").Style(orderHeadStyle);
                                    table.Cell().Row(1).Column(2).Element(OrderHeadTopStyle).Text("房间数").Style(orderHeadStyle);
                                    table.Cell().Row(2).Column(2).Element(OrderHeadBottonStyle).Text("room").Style(orderHeadStyle);
                                    if (!gdsSupplierApiTypes.Contains(detail.OrderDetail.SupplierApiType))
                                    {
                                        table.Cell().Row(1).Column(3).Element(OrderHeadTopStyle).Text("餐型/间").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(3).Element(OrderHeadBottonStyle).Text("meal plan/room").Style(orderHeadStyle);
                                    }
                                    else
                                    {
                                        table.Cell().Row(1).Column(3).Element(OrderHeadTopStyle).Text("早餐/间").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(3).Element(OrderHeadBottonStyle).Text("breakfast/room").Style(orderHeadStyle);
                                    }
                                    table.Cell().Row(1).Column(4).Element(OrderHeadTopStyle).Text("入住人国籍").Style(orderHeadStyle);
                                    table.Cell().Row(2).Column(4).Element(OrderHeadBottonStyle).Text("nationality").Style(orderHeadStyle);
                                    table.Cell().Row(1).Column(5).Element(OrderHeadTopStyle).Text("入住人").Style(orderHeadStyle);
                                    table.Cell().Row(2).Column(5).Element(OrderHeadBottonStyle).Text("guests").Style(orderHeadStyle);
                                    table.Cell().Row(1).Column(6).Element(OrderHeadTopStyle).Text("确认号").Style(orderHeadStyle);
                                    table.Cell().Row(2).Column(6).Element(OrderHeadBottonStyle).Text("confirmation code").Style(orderHeadStyle);
                                });
                                var nationalName = string.Empty;
                                if (detail.OrderDetail?.NationalityInfo is not null)
                                {
                                    nationalName =
                                        $"{detail.OrderDetail.NationalityInfo.ZHName}\r\n{detail.OrderDetail.NationalityInfo.ENName}";
                                }
                                var guestNames = string.Join("\r\n", detail.HotelGuests
                                    .Select(g =>
                                    {
                                        var str = "";
                                        if (g.RoomNumber.HasValue)
                                            str += $"Room {g.RoomNumber}:\r\n";
                                        str += string.Join("\r\n", g.GuestInfos.Select(x =>
                                        {
                                            if (x.HotelOrderGuestType == HotelOrderGuestType.Child && x.Age.HasValue)
                                                return $"儿童({x.Age.Value}岁):{x.GuestName}";
                                            else
                                                return $"成人:{x.GuestName}";
                                        }));
                                        return str;
                                    }));
                                var hotelRoomName = detail.OrderDetail.HotelRoomName;
                                if (!string.IsNullOrEmpty(detail.OrderDetail.HotelRoomEnName))
                                    hotelRoomName = hotelRoomName + "\r\n" + detail.OrderDetail.HotelRoomEnName;
                                table.Cell().Row(3).Column(1).Element(OrderDetailStyle).Text(hotelRoomName).Style(orderDetailStyle);
                                table.Cell().Row(3).Column(2).Element(OrderDetailStyle).Text(detail.OrderDetail.PriceStrategyRoomsCount).Style(orderDetailStyle);
                                if (!gdsSupplierApiTypes.Contains(detail.OrderDetail.SupplierApiType))
                                {
                                    var boardCodeStr = GetBoardCodeStr(detail.OrderDetail.BoardCodeType, detail.OrderDetail.BoardCount, "zh");
                                    table.Cell().Row(3).Column(3).Element(OrderDetailStyle).Text(boardCodeStr).Style(orderDetailStyle);
                                }
                                else
                                    table.Cell().Row(3).Column(3).Element(OrderDetailStyle).Text(detail.OrderDetail.PriceStrategyNumberOfBreakfast).Style(orderDetailStyle);
                                table.Cell().Row(3).Column(4).Element(OrderDetailStyle).Text(nationalName).Style(orderDetailStyle);
                                table.Cell().Row(3).Column(5).Element(OrderDetailStyle).Text(guestNames).Style(orderDetailStyle);
                                table.Cell().Row(3).Column(6).Element(OrderDetailStyle).Text(detail.OrderDetail.ConfirmCode).Style(orderDetailStyle);
                            });
                        #endregion

                        //订单备注
                        column.Item()
                            .PaddingTop(5)
                            .Text(text =>
                            {
                                text.Span("◆").Style(diamondStyle);
                                text.Span("订单备注（remark）").Style(subHeadStyle);
                            });
                        AddLine(column.Item());

                        column.Item().PaddingBottom(10).Element(HotelInfoStyle)
                            .Text(string.IsNullOrEmpty(detail.Message) ? "-" : detail.Message);

                        #region 温馨提示
                        column.Item()
                            .Text(text =>
                            {
                                text.Span("◆").Style(diamondStyle);
                                text.Span("温馨提示（reminder）").Style(subHeadStyle);
                            });
                        AddLine(column.Item());

                        var firstStr = "1. 请您于酒店前台办理入住手续时出示您的有效护照证件或提供您护照证件上的英文名，以便酒店前台准确查询到您的预订信息" +
                            "（英文名顺序为姓/名 例如 zhang/ming） Upon your arrival please provide valid government-issued ID to the hotel front desk to " +
                            "locate the accurate booking.";
                        var secondStr = "2. 请您于酒店前台办理入住手续时向前台明确你所需要的床型，酒店将尽量为您安排（大床房DOUBLE/KING双床房TWINS） " +
                            "Please tell front desk agent your preferred bed type if your booking comes with more than one (e.g. Double or Twin). " +
                            "The final arrangement is fully subject to hotel’savailability.";
                        var thridStr = "3. 所有特殊申请需于酒店前台办理入住手续时以酒店最终确认为准 All special requests are not guaranteed. Please confirm your " +
                            "special requests with front desk upon arrival.";
                        var fourStr = "4. 如需延迟入住或于21:00后到店办理入住，请提前联系酒店确认，否则有可能被酒店视作自动放弃房间保留权（房费均不予以退还）" +
                            "Please contact your hotel in advance should you need a late check-in after 9pm on your check-in date, or your booking might be canceled by hotel " +
                            "(with no refund).";
                        var fiveStr = "5. 如房费包含早餐，部分酒店可能会对随行儿童收取额外的早餐费用，具体情况以酒店核实为准 " +
                            "Please be noted some hotels charge extra breakfast fee for children even when your room offers breakfast. " +
                            "The actual situation is subject to the hotel regulations.";
                        var sixStr = "6. 预订成功后不接受任何形式的更改与取消 No-show/Change/Cancellation are NOT refundable.";

                        column.Item().PaddingBottom(10).Text(firstStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(secondStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(thridStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(fourStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(fiveStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(sixStr).Style(reminderStyle);
                        #endregion

                        if (detail.ImpressionImage != null && detail.ImpressionImage.Length > 0
                            && !gdsSupplierApiTypes.Contains(detail.OrderDetail.SupplierApiType)
                            && detail.ExportHotelOrderType == ExportHotelOrderType.Confirm)
                        {
                            column.Item().PaddingTop(10).TranslateX(340).TranslateY(0)
                                .Width(150, QuestPDF.Infrastructure.Unit.Point)
                                .AlignCenter()
                                .Image(detail.ImpressionImage)
                                .FitArea()
                                .WithCompressionQuality(ImageCompressionQuality.High);
                        }

                        #region 自定义样式
                        IContainer HeadingStyle(IContainer container)
                        {
                            return container
                                .AlignCenter()
                                .PaddingBottom(10);
                        }

                        IContainer LabelStyle(IContainer container)
                        {
                            return container
                                .AlignCenter()
                                .PaddingBottom(10);
                        }

                        IContainer HotelInfoLabelStyle(IContainer container)
                        {
                            return container
                                .Width(160F)
                                .Height(24F)
                                .DefaultTextStyle(style => style.FontSize(10).FontColor(fontColor))
                                .AlignLeft()
                                .AlignMiddle();
                        }

                        IContainer HotelInfoStyle(IContainer container)
                        {
                            return container
                                .DefaultTextStyle(style => style.FontSize(10).FontColor(fontColor))
                                .AlignLeft()
                                .AlignMiddle();
                        }

                        IContainer OrderHeadTopStyle(IContainer container)
                        {
                            return container
                                .BorderLeft(0.5F)
                                .BorderRight(0.5F)
                                .BorderTop(0.5F)
                                .BorderColor(Color.FromRGB(242, 243, 247))
                                .Background(Color.FromRGB(242, 243, 247))
                                .ShowOnce()
                                .MinWidth(20F)
                                .MinHeight(17F)
                                .AlignCenter()
                                .AlignBottom();
                        }

                        IContainer OrderHeadBottonStyle(IContainer container)
                        {
                            return container
                                .BorderLeft(0.5F)
                                .BorderRight(0.5F)
                                .BorderBottom(0.5F)
                                .BorderColor(Color.FromRGB(242, 243, 247))
                                .Background(Color.FromRGB(242, 243, 247))
                                .AlignTop()
                                .ShowOnce()
                                .MinHeight(17F)
                                .AlignCenter()
                                .AlignTop();
                        }

                        IContainer OrderDetailStyle(IContainer container)
                        {
                            return container
                                .Border(1F)
                                .BorderColor(Colors.Grey.Lighten4)
                                .ShowOnce()
                                .MinWidth(20F)
                                .MinHeight(40F)
                                .AlignCenter()
                                .AlignMiddle();
                        }

                        void AddLine(IContainer container)
                        {
                            container
                                .PaddingBottom(7)
                                .PaddingTop(5)
                                .LineHorizontal(0.5F)
                                .LineColor(Colors.Grey.Lighten3);
                        }
                        #endregion
                    });

                page.Footer()
                    .AlignCenter()
                    .Text(x =>
                    {
                        x.Span("Page");
                        x.CurrentPageNumber();
                    });
            });
        })
        .GeneratePdf(memoryStream);

        return memoryStream.ToArray();
    }

    /// <summary>
    /// 酒店入住单pdf-英文
    /// </summary>
    /// <returns></returns>
    private byte[] GenerateHotelOrderPdfByEn(ExportHotelOrderPdfInput detail)
    {
        var gdsSupplierApiTypes = new List<SupplierApiType>() {
            SupplierApiType.GDS,
            SupplierApiType.Youxia
        };
        using var memoryStream = new MemoryStream();
        Document.Create(container =>
        {
            container.Page(page =>
            {
                var fontColor = Color.FromHex("#333333");
                page.Size(PageSizes.A4);
                page.MarginLeft(24);
                page.MarginRight(24);
                page.PageColor(Colors.White);
                page.DefaultTextStyle(x => x.FontSize(10F).FontFamily("msyh"));

                page.Content()
                    .Padding(10)
                    .Column(column =>
                    {
                        var subHeadStyle = TextStyle
                            .Default
                            .FontSize(11)
                            .ExtraBold()
                            .FontColor(fontColor);

                        var diamondStyle = TextStyle
                            .Default
                            .FontSize(11)
                            .FontColor(Colors.LightBlue.Medium);

                        var reminderStyle = TextStyle
                            .Default
                            .FontSize(9)
                            .FontColor(fontColor);

                        var orderHeadStyle = TextStyle
                          .Default
                          .FontSize(10)
                          .FontColor(Color.FromRGB(90, 90, 90));

                        var orderDetailStyle = TextStyle
                          .Default
                          .FontSize(9)
                          .FontColor(fontColor);

                        column.Item()
                            .Element(HeadingStyle)
                            .PaddingTop(10)
                            .PaddingBottom(10)
                            .Text(detail.Title)
                            .FontSize(15)
                            .SemiBold();

                        #region 酒店信息
                        column.Item()
                            .Text(text =>
                            {
                                text.Span("◆").Style(diamondStyle);
                                text.Span("Hotel Information").Style(subHeadStyle);
                            });

                        AddLine(column.Item());

                        column.Item()
                            .PaddingLeft(5)
                            .PaddingBottom(10)
                            .Table(table =>
                            {
                                table.ColumnsDefinition(columns =>
                                {
                                    columns.RelativeColumn();
                                    columns.RelativeColumn();
                                    columns.RelativeColumn();
                                });

                                table.Cell().Row(1).Column(1).Element(HotelInfoLabelStyle).Text("Hotel name:");
                                table.Cell().Row(1).Column(2).Element(HotelInfoStyle).Text($"{detail.OrderDetail.HotelEnName}");
                                table.Cell().Row(2).Column(1).Element(HotelInfoLabelStyle).Text("Telephone:");
                                table.Cell().Row(2).Column(2).Element(HotelInfoStyle).Text(detail.OrderDetail.HotelTelePhone ?? "-");
                                table.Cell().Row(3).Column(1).Element(HotelInfoLabelStyle).Text("Address:");
                                table.Cell().Row(3).Column(2).Element(HotelInfoStyle).Text(string.IsNullOrEmpty(detail.OrderDetail.EnHotelAddress) ? 
                                    detail.OrderDetail.HotelAddress : detail.OrderDetail.EnHotelAddress ?? "-");


                            });
                        #endregion

                        #region 订单信息
                        column.Item()
                            .PaddingTop(5)
                            .Text(text =>
                            {
                                text.Span("◆").Style(diamondStyle);
                                text.Span("Order Information").Style(subHeadStyle);
                            });
                        AddLine(column.Item());
                        //入住单
                        if (detail.ExportHotelOrderType == ExportHotelOrderType.CheckIn)
                        {
                            column.Item()
                                .PaddingBottom(10)
                                .Table(table =>
                                {

                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn();

                                        columns.ConstantColumn(200);
                                    });

                                    table.Header(header =>
                                    {
                                        table.Cell().Row(2).Column(1).Element(OrderHeadBottonStyle).Text("Reference Number").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(2).Element(OrderHeadBottonStyle).Text("Travel Date").Style(orderHeadStyle);
                                    });

                                    var checkInDate = $"{detail.OrderDetail.CheckInDate.ToString("yyyy-MM-dd")}~{detail.OrderDetail.CheckOutDate.ToString("yyyy-MM-dd")}"
                                        + $"（{detail.OrderDetail.PriceStrategyNightsCount}N）";
                                    table.Cell().Row(3).Column(1).Element(OrderDetailStyle).Text(detail.OrderDetail.BaseOrderId).Style(orderDetailStyle);
                                    table.Cell().Row(3).Column(2).Element(OrderDetailStyle).Text(checkInDate).Style(orderDetailStyle);
                                });
                        }
                        else //确认单
                        {
                            column.Item()
                                .PaddingBottom(10)
                                .Table(table =>
                                {

                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn();

                                        columns.ConstantColumn(200);
                                        columns.ConstantColumn(200);
                                    });

                                    table.Header(header =>
                                    {
                                        table.Cell().Row(2).Column(1).Element(OrderHeadBottonStyle).Text("Total Cost").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(2).Element(OrderHeadBottonStyle).Text("Reference Number").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(3).Element(OrderHeadBottonStyle).Text("Travel Date").Style(orderHeadStyle);
                                    });

                                    var checkInDate = $"{detail.OrderDetail.CheckInDate.ToString("yyyy-MM-dd")}~{detail.OrderDetail.CheckOutDate.ToString("yyyy-MM-dd")}"
                                        + $"（{detail.OrderDetail.PriceStrategyNightsCount}N）";
                                    table.Cell().Row(3).Column(1).Element(OrderDetailStyle)
                                        .Text($"{detail.OrderDetail.TotalAmount} {detail.OrderDetail.PaymentCurrencyCode}").Style(orderDetailStyle);
                                    table.Cell().Row(3).Column(2).Element(OrderDetailStyle).Text(detail.OrderDetail.BaseOrderId).Style(orderDetailStyle);
                                    table.Cell().Row(3).Column(3).Element(OrderDetailStyle).Text(checkInDate).Style(orderDetailStyle);
                                });
                        }

                        column.Item()
                            .PaddingBottom(10)
                            .Table(table =>
                            {

                                table.ColumnsDefinition(columns =>
                                {
                                    columns.RelativeColumn();

                                    columns.ConstantColumn(60);
                                    columns.ConstantColumn(60);
                                    columns.ConstantColumn(60);
                                    columns.ConstantColumn(120);
                                    columns.ConstantColumn(100);
                                });

                                table.Header(header =>
                                {
                                    table.Cell().Row(2).Column(1).Element(OrderHeadBottonStyle).Text("room type").Style(orderHeadStyle);
                                    table.Cell().Row(2).Column(2).Element(OrderHeadBottonStyle).Text("room").Style(orderHeadStyle);
                                    if (!gdsSupplierApiTypes.Contains(detail.OrderDetail.SupplierApiType))
                                        table.Cell().Row(2).Column(3).Element(OrderHeadBottonStyle).Text("meal plan/room").Style(orderHeadStyle);
                                    else
                                        table.Cell().Row(2).Column(3).Element(OrderHeadBottonStyle).Text("breakfast/room").Style(orderHeadStyle);
                                    table.Cell().Row(2).Column(4).Element(OrderHeadBottonStyle).Text("nationality").Style(orderHeadStyle);
                                    table.Cell().Row(2).Column(5).Element(OrderHeadBottonStyle).Text("guests").Style(orderHeadStyle);
                                    table.Cell().Row(2).Column(6).Element(OrderHeadBottonStyle).Text("confirmation code").Style(orderHeadStyle);
                                });
                                var nationalName = string.Empty;
                                if (detail.OrderDetail?.NationalityInfo is not null)
                                {
                                    nationalName = $"{detail.OrderDetail.NationalityInfo.ENName}";
                                }
                                var guestNames = string.Join("\r\n", detail.HotelGuests
                                    .Select(g =>
                                    {
                                        var str = "";
                                        if (g.RoomNumber.HasValue)
                                            str += $"Room {g.RoomNumber}:\r\n";
                                        str += string.Join("\r\n", g.GuestInfos.Select(x =>
                                        {
                                            if (x.HotelOrderGuestType == HotelOrderGuestType.Child && x.Age.HasValue)
                                                return $"Child({x.Age.Value}year old):{x.GuestName}";
                                            else
                                                return $"Adult:{x.GuestName}";
                                        }));
                                        return str;
                                    }));
                                var hotelRoomName = detail.OrderDetail.HotelRoomEnName;
                                table.Cell().Row(3).Column(1).Element(OrderDetailStyle).Text(hotelRoomName).Style(orderDetailStyle);
                                table.Cell().Row(3).Column(2).Element(OrderDetailStyle).Text(detail.OrderDetail.PriceStrategyRoomsCount).Style(orderDetailStyle);
                                if (!gdsSupplierApiTypes.Contains(detail.OrderDetail.SupplierApiType))
                                {
                                    var boardCodeStr = GetBoardCodeStr(detail.OrderDetail.BoardCodeType, detail.OrderDetail.BoardCount, "en");
                                    table.Cell().Row(3).Column(3).Element(OrderDetailStyle).Text(boardCodeStr).Style(orderDetailStyle);
                                }
                                else
                                    table.Cell().Row(3).Column(3).Element(OrderDetailStyle).Text(detail.OrderDetail.PriceStrategyNumberOfBreakfast).Style(orderDetailStyle);
                                table.Cell().Row(3).Column(4).Element(OrderDetailStyle).Text(nationalName).Style(orderDetailStyle);
                                table.Cell().Row(3).Column(5).Element(OrderDetailStyle).Text(guestNames).Style(orderDetailStyle);
                                table.Cell().Row(3).Column(6).Element(OrderDetailStyle).Text(detail.OrderDetail.ConfirmCode).Style(orderDetailStyle);
                            });
                        #endregion

                        //订单备注
                        column.Item()
                            .PaddingTop(5)
                            .Text(text =>
                            {
                                text.Span("◆").Style(diamondStyle);
                                text.Span("Remark").Style(subHeadStyle);
                            });
                        AddLine(column.Item());

                        column.Item().PaddingBottom(10).Element(HotelInfoStyle)
                            .Text(string.IsNullOrEmpty(detail.Message) ? "-" : detail.Message);

                        #region 温馨提示
                        column.Item()
                            .Text(text =>
                            {
                                text.Span("◆").Style(diamondStyle);
                                text.Span("Reminder").Style(subHeadStyle);
                            });
                        AddLine(column.Item());

                        var firstStr = "1. Upon your arrival please provide valid government-issued ID to the hotel front desk to " +
                            "locate the accurate booking.";
                        var secondStr = "2. Please tell front desk agent your preferred bed type if your booking comes with more than one (e.g. Double or Twin). " +
                            "The final arrangement is fully subject to hotel’savailability.";
                        var thridStr = "3. All special requests are not guaranteed. Please confirm your special requests with front desk upon arrival.";
                        var fourStr = "4. Please contact your hotel in advance should you need a late check-in after 9pm on your check-in date, or your booking might be canceled by hotel " +
                            "(with no refund).";
                        var fiveStr = "5. Please be noted some hotels charge extra breakfast fee for children even when your room offers breakfast. " +
                            "The actual situation is subject to the hotel regulations.";
                        var sixStr = "6. No-show/Change/Cancellation are NOT refundable.";

                        column.Item().PaddingBottom(10).Text(firstStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(secondStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(thridStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(fourStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(fiveStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(sixStr).Style(reminderStyle);
                        #endregion

                        if (detail.ImpressionImage != null && detail.ImpressionImage.Length > 0
                             && !gdsSupplierApiTypes.Contains(detail.OrderDetail.SupplierApiType)
                             && detail.ExportHotelOrderType == ExportHotelOrderType.Confirm)
                        {
                            column.Item().PaddingTop(10).TranslateX(340).TranslateY(0)
                                .Width(150, QuestPDF.Infrastructure.Unit.Point)
                                .AlignCenter()
                                .Image(detail.ImpressionImage)
                                .FitArea()
                                .WithCompressionQuality(ImageCompressionQuality.High);
                        }

                        #region 自定义样式
                        IContainer HeadingStyle(IContainer container)
                        {
                            return container
                                .AlignCenter()
                                .PaddingBottom(10);
                        }

                        IContainer LabelStyle(IContainer container)
                        {
                            return container
                                .AlignCenter()
                                .PaddingBottom(10);
                        }

                        IContainer HotelInfoLabelStyle(IContainer container)
                        {
                            return container
                                .Width(160F)
                                .Height(24F)
                                .DefaultTextStyle(style => style.FontSize(10).FontColor(fontColor))
                                .AlignLeft()
                                .AlignMiddle();
                        }

                        IContainer HotelInfoStyle(IContainer container)
                        {
                            return container
                                .DefaultTextStyle(style => style.FontSize(10).FontColor(fontColor))
                                .AlignLeft()
                                .AlignMiddle();
                        }

                        IContainer OrderHeadTopStyle(IContainer container)
                        {
                            return container
                                .BorderLeft(0.5F)
                                .BorderRight(0.5F)
                                .BorderTop(0.5F)
                                .BorderColor(Color.FromRGB(242, 243, 247))
                                .Background(Color.FromRGB(242, 243, 247))
                                .ShowOnce()
                                .MinWidth(20F)
                                .MinHeight(17F)
                                .AlignCenter()
                                .AlignBottom();
                        }

                        IContainer OrderHeadBottonStyle(IContainer container)
                        {
                            return container
                                .BorderLeft(0.5F)
                                .BorderRight(0.5F)
                                .BorderBottom(0.5F)
                                .BorderTop(0.5F)
                                .BorderColor(Color.FromRGB(242, 243, 247))
                                .Background(Color.FromRGB(242, 243, 247))
                                .ShowOnce()
                                .MinHeight(20F)
                                .AlignCenter()
                                .AlignMiddle();
                        }

                        IContainer OrderDetailStyle(IContainer container)
                        {
                            return container
                                .Border(1F)
                                .BorderColor(Colors.Grey.Lighten4)
                                .ShowOnce()
                                .MinWidth(20F)
                                .MinHeight(40F)
                                .AlignCenter()
                                .AlignMiddle();
                        }

                        void AddLine(IContainer container)
                        {
                            container
                                .PaddingBottom(7)
                                .PaddingTop(5)
                                .LineHorizontal(0.5F)
                                .LineColor(Colors.Grey.Lighten3);
                        }
                        #endregion
                    });

                page.Footer()
                    .AlignCenter()
                    .Text(x =>
                    {
                        x.Span("Page");
                        x.CurrentPageNumber();
                    });
            });
        })
        .GeneratePdf(memoryStream);

        return memoryStream.ToArray();
    }

    public async Task<byte[]> ExportGroupBookingOrderPdf(ExportGroupBookingOrderPdfInput input)
    {
        var orderPrintSetting = await _dbContext.GroupBookingPreOrderSettings.AsNoTracking()
            .FirstOrDefaultAsync();
        if (orderPrintSetting != null
            && !string.IsNullOrEmpty(orderPrintSetting.ElectronicSeal))
        {
            var client = _httpClientFactory.CreateClient();
            var response =
                await client.GetAsync(_servicesAddress.Resource_FileDownloadOssObject(orderPrintSetting.ElectronicSeal));
            var data = await response.Content.ReadAsByteArrayAsync();
            input.ImpressionImage = data;
        }
        if (input.ExportHotelOrderType == ExportHotelOrderType.Confirm)
        {
            var baseOrderIds = input.Hotels.SelectMany(x => x.OrderItems)
                .Select(x => x.BaseOrderId)
                .Distinct()
                .ToList();

            var offsetOrders = _dbContext.OffsetOrders.AsNoTracking()
                .Where(x => baseOrderIds.Contains(x.BaseOrderId)).ToList();
            if (offsetOrders.Any())
                input.TotalAmount = input.TotalAmount + offsetOrders.Sum(x => x.Amount);
        }

        if (input.Language == "zh")
            return GenerateGroupBookingOrderPdf(input);
        else
            return GenerateGroupBookingOrderPdfByEn(input);
    }

    /// <summary>
    /// 团房入住单pdf
    /// </summary>
    /// <returns></returns>
    private byte[] GenerateGroupBookingOrderPdf(ExportGroupBookingOrderPdfInput detail)
    {
        using var memoryStream = new MemoryStream();
        Document.Create(container =>
        {
            container.Page(page =>
            {
                var fontColor = Color.FromHex("#333333");
                page.Size(PageSizes.A4);
                page.MarginLeft(24);
                page.MarginRight(24);
                page.PageColor(Colors.White);
                page.DefaultTextStyle(x => x.FontSize(10F).FontFamily("msyh"));

                page.Content()
                    .Padding(10)
                    .Column(column =>
                    {
                        var subHeadStyle = TextStyle
                            .Default
                            .FontSize(11)
                            .ExtraBold()
                            .FontColor(fontColor);

                        var diamondStyle = TextStyle
                            .Default
                            .FontSize(11)
                            .FontColor(Colors.LightBlue.Medium);

                        var reminderStyle = TextStyle
                            .Default
                            .FontSize(9)
                            .FontColor(fontColor);

                        var orderHeadStyle = TextStyle
                            .Default
                            .FontSize(10)
                            .FontColor(Color.FromRGB(90, 90, 90));

                        var orderDetailStyle = TextStyle
                            .Default
                            .FontSize(9)
                            .FontColor(fontColor);


                        column.Item()
                            .Element(HeadingStyle)
                            .PaddingTop(10)
                            .PaddingBottom(10)
                            .Text(detail.Title)
                            .FontSize(15)
                            .SemiBold();

                        #region 团房订单号-表头
                        column.Item()
                            .PaddingBottom(10)
                            .Table(table =>
                            {
                                if (detail.ExportHotelOrderType == ExportHotelOrderType.CheckIn)
                                {
                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn();
                                    });
                                    table.Header(header =>
                                    {
                                        table.Cell().Row(1).Column(1).Element(OrderIdTopStyle).Text($"申请单号(Application Order Number): {detail.GroupBookingApplicationFormId}").Style(orderHeadStyle);
                                    });
                                }
                                else
                                {
                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn();
                                        columns.RelativeColumn();
                                    });
                                    table.Header(header =>
                                    {
                                        table.Cell().Row(1).Column(1).Element(OrderIdTopStyle).Text("订单总价  Total Cost").Style(orderHeadStyle);
                                        table.Cell().Row(1).Column(2).Element(OrderIdTopStyle).Text("订单号  Reference Number").Style(orderHeadStyle);
                                    });
                                    table.Cell().Row(2).Column(1).Element(OrderIdDetailTopStyle).Text($"{detail.TotalAmount} {detail.PaymentCurrencyCode}").Style(orderDetailStyle);
                                    table.Cell().Row(2).Column(2).Element(OrderIdDetailTopStyle).Text(detail.GroupBookingOrderId).Style(orderDetailStyle);
                                }
                            });
                        #endregion

                        //团房订单详情
                        foreach (var hotel in detail.Hotels)
                        {
                            #region 酒店信息

                            column.Item()
                                .Text(text =>
                                {
                                    text.Span("◆").Style(diamondStyle);
                                    text.Span("酒店信息（hotel information）").Style(subHeadStyle);
                                });

                            AddLine(column.Item());

                            column.Item()
                                .PaddingLeft(5)
                                .PaddingBottom(10)
                                .Table(table =>
                                {
                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn();
                                        columns.RelativeColumn();
                                        columns.RelativeColumn();
                                    });

                                    table.Cell().Row(1).Column(1).Element(HotelInfoLabelStyle).Text("酒店名称(hotel name):");
                                    var hotelName = hotel.HotelName +
                                        (string.IsNullOrEmpty(hotel.HotelEnName) ? string.Empty : $"•{hotel.HotelEnName}");
                                    table.Cell().Row(1).Column(2).Element(HotelInfoStyle).Text(hotelName);
                                    table.Cell().Row(2).Column(1).Element(HotelInfoLabelStyle).Text("电话(telephone):");
                                    table.Cell().Row(2).Column(2).Element(HotelInfoStyle).Text(hotel.HotelTelePhone ?? "-");
                                    table.Cell().Row(3).Column(1).Element(HotelInfoLabelStyle).Text("地址(address):");
                                    table.Cell().Row(3).Column(2).Element(HotelInfoStyle).Text(hotel.HotelAddress ?? "-");

                                });
                            #endregion

                            #region 订单信息
                            column.Item()
                                .PaddingTop(5)
                                .Text(text =>
                                {
                                    text.Span("◆").Style(diamondStyle);
                                    text.Span("订单信息（order information）").Style(subHeadStyle);
                                });
                            AddLine(column.Item());
                            //入住单
                            column.Item()
                                .PaddingBottom(10)
                                .Table(table =>
                                {

                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn();

                                        columns.ConstantColumn(130);
                                        columns.ConstantColumn(40);
                                        columns.ConstantColumn(50);
                                        columns.ConstantColumn(130);
                                        columns.ConstantColumn(100);

                                    });

                                    table.Header(header =>
                                    {
                                        table.Cell().Row(1).Column(1).Element(OrderHeadTopStyle).Text("入离日期").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(1).Element(OrderHeadBottonStyle).Text("Travel Date").Style(orderHeadStyle);
                                        table.Cell().Row(1).Column(2).Element(OrderHeadTopStyle).Text("房型").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(2).Element(OrderHeadBottonStyle).Text("room type").Style(orderHeadStyle);
                                        table.Cell().Row(1).Column(3).Element(OrderHeadTopStyle).Text("房间数").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(3).Element(OrderHeadBottonStyle).Text("room").Style(orderHeadStyle);
                                        table.Cell().Row(1).Column(4).Element(OrderHeadTopStyle).Text("早餐").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(4).Element(OrderHeadBottonStyle).Text("breakfast").Style(orderHeadStyle);
                                        table.Cell().Row(1).Column(5).Element(OrderHeadTopStyle).Text("入住人").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(5).Element(OrderHeadBottonStyle).Text("guests").Style(orderHeadStyle);
                                        table.Cell().Row(1).Column(6).Element(OrderHeadTopStyle).Text("确认号").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(6).Element(OrderHeadBottonStyle).Text("confirmation code").Style(orderHeadStyle);
                                    });

                                    uint rowIndex = 3;
                                    var preCheckInDate = string.Empty;
                                    foreach (var orderItemsGroup in hotel.OrderItems.GroupBy(x=> new { x.CheckInDate,x.CheckOutDate}))
                                    {
                                        var checkInDateStr = $"{orderItemsGroup.Key.CheckInDate.ToString("yyyy-MM-dd")}\r\n{orderItemsGroup.Key.CheckOutDate.ToString("yyyy-MM-dd")}";
                                        table.Cell().RowSpan((uint)orderItemsGroup.Count()).Element(OrderDetailStyle).Text(checkInDateStr).Style(orderDetailStyle);
                                        foreach (var orderDetail in orderItemsGroup)
                                        {
                                            var guestNames = string.Join("；", orderDetail.HotelOrderGuests.Select(x => 
                                            {
                                                if(x.HotelOrderGuestType == HotelOrderGuestType.Child)
                                                    return $"{x.GuestName}({x.Age.Value}year)";
                                                return x.GuestName;
                                            }));
                                            var hotelRoomName = orderDetail.HotelRoomName;
                                            if (!string.IsNullOrEmpty(orderDetail.HotelRoomEnName))
                                                hotelRoomName = hotelRoomName + "\r\n" + orderDetail.HotelRoomEnName;
                                            //table.Cell().Row(rowIndex).Column(1).Element(OrderDetailStyle).Text(checkInDateStr).Style(orderDetailStyle);
                                            table.Cell().Row(rowIndex).Column(2).Element(OrderDetailStyle).Text(hotelRoomName).Style(orderDetailStyle);
                                            table.Cell().Row(rowIndex).Column(3).Element(OrderDetailStyle).Text(orderDetail.PriceStrategyRoomsCount).Style(orderDetailStyle);
                                            table.Cell().Row(rowIndex).Column(4).Element(OrderDetailStyle).Text(orderDetail.PriceStrategyNumberOfBreakfast).Style(orderDetailStyle);
                                            table.Cell().Row(rowIndex).Column(5).Element(OrderDetailStyle).Text(guestNames).Style(orderDetailStyle);
                                            table.Cell().Row(rowIndex).Column(6).Element(OrderDetailStyle).Text(orderDetail.ConfirmCode).Style(orderDetailStyle);

                                            rowIndex++;
                                        }
                                    }
                                });

                            #endregion
                        }

                        //订单备注
                        column.Item()
                            .PaddingTop(5)
                            .Text(text =>
                            {
                                text.Span("◆").Style(diamondStyle);
                                text.Span("订单备注（remark）").Style(subHeadStyle);
                            });
                        AddLine(column.Item());

                        column.Item().PaddingBottom(10).Element(HotelInfoStyle)
                            .Text(string.IsNullOrEmpty(detail.Message) ? "-" : detail.Message);

                        #region 温馨提示
                        column.Item()
                            .Text(text =>
                            {
                                text.Span("◆").Style(diamondStyle);
                                text.Span("温馨提示（reminder）").Style(subHeadStyle);
                            });
                        AddLine(column.Item());

                        var firstStr = "1. 请您于酒店前台办理入住手续时出示您的有效护照证件或提供您护照证件上的英文名，以便酒店前台准确查询到您的预订信息" +
                            "（英文名顺序为姓/名 例如 zhang/ming） Upon your arrival please provide valid government-issued ID to the hotel front desk to " +
                            "locate the accurate booking.";
                        var secondStr = "2. 请您于酒店前台办理入住手续时向前台明确你所需要的床型，酒店将尽量为您安排（大床房DOUBLE/KING双床房TWINS） " +
                            "Please tell front desk agent your preferred bed type if your booking comes with more than one (e.g. Double or Twin). " +
                            "The final arrangement is fully subject to hotel’savailability.";
                        var thridStr = "3. 所有特殊申请需于酒店前台办理入住手续时以酒店最终确认为准 All special requests are not guaranteed. Please confirm your " +
                            "special requests with front desk upon arrival.";
                        var fourStr = "4. 如需延迟入住或于21:00后到店办理入住，请提前联系酒店确认，否则有可能被酒店视作自动放弃房间保留权（房费均不予以退还）" +
                            "Please contact your hotel in advance should you need a late check-in after 9pm on your check-in date, or your booking might be canceled by hotel " +
                            "(with no refund).";
                        var fiveStr = "5. 如房费包含早餐，部分酒店可能会对随行儿童收取额外的早餐费用，具体情况以酒店核实为准 " +
                            "Please be noted some hotels charge extra breakfast fee for children even when your room offers breakfast. " +
                            "The actual situation is subject to the hotel regulations.";
                        var sixStr = "6. 预订成功后不接受任何形式的更改与取消 No-show/Change/Cancellation are NOT refundable.";

                        column.Item().PaddingBottom(10).Text(firstStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(secondStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(thridStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(fourStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(fiveStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(sixStr).Style(reminderStyle);
                        #endregion

                        if (detail.ImpressionImage != null && detail.ImpressionImage.Length > 0 
                            && detail.ExportHotelOrderType == ExportHotelOrderType.Confirm)
                        {
                            column.Item().PaddingTop(10).TranslateX(340).TranslateY(0)
                                .Width(150, QuestPDF.Infrastructure.Unit.Point)
                                .AlignCenter()
                                .Image(detail.ImpressionImage)
                                .FitArea()
                                .WithCompressionQuality(ImageCompressionQuality.High);
                        }

                        #region 自定义样式
                        IContainer HeadingStyle(IContainer container)
                        {
                            return container
                                .AlignCenter()
                                .PaddingBottom(10);
                        }

                        IContainer LabelStyle(IContainer container)
                        {
                            return container
                                .AlignCenter()
                                .PaddingBottom(10);
                        }

                        IContainer HotelInfoLabelStyle(IContainer container)
                        {
                            return container
                                .Width(160F)
                                .Height(24F)
                                .DefaultTextStyle(style => style.FontSize(10).FontColor(fontColor))
                                .AlignLeft()
                                .AlignMiddle();
                        }

                        IContainer HotelInfoStyle(IContainer container)
                        {
                            return container
                                .DefaultTextStyle(style => style.FontSize(10).FontColor(fontColor))
                                .AlignLeft()
                                .AlignMiddle();
                        }

                        IContainer OrderHeadTopStyle(IContainer container)
                        {
                            return container
                                .BorderLeft(0.5F)
                                .BorderRight(0.5F)
                                .BorderTop(0.5F)
                                .BorderColor(Color.FromRGB(242, 243, 247))
                                .Background(Color.FromRGB(242, 243, 247))
                                .ShowOnce()
                                .MinWidth(20F)
                                .MinHeight(17F)
                                .AlignCenter()
                                .AlignBottom();
                        }

                        IContainer OrderHeadBottonStyle(IContainer container)
                        {
                            return container
                                .BorderLeft(0.5F)
                                .BorderRight(0.5F)
                                .BorderBottom(0.5F)
                                .BorderColor(Color.FromRGB(242, 243, 247))
                                .Background(Color.FromRGB(242, 243, 247))
                                .AlignTop()
                                .ShowOnce()
                                .MinHeight(17F)
                                .AlignCenter()
                                .AlignTop();
                        }

                        IContainer OrderDetailStyle(IContainer container)
                        {
                            return container
                                .Border(1F)
                                .BorderColor(Colors.Grey.Lighten4)
                                .ShowOnce()
                                .MinWidth(20F)
                                .MinHeight(40F)
                                .AlignCenter()
                                .AlignMiddle();
                        }

                        IContainer OrderIdTopStyle(IContainer container)
                        {
                            return container
                                .Border(1F)
                                .BorderColor(Color.FromRGB(242, 243, 247))
                                .Background(Color.FromRGB(242, 243, 247))
                                .PaddingHorizontal(10)
                                .ShowOnce()
                                .MinWidth(20F)
                                .MinHeight(30F)
                                .AlignLeft()
                                .AlignMiddle();
                        }

                        IContainer OrderIdDetailTopStyle(IContainer container)
                        {
                            return container
                                .Border(1F)
                                .BorderColor(Colors.Grey.Lighten4)
                                .PaddingHorizontal(10)
                                .ShowOnce()
                                .MinWidth(20F)
                                .MinHeight(30F)
                                .AlignLeft()
                                .AlignMiddle();
                        }

                        void AddLine(IContainer container)
                        {
                            container
                                .PaddingBottom(7)
                                .PaddingTop(5)
                                .LineHorizontal(0.5F)
                                .LineColor(Colors.Grey.Lighten3);
                        }
                        #endregion
                    });

                page.Footer()
                    .AlignCenter()
                    .Text(x =>
                    {
                        x.Span("Page");
                        x.CurrentPageNumber();
                    });
            });
        })
        .GeneratePdf(memoryStream);

        return memoryStream.ToArray();
    }

    /// <summary>
    /// 团房入住单pdf-英文
    /// </summary>
    /// <returns></returns>
    private byte[] GenerateGroupBookingOrderPdfByEn(ExportGroupBookingOrderPdfInput detail)
    {
        using var memoryStream = new MemoryStream();
        Document.Create(container =>
        {
            container.Page(page =>
            {
                var fontColor = Color.FromHex("#333333");
                page.Size(PageSizes.A4);
                page.MarginLeft(24);
                page.MarginRight(24);
                page.PageColor(Colors.White);
                page.DefaultTextStyle(x => x.FontSize(10F).FontFamily("msyh"));

                page.Content()
                    .Padding(10)
                    .Column(column =>
                    {
                        var subHeadStyle = TextStyle
                            .Default
                            .FontSize(11)
                            .ExtraBold()
                            .FontColor(fontColor);

                        var diamondStyle = TextStyle
                            .Default
                            .FontSize(11)
                            .FontColor(Colors.LightBlue.Medium);

                        var reminderStyle = TextStyle
                            .Default
                            .FontSize(9)
                            .FontColor(fontColor);

                        var orderHeadStyle = TextStyle
                            .Default
                            .FontSize(10)
                            .FontColor(Color.FromRGB(90, 90, 90));

                        var orderDetailStyle = TextStyle
                            .Default
                            .FontSize(9)
                            .FontColor(fontColor);


                        column.Item()
                            .Element(HeadingStyle)
                            .PaddingTop(10)
                            .PaddingBottom(10)
                            .Text(detail.Title)
                            .FontSize(15)
                            .SemiBold();

                        #region 团房订单号-表头
                        column.Item()
                            .PaddingBottom(10)
                            .Table(table =>
                            {
                                if (detail.ExportHotelOrderType == ExportHotelOrderType.CheckIn)
                                {
                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn();
                                    });
                                    table.Header(header =>
                                    {
                                        table.Cell().Row(1).Column(1).Element(OrderIdTopStyle).Text($"Application Order Number: {detail.GroupBookingApplicationFormId}").Style(orderHeadStyle);
                                    });
                                }
                                else
                                {
                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn();
                                        columns.RelativeColumn();
                                    });
                                    table.Header(header =>
                                    {
                                        table.Cell().Row(1).Column(1).Element(OrderIdTopStyle).Text("Total Cost").Style(orderHeadStyle);
                                        table.Cell().Row(1).Column(2).Element(OrderIdTopStyle).Text("Reference Number").Style(orderHeadStyle);
                                    });
                                    table.Cell().Row(2).Column(1).Element(OrderIdDetailTopStyle).Text($"{detail.TotalAmount} {detail.PaymentCurrencyCode}").Style(orderDetailStyle);
                                    table.Cell().Row(2).Column(2).Element(OrderIdDetailTopStyle).Text(detail.GroupBookingOrderId).Style(orderDetailStyle);
                                }
                            });
                        #endregion

                        //团房订单详情
                        foreach (var hotel in detail.Hotels)
                        {
                            #region 酒店信息

                            column.Item()
                                .Text(text =>
                                {
                                    text.Span("◆").Style(diamondStyle);
                                    text.Span("Hotel Information）").Style(subHeadStyle);
                                });

                            AddLine(column.Item());

                            column.Item()
                                .PaddingLeft(5)
                                .PaddingBottom(10)
                                .Table(table =>
                                {
                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn();
                                        columns.RelativeColumn();
                                        columns.RelativeColumn();
                                    });

                                    table.Cell().Row(1).Column(1).Element(HotelInfoLabelStyle).Text("Hotel Name :");
                                    table.Cell().Row(1).Column(2).Element(HotelInfoStyle).Text(hotel.HotelEnName);
                                    table.Cell().Row(2).Column(1).Element(HotelInfoLabelStyle).Text("Telephone :");
                                    table.Cell().Row(2).Column(2).Element(HotelInfoStyle).Text(hotel.HotelTelePhone ?? "-");
                                    table.Cell().Row(3).Column(1).Element(HotelInfoLabelStyle).Text("Address :");
                                    table.Cell().Row(3).Column(2).Element(HotelInfoStyle).Text(hotel.HotelAddress ?? "-");

                                });
                            #endregion

                            #region 订单信息
                            column.Item()
                                .PaddingTop(5)
                                .Text(text =>
                                {
                                    text.Span("◆").Style(diamondStyle);
                                    text.Span("Order Information").Style(subHeadStyle);
                                });
                            AddLine(column.Item());
                            //入住单
                            column.Item()
                                .PaddingBottom(10)
                                .Table(table =>
                                {

                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn();

                                        columns.ConstantColumn(130);
                                        columns.ConstantColumn(40);
                                        columns.ConstantColumn(50);
                                        columns.ConstantColumn(130);
                                        columns.ConstantColumn(100);

                                    });

                                    table.Header(header =>
                                    {
                                        table.Cell().Row(2).Column(1).Element(OrderHeadBottonStyle).Text("Travel Date").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(2).Element(OrderHeadBottonStyle).Text("room type").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(3).Element(OrderHeadBottonStyle).Text("room").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(4).Element(OrderHeadBottonStyle).Text("breakfast").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(5).Element(OrderHeadBottonStyle).Text("guests").Style(orderHeadStyle);
                                        table.Cell().Row(2).Column(6).Element(OrderHeadBottonStyle).Text("confirmation code").Style(orderHeadStyle);
                                    });

                                    uint rowIndex = 3;
                                    var preCheckInDate = string.Empty;
                                    foreach (var orderItemsGroup in hotel.OrderItems.GroupBy(x => new { x.CheckInDate, x.CheckOutDate }))
                                    {
                                        var checkInDateStr = $"{orderItemsGroup.Key.CheckInDate.ToString("yyyy-MM-dd")}\r\n{orderItemsGroup.Key.CheckOutDate.ToString("yyyy-MM-dd")}";
                                        table.Cell().RowSpan((uint)orderItemsGroup.Count()).Element(OrderDetailStyle).Text(checkInDateStr).Style(orderDetailStyle);
                                        foreach (var orderDetail in orderItemsGroup)
                                        {
                                            var guestNames = string.Join("；", orderDetail.HotelOrderGuests.Select(x =>
                                            {
                                                if (x.HotelOrderGuestType == HotelOrderGuestType.Child)
                                                    return $"{x.GuestName}({x.Age.Value}year)";
                                                return x.GuestName;
                                            }));
                                            table.Cell().Row(rowIndex).Column(2).Element(OrderDetailStyle).Text(orderDetail.HotelRoomEnName).Style(orderDetailStyle);
                                            table.Cell().Row(rowIndex).Column(3).Element(OrderDetailStyle).Text(orderDetail.PriceStrategyRoomsCount).Style(orderDetailStyle);
                                            table.Cell().Row(rowIndex).Column(4).Element(OrderDetailStyle).Text(orderDetail.PriceStrategyNumberOfBreakfast).Style(orderDetailStyle);
                                            table.Cell().Row(rowIndex).Column(5).Element(OrderDetailStyle).Text(guestNames).Style(orderDetailStyle);
                                            table.Cell().Row(rowIndex).Column(6).Element(OrderDetailStyle).Text(orderDetail.ConfirmCode).Style(orderDetailStyle);

                                            rowIndex++;
                                        }
                                    }
                                });

                            #endregion
                        }

                        //订单备注
                        column.Item()
                            .PaddingTop(5)
                            .Text(text =>
                            {
                                text.Span("◆").Style(diamondStyle);
                                text.Span("Remark").Style(subHeadStyle);
                            });
                        AddLine(column.Item());

                        column.Item().PaddingBottom(10).Element(HotelInfoStyle)
                            .Text(string.IsNullOrEmpty(detail.Message) ? "-" : detail.Message);

                        #region 温馨提示
                        column.Item()
                            .Text(text =>
                            {
                                text.Span("◆").Style(diamondStyle);
                                text.Span("Reminder").Style(subHeadStyle);
                            });
                        AddLine(column.Item());

                        var firstStr = "1. Upon your arrival please provide valid government-issued ID to the hotel front desk to " +
                            "locate the accurate booking.";
                        var secondStr = "2. Please tell front desk agent your preferred bed type if your booking comes with more than one (e.g. Double or Twin). " +
                            "The final arrangement is fully subject to hotel’savailability.";
                        var thridStr = "3. All special requests are not guaranteed. Please confirm your " +
                            "special requests with front desk upon arrival.";
                        var fourStr = "4. Please contact your hotel in advance should you need a late check-in after 9pm on your check-in date, or your booking might be canceled by hotel " +
                            "(with no refund).";
                        var fiveStr = "5. Please be noted some hotels charge extra breakfast fee for children even when your room offers breakfast. " +
                            "The actual situation is subject to the hotel regulations.";
                        var sixStr = "6. No-show/Change/Cancellation are NOT refundable.";

                        column.Item().PaddingBottom(10).Text(firstStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(secondStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(thridStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(fourStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(fiveStr).Style(reminderStyle);
                        column.Item().PaddingBottom(10).Text(sixStr).Style(reminderStyle);
                        #endregion

                        if (detail.ImpressionImage != null && detail.ImpressionImage.Length > 0 
                            && detail.ExportHotelOrderType == ExportHotelOrderType.Confirm)
                        {
                            column.Item().PaddingTop(10).TranslateX(340).TranslateY(0)
                                .Width(150, QuestPDF.Infrastructure.Unit.Point)
                                .AlignCenter()
                                .Image(detail.ImpressionImage)
                                .FitArea()
                                .WithCompressionQuality(ImageCompressionQuality.High);
                        }

                        #region 自定义样式
                        IContainer HeadingStyle(IContainer container)
                        {
                            return container
                                .AlignCenter()
                                .PaddingBottom(10);
                        }

                        IContainer LabelStyle(IContainer container)
                        {
                            return container
                                .AlignCenter()
                                .PaddingBottom(10);
                        }

                        IContainer HotelInfoLabelStyle(IContainer container)
                        {
                            return container
                                .Width(160F)
                                .Height(24F)
                                .DefaultTextStyle(style => style.FontSize(10).FontColor(fontColor))
                                .AlignLeft()
                                .AlignMiddle();
                        }

                        IContainer HotelInfoStyle(IContainer container)
                        {
                            return container
                                .DefaultTextStyle(style => style.FontSize(10).FontColor(fontColor))
                                .AlignLeft()
                                .AlignMiddle();
                        }

                        IContainer OrderHeadTopStyle(IContainer container)
                        {
                            return container
                                .BorderLeft(0.5F)
                                .BorderRight(0.5F)
                                .BorderTop(0.5F)
                                .BorderColor(Color.FromRGB(242, 243, 247))
                                .Background(Color.FromRGB(242, 243, 247))
                                .ShowOnce()
                                .MinWidth(20F)
                                .MinHeight(17F)
                                .AlignCenter()
                                .AlignBottom();
                        }

                        IContainer OrderHeadBottonStyle(IContainer container)
                        {
                            return container
                                .BorderLeft(0.5F)
                                .BorderRight(0.5F)
                                .BorderBottom(0.5F)
                                .BorderTop(0.5F)
                                .BorderColor(Color.FromRGB(242, 243, 247))
                                .Background(Color.FromRGB(242, 243, 247))
                                .ShowOnce()
                                .MinHeight(20F)
                                .AlignCenter()
                                .AlignMiddle();
                        }

                        IContainer OrderDetailStyle(IContainer container)
                        {
                            return container
                                .Border(1F)
                                .BorderColor(Colors.Grey.Lighten4)
                                .ShowOnce()
                                .MinWidth(20F)
                                .MinHeight(40F)
                                .AlignCenter()
                                .AlignMiddle();
                        }

                        IContainer OrderIdTopStyle(IContainer container)
                        {
                            return container
                                .Border(1F)
                                .BorderColor(Color.FromRGB(242, 243, 247))
                                .Background(Color.FromRGB(242, 243, 247))
                                .PaddingHorizontal(10)
                                .ShowOnce()
                                .MinWidth(20F)
                                .MinHeight(30F)
                                .AlignLeft()
                                .AlignMiddle();
                        }

                        IContainer OrderIdDetailTopStyle(IContainer container)
                        {
                            return container
                                .Border(1F)
                                .BorderColor(Colors.Grey.Lighten4)
                                .PaddingHorizontal(10)
                                .ShowOnce()
                                .MinWidth(20F)
                                .MinHeight(30F)
                                .AlignLeft()
                                .AlignMiddle();
                        }

                        void AddLine(IContainer container)
                        {
                            container
                                .PaddingBottom(7)
                                .PaddingTop(5)
                                .LineHorizontal(0.5F)
                                .LineColor(Colors.Grey.Lighten3);
                        }
                        #endregion
                    });

                page.Footer()
                    .AlignCenter()
                    .Text(x =>
                    {
                        x.Span("Page");
                        x.CurrentPageNumber();
                    });
            });
        })
        .GeneratePdf(memoryStream);

        return memoryStream.ToArray();
    }

    private string GetBoardCodeStr(BoardCodeType? boardCodeType,int? boardCount,string language)
    {
        var describe = string.Empty;
        if (!boardCodeType.HasValue)
            return describe;
        var boardCodeTypeDescribes = new List<BoardCodeDescribe>() {
            new BoardCodeDescribe(BoardCodeType.RO,"不含早","No Breakfast"),
            new BoardCodeDescribe(BoardCodeType.BB,"早餐","Breakfast Only"),
            new BoardCodeDescribe(BoardCodeType.HB,"半餐（早餐 + 晚餐）","Half Board(Breakfast +Dinner)"),
            new BoardCodeDescribe(BoardCodeType.FB,"全餐（早餐 + 午餐 + 晚餐）","Full Board(Breakfast +Lunch + Dinner)"),
            new BoardCodeDescribe(BoardCodeType.BL,"午餐","Lunch Only"),
            new BoardCodeDescribe(BoardCodeType.BD,"晚餐","Dinner Only"),
            new BoardCodeDescribe(BoardCodeType.HBBL,"早餐 + 午餐","Breakfast + Lunch"),
            new BoardCodeDescribe(BoardCodeType.HBLD,"午餐 + 晚餐","Lunch + Dinner"),
            new BoardCodeDescribe(BoardCodeType.AI,"全包","All Inclusive"),
        };
        var boardCodeTypeDescribe = boardCodeTypeDescribes.FirstOrDefault(x => x.BoardCodeType == boardCodeType);
        if (boardCodeTypeDescribe == null)
            return describe;
        describe = language == "zh" ? boardCodeTypeDescribe.ZHName : boardCodeTypeDescribe.ENName;
        if(boardCount.HasValue)
            describe += $" X{boardCount}";

        return describe;
    }

    record BoardCodeDescribe(BoardCodeType BoardCodeType, string ZHName,string ENName);
    #endregion
}
