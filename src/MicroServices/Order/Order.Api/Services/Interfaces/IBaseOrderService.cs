using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.Messages;
using Contracts.Common.Tenant.DTOs.AgencyRecencyOrder;
using EfCoreExtensions.Abstract;

namespace Order.Api.Services.Interfaces
{
    public interface IBaseOrderService
    {
        /// <summary>
        /// 获取订单支付简讯
        /// </summary>
        /// <param name="orderId">BaseOrderId</param>
        /// <returns></returns>
        Task<PaymentInfoOutput> GetPaymentInfo(long orderId);

        /// <summary>
        /// 用户订单列表
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PagingModel<SearchOrderByUserOutput>> SearchOrderByUser(long userId, SearchOrderByUserInput input);

        /// <summary>
        /// 获取操作日志
        /// </summary>
        /// <param name="orderId"></param>
        /// <returns></returns>
        Task<IEnumerable<OrderLogsOutput>> OperatingRecords(long orderId);

        /// <summary>
        /// 取消订单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task Cancel(CancelOrderInput input);

        /// <summary>
        /// 查询用户不同订单状态的订单数量
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<GetBaseOrderStatusCountOutPut>> GetBaseOrderStatusCount(GetBaseOrderStatusCountInput input);

        /// <summary>
        /// 根据订单类型订单号 获取订单价格信息（对账单订单总额、优惠金额）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<IList<OrderAmountInfoOutput>> GetOrderAmountInfos(OrderAmountInfoInput input);

        /// <summary>
        /// 查询用户消费统计分页数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PagingModel<SearchUserConsumptionOutput>> SearchConsumptionStatistic(SearchUserConsumptionPageInput input);

        /// <summary>
        /// 通过用户id查询消费统计
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<IEnumerable<SearchUserConsumptionOutput>> GetConsumptionStatisticByUserIds(List<long> ids);

        /// <summary>
        /// 订阅 - 订单支付成功（不含预约单）
        /// </summary>
        /// <param name="receive"></param>
        /// <returns></returns>
        Task OrderStatusChangeByPaySuccess(OrderStatusChangeByPaySuccessMessage receive);

        /// <summary>
        /// 订阅 - 订单退款结果
        /// </summary>
        /// <param name="receive"></param>
        /// <returns></returns>
        Task RefundResult(RefundResultMessage receive);

        /// <summary>
        /// 检查渠道单信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<CheckChannelOrderOutput> CheckChannelOrder(CheckChannelOrderInput input);

        /// <summary>
        /// 异常单渠道单号检测
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<CheckChannelOrderOutput> CheckChannelOrderAbnormalOrder(CheckChannelOrderInput input);

        /// <summary>
        /// 检查供应商单信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<CheckSupplierOrderOutput> CheckSupplierOrder(CheckSupplierOrderInput input);

        /// <summary>
        /// 获取分销商订单最近购买时间
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<SyncAgencyRecencyOrderOutput>> SearchAgencyOrderRecencyTime(SearchAgencyOrderRecencyTimeInput input);

        /// <summary>
        /// 设置跟单人
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<bool> SetTrackingUserId(SetTrackingUserIdInput input);

        /// <summary>
        /// 更新vcc支付状态
        /// </summary>
        /// <param name="receive"></param>
        /// <returns></returns>
        Task UpdateVccPaymentStatus(UpdateVccPaymentStatusMessage receive);

        /// <summary>
        /// 获取能开票的订单列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PagingModel<CanBeInvoiceDto>> GetListByCanBeInvoice(GetListByCanBeInvoiceInput input);

        /// <summary>
        /// 获取能开票的订单数量
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BeInvoiceCountDto> GetBeInvoiceCountAsync(GetListByCanBeInvoiceInput input);

        Task<bool> AddOrderPaymentCard(CreditCardGuaranteeInput payInput);

        /// <summary>
        /// 获取是否支持开票订单信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<CanBeInvoiceDto>> CanIssueInvoiceList(GetListByCanBeInvoiceInput input);

        Task<bool> SetOrderOpUserId(SetOrderOpUserIdInput input);
    }
}