using AutoMapper;
using Cit.OpenAPI.YouxiaTrip;
using Cit.OpenAPI.YouxiaTrip.Client;
using Cit.OpenAPI.YouxiaTrip.Models.Order;
using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common.Order.DTOs.HotelOrder;
using Contracts.Common.Order.DTOs.MessageNotify;
using Contracts.Common.Order.DTOs.YouxiaTripOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Tenant.DTOs.SupplierApiFields;
using Contracts.Common.Tenant.DTOs.SupplierApiSetting;
using Contracts.Common.Tenant.Enums;
using Contracts.Common.User.Enums;
using Hangfire;
using HangfireClient.Jobs.Order;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Order.Api.Extensions;
using Order.Api.Services.Interfaces;

namespace Order.Api.Services;

public class YouxiaTripOrderService : IYouxiaTripOrderService
{
    private readonly IMapper _mapper;
    private readonly CustomDbContext _dbContext;

    private readonly ILogger<YouxiaTripOrderService> _logger;
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly ServicesAddress _servicesAddress;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IMessageNotifyService _messageNotifyService;
    private readonly IYouxiaTripClientFactory _youxiaTripClientFactory;

    public YouxiaTripOrderService(IMapper mapper,
        CustomDbContext dbContext,
        IBackgroundJobClient backgroundJobClient,
        IOptions<ServicesAddress> servicesAddressOptions,
        IHttpClientFactory httpClientFactory,
        IMessageNotifyService messageNotifyService,
         ILogger<YouxiaTripOrderService> logger,
         IYouxiaTripClientFactory youxiaTripClientFactory)
    {
        _mapper = mapper;
        _dbContext = dbContext;
        _logger = logger;
        _backgroundJobClient = backgroundJobClient;
        _httpClientFactory = httpClientFactory;
        _servicesAddress = servicesAddressOptions.Value;
        _messageNotifyService = messageNotifyService;
        _youxiaTripClientFactory = youxiaTripClientFactory;
    }


    public async Task CreateOrder(HotelSupplierOrderCreateMessage input)
    {
        var baseOrder = await _dbContext.BaseOrders.IgnoreQueryFilters()
                         .FirstOrDefaultAsync(x => x.Id == input.BaseOrderId && x.TenantId == input.TenantId);
        var hotelOrder = await _dbContext.HotelOrders.IgnoreQueryFilters()
                       .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId && x.TenantId == input.TenantId);

        if (hotelOrder.SupplierApiType != Contracts.Common.Tenant.Enums.SupplierApiType.Youxia)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        var orderPayCard = await _dbContext.OrderPaymentCard.IgnoreQueryFilters()
                        .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        var guests = await _dbContext.HotelOrderGuests.IgnoreQueryFilters()
                    .Where(x => x.BaseOrderId == input.BaseOrderId)
                    .ToListAsync();

        var gdsHotelOrder = await _dbContext.GDSHotelOrder.IgnoreQueryFilters()
                        .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);

        var hotelApiOrder = await _dbContext.HotelApiOrders.IgnoreQueryFilters()
                       .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);

        //api订单状态检查
        if (hotelApiOrder.Status != Contracts.Common.Resource.Enums.SupplierApiOrderStatus.WaitForOrder)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }
        var youxiaConfig = await GetConfig(hotelOrder.PriceStrategySupplierId);
        var client = _youxiaTripClientFactory.Create(youxiaConfig);
        // 格式：+8613888888888
        var phone = baseOrder.ContactsPhoneNumber.Replace("[", "").Replace("]", "");
        var adults = guests.Where(x => x.HotelOrderGuestType == HotelOrderGuestType.Adult).ToList();
        var childs = guests.Where(x => x.HotelOrderGuestType == HotelOrderGuestType.Child).ToList();
        try
        {
            var roomNum = hotelOrder.PriceStrategyRoomsCount;
            var adultCount = adults.Count();
            var childCount = childs.Count();
            var peoples = new List<PeopleInfo>();

            // 验证成人数量是否满足基本要求
            if (adultCount < roomNum)
                throw new BusinessException("成人数量不足，每个房间至少需要1个成人");
            if (adultCount > roomNum * 2)
                throw new BusinessException("成人数量超过限制，每个房间最多2个成人");

            // 计算每个房间初始分配1个成人后剩余的成人数量
            int remainingAdults = adultCount - roomNum;
            int[] adultsPerRoom = new int[roomNum]; // 记录每个房间最终分配的成人数量

            // 先为每个房间分配1个成人，再分配剩余成人（每个房间最多再分1个）
            for (int i = 0; i < remainingAdults; i++)
            {
                adultsPerRoom[i] = 2; // 前remainingAdults个房间分配2个成人
            }
            for (int i = remainingAdults; i < roomNum; i++)
            {
                adultsPerRoom[i] = 1; // 剩余房间分配1个成人
            }

            // 平均分配儿童到房间
            int[] childrenPerRoom = new int[roomNum];
            for (int i = 0; i < childCount; i++)
            {
                childrenPerRoom[i % roomNum]++;
            }

            int adultIndex = 0;
            int childIndex = 0;

            // 按房间分配成人和儿童
            for (var room = 0; room < roomNum; room++)
            {
                int adultCountInRoom = adultsPerRoom[room];
                var adult1 = adults[adultIndex++];

                // 创建主成人信息
                var people = new PeopleInfo
                {
                    Firstname = adult1.FirstName,
                    Lastname = adult1.LastName,
                    Sex = GetGender(adult1.Gender),
                    Phone = phone,
                    Children = new List<ChildrenInfo>(),
                };

                // 若有第二个成人，设置共同入住信息
                if (adultCountInRoom == 2 && adultIndex < adultCount)
                {
                    var adult2 = adults[adultIndex++];
                    people.TogetherFirstname = adult2.FirstName;
                    people.TogetherLastname = adult2.LastName;
                }

                // 分配儿童到当前房间
                int childrenToAdd = childrenPerRoom[room];
                for (int j = 0; j < childrenToAdd && childIndex < childCount; j++)
                {
                    var child = childs[childIndex++];
                    people.Children.Add(new ChildrenInfo
                    {
                        Age = child.Age ?? 0,
                        Gender = GetGender(child.Gender)
                    });
                }

                // 添加特殊请求
                if (!string.IsNullOrEmpty(baseOrder.Message))
                {
                    people.SpecialRequest = new List<string> { baseOrder.Message };
                }

                peoples.Add(people);
            }

            var card = new CreditCardInfo()
            {
                CardNo = orderPayCard.CardNumber,
                CardType = orderPayCard.CardCode,
                CardValidDate = new DateTime(orderPayCard.ExpiryYear, orderPayCard.ExpiryMonth, 1).ToString("yyyy-MM"),
            };
            long.TryParse(gdsHotelOrder.RateKey, out long detailId);
            var request = new CreateHotelOrderRequest
            {
                RoomNum = hotelOrder.PriceStrategyRoomsCount,
                CheckinTime = hotelOrder.CheckInDate.ToString("yyyy-MM-dd"),
                CheckoutTime = hotelOrder.CheckOutDate.ToString("yyyy-MM-dd"),
                AdultNum = adultCount,
                PayType = Cit.OpenAPI.YouxiaTrip.Models.Enums.PayType.CreditCard,
                Peoples = peoples,
                PayExtra = card,
                RateId = gdsHotelOrder.BookingKey,
                DetailId = detailId
            };
            var apiRes = await client.CreateOrder(request);
            if (apiRes.Code == 0)
            {
                baseOrder.Status = BaseOrderStatus.UnFinished;
                hotelOrder.Status = HotelOrderStatus.WaitingForConfirm;
                hotelOrder.SupplierOrderId = apiRes?.Data?.OrderId;
                hotelApiOrder.SupplierOrderId = apiRes?.Data?.OrderId;
                hotelApiOrder.Status = SupplierApiOrderStatus.WaitForConfirm;
                hotelApiOrder.UpdateTime = DateTime.Now;
                hotelOrder.UpdateTime = DateTime.Now;
                baseOrder.UpdateTime = DateTime.Now;

                _backgroundJobClient
                    .Schedule<HangfireClient.Jobs.Order.IHotelOrderJob>(s =>
                            s.GDSAutoConfirmed(hotelOrder.Id, baseOrder.TenantId, (int)hotelOrder.SupplierApiType),
                            TimeSpan.FromSeconds(30)
                        );
            }
            else
            {
                hotelApiOrder.Message = apiRes.Msg;
                hotelApiOrder.Status = SupplierApiOrderStatus.OrderError;
                hotelApiOrder.UpdateTime = DateTime.Now;
            }
        }
        catch (Exception ex)
        {
            hotelApiOrder.Message = ex.Message;
            hotelApiOrder.Status = SupplierApiOrderStatus.OrderError;
            hotelApiOrder.UpdateTime = DateTime.Now;
        }
        await _dbContext.SaveChangesAsync();
    }

    public async Task GetBookingConfirmed(GDSGetBookingMessage input)
    {
        var hotelOrder = await _dbContext.HotelOrders.IgnoreQueryFilters()
                     .FirstOrDefaultAsync(x => x.Id == input.HotelOrderId);

        if (hotelOrder.SupplierApiType != Contracts.Common.Tenant.Enums.SupplierApiType.Youxia)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }
        var baseOrder = await _dbContext.BaseOrders.IgnoreQueryFilters()
                .FirstOrDefaultAsync(x => x.Id == hotelOrder.BaseOrderId);
        var hotelApiOrder = await _dbContext.HotelApiOrders.IgnoreQueryFilters()
                       .FirstOrDefaultAsync(x => x.BaseOrderId == hotelOrder.BaseOrderId);

        var youxiaConfig = await GetConfig(hotelOrder.PriceStrategySupplierId);
        var client = _youxiaTripClientFactory.Create(youxiaConfig);
        var requst = new GetHotelOrderDetailRequest
        {
            Id = hotelApiOrder.SupplierOrderId,
        };
        var response = await client.GetHotelOrderDetail(requst);
        if (response.Code == 0)
        {
            var orderInfo = response.Data.OrderInfo;
            var guests = await _dbContext.HotelOrderGuests.IgnoreQueryFilters()
                               .Where(x => x.HotelOrderId == hotelOrder.Id)
                               .Select(x => x.GuestName)
                               .ToListAsync();
            // 预定提交：1 取消提交：2 预定成功：5 待客服取消：6 客服已取消：7 下单中：8 请求取消：9 违规取消：10 交易完成：100
            switch (orderInfo.Status)
            {
                case "5":
                    // 判断是否已确认，因为是回调通知，防止重复确认
                    if (hotelApiOrder.Status == SupplierApiOrderStatus.WaitForConfirm)
                    {
                        hotelOrder.ConfirmCode = orderInfo.ConfirmNum;
                        hotelOrder.Status = HotelOrderStatus.WaitingForCheckIn;
                        hotelApiOrder.Status = SupplierApiOrderStatus.Confirmed;
                        hotelOrder.ConfirmTime = DateTime.Now;
                        hotelApiOrder.UpdateTime = DateTime.Now;
                        hotelOrder.UpdateTime = DateTime.Now;
                        OrderNotifyDto<HotelOrderConfirmNotifyDto> orderNotifyDto = new()
                        {
                            BaseOrder = _mapper.Map<BaseOrderNotify>(baseOrder),
                            NotifyDto = new HotelOrderConfirmNotifyDto
                            {
                                HotelName = baseOrder.ResourceName,
                                HotelRoomName = baseOrder.ProductName,
                                SkuName = baseOrder.ProductSkuName,
                                CheckInDate = hotelOrder.CheckInDate,
                                CheckOutDate = hotelOrder.CheckOutDate,
                                NightsCount = hotelOrder.PriceStrategyNightsCount,
                                NumberOfBreakfast = hotelOrder.PriceStrategyNumberOfBreakfast,
                                RoomsCount = hotelOrder.PriceStrategyRoomsCount,
                                Guests = guests.ToArray()!,
                                IsGroupBooking = hotelOrder.IsGroupBooking,
                                ConfirmCode = hotelOrder.ConfirmCode,
                                BoardCount = hotelOrder.BoardCount,
                                BoardCodeType = hotelOrder.BoardCodeType
                            }
                        };
                        await _messageNotifyService.HotelOrderConfirmNotify(orderNotifyDto);

                        //入住日22:00后自动变为已入住
                        var checkInTime = hotelOrder.CheckInDate.Date.AddHours(22);
                        var ts = checkInTime.Subtract(DateTime.Now);
                        _backgroundJobClient?.Schedule<IHotelOrderJob>(x => x.AutoCheckIn(hotelOrder.Id, hotelOrder.TenantId), ts);
                    }
                    break;
                case "2":// 还没预订成功就取消，1-》2
                case "7":// 预订成功后取消，5 -》6||10 -》7
                    // 不是我们系统发起的，供应商主动取消通知
                    if (hotelOrder.Status != HotelOrderStatus.Refunded
                         && hotelOrder.Status != HotelOrderStatus.Closed
                          && hotelOrder.Status != HotelOrderStatus.Finished
                        )
                    {
                        await RefundByJob(new RefundByJobInput()
                        {
                            OrderId = baseOrder.Id,
                            RefundAmount = baseOrder.PaymentAmount,
                            Message = "供应商主动退款",
                            IsPassive = true,
                            OperationUser = new Contracts.Common.Order.DTOs.OperationUserDto
                            {
                                UserId = 0,
                                Name = "System",
                                UserType = Contracts.Common.Order.Enums.UserType.Merchant
                            },
                        }, baseOrder.TenantId);
                    }
                    break;
                case "1": // 状态变更会主动调用此接口，所以不做处理
                    //_backgroundJobClient
                    //   .Schedule<HangfireClient.Jobs.Order.IHotelOrderJob>(s =>
                    //           s.GDSAutoConfirmed(hotelOrder.Id, baseOrder.TenantId, (int)hotelOrder.SupplierApiType),
                    //           TimeSpan.FromMinutes(1)
                    //       );
                    break;
            }
        }
        await _dbContext.SaveChangesAsync();
    }

    public async Task<bool> CancelBooking(GDSCancelBookingMessage input)
    {
        var hotelOrder = await _dbContext.HotelOrders.IgnoreQueryFilters()
                     .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);

        if (hotelOrder.SupplierApiType != Contracts.Common.Tenant.Enums.SupplierApiType.Youxia)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }
        var hotelApiOrder = await _dbContext.HotelApiOrders.IgnoreQueryFilters()
                       .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);

        //api订单状态检查
        var status = new List<SupplierApiOrderStatus>() {
            SupplierApiOrderStatus.WaitForConfirm,
            SupplierApiOrderStatus.Confirmed,
        };
        if (!status.Contains(hotelApiOrder.Status))
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        var youxiaConfig = await GetConfig(hotelOrder.PriceStrategySupplierId);
        var client = _youxiaTripClientFactory.Create(youxiaConfig);

        bool success = false;
        var requst = new CancelHotelOrderRequest()
        {
            Id = hotelApiOrder.SupplierOrderId,
        };
        var response = await client.CancelOrder(requst);

        if (response.Code == 0)
        {
            // 1 预定提交，2 取消提交，5 预定成功，6 待客服取消，7 客服已取消，100 交易完成，8 下单中，9 请求取消，10 违规取消
            // 因为游虾那边说了，只要提交成功，待客服取消状态也是算成功了的，后续是他们那边问题，不存在 待客服取消 状态不取消成功的情况
            // 所以我们只要判断到 待客服取消 状态也是算成功的
            switch (response.Data.Status)
            {
                case Cit.OpenAPI.YouxiaTrip.Models.Enums.HotelOrderStatus.Customer_WaitCancel:
                case Cit.OpenAPI.YouxiaTrip.Models.Enums.HotelOrderStatus.Customer_Cancel:
                case Cit.OpenAPI.YouxiaTrip.Models.Enums.HotelOrderStatus.ViolationCancel:
                case Cit.OpenAPI.YouxiaTrip.Models.Enums.HotelOrderStatus.RequestCancel:
                case Cit.OpenAPI.YouxiaTrip.Models.Enums.HotelOrderStatus.Reserve_Cancel:
                    success = true;
                    break;
            }
        }
        return success;
    }

    #region private

    private string GetGender(UserGender? gender)
    {
        return gender switch
        {
            UserGender.Female => "女",
            _ => "男"
        };
    }

    private async Task<YouxiaTripConfig> GetConfig(long supplierId)
    {
        var supplier = (await TenantSupplierGetByIds(new List<long>() { supplierId })).FirstOrDefault();
        var gdsConfig = FieldToConfig(supplier?.SupplierApiSetting);
        return gdsConfig;
    }

    private async Task<IEnumerable<Contracts.Common.Tenant.DTOs.Supplier.GetSupplierOutput>> TenantSupplierGetByIds(List<long> supplierIds)
    {
        var httpContent = new StringContent(JsonConvert.SerializeObject(supplierIds),
            Encoding.UTF8, "application/json");
        var response = await _httpClientFactory.InternalPostAsync<IEnumerable<Contracts.Common.Tenant.DTOs.Supplier.GetSupplierOutput>>(
            requestUri: _servicesAddress.Tenant_Supplier_GetByIds(),
            httpContent: httpContent);
        return response;
    }

    private async Task RefundByJob(RefundByJobInput input, long tenantId)
    {
        var headers = new List<KeyValuePair<string, string>>(1) {
                new("tenant", tenantId.ToString())
            };
        var httpContent = new StringContent(JsonConvert.SerializeObject(input),
            Encoding.UTF8, "application/json");
        await _httpClientFactory.InternalPostAsync(
            requestUri: _servicesAddress.Order_RefundByJob(),
            httpContent: httpContent,
            headers: headers);
    }

    private YouxiaTripConfig FieldToConfig(SupplierApiSettingDto supplierApiSetting)
    {
        var config = new YouxiaTripConfig();
        config.AppKey = supplierApiSetting?.SupplierApiSettingFields?
                   .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.YouXiaCode.YouXiaKey)?.FieldValue;
        config.AppSecert = supplierApiSetting?.SupplierApiSettingFields?
                  .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.YouXiaCode.YouXiaSecret)?.FieldValue;
        config.Host = supplierApiSetting?.SupplierApiSettingFields?
                  .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.YouXiaCode.YouXiaHost)?.FieldValue;
        return config;
    }
    #endregion

    public async Task OrderStatusChange(OrderStatusChangeInput input)
    {
        var hotelOrder = await _dbContext.HotelOrders.IgnoreQueryFilters()
                    .Where(x => x.SupplierOrderId == input.SupplierOrderId)
                    .Where(x => x.SupplierApiType == SupplierApiType.Youxia)
                    .FirstOrDefaultAsync();
        if (hotelOrder == null)
        {
            _logger.LogInformation("OrderStatusChange not found:{@input}", input);
            return;
        }
        await GetBookingConfirmed(new GDSGetBookingMessage()
        {
            HotelOrderId = hotelOrder.Id,
            SupplierApiType = SupplierApiType.Youxia
        });
    }

    public async Task OrderPriceChange(OrderPriceChangeInput input)
    {
        var hotelOrder = await _dbContext.HotelOrders.IgnoreQueryFilters()
                     .Where(x => x.SupplierOrderId == input.SupplierOrderId)
                    .Where(x => x.SupplierApiType == SupplierApiType.Youxia)
                    .FirstOrDefaultAsync();
        if (hotelOrder == null)
        {
            _logger.LogInformation("OrderPriceChange not found:{@input}", input);
            return;
        }
        var calendarPrice = await _dbContext.HotelOrderCalendarPrices.IgnoreQueryFilters()
                   .FirstOrDefaultAsync(x => x.HotelOrderId == hotelOrder.Id);
        // 价格变高需要提交工单，要不24小时自动取消，目前系统不接受变高，所以不处理留自动取消，只做记录
        var record = new OrderPriceChangeRecord()
        {
            NewAmount = input.NewAmount,
            OldAmount = input.OldAmount,
            Amount = input.Amount,
            BaseOrderId = hotelOrder.BaseOrderId,
            Rate = input.Rate,
            SupplierApiType = hotelOrder.SupplierApiType,
            SupplierOrderId = input.SupplierOrderId,
            CurrencyCode = calendarPrice.CostCurrencyCode,
            CreateTime = DateTime.Now,
        };
        record.SetTenantId(hotelOrder.TenantId);
        await _dbContext.AddAsync(record);
        await _dbContext.SaveChangesAsync();
    }
}
