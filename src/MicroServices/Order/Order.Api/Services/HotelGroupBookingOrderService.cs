using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common.Notify.DTOs.StaffNotify;
using Contracts.Common.Notify.Enums;
using Contracts.Common.Notify.Messages;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.GroupBookingOrder;
using Contracts.Common.Order.DTOs.HotelOrder;
using Contracts.Common.Order.DTOs.MessageNotify;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Tenant.DTOs.Agency;
using Contracts.Common.Tenant.DTOs.B2BWebConfiguration;
using Contracts.Common.Tenant.DTOs.Tenant;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using EnumsNET;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Order.Api.Extensions;
using Order.Api.Services.Interfaces;
using System.Text.RegularExpressions;
using DetailInput = Contracts.Common.Order.DTOs.GroupBookingOrder.DetailInput;
using DetailOutput = Contracts.Common.Order.DTOs.GroupBookingOrder.DetailOutput;
using GroupBookingOrderAdditionOutput = Contracts.Common.Order.DTOs.GroupBookingOrder.GroupBookingOrderAdditionOutput;
using GroupBookingOrderPaymentOutput = Contracts.Common.Order.DTOs.GroupBookingOrder.GroupBookingOrderPaymentOutput;
using SearchInput = Contracts.Common.Order.DTOs.GroupBookingOrder.SearchInput;
using SearchOutput = Contracts.Common.Order.DTOs.GroupBookingOrder.SearchOutput;

namespace Order.Api.Services;

public class HotelGroupBookingOrderService : IHotelGroupBookingOrderService
{
    private readonly CustomDbContext _dbContext;
    private readonly IHotelOrderService _hotelOrderService;
    private readonly ICapPublisher _capPublisher;
    private readonly IHotelGroupBookingService _hotelGroupBookingService;
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly IMessageNotifyService _messageNotifyService;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IOptions<ServicesAddress> _servicesAddress;

    public HotelGroupBookingOrderService(CustomDbContext dbContext,
        IHotelOrderService hotelOrderService,
        ICapPublisher capPublisher,
        IHotelGroupBookingService hotelGroupBookingService,
        IBackgroundJobClient backgroundJobClient,
        IMessageNotifyService messageNotifyService,
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> servicesAddress)
    {
        _dbContext = dbContext;
        _hotelOrderService = hotelOrderService;
        _capPublisher = capPublisher;
        _hotelGroupBookingService = hotelGroupBookingService;
        _backgroundJobClient = backgroundJobClient;
        _messageNotifyService = messageNotifyService;
        _httpClientFactory = httpClientFactory;
        _servicesAddress = servicesAddress;
    }

    [UnitOfWork]
    public async Task<OrderCreateOutput> OrderCreate(OrderCreateInput input)
    {
        var order = input.GroupBookingOrder;
        var userInfo = order.UserInfo;

        var preOrder = await _dbContext.GroupBookingPreOrders
            .Where(x => x.Id == order.GroupBookingPreOrderId && x.Enabled)
            .FirstAsync();
        if (preOrder.OrderStatus != GroupBookingPreOrderStatus.NotCreated)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        preOrder.OrderStatus = GroupBookingPreOrderStatus.Created;
        preOrder.UpdateTime = DateTime.Now;

        GroupBookingOrder groupBookingOrder = new()
        {
            GroupBookingApplicationFormId = order.GroupBookingApplicationFormId,
            GroupBookingPreOrderId = order.GroupBookingPreOrderId,
            SellingChannels = order.SellingChannels,
            SellingPlatform = order.SellingPlatform,
            ChannelOrderNo = order.ChannelOrderNo,
            ContactsName = order.ContactsName,
            ContactsPhoneNumber = order.ContactsPhoneNumber,
            ContactsEmail = order.ContactsEmail,
            GroupNo = order.GroupNo,
            Message = order.Message,
            HotelOrderStatus = HotelOrderStatus.WaitingForPay,
            AgencyId = userInfo.AgencyId,
            AgencyName = userInfo.AgencyName,
            SalespersonId = userInfo.SalespersonId,
            SalespersonName = userInfo.SalespersonName,
            UserId = userInfo.UserId,
            UserNickName = userInfo.NickName,
            VipLevelId = userInfo.VipLevelId,
            VipLevelName = userInfo.VipLevelName,
        };
        List<GroupBookingOrderItem> groupBookingOrderItems = new();
        List<GroupBookingOrderAddition> groupBookingOrderAdditions = new();

        var orderItemDtos = input.OrderItemDtos;
        for (int i = 0; i < orderItemDtos.Length; i++)
        {
            var orderItemDto = orderItemDtos[i];
            var item = orderItemDto.CreateHotelOrderInput;

            if (orderItemDto.Additions?.Length is > 0)
            {
                var addAmount = orderItemDto.Additions.Sum(x => x.Amount * x.Quantity);
                item.TotalAmount += addAmount;
                item.PaymentAmount += addAmount;
            }
            item.AutoCloseTimeoutOrder = false;//不超时自动关闭订单
            var createHotelOrderOutput = await _hotelOrderService.Create(item);
            GroupBookingOrderItem groupBookingOrderItem = new()
            {
                GroupBookingPreOrderItemId = orderItemDto.GroupBookingPreOrderItemId,
                BaseOrderId = createHotelOrderOutput.BaseOrderId,
                HotelOrderId = createHotelOrderOutput.HotelOrderId,
                GroupBookingOrderId = groupBookingOrder.Id,
                HotelId = item.HotelId,
                ResourceHotelId = orderItemDto.ResourceHotelId,
                HotelZHName = orderItemDto.HotelZHName,
                HotelENName = orderItemDto.HotelENName,
                CityName = orderItemDto.CityName,
                CityCode = orderItemDto.CityCode,
                HotelRoomId = item.Room.Id,
                HotelRoomName = item.Room.Name,
                HotelRoomEnName = item.Room.EnName,
                BedType = item.Room.BedTypes?.Count is > 0 ? JsonConvert.SerializeObject(item.Room.BedTypes) : null,
                MaximumOccupancy = item.Room.MaximumOccupancy,
                PriceStrategyId = item.PriceStrategy.Id,
                PriceStrategyName = item.PriceStrategy.Name,
                PriceStrategyEnName = item.PriceStrategy.EnName,
                SupplierApiType = item.SupplierApiType,
                SupplierId = item.PriceStrategy.SupplierId,
                NumberOfBreakfast = item.PriceStrategy.NumberOfBreakfast,
                RoomCount = item.RoomCount,
                CheckInDate = orderItemDto.CheckInDate,
                CheckOutDate = orderItemDto.CheckOutDate,
                HotelOrderStatus = Contracts.Common.Order.Enums.HotelOrderStatus.WaitingForPay,
            };
            groupBookingOrderItems.Add(groupBookingOrderItem);

            var groupBookingOrderAdditionItems = orderItemDto.Additions?.Select(x =>
                new GroupBookingOrderAddition
                {
                    GroupBookingOrderItemId = groupBookingOrderItem.Id,
                    BaseOrderId = groupBookingOrderItem.BaseOrderId,
                    SupplierId = groupBookingOrderItem.SupplierId,
                    GroupBookingOrderId = groupBookingOrder.Id,
                    AdditionName = x.AdditionName,
                    Amount = x.Amount,
                    CurrencyCode = x.CurrencyCode,
                    Cost = x.Cost,
                    CostCurrencyCode = x.CostCurrencyCode,
                    Quantity = x.Quantity,
                    Remark = x.Remark
                })
                .ToList();
            if (groupBookingOrderAdditionItems?.Count is > 0)
                groupBookingOrderAdditions.AddRange(groupBookingOrderAdditionItems);
        }

        var orderItem = groupBookingOrderItems.FirstOrDefault();
        if (orderItem is not null)
        {
            groupBookingOrder.CityCode = orderItem.CityCode;
            groupBookingOrder.CityName = orderItem.CityName;
            groupBookingOrder.HotelENName = orderItem.HotelENName;
            groupBookingOrder.HotelZHName = orderItem.HotelZHName;
            groupBookingOrder.ResourceHotelId = orderItem.ResourceHotelId;
            groupBookingOrder.CheckInDate = orderItem.CheckInDate;
            groupBookingOrder.CheckOutDate = orderItem.CheckOutDate;
        }

        groupBookingOrder.TotalPayment = orderItemDtos.Sum(x => x.CreateHotelOrderInput.PaymentAmount);
        groupBookingOrder.PaymentCurrencyCode = input.DownPayment.PaymentCurrencyCode;

        GroupBookingOrderPayment downPayment = new()
        {
            GroupBookingOrderId = groupBookingOrder.Id,
            LatestPaymentTime = input.DownPayment.LatestPaymentTime,
            PaymentCurrencyCode = input.DownPayment.PaymentCurrencyCode,
            PaymentRatio = input.DownPayment.PaymentRatio,
            PaymentRatioType = PaymentRatioType.DownPayment,
            PaymentAmount = input.DownPayment.InitialPaymentAmount,
            PayStatus = PayStatus.Unpaid,
        };
        GroupBookingOrderPayment finalPayment = new()
        {
            GroupBookingOrderId = groupBookingOrder.Id,
            LatestPaymentTime = input.DownPayment.FinalPaymentTime,
            PaymentCurrencyCode = input.DownPayment.PaymentCurrencyCode,
            PaymentRatio = 100 - downPayment.PaymentRatio,
            PaymentRatioType = PaymentRatioType.FinalPayment,
            PaymentAmount = groupBookingOrder.TotalPayment - downPayment.PaymentAmount,
            PayStatus = PayStatus.Unpaid,
        };

        await _dbContext.GroupBookingOrders.AddAsync(groupBookingOrder);
        await _dbContext.GroupBookingOrderItems.AddRangeAsync(groupBookingOrderItems);
        await _dbContext.GroupBookingOrderAdditions.AddRangeAsync(groupBookingOrderAdditions);
        await _dbContext.GroupBookingOrderPayments.AddAsync(downPayment);
        await _dbContext.GroupBookingOrderPayments.AddAsync(finalPayment);

        return new OrderCreateOutput
        {
            GroupBookingOrderId = groupBookingOrder.Id,
            PaymentId = downPayment.Id,
            LatestPaymentTime = downPayment.LatestPaymentTime,
        };
    }

    public async Task<PaymentInfoOutput> GetPaymentInfo(long orderId)
    {
        var payment = await _dbContext.GroupBookingOrderPayments.AsNoTracking()
            .Where(x => x.Id == orderId)
            .FirstOrDefaultAsync();
        var order = await _dbContext.GroupBookingOrders.AsNoTracking()
            .Where(x => x.Id == payment.GroupBookingOrderId)
            .FirstOrDefaultAsync();
        PaymentInfoOutput output = new()
        {
            SellingChannels = order.SellingChannels,
            SellingPlatform = order.SellingPlatform,
            ResourceName = order.HotelZHName,
            ProductName = order.HotelZHName,
            UserId = order.UserId,
            UserNickName = order.UserNickName,
            AgencyId = order.AgencyId,
            AgencyName = order.AgencyName,
            Amount = payment.PaymentAmount,
            CurrencyCode = payment.PaymentCurrencyCode,
            OrderStatus = payment.PayStatus switch
            {
                PayStatus.Unpaid => OrderStatus.WaitingForPay,
                PayStatus.Processing => OrderStatus.WaitingForPay,
                PayStatus.Paid => OrderStatus.Paid,
                PayStatus.Closed => OrderStatus.Closed,
                _ => OrderStatus.Closed
            },
            ProductBusinessType = Contracts.Common.Marketing.Enums.ProductBusinessType.Hotel,
            OrderType = OrderType.Hotel,
            CreateTime = order.CreateTime,
        };
        //订单已关闭
        if (order.HotelOrderStatus == HotelOrderStatus.Closed)
            output.OrderStatus = OrderStatus.Closed;
        return output;
    }

    [UnitOfWork]
    public async Task OrderStatusChangeByPaySuccess(OrderStatusChangeByPaySuccessMessage receive)
    {
        var payment = await _dbContext.GroupBookingOrderPayments
            .Where(x => x.Id == receive.OrderId)
            .FirstOrDefaultAsync();
        //订单非待支付状态 订单无效支付退款 (如订单关闭、重复支付等场景)
        if (payment.PayStatus != PayStatus.Unpaid && payment.PayStatus != PayStatus.Processing)
        {
            await _capPublisher.PublishDelayAsync(TimeSpan.FromSeconds(10),//延时10秒处理，避免订单状态未更新
            CapTopics.Payment.OrderPaymentUselessRefund, new Contracts.Common.Payment.Messages.OrderPaymentUselessRefundMessage
            {
                OrderPaymentId = receive.OrderPaymentId,
                OrderPaymentType = receive.OrderPaymentType,
                OrderId = receive.OrderId,
            });
            return;
        }
        payment.PayType = receive.PaymentType;
        payment.PayStatus = PayStatus.Paid;
        payment.PaymentTime = DateTime.Now;
        payment.PaymentExternalNo = receive.PaymentExternalNo ?? string.Empty;

        var groupBookingOrderId = payment.GroupBookingOrderId;
        var order = await _dbContext.GroupBookingOrders
            .Where(x => x.Id == groupBookingOrderId)
            .FirstOrDefaultAsync();
        var preOrder = await _dbContext.GroupBookingPreOrders
        .Where(x => x.Id == order.GroupBookingPreOrderId)
        .FirstOrDefaultAsync();
        switch (payment.PaymentRatioType)
        {
            case PaymentRatioType.DownPayment:
                {
                    if (order?.HotelOrderStatus != HotelOrderStatus.WaitingForPay)
                        throw new BusinessException($"The order status does not match,current status：{order.HotelOrderStatus}");
                    order.HotelOrderStatus = HotelOrderStatus.WaitingForConfirm;
                    order.UpdateTime = DateTime.Now;

                    var orderItems = await _dbContext.GroupBookingOrderItems
                        .Where(x => x.GroupBookingOrderId == groupBookingOrderId)
                        .ToListAsync();
                    foreach (var item in orderItems)
                    {
                        item.HotelOrderStatus = HotelOrderStatus.WaitingForConfirm;
                        await _capPublisher.PublishAsync(CapTopics.Order.StatusChangeByPaySuccess,
                            new OrderStatusChangeByPaySuccessMessage
                            {
                                OrderPaymentId = receive.OrderPaymentId,
                                OrderPaymentType = receive.OrderPaymentType,
                                PayTime = receive.PayTime,
                                OrderId = item.BaseOrderId,
                                PaymentChannel = receive.PaymentChannel,
                                PaymentExternalNo = receive.PaymentExternalNo,
                                PaymentMode = receive.PaymentMode,
                                PaymentType = receive.PaymentType,
                            });
                    }
                    preOrder.HasDownPayment = true;

                    if (payment.PaymentRatio == 100)
                    {
                        var finalPayment = await _dbContext.GroupBookingOrderPayments
                            .Where(x => x.GroupBookingOrderId == groupBookingOrderId && x.PaymentRatioType == PaymentRatioType.FinalPayment)
                            .FirstOrDefaultAsync();
                        if (finalPayment.PaymentRatio == 0 && finalPayment?.PaymentAmount == 0)
                        {
                            finalPayment.PayStatus = PayStatus.Paid;
                            finalPayment.PaymentTime = payment.PaymentTime;
                            preOrder.HasFinalPayment = true;
                        }
                    }
                }
                break;
            case PaymentRatioType.FinalPayment:
                {
                    preOrder.HasFinalPayment = true;
                    if (preOrder.InitialPaymentAmount <= 0)
                    {
                        var baseOrderIds = await _dbContext.GroupBookingOrderItems
                            .Where(x => x.GroupBookingOrderId == groupBookingOrderId)
                            .Select(s => s.BaseOrderId)
                            .ToListAsync();
                        var baseOrders = await _dbContext.BaseOrders
                            .Where(x => baseOrderIds.Contains(x.Id))
                            .ToListAsync();
                        foreach (var item in baseOrders)
                        {
                            item.PaymentType = receive.PaymentType;
                            item.PaymentMode = receive.PaymentMode;
                            item.PaymentChannel = receive.PaymentChannel;
                            item.PaymentExternalNo = receive.PaymentExternalNo;
                            if (DateTime.TryParse(receive.PayTime, out var payTime))
                                item.PayTime = payTime;
                        }
                    }
                }
                break;
        }

        #region 团房已付款提醒
        #region 获取首款，尾款金额
        var payments = await _dbContext.GroupBookingOrderPayments
            .Where(x => x.GroupBookingOrderId == groupBookingOrderId)
            .Select(x => new { x.PaymentRatio, x.PaymentAmount, x.PaymentRatioType, })
            .ToListAsync();
        var initialPaymentAmount = payments
            .FirstOrDefault(x => x.PaymentRatioType == PaymentRatioType.DownPayment)?.PaymentAmount ?? 0;
        var finalPaymentAmount = payments
            .FirstOrDefault(x => x.PaymentRatioType == PaymentRatioType.FinalPayment)?.PaymentAmount ?? 0;

        //团房单首项信息
        var groupBookingOrderItem = await _dbContext.GroupBookingOrderItems
            .Where(x => x.GroupBookingOrderId == order.Id)
            .Select(x => new
            {
                x.HotelZHName,
                x.HotelENName,
                x.CheckInDate,
                x.CheckOutDate,
            })
            .FirstOrDefaultAsync();

        #endregion

        var form = await _dbContext.GroupBookingApplicationForms.AsNoTracking()
            .Where(x => x.Id.Equals(order.GroupBookingApplicationFormId))
            .Select(x => new
            {
                x.Id,
                x.TenantId,
                x.AgencyId,
                x.UserType,
                x.OperatorUserId,
                x.AssignorId,
                x.ContactPhone,
                x.ContactEmail
            })
            .FirstOrDefaultAsync();
        var applicationFormDemands = await _hotelGroupBookingService.GetApplicationDemands(form.Id);
        StaffUserDto[] users = Array.Empty<StaffUserDto>();
        if (order.AgencyId is > 0)
        {
            var agencyDetail = await TenantGetAgencyDetail(order.AgencyId);
            //销售BD
            if (agencyDetail.SalespersonId is not null)
                users = users.Append(new StaffUserDto
                {
                    Id = agencyDetail.SalespersonId.Value,
                    Role = SendToTheRole.TenantStaff,
                }).ToArray();
        }
        //运营
        if (form.OperatorUserId.HasValue)
            users = users.Append(new StaffUserDto
            {
                Id = form.OperatorUserId!.Value,
                Role = SendToTheRole.TenantStaff,
            }).ToArray();
        await _messageNotifyService.GroupRoomPaidReminderNotify(new GroupRoomPaidReminderNotifyDto
        {
            Users = users,
            TenantId = order.TenantId,
            SellingPlatform = order.SellingPlatform,
            AgencyId = order.AgencyId,
            AgencyName = order.AgencyName,
            GroupBookingApplicationFormId = order.GroupBookingApplicationFormId,
            InitialPaymentAmount = initialPaymentAmount,
            PaymentRatioType = payment.PaymentRatioType,
            FirstHotelName = groupBookingOrderItem.HotelZHName,
            FinalPaymentAmount = finalPaymentAmount,
            RegionCodes = applicationFormDemands.Select(s => new GroupRoomPaidReminderNotifyRegionDto
            {
                CountryCode = s.CountryCode,
                ProvinceCode = s.ProvinceCode
            }).ToArray()
        });

        var assignorIds = applicationFormDemands.SelectMany(s => s.Hotels)
            .Where(s => s.AssignorId.HasValue)
            .Select(s => s.AssignorId!.Value)
            .Distinct()
            .ToArray();
        if (assignorIds.Any())
        {
            await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess, new NotifyMessageProcessMessage
            {
                SendToTheRole = SendToTheRole.ManagerStaff,
                NotifyEventSubType = NotifyEventSubType.Hotel_GroupRoom_Paid_Reminder,
                NotifyMode = NotifyMode.Wechat,
                TenantId = order.TenantId,
                Variables = new GroupRoomPaidReminderNotifyDto
                {
                    TenantId = order.TenantId,
                    SellingPlatform = order.SellingPlatform,
                    AgencyId = order.AgencyId,
                    AgencyName = order.AgencyName,
                    GroupBookingApplicationFormId = order.GroupBookingApplicationFormId,
                    InitialPaymentAmount = initialPaymentAmount,
                    PaymentRatioType = payment.PaymentRatioType,
                    FirstHotelName = applicationFormDemands.FirstOrDefault()?.Hotels?.FirstOrDefault()?.HotelName,
                    FinalPaymentAmount = finalPaymentAmount,
                    Users = assignorIds.Select(s => new StaffUserDto { Id = s, Role = SendToTheRole.ManagerStaff }).ToArray(),
                }
            });
        }
        await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess, new NotifyMessageProcessMessage
        {
            SendToTheRole = SendToTheRole.ManagerStaff,
            NotifyEventSubType = NotifyEventSubType.Hotel_GroupRoom_Paid_Reminder,
            NotifyMode = NotifyMode.DingTalkRobot,
            TenantId = order.TenantId,
            Variables = new
            {
                GroupBookingApplicationFormId = order.GroupBookingApplicationFormId,
                order.AgencyName,
                payment.PaymentRatioType,
                InitialPaymentAmount = initialPaymentAmount,
                FinalPaymentAmount = finalPaymentAmount,
                StaffUserIds = assignorIds,
                RegionCodes = applicationFormDemands.Select(s => new { CountryCode = s.CountryCode, ProvinceCode = s.ProvinceCode }).ToArray(),
            }
        });

        //尾款支付通知
        if (preOrder.HasFinalPayment)
        {
            var b2bWebConfiguration = await TenantB2bWebConfigurationGet();
            var brandName = b2bWebConfiguration.B2BTitleConfigs.FirstOrDefault(x => x.Language.Equals("zh")).BrandName;
            var brandEnName = b2bWebConfiguration.B2BTitleConfigs.FirstOrDefault(x => x.Language.Equals("en")).BrandName;

            await _messageNotifyService.GroupRoomFinalPaymentNotify(new GroupRoomFinalPaymentNotifyDto
            {
                TenantId = order.TenantId,
                SellingPlatform = order.SellingPlatform,
                HotelName = groupBookingOrderItem.HotelZHName,
                CheckInDate = groupBookingOrderItem.CheckInDate,
                CheckOutDate = groupBookingOrderItem.CheckOutDate,
                Phone = form.ContactPhone,
                BrandName = brandName
            });


            if (string.IsNullOrEmpty(form.ContactEmail) is false)
            {
                await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess, new NotifyMessageProcessMessage
                {
                    SendToTheRole = SendToTheRole.Customer,
                    NotifyChannel = NotifyChannel.B2b,
                    NotifyEventSubType = NotifyEventSubType.Hotel_GroupRoom_FinalPayment,
                    NotifyMode = NotifyMode.Email,
                    TenantId = order.TenantId,
                    Variables = new
                    {
                        Addressee = form.ContactEmail,
                        HotelName = groupBookingOrderItem.HotelZHName,
                        HotelEnName = groupBookingOrderItem.HotelENName,
                        CheckInDate = groupBookingOrderItem.CheckInDate,
                        CheckOutDate = groupBookingOrderItem.CheckOutDate,
                        BrandEnName = brandEnName,
                        BrandName = brandName
                    }
                });
            }

        }

        #endregion
    }

    public async Task<PagingModel<SearchOutput, IEnumerable<SearchSupplement>>> Search(SearchInput input)
    {
        var baseOrderIds = new List<long>();
        if (input.BaseOrderId.HasValue)
            baseOrderIds.Add(input.BaseOrderId!.Value);
        if (!string.IsNullOrWhiteSpace(input.ConfirmCode) || !string.IsNullOrWhiteSpace(input.GuestName))
        {
            var boIds = await _dbContext.HotelOrders
                .Join(_dbContext.HotelOrderGuests, o => o.Id, g => g.HotelOrderId, (o, g) => new
                {
                    o.Id,
                    o.BaseOrderId,
                    o.IsGroupBooking,
                    o.ConfirmCode,
                    g.GuestName,
                })
                .Where(x => x.IsGroupBooking)
                .WhereIF(!string.IsNullOrWhiteSpace(input.ConfirmCode), x => x.ConfirmCode.Contains(input.ConfirmCode!))
                .WhereIF(!string.IsNullOrWhiteSpace(input.GuestName), x => x.GuestName.Contains(input.GuestName!))
                .GroupBy(x => x.BaseOrderId)
                .Select(x => x.Key)
                .ToListAsync();
            if (!boIds.Any())
            {
                return new PagingModel<SearchOutput, IEnumerable<SearchSupplement>>()
                {
                    PageIndex = input.PageIndex,
                    PageSize = input.PageSize,
                    Total = 0,
                };
            }
            baseOrderIds.AddRange(boIds);
        }

        var query = Query(new QueryGroupBookingOrderDto
        {
            TenantId = input.TenantId,
            ApplicationFormId = input.ApplicationFormId,
            GroupBookingOrderId = input.GroupBookingOrderId,
            BaseOrderIds = baseOrderIds,
            SupplierId = input.SupplierId,
            AgencyId = input.AgencyId,
            UserId = input.UserId,
            SalePersonId = input.SalePersonId,
            GroupNo = input.GroupNo,
            ChannelOrderNo = input.ChannelOrderNo,
            HotelName = input.HotelName,
            OrderBeginDate = input.OrderBeginDate,
            OrderEndDate = input.OrderEndDate,
            CheckInBeginDate = input.CheckInBeginDate,
            CheckInEndDate = input.CheckInEndDate,
            CheckOutBeginDate = input.CheckOutBeginDate,
            CheckOutEndDate = input.CheckOutEndDate,
            PayStatus = input.PayStatus,
            FinalPaymentBeginDate = input.FinalPaymentBeginDate,
            FinalPaymentEndDate = input.FinalPaymentEndDate,
        });
        var searchSupplements = await query
            .GroupBy(x => x.HotelOrderStatus)
            .Select(x => new SearchSupplement
            {
                HotelOrderStatus = x.Key,
                Count = x.Count()
            })
            .ToListAsync();

        if (input.HotelOrderStatus.HasValue)
        {
            query = query.Where(x => x.HotelOrderStatus == input.HotelOrderStatus);
        }
        var total = await query.CountAsync();
        query = query.Skip((input.PageIndex - 1) * input.PageSize)
        .Take(input.PageSize);
        var q = from dto in query
                join item in _dbContext.GroupBookingOrderItems
                on dto.GroupBookingOrderId equals item.GroupBookingOrderId
                select new SearchOutput
                {
                    GroupBookingApplicationFormId = dto.GroupBookingApplicationFormId,
                    AgencyId = dto.AgencyId,
                    AgencyName = dto.AgencyName,
                    SalespersonName = dto.SalespersonName,
                    GroupBookingOrderId = dto.GroupBookingOrderId,
                    HotelOrderStatus = dto.HotelOrderStatus,
                    HotelId = item.HotelId,
                    HotelZHName = item.HotelZHName,
                    HotelENName = item.HotelENName,
                    CheckInDate = item.CheckInDate,
                    CheckOutDate = item.CheckOutDate,
                    HotelRoomName = item.HotelRoomName,
                    HotelRoomEnName = item.HotelRoomEnName,
                    PriceStrategyName = item.PriceStrategyName,
                    PriceStrategyEnName = item.PriceStrategyEnName,
                    RoomCount = item.RoomCount,
                    ContactsName = dto.ContactsName,
                    TotalPayment = dto.TotalPayment,
                    PaymentCurrencyCode = dto.PaymentCurrencyCode,
                    DownPaymentAmount = dto.DownPaymentAmount,
                    DownPaymentPayStatus = dto.DownPaymentPayStatus,
                    LatestDownPaymentTime = dto.LatestDownPaymentTime,
                    FinalPaymentAmount = dto.FinalPaymentAmount,
                    LatestFinalPaymentTime = dto.LatestFinalPaymentTime,
                    FinalPaymentPayStatus = dto.FinalPaymentPayStatus,
                };
        var result = await q.OrderByDescending(x => x.GroupBookingOrderId).ThenBy(x => x.HotelId).ToListAsync();
        return new PagingModel<SearchOutput, IEnumerable<SearchSupplement>>
        {
            Data = result,
            Supplement = searchSupplements,
            Total = total,
            PageIndex = input.PageIndex,
            PageSize = input.PageSize,
        };
    }

    private IQueryable<SearchGroupBookingOrderDto> Query(QueryGroupBookingOrderDto input)
    {
        var query = _dbContext.GroupBookingOrders.IgnoreQueryFilters()
            .Join(_dbContext.GroupBookingOrderItems, o => o.Id, i => i.GroupBookingOrderId, (o, i) => new
            {
                GroupBookingOrder = o,
                GroupBookingOrderItem = i
            })
            .Where(x => x.GroupBookingOrder.TenantId == input.TenantId)
            .WhereIF(input.ApplicationFormId.HasValue, x => x.GroupBookingOrder.GroupBookingApplicationFormId == input.ApplicationFormId!.Value)
            .WhereIF(input.GroupBookingOrderId.HasValue, x => x.GroupBookingOrder.Id == input.GroupBookingOrderId!.Value)
            .WhereIF(input.SupplierId.HasValue, x => x.GroupBookingOrderItem.SupplierId == input.SupplierId!.Value)
            .WhereIF(input.AgencyId.HasValue, x => x.GroupBookingOrder.AgencyId == input.AgencyId!.Value)
            .WhereIF(input.UserId.HasValue, x => x.GroupBookingOrder.UserId == input.UserId!.Value)
            .WhereIF(input.SalePersonId.HasValue, x => x.GroupBookingOrder.SalespersonId == input.SalePersonId!.Value)
            .WhereIF(!string.IsNullOrWhiteSpace(input.GroupNo), x => x.GroupBookingOrder.GroupNo.Contains(input.GroupNo!))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ChannelOrderNo), x => x.GroupBookingOrder.ChannelOrderNo.Contains(input.ChannelOrderNo!))
            .WhereIF(input.BaseOrderIds?.Count is > 0, x => input.BaseOrderIds.Contains(x.GroupBookingOrderItem.BaseOrderId))
            .WhereIF(!string.IsNullOrWhiteSpace(input.HotelName), x => x.GroupBookingOrderItem.HotelZHName.Contains(input.HotelName!))
            .WhereIF(input.OrderBeginDate.HasValue, x => x.GroupBookingOrder.CreateTime >= input.OrderBeginDate)
            .WhereIF(input.OrderEndDate.HasValue, x => x.GroupBookingOrder.CreateTime < input.OrderEndDate!.Value.AddDays(1))
            .WhereIF(input.CheckInBeginDate.HasValue, x => x.GroupBookingOrderItem.CheckInDate >= input.CheckInBeginDate)
            .WhereIF(input.CheckInEndDate.HasValue, x => x.GroupBookingOrderItem.CheckInDate < input.CheckInEndDate!.Value.AddDays(1))
            .WhereIF(input.CheckOutBeginDate.HasValue, x => x.GroupBookingOrderItem.CheckOutDate >= input.CheckOutBeginDate)
            .WhereIF(input.CheckOutEndDate.HasValue, x => x.GroupBookingOrderItem.CheckOutDate < input.CheckOutEndDate!.Value.AddDays(1))
            .GroupBy(x => new
            {
                x.GroupBookingOrder.GroupBookingApplicationFormId,
                x.GroupBookingOrder.SalespersonName,
                x.GroupBookingOrder.Id,
                x.GroupBookingOrder.AgencyId,
                x.GroupBookingOrder.AgencyName,
                x.GroupBookingOrder.HotelOrderStatus,
                x.GroupBookingOrder.ContactsName,
                x.GroupBookingOrder.TotalPayment,
                x.GroupBookingOrder.PaymentCurrencyCode,
            })
            .Select(x => new QueryGroupBookingOrderOutDto
            {
                GroupBookingApplicationFormId = x.Key.GroupBookingApplicationFormId,
                SalespersonName = x.Key.SalespersonName,
                AgencyId = x.Key.AgencyId,
                AgencyName = x.Key.AgencyName,
                GroupBookingOrderId = x.Key.Id,
                HotelOrderStatus = x.Key.HotelOrderStatus,
                ContactsName = x.Key.ContactsName,
                TotalPayment = x.Key.TotalPayment,
                PaymentCurrencyCode = x.Key.PaymentCurrencyCode,
            });

        var q = _dbContext.GroupBookingOrders.IgnoreQueryFilters()
            .Join(_dbContext.GroupBookingOrderPayments, o => o.Id, p => p.GroupBookingOrderId, (o, p) => new
            {
                GroupBookingOrder = o,
                GroupBookingOrderPayment = p
            })
            .Where(x => x.GroupBookingOrder.TenantId == input.TenantId)
            .GroupBy(x => new { x.GroupBookingOrder.Id })
            .Select(x => new QueryGroupBookingOrderPaymentOutDto
            {
                GroupBookingOrderId = x.Key.Id,

                DownPaymentAmount = x.Where(x => x.GroupBookingOrderPayment.PaymentRatioType == PaymentRatioType.DownPayment)
                    .Sum(x => x.GroupBookingOrderPayment.PaymentAmount),
                LatestDownPaymentTime = x.Where(x => x.GroupBookingOrderPayment.PaymentRatioType == PaymentRatioType.DownPayment)
                    .Min(x => x.GroupBookingOrderPayment.LatestPaymentTime),
                DownPaymentPayStatus = x.Where(x => x.GroupBookingOrderPayment.PaymentRatioType == PaymentRatioType.DownPayment)
                    .Min(x => x.GroupBookingOrderPayment.PayStatus),

                FinalPaymentAmount = x.Where(x => x.GroupBookingOrderPayment.PaymentRatioType == PaymentRatioType.FinalPayment)
                    .Sum(x => x.GroupBookingOrderPayment.PaymentAmount),
                LatestFinalPaymentTime = x.Where(x => x.GroupBookingOrderPayment.PaymentRatioType == PaymentRatioType.FinalPayment)
                    .Min(x => x.GroupBookingOrderPayment.LatestPaymentTime),
                FinalPaymentPayStatus = x.Where(x => x.GroupBookingOrderPayment.PaymentRatioType == PaymentRatioType.FinalPayment)
                    .Min(x => x.GroupBookingOrderPayment.PayStatus)
            })
            .WhereIF(input.FinalPaymentBeginDate.HasValue, x => x.LatestFinalPaymentTime >= input.FinalPaymentBeginDate)
            .WhereIF(input.FinalPaymentEndDate.HasValue, x => x.LatestFinalPaymentTime < input.FinalPaymentEndDate!.Value.AddDays(1));

        var dataQuery = from o in query
                        join p in q on o.GroupBookingOrderId equals p.GroupBookingOrderId
                        select new SearchGroupBookingOrderDto
                        {
                            GroupBookingApplicationFormId = o.GroupBookingApplicationFormId,
                            SalespersonName = o.SalespersonName,
                            AgencyId = o.AgencyId,
                            AgencyName = o.AgencyName,
                            GroupBookingOrderId = o.GroupBookingOrderId,
                            HotelOrderStatus = o.HotelOrderStatus,
                            ContactsName = o.ContactsName,
                            TotalPayment = o.TotalPayment,
                            PaymentCurrencyCode = o.PaymentCurrencyCode,
                            DownPaymentAmount = p.DownPaymentAmount,
                            LatestDownPaymentTime = p.LatestDownPaymentTime,
                            DownPaymentPayStatus = p.DownPaymentPayStatus,
                            FinalPaymentAmount = p.FinalPaymentAmount,
                            FinalPaymentPayStatus = p.FinalPaymentPayStatus,
                            LatestFinalPaymentTime = p.LatestFinalPaymentTime,
                        };

        switch (input.PayStatus)
        {
            case 0:
                dataQuery = dataQuery.Where(x => x.DownPaymentPayStatus != PayStatus.Paid || x.FinalPaymentPayStatus != PayStatus.Paid);
                break;
            case 1:
                dataQuery = dataQuery.Where(x => x.DownPaymentPayStatus != PayStatus.Paid);
                break;
            case 2:
                dataQuery = dataQuery.Where(x => x.DownPaymentPayStatus == PayStatus.Paid && x.FinalPaymentPayStatus != PayStatus.Paid);
                break;
        }

        return dataQuery.OrderByDescending(x => x.GroupBookingOrderId);
    }

    public async Task<DetailOutput> Detail(DetailInput input)
    {
        var groupBookingOrder = await _dbContext.GroupBookingOrders
            .Where(x => x.Id == input.GroupBookingOrderId)
            .WhereIF(input.AgencyId.HasValue, x => x.AgencyId == input.AgencyId!.Value)
            .WhereIF(input.UserId.HasValue, x => x.UserId == input.UserId!.Value)
            .Select(x => new GroupBookingOrderOutput
            {
                Id = x.Id,
                GroupBookingPreOrderId = x.GroupBookingPreOrderId,
                GroupBookingApplicationFormId = x.GroupBookingApplicationFormId,
                UserId = x.UserId,
                UserNickName = x.UserNickName,
                VipLevelId = x.VipLevelId,
                VipLevelName = x.VipLevelName,
                ContactsName = x.ContactsName,
                ContactsPhoneNumber = x.ContactsPhoneNumber,
                ContactsEmail = x.ContactsEmail,
                AgencyId = x.AgencyId,
                AgencyName = x.AgencyName,
                SalespersonId = x.SalespersonId,
                SalespersonName = x.SalespersonName,
                SellingPlatform = x.SellingPlatform,
                SellingChannels = x.SellingChannels,
                ChannelOrderNo = x.ChannelOrderNo,
                GroupNo = x.GroupNo,
                CityCode = x.CityCode,
                CityName = x.CityName,
                ResourceHotelId = x.ResourceHotelId,
                HotelZHName = x.HotelZHName,
                HotelENName = x.HotelENName,
                CheckInDate = x.CheckInDate,
                CheckOutDate = x.CheckOutDate,
                Message = x.Message,
                TotalPayment = x.TotalPayment,
                PaymentCurrencyCode = x.PaymentCurrencyCode,
                CreateTime = x.CreateTime,
                HotelOrderStatus = x.HotelOrderStatus,
                HotelOrderGuestFilePath = x.HotelOrderGuestFilePath,
            })
            .FirstAsync();

        var orderPayments = await _dbContext.GroupBookingOrderPayments
            .Where(x => x.GroupBookingOrderId == groupBookingOrder.Id)
            .Select(x => new GroupBookingOrderPaymentOutput
            {
                Id = x.Id,
                GroupBookingOrderId = x.GroupBookingOrderId,
                LatestPaymentTime = x.LatestPaymentTime,
                PaymentRatioType = x.PaymentRatioType,
                PaymentRatio = x.PaymentRatio,
                PaymentAmount = x.PaymentAmount,
                PaymentCurrencyCode = x.PaymentCurrencyCode,
                PayType = x.PayType,
                PaymentTime = x.PaymentTime,
                PayStatus = x.PayStatus,
                HandleOrderStatus = x.HandleOrderStatus,
            })
            .ToListAsync();

        var orderItems = await _dbContext.GroupBookingOrderItems
            .Join(_dbContext.HotelOrders, i => i.HotelOrderId, o => o.Id, (i, o) =>
                new GroupBookingOrderItemOutput
                {
                    Id = i.Id,
                    GroupBookingOrderId = i.GroupBookingOrderId,
                    GroupBookingPreOrderItemId = i.GroupBookingPreOrderItemId,
                    BaseOrderId = i.BaseOrderId,
                    HotelOrderId = i.HotelOrderId,
                    SupplierId = i.SupplierId,
                    SupplierApiType = i.SupplierApiType,
                    HotelId = i.HotelId,
                    CityCode = i.CityCode,
                    CityName = i.CityName,
                    CheckInDate = i.CheckInDate,
                    CheckOutDate = i.CheckOutDate,
                    HotelENName = i.HotelENName,
                    HotelZHName = i.HotelZHName,
                    ResourceHotelId = i.ResourceHotelId,
                    HotelRoomId = i.HotelRoomId,
                    HotelRoomName = i.HotelRoomName,
                    HotelRoomEnName = i.HotelRoomEnName,
                    BedType = i.BedType,
                    MaximumOccupancy = i.MaximumOccupancy,
                    PriceStrategyId = i.PriceStrategyId,
                    PriceStrategyName = i.PriceStrategyName,
                    PriceStrategyEnName = i.PriceStrategyEnName,
                    RoomCount = i.RoomCount,
                    NumberOfBreakfast = i.NumberOfBreakfast,
                    HotelOrderStatus = i.HotelOrderStatus,
                    GuestFilePath = i.GuestFilePath,
                    ConfirmCode = o.ConfirmCode,
                    SupplierOrderId = o.SupplierOrderId,
                })
            .Where(x => x.GroupBookingOrderId == groupBookingOrder.Id)
            .ToListAsync();

        var hotelOrderIds = orderItems.Select(x => x.HotelOrderId);
        var calendarPrices = await _dbContext.HotelOrderCalendarPrices
            .Where(x => hotelOrderIds.Contains(x.HotelOrderId))
            .ToListAsync();
        var guests = await _dbContext.HotelOrderGuests
            .Where(x => hotelOrderIds.Contains(x.HotelOrderId))
            .ToListAsync();
        foreach (var item in orderItems)
        {
            item.HotelCalendarPrices = calendarPrices.Where(x => x.HotelOrderId == item.HotelOrderId)
                .Select(x => new HotelCalendarPriceOutput
                {
                    Date = x.Date,
                    CostCurrencyCode = x.CostCurrencyCode,
                    CostPrice = x.CostPrice,
                    PaymentCurrencyCode = x.PaymentCurrencyCode,
                    SalePrice = x.SalePrice,
                    OrgPrice = x.OrgPrice,
                    OrgPriceCurrencyCode = x.OrgPriceCurrencyCode,
                    CostExchangeRate = x.CostExchangeRate,
                    ExchangeRate = x.ExchangeRate
                });
            item.HotelOrderGuests = guests.Where(x => x.HotelOrderId == item.HotelOrderId)
                .Select(x => new Contracts.Common.Order.DTOs.HotelOrder.HotelOrderGuestInfo
                {
                    GuestName = x.GuestName,
                    FirstName = x.FirstName,
                    LastName = x.LastName
                });
        }

        var additions = await _dbContext.GroupBookingOrderAdditions
            .Where(x => x.GroupBookingOrderId == groupBookingOrder.Id)
            .Select(x => new GroupBookingOrderAdditionOutput
            {
                GroupBookingOrderId = x.GroupBookingOrderId,
                GroupBookingOrderItemId = x.GroupBookingOrderItemId,
                BaseOrderId = x.BaseOrderId,
                SupplierId = x.SupplierId,
                AdditionName = x.AdditionName,
                Amount = x.Amount,
                CurrencyCode = x.CurrencyCode,
                Cost = x.Cost,
                CostCurrencyCode = x.CostCurrencyCode,
                Quantity = x.Quantity,
                Remark = x.Remark
            })
            .ToListAsync();
        var orderIds = orderItems.Select(x => x.HotelOrderId);
        var adjusts = await _dbContext.OrderPaymentAdjusts
            .Where(x => orderIds.Contains(x.OrderId))
            .ToListAsync();
        List<HotelOrderPaymentAdjustOutput> orderPaymentAdjusts = adjusts.Select(s =>
        {
            var orderItem = orderItems.FirstOrDefault(x => x.HotelOrderId == s.OrderId);
            return new HotelOrderPaymentAdjustOutput()
            {
                BaseOrderId = s.BaseOrderId,
                OrderId = s.OrderId,
                HotelName = orderItem?.HotelZHName,
                HotelEnName = orderItem?.HotelENName,
                HotelRoomName = orderItem?.HotelRoomName,
                HotelRoomEnName = orderItem?.HotelRoomEnName,
                PriceStrategyName = orderItem?.PriceStrategyName,
                PriceStrategyEnName = orderItem?.PriceStrategyEnName,
                WorkOrderId = s.WorkOrderId,
                AdjustAmount = s.AdjustAmount,
                PaymentCurrencyCode = s.PaymentCurrencyCode,
                Remark = s.Remark,
                Operator = s.Operator,
                OperatorId = s.OperatorId,
                CreateTime = s.CreateTime
            };
        }).ToList();
        DetailOutput output = new()
        {
            GroupBookingOrder = groupBookingOrder,
            Payments = orderPayments,
            OrderItems = orderItems,
            Additions = additions,
            OrderPaymentAdjusts = orderPaymentAdjusts,
        };
        return output;
    }

    public async ValueTask<List<GroupBookingOrderPaymentOutput>> BaseOrderItemPayments(BaseOrderItemPaymentInput input)
    {
        List<GroupBookingOrderPaymentOutput> result = new();
        var baseOrder = await _dbContext.BaseOrders
            .Where(x => x.Id == input.BaseOrderId)
            .Select(x => new { x.Id, x.PaymentAmount, x.PaymentCurrencyCode })
            .FirstOrDefaultAsync();
        var groupBookingOrderId = await _dbContext.GroupBookingOrderItems
                .Where(x => x.BaseOrderId == baseOrder.Id)
                .Select(x => x.GroupBookingOrderId)
                .FirstOrDefaultAsync();
        if (groupBookingOrderId == 0)
            return result;
        var gboPayments = await _dbContext.GroupBookingOrderPayments.AsNoTracking()
            .Where(x => x.GroupBookingOrderId == groupBookingOrderId)
            .ToListAsync();
        var paymentAmount = baseOrder.PaymentAmount;
        var downPayment = gboPayments.Where(x => x.PaymentRatioType == PaymentRatioType.DownPayment)
            .Select(x => new GroupBookingOrderPaymentOutput
            {
                Id = x.Id,
                GroupBookingOrderId = x.GroupBookingOrderId,
                LatestPaymentTime = x.LatestPaymentTime,
                PaymentRatioType = x.PaymentRatioType,
                PaymentRatio = x.PaymentRatio,
                PaymentCurrencyCode = x.PaymentCurrencyCode,
                PayType = x.PayType,
                PaymentTime = x.PaymentTime,
                PayStatus = x.PayStatus,
                PaymentExternalNo = x.PaymentExternalNo,
                HandleOrderStatus = x.HandleOrderStatus,
                PaymentAmount = (int)Math.Floor(paymentAmount * (x.PaymentRatio / 100)),
            })
            .First();
        result.Add(downPayment);
        var finnalPayment = gboPayments.Where(x => x.PaymentRatioType == PaymentRatioType.FinalPayment)
            .Select(x => new GroupBookingOrderPaymentOutput
            {
                Id = x.Id,
                GroupBookingOrderId = x.GroupBookingOrderId,
                LatestPaymentTime = x.LatestPaymentTime,
                PaymentRatioType = x.PaymentRatioType,
                PaymentRatio = x.PaymentRatio,
                PaymentCurrencyCode = x.PaymentCurrencyCode,
                PayType = x.PayType,
                PaymentTime = x.PaymentTime,
                PayStatus = x.PayStatus,
                HandleOrderStatus = x.HandleOrderStatus,
                PaymentExternalNo = x.PaymentExternalNo,
                PaymentAmount = paymentAmount - downPayment.PaymentAmount,
            })
            .First();
        result.Add(finnalPayment);
        return result;
    }


    public async Task Close(long groupBookingOrderId)
    {
        var groupBookingOrder = await _dbContext.GroupBookingOrders
            .Where(x => x.Id == groupBookingOrderId)
            .FirstOrDefaultAsync();
        if (groupBookingOrder?.HotelOrderStatus != HotelOrderStatus.WaitingForPay)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        groupBookingOrder.HotelOrderStatus = HotelOrderStatus.Closed;
        groupBookingOrder.UpdateTime = DateTime.Now;

        var orderItems = await _dbContext.GroupBookingOrderItems
            .Where(x => x.GroupBookingOrderId == groupBookingOrderId)
            .ToListAsync();
        foreach (var item in orderItems)
        {
            item.HotelOrderStatus = HotelOrderStatus.Closed;
        }
        await _dbContext.SaveChangesAsync();
        foreach (var item in orderItems)
        {
            _backgroundJobClient.Enqueue<HangfireClient.Jobs.IOrderJob>(s => s.CloseTimeoutOrder(item.BaseOrderId, item.TenantId));
        }
    }

    [UnitOfWork]
    public async Task AddOrderGuest(AddOrderGuestInput input)
    {
        var order = await _dbContext.GroupBookingOrders
            .Where(x => x.Id == input.GroupBookingOrderId)
            .WhereIF(input.AgencyId.HasValue, x => x.AgencyId == input.AgencyId)
            .WhereIF(input.UserId.HasValue, x => x.UserId == input.UserId!.Value)
            .FirstAsync();
        var statuses = new HotelOrderStatus[] 
        { 
            HotelOrderStatus.WaitingForConfirm,
            HotelOrderStatus.WaitingForCheckIn
        };
        if (!statuses.Contains(order.HotelOrderStatus) || !string.IsNullOrWhiteSpace(order.HotelOrderGuestFilePath))
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var orderItemIds = input.OrderGuests?.Length is > 0 ?
            input.OrderGuests.Select(x => x.GroupBookingOrderItemId) : Enumerable.Empty<long>();
        var orderItems = await _dbContext.GroupBookingOrderItems
            .Where(x => x.GroupBookingOrderId == order.Id)
            .WhereIF(orderItemIds.Any(), x => orderItemIds.Contains(x.Id))
            .ToListAsync();
        var hotelOrderIds = orderItems.Select(x => x.HotelOrderId);

        var isExists = orderItems.Any(x => !string.IsNullOrWhiteSpace(x.GuestFilePath));
        if (isExists)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        List<HotelOrderGuest> hotelOrderGuests = new();
        var pattern = @"^[\u4e00-\u9fa5·]+";
        foreach (var item in orderItems)
        {
            var orderGuest = input.OrderGuests?.FirstOrDefault(x => x.GroupBookingOrderItemId == item.Id);
            //入住人附件文件路径
            item.GuestFilePath = orderGuest.FilePath;

            if (orderGuest?.GuestInfos?.Any() is not true)
                continue;
            var guests = orderGuest.GuestInfos.Select(x =>
            {
                var guestName = x.GuestName;
                if (string.IsNullOrWhiteSpace(guestName))
                {
                    var delimiter = Regex.IsMatch(x.LastName!, pattern) && Regex.IsMatch(x.FirstName!, pattern) ? "" : " ";
                    guestName = $"{x.LastName}{delimiter}{x.FirstName}";
                }
                return new HotelOrderGuest
                {
                    RoomNumber = x.RoomNumber,
                    BaseOrderId = item.BaseOrderId,
                    HotelOrderId = item.HotelOrderId,
                    GuestName = guestName,
                    FirstName = x.FirstName,
                    LastName = x.LastName,
                    HotelOrderGuestType = x.HotelOrderGuestType.IsValid() ? x.HotelOrderGuestType : HotelOrderGuestType.Adult,
                };
            });
            hotelOrderGuests.AddRange(guests);
        }
        await _dbContext.AddRangeAsync(hotelOrderGuests);

        UpdateOrderGuestMessage message = new()
        {
            TenantId = order.TenantId,
            OrderGuests = orderItems
             .Select(x => new UpdateOrderGuestModel
             {
                 BaseOrderId = x.BaseOrderId,
                 FilePath = x.GuestFilePath,
                 GuestInfos = hotelOrderGuests
                    .Where(i => i.BaseOrderId == x.BaseOrderId)
                    .Select(s => new Contracts.Common.Resource.DTOs.ThirdHotel.HotelOrderGuestDto
                    {
                        GuestName = s.GuestName,
                        FirstName = s.FirstName,
                        LastName = s.LastName
                    })
             })
             .ToArray()
        };
        await _capPublisher.PublishAsync(CapTopics.Order.UpdateHotelApiOrderGuest, message);
    }

    public async Task EditOrderMessage(EditOrderMessageInput input)
    {
        var order = await _dbContext.GroupBookingOrders
            .Where(x => x.Id == input.GroupBookingOrderId)
            .WhereIF(input.AgencyId.HasValue, x => x.AgencyId == input.AgencyId)
            .WhereIF(input.UserId.HasValue, x => x.UserId == input.UserId!.Value)
            .FirstAsync();
        var statuses = new HotelOrderStatus[] { HotelOrderStatus.WaitingForPay, HotelOrderStatus.WaitingForConfirm, HotelOrderStatus.WaitingForCheckIn };
        if (!statuses.Contains(order.HotelOrderStatus))
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        order.Message = input.Message;
        order.UpdateTime = DateTime.Now;
        var orderItems = await _dbContext.GroupBookingOrderItems
            .Where(x => x.GroupBookingOrderId == order.Id)
            .Select(x => new { x.Id, x.BaseOrderId, x.HotelOrderId })
            .ToListAsync();
        var baseOrderIds = orderItems.Select(x => x.BaseOrderId);
        var baseOrders = await _dbContext.BaseOrders
            .Where(x => baseOrderIds.Contains(x.Id))
            .ToListAsync();
        foreach (var baseOrder in baseOrders)
        {
            baseOrder.Message = input.Message;
        }
        await _dbContext.SaveChangesAsync();
    }

    public async Task AdjustFinalPayment(AdjustFinalPaymentInput input)
    {
        var groupBookingOrderPayment = await _dbContext.GroupBookingOrderPayments
            .Where(x => x.Id == input.GroupBookingOrderPaymentId)
            .FirstOrDefaultAsync();
        if (groupBookingOrderPayment?.PayStatus is not PayStatus.Unpaid)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        if ((groupBookingOrderPayment.PaymentAmount + input.AdjustAmount) < 0)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var groupBookingOrder = await _dbContext.GroupBookingOrders
            .Where(x => x.Id == groupBookingOrderPayment.GroupBookingOrderId)
            .FirstAsync();

        var groupBookingPreOrder = await _dbContext.GroupBookingPreOrders
            .Where(x => x.Id == groupBookingOrder.GroupBookingPreOrderId)
            .FirstAsync();
        var baseOrder = await _dbContext.BaseOrders
            .Where(x => x.Id == input.BaseOrderId)
            .FirstAsync();
        var hotelOrder = await _dbContext.HotelOrders
            .Where(x => x.BaseOrderId == baseOrder.Id)
            .FirstAsync();
        var beforeAmount = groupBookingOrderPayment.PaymentAmount;
        var afterAmount = groupBookingOrderPayment.PaymentAmount + input.AdjustAmount;
        GroupBookingOrderPaymentAdjust groupBookingOrderPaymentAdjust = new()
        {
            GroupBookingOrderPaymentId = groupBookingOrderPayment.Id,
            GroupBookingOrderId = groupBookingOrderPayment.GroupBookingOrderId,
            BeforePaymentAmount = beforeAmount,
            AfterPaymentAmount = afterAmount,
            AdjustAmount = input.AdjustAmount,
            PaymentCurrencyCode = groupBookingOrderPayment.PaymentCurrencyCode,
            Remark = input.Remark,
            Operator = input.OperationUser.Name,
            OperatorId = input.OperationUser.UserId,
        };
        groupBookingOrderPayment.PaymentAmount = afterAmount;

        groupBookingPreOrder.FinalPaymentAmount = afterAmount;
        groupBookingPreOrder.TotalAmount =
            groupBookingPreOrder.InitialPaymentAmount + groupBookingPreOrder.FinalPaymentAmount;
        groupBookingPreOrder.UpdateTime = DateTime.Now;

        groupBookingOrder.TotalPayment += input.AdjustAmount;
        groupBookingOrder.UpdateTime = DateTime.Now;

        var beforePaymentAmount = baseOrder.PaymentAmount;
        var afterPaymentAmount = baseOrder.PaymentAmount + input.AdjustAmount;
        OrderPaymentAdjust orderPaymentAdjust = new()
        {
            BaseOrderId = baseOrder.Id,
            BeforePaymentAmount = beforePaymentAmount,
            AfterPaymentAmount = afterPaymentAmount,
            AdjustAmount = input.AdjustAmount,
            PaymentCurrencyCode = baseOrder.PaymentCurrencyCode,
            OrderId = hotelOrder.Id,
            WorkOrderId = input.WorkOrderId,
            Remark = input.Remark,
            Operator = input.OperationUser.Name,
            OperatorId = input.OperationUser.UserId,
            CreateTime = DateTime.Now
        };
        baseOrder.PaymentAmount = afterPaymentAmount;
        baseOrder.UpdateTime = DateTime.Now;
        OrderLogs orderLog = new()
        {
            OrderId = baseOrder.Id,
            OrderLogType = OrderLogType.Hotel,
            OperationRole = input.OperationUser.UserType,
            OperationType = OrderOperationType.AdjustFinalPayment,
            UserId = input.OperationUser.UserId,
            UserName = input.OperationUser.Name,
            CreateTime = DateTime.Now
        };
        await _dbContext.AddAsync(orderPaymentAdjust);
        await _dbContext.AddAsync(groupBookingOrderPaymentAdjust);
        await _dbContext.AddAsync(orderLog);

        await _dbContext.SaveChangesAsync();
    }

    #region private

    private async Task<GetAgenciesByIdsOutput> TenantGetAgencyDetail(long agencyId)
    {
        var response = await _httpClientFactory.InternalGetAsync<GetAgenciesByIdsOutput>(
            requestUri: _servicesAddress.Value.Tenant_GetAgencyDetail(agencyId));
        return response;
    }

    private async Task<B2BWebConfigurationDto> TenantB2bWebConfigurationGet()
    {
        var response = await _httpClientFactory.InternalGetAsync<B2BWebConfigurationDto>(
            requestUri: _servicesAddress.Value.Tenant_B2bWebConfiguration_Get());
        return response;
    }

    private async Task<GetTenantSysConfigOutput> TenantSysConfigByTenantIdGet(long tenantId)
    {
        var response = await _httpClientFactory.InternalGetAsync<GetTenantSysConfigOutput>(
            requestUri: _servicesAddress.Value.Tenant_SysConfigByTenantId_Get(tenantId));
        return response;
    }

    #endregion


}
