using AutoMapper;
using Cit.Storage.Redis;
using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common.Order.DTOs.GroupBookingAggregate;
using Contracts.Common.Order.Enums;
using Contracts.Common.Resource.DTOs;
using Contracts.Common.Resource.DTOs.Country;
using Contracts.Common.Resource.DTOs.Holiday;
using Contracts.Common.Resource.Enums;
using Contracts.Common.User.Enums;
using EfCoreExtensions;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using Order.Api.Extensions;
using Order.Api.Services.Interfaces;
using System.Linq.Expressions;

namespace Order.Api.Services;

public class GroupBookingAggregateService : IGroupBookingAggregateService
{
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly ILogger<GroupBookingAggregateService> _logger;
    private readonly IRedisClient _redisClient;
    private readonly string _redisKey = "Order:GroupBookingAggregate:SyncByJob:Time"; // Redis 缓存键
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ServicesAddress _servicesAddress;

    public GroupBookingAggregateService(CustomDbContext dbContext,
        IMapper mapper,
        ILogger<GroupBookingAggregateService> logger,
        IOptions<ServicesAddress> servicesAddress,
        IHttpClientFactory httpClientFactory,
        IRedisClient redisClient)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _logger = logger;
        _redisClient = redisClient;
        _httpClientFactory = httpClientFactory;
        _servicesAddress = servicesAddress.Value;
    }

    /// <summary>
    /// 基础数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<GetBasicDataOutput> GetBasicData(GetBasicDataInput input)
    {
        var days = (input.EndDate - input.StartDate).TotalDays;
        var res = new GetBasicDataOutput()
        {
            StartDate = input.StartDate,
            EndDate = input.EndDate,
        };
        res.QoQEndDate = input.StartDate.AddDays(-1);// 环比结束日期
        res.QoQStartDate = res.QoQEndDate.AddDays(-days); // 环比开始日期
        //
        var searchEndDate = input.EndDate.Date.AddDays(1); // 查询结束日期为结束日期的下一天0点
        var QoQSearchEndDate = res.QoQEndDate.Date.AddDays(1); // 查询结束日期为结束日期的下一天0点

        var areaData = await GetAreaData(input.AreaIds, input.DimensionType, input.CountryCodes);
        var countryCodes = areaData.CountryCodes.Distinct().ToList();
        var provinceCodes = areaData.ProvinceCodes.Distinct().ToList();
        var noOther = areaData.NoOther;
        var chinaCountryCode = areaData.ChinaCountryCode;
        var areasList = areaData.AreaDatas;

        var baseQuery = _dbContext.GroupBookingAggregate.AsNoTracking()
            .WhereIF(input.OperatorUserIds?.Any() is true, x => input.OperatorUserIds.Contains(x.OperatorUserId))
            //.WhereIF(countryCodes.Any() && noOther, x => countryCodes.Contains(x.CountryCode!.Value))
            //.WhereIF(provinceCodes.Any() && noOther, x => provinceCodes.Contains(x.ProvinceCode!.Value))
            .WhereIF(countryCodes.Any() && noOther == false, x => !countryCodes.Contains(x.CountryCode!.Value))
            .WhereIF(provinceCodes.Any() && noOther == false, x => !(provinceCodes.Contains(x.ProvinceCode!.Value) && x.CountryCode == chinaCountryCode));
        if (areaData.IsAllArea != true && noOther && (countryCodes.Any() || provinceCodes.Any()))
        {
            Expression<Func<GroupBookingAggregate, bool>> expression = t => false;
            if (countryCodes.Any())
            {
                expression = expression.Or(x => countryCodes.Contains(x.CountryCode!.Value));
            }
            if (provinceCodes.Any())
            {
                expression = expression.Or(x => provinceCodes.Contains(x.ProvinceCode!.Value) && x.CountryCode == chinaCountryCode);
            }
            baseQuery = baseQuery.Where(expression);
        }
        var query = baseQuery
          .Select(x => new GroupBookingAggregateChatDto
          {
              ApplicationFormId = x.ApplicationFormId,
              ApplicationTime = x.ApplicationTime,
              CountryCode = x.CountryCode,
              ProvinceCode = x.ProvinceCode,
              TotalAmount = x.TotalAmount ?? 0,
              AgencyId = x.AgencyId,
              SourceType = (x.ApplicantUserType == Contracts.Common.Order.Enums.UserType.Merchant ? GroupBookingSourceType.Vebk :
                             x.UserPlatform == Contracts.Common.User.Enums.UserPlatform.Web ? GroupBookingSourceType.B2B :
                             x.UserPlatform == Contracts.Common.User.Enums.UserPlatform.WechatMiniProgram ? GroupBookingSourceType.WechatMiniProgram : GroupBookingSourceType.None),
              HasDownPayment = x.HasDownPayment,
              InitialPaymentTime = x.InitialPaymentTime
          });

        var applyQuery = query.Clone().Where(x => x.ApplicationTime >= input.StartDate.Date && x.ApplicationTime < searchEndDate);
        var data = await BasicData(applyQuery, areaData);
        var payQuery = query.Clone().Where(x => x.HasDownPayment)
            .Where(x => x.InitialPaymentTime >= input.StartDate.Date && x.InitialPaymentTime < searchEndDate);
        var payData = await BasicData(payQuery, areaData);
        var gmvData = (await GMVData(new GMVDataQuery
        {
            CountryCodes = countryCodes,
            ProvinceCodes = provinceCodes,
            ChinaCountryCode = chinaCountryCode,
            NoOther = noOther,
            StartDate = input.StartDate.Date,
            EndDate = searchEndDate,
            IsAllArea = areaData.IsAllArea,
            OperatorUserIds = input.OperatorUserIds,
        })).GroupBy(x => 1).Select(x => new
        {
            GMV = x.Sum(s => s.GMV)
        }).FirstOrDefault();
        res.FormCount = data?.Count ?? 0;
        res.GMV = gmvData?.GMV ?? 0; // 要求统计的是团房单的子订单的金额，以团房酒店的国家城市当对应的区域统计
        res.ConversionRate = data?.Count > 0 ? decimal.Round((decimal)data.DownPaymentCount / data.Count * 100, 2, MidpointRounding.AwayFromZero) : 0;
        res.DownPaymentCount = payData?.DownPaymentCount ?? 0;
        res.AvgProcessingTime = payData?.Count > 0 ? decimal.Round((decimal)payData.TotalPayTimes / payData.Count, 2, MidpointRounding.AwayFromZero) : 0;
        res.AgencyCount = data?.AgencyCount ?? 0;


        var QoQApplyQuery = query.Clone().Where(x => x.ApplicationTime >= res.QoQStartDate.Date && x.ApplicationTime < QoQSearchEndDate);
        var QoQData = await BasicData(QoQApplyQuery, areaData);
        var QoQpayQuery = query.Clone().Where(x => x.HasDownPayment)
            .Where(x => x.InitialPaymentTime >= res.QoQStartDate.Date && x.InitialPaymentTime < QoQSearchEndDate);
        var QoQPayData = await BasicData(QoQpayQuery, areaData);
        var QoQGMVData = (await GMVData(new GMVDataQuery
        {
            CountryCodes = countryCodes,
            ProvinceCodes = provinceCodes,
            ChinaCountryCode = chinaCountryCode,
            NoOther = noOther,
            StartDate = res.QoQStartDate.Date,
            EndDate = QoQSearchEndDate,
            OperatorUserIds = input.OperatorUserIds,
        })).GroupBy(x => 1).Select(x => new
        {
            GMV = x.Sum(s => s.GMV)
        }).FirstOrDefault();


        res.QoQFormCountPercent = QoQData?.Count > 0 ?
            decimal.Round(((res.FormCount - QoQData.Count) / (decimal)QoQData.Count * 100), 2, MidpointRounding.AwayFromZero) : 0;
        res.QoQGMVPercent = QoQGMVData?.GMV > 0 ?
               decimal.Round((decimal)((res.GMV - QoQGMVData.GMV) / (decimal)QoQGMVData.GMV * 100), 2, MidpointRounding.AwayFromZero) : 0;

        var QoQConversionRate = QoQData?.Count > 0 ?
            decimal.Round((decimal)QoQData.DownPaymentCount / QoQData.Count * 100, 2, MidpointRounding.AwayFromZero) : 0;
        res.QoQConversionRatePercent = QoQConversionRate > 0 ?
                decimal.Round((decimal)((res.ConversionRate - QoQConversionRate) / QoQConversionRate * 100), 2, MidpointRounding.AwayFromZero) : 0;

        res.QoQDownPaymentCountPercent = QoQPayData?.DownPaymentCount > 0 ?
                               decimal.Round((decimal)((res.DownPaymentCount - QoQPayData.DownPaymentCount) / (decimal)QoQPayData.DownPaymentCount * 100), 2, MidpointRounding.AwayFromZero) : 0;
        var QoQAvgProcessingTime = QoQPayData?.Count > 0 ?
                           decimal.Round((decimal)QoQPayData.TotalPayTimes / (decimal)QoQPayData.Count, 2, MidpointRounding.AwayFromZero) : 0;
        res.QoQAvgProcessingTimePercent = QoQAvgProcessingTime > 0 ?
                             decimal.Round((decimal)((res.AvgProcessingTime - QoQAvgProcessingTime) / (decimal)QoQAvgProcessingTime * 100), 2, MidpointRounding.AwayFromZero) : 0;
        res.QoQAgencyCountPercent = QoQData?.AgencyCount > 0 ?
                             decimal.Round((decimal)((res.AgencyCount - QoQData.AgencyCount) / (decimal)QoQData.AgencyCount * 100), 2, MidpointRounding.AwayFromZero) : 0;

        return res;
    }

    /// <summary>
    /// 数据展示
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<GetChatDataOutput> GetChatData(GetChatDataInput input)
    {
        var res = new GetChatDataOutput();
        var areaData = await GetAreaData(input.AreaIds, input.DimensionType, input.CountryCodes);
        var countryCodes = areaData.CountryCodes.Distinct().ToList();
        var provinceCodes = areaData.ProvinceCodes.Distinct().ToList();
        var noOther = areaData.NoOther;
        var chinaCountryCode = areaData.ChinaCountryCode;
        var areasList = areaData.AreaDatas;
        var isAllArea = areaData.IsAllArea;
        var baseQuery = _dbContext.GroupBookingAggregate.AsNoTracking()
           .WhereIF(input.OperatorUserIds?.Any() is true, x => input.OperatorUserIds.Contains(x.OperatorUserId))
           //.WhereIF(countryCodes.Any() && noOther, x => countryCodes.Contains(x.CountryCode!.Value))
           //.WhereIF(provinceCodes.Any() && noOther, x => provinceCodes.Contains(x.ProvinceCode!.Value))
           .WhereIF(countryCodes.Any() && noOther == false, x => !countryCodes.Contains(x.CountryCode!.Value))
           .WhereIF(provinceCodes.Any() && noOther == false, x => !(provinceCodes.Contains(x.ProvinceCode!.Value) && x.CountryCode == chinaCountryCode));
        if (areaData.IsAllArea != true && noOther && (countryCodes.Any() || provinceCodes.Any()))
        {
            Expression<Func<GroupBookingAggregate, bool>> expression = t => false;
            if (countryCodes.Any())
            {
                expression = expression.Or(x => countryCodes.Contains(x.CountryCode!.Value));
            }
            if (provinceCodes.Any())
            {
                expression = expression.Or(x => provinceCodes.Contains(x.ProvinceCode!.Value) && x.CountryCode == chinaCountryCode);
            }
            baseQuery = baseQuery.Where(expression);
        }
        var query = baseQuery
            .Select(x => new GroupBookingAggregateChatDto
            {
                ApplicationFormId = x.ApplicationFormId,
                ApplicationTime = x.ApplicationTime,
                CountryCode = x.CountryCode,
                ProvinceCode = x.ProvinceCode,
                TotalAmount = x.TotalAmount ?? 0,
                AgencyId = x.AgencyId,
                SourceType = (x.ApplicantUserType == Contracts.Common.Order.Enums.UserType.Agency ?
                            (x.UserPlatform == Contracts.Common.User.Enums.UserPlatform.WechatMiniProgram ?
                               GroupBookingSourceType.WechatMiniProgram : GroupBookingSourceType.B2B)
                            :
                            (x.ApplicantUserType == Contracts.Common.Order.Enums.UserType.Merchant ? GroupBookingSourceType.Vebk : GroupBookingSourceType.None)),
                HasDownPayment = x.HasDownPayment,
                InitialPaymentTime = x.InitialPaymentTime
            })
             .WhereIF(input.SourceTypes.Any() && input.DimensionType == StatisticalDimensionType.Source, x => input.SourceTypes.Contains(x.SourceType));
        //
        var searchEndDate = input.EndDate.Date.AddDays(1); // 查询结束日期为结束日期的下一天0点

        switch (input.IndicatorsType)
        {
            case GroupBookingStatisticalIndicatorsType.FormCount:
            case GroupBookingStatisticalIndicatorsType.ConversionRate:
            case GroupBookingStatisticalIndicatorsType.AgencyCount:
                query = query.Where(x => x.ApplicationTime >= input.StartDate.Date && x.ApplicationTime < searchEndDate);
                res = await ChatData(query, input, areaData);
                break;
            case GroupBookingStatisticalIndicatorsType.DownPaymentOrderCount:
            case GroupBookingStatisticalIndicatorsType.AvgProcessingTime:
                query = query.Where(x => x.HasDownPayment)
                         .Where(x => x.InitialPaymentTime >= input.StartDate.Date && x.InitialPaymentTime < searchEndDate);
                res = await ChatData(query, input, areaData);
                break;
            case GroupBookingStatisticalIndicatorsType.GMV:
                res = await ChatCMVData(new GMVDataQuery
                {
                    CountryCodes = countryCodes,
                    ProvinceCodes = provinceCodes,
                    ChinaCountryCode = chinaCountryCode,
                    NoOther = noOther,
                    StartDate = input.StartDate.Date,
                    EndDate = searchEndDate,
                    OperatorUserIds = input.OperatorUserIds,
                }, input, areaData);
                break;
        }
        return res;
    }

    /// <summary>
    /// 获取来源占比
    /// </summary>
    public async Task<GetProportionOutput> GetProportion(GetProportionInput input)
    {
        var res = new GetProportionOutput()
        {
            StartDate = input.StartDate,
            EndDate = input.EndDate,
            IndicatorsType = input.IndicatorsType
        };
        //
        var searchEndDate = input.EndDate.Date.AddDays(1); // 查询结束日期为结束日期的下一天0点

        var query = _dbContext.GroupBookingAggregate.AsNoTracking()
            .WhereIF(input.OperatorUserIds?.Any() is true, x => input.OperatorUserIds.Contains(x.OperatorUserId));
        var data = await query
            .Select(x => new
            {
                ApplicationFormId = x.ApplicationFormId,
                SourceType = (x.ApplicantUserType == Contracts.Common.Order.Enums.UserType.Agency ?
                            (x.UserPlatform == Contracts.Common.User.Enums.UserPlatform.WechatMiniProgram ?
                               GroupBookingSourceType.WechatMiniProgram : GroupBookingSourceType.B2B)
                            :
                            (x.ApplicantUserType == Contracts.Common.Order.Enums.UserType.Merchant ? GroupBookingSourceType.Vebk : GroupBookingSourceType.None)),
                AgencyId = x.AgencyId,
                x.ApplicationTime.Date,
            })
            .Where(x => x.Date >= input.StartDate.Date && x.Date < searchEndDate)
            .GroupBy(x => new { x.SourceType, x.ApplicationFormId })
            .Select(x => new
            {
                ApplicationFormId = x.Key.ApplicationFormId,
                SourceType = x.Key.SourceType,
                AgencyId = x.FirstOrDefault().AgencyId, // 询单客户
            })
            .ToListAsync();

        res.Items = data.Where(x => x.SourceType != GroupBookingSourceType.None)
            .OrderBy(x => x.SourceType)
            .GroupBy(x => x.SourceType)
            .Select(x => new GetProportionItem
            {
                SourceType = x.Key,
                Count = input.IndicatorsType == GroupBookingStatisticalIndicatorsType.AgencyCount ?
                                x.Where(x => x.AgencyId > 0).Select(x => x.AgencyId).Distinct().Count() :
                                input.IndicatorsType == GroupBookingStatisticalIndicatorsType.FormCount ?
                                x.Count() :
                                throw new BusinessException(ErrorTypes.Order.NotSupportIndicatorsType)
            }).ToList();

        var totalCount = res.Items.Sum(x => x.Count);
        if (totalCount > 0)
            for (int i = 0; i < res.Items.Count; i++)
            {
                var item = res.Items[i];
                item.Percent = decimal.Round((decimal)item.Count / totalCount * 100, 2, MidpointRounding.AwayFromZero);
                if (i == res.Items.Count - 1)
                    item.Percent = 100 - res.Items.Take(res.Items.Count - 1).Sum(x => x.Percent); // 最后一个项补齐100%
            }
        return res;
    }

    /// <summary>
    /// 其他数据下载
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<QueryOperationOutput> QueryOperationData(QueryOperationDataInput input)
    {
        var searchEndDate = input.EndDate.Date.AddDays(1); // 查询结束日期为结束日期的下一天0点
        var res = new QueryOperationOutput()
        {
            Data = new List<QueryOperationDataOutput>(),
            Details = new List<QueryOperationDetailOutput>()
        };

        var areaData = await GetAreaData(input.AreaIds, input.DimensionType, input.CountryCodes);
        var countryCodes = areaData.CountryCodes.Distinct().ToList();
        var provinceCodes = areaData.ProvinceCodes.Distinct().ToList();
        var noOther = areaData.NoOther;
        var chinaCountryCode = areaData.ChinaCountryCode;
        var areaList = areaData.AreaDatas;

        var baseQuery = _dbContext.GroupBookingAggregate.AsNoTracking()
            .Where(x => x.ApplicationTime >= input.StartDate.Date && x.ApplicationTime < searchEndDate)
            .WhereIF(input.OperatorUserIds?.Any() is true, x => input.OperatorUserIds.Contains(x.OperatorUserId))
            //.WhereIF(countryCodes.Any() && noOther, x => countryCodes.Contains(x.CountryCode!.Value))
            //.WhereIF(provinceCodes.Any() && noOther, x => provinceCodes.Contains(x.ProvinceCode!.Value))
            .WhereIF(countryCodes.Any() && noOther == false, x => !countryCodes.Contains(x.CountryCode!.Value))
            .WhereIF(provinceCodes.Any() && noOther == false, x => !(provinceCodes.Contains(x.ProvinceCode!.Value) && x.CountryCode == chinaCountryCode));
        if (areaData.IsAllArea != true && noOther && (countryCodes.Any() || provinceCodes.Any()))
        {
            Expression<Func<GroupBookingAggregate, bool>> expression = t => false;
            if (countryCodes.Any())
            {
                expression = expression.Or(x => countryCodes.Contains(x.CountryCode!.Value));
            }
            if (provinceCodes.Any())
            {
                expression = expression.Or(x => provinceCodes.Contains(x.ProvinceCode!.Value) && x.CountryCode == chinaCountryCode);
            }
            baseQuery = baseQuery.Where(expression);
        }

        var logs = await baseQuery
            .Join(_dbContext.GroupBookingOperationLogs, form => form.ApplicationFormId,
            log => log.ApplicationFormId, (form, log) => new { form, log })
            .Where(x => x.log.OperationType == input.OperationType)
            .Where(x => x.log.OperationReasonType != null) // 排除旧数据的记录
            .Select(x => new
            {
                x.form.ApplicationFormId,
                x.form.CountryCode,
                x.form.CountryName,
                x.form.ProvinceCode,
                x.form.ProvinceName,
                x.form.CityCode,
                x.form.CityName,
                x.log.Remark,
                x.log.Status,
                OriginalStatus = x.log.OriginalStatus,
                x.log.OperationReasonType,
                x.form.OperatorUserId,
            })
            .ToListAsync();
        var data = logs.Where(x => x.OperationReasonType?.Any() is true).GroupBy(x => new
        {
            x.OperatorUserId,
            x.ApplicationFormId,
            x.CountryCode,
            x.ProvinceCode,
            x.OriginalStatus,
            OperationReasonType = x.OperationReasonType!.FirstOrDefault()
        }).Select(x => new QueryOperationDataLogItem
        {
            OperatorUserId = x.Key.OperatorUserId,
            ApplicationFormId = x.Key.ApplicationFormId,
            CountryCode = x.Key.CountryCode,
            ProvinceCode = x.Key.ProvinceCode,
            Status = x.Key.OriginalStatus,
            OperationReasonType = x.Key.OperationReasonType,
        }).ToList();

        var areaIds = input.AreaIds?.Any() is true ? input.AreaIds :
                     areaList.Select(x => x.Id).Distinct().ToList();
        if (input.DimensionType == StatisticalDimensionType.Country)
        {
            areaIds = areaData.MixedCountrys.Select(x => (long)x.MixedRegionCountryCode).Distinct().ToList();
        }

        foreach (var areaItem in areaIds)
        {
            //var info = new QueryOperationDataOutput();
            var list = data.ToList();
            var areaName = string.Empty;
            if (input.DimensionType == StatisticalDimensionType.Country)
            {
                var area = areaData.MixedCountrys.FirstOrDefault(x => x.MixedRegionCountryCode == areaItem);
                areaName = area.MixedRegionCountryName;
                list = list.Where(x => area.CountryCodes.Contains(x.CountryCode ?? 0)).ToList();
                if (area.ProvinceCodes?.Any() is true)
                    list = list.Where(x => area.ProvinceCodes.Contains(x.ProvinceCode ?? 0)).ToList();
            }
            else
            {
                var area = areaList.FirstOrDefault(x => x.Id == areaItem);
                areaName = area.Name;
                var countryCodeList = areaList.Where(x => x.Id == areaItem).Select(x => x.CountryCode).ToList();
                var provinceCodeList = areaList.Where(x => x.Id == areaItem).Select(x => x.ProvinceCode).ToList();
                if (area.AreaSettingType == GroupBookingAreaSettingType.China)
                {
                    list = list.Where(x => countryCodeList.Contains(x.CountryCode))
                            .Where(x => provinceCodeList.Contains(x.ProvinceCode))
                            .ToList();
                }
                else if (area.AreaSettingType == GroupBookingAreaSettingType.Other)
                {
                    countryCodeList = areaList.Where(x => x.AreaSettingType != GroupBookingAreaSettingType.Other)
                                            .Select(x => x.CountryCode).ToList();
                    provinceCodeList = areaList.Where(x => x.AreaSettingType != GroupBookingAreaSettingType.Other)
                                     .Where(x => x.ProvinceCode != null)
                                    .Select(x => x.ProvinceCode).ToList();
                    if (provinceCodeList.Any())
                    {
                        countryCodeList = areaList.Where(x => x.AreaSettingType != GroupBookingAreaSettingType.Other
                                && x.AreaSettingType != GroupBookingAreaSettingType.China)
                                            .Select(x => x.CountryCode).ToList();
                        list = list.Where(x => !countryCodeList.Contains(x.CountryCode))
                           .Where(x => !(provinceCodeList.Contains(x.ProvinceCode) && x.CountryCode == chinaCountryCode))
                           .ToList();
                    }
                    else
                    {
                        list = list.Where(x => !countryCodeList.Contains(x.CountryCode))
                            .ToList();
                    }
                }
                else
                {
                    list = list.Where(x => countryCodeList.Contains(x.CountryCode))
                         .ToList();
                }
            }

            if (list.Any())
            {
                var all = list.GroupBy(x => new
                {
                    x.OperationReasonType,
                    x.Status
                }).Select(x => new QueryOperationDataOutput
                {
                    ReasonType = x.Key.OperationReasonType,
                    Status = x.Key.Status,
                    Count = x.Count(),
                    AreaId = areaItem,
                    AreaName = areaName,
                }).OrderBy(x => x.Status).ThenBy(x => x.ReasonType).ToList();
                res.Data.AddRange(all);

                // 按运营人员分组
                var userGroups = list.Where(x => x.OperatorUserId > 0)
                    .GroupBy(x => x.OperatorUserId).ToList();
                foreach (var userGroupItem in userGroups)
                {
                    var userList = userGroupItem.ToList().GroupBy(x => new
                    {
                        x.OperationReasonType,
                        x.Status
                    }).Select(x => new QueryOperationDataOutput
                    {
                        ReasonType = x.Key.OperationReasonType,
                        Status = x.Key.Status,
                        Count = x.Count(),
                        AreaId = areaItem,
                        AreaName = areaName,
                        OperatorUserId = userGroupItem.Key,
                    }).OrderBy(x => x.Status).ThenBy(x => x.ReasonType).ToList();
                    res.Data.AddRange(userList);
                }
                var formIds = list.Select(x => x.ApplicationFormId).ToList();
                var details = logs.Where(x => formIds.Contains(x.ApplicationFormId)).Select(x => new QueryOperationDetailOutput
                {
                    ApplicationFormId = x.ApplicationFormId,
                    AreaId = areaItem,
                    AreaName = areaName,
                    CityCode = x.CityCode,
                    CityName = x.CityName,
                    ProvinceName = x.ProvinceName,
                    CountryName = x.CountryName,
                    CountryCode = x.CountryCode,
                    OperatorUserId = x.OperatorUserId,
                    OriginalStatus = x.OriginalStatus,
                    Status = x.Status,
                    ProvinceCode = x.ProvinceCode,
                    Remark = x.Remark,
                    OperationReasonType = x.OperationReasonType!.FirstOrDefault(), // 只取第一个
                    SubOperationReasonType = x.OperationReasonType!.LastOrDefault() // 只取最后一个
                });
                res.Details.AddRange(details);
            }
        }
        return res;
    }

    /// <summary>
    /// 获取状态漏斗统计信息
    /// </summary>
    /// <param name="info"></param>
    /// <returns></returns>
    public async Task<List<GetStatusStatisticsOutput>> GetStatusStatistics(GetStatusStatisticsInput input)
    {
        var data = await QueryMapToStatus(input);

        var list = data.Select(x => new
        {
            Status = x.Status,
            Count = x.MappedItems.Count(),
            AvgTime = x.MappedItems.Where(x => x.Time > 0).Any() ?
                                       decimal.Round(x.MappedItems.Where(x => x.Time > 0).Average(x => x.Time), 2, MidpointRounding.AwayFromZero) : 0, // 平均时间
            AvgCancelTime = x.MappedItems.Where(x => x.CancelTime > 0).Any() ?
                                       decimal.Round(x.MappedItems
                                                        .Where(x => x.CancelTime > 0)
                                                        .Average(x => x.CancelTime), 2, MidpointRounding.AwayFromZero) : 0, // 平均取消时间
            CancelCount = x.MappedItems.Count(x => x.CancelTime > 0), // 取消数
            CancelApplicationFormIds = x.MappedItems.Where(x => x.CancelTime > 0).Select(x => x.ApplicationFormId).Distinct().ToList(),
            AvgWorkTime = x.MappedItems.Where(x => x.IsWork).Any() ?
                                       decimal.Round(x.MappedItems.Where(x => x.IsWork).Average(x => x.WorkTime), 2, MidpointRounding.AwayFromZero) : 0,
        }).OrderBy(x => x.Status).ToList();

        var cancelFormIds = list.SelectMany(x => x.CancelApplicationFormIds).ToList();
        var logs = await _dbContext.GroupBookingOperationLogs
            .Where(x => cancelFormIds.Contains(x.ApplicationFormId))
            .Where(x => x.OperationType == GrouBookingOperationType.Cancellation)
            .Select(x => new
            {
                x.ApplicationFormId,
                x.Remark,
                x.OperationReasonType,
                x.CreateTime
            }).ToListAsync();

        var res = new List<GetStatusStatisticsOutput>();
        for (int i = 0; i < list.Count; i++)
        {
            var info = list[i];
            var item = new GetStatusStatisticsOutput()
            {
                AvgCancelTime = info.AvgCancelTime,
                AvgTime = info.AvgTime,
                CancelCount = info.CancelCount,
                Count = info.Count,
                Status = info.Status,
                CancelDetails = new()
            };
            item.AvgCancelTime = (int)Math.Ceiling(info.AvgCancelTime);
            item.AvgTime = (int)Math.Ceiling(info.AvgTime);
            item.AvgWorkTime = (int)Math.Ceiling(info.AvgWorkTime);

            if (item.Status == StatisticsFunnelStatus.InitialPayment)
            {
                item.AvgCancelTime = null;
            }
            if (item.Status == StatisticsFunnelStatus.NewApplication)
            {
                item.ConversionRate = null;
                item.AvgTime = null;
                item.AvgWorkTime = null;
            }
            else
            {
                var preCount = res[i - 1].Count;
                item.ConversionRate = preCount > 0 ? decimal.Round((decimal)item.Count / preCount * 100, 2, MidpointRounding.AwayFromZero) : 0;
            }
            var cancelItems = data.FirstOrDefault(x => x.Status == item.Status)
                              .MappedItems.Where(x => x.CancelTime > 0).ToList();
            if (cancelItems.Any())
            {
                foreach (var cancelItem in cancelItems)
                {
                    var log = logs.OrderByDescending(x => x.CreateTime)
                             .FirstOrDefault(x => x.ApplicationFormId == cancelItem.ApplicationFormId);
                    item.CancelDetails.Add(new CancelDetailDto()
                    {
                        AreaId = cancelItem.AreaId,
                        AreaName = cancelItem.AreaName,
                        CityCode = cancelItem.CityCode,
                        CityName = cancelItem.CityName,
                        CountryCode = cancelItem.CountryCode,
                        CountryName = cancelItem.CountryName,
                        CreateTime = log?.CreateTime,
                        Remark = log?.Remark,
                        OperationReasonType = log?.OperationReasonType,
                        OperatorUserId = cancelItem.OperatorUserId,
                        ApplicationFormId = cancelItem.ApplicationFormId,
                    });
                }
            }
            res.Add(item);
        }
        return res;
    }

    /// <summary>
    /// 导出状态漏斗统计信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<List<ExportStatusStatisticsOutput>> ExportStatusStatistics(GetStatusStatisticsInput input)
    {
        //var res = new List<ExportStatusStatisticsOutput>();
        var data = await QueryMapToStatus(input);
        // 导出是区域的时候，是要求全部区域的
        // 国家维度的时候，是选择的国家
        var areaData = await GetAreaData(new List<long>(), input.DimensionType, input.CountryCodes);
        var areaIds = input.AreaIds?.Any() is true ? input.AreaIds :
                       areaData.AreaDatas.Select(x => x.Id).Distinct().ToList();
        if (input.DimensionType == StatisticalDimensionType.Country)
        {
            areaIds = areaData.MixedCountrys.Select(x => (long)x.MixedRegionCountryCode).Distinct().ToList();
        }
        var areaDatas = areaData.AreaDatas;
        var allList = new List<ExportStatusStatisticsOutput>();
        foreach (var areaId in areaIds)
        {
            foreach (var item in data)
            {
                var statusInfo = GetExportStatusStatisticsItem(item.MappedItems.ToList(), item.Status, areaData, input.DimensionType, areaId);

                if (item.Status != StatisticsFunnelStatus.NewApplication)
                {
                    var preCount = allList.Where(x => x.OperatorUserId == null && x.AreaId == areaId)
                                      .OrderBy(x => x.Status)
                                      .LastOrDefault().Count;
                    statusInfo.ConversionRate = preCount > 0 ?
                                          decimal.Round((decimal)statusInfo.Count / preCount * 100, 2, MidpointRounding.AwayFromZero) : 0;

                }

                allList.Add(statusInfo);
                var groupedItems = item.MappedItems.Where(x => x.OperatorUserId > 0)
                    .GroupBy(x => x.OperatorUserId).ToList();

                foreach (var groupedItem in groupedItems)
                {
                    var groupStatusInfo = GetExportStatusStatisticsItem(groupedItem.ToList(), item.Status, areaData, input.DimensionType, areaId);
                    groupStatusInfo.OperatorUserId = groupedItem.Key;
                    if (item.Status != StatisticsFunnelStatus.NewApplication)
                    {
                        var preCount = allList.Where(x => x.OperatorUserId == groupStatusInfo.OperatorUserId && x.AreaId == areaId)
                                          .OrderBy(x => x.Status)
                                          .LastOrDefault().Count;
                        groupStatusInfo.ConversionRate = preCount > 0 ?
                                              decimal.Round((decimal)groupStatusInfo.Count / preCount * 100, 2, MidpointRounding.AwayFromZero) : 0;

                    }
                    allList.Add(groupStatusInfo);
                }
            }
        }

        return allList;
    }

    #region private

    private async Task<List<MapToStatusDto>> QueryMapToStatus(GetStatusStatisticsInput input)
    {
        var searchEndDate = input.EndDate.Date.AddDays(1); // 查询结束日期为结束日期的下一天0点

        var areaData = await GetAreaData(input.AreaIds, input.DimensionType, input.CountryCodes);
        var countryCodes = areaData.CountryCodes.Distinct().ToList();
        var provinceCodes = areaData.ProvinceCodes.Distinct().ToList();
        var noOther = areaData.NoOther;
        var chinaCountryCode = areaData.ChinaCountryCode;
        var areasList = areaData.AreaDatas;
        var apiHolidays = await HolidayList();
        var holidays = apiHolidays.SelectMany(x => x.Holidays).ToList();

        var baseQuery = _dbContext.GroupBookingAggregate.AsNoTracking()
            .WhereIF(input.OperatorUserIds?.Any() is true, x => input.OperatorUserIds.Contains(x.OperatorUserId))
            //.WhereIF(countryCodes.Any() && noOther, x => countryCodes.Contains(x.CountryCode!.Value))
            //.WhereIF(provinceCodes.Any() && noOther, x => provinceCodes.Contains(x.ProvinceCode!.Value))
            .WhereIF(countryCodes.Any() && noOther == false, x => !countryCodes.Contains(x.CountryCode!.Value))
            .WhereIF(provinceCodes.Any() && noOther == false, x => !(provinceCodes.Contains(x.ProvinceCode!.Value) && x.CountryCode == chinaCountryCode));
        if (areaData.IsAllArea != true && noOther && (countryCodes.Any() || provinceCodes.Any()))
        {
            Expression<Func<GroupBookingAggregate, bool>> expression = t => false;
            if (countryCodes.Any())
            {
                expression = expression.Or(x => countryCodes.Contains(x.CountryCode!.Value));
            }
            if (provinceCodes.Any())
            {
                expression = expression.Or(x => provinceCodes.Contains(x.ProvinceCode!.Value) && x.CountryCode == chinaCountryCode);
            }
            baseQuery = baseQuery.Where(expression);
        }
        var query = baseQuery
            .Where(x => x.ApplicationTime >= input.StartDate.Date && x.ApplicationTime <= searchEndDate);
        var statuses = Enum.GetValues(typeof(StatisticsFunnelStatus)).Cast<StatisticsFunnelStatus>();
        // 将数据库查询结果先加载到内存
        var queryResults = await query
            .GroupBy(x => new
            {
                x.ApplicationFormId,
                x.CountryCode,
                x.ProvinceCode,
                x.ApplicationTime
            })
            .Select(x => x.First())
            .ToListAsync();

        var areaIds = input.AreaIds?.Any() is true ? input.AreaIds :
                      areasList.Select(x => x.Id).Distinct().ToList();
        if (input.DimensionType == StatisticalDimensionType.Country)
        {
            areaIds = areaData.MixedCountrys.Select(x => (long)x.MixedRegionCountryCode).Distinct().ToList();
        }
        var allData = new List<GroupBookingAggregate>();

        foreach (var areaId in areaIds)
        {
            var dataItems = queryResults.Select(x => new GroupBookingAggregateItemDto()
            {
                ApplicationFormId = x.ApplicationFormId,
                CountryCode = x.CountryCode,
                ProvinceCode = x.ProvinceCode,
            });
            var list = new List<GroupBookingAggregateItemDto>();
            if (input.DimensionType == StatisticalDimensionType.Country)
            {
                var area = areaData.MixedCountrys.FirstOrDefault(x => x.MixedRegionCountryCode == areaId);

                list = dataItems.Where(x => area.CountryCodes.Contains(x.CountryCode ?? 0)).ToList();
                if (area.ProvinceCodes?.Any() is true)
                    list = list.Where(x => area.ProvinceCodes.Contains(x.ProvinceCode ?? 0)).ToList();
            }
            else
            {
                var area = areasList.FirstOrDefault(x => x.Id == areaId);
                list = GetDataList(dataItems.ToList(), areasList, areaId);
            }
            if (list.Any())
            {
                // 根据ApplicationFormId去重
                var items = queryResults.Where(x => list.Any(y => y.ApplicationFormId == x.ApplicationFormId))
                    .GroupBy(x => x.ApplicationFormId).Select(x => x.First()).ToList();
                allData.AddRange(items);
            }
        }

        // 定义状态映射函数（根据实际业务逻辑实现）
        Func<GroupBookingAggregate, IEnumerable<StatisticsFunnelStatusDto>> mapToStatuses = item =>
        {
            return GetStatisticsFunnelStatus(item, areaData, holidays);
        };

        var data = statuses
             .GroupJoin(
                 allData.SelectMany(item => mapToStatuses(item).Select(status => status)),
                 status => status,
                 mapped => mapped.Status,
                 (status, mappedItems) => new MapToStatusDto
                 {
                     Status = status,
                     MappedItems = mappedItems,
                 })
             .OrderBy(x => x.Status).ToList();
        return data;
    }

    private ExportStatusStatisticsOutput GetExportStatusStatisticsItem(List<StatisticsFunnelStatusDto> list,
       StatisticsFunnelStatus Status,
        GetAreaDataDto areaData,
        StatisticalDimensionType dimensionType,
        long areaId)
    {
        var areaName = "";
        if (dimensionType == StatisticalDimensionType.Country)
        {
            var area = areaData.MixedCountrys.FirstOrDefault(x => x.MixedRegionCountryCode == areaId);
            areaName = area.MixedRegionCountryName;
            list = list.Where(x => area.CountryCodes.Contains(x.CountryCode ?? 0)).ToList();
            if (area.ProvinceCodes?.Any() is true)
                list = list.Where(x => area.ProvinceCodes.Contains(x.ProvinceCode ?? 0)).ToList();
        }
        else
        {
            var areaDatas = areaData.AreaDatas;
            var area = areaDatas.FirstOrDefault(x => x.Id == areaId);
            var countryCodes = areaDatas.Where(x => x.Id == areaId).Select(x => x.CountryCode).ToList();
            var provinceCodes = areaDatas.Where(x => x.Id == areaId).Select(x => x.ProvinceCode).ToList();
            areaName = area.Name;
            if (area.AreaSettingType == GroupBookingAreaSettingType.China)
            {
                list = list.Where(x => countryCodes.Contains(x.CountryCode))
                        .Where(x => provinceCodes.Contains(x.ProvinceCode))
                        .ToList();
            }
            else if (area.AreaSettingType == GroupBookingAreaSettingType.Other)
            {
                countryCodes = areaDatas.Where(x => x.AreaSettingType != GroupBookingAreaSettingType.Other)
                                        .Select(x => x.CountryCode).ToList();
                provinceCodes = areaDatas.Where(x => x.AreaSettingType != GroupBookingAreaSettingType.Other)
                                 .Where(x => x.ProvinceCode != null)
                                .Select(x => x.ProvinceCode).ToList();
                if (provinceCodes.Any())
                {
                    countryCodes = areaDatas.Where(x => x.AreaSettingType != GroupBookingAreaSettingType.Other
                            && x.AreaSettingType != GroupBookingAreaSettingType.China)
                                        .Select(x => x.CountryCode).ToList();
                    var chinaCountryCode = areaDatas.Where(x => x.AreaSettingType == GroupBookingAreaSettingType.China)
                        .Select(x => x.CountryCode).FirstOrDefault();
                    list = list.Where(x => !countryCodes.Contains(x.CountryCode))
                       .Where(x => !(provinceCodes.Contains(x.ProvinceCode) && x.CountryCode == chinaCountryCode))
                       .ToList();
                }
                else
                {
                    list = list.Where(x => !countryCodes.Contains(x.CountryCode))
                        .ToList();
                }
            }
            else
            {
                list = list.Where(x => countryCodes.Contains(x.CountryCode))
                     .ToList();
            }
        }

        var statusInfo = new ExportStatusStatisticsOutput()
        {
            Status = Status,
            AreaName = areaName,
            AreaId = areaId,
        };
        statusInfo.Count = list.Count();
        if (Status != StatisticsFunnelStatus.NewApplication)
        {
            //var preCount = info.Items.LastOrDefault().Count;
            //statusInfo.ConversionRate = preCount > 0 ? decimal.Round((decimal)statusInfo.Count / preCount * 100, 2, MidpointRounding.AwayFromZero) : 0;
            statusInfo.AvgTime = list.Where(x => x.Time > 0).Any() ?
                                decimal.Round(list.Where(x => x.Time > 0).Average(x => x.Time), 2, MidpointRounding.AwayFromZero) : 0; // 平均取消时间
                                                                                                                                       // 要求分钟转小时
            statusInfo.AvgTime = statusInfo.AvgTime > 0 ? decimal.Round(statusInfo.AvgTime.Value / 60, 2, MidpointRounding.AwayFromZero) : 0;

            statusInfo.AvgWorkTime = list.Where(x => x.IsWork).Any() ?
                            decimal.Round(list.Where(x => x.IsWork).Average(x => x.WorkTime), 2, MidpointRounding.AwayFromZero) : 0; // 平均取消时间
                                                                                                                                           // 要求分钟转小时
            statusInfo.AvgWorkTime = statusInfo.AvgWorkTime > 0 ? decimal.Round(statusInfo.AvgWorkTime.Value / 60, 2, MidpointRounding.AwayFromZero) : 0;
        }
        if (Status != StatisticsFunnelStatus.InitialPayment)
        {
            statusInfo.AvgCancelTime = list.Where(x => x.CancelTime > 0).Any() ?
                           decimal.Round(list
                                     .Where(x => x.CancelTime > 0)
                                     .Average(x => x.CancelTime), 2, MidpointRounding.AwayFromZero) : 0; // 平均取消时间
                                                                                                         // 要求分钟转小时
            statusInfo.AvgCancelTime = statusInfo.AvgCancelTime > 0 ? decimal.Round(statusInfo.AvgCancelTime.Value / 60, 2, MidpointRounding.AwayFromZero) : 0;
            statusInfo.CancelCount = list.Count(x => x.CancelTime > 0); // 取消数
        }
        return statusInfo;
    }

    /// <summary>
    /// 获取更新同步时间
    /// </summary>
    /// <returns></returns>
    public async Task<GetSyncTimeOutput> GetSyncTime()
    {
        var data = await _redisClient.StringGetAsync<DateTime>(_redisKey);
        var res = new GetSyncTimeOutput()
        {
            SyncTime = data
        };
        return res;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    public async Task SyncByJob(SyncByJobInput input)
    {
        var date = input.Date.Date;//
        var yesToday = date.AddDays(-1); // 昨天
        _logger.LogInformation("GroupBookingAggregate SyncByJob start :input:{@input},yesToday:{@yesToday}", input, yesToday);

        // 记录数据更新时间
        await _redisClient.StringSetAsync<DateTime>(_redisKey, DateTime.Now);

        int page = 1, pageSize = 500, totalCount = 0, addCount = 0, orderItemCount = 0, addOrderItemCount = 0, errorCount = 0;
        bool isDo = true;
        var first = (await _dbContext.GroupBookingAggregate.FirstOrDefaultAsync()) == null ? true : false;
        do
        {

            // 查询昨天之前的没有结束时间的数据进行更新
            var forms = await _dbContext.GroupBookingApplicationForms
                .WhereIF(first == false, x => x.FinishTime == null || x.FinishTime >= yesToday)// 未结束的或者昨天结束的
                .OrderBy(x => x.Id)
                .Skip((page - 1) * pageSize).Take(pageSize)
                .ToListAsync();
            if (forms.Any())
            {
                var formIds = forms.Select(x => x.Id).ToList();
                try
                {
                    var oldForms = await _dbContext.GroupBookingAggregate
                                        .Where(x => formIds.Contains(x.ApplicationFormId)).ToListAsync();

                    var oldFormOrderDetails = await _dbContext.GroupBookingAggregateOrderDetail
                                        .Where(x => formIds.Contains(x.ApplicationFormId)).ToListAsync();

                    var demands = await _dbContext.GroupBookingApplicationDemands.AsNoTracking()
                                         .Where(x => formIds.Contains(x.ApplicationFormId)).ToListAsync();
                    var demandIds = demands.Select(x => x.Id).ToList();
                    var hotelRecoveryTimes = await _dbContext.GroupBookingQuotations.AsNoTracking()
                                       .Join(_dbContext.GroupBookingQuotationItems.AsNoTracking(), q => q.Id, qi => qi.GroupBookingQuotationId, (q, qi) => new { q, qi })
                                       .Where(x => formIds.Contains(x.q.ApplicationFormId))
                                       .Where(x => x.qi.HotelRecoveryTime != null)
                                       .Select(x => new
                                       {
                                           ApplicationFormId = x.q.ApplicationFormId,
                                           x.qi.HotelRecoveryTime,
                                       })
                                       .ToListAsync();

                    var preOrders = await _dbContext.GroupBookingPreOrders.AsNoTracking()
                                    .Where(x => formIds.Contains(x.ApplicationFormId))
                                    .ToListAsync();
                    var preOrderIds = preOrders.Select(x => x.Id).ToList();
                    var orders = await _dbContext.GroupBookingOrders.AsNoTracking()
                                    .Where(x => preOrderIds.Contains(x.GroupBookingPreOrderId))
                                    .ToListAsync();
                    var orderIds = orders.Select(x => x.Id);
                    var orderPayments = await _dbContext.GroupBookingOrderPayments.AsNoTracking()
                                   .Where(x => orderIds.Contains(x.GroupBookingOrderId)).ToListAsync();
                    var orderItems = await _dbContext.GroupBookingOrderItems.AsNoTracking()
                                     .Where(x => orderIds.Contains(x.GroupBookingOrderId)).ToListAsync();
                    var baseOrderIds = orderItems.Select(x => x.BaseOrderId);
                    var baseOrders = await _dbContext.BaseOrders.AsNoTracking()
                                         .Where(x => baseOrderIds.Contains(x.Id))
                                         .ToListAsync();

                    var logs = await _dbContext.GroupBookingOperationLogs.AsNoTracking()
                                 .Where(x => formIds.Contains(x.ApplicationFormId))
                                 .ToListAsync();

                    totalCount += demands.Count;
                    var addlist = new List<GroupBookingAggregate>();
                    var addOrderDetaillist = new List<GroupBookingAggregateOrderDetail>();
                    #region GroupBookingAggregate
                    foreach (var demand in demands)
                    {
                        var info = oldForms.FirstOrDefault(x => x.ApplicationDemandId == demand.Id);
                        if (info == null)
                        {
                            info = new GroupBookingAggregate();
                            info.SetTenantId(demand.TenantId);
                            info.CreateTime = DateTime.Now;
                            addlist.Add(info);
                        }
                        _mapper.Map(demand, info);
                        var form = forms.FirstOrDefault(x => x.Id == demand.ApplicationFormId);
                        var preOrder = preOrders.OrderByDescending(x => x.HasDownPayment) // 两个预订单，优先取首付的
                                           .FirstOrDefault(x => x.ApplicationFormId == demand.ApplicationFormId);
                        var order = orders.FirstOrDefault(x => x.GroupBookingApplicationFormId == demand.ApplicationFormId);

                        if (form == null)
                            continue;
                        _mapper.Map(form, info);

                        info.UpdateTime = DateTime.Now;
                        info.ApplicationDemandId = demand.Id;
                        info.ApplicationFormId = demand.ApplicationFormId;
                        if (order != null)
                        {
                            info.HasDownPayment = preOrder.HasDownPayment;
                            info.HasFinalPayment = preOrder.HasFinalPayment;
                            info.TotalAmount = order.TotalPayment;
                            var initialPayment = orderPayments.FirstOrDefault(x => x.GroupBookingOrderId == order.Id && x.PaymentRatioType == PaymentRatioType.DownPayment && x.PayStatus == Contracts.Common.Payment.Enums.PayStatus.Paid);
                            var finalPayment = orderPayments.FirstOrDefault(x => x.GroupBookingOrderId == order.Id && x.PaymentRatioType == PaymentRatioType.FinalPayment && x.PayStatus == Contracts.Common.Payment.Enums.PayStatus.Paid);
                            info.InitialPaymentRatio = initialPayment?.PaymentRatio;
                            info.InitialPaymentAmount = initialPayment?.PaymentAmount;
                            info.InitialPaymentPayType = initialPayment?.PayType;
                            info.InitialPaymentCurrencyCode = initialPayment?.PaymentCurrencyCode;
                            info.InitialPaymentTime = initialPayment?.PaymentTime;

                            info.FinalPaymentRatio = finalPayment?.PaymentRatio;
                            info.FinalPaymentAmount = finalPayment?.PaymentAmount;
                            info.FinalPaymentPayType = finalPayment?.PayType;
                            info.FinalPaymentCurrencyCode = finalPayment?.PaymentCurrencyCode;
                            info.FinalPaymentTime = finalPayment?.PaymentTime;
                        }

                        info.WaitForInquiryTime = GetStatusTime(logs,
                                                      demand.ApplicationFormId,
                                                      GroupBookingApplicationFormStatus.NewApplication,
                                                      GroupBookingApplicationFormStatus.WaitForInquiry,
                                                      GroupBookingApplicationFormStatus.Inquiried,
                                                      GrouBookingOperationType.ApplicationConfirmed);
                        // 因为加了状态 Inquiried ，所以这里需要判断一下，兼容旧数据
                        // 旧数据是 1-》3，新数据是 1-》2
                        // 7-29 版本
                        if (info.WaitForInquiryTime == null)
                        {
                            info.WaitForInquiryTime = GetStatusTime(logs,
                                                      demand.ApplicationFormId,
                                                      GroupBookingApplicationFormStatus.NewApplication,
                                                      GroupBookingApplicationFormStatus.WaitForInquiry,
                                                      GroupBookingApplicationFormStatus.Quoted,
                                                      GrouBookingOperationType.ApplicationConfirmed);
                        }


                        var log = logs.OrderBy(x => x.CreateTime)
                                       .FirstOrDefault(x => x.ApplicationFormId == demand.ApplicationFormId
                                       && (x.OperationType == GrouBookingOperationType.HotelReplyQuotation));

                        info.HotelRecoveryTime = log?.CreateTime;
                        var demandHotel = hotelRecoveryTimes.Where(x => x.HotelRecoveryTime != null)
                                    .Where(x => x.ApplicationFormId == demand.ApplicationFormId)
                                    .OrderBy(x => x.HotelRecoveryTime)
                                    .FirstOrDefault();
                        if (info.HotelRecoveryTime == null || demandHotel?.HotelRecoveryTime < info.HotelRecoveryTime)
                            info.HotelRecoveryTime = demandHotel?.HotelRecoveryTime;

                        // 报价审核
                        info.InquiriedTime = GetStatusTime(logs,
                                                      demand.ApplicationFormId,
                                                      GroupBookingApplicationFormStatus.WaitForInquiry,
                                                      GroupBookingApplicationFormStatus.Inquiried,
                                                      GroupBookingApplicationFormStatus.Quoted,
                                                      GrouBookingOperationType.InquiryReply);

                        info.QuotedTime = GetStatusTime(logs,
                                                     demand.ApplicationFormId,
                                                     GroupBookingApplicationFormStatus.Inquiried,
                                                     GroupBookingApplicationFormStatus.Quoted,
                                                     GroupBookingApplicationFormStatus.QuotationConfirmed,
                                                      GrouBookingOperationType.Quoted);
                        // 兼容旧数据
                        // 旧数据是 1-》3，新数据是 2-》3
                        if (info.QuotedTime == null)
                        {
                            info.QuotedTime = GetStatusTime(logs,
                                                     demand.ApplicationFormId,
                                                     GroupBookingApplicationFormStatus.WaitForInquiry,
                                                     GroupBookingApplicationFormStatus.Quoted,
                                                     GroupBookingApplicationFormStatus.QuotationConfirmed,
                                                      GrouBookingOperationType.InquiryReply);
                        }

                        if (info.HotelRecoveryTime == null && info.QuotedTime != null)
                            // 如果没有酒店回复时间，则取报价时间
                            info.HotelRecoveryTime = info.QuotedTime;
                        if (info.HotelRecoveryTime > info.QuotedTime)
                            // 取最早时间
                            info.HotelRecoveryTime = info.QuotedTime;

                        // 兼容旧数据
                        if (info.InquiriedTime == null && info.QuotedTime != null)
                        {
                            info.InquiriedTime = info.QuotedTime;
                        }

                        info.QuotationConfirmedTime = GetStatusTime(logs,
                                               demand.ApplicationFormId,
                                               GroupBookingApplicationFormStatus.Quoted,
                                               GroupBookingApplicationFormStatus.QuotationConfirmed,
                                               GroupBookingApplicationFormStatus.WaitForAuditPreOrder,
                                                GrouBookingOperationType.QuotationConfirmed);

                        info.WaitForAuditPreOrderTime = GetStatusTime(logs,
                                                   demand.ApplicationFormId,
                                                   GroupBookingApplicationFormStatus.QuotationConfirmed,
                                                   GroupBookingApplicationFormStatus.WaitForAuditPreOrder,
                                                   GroupBookingApplicationFormStatus.PreOrdered,
                                                    GrouBookingOperationType.WaitForAuditPreOrder);

                        // 预订单没有下一个状态了，直接变支付了
                        // 获取第一次的预订单的时间
                        var statuslogs = logs
                            .Where(x => x.ApplicationFormId == demand.ApplicationFormId
                            && x.Status == GroupBookingApplicationFormStatus.PreOrdered
                            && x.OriginalStatus == GroupBookingApplicationFormStatus.WaitForAuditPreOrder
                            && x.OperationType == GrouBookingOperationType.PreOrdered);
                        var preOrderedLog = statuslogs.OrderBy(x => x.CreateTime).FirstOrDefault();
                        info.PreOrderedTime = preOrderedLog?.CreateTime;

                        if (form.Status == GroupBookingApplicationFormStatus.Cancellation)
                        {
                            var cancellog = logs
                               .Where(x => x.ApplicationFormId == demand.ApplicationFormId)
                               .Where(x => x.Status == GroupBookingApplicationFormStatus.Cancellation)
                               .OrderByDescending(x => x.CreateTime)
                               .FirstOrDefault();
                            info.CancellationTime = cancellog?.CreateTime;
                            info.CancelStatus = cancellog?.OriginalStatus;
                        }
                    }
                    #endregion

                    #region OrderDetail
                    var cityCodes = orderItems.Count > 0 ? orderItems.Select(x => x.CityCode).Distinct().ToList() : new List<int>();
                    var cities = await QueryCity(new Contracts.Common.Resource.DTOs.QueryInput
                    {
                        CityCodes = cityCodes.ToArray()
                    });

                    orderItemCount += orderItems.Count;
                    foreach (var item in orderItems)
                    {
                        var info = oldFormOrderDetails.FirstOrDefault(x => x.GroupBookingOrderItemId == item.Id);
                        if (info == null)
                        {
                            info = new GroupBookingAggregateOrderDetail();
                            info.SetTenantId(item.TenantId);
                            addOrderDetaillist.Add(info);
                        }

                        var order = orders.FirstOrDefault(x => x.Id == item.GroupBookingOrderId);
                        var baseOrder = baseOrders.FirstOrDefault(x => x.Id == item.BaseOrderId);
                        var city = cities.FirstOrDefault(x => x.CityCode == item.CityCode);
                        var preOrder = preOrders.OrderByDescending(x => x.HasDownPayment) // 两个预订单，优先取首付的
                                        .FirstOrDefault(x => x.ApplicationFormId == order.GroupBookingApplicationFormId);

                        var form = forms.FirstOrDefault(x => x.Id == order.GroupBookingApplicationFormId);

                        info.ApplicationFormId = order.GroupBookingApplicationFormId;
                        info.GroupBookingOrderId = item.GroupBookingOrderId;
                        info.GroupBookingOrderItemId = item.Id;
                        info.GroupBookingPreOrderItemId = item.GroupBookingPreOrderItemId;
                        info.BaseOrderId = item.BaseOrderId;
                        info.HotelOrderId = item.HotelOrderId;

                        info.CountryCode = city?.CountryCode;
                        info.CountryName = city?.CountryName;
                        info.ProvinceCode = city?.ProvinceCode;
                        info.ProvinceName = city?.ProvinceName;
                        info.CityCode = item.CityCode;
                        info.CityName = item.CityName;

                        info.HotelId = item.HotelId;
                        info.HotelZHName = item.HotelZHName;
                        info.CheckInDate = item.CheckInDate;
                        info.CheckOutDate = item.CheckOutDate;

                        info.TotalAmount = baseOrder.TotalAmount;
                        info.DiscountAmount = baseOrder.DiscountAmount;
                        info.PaymentAmount = baseOrder.PaymentAmount;
                        info.CostDiscountAmount = baseOrder.CostDiscountAmount;
                        info.PaymentCurrencyCode = baseOrder.PaymentCurrencyCode;
                        info.PaymentType = baseOrder.PaymentType;

                        info.HasDownPayment = preOrder.HasDownPayment;
                        info.HasFinalPayment = preOrder.HasFinalPayment;

                        info.ApplicationTime = form.ApplicationTime;
                        info.UserPlatform = form.UserPlatform;
                        info.ApplicantUserType = form.ApplicantUserType;

                        var initialPayment = orderPayments.FirstOrDefault(x => x.GroupBookingOrderId == order.Id && x.PaymentRatioType == PaymentRatioType.DownPayment && x.PayStatus == Contracts.Common.Payment.Enums.PayStatus.Paid);
                        var finalPayment = orderPayments.FirstOrDefault(x => x.GroupBookingOrderId == order.Id && x.PaymentRatioType == PaymentRatioType.FinalPayment && x.PayStatus == Contracts.Common.Payment.Enums.PayStatus.Paid);
                        info.InitialPaymentTime = initialPayment?.PaymentTime;
                        info.FinalPaymentTime = finalPayment?.PaymentTime;
                    }
                    #endregion

                    addCount += addlist.Count;
                    if (addlist.Any())
                    {
                        await _dbContext.GroupBookingAggregate.AddRangeAsync(addlist);
                    }

                    addOrderItemCount += addOrderDetaillist.Count;
                    if (addOrderDetaillist.Any())
                    {
                        await _dbContext.GroupBookingAggregateOrderDetail.AddRangeAsync(addOrderDetaillist);
                    }
                    await _dbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    errorCount += formIds.Count;
                    _logger.LogError(ex, "GroupBookingAggregate SyncByJob Error:input:{@input},formIds：{@formIds}", input, formIds);
                }
            }
            else
            {
                isDo = false;
            }
            page++;
        } while (isDo);

        _logger.LogInformation("GroupBookingAggregate SyncByJob success :input:{@input},res:{@res}",
            input, new { totalCount, addCount, orderItemCount, addOrderItemCount, errorCount });
    }

    private DateTime? GetStatusTime(List<GroupBookingOperationLog> logs,
            long applicationFormId,
            GroupBookingApplicationFormStatus preStatus,
            GroupBookingApplicationFormStatus status,
            GroupBookingApplicationFormStatus nextStatus,
            GrouBookingOperationType operationType)
    {
        // 当前状态首次转到下个状态
        var nextStatuslog = logs.OrderBy(x => x.CreateTime)
           .FirstOrDefault(x => x.ApplicationFormId == applicationFormId
           && x.OriginalStatus == status
           && x.Status == nextStatus);
        // 上个状态最后转到当前状态的时间，并且比 当前状态首次转到下个状态的时间要早，
        // 因为有可能是跨状态回滚时影响
        var statuslogs = logs
            .Where(x => x.ApplicationFormId == applicationFormId
            && x.Status == status
            && x.OriginalStatus == preStatus
            && x.OperationType == operationType);// 指定状态的转变操作，排除在当前状态时的其它操作
        if (nextStatuslog != null)
        {
            // 已经流转到下一个状态了，那些回滚的消耗时间是算给下个状态的，所以需要过滤掉
            statuslogs = statuslogs.Where(x => x.CreateTime < nextStatuslog.CreateTime);
        }
        var log = statuslogs.OrderByDescending(x => x.CreateTime).FirstOrDefault();

        return log?.CreateTime;
    }

    /// <summary>
    /// 查询城市信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private async Task<IEnumerable<Contracts.Common.Resource.DTOs.CityOutput>> QueryCity(
        Contracts.Common.Resource.DTOs.QueryInput input)
    {
        var httpContent = new StringContent(JsonConvert.SerializeObject(input),
            Encoding.UTF8, "application/json");
        var response =
            await _httpClientFactory.InternalPostAsync<IEnumerable<Contracts.Common.Resource.DTOs.CityOutput>>(
                requestUri: _servicesAddress.Resource_CityQuery(), httpContent: httpContent);
        return response;
    }

    private async Task<IEnumerable<CountryProvinceOutput>> QueryProvinces(int countryCode)
    {
        var response =
            await _httpClientFactory.InternalGetAsync<IEnumerable<CountryProvinceOutput>>(
                requestUri: _servicesAddress.Resource_QueryProvince(countryCode));
        return response;
    }

    private async Task<IEnumerable<MixedCountryDto>> QueryCountrys()
    {
        var response =
            await _httpClientFactory.InternalGetAsync<IEnumerable<CountryOutput>>(
                requestUri: _servicesAddress.Resource_Country());
        var result = response.Select(x => new MixedCountryDto
        {
            CountryCode = x.CountryCode,
            ZHName = x.ZHName,
            ENName = x.ENName,
            IsoCode = x.IsoCode,
        })
       .Where(x => x.CountryCode > 10)//排除其他
       .ToList();

        //添加 中国大陆,中国香港，中国澳门，中国台湾
        result.ApendMixedCountry();

        return result.OrderBy(s => s.CountryCode);
    }


    private async Task<GetChatDataOutput> ChatData(IQueryable<GroupBookingAggregateChatDto> query,
        GetChatDataInput input,
        GetAreaDataDto searchAreaData)
    {
        var res = new GetChatDataOutput()
        {
            ChatDatas = new List<ChatDataDto>(),
            DimensionType = input.DimensionType
        };
        var days = input.EndDate.Subtract(input.StartDate).Days;
        var areaDatas = searchAreaData.AreaDatas;
        var isAllArea = searchAreaData.IsAllArea;

        if (input.DimensionType == StatisticalDimensionType.Area || input.DimensionType == StatisticalDimensionType.Country)
        {
            IQueryable<IGrouping<ChatDataGroup, GroupBookingAggregateChatDto>> groupQuery = null;
            if (input.IndicatorsType == GroupBookingStatisticalIndicatorsType.DownPaymentOrderCount
                || input.IndicatorsType == GroupBookingStatisticalIndicatorsType.AvgProcessingTime)
            {
                groupQuery = query.GroupBy(x => new ChatDataGroup
                {
                    ApplicationFormId = x.ApplicationFormId,
                    CountryCode = x.CountryCode,
                    ProvinceCode = x.ProvinceCode,
                    Date = x.InitialPaymentTime!.Value.Date,
                });
            }
            else
            {
                groupQuery = query.GroupBy(x => new ChatDataGroup
                {
                    ApplicationFormId = x.ApplicationFormId,
                    CountryCode = x.CountryCode,
                    ProvinceCode = x.ProvinceCode,
                    Date = x.ApplicationTime.Date,
                });
            }

            // 获取每个订单的首付单数、询单客户、GMV 和处理时间
            var data = await groupQuery.Select(x => new GroupBookingAggregateItemDto
            {
                ApplicationFormId = x.Key.ApplicationFormId,
                Date = x.Key.Date,
                CountryCode = x.Key.CountryCode,
                ProvinceCode = x.Key.ProvinceCode,
                HasDownPayment = x.FirstOrDefault().HasDownPayment, // 首付单数
                AgencyId = x.FirstOrDefault().AgencyId, // 询单客户
                TotalAmount = x.FirstOrDefault().TotalAmount,// GMV,  根据 ApplicationFormId 分组了，是同一单，但是多个的时候，会重复计算了
                TotalPayTimes = x.Average(s => EF.Functions.DateDiffMinute(s.ApplicationTime, s.InitialPaymentTime) ?? 0) //处理时间,  根据 ApplicationFormId 分组了，是同一单，但是多个的时候，会重复计算了
            }).ToListAsync();

            var areaIds = input.AreaIds?.Any() is true ? input.AreaIds :
                       areaDatas.Select(x => x.Id).Distinct().ToList();
            if (input.DimensionType == StatisticalDimensionType.Country)
            {
                areaIds = searchAreaData.MixedCountrys.Select(x => (long)x.MixedRegionCountryCode).Distinct().ToList();
            }
            var allData = new List<GroupBookingAggregateItemDto>();
            foreach (var areaItem in areaIds)
            {
                var chatInfo = new ChatDataDto();
                var list = new List<GroupBookingAggregateItemDto>();
                if (input.DimensionType == StatisticalDimensionType.Country)
                {
                    var area = searchAreaData.MixedCountrys.FirstOrDefault(x => x.MixedRegionCountryCode == areaItem);
                    chatInfo.Name = area.MixedRegionCountryName;
                    chatInfo.AreaId = areaItem;

                    list = data.Where(x => area.CountryCodes.Contains(x.CountryCode ?? 0)).ToList();
                    if (area.ProvinceCodes?.Any() is true)
                        list = list.Where(x => area.ProvinceCodes.Contains(x.ProvinceCode ?? 0)).ToList();
                }
                else
                {
                    var area = areaDatas.FirstOrDefault(x => x.Id == areaItem);
                    chatInfo.Name = area.Name;
                    chatInfo.AreaId = areaItem;
                    list = GetDataList(data.ToList(), areaDatas, areaItem);
                }

                // 多个目的地,同一订单可能会重复计算，所以需要按 ApplicationFormId 分组
                list = list.GroupBy(x => new
                {
                    x.ApplicationFormId,
                })
                .Select(x => x.First())
                .ToList();
                allData.AddRange(list);
                for (int i = 0; i <= days; i++)
                {
                    var date = input.StartDate.AddDays(i).Date;
                    var chat = new DataItem()
                    {
                        Date = date,
                        Data = 0 // 默认值为0
                    };
                    chatInfo.DataItems.Add(chat);
                    var item = list.Where(x => x.Date == date).GroupBy(x => 1).Select(x => new
                    {
                        Count = x.Count(), // 首付订单数
                        DownPaymentCount = x.Count(s => s.HasDownPayment == true), // 首付单数
                        TotalPayTimes = x.Sum(s => s.TotalPayTimes),
                        AgencyCount = x.Where(s => s.AgencyId > 0).Select(s => s.AgencyId).Distinct().Count(), // 询单客户
                    }).FirstOrDefault();
                    if (item == null)
                        continue; // 如果没有数据则跳过
                    switch (input.IndicatorsType)
                    {
                        case GroupBookingStatisticalIndicatorsType.FormCount:
                            chat.Data = item.Count;
                            break;
                        case GroupBookingStatisticalIndicatorsType.ConversionRate:
                            chat.Data = item.Count > 0 ? decimal.Round((decimal)item.DownPaymentCount / item.Count * 100, 2, MidpointRounding.AwayFromZero) : 0;
                            break;
                        case GroupBookingStatisticalIndicatorsType.AgencyCount:
                            chat.Data = item.AgencyCount;
                            break;
                            break;
                        case GroupBookingStatisticalIndicatorsType.AvgProcessingTime:// 要求分钟转小时
                            chat.Data = item?.Count > 0 ? decimal.Round((decimal)item.TotalPayTimes / (item.Count * 60), 2, MidpointRounding.AwayFromZero) : 0;
                            break;
                        case GroupBookingStatisticalIndicatorsType.DownPaymentOrderCount:
                            chat.Data = item?.DownPaymentCount ?? 0;
                            break;
                    }

                }
                res.ChatDatas.Add(chatInfo);
            }

            // 要求全部时是整合成一条线的数据
            if (isAllArea)
            {
                res.ChatDatas = res.ChatDatas.GroupBy(x => 1).Select(x => new ChatDataDto()
                {
                    Name = "全部",
                    DataItems = x.SelectMany(s => s.DataItems).GroupBy(s => s.Date).Select(s =>
                    {
                        var info = new DataItem()
                        {
                            Date = s.Key,
                            Data = s.Sum(k => k.Data)
                        };
                        if (input.IndicatorsType == GroupBookingStatisticalIndicatorsType.AgencyCount)
                        {
                            // 因为分销商需要去重，所以这里需要重新计算
                            info.Data = data.Where(m => m.Date == info.Date).Where(x => x.AgencyId > 0).Select(m => m.AgencyId).Distinct().Count();
                        }
                        if (input.IndicatorsType == GroupBookingStatisticalIndicatorsType.ConversionRate)
                        {

                            var totalCount = allData.Where(m => m.Date == info.Date).Count();
                            var downPaymentCount = allData.Where(m => m.Date == info.Date).Count(m => m.HasDownPayment);
                            info.Data = totalCount > 0 ? decimal.Round((decimal)downPaymentCount / totalCount * 100, 2, MidpointRounding.AwayFromZero) : 0;
                        }
                        if (input.IndicatorsType == GroupBookingStatisticalIndicatorsType.AvgProcessingTime)
                        {
                            var totalCount = allData.Where(m => m.Date == info.Date).Count();
                            var totalTimes = allData.Where(m => m.Date == info.Date).Sum(m => m.TotalPayTimes);
                            // 要求分钟转小时
                            info.Data = totalCount > 0 ? decimal.Round((decimal)totalTimes / (totalCount * 60), 2, MidpointRounding.AwayFromZero) : 0;
                        }
                        return info;
                    }).ToList(),
                }).ToList();
            }
        }
        else
        {

            IQueryable<IGrouping<ChatDataGroup, GroupBookingAggregateChatDto>> groupQuery = null;
            if (input.IndicatorsType == GroupBookingStatisticalIndicatorsType.DownPaymentOrderCount
                || input.IndicatorsType == GroupBookingStatisticalIndicatorsType.AvgProcessingTime)
            {
                groupQuery = query.GroupBy(x => new ChatDataGroup
                {
                    ApplicationFormId = x.ApplicationFormId,
                    SourceType = x.SourceType,
                    Date = x.InitialPaymentTime!.Value.Date,
                });
            }
            else
            {
                groupQuery = query.GroupBy(x => new ChatDataGroup
                {
                    ApplicationFormId = x.ApplicationFormId,
                    SourceType = x.SourceType,
                    Date = x.ApplicationTime.Date,
                });
            }
            var dataSource = await groupQuery.Select(x => new
            {
                ApplicationFormId = x.Key.ApplicationFormId,
                Date = x.Key.Date,
                SourceType = x.Key.SourceType,
                HasDownPayment = x.FirstOrDefault().HasDownPayment, // 首付单数
                AgencyId = x.FirstOrDefault().AgencyId, // 询单客户
                TotalAmount = x.FirstOrDefault().TotalAmount,// GMV,  根据 ApplicationFormId 分组了，是同一单，但是多个的时候，会重复计算了
                TotalPayTimes = x.Average(s => EF.Functions.DateDiffMinute(s.ApplicationTime, s.InitialPaymentTime) ?? 0) //处理时间,  根据 ApplicationFormId 分组了，是同一单，但是多个的时候，会重复计算了
            }).ToListAsync();

            var data = dataSource.GroupBy(x => new
            {
                x.SourceType,
                x.Date,
            })
             .Select(x => new
             {
                 x.Key.Date,
                 SourceType = x.Key.SourceType,
                 Count = x.Count(), // 订单数
                 GMV = x.Sum(s => s.TotalAmount) ?? 0,// GMV
                 DownPaymentCount = x.Count(s => s.HasDownPayment == true), // 首付单数
                 TotalTimes = x.Sum(s => s.TotalPayTimes),
                 AgencyIds = x.Where(s => s.AgencyId > 0).Select(s => s.AgencyId).Distinct().ToList(), // 询单客户
             })
             .ToList();
            var chatTypes = input.SourceTypes.Any() ? input.SourceTypes :
                              Enum.GetValues(typeof(GroupBookingSourceType)).Cast<GroupBookingSourceType>().ToList();

            foreach (var type in chatTypes)
            {
                if (type == GroupBookingSourceType.None)
                    continue;
                var chatInfo = new ChatDataDto() { SourceType = type };
                var list = data
                  .Where(x => x.SourceType == type)
                  .OrderBy(x => x.Date)
                  .ToList();
                for (int i = 0; i <= days; i++)
                {
                    var date = input.StartDate.AddDays(i).Date;
                    var chat = new DataItem()
                    {
                        Date = date,
                        Data = 0 // 默认值为0
                    };
                    chatInfo.DataItems.Add(chat);
                    var item = list.Where(x => x.Date == date).GroupBy(x => 1).Select(x => new
                    {
                        Count = x.Sum(x => x.Count), // 首付订单数
                        GMV = x.Sum(s => s.GMV),// GMV
                        DownPaymentCount = x.Sum(s => s.DownPaymentCount), // 首付单数
                        TotalPayTimes = x.Sum(s => s.TotalTimes),
                        AgencyCount = x.SelectMany(s => s.AgencyIds).Distinct().Count(), // 询单客户
                    }).FirstOrDefault();
                    if (item == null)
                        continue; // 如果没有数据则跳过
                    switch (input.IndicatorsType)
                    {
                        case GroupBookingStatisticalIndicatorsType.FormCount:
                            chat.Data = item.Count;
                            break;
                        case GroupBookingStatisticalIndicatorsType.ConversionRate:
                            chat.Data = item.Count > 0 ? decimal.Round((decimal)item.DownPaymentCount / item.Count * 100, 2, MidpointRounding.AwayFromZero) : 0;
                            break;
                        case GroupBookingStatisticalIndicatorsType.AgencyCount:
                            chat.Data = item.AgencyCount;
                            break;
                        case GroupBookingStatisticalIndicatorsType.GMV:
                            chat.Data = item.GMV;
                            break;
                        case GroupBookingStatisticalIndicatorsType.AvgProcessingTime://要求分钟转小时
                            chat.Data = item?.Count > 0 ? decimal.Round((decimal)item.TotalPayTimes / (item.Count * 60), 2, MidpointRounding.AwayFromZero) : 0;
                            break;
                        case GroupBookingStatisticalIndicatorsType.DownPaymentOrderCount:
                            chat.Data = item?.DownPaymentCount ?? 0;
                            break;
                    }

                }
                res.ChatDatas.Add(chatInfo);
            }
        }

        return res;
    }

    private async Task<GetChatDataOutput> ChatCMVData(GMVDataQuery gmvInput,
      GetChatDataInput input,
      GetAreaDataDto searchAreaData)
    {
        var res = new GetChatDataOutput() { ChatDatas = new List<ChatDataDto>() };
        var days = input.EndDate.Subtract(input.StartDate).Days;
        var dataSource = await GMVData(gmvInput);
        var areaDatas = searchAreaData.AreaDatas;
        var isAllArea = searchAreaData.IsAllArea;
        if (input.DimensionType == StatisticalDimensionType.Area || input.DimensionType == StatisticalDimensionType.Country)
        {
            // 对数据进行分组，按国家和省份统计
            var data = dataSource.GroupBy(x => new
            {
                x.CountryCode,
                x.ProvinceCode,
                x.Date,
            })
             .Select(x => new GroupBookingAggregateItemDto
             {
                 Date = x.Key.Date,
                 CountryCode = x.Key.CountryCode,
                 ProvinceCode = x.Key.ProvinceCode,
                 TotalAmount = x.Sum(s => s.GMV),
             })
             .ToList();

            var areaIds = input.AreaIds?.Any() is true ? input.AreaIds :
                      areaDatas.Select(x => x.Id).Distinct().ToList();
            if (input.DimensionType == StatisticalDimensionType.Country)
            {
                areaIds = searchAreaData.MixedCountrys.Select(x => (long)x.MixedRegionCountryCode).Distinct().ToList();
            }
            foreach (var areaItem in areaIds)
            {
                var chatInfo = new ChatDataDto();
                var list = new List<GroupBookingAggregateItemDto>();
                if (input.DimensionType == StatisticalDimensionType.Country)
                {
                    var area = searchAreaData.MixedCountrys.FirstOrDefault(x => x.MixedRegionCountryCode == areaItem);
                    chatInfo.Name = area.MixedRegionCountryName;
                    chatInfo.AreaId = areaItem;

                    list = data.Where(x => area.CountryCodes.Contains(x.CountryCode ?? 0)).ToList();
                    if (area.ProvinceCodes?.Any() is true)
                        list = list.Where(x => area.ProvinceCodes.Contains(x.ProvinceCode ?? 0)).ToList();
                }
                else
                {
                    var area = areaDatas.FirstOrDefault(x => x.Id == areaItem);
                    chatInfo.Name = area.Name;
                    chatInfo.AreaId = areaItem;
                    list = GetDataList(data.ToList(), areaDatas, areaItem);
                }

                for (int i = 0; i <= days; i++)
                {
                    var date = input.StartDate.AddDays(i).Date;
                    var chat = new DataItem()
                    {
                        Date = date,
                        Data = 0 // 默认值为0
                    };
                    chatInfo.DataItems.Add(chat);
                    var item = list.Where(x => x.Date == date).GroupBy(x => 1).Select(x => new
                    {
                        GMV = x.Sum(s => s.TotalAmount),// GMV
                    }).FirstOrDefault();
                    if (item == null)
                        continue; // 如果没有数据则跳过
                    chat.Data = item.GMV ?? 0;
                }
                res.ChatDatas.Add(chatInfo);
            }

            // 要求全部时是整合成一条线的数据
            if (isAllArea)
            {
                res.ChatDatas = res.ChatDatas.GroupBy(x => 1).Select(x => new ChatDataDto()
                {
                    Name = "全部",
                    DataItems = x.SelectMany(s => s.DataItems).GroupBy(s => s.Date).Select(s =>
                    {
                        var info = new DataItem()
                        {
                            Date = s.Key,
                            Data = s.Sum(k => k.Data)
                        };
                        return info;
                    }).ToList(),
                }).ToList();
            }
        }
        else
        {
            var data = dataSource.GroupBy(x => new
            {
                x.SourceType,
                x.Date,
            })
             .Select(x => new
             {
                 x.Key.Date,
                 SourceType = x.Key.SourceType,
                 GMV = x.Sum(s => s.GMV)// GMV
             })
             .ToList();
            var chatTypes = input.SourceTypes.Any() ? input.SourceTypes :
                              Enum.GetValues(typeof(GroupBookingSourceType)).Cast<GroupBookingSourceType>().ToList();

            foreach (var type in chatTypes)
            {
                if (type == GroupBookingSourceType.None)
                    continue;
                var chatInfo = new ChatDataDto() { SourceType = type };
                var list = data
                  .Where(x => x.SourceType == type)
                  .OrderBy(x => x.Date)
                  .ToList();
                for (int i = 0; i <= days; i++)
                {
                    var date = input.StartDate.AddDays(i).Date;
                    var chat = new DataItem()
                    {
                        Date = date,
                        Data = 0 // 默认值为0
                    };
                    chatInfo.DataItems.Add(chat);
                    var item = list.Where(x => x.Date == date).GroupBy(x => 1).Select(x => new
                    {
                        GMV = x.Sum(s => s.GMV),// GMV
                    }).FirstOrDefault();
                    if (item == null)
                        continue; // 如果没有数据则跳过
                    chat.Data = item.GMV;
                }
                res.ChatDatas.Add(chatInfo);
            }
        }

        return res;
    }

    private async Task<BasicData> BasicData(IQueryable<GroupBookingAggregateChatDto> query,
        GetAreaDataDto searchAreaData)
    {
        var res = new List<BasicDataItem>();
        // 先按 ApplicationFormId、国家和省份 分组，获取每个订单的首付单数、询单客户、GMV 和处理时间
        var dataBasic = await query.GroupBy(x => new
        {
            x.ApplicationFormId,
            x.CountryCode,
            x.ProvinceCode,
        }).Select(x => new GroupBookingAggregateItemDto
        {
            ApplicationFormId = x.Key.ApplicationFormId,
            CountryCode = x.Key.CountryCode,
            ProvinceCode = x.Key.ProvinceCode,
            HasDownPayment = x.FirstOrDefault().HasDownPayment, // 首付单数
            AgencyId = x.FirstOrDefault().AgencyId, // 询单客户
            TotalAmount = x.FirstOrDefault().TotalAmount,// GMV ,  根据 ApplicationFormId 分组了，是同一单，但是多个的时候，会重复计算了
            TotalPayTimes = x.Average(s => EF.Functions.DateDiffMinute(s.ApplicationTime, s.InitialPaymentTime) ?? 0) //处理时间，根据 ApplicationFormId 分组了，是同一单，但是多个的时候，会重复计算了
        })
        .ToListAsync();
        if (searchAreaData.DimensionType == StatisticalDimensionType.Area)
        {
            var areaDatas = searchAreaData.AreaDatas;
            var groupAreaItems = areaDatas.Select(x => x.Id).Distinct().ToList();
            foreach (var areaItem in groupAreaItems)
            {
                var list = GetDataList(dataBasic.ToList(), areaDatas, areaItem);
                // 多个目的地,同一订单可能会重复计算，所以需要按 ApplicationFormId 分组
                var data = list.GroupBy(x => new
                {
                    x.ApplicationFormId,
                })
                .Select(x => x.First())
                .ToList();
                var info = data.GroupBy(x => 1) // 按常数分组以获得函数统计单个结果
                   .Select(x => new BasicDataItem
                   {
                       FormCount = x.Count(), // 首付订单数
                                              //GMV = x.Sum(s => s.GMV),// GMV
                       DownPaymentCount = x.Count(s => s.HasDownPayment == true),// 首付单数
                       TotalTimes = x.Sum(s => s.TotalPayTimes),
                       AgencyIds = x.Where(s => s.AgencyId > 0).Select(s => s.AgencyId).Distinct().ToList(), // 询单客户
                       AreaId = areaItem,
                   }).FirstOrDefault();
                if (info != null)
                    res.Add(info);
            }
        }
        else if (searchAreaData.DimensionType == StatisticalDimensionType.Country)
        {
            foreach (var item in searchAreaData.MixedCountrys)
            {
                var list = dataBasic.Where(x => item.CountryCodes.Contains(x.CountryCode ?? 0))
                    .ToList();

                if (item.ProvinceCodes?.Any() is true)
                    list = list.Where(x => item.ProvinceCodes.Contains(x.ProvinceCode ?? 0)).ToList();
                // 多个目的地,同一订单可能会重复计算，所以需要按 ApplicationFormId 分组
                var data = list.GroupBy(x => new
                {
                    x.ApplicationFormId,
                })
                .Select(x => x.First())
                .ToList();
                var info = data.GroupBy(x => 1) // 按常数分组以获得函数统计单个结果
                   .Select(x => new BasicDataItem
                   {
                       FormCount = x.Count(), // 首付订单数
                                              //GMV = x.Sum(s => s.GMV),// GMV
                       DownPaymentCount = x.Count(s => s.HasDownPayment == true),// 首付单数
                       TotalTimes = x.Sum(s => s.TotalPayTimes),
                       AgencyIds = x.Where(s => s.AgencyId > 0).Select(s => s.AgencyId).Distinct().ToList(), // 询单客户
                   }).FirstOrDefault();
                if (info != null)
                    res.Add(info);
            }
        }
        return res.GroupBy(x => 1) // 按常数分组以获得函数统计单个结果
            .Select(x => new BasicData
            {
                Count = x.Sum(s => s.FormCount), // 首付订单数
                                                 // GMV = x.Sum(s => s.GMV),// GMV
                DownPaymentCount = x.Sum(s => s.DownPaymentCount), // 首付单数
                TotalPayTimes = x.Sum(s => s.TotalTimes),
                AgencyCount = x.SelectMany(s => s.AgencyIds).Distinct().Count(), // 询单客户
            })
            .FirstOrDefault();
    }
    private async Task<(List<MixedRegionCountryDto> MixedCountrys, int ChinaCountryCode)> GetCountry(List<int> inputCountryCodes)
    {
        var res = new List<MixedRegionCountryDto>();
        int chinaCountryCode = 0;
        int china = 10;
        int hkProvinceCode = 810000; // 中国香港
        int twProvinceCode = 710000; // 中国台湾
        int macaoProvinceCode = 820000; // 中国澳门
        var chinaProvinces = await QueryProvinces(china);
        var countryCodes = inputCountryCodes.Where(x => x != (int)MixedRegionCountry.MainlandChina
                                                       && x != (int)MixedRegionCountry.HongKong
                                                       && x != (int)MixedRegionCountry.Macao
                                                       && x != (int)MixedRegionCountry.Taiwan
                                                       ).ToList();
        var coutrys = await QueryCountrys();
        if (inputCountryCodes?.Any() is not true)
            inputCountryCodes = coutrys.Select(x => x.CountryCode).ToList();
        foreach (var item in inputCountryCodes)
        {
            switch (item)
            {
                case (int)MixedRegionCountry.MainlandChina://中国大陆
                    chinaCountryCode = china;
                    var provinces = chinaProvinces.Where(x => x.ProvinceCode != hkProvinceCode
                                 && x.ProvinceCode != twProvinceCode && x.ProvinceCode != macaoProvinceCode)
                                  .ToList();
                    res.Add(new MixedRegionCountryDto
                    {
                        MixedRegionCountryCode = item,
                        MixedRegionCountryName = "中国大陆",
                        CountryCodes = provinces.Select(x => x.CountryCode!.Value).ToList(),
                        ProvinceCodes = provinces.Select(x => x.ProvinceCode).ToList()
                    });
                    break;
                case (int)MixedRegionCountry.HongKong: // 中国香港
                    chinaCountryCode = china;
                    res.Add(new MixedRegionCountryDto
                    {
                        MixedRegionCountryCode = item,
                        MixedRegionCountryName = "中国香港",
                        CountryCodes = new List<int>() { china },
                        ProvinceCodes = new List<int>() { hkProvinceCode }
                    });
                    break;
                case (int)MixedRegionCountry.Macao: // 中国澳门
                    chinaCountryCode = china;
                    res.Add(new MixedRegionCountryDto
                    {
                        MixedRegionCountryCode = item,
                        MixedRegionCountryName = "中国澳门",
                        CountryCodes = new List<int>() { china },
                        ProvinceCodes = new List<int>() { macaoProvinceCode }
                    });
                    break;
                case (int)MixedRegionCountry.Taiwan: // 中国台湾
                    chinaCountryCode = china;
                    res.Add(new MixedRegionCountryDto
                    {
                        MixedRegionCountryCode = item,
                        MixedRegionCountryName = "中国台湾",
                        CountryCodes = new List<int>() { china },
                        ProvinceCodes = new List<int>() { twProvinceCode }
                    });
                    break;
                default:
                    var country = coutrys.FirstOrDefault(x => x.CountryCode == item);
                    res.Add(new MixedRegionCountryDto
                    {
                        MixedRegionCountryCode = item,
                        MixedRegionCountryName = country?.ZHName,
                        CountryCodes = new List<int>() { item },
                        ProvinceCodes = new List<int>()
                    });
                    break;
            }
        }
        return (res, chinaCountryCode);
    }

    private async Task<GetAreaDataDto> GetAreaData(List<long> areaIds,
        StatisticalDimensionType statisticalDimensionType,
        List<int> inputCountryCodes)
    {
        var res = new GetAreaDataDto() { DimensionType = statisticalDimensionType };
        var countryCodes = new List<int>();
        var provinceCodes = new List<int>();
        var areaDatas = await _dbContext.GroupBookingAreaSetting
                               .GroupJoin(_dbContext.GroupBookingAreaSettingDetail, setting => setting.Id
                               , detail => detail.GroupBookingAreaSettingId, (setting, detail) => new { setting, detail })
                              .SelectMany(x => x.detail.DefaultIfEmpty(), (x, detail) => new { x.setting, detail })
                              .Select(x => new AreaDto
                              {
                                  Id = x.setting.Id,
                                  Name = x.setting.Name,
                                  AreaSettingType = x.setting.Type,
                                  CountryCode = x.detail.CountryCode,
                                  ProvinceCode = x.detail.ProvinceCode
                              })
                              .ToListAsync();
        bool noOther = true;
        bool isAllArea = false;
        int chinaCountryCode = 10;
        var resAreaDatas = areaDatas;
        if (statisticalDimensionType == StatisticalDimensionType.Area)
        {
            if (areaIds?.Any() is true)
            {
                if (areaDatas.Where(x => areaIds.Contains(x.Id)).Select(x => x.Id).Distinct().Count() != areaIds.Count)
                {
                    throw new BusinessException(ErrorTypes.Order.AreaChange);
                }
                var otherArea = areaDatas
                    .Where(x => areaIds.Contains(x.Id))
                    .Where(x => x.AreaSettingType == GroupBookingAreaSettingType.Other)
                    .FirstOrDefault();
                List<AreaDto> areas = areaDatas.Where(x => areaIds.Contains(x.Id)).ToList();
                // 如果包含了其它，那只能使用 not in 排查
                if (otherArea != null)
                {
                    areas = areaDatas.Where(x => !areaIds.Contains(x.Id)).ToList();
                    noOther = false;
                }
                areas.ForEach(x =>
                {
                    if (x.AreaSettingType != GroupBookingAreaSettingType.China)
                        countryCodes.Add(x.CountryCode ?? 0);
                    if (x.ProvinceCode > 0)
                        provinceCodes.Add(x.ProvinceCode ?? 0);
                });

                resAreaDatas = areaDatas.Where(x => areaIds.Contains(x.Id)).ToList();
            }
            else // 全部
            {
                isAllArea = true;
                areaIds = areaDatas.Select(x => x.Id).Distinct().ToList();
                resAreaDatas = areaDatas;
            }
        }
        else if (statisticalDimensionType == StatisticalDimensionType.Country)
        {
            var coutryDatas = await GetCountry(inputCountryCodes);
            res.MixedCountrys = coutryDatas.MixedCountrys;
            if (inputCountryCodes?.Any() is not true)
                isAllArea = true;
            else
            {
                countryCodes = coutryDatas.MixedCountrys.SelectMany(x => x.CountryCodes).Distinct().ToList();
                provinceCodes = coutryDatas.MixedCountrys.SelectMany(x => x.ProvinceCodes).Distinct().ToList();
            }
        }
        countryCodes = countryCodes.Distinct().ToList();
        provinceCodes = provinceCodes.Distinct().ToList();
        if (provinceCodes.Any())
        {
            countryCodes.Remove(chinaCountryCode);
        }
        res.NoOther = noOther;
        res.IsAllArea = isAllArea;
        res.ChinaCountryCode = chinaCountryCode;
        res.ProvinceCodes = provinceCodes;
        res.CountryCodes = countryCodes;
        res.AreaDatas = resAreaDatas;
        return res;
    }

    record GMVDataQuery
    {
        public List<int> CountryCodes { get; set; } = new();

        public List<int> ProvinceCodes { get; set; } = new();
        public int? ChinaCountryCode { get; set; }
        public bool NoOther { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime EndDate { get; set; }

        /// <summary>
        /// 是否全部区域或者国家/地区
        /// </summary>
        public bool IsAllArea { get; set; }

        /// <summary>
        /// 跟进运营id 商户员工id
        /// </summary>
        public List<long?> OperatorUserIds { get; set; } = new();
    }

    private async Task<List<GMVDataItem>> GMVData(GMVDataQuery queryInput)
    {
        var baseQuery = _dbContext.GroupBookingAggregateOrderDetail
            .Join(_dbContext.GroupBookingAggregate,
                detail => detail.ApplicationFormId,
                order => order.ApplicationFormId,
                (detail, order) => new
                {
                    detail,
                    order
                })
             .Where(x => x.detail.HasDownPayment)
             .Where(x => x.detail.InitialPaymentTime >= queryInput.StartDate && x.detail.InitialPaymentTime < queryInput.EndDate)
            .WhereIF(queryInput.OperatorUserIds?.Any() is true, x => queryInput.OperatorUserIds.Contains(x.order.OperatorUserId))
            //.WhereIF(queryInput.CountryCodes.Any() && queryInput.NoOther, x => queryInput.CountryCodes.Contains(x.CountryCode!.Value))
            //.WhereIF(queryInput.ProvinceCodes.Any() && queryInput.NoOther, x => queryInput.ProvinceCodes.Contains(x.ProvinceCode!.Value))
            .WhereIF(queryInput.CountryCodes.Any() && queryInput.NoOther == false, x => !queryInput.CountryCodes.Contains(x.detail.CountryCode!.Value))
            .WhereIF(queryInput.ProvinceCodes.Any() && queryInput.NoOther == false,
                             x => !(queryInput.ProvinceCodes.Contains(x.detail.ProvinceCode!.Value) && x.detail.CountryCode == queryInput.ChinaCountryCode))
            //.GroupBy(x => x.detail.Id)
            .Select(g => g.detail);
        if (queryInput.IsAllArea != true && queryInput.NoOther && (queryInput.CountryCodes.Any() || queryInput.ProvinceCodes.Any()))
        {
            Expression<Func<GroupBookingAggregateOrderDetail, bool>> expression = t => false;
            if (queryInput.CountryCodes.Any())
            {
                expression = expression.Or(x => queryInput.CountryCodes.Contains(x.CountryCode!.Value));
            }
            if (queryInput.ProvinceCodes.Any())
            {
                expression = expression.Or(x => queryInput.ProvinceCodes.Contains(x.ProvinceCode!.Value) && x.CountryCode == queryInput.ChinaCountryCode);
            }
            baseQuery = baseQuery.Where(expression);
        }
        var data = await baseQuery
            .Select(x => new
            {
                x.ApplicationFormId,
                x.BaseOrderId,
                x.InitialPaymentTime,
                x.CountryCode,
                x.ProvinceCode,
                x.TotalAmount,
                SourceType = (x.ApplicantUserType == Contracts.Common.Order.Enums.UserType.Agency ?
                            (x.UserPlatform == Contracts.Common.User.Enums.UserPlatform.WechatMiniProgram ?
                               GroupBookingSourceType.WechatMiniProgram : GroupBookingSourceType.B2B)
                            :
                            (x.ApplicantUserType == Contracts.Common.Order.Enums.UserType.Merchant ? GroupBookingSourceType.Vebk : GroupBookingSourceType.None)),
            })
             .GroupBy(x => new
             {
                 x.BaseOrderId,
                 x.CountryCode,
                 x.ProvinceCode,
                 x.InitialPaymentTime!.Value.Date // 按日期分组
             })
             .Select(x => new GMVDataItem
             {
                 Date = x.Key.Date,
                 BaseOrderId = x.Key.BaseOrderId, // 要求统计团房单的子订单
                 CountryCode = x.Key.CountryCode,
                 ProvinceCode = x.Key.ProvinceCode,
                 GMV = x.Sum(s => s.TotalAmount),// 同一个 ApplicationFormId 可能有多个记录，对应具体的 baseOrder，金额是独立的，这里是求和
                 SourceType = x.FirstOrDefault().SourceType
             }).ToListAsync();

        return data;
    }

    private IEnumerable<StatisticsFunnelStatusDto> GetStatisticsFunnelStatus(GroupBookingAggregate info, GetAreaDataDto areaData, List<HolidayDto> holidays)
    {
        IEnumerable<StatisticsFunnelStatusDto> funnelStatus = new List<StatisticsFunnelStatusDto>();
        var status = info.Status;
        var isCancel = false;
        if (info.Status == GroupBookingApplicationFormStatus.Cancellation)
        {
            status = info.CancelStatus!.Value;// 取消时，使用取消当时的状态
            isCancel = true;
        }
        var isWork = false;
        switch (status)
        {
            case GroupBookingApplicationFormStatus.NewApplication:
                funnelStatus = new[] {
                    new StatisticsFunnelStatusDto{
                        Status= StatisticsFunnelStatus.NewApplication,
                        Time= 0,
                        CancelTime=isCancel? GetTimes(info.ApplicationTime,info.CancellationTime):0,
                    },
                };
                break;
            case GroupBookingApplicationFormStatus.WaitForInquiry:
                funnelStatus = new[] {
                     new StatisticsFunnelStatusDto{
                         Status=StatisticsFunnelStatus.NewApplication,
                         Time=0,
                         CancelTime=0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.WaitForInquiry,
                         Time= GetTimes(info.ApplicationTime,info.WaitForInquiryTime),
                         CancelTime= 0,
                         WorkTime= GetWorkTimes(info.ApplicationTime,info.WaitForInquiryTime,holidays,out isWork),
                         IsWork = isWork

                     },
                };
                if (info.HotelRecoveryTime.HasValue)
                {
                    funnelStatus = funnelStatus.Append(new StatisticsFunnelStatusDto
                    {
                        Status = StatisticsFunnelStatus.HotelRecovery,
                        Time = GetTimes(info.WaitForInquiryTime, info.HotelRecoveryTime),
                        CancelTime = isCancel ? GetTimes(info.HotelRecoveryTime, info.CancellationTime) : 0
                    });
                }
                else
                {
                    funnelStatus.LastOrDefault().CancelTime = isCancel ? GetTimes(info.WaitForInquiryTime, info.CancellationTime) : 0;
                }
                break;
            case GroupBookingApplicationFormStatus.Inquiried:
                funnelStatus = new[] {
                     new StatisticsFunnelStatusDto{
                         Status=StatisticsFunnelStatus.NewApplication,
                         Time=0,
                         CancelTime= 0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.WaitForInquiry,
                         Time= GetTimes(info.ApplicationTime,info.WaitForInquiryTime),
                         WorkTime= GetWorkTimes(info.ApplicationTime,info.WaitForInquiryTime,holidays,out isWork),
                         IsWork = isWork,
                         CancelTime= 0
                     },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.HotelRecovery,
                         Time= GetTimes(info.WaitForInquiryTime, info.HotelRecoveryTime),
                         CancelTime= 0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.Inquiried,
                          Time= GetTimes(info.HotelRecoveryTime, info.InquiriedTime),
                         CancelTime=isCancel? GetTimes(info.InquiriedTime, info.CancellationTime):0 },
                };

                break;
            case GroupBookingApplicationFormStatus.Quoted:
                funnelStatus = new[] {
                     new StatisticsFunnelStatusDto{
                         Status=StatisticsFunnelStatus.NewApplication,
                         Time=0,
                         CancelTime=0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.WaitForInquiry,
                         Time= GetTimes(info.ApplicationTime,info.WaitForInquiryTime),
                         WorkTime= GetWorkTimes(info.ApplicationTime,info.WaitForInquiryTime,holidays,out isWork),
                         IsWork = isWork,
                         CancelTime= 0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.HotelRecovery,
                         Time= GetTimes(info.WaitForInquiryTime, info.HotelRecoveryTime),
                         CancelTime= 0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.Inquiried,
                          Time= GetTimes(info.HotelRecoveryTime, info.InquiriedTime),
                         CancelTime= 0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.Quoted,
                         Time= GetTimes(info.InquiriedTime, info.QuotedTime),
                         CancelTime=isCancel?  GetTimes(info.QuotedTime, info.CancellationTime):0,
                         WorkTime= GetWorkTimes(info.InquiriedTime, info.QuotedTime,holidays,out isWork),
                          IsWork = isWork,
                     },
                };
                break;
            case GroupBookingApplicationFormStatus.QuotationConfirmed:
                funnelStatus = new[] {
                     new StatisticsFunnelStatusDto{
                         Status=StatisticsFunnelStatus.NewApplication,
                         Time=0,
                         CancelTime=0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.WaitForInquiry,
                         Time= GetTimes(info.ApplicationTime,info.WaitForInquiryTime),
                         WorkTime= GetWorkTimes(info.ApplicationTime,info.WaitForInquiryTime,holidays,out isWork),
                          IsWork = isWork,
                         CancelTime= 0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.HotelRecovery,
                         Time= GetTimes(info.WaitForInquiryTime, info.HotelRecoveryTime),
                         CancelTime= 0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.Inquiried,
                          Time= GetTimes(info.HotelRecoveryTime, info.InquiriedTime),
                         CancelTime= 0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.Quoted,
                         Time= GetTimes(info.InquiriedTime, info.QuotedTime),
                         WorkTime= GetWorkTimes(info.InquiriedTime, info.QuotedTime,holidays,out isWork),
                          IsWork = isWork,
                         CancelTime= 0 },
                     new StatisticsFunnelStatusDto{
                          Status= StatisticsFunnelStatus.QuotationConfirmed,
                         Time= GetTimes(info.QuotedTime, info.QuotationConfirmedTime),
                         CancelTime=isCancel? GetTimes(info.QuotationConfirmedTime, info.CancellationTime):0 },
                };
                break;
            case GroupBookingApplicationFormStatus.WaitForAuditPreOrder:
                funnelStatus = new[] {
                    new StatisticsFunnelStatusDto{
                         Status=StatisticsFunnelStatus.NewApplication,
                         Time=0,
                         CancelTime=0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.WaitForInquiry,
                         Time= GetTimes(info.ApplicationTime,info.WaitForInquiryTime),
                         WorkTime= GetWorkTimes(info.ApplicationTime,info.WaitForInquiryTime,holidays,out isWork),
                          IsWork = isWork,
                         CancelTime=0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.HotelRecovery,
                         Time= GetTimes(info.WaitForInquiryTime, info.HotelRecoveryTime),
                         CancelTime= 0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.Inquiried,
                          Time= GetTimes(info.HotelRecoveryTime, info.InquiriedTime),
                         CancelTime= 0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.Quoted,
                          Time= GetTimes(info.InquiriedTime, info.QuotedTime),
                         WorkTime= GetWorkTimes(info.InquiriedTime, info.QuotedTime,holidays,out isWork),
                          IsWork = isWork,
                         CancelTime=0 },
                     new StatisticsFunnelStatusDto{
                          Status= StatisticsFunnelStatus.QuotationConfirmed,
                         Time= GetTimes(info.QuotedTime, info.QuotationConfirmedTime),
                         CancelTime= 0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.WaitForAuditPreOrder,
                         Time= GetTimes(info.QuotationConfirmedTime, info.WaitForAuditPreOrderTime),
                         CancelTime= isCancel? GetTimes(info.WaitForAuditPreOrderTime, info.CancellationTime):0 },
                };
                break;
            case GroupBookingApplicationFormStatus.PreOrdered:
                funnelStatus = new[] {
                     new StatisticsFunnelStatusDto{
                         Status=StatisticsFunnelStatus.NewApplication,
                         Time=0,
                         CancelTime=0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.WaitForInquiry,
                         Time= GetTimes(info.ApplicationTime,info.WaitForInquiryTime),
                         WorkTime= GetWorkTimes(info.ApplicationTime,info.WaitForInquiryTime,holidays,out isWork),
                          IsWork = isWork,
                         CancelTime=0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.HotelRecovery,
                         Time= GetTimes(info.WaitForInquiryTime, info.HotelRecoveryTime),
                         CancelTime= 0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.Inquiried,
                          Time= GetTimes(info.HotelRecoveryTime, info.InquiriedTime),
                         CancelTime= 0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.Quoted,
                          Time= GetTimes(info.InquiriedTime, info.QuotedTime),
                          WorkTime= GetWorkTimes(info.InquiriedTime, info.QuotedTime,holidays,out isWork),
                           IsWork = isWork,
                         CancelTime= 0 },
                     new StatisticsFunnelStatusDto{
                          Status= StatisticsFunnelStatus.QuotationConfirmed,
                         Time= GetTimes(info.QuotedTime, info.QuotationConfirmedTime),
                         CancelTime= 0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.WaitForAuditPreOrder,
                         Time= GetTimes(info.QuotationConfirmedTime, info.WaitForAuditPreOrderTime),
                         CancelTime= 0 },
                     new StatisticsFunnelStatusDto{
                         Status= StatisticsFunnelStatus.PreOrdered,
                         Time= GetTimes(info.WaitForAuditPreOrderTime, info.PreOrderedTime),
                         CancelTime= isCancel? GetTimes(info.PreOrderedTime, info.CancellationTime):0,
                         WorkTime= GetWorkTimes(info.WaitForAuditPreOrderTime, info.PreOrderedTime,holidays,out isWork),
                          IsWork = isWork,
                     },
                };
                if (info.HasDownPayment)
                {
                    funnelStatus = funnelStatus.Append(new StatisticsFunnelStatusDto
                    {
                        Status = StatisticsFunnelStatus.InitialPayment,
                        Time = GetTimes(info.PreOrderedTime, info.InitialPaymentTime),
                        CancelTime = 0
                    });
                }
                break;
        }
        var otherArea = areaData.AreaDatas.Where(x => x.AreaSettingType == GroupBookingAreaSettingType.Other)
            .FirstOrDefault();
        foreach (var item in funnelStatus)
        {
            item.ApplicationFormId = info.ApplicationFormId;
            item.CountryCode = info.CountryCode;
            item.CountryName = info.CountryName;
            item.ProvinceCode = info.ProvinceCode;
            item.ProvinceName = info.ProvinceName;
            item.CityCode = info.CityCode;
            item.CityName = info.CityName;
            item.FormStatus = info.Status;
            item.OperatorUserId = info.OperatorUserId;
            var area = areaData.AreaDatas.FirstOrDefault(x => x.CountryCode == info.CountryCode);
            if (area != null && area.AreaSettingType == GroupBookingAreaSettingType.China)
            {
                area = areaData.AreaDatas.FirstOrDefault(x => x.CountryCode == info.CountryCode
                                                    && x.ProvinceCode == info.ProvinceCode);
            }
            if (area == null)
            {
                area = otherArea;
            }
            item.AreaName = area?.Name;
            item.AreaId = area?.Id;
        }
        return funnelStatus;
    }

    private decimal GetTimes(DateTime? startTime, DateTime? endTime)
    {
        if (startTime == null || endTime == null)
            return 0;
        var times = (endTime.Value - startTime.Value).TotalMinutes;
        return decimal.Round((decimal)times, 2, MidpointRounding.AwayFromZero);
    }

    private decimal GetWorkTimes(DateTime? startTime, DateTime? endTime, List<HolidayDto> holidayDetails, out bool isWork)
    {
        isWork = false;
        // 入参校验：空值或开始时间晚于结束时间，返回0
        if (startTime == null || endTime == null || startTime >= endTime)
        {
            return 0;
        }
        DateTime start = startTime.Value;
        DateTime end = endTime.Value;

        decimal totalMinutes = 0;

        //// 收集所有调休日（去重，仅保留日期部分）
        var inverseDays = holidayDetails.Where(x => x.IsOffDay == false).Select(x => x.Date.Date).ToList();
        var holidays = holidayDetails.Where(x => x.IsOffDay == true).Select(x => x.Date.Date).ToList();
        // 按天循环处理（包含开始日和结束日）
        DateTime currentDate = start.Date;
        while (currentDate <= end.Date)
        {
            // 1. 判断当前日期是否为工作日
            bool isWorkDay = IsWorkDay(currentDate, inverseDays, holidays);
            if (!isWorkDay)
            {
                currentDate = currentDate.AddDays(1);
                continue;
            }

            // 2. 计算当天的工作时间窗口（9:30-19:00）
            DateTime dayStart = currentDate.AddHours(9).AddMinutes(30); // 9:30
            DateTime dayEnd = currentDate.AddHours(19); // 19:00

            // 3. 计算与输入时间范围的重叠部分
            DateTime periodStart = start > dayStart ? start : dayStart; // 取较晚的开始时间
            DateTime periodEnd = end < dayEnd ? end : dayEnd; // 取较早的结束时间

            // 4. 累加有效分钟数（若重叠则计算，否则为0）
            if (periodStart < periodEnd)
            {
                totalMinutes += (decimal)(periodEnd - periodStart).TotalMinutes;
            }

            // 处理下一天
            currentDate = currentDate.AddDays(1);
        }
        isWork = true;
        // 四舍五入保留2位小数
        return decimal.Round(totalMinutes, 2, MidpointRounding.AwayFromZero);
    }

    /// <summary>
    /// 判断指定日期是否为工作日
    /// </summary>
    /// <param name="date">待判断日期</param>
    /// <param name="inverseDays">所有调休日（仅日期部分）</param>
    /// <param name="holidays">节假日列表</param>
    /// <returns>是否为工作日</returns>
    private bool IsWorkDay(DateTime date, List<DateTime> inverseDays, List<DateTime> holidays)
    {
        DateTime dateOnly = date.Date;

        // 1. 调休日视为工作日（优先级最高）
        if (inverseDays.Contains(dateOnly))
            return true;

        // 2. 周末（周六、周日）视为非工作日
        if (date.DayOfWeek == DayOfWeek.Saturday || date.DayOfWeek == DayOfWeek.Sunday)
            return false;

        // 3. 检查是否为节假日
        bool isHoliday = holidays.Contains(dateOnly);
        if (isHoliday)
            return false;

        // 4. 其他情况为工作日
        return true;
    }

    private List<GroupBookingAggregateItemDto> GetDataList(List<GroupBookingAggregateItemDto> data, List<AreaDto> areaDatas, long areaItem)
    {
        var area = areaDatas.FirstOrDefault(x => x.Id == areaItem);
        var countryCodes = areaDatas.Where(x => x.Id == areaItem).Select(x => x.CountryCode).ToList();
        var provinceCodes = areaDatas.Where(x => x.Id == areaItem).Where(x => x.ProvinceCode != null).Select(x => x.ProvinceCode).ToList();
        var list = data.ToList();
        if (area.AreaSettingType == GroupBookingAreaSettingType.China)
        {
            list = list.Where(x => countryCodes.Contains(x.CountryCode))
                    .Where(x => provinceCodes.Contains(x.ProvinceCode))
                    .ToList();
        }
        else if (area.AreaSettingType == GroupBookingAreaSettingType.Other)
        {
            countryCodes = areaDatas.Where(x => x.AreaSettingType != GroupBookingAreaSettingType.Other)
                               .Select(x => x.CountryCode).ToList();
            provinceCodes = areaDatas.Where(x => x.AreaSettingType != GroupBookingAreaSettingType.Other && x.ProvinceCode != null)
                      .Select(x => x.ProvinceCode).ToList();
            if (provinceCodes.Any())
            {
                countryCodes = areaDatas.Where(x => x.AreaSettingType != GroupBookingAreaSettingType.Other
                        && x.AreaSettingType != GroupBookingAreaSettingType.China)
                                    .Select(x => x.CountryCode).ToList();
                var chinaCountryCode = areaDatas.Where(x => x.AreaSettingType == GroupBookingAreaSettingType.China)
                    .Select(x => x.CountryCode).FirstOrDefault();
                list = list.Where(x => !countryCodes.Contains(x.CountryCode))
                   .Where(x => !(provinceCodes.Contains(x.ProvinceCode) && x.CountryCode == chinaCountryCode))
                   .ToList();
            }
            else
            {
                list = list.Where(x => !countryCodes.Contains(x.CountryCode))
                    .ToList();
            }
        }
        else
        {
            list = list.Where(x => countryCodes.Contains(x.CountryCode))
                 .ToList();
        }
        return list;
    }


    private async Task<List<HolidayListOutput>> HolidayList()
    {
        // 因为有些节假日数据是跨年的，12 - 30、12 - 31有时候会跨年，放在下一年数据中了
        var year = DateTime.Now.Year;
        var years = new List<int> { year, year + 1 };
        var httpContent = new StringContent(JsonConvert.SerializeObject(years),
          Encoding.UTF8, "application/json");
        var response =
            await _httpClientFactory.InternalPostAsync<List<HolidayListOutput>>(
                requestUri: _servicesAddress.Resource_HolidayList(), httpContent: httpContent);
        return response;
    }

    #endregion
}

public record AreaGroupDataDto
{
    public int? CountryCode { get; set; }
    public int? ProvinceCode { get; set; }
    public int Count { get; set; }

    public decimal GMV { get; set; }

    public int DownPaymentCount { get; set; }

    public double TotalTimes { get; set; }
    public List<long?> AgencyIds { get; set; }
    public DateTime? Date { get; set; }
}