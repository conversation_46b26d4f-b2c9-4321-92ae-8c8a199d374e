using AutoMapper;
using Common.GlobalException;
using Contracts.Common.Notify.Messages;
using Contracts.Common.Order.DTOs.GroupBookingFinancialHandleOrder;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using Order.Api.Services.Interfaces;

namespace Order.Api.Services;

public class GroupBookingFinancialHandleOrderService : IGroupBookingFinancialHandleOrderService
{
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly ICapPublisher _capPublisher;

    public GroupBookingFinancialHandleOrderService(CustomDbContext dbContext,
        IMapper mapper,
        ICapPublisher capPublisher)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _capPublisher = capPublisher;
    }

    public async Task<long> Create(FinancialHandleOrderCreateInput input)
    {
        var payment = await _dbContext.GroupBookingOrderPayments
            .FirstOrDefaultAsync(s => s.Id == input.GroupBookingOrderPaymentId);

        if (payment.HandleOrderStatus == Contracts.Common.Order.Enums.GroupBookingFinancialHandleOrderStatus.Confirming)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var groupBookingOrder = await _dbContext.GroupBookingOrders
            .Where(s => s.Id == payment.GroupBookingOrderId)
            .FirstOrDefaultAsync();

        GroupBookingFinancialHandleOrder financialHandleOrder = new()
        {
            GroupBookingOrderPaymentId = input.GroupBookingOrderPaymentId,
            ApplicationFormId = groupBookingOrder.GroupBookingApplicationFormId,
            GroupBookingOrderId = payment.GroupBookingOrderId,
            AccountTime = input.AccountTime,
            AgencyId = input.AgencyId,
            AgencyName = input.AgencyName,
            Amount = input.Amount,
            CurrencyCode = input.CurrencyCode,
            PayType = input.PayType,
            Remark = input.Remark,
            TenantBankAccount = new TenantBankAccountInfo
            {
                Id = input.TenantBankAccount.Id,
                AccountName = input.TenantBankAccount.AccountName,
                AccountNo = input.TenantBankAccount.AccountNo,
                Address = input.TenantBankAccount.Address,
                BankCode = input.TenantBankAccount.BankCode,
                BankName = input.TenantBankAccount.BankName,
                BranchName = input.TenantBankAccount.BranchName,
                CurrencyCode = input.TenantBankAccount.CurrencyCode,
                OpeningBankCode = input.TenantBankAccount.OpeningBankCode,
                SwiftCode = input.TenantBankAccount.SwiftCode,
                TenantBankAccountType = input.TenantBankAccount.TenantBankAccountType,
            },
            Creator = input.Creator,
            CreatorId = input.CreatorId,
            Proof = input.Proof?.Any() is true ? string.Join(",", input.Proof!) : null,
            Status = Contracts.Common.Order.Enums.GroupBookingFinancialHandleOrderStatus.Confirming,
            CreateTime = DateTime.Now,
        };
      
        payment.HandleOrderStatus = financialHandleOrder.Status;

        financialHandleOrder.SetTenantId(groupBookingOrder.TenantId);
        await _dbContext.GroupBookingFinancialHandleOrders.AddAsync(financialHandleOrder);
        await _dbContext.SaveChangesAsync();

        await SendFinancialMessageAsync(financialHandleOrder);

        return financialHandleOrder.Id;
    }

    public async Task<PagingModel<FinancialHandleOrderSearchOutput>> Search(FinancialHandleOrderSearchInput input)
    {
        var paging = await _dbContext.GroupBookingFinancialHandleOrders
            .WhereIF(input.Id.HasValue, s => s.Id == input.Id!.Value)
            .WhereIF(input.GroupBookingOrderId.HasValue, s => s.GroupBookingOrderId == input.GroupBookingOrderId!.Value)
            .WhereIF(input.GroupBookingOrderPaymentId.HasValue, s => s.GroupBookingOrderPaymentId == input.GroupBookingOrderPaymentId!.Value)
            .WhereIF(input.Status.HasValue, s => s.Status == input.Status!.Value)
            .OrderByDescending(s => s.Id)
            .PagingAsync(input.PageIndex, input.PageSize,
            s => new FinancialHandleOrderSearchOutput
            {
                Id = s.Id,
                Status = s.Status,
                Amount = s.Amount,
                CurrencyCode = s.CurrencyCode,
                PayType = s.PayType,
                GroupBookingOrderId = s.GroupBookingOrderId,
                CreateTime = s.CreateTime,
                Creator = s.Creator,
                CreatorId = s.CreatorId,
                TenantBankAccount = new()
                {
                    Id = s.TenantBankAccount.Id,
                    BankName = s.TenantBankAccount.BankName,
                    AccountNo = s.TenantBankAccount.AccountNo,
                    AccountName = s.TenantBankAccount.AccountName,
                    Address = s.TenantBankAccount.Address,
                    BankCode = s.TenantBankAccount.BankCode,
                    BranchName = s.TenantBankAccount.BranchName,
                    CurrencyCode = s.TenantBankAccount.CurrencyCode,
                    OpeningBankCode = s.TenantBankAccount.OpeningBankCode,
                    SwiftCode = s.TenantBankAccount.SwiftCode,
                    TenantBankAccountType = s.TenantBankAccount.TenantBankAccountType,
                },
                AccountTime = s.AccountTime,
            });
        return paging;
    }

    public async Task<FinancialHandleOrderDetailOutput> Detail(FinancialHandleOrderDetailInput input)
    {
        var model = await _dbContext.GroupBookingFinancialHandleOrders
            .Where(s => s.Id == input.Id)
            .FirstOrDefaultAsync();
        var output = _mapper.Map<FinancialHandleOrderDetailOutput>(model);
        return output;
    }

    public async Task Handle(FinancialHandleOrderHandleInput input)
    {
        var model = await _dbContext.GroupBookingFinancialHandleOrders
           .Where(s => s.Id == input.Id)
           .FirstOrDefaultAsync();

        if (model.Status != Contracts.Common.Order.Enums.GroupBookingFinancialHandleOrderStatus.Confirming)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        model.Status = input.Status;
        model.FinishTime = DateTime.Now;
        model.Operator = input.Operator;
        model.OperatorId = input.OperatorId;
        model.RejectReason = input.RejectReason;

        var payment = await _dbContext.GroupBookingOrderPayments
            .FirstOrDefaultAsync(s => s.Id == model.GroupBookingOrderPaymentId);
        payment.HandleOrderStatus = model.Status;
        switch (model.Status)
        {
            case Contracts.Common.Order.Enums.GroupBookingFinancialHandleOrderStatus.Finished:
                //处理团房单支付成功状态
                await _capPublisher.PublishAsync(CapTopics.Order.GroupBookingOrderStatusChangeByPaySuccess,
                    new Contracts.Common.Order.Messages.OrderStatusChangeByPaySuccessMessage
                    {
                        OrderId = payment.Id,
                        PaymentType = Contracts.Common.Payment.Enums.PayType.Offline,
                        PayTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        PaymentExternalNo = model.Id.ToString(),
                    });
                break;
        }
        await _dbContext.SaveChangesAsync();
    }

    private async Task SendFinancialMessageAsync(GroupBookingFinancialHandleOrder handleOrder)
    {
        await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess, new NotifyMessageProcessMessage
        {
            TenantId = handleOrder.TenantId,
            SendToTheRole = Contracts.Common.Notify.Enums.SendToTheRole.TenantStaff,
            NotifyEventSubType = Contracts.Common.Notify.Enums.NotifyEventSubType.Financial_Offline_Payment_Review,
            NotifyMode = Contracts.Common.Notify.Enums.NotifyMode.DingTalkRobot,
            Variables = new
            {
                handleOrder.Creator,
                handleOrder.Amount,
                handleOrder.AccountTime,
                OrderId = handleOrder.GroupBookingOrderId,
            }
        });
    }
}
