using AutoMapper;
using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common.Inventory.Messages;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.MessageNotify;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Tenant.DTOs.AgencyLevelConfigHotel;
using Contracts.Common.Tenant.DTOs.AgencyLevelConfigOvertimeHotel;
using Contracts.Common.User.Messages;
using DotNetCore.CAP;
using Hangfire;
using HangfireClient.Jobs.Order;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Order.Api.Notification;
using Order.Api.Services.Interfaces;
using System.Net.Http;
using Order.Api.Extensions;
using Contracts.Common.Tenant.Enums;

namespace Order.Api.Services.OrderProcessing;

public class HotelOrderProcessingService : IOrderProcessingService
{
    private readonly CustomDbContext _dbContext;
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly IMapper _mapper;
    private readonly IMediator _mediator;
    private readonly ICapPublisher _capPublisher;
    private readonly IMessageNotifyService _messageNotifyService;
    private readonly IOrderShareInfoService _orderShareInfoService;

    public HotelOrderProcessingService(CustomDbContext dbContext,
        IBackgroundJobClient backgroundJobClient,
        IMapper mapper,
        IMediator mediator,
        ICapPublisher capPublisher,
        IMessageNotifyService messageNotifyService,
        IOrderShareInfoService orderShareInfoService)
    {
        _dbContext = dbContext;
        _backgroundJobClient = backgroundJobClient;
        _mapper = mapper;
        _mediator = mediator;
        _capPublisher = capPublisher;
        _messageNotifyService = messageNotifyService;
        _orderShareInfoService = orderShareInfoService;
    }

    public OrderType OrderType => OrderType.Hotel;

    public RefundOrderType RefundOrderType => RefundOrderType.Hotel;

    public async Task PaySuccessProcessing(BaseOrder baseOrder)
    {
        var baseOrderId = baseOrder.Id;
        //改变子单状态
        var hotelOrder = await _dbContext.HotelOrders
            .IgnoreQueryFilters()
            .Where(s => s.BaseOrderId == baseOrderId)
            .FirstOrDefaultAsync();
        //本地酒店自动确认非超卖
        hotelOrder.Status = hotelOrder.PriceStrategyIsAutoConfirm
            && hotelOrder.IsOverSaleable != true
            && hotelOrder.PriceStrategyType == Contracts.Common.Hotel.Enums.PriceStrategyType.None
        ? HotelOrderStatus.WaitingForCheckIn
        : HotelOrderStatus.WaitingForConfirm;
        hotelOrder.UpdateTime = DateTime.Now;
        if (hotelOrder.Status == HotelOrderStatus.WaitingForCheckIn)
            hotelOrder.ConfirmTime = DateTime.Now;
        //待入住 && 自动入住
        if (hotelOrder.Status == HotelOrderStatus.WaitingForCheckIn && hotelOrder.HotelIsAutoConfirmRoomStatus)
        {
            //入住日22:00后自动变为已入住
            var checkInTime = hotelOrder.CheckInDate.Date.AddHours(22);
            var ts = checkInTime.Subtract(DateTime.Now);
            _backgroundJobClient?.Schedule<IHotelOrderJob>(x => x.AutoCheckIn(hotelOrder.Id, hotelOrder.TenantId), ts);
        }
        var guests = await _dbContext.HotelOrderGuests.AsNoTracking()
            .IgnoreQueryFilters()
            .Where(x => x.HotelOrderId == hotelOrder.Id)
            .ToListAsync();

        //消息通知
        var orderNotifyDto = new OrderNotifyDto<HotelCreateOrderNotifyDto>()
        {
            BaseOrder = _mapper.Map<BaseOrderNotify>(baseOrder),
            NotifyDto = new HotelCreateOrderNotifyDto
            {
                HotelName = hotelOrder.HotelName,
                HotelRoomName = hotelOrder.HotelRoomName,
                PriceStrategyNumberOfBreakfast = hotelOrder.PriceStrategyNumberOfBreakfast,
                CheckInDate = hotelOrder.CheckInDate,
                CheckOutDate = hotelOrder.CheckOutDate,
                RoomsCount = hotelOrder.PriceStrategyRoomsCount,
                NightsCount = hotelOrder.PriceStrategyNightsCount,
                Status = hotelOrder.Status,
                Guests = guests.Select(x => x.GuestName).ToArray(),
                PriceStrategySupplierId = hotelOrder.PriceStrategySupplierId,
                BoardCodeType = hotelOrder.BoardCodeType,
                BoardCount = hotelOrder.BoardCount
            }
        };
        await _messageNotifyService.HotelCreateOrderNotify(orderNotifyDto);

        //待入住 已确认
        if (hotelOrder.Status == HotelOrderStatus.WaitingForCheckIn)
        {
            OrderNotifyDto<HotelOrderConfirmNotifyDto> orderConfirmNotifyDto = new()
            {
                BaseOrder = _mapper.Map<BaseOrderNotify>(baseOrder),
                NotifyDto = new HotelOrderConfirmNotifyDto
                {
                    HotelName = baseOrder.ResourceName,
                    HotelRoomName = baseOrder.ProductName,
                    SkuName = baseOrder.ProductSkuName,
                    CheckInDate = hotelOrder.CheckInDate,
                    CheckOutDate = hotelOrder.CheckOutDate,
                    NightsCount = hotelOrder.PriceStrategyNightsCount,
                    NumberOfBreakfast = hotelOrder.PriceStrategyNumberOfBreakfast,
                    RoomsCount = hotelOrder.PriceStrategyRoomsCount,
                    Guests = guests.Select(x => x.GuestName).ToArray(),
                    IsGroupBooking = hotelOrder.IsGroupBooking,
                    ConfirmCode = hotelOrder.ConfirmCode,
                    BoardCount = hotelOrder.BoardCount,
                    BoardCodeType = hotelOrder.BoardCodeType
                }
            };
            await _messageNotifyService.HotelOrderConfirmNotify(orderConfirmNotifyDto);
        }

        //达人奖金预发放
        var totalSalePrice = await _dbContext.HotelOrderCalendarPrices
            .Where(x => x.HotelOrderId == hotelOrder.Id)
            .SumAsync(x => x.SalePrice);
        var bonusPreReleaseSubOrder = new SubOrder
        {
            ProductName = hotelOrder.HotelName,
            ProductSkuName = $"{hotelOrder.HotelRoomName}({hotelOrder.PriceStrategyName})",
            Price = totalSalePrice,
            Quantity = hotelOrder.PriceStrategyRoomsCount,
            SkuId = hotelOrder.PriceStrategyId,
            SubOrderId = hotelOrder.Id
        };

        if (hotelOrder.IsDirect == true && baseOrder.AgencyId > 0)
        {
            var overtimeHotels = await _orderShareInfoService.GetLevelConfigOvertimeHotelByHotelIds(new AgencyLevelConfigOvertimeHotelInput()
            {
                AgencyId = baseOrder.AgencyId,
                HotelIds = new List<long>() { hotelOrder.HotelId },
                TenantId = hotelOrder.TenantId,
            });
            var overtimeHotel = overtimeHotels.FirstOrDefault(x => x.HotelId == hotelOrder.HotelId);
            if (overtimeHotel != null)
            {
                hotelOrder.IsOvertimeCompensation = true;
                hotelOrder.OvertimeDescription = overtimeHotel.Description;
            }
        }

        await _orderShareInfoService.OrderDarenPreRelease(baseOrder.Id, new SubOrder[] { bonusPreReleaseSubOrder });
        if (hotelOrder.SupplierApiType == Contracts.Common.Tenant.Enums.SupplierApiType.None)
        {
            //库存扣减
            await _capPublisher.PublishAsync(CapTopics.Inventory.DeductInventory,
                new DeductInventoryMessage
                {
                    TenantId = hotelOrder.TenantId,
                    OrderId = hotelOrder.Id
                });
        }
        else if (hotelOrder.SupplierApiType == SupplierApiType.GDS || hotelOrder.SupplierApiType == SupplierApiType.Youxia)
        {
            //发起预订
            await _capPublisher.PublishAsync(CapTopics.Order.HotelSupplierOrderCreate, new HotelSupplierOrderCreateMessage
            {
                BaseOrderId = hotelOrder.BaseOrderId,
                TenantId = hotelOrder.TenantId,
                SupplierApiType = hotelOrder.SupplierApiType,
            });
        }
        else
        {
            //api下单
            await _capPublisher.PublishAsync(CapTopics.Order.CreateHotelApiOrder, new CreateHotelApiOrderMessage
            {
                BaseOrderId = hotelOrder.BaseOrderId,
                TenantId = hotelOrder.TenantId,
            });
        }
        //订单状态变更通知
        await _mediator.Publish(new HotelOrderChangeNotification
        {
            TenantId = hotelOrder.TenantId,
            SellingChannel = baseOrder.SellingChannels,
            UserId = baseOrder.UserId,
            ChannelOrderNo = baseOrder.ChannelOrderNo,
            BaseOrderId = hotelOrder.BaseOrderId,//主单id
            OrderStatus = hotelOrder.Status,
            ConfirmCode = hotelOrder.ConfirmCode,
            ChangeType = HotelOrderChangeType.PaySuccess,
        });
    }

    public async Task<RefundSuccessProcessOutput> RefundSuccessProcessing(RefundOrder refundOrder)
    {
        var hotelOrder = await _dbContext.HotelOrders
            .IgnoreQueryFilters()
            .Where(s => s.BaseOrderId == refundOrder.BaseOrderId)
            .FirstOrDefaultAsync();
        if (hotelOrder.Status != HotelOrderStatus.Refunding)
            throw new BusinessException($"退款结果处理，订单状态不符，当前OrderStatus：{hotelOrder.Status}");

        hotelOrder.Status = HotelOrderStatus.Refunded;
        hotelOrder.UpdateTime = DateTime.Now;

        var baseOrder = await _dbContext.BaseOrders
             .IgnoreQueryFilters()
             .FirstAsync(s => s.Id == refundOrder.BaseOrderId);
        baseOrder.Status = BaseOrderStatus.Closed;
        baseOrder.UpdateTime = DateTime.Now;

        var guests = await _dbContext.HotelOrderGuests.AsNoTracking()
            .IgnoreQueryFilters()
            .Where(x => x.HotelOrderId == hotelOrder.Id)
            .ToListAsync();
        //订单消息通知
        OrderNotifyDto<HotelRefundSucceededNotifyDto> orderNotifyDto = new()
        {
            BaseOrder = _mapper.Map<BaseOrderNotify>(baseOrder),
            NotifyDto = new HotelRefundSucceededNotifyDto
            {
                HotelName = hotelOrder.HotelName,
                HotelRoomName = hotelOrder.HotelRoomName,
                CheckInDate = hotelOrder.CheckInDate,
                CheckOutDate = hotelOrder.CheckOutDate,
                PriceStrategyNumberOfBreakfast = hotelOrder.PriceStrategyNumberOfBreakfast,
                PriceStrategySupplierId = hotelOrder.PriceStrategySupplierId,
                PriceStrategyRoomsCount = hotelOrder.PriceStrategyRoomsCount,
                Guests = guests.Select(x => x.GuestName).ToArray(),
                RefundAmount = refundOrder.TotalAmount,
                BoardCount = hotelOrder.BoardCount,
                BoardCodeType = hotelOrder.BoardCodeType
            }
        };
        await _messageNotifyService.HotelRefundSucceededNotify(orderNotifyDto);

        //订单状态变更通知
        await _mediator.Publish(new HotelOrderChangeNotification
        {
            TenantId = hotelOrder.TenantId,
            SellingChannel = baseOrder.SellingChannels,
            UserId = baseOrder.UserId,
            ChannelOrderNo = baseOrder.ChannelOrderNo,
            BaseOrderId = hotelOrder.BaseOrderId,//主单id
            OrderStatus = hotelOrder.Status,
            ConfirmCode = hotelOrder.ConfirmCode,
            ChangeType = HotelOrderChangeType.RefundSuccess
        });
        return new RefundSuccessProcessOutput { BaseOrderId = baseOrder.Id, BaseOrderStatus = baseOrder.Status };
    }

}
