using AutoMapper;
using Common.GlobalException;
using Contracts.Common.Inventory.Messages;
using Contracts.Common.Marketing.Enums;
using Contracts.Common.Marketing.Messages;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.DTOs.MessageNotify;
using Contracts.Common.Order.DTOs.OrderLog;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Payment.Messages;
using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.Enums;
using Contracts.Common.Tenant.DTOs.AgencyRecencyOrder;
using Contracts.Common.User.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using EfCoreExtensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Order.Api.ConfigModel;
using Order.Api.Services.Interfaces;
using System.Linq.Expressions;
using Contracts.Common.Tenant.Messages;
using Contracts.Common.Tenant.Enums;

namespace Order.Api.Services;

public class BaseOrderService : IBaseOrderService
{
    private readonly CustomDbContext _dbContext;
    private readonly ICapPublisher _capPublisher;
    private readonly IMessageNotifyService _messageNotifyService;
    private readonly IMapper _mapper;
    private readonly IEnumerable<IOrderProcessingService> _orderProcessingServices;
    private readonly IOptions<SellingPlatformConfig> _sellingPlatformConfig;

    public BaseOrderService(
        CustomDbContext dbContext,
        ICapPublisher capPublisher,
        IMessageNotifyService messageNotifyService,
        IMapper mapper,
        IEnumerable<IOrderProcessingService> orderProcessingServices,
        IOptions<SellingPlatformConfig> sellingPlatformConfig)
    {
        _dbContext = dbContext;
        _capPublisher = capPublisher;
        _messageNotifyService = messageNotifyService;
        _mapper = mapper;
        _orderProcessingServices = orderProcessingServices;
        _sellingPlatformConfig = sellingPlatformConfig;
    }

    public async Task<PaymentInfoOutput> GetPaymentInfo(long orderId)
    {
        var result = await _dbContext.BaseOrders
            .Where(s => s.Id == orderId)
            .Select(s => new PaymentInfoOutput
            {
                BaseOrderId = s.Id,
                ProductName = s.ProductName,
                ProductSkuName = s.ProductSkuName,
                ResourceName = s.ResourceName,
                OrderType = s.OrderType,
                CurrencyCode = s.PaymentCurrencyCode,
                Amount = s.PaymentAmount,
                UserId = s.UserId,
                UserNickName = s.UserNickName,
                AgencyId = s.AgencyId,
                AgencyName = s.AgencyName,
                ContactsEmail = s.ContactsEmail,
                SellingChannels = s.SellingChannels,
                PaymentType = s.PaymentType,
                PaymentExternalNo = s.PaymentExternalNo,
                SellingPlatform = s.SellingPlatform,
                OrderStatus = GetOrderStatus(s.Status, s.DelayedPayStatus),
                CreateTime = s.CreateTime,
                DelayedPayDeadline = s.DelayedPayDeadline,
                ExpireStartTime = s.ExpireStartTime,
                BaseOrderStatus = s.Status,
                CommissionFee = s.CommissionFee,
                CostDiscountAmount = s.CostDiscountAmount,
            })
            .FirstOrDefaultAsync();
        ProductBusinessType? productBusinessType = null;
        switch (result.OrderType)
        {
            case OrderType.Ticket:
                var ticketOrder = await _dbContext.TicketOrders
                    .Where(x => x.BaseOrderId == orderId)
                    .Select(x => new
                    {
                        x.Id,
                        x.ProductTicketBusinessType
                    })
                    .FirstOrDefaultAsync();
                productBusinessType = ticketOrder.ProductTicketBusinessType switch
                {
                    TicketBusinessType.HotelPackages => ProductBusinessType.Ticket_HotelPackages,
                    TicketBusinessType.RoomVoucher => ProductBusinessType.Ticket_RoomVoucher,
                    TicketBusinessType.Catering => ProductBusinessType.Ticket_Catering,
                };
                result.SubOrderIds = new List<long> { ticketOrder.Id };
                break;
            case OrderType.Mail:
                var mailOrder = await _dbContext.MailOrders.AsNoTracking()
                    .Where(x => x.BaseOrderId == orderId)
                    .Select(x => new { x.Id })
                    .ToListAsync();
                productBusinessType = ProductBusinessType.Mail;
                result.SubOrderIds = mailOrder.Select(x => x.Id).ToList();
                break;
            case OrderType.Hotel:
                var hotelOrder = await _dbContext.HotelOrders.AsNoTracking()
                    .Where(x => x.BaseOrderId == orderId)
                    .Select(x => new { x.Id })
                    .FirstOrDefaultAsync();
                productBusinessType = ProductBusinessType.Hotel;
                result.SubOrderIds = new List<long> { hotelOrder.Id };
                break;
            case OrderType.ScenicTicket:
                {
                    var scenicTicketOrder = await _dbContext.Set<ScenicTicketOrder>()
                        .Where(x => x.BaseOrderId == orderId)
                        .Select(x => new
                        {
                            x.Id,
                            x.ScenicTicketsType
                        })
                        .FirstOrDefaultAsync();
                    productBusinessType = scenicTicketOrder.ScenicTicketsType switch
                    {
                        ScenicTicketsType.Reservation => ProductBusinessType.Scenic_BookingTicket,
                        ScenicTicketsType.PromissoryNote => ProductBusinessType.Scenic_PeakDayTicket,
                    };
                    result.SubOrderIds = new List<long> { scenicTicketOrder.Id };
                }
                break;
            case OrderType.TravelLineOrder:
                var travelLineOrder = await _dbContext.TravelLineOrder.AsNoTracking()
                    .Where(x => x.BaseOrderId == orderId)
                    .Select(x => new { x.Id })
                    .FirstOrDefaultAsync();
                productBusinessType = ProductBusinessType.TravelLine;
                result.SubOrderIds = new List<long> { travelLineOrder.Id };
                break;
            case OrderType.CarProduct:
                var carProductOrder = await _dbContext.CarProductOrders
                    .Where(x => x.BaseOrderId == orderId)
                    .Select(x => new { x.Id })
                    .FirstOrDefaultAsync();
                if (carProductOrder is not null)
                    result.SubOrderIds.Add(carProductOrder.Id);
                productBusinessType = ProductBusinessType.CarProduct;
                break;
        }
        result.ProductBusinessType = productBusinessType;
        return result;
    }

    private static OrderStatus GetOrderStatus(BaseOrderStatus baseOrderStatus, bool? delayedPayStatus)
    {
        //延时待支付
        if (delayedPayStatus.HasValue && (baseOrderStatus is BaseOrderStatus.UnFinished or BaseOrderStatus.Finished))
        {
            return delayedPayStatus.Value ? OrderStatus.Paid : OrderStatus.WaitingForPay;
        }

        return baseOrderStatus switch
        {
            BaseOrderStatus.WaitingForPay => OrderStatus.WaitingForPay,
            BaseOrderStatus.Closed => OrderStatus.Closed,
            _ => OrderStatus.Paid
        };
    }

    public async Task<IEnumerable<OrderLogsOutput>> OperatingRecords(long orderId)
    {
        var entities = await _dbContext.OrderLogs
            .Where(s => s.OrderId == orderId)
            .OrderByDescending(s => s.Id)
            .ToListAsync();
        var result = entities.Select(s =>
        {
            var log = new OrderLogsOutput
            {
                CreateTime = s.CreateTime,
                OperationType = s.OperationType,
                OrderLogType = s.OrderLogType,
                OperationRole = s.OperationRole,
                UserId = s.UserId,
                UserName = s.UserName
            };

            if (!string.IsNullOrEmpty(s.ExtensionData))
            {
                log.ExtensionData = JsonConvert.DeserializeObject<OrderLogExtensionDataDto>(s.ExtensionData);
            }

            return log;
        }).ToList();
        if (result.Any(x => x.OperationType == OrderOperationType.OrderDelayPaied))
        {
            var baseOrder = await _dbContext.BaseOrders
                .Where(s => s.Id == orderId)
                .Select(s => new
                {
                    s.Id,
                    s.PaymentAmount,
                    s.PaymentCurrencyCode,
                    s.PaymentType,
                    s.PaymentMode,
                    s.PaymentChannel,
                    s.DelayedPayStatus
                })
                .FirstOrDefaultAsync();
            if (baseOrder is not null)
            {
                var delayPaiedLog = result.First(x => x.OperationType == OrderOperationType.OrderDelayPaied);
                delayPaiedLog.OrderDelayPaiedLog = new()
                {
                    PaymentAmount = baseOrder.PaymentAmount,
                    PaymentCurrencyCode = baseOrder.PaymentCurrencyCode,
                    PaymentType = baseOrder.PaymentType,
                };
            }
        }
        return result;
    }

    #region [订阅] 订单支付成功（不含预约单）

    [UnitOfWork]
    public async Task OrderStatusChangeByPaySuccess(OrderStatusChangeByPaySuccessMessage receive)
    {
        var baseOrder = await _dbContext.BaseOrders
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(s => s.Id == receive.OrderId);
        if (baseOrder == null)
            throw new BusinessException("订单不存在");
        //; 订单无效支付退款 (如订单关闭、重复支付等场景)
        bool needUselessRefund = (baseOrder.Status != BaseOrderStatus.WaitingForPay && baseOrder.DelayedPayStatus is null)//非延时非待支付状态
            || baseOrder.Status == BaseOrderStatus.Closed//订单已关闭
            || (baseOrder.DelayedPayStatus is true && baseOrder.PaymentType != PayType.None);//延时订单已实付
        if (needUselessRefund)
        {
            //throw new BusinessException($"支付成功处理，订单状态不符，当前OrderStatus：{baseOrder.Status}");
            await _capPublisher.PublishDelayAsync(TimeSpan.FromSeconds(10),//延时10秒处理，避免订单状态未更新
                CapTopics.Payment.OrderPaymentUselessRefund, new OrderPaymentUselessRefundMessage
                {
                    OrderPaymentId = receive.OrderPaymentId,
                    OrderPaymentType = receive.OrderPaymentType,
                    OrderId = receive.OrderId,
                });
            return;
        }
        baseOrder.PaymentType = receive.PaymentType;
        baseOrder.PaymentChannel = receive.PaymentChannel;
        baseOrder.PaymentMode = receive.PaymentMode;
        baseOrder.PaymentExternalNo = receive.PaymentExternalNo;
        if (DateTime.TryParse(receive.PayTime, out var payTime))
            baseOrder.PayTime = payTime;
        //延时 已支付
        if (baseOrder.DelayedPayStatus is false)
            baseOrder.DelayedPayStatus = true;

        //常规订单处理逻辑 非延时支付订单
        if (baseOrder.DelayedPayStatus is null)
        {
            //修改主单信息
            baseOrder.Status = BaseOrderStatus.UnFinished;
            baseOrder.UpdateTime = DateTime.Now;
            //子单处理
            var orderProcessingService = _orderProcessingServices
                                             .FirstOrDefault(x => x.OrderType == baseOrder.OrderType)
                                         ?? throw new InvalidOperationException("找不到对应的订单处理服务");
            await orderProcessingService.PaySuccessProcessing(baseOrder);
        }
        else if (baseOrder.DelayedPayStatus == true)
        {
            //实付后恢复延时支付额度
            await _capPublisher.PublishAsync(CapTopics.Tenant.AgencyDelayedCreditOrderCancelRecord, new AgencyDelayedCreditOrderRestoreRecordMessage
            {
                CreditBusinessType = Contracts.Common.Tenant.Enums.DelayCreditBusinessType.OrderRealPaied,
                TenantId = baseOrder.TenantId,
                CancelAmount = baseOrder.PaymentAmount,
                OperationUser = new Contracts.Common.Tenant.DTOs.OperationUserDto()
                {
                    UserType = Contracts.Common.Tenant.Enums.UserType.None,
                },
                OrderId = baseOrder.Id,
                OrderType = baseOrder.OrderType,
                UniqueOrderId = baseOrder.Id
            });
        }

        if (baseOrder.OrderType is OrderType.ScenicTicket or OrderType.TravelLineOrder or OrderType.CarProduct)
        {
            var platformCommission =
                _sellingPlatformConfig?.Value?.Commissions?.FirstOrDefault(x =>
                    x.SellingPlatform == baseOrder.SellingPlatform);
            if (platformCommission is not null) //记录佣金
                baseOrder.CommissionFee = platformCommission.CommissionRate * baseOrder.PaymentAmount;
        }

        //日志
        var orderLog = new OrderLogs
        {
            OrderId = receive.OrderId,
            OperationRole = Contracts.Common.Order.Enums.UserType.None,
            OperationType =
                baseOrder.DelayedPayStatus is true ? OrderOperationType.OrderDelayPaied : OrderOperationType.Paid,
            OrderLogType = baseOrder.OrderType switch
            {
                OrderType.Mail => OrderLogType.Mail,
                OrderType.Ticket => OrderLogType.Ticket,
                OrderType.Hotel => OrderLogType.Hotel,
                OrderType.StoredValueCard => OrderLogType.StoredValueCard,
                OrderType.ScenicTicket => OrderLogType.ScenicTicket,
                OrderType.TravelLineOrder => OrderLogType.TravelLine,
                OrderType.CarHailing => OrderLogType.CarHailing,
                OrderType.CarProduct => OrderLogType.CarProduct,
                _ => OrderLogType.None,
            }
        };
        await _dbContext.AddAsync(orderLog);

        //消费赠送优惠券
        LimitProductType? limitProductType = baseOrder.OrderType switch
        {
            OrderType.Ticket => LimitProductType.Ticket,
            OrderType.Mail => LimitProductType.Mail,
            OrderType.Hotel => LimitProductType.Hotel,
            OrderType.ScenicTicket => LimitProductType.Scenic,
            OrderType.TravelLineOrder => LimitProductType.Line,
            _ => null
        };
        if (limitProductType is not null)
        {
            await _capPublisher.PublishAsync(CapTopics.Marketing.CouponActivityByConsumption, new
            {
                baseOrder.TenantId,
                BaseOrderId = baseOrder.Id,
                baseOrder.UserId,
                ProductType = limitProductType,
                ConsumeAmount = baseOrder.PaymentAmount//消费金额
            });
        }

        if (baseOrder.PaymentType == PayType.YeePay)
        {
            //收款流水
            await _capPublisher.PublishAsync(CapTopics.Payment.TenantReceiptFlow, new TenantReceiptFlowMessage
            {
                TenantId = baseOrder.TenantId,
                AgencyId = baseOrder.AgencyId,
                Amount = baseOrder.PaymentAmount,
                BusinessOrderId = baseOrder.Id,
                CreateTime = DateTime.Now,
                PayType = baseOrder.PaymentType,
                TenantReceiptFlowType = TenantReceiptFlowType.OrderPayment,
            });
        }
        //上送跟踪日志
        await PushTraceRecord(baseOrder.Id, baseOrder.OrderType, baseOrder.PaymentAmount, baseOrder.ResourceName, baseOrder.ProductName,
            TraceBehaviorType.Payment);

        _dbContext.SetTenantId(baseOrder.TenantId);
    }

    #endregion

    #region [订阅] 订单退款结果

    [UnitOfWork]
    public async Task RefundResult(RefundResultMessage receive)
    {
        var refundOrder = await _dbContext.RefundOrders
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(s => s.Id == receive.RefundOrderId);
        if (refundOrder.Status != RefundOrderStatus.Refunding && refundOrder.Status != RefundOrderStatus.RefundFailed)
            throw new BusinessException($"退款结果处理，订单状态不符，当前OrderStatus：{refundOrder.Status}");

        refundOrder.Status = receive.IsSuccess
            ? RefundOrderStatus.Refunded
            : RefundOrderStatus.RefundFailed;
        refundOrder.UpdateTime = DateTime.Now;
        refundOrder.FailedReason = receive?.FailedReason?.Length > 200
            ? receive.FailedReason[..200]
            : receive.FailedReason;
        refundOrder.ExtRefundNo = receive.ExtRefundNo;

        //退款失败，不做后续处理
        if (receive.IsSuccess is false)
            await _dbContext.SaveChangesAsync();
        else
            await RefundSuccessed(refundOrder);
    }

    /// <summary>
    /// 退款成功
    /// </summary>
    /// <param name="refundOrder"></param>
    /// <returns></returns>
    private async Task RefundSuccessed(RefundOrder refundOrder)
    {
        //1 日志
        var orderLog = new OrderLogs
        {
            OrderId = refundOrder.BaseOrderId,
            OperationRole = refundOrder.UserType,
            OperationType = OrderOperationType.RefundSuccessed,
            OrderLogType = refundOrder.OrderType switch
            {
                RefundOrderType.Mail => OrderLogType.Mail,
                RefundOrderType.Ticket => OrderLogType.Ticket,
                RefundOrderType.Hotel => OrderLogType.Hotel,
                RefundOrderType.ReservationOrder => OrderLogType.Reservation,
                RefundOrderType.StoredValueCardOrder => OrderLogType.StoredValueCard,
                RefundOrderType.ScenicTicket => OrderLogType.ScenicTicket,
                RefundOrderType.TravelLine => OrderLogType.TravelLine,
                RefundOrderType.CarHailing => OrderLogType.CarHailing,
                _ => OrderLogType.None,
            },
            UserName = refundOrder.UserName,
            UserId = refundOrder.UserId ?? 0,
        };
        await _dbContext.AddAsync(orderLog);

        var baseOrder = await _dbContext.BaseOrders
            .AsNoTracking()
            .IgnoreQueryFilters()
            .FirstAsync(s => s.Id == refundOrder.BaseOrderId);
        //2 订单处理
        var orderProcessingService = _orderProcessingServices
                        .Where(x => x.RefundOrderType == refundOrder.OrderType)
                        .First();
        var refundSuccessProcessOutput = await orderProcessingService.RefundSuccessProcessing(refundOrder);

        //3 库存退还
        await _capPublisher.PublishAsync(CapTopics.Inventory.RenewInventory,
            new RenewInventoryMessage
            {
                TenantId = refundOrder.TenantId,
                OrderId = refundOrder.SubOrdeId,
                Quantity = refundOrder.Quantity
            });

        //返还优惠券
        if (refundSuccessProcessOutput.BaseOrderStatus == BaseOrderStatus.Closed
            && baseOrder.DiscountAmount > 0)
        {
            var orderDiscount = await _dbContext.BaseOrderDiscounts.AsNoTracking()
                .IgnoreQueryFilters()
                .Where(x => x.DiscountType == OrderDiscountType.UserCoupon && x.BaseOrderId == refundSuccessProcessOutput.BaseOrderId)
                .FirstOrDefaultAsync();
            if (orderDiscount is not null)
            {
                await _capPublisher.PublishAsync(CapTopics.Marketing.UserCouponReturn, new UserCouponReturnMessage
                {
                    BaseOrderId = orderDiscount.BaseOrderId,
                    TenantId = orderDiscount.TenantId,
                });
            }
        }
        //订单关闭 发布达人奖金取消事件
        await _capPublisher.PublishAsync(CapTopics.User.BonusRelease,
            new BonusReleaseMessage
            {
                TenantId = baseOrder.TenantId,
                BaseOrderId = baseOrder.Id,
                UserNickName = baseOrder.UserNickName,
                Status = 2//1-完成 2-取消
            });

        string? productName = baseOrder.ProductName;
        string? skuName = baseOrder.ProductSkuName;

        switch (refundOrder.OrderType)
        {
            case RefundOrderType.ScenicTicket:
                productName = baseOrder?.ResourceName;
                skuName = baseOrder?.ProductName;
                break;
        }
        //退款成功消息通知
        OrderNotifyDto<OrderRefundSucceededNotifyDto> orderNotifyDto = new()
        {
            BaseOrder = _mapper.Map<BaseOrderNotify>(baseOrder),
            NotifyDto = new OrderRefundSucceededNotifyDto
            {
                OrderType = baseOrder.OrderType,
                RefundOrderId = refundOrder.Id,
                RefundAmount = refundOrder.TotalAmount,
                Quantity = refundOrder.Quantity,
                ProductName = productName,
                SkuName = skuName,
                UserType = refundOrder.UserType,
                Reason = refundOrder.Reason,
                RefundStatus = refundOrder.Status
            }
        };
        await _messageNotifyService.OrderRefundSucceededNotify(orderNotifyDto);

        //上送跟踪日志
        await PushTraceRecord(baseOrder.Id, baseOrder.OrderType, refundOrder.TotalAmount, baseOrder.ResourceName, baseOrder.ProductName,
            TraceBehaviorType.Refund);

        if (baseOrder.PaymentType == PayType.YeePay)
        {
            //收款流水
            await _capPublisher.PublishAsync(CapTopics.Payment.TenantReceiptFlow, new TenantReceiptFlowMessage
            {
                TenantId = baseOrder.TenantId,
                AgencyId = baseOrder.AgencyId,
                Amount = -refundOrder.TotalAmount,
                BusinessOrderId = refundOrder.Id,
                CreateTime = DateTime.Now,
                PayType = baseOrder.PaymentType,
                TenantReceiptFlowType = TenantReceiptFlowType.OrderPaymentRefund,
            });
        }

        _dbContext.SetTenantId(refundOrder.TenantId);
    }

    #endregion

    public async Task<PagingModel<SearchOrderByUserOutput>> SearchOrderByUser(long userId, SearchOrderByUserInput input)
    {
        var result = await _dbContext.BaseOrders
            .AsNoTracking()
            .Where(o => o.UserId == userId)
            .WhereIF(input.Status.HasValue, o => o.Status == input.Status.Value)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                o => o.ProductName.Contains(input.Keyword) || o.ResourceName.Contains(input.Keyword))
            .OrderByDescending(o => o.UpdateTime)
            .PagingAsync(input.PageIndex, input.PageSize, o => new SearchOrderByUserOutput
            {
                BaseOrderId = o.Id,
                OrderType = o.OrderType,
                ResourceName = o.ResourceName,
                ProductName = o.ProductName,
                ProductSkuName = o.ProductSkuName,
                Status = o.Status,
                TotalAmount = o.TotalAmount,
                PaymentAmount = o.PaymentAmount,
                DiscountAmount = o.DiscountAmount,
                CreateTime = o.CreateTime
            });
        //根据订单品类分组 获取详细信息
        var orders = result.Data.ToList();
        var groups = orders.GroupBy(o => o.OrderType).Select(o => new
        {
            OrderType = o.Key,
            BaseOrderIds = o.Select(s => s.BaseOrderId)
        });
        foreach (var group in groups)
        {
            var baseOrderIds = group.BaseOrderIds;
            List<SubOrderDetailOutput> subOrderDetails = new();
            switch (group.OrderType)
            {
                case OrderType.Ticket:
                    var ticketOrders = await _dbContext.TicketOrders
                        .Join(_dbContext.OrderPrices, x => x.Id, x => x.SubOrderId, (o, p) => new
                        {
                            o.Id,
                            o.BaseOrderId,
                            o.ProductTitle,
                            o.SkuName,
                            o.SkuImagePath,
                            p.Quantity,
                            o.ProductTicketBusinessType
                        })
                        .Where(s => baseOrderIds.Contains(s.BaseOrderId))
                        .ToListAsync();
                    foreach (var ticketOrder in ticketOrders)
                    {
                        var baseOrder = orders.FirstOrDefault(o => o.BaseOrderId == ticketOrder.BaseOrderId);
                        baseOrder.OrderDetails.Add(new SubOrderDetailOutput
                        {
                            OrderId = ticketOrder.Id,
                            ProductName = ticketOrder.ProductTitle,
                            ProductSkuName = ticketOrder.SkuName,
                            Quantity = ticketOrder.Quantity,
                            ImagePath = ticketOrder.SkuImagePath,
                            TickerOrderExt = new TickerOrderExt
                            {
                                ProductTicketBusinessType = ticketOrder.ProductTicketBusinessType
                            }
                        });
                    }
                    break;
                case OrderType.Mail:
                    var mailOrders = await _dbContext.MailOrders
                        .Join(_dbContext.OrderPrices, x => x.Id, x => x.SubOrderId, (o, p) => new
                        {
                            o.Id,
                            o.BaseOrderId,
                            o.ProductName,
                            o.ProductSkuName,
                            o.ProductImagePath,
                            p.Quantity
                        })
                        .Where(s => baseOrderIds.Contains(s.BaseOrderId))
                        .ToListAsync();
                    foreach (var mailOrder in mailOrders)
                    {
                        var baseOrder = orders.FirstOrDefault(o => o.BaseOrderId == mailOrder.BaseOrderId);
                        baseOrder.OrderDetails.Add(new SubOrderDetailOutput
                        {
                            OrderId = mailOrder.Id,
                            ProductName = mailOrder.ProductName,
                            ProductSkuName = mailOrder.ProductSkuName,
                            Quantity = mailOrder.Quantity,
                            ImagePath = mailOrder.ProductImagePath
                        });
                    }
                    break;
                case OrderType.Hotel:
                    var hotelOrders = await _dbContext.HotelOrders
                        .AsNoTracking()
                        .Where(s => baseOrderIds.Contains(s.BaseOrderId))
                        .ToListAsync();
                    foreach (var hotelOrder in hotelOrders)
                    {
                        var baseOrder = orders.FirstOrDefault(o => o.BaseOrderId == hotelOrder.BaseOrderId);
                        baseOrder.OrderDetails.Add(new SubOrderDetailOutput
                        {
                            OrderId = hotelOrder.Id,
                            ProductName = hotelOrder.HotelRoomName,
                            ProductSkuName = hotelOrder.PriceStrategyName,
                            Quantity = hotelOrder.PriceStrategyRoomsCount,
                            ImagePath = hotelOrder.HotelRoomImgPath,//房型图片
                            HotelOrderExt = new SubHotelOrderExt
                            {
                                CheckInDate = hotelOrder.CheckInDate,
                                CheckOutDate = hotelOrder.CheckOutDate,
                                Nights = hotelOrder.PriceStrategyNightsCount,
                                Rooms = hotelOrder.PriceStrategyRoomsCount
                            }
                        });
                    }
                    break;
                case OrderType.StoredValueCard:
                    {
                        var storedValueCardOrders = await _dbContext.StoredValueCardOrders
                            .AsNoTracking()
                            .Where(s => baseOrderIds.Contains(s.BaseOrderId))
                            .ToListAsync();
                        foreach (var storedValueCardOrder in storedValueCardOrders)
                        {
                            var baseOrder = orders.FirstOrDefault(o => o.BaseOrderId == storedValueCardOrder.BaseOrderId);
                            baseOrder.OrderDetails.Add(new SubOrderDetailOutput
                            {
                                OrderId = storedValueCardOrder.Id,
                                ImagePath = storedValueCardOrder.StoredValueCardInfo.Cover,
                                ProductName = storedValueCardOrder.StoredValueCardInfo.CardName,
                                Quantity = 1,
                                ProductSkuName = $"{storedValueCardOrder.StoredValueCardGearInfo.Price:N2}"
                            });
                        }
                    }
                    break;
                case OrderType.ScenicTicket:
                    {
                        var scenicTicketsOrders = await _dbContext.Set<ScenicTicketOrder>()
                            .Join(_dbContext.OrderPrices, x => x.Id, x => x.SubOrderId, (scenicTicketOrder, orderPrice) => new
                            {
                                scenicTicketOrder.Id,
                                scenicTicketOrder.BaseOrderId,
                                scenicTicketOrder.ScenicSpotPhotoPath,
                                scenicTicketOrder.ValidityBegin,
                                scenicTicketOrder.ValidityEnd,
                                scenicTicketOrder.ScenicTicketsType,
                                orderPrice.Quantity,
                            })
                            .Where(x => baseOrderIds.Contains(x.BaseOrderId))
                            .ToListAsync();
                        foreach (var scenicTicketOrder in scenicTicketsOrders)
                        {
                            var baseOrder =
                                orders.FirstOrDefault(o => o.BaseOrderId == scenicTicketOrder.BaseOrderId);
                            baseOrder.OrderDetails.Add(new SubOrderDetailOutput
                            {
                                OrderId = scenicTicketOrder.Id,
                                ProductName = baseOrder.ProductName,
                                ProductSkuName = baseOrder.ProductSkuName,
                                Quantity = scenicTicketOrder.Quantity,
                                ImagePath = scenicTicketOrder.ScenicSpotPhotoPath,
                                ScenicTickerOrderExt = new ScenicTickerOrderExt
                                {
                                    ValidityBegin = scenicTicketOrder.ValidityBegin,
                                    ValidityEnd = scenicTicketOrder.ValidityEnd,
                                    ScenicTicketsType = scenicTicketOrder.ScenicTicketsType
                                }
                            });
                        }
                    }
                    break;
                case OrderType.TravelLineOrder:
                    {
                        var travelLineOrders = await _dbContext.Set<TravelLineOrder>()
                            .AsNoTracking()
                            .Where(x => baseOrderIds.Contains(x.BaseOrderId))
                            .ToListAsync();
                        foreach (var travelLineOrder in travelLineOrders)
                        {
                            var baseOrder =
                                orders.FirstOrDefault(o => o.BaseOrderId == travelLineOrder.BaseOrderId);
                            baseOrder.OrderDetails.Add(new SubOrderDetailOutput
                            {
                                OrderId = travelLineOrder.Id,
                                ProductName = baseOrder.ProductName,
                                ProductSkuName = baseOrder.ProductSkuName,
                                ImagePath = travelLineOrder.ImagePath,
                                TravelLineProductOrderExt = new TravelLineProductOrderExt
                                {
                                    TravelBeginDate = travelLineOrder.TravelBeginDate,
                                    TravelEndDate = travelLineOrder.TravelEndDate,
                                    Days = travelLineOrder.Days,
                                    Nights = travelLineOrder.Nights
                                }
                            });
                        }
                    }
                    break;
                case OrderType.CarHailing:
                    {
                        var carHailingOrders = await _dbContext.Set<CarHailingOrder>()
                                .AsNoTracking()
                                .Where(x => baseOrderIds.Contains(x.BaseOrderId))
                                .ToListAsync();
                        foreach (var carHailingOrder in carHailingOrders)
                        {
                            var baseOrder = orders.FirstOrDefault(o => o.BaseOrderId == carHailingOrder.BaseOrderId);
                            baseOrder.OrderDetails.Add(new SubOrderDetailOutput
                            {
                                OrderId = carHailingOrder.Id,
                                ProductName = carHailingOrder.ProductTitle,
                                ProductSkuName = carHailingOrder.SkuName,
                                ImagePath = carHailingOrder.Image,
                                Quantity = carHailingOrder.Quantity
                            });
                        }
                    }
                    break;
            }
        }
        result.Data = orders;

        return result;
    }

    [UnitOfWork]
    public async Task Cancel(CancelOrderInput input)
    {
        long baseOrderId = input.BaseOrderId;
        OperationUserDto operationUser = input.OperationUser;
        var baseOrder = await _dbContext.BaseOrders
            .Where(s => s.Id == baseOrderId)
            .WhereIF(operationUser.UserType == Contracts.Common.Order.Enums.UserType.Customer, x => x.UserId == operationUser.UserId)
            .WhereIF(operationUser.UserType == Contracts.Common.Order.Enums.UserType.Agency, x => x.AgencyId == operationUser.AgencyId)
            .FirstOrDefaultAsync();
        if (baseOrder.Status != BaseOrderStatus.WaitingForPay)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        //修改主单状态
        baseOrder.Status = BaseOrderStatus.Closed;
        baseOrder.UpdateTime = DateTime.Now;

        //订单日志
        var orderLog = new OrderLogs
        {
            OrderId = baseOrderId,
            OperationRole = operationUser.UserType,
            OperationType = OrderOperationType.Closed,
            OrderLogType = baseOrder.OrderType switch
            {
                OrderType.Mail => OrderLogType.Mail,
                OrderType.Ticket => OrderLogType.Ticket,
                OrderType.Hotel => OrderLogType.Hotel,
                OrderType.StoredValueCard => OrderLogType.StoredValueCard,
                OrderType.ScenicTicket => OrderLogType.ScenicTicket,
                OrderType.TravelLineOrder => OrderLogType.TravelLine,
                OrderType.CarHailing => OrderLogType.CarHailing,
                OrderType.CarProduct => OrderLogType.CarProduct,
                _ => throw new NotImplementedException()
            },
            UserId = operationUser.UserId,
            UserName = operationUser.Name,
        };
        await _dbContext.AddAsync(orderLog);

        //子单状态
        #region 
        var gdsSupplierApiTypes = new SupplierApiType[] { SupplierApiType.GDS, SupplierApiType.Youxia };
        List<long> subOrderIds = new();
        switch (baseOrder.OrderType)
        {
            case OrderType.Mail:
                var mailOrders = await _dbContext.MailOrders
                    .Where(s => s.BaseOrderId == baseOrderId)
                    .ToListAsync();
                foreach (var mailOrder in mailOrders)
                {
                    mailOrder.Status = MailOrderStatus.Closed;
                    mailOrder.UpdateTime = DateTime.Now;
                }
                subOrderIds.AddRange(mailOrders.Select(s => s.Id));
                break;
            case OrderType.Ticket:
                var ticketOrderId = await _dbContext.TicketOrders
                    .Where(s => s.BaseOrderId == baseOrderId)
                    .Select(s => s.Id)
                    .FirstAsync();
                subOrderIds.Add(ticketOrderId);
                break;
            case OrderType.Hotel:
                var hotelOrder = await _dbContext.HotelOrders
                    .Where(s => s.BaseOrderId == baseOrderId)
                    .FirstAsync();
                hotelOrder.Status = HotelOrderStatus.Closed;
                hotelOrder.UpdateTime = DateTime.Now;
                if (!gdsSupplierApiTypes.Contains(hotelOrder.SupplierApiType))
                    subOrderIds.Add(hotelOrder.Id);
                break;
            case OrderType.StoredValueCard:
                var storedValueCardOrder = await _dbContext.StoredValueCardOrders
                     .Where(x => x.BaseOrderId == baseOrderId)
                     .FirstOrDefaultAsync();
                storedValueCardOrder.UpdateTime = DateTime.Now;
                storedValueCardOrder.Status = StoredValueCardOrderStatus.Closed;
                break;
            case OrderType.ScenicTicket:
                var scenicTicketOrderId = await _dbContext.ScenicTicketOrders
                    .Where(x => x.BaseOrderId == baseOrderId)
                    .Select(x => x.Id)
                    .FirstAsync();
                subOrderIds.Add(scenicTicketOrderId);
                break;
            case OrderType.TravelLineOrder:
                var travelLineOrder = await _dbContext.TravelLineOrder
                    .Where(x => x.BaseOrderId == baseOrderId)
                    .FirstAsync();
                travelLineOrder.Status = TravelLineOrderStatus.Canceled;
                subOrderIds.Add(travelLineOrder.Id);
                break;
            case OrderType.CarHailing:
                var carHailingOrder = await _dbContext.Set<CarHailingOrder>()
                    .Where(x => x.BaseOrderId == baseOrderId)
                    .FirstAsync();
                carHailingOrder.Status = CarHailingOrderStatus.Canceled;
                subOrderIds.Add(carHailingOrder.Id);
                break;
            case OrderType.CarProduct:
                var carProductOrder = await _dbContext.CarProductOrders
                    .Where(x => x.BaseOrderId == baseOrderId)
                    .FirstAsync();
                carProductOrder.Status = CarProductOrderStatus.Closed;
                carProductOrder.FinishTime = DateTime.Now;
                carProductOrder.UpdateTime = DateTime.Now;
                subOrderIds.Add(carProductOrder.Id);
                break;
            default: throw new NotImplementedException();
        }

        #endregion

        //子单价格类型查询
        var orderPrices = await _dbContext.OrderPrices
            .Where(x => subOrderIds.Contains(x.SubOrderId))
            .Select(x => new { x.SubOrderId, x.PriceType })
            .ToListAsync();

        var orderCommissions = await _dbContext.OrderCommission
          .Where(x => x.BaseOrderId == baseOrder.Id)
          .ToListAsync();
        if (orderCommissions.Any())
        {
            orderCommissions.ForEach(x =>
            {
                x.AgencyCommisionStatus = CommisionStatus.Cancel;
                x.SupplierCommisionStatus = CommisionStatus.Cancel;
            });

        }
        //解冻库存事件 && 提交事务
        foreach (var subOrderId in subOrderIds)
        {
            await _capPublisher.PublishAsync(CapTopics.Inventory.UnfrozenInventory, new UnfrozenInventoryMessage
            {
                TenantId = baseOrder.TenantId,
                OrderId = subOrderId
            });

        }
        foreach (var orderPrice in orderPrices)
        {
            //限时抢购类的订单
            if (orderPrice.PriceType == OrderPriceType.FlashSalePrice)
            {
                await _capPublisher.PublishAsync(CapTopics.Marketing.FlashSaleOrderCancel, new FlashSaleOrderCancelMessage
                {
                    TenantId = baseOrder.TenantId,
                    OrderId = orderPrice.SubOrderId
                });
            }
        }

        if (baseOrder.DiscountAmount > 0)//存在优惠金额
        {
            //返还优惠券
            await _capPublisher.PublishAsync(CapTopics.Marketing.UserCouponReturn, new UserCouponReturnMessage
            {
                BaseOrderId = baseOrder.Id,
                TenantId = baseOrder.TenantId,
            });
        }

        //订单关闭 发布达人奖金取消事件
        await _capPublisher.PublishAsync(CapTopics.User.BonusRelease,
            new BonusReleaseMessage
            {
                TenantId = baseOrder.TenantId,
                BaseOrderId = baseOrder.Id,
                UserNickName = baseOrder.UserNickName,
                Status = 2//1-完成 2-取消
            });

        //上送跟踪日志
        await PushTraceRecord(baseOrder.Id, baseOrder.OrderType, null, baseOrder.ResourceName, baseOrder.ProductName,
            TraceBehaviorType.CancelOrder);
    }


    /// <summary>
    /// 查询用户不同订单状态的订单数量
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    public async Task<List<GetBaseOrderStatusCountOutPut>> GetBaseOrderStatusCount(GetBaseOrderStatusCountInput input)
    {
        var statusCount = await _dbContext.BaseOrders.AsNoTracking()
            .WhereIF(input.UserId is > 0, x => x.UserId == input.UserId!.Value)
            .WhereIF(input.AgencyId is > 0, x => x.AgencyId == input.AgencyId!.Value)
            .GroupBy(x => x.Status)
            .Select(x => new GetBaseOrderStatusCountOutPut
            {
                Status = x.Key,
                Count = x.Count()
            }).ToListAsync();
        return statusCount;
    }

    public async Task<IList<OrderAmountInfoOutput>> GetOrderAmountInfos(OrderAmountInfoInput input)
    {
        List<OrderAmountInfoOutput> outputs = new();
        var baseOrderIds = input.OrderInfos.Where(x => x.OrderPaymentType == 1).Select(x => x.OrderId);
        if (baseOrderIds.Any())
        {
            var orderAmountInfos = await _dbContext.BaseOrders
                .Where(x => baseOrderIds.Contains(x.Id))
                .Select(x => new OrderAmountInfoOutput
                {
                    OrderId = x.Id,
                    AgencyName = x.AgencyName,
                    TotalAmount = x.TotalAmount,
                    DiscountAmount = x.DiscountAmount
                })
                .ToListAsync();
            outputs.AddRange(orderAmountInfos);
        }
        var reservationOrderIds = input.OrderInfos.Where(x => x.OrderPaymentType == 2).Select(x => x.OrderId);
        if (reservationOrderIds.Any())
        {

            var reservationOrderAmountInfos = await _dbContext.ReservationOrders
                .Join(_dbContext.BaseOrders.IgnoreQueryFilters(), r => r.BaseOrderId, b => b.Id,
                (r, b) => new { ReservationOrder = r, BaseOrder = b })
                .Where(x => reservationOrderIds.Contains(x.ReservationOrder.Id))
                .Select(x => new OrderAmountInfoOutput
                {
                    OrderId = x.ReservationOrder.Id,
                    AgencyName = x.BaseOrder.AgencyName,
                    TotalAmount = x.ReservationOrder.PaymentAmount,
                    DiscountAmount = 0
                })
                .ToListAsync();
            outputs.AddRange(reservationOrderAmountInfos);
        }
        return outputs;
    }

    /// <summary>
    /// 查询用户消费统计分页数据
    /// 待完成/已完成订单 - 对应的退款订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<PagingModel<SearchUserConsumptionOutput>> SearchConsumptionStatistic(
        SearchUserConsumptionPageInput input)
    {
        var baseOrderQueryable = _dbContext.BaseOrders.AsNoTracking()
            .Where(x => x.SellingPlatform != SellingPlatform.System)
            .Where(x => x.Status == BaseOrderStatus.UnFinished || x.Status == BaseOrderStatus.Finished)
            .WhereIF(input.BeginConsumptionTime.HasValue,
                x => x.CreateTime >= input.BeginConsumptionTime.Value)
            .WhereIF(input.EndConsumptionTime.HasValue,
                x => x.CreateTime < input.EndConsumptionTime.Value.AddDays(1));

        var refundOrderQueryable =
            _dbContext.RefundOrders.AsNoTracking().Where(x => x.Status == RefundOrderStatus.Refunded);

        var query = baseOrderQueryable
            .GroupJoin(refundOrderQueryable,
                b => b.Id,
                r => r.BaseOrderId,
                (b, r) => new { b, r })
            .SelectMany(x => x.r.DefaultIfEmpty(), (b, r) => new { b.b, r })
            .GroupBy(x => x.b.UserId)
            .Select(x => new
            {
                CustomerUserId = x.Key,
                ConsumptionAmout = x.Sum(g => g.b.PaymentAmount) - x.Sum(g => g.r.TotalAmount),
                ConsumptionCount = x.Count(),
                LastConsumptionTime = x.Max(g => g.b.CreateTime)
            })
            .WhereIF(input.MinConsumptionAmount is > 0,
                x => x.ConsumptionAmout >= input.MinConsumptionAmount)
            .WhereIF(input.MaxConsumptionAmount is > 0,
                x => x.ConsumptionAmout < input.MaxConsumptionAmount)
            .WhereIF(input.MinConsumptionCount is > 0, x => x.ConsumptionCount >= input.MinConsumptionCount)
            .WhereIF(input.MaxConsumptionCount is > 0, x => x.ConsumptionCount < input.MaxConsumptionCount)
            .OrderByDescending(x => x.ConsumptionAmout);

        return await query.PagingAsync(input.PageIndex, input.PageSize,
            x => new SearchUserConsumptionOutput
            {
                CustomerUserId = x.CustomerUserId,
                ConsumptionAmount = x.ConsumptionAmout,
                ConsumptionCount = x.ConsumptionCount,
                LastConsumptionTime = x.LastConsumptionTime
            });
    }

    /// <summary>
    /// 通过用户id查询用户的消费累积数据
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    public async Task<IEnumerable<SearchUserConsumptionOutput>> GetConsumptionStatisticByUserIds(List<long> ids)
    {
        var baseOrderQueryable = _dbContext.BaseOrders.AsNoTracking()
            .Where(x => x.Status == BaseOrderStatus.UnFinished || x.Status == BaseOrderStatus.Finished)
            .Where(x => ids.Contains(x.UserId));
        var refundOrderQueryable =
            _dbContext.RefundOrders.AsNoTracking().Where(x => x.Status == RefundOrderStatus.Refunded);

        var queryable = baseOrderQueryable
            .GroupJoin(refundOrderQueryable,
                b => b.Id,
                r => r.BaseOrderId,
                (b, r) => new { b, r })
            .SelectMany(x => x.r.DefaultIfEmpty(), (b, r) => new { b.b, r })
            .GroupBy(x => x.b.UserId)
            .Select(x => new SearchUserConsumptionOutput
            {
                CustomerUserId = x.Key,
                ConsumptionAmount = x.Sum(g => g.b.PaymentAmount) - x.Sum(g => g.r.TotalAmount),
                ConsumptionCount = x.Count(),
                LastConsumptionTime = x.Max(g => g.b.CreateTime)
            });

        return await queryable.ToListAsync();
    }

    public async Task<CheckChannelOrderOutput> CheckChannelOrder(CheckChannelOrderInput input)
    {
        var result = new CheckChannelOrderOutput();

        //多渠道单号以逗号分割,需要处理成数组
        var channelOrderNos = input.ChannelOrderNo.Split(',').ToList();
        Expression<Func<BaseOrder, bool>> predicate = x => false;
        predicate = channelOrderNos.Aggregate(predicate,
            (current, channelOrderNo) => current.Or(x => x.ChannelOrderNo.Contains(channelOrderNo)));

        //查询订单
        var baseOrders = await _dbContext.BaseOrders
            .AsNoTracking()
            .WhereIF(input.AgencyId.HasValue, x => x.AgencyId == input.AgencyId)
            .Where(predicate)
            .ToListAsync();
        result.IsExists = baseOrders.Any();

        if (baseOrders.Any() is false)
            return result;

        foreach (var baseOrder in baseOrders)
        {
            result.RelatedBaseOrderInfos.Add(new RelatedBaseOrderInfo
            {
                BaseOrderId = baseOrder.Id,
                AgencyId = baseOrder.AgencyId,
                ChannelOrderNo = baseOrder.ChannelOrderNo,
                ChannelOrderNoList = baseOrder.ChannelOrderNo.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries).ToList(),
                OrderType = baseOrder.OrderType,
                SellingPlatform = baseOrder.SellingPlatform,
                SellingChannels = baseOrder.SellingChannels,
                OrderCategory = baseOrder.OrderCategory
            });
        }

        return result;
    }

    public async Task<CheckChannelOrderOutput> CheckChannelOrderAbnormalOrder(CheckChannelOrderInput input)
    {
        var result = new CheckChannelOrderOutput();

        //多渠道单号以逗号分割,需要处理成数组
        var channelOrderSegments = input.ChannelOrderNo
            .Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
            .ToList();
        Expression<Func<OpenChannelSyncFailOrder, bool>> predicate = x => false;
        predicate = channelOrderSegments.Aggregate(predicate,
            (current, channelOrderNo) => current.Or(x => x.ChannelOrderNo.Contains(channelOrderNo)));

        //查询订单
        var openChannelSyncFailOrders = await _dbContext.OpenChannelSyncFailOrders
            .AsNoTracking()
            .WhereIF(input.AgencyId.HasValue, x => x.AgencyId == input.AgencyId)
            .Where(predicate)
            .ToListAsync();

        foreach (var openChannelSyncFailOrder in openChannelSyncFailOrders)
        {
            result.RelatedBaseOrderInfos.Add(new RelatedBaseOrderInfo
            {
                ChannelOrderNo = openChannelSyncFailOrder.ChannelOrderNo,
                ChannelOrderNoList = openChannelSyncFailOrder.ChannelOrderNo.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries).ToList(),
                OrderType = openChannelSyncFailOrder.OrderType,
                SellingPlatform = openChannelSyncFailOrder.SellingPlatform,
                SellingChannels = openChannelSyncFailOrder.SellingChannels,
                OrderCategory = OrderCategory.RegularOrder,
                IsAbnormalOrder = true,
                AbnormalOrderId = openChannelSyncFailOrder.Id
            });
        }

        //取出未匹配到的渠道单号
        var ticketCombinationAbnormalOrderNos = channelOrderSegments.Except(result.RelatedBaseOrderInfos.SelectMany(x => x.ChannelOrderNoList)).ToList();
        if (ticketCombinationAbnormalOrderNos.Any())
        {
            Expression<Func<TicketsCombinationOrder, bool>> combinationPredicate = x => false;
            combinationPredicate = ticketCombinationAbnormalOrderNos.Aggregate(combinationPredicate,
                (current, channelOrderNo) => current.Or(x => x.ChannelOrderNo.Contains(channelOrderNo)));
            //查询订单
            var ticketsCombinationOrders = await _dbContext.TicketsCombinationOrders
                .AsNoTracking()
                .WhereIF(input.AgencyId.HasValue, x => x.AgencyId == input.AgencyId)
                .Where(combinationPredicate)
                .ToListAsync();

            foreach (var ticketsCombinationOrder in ticketsCombinationOrders)
            {
                result.RelatedBaseOrderInfos.Add(new RelatedBaseOrderInfo
                {
                    ChannelOrderNo = ticketsCombinationOrder.ChannelOrderNo,
                    ChannelOrderNoList = ticketsCombinationOrder.ChannelOrderNo.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries).ToList(),
                    OrderType = OrderType.ScenicTicket,
                    OrderCategory = OrderCategory.CombinationOrder,
                    IsAbnormalOrder = true,
                    AbnormalOrderId = ticketsCombinationOrder.Id
                });
            }
        }

        result.IsExists = result.RelatedBaseOrderInfos.Any();
        return result;
    }

    public async Task<CheckSupplierOrderOutput> CheckSupplierOrder(CheckSupplierOrderInput input)
    {
        var result = new CheckSupplierOrderOutput();
        var orderInfo = await _dbContext.AggregateSupplierOrderId.AsNoTracking()
            .Where(x => !string.IsNullOrEmpty(x.SupplierOrderId))
            .Where(x => input.SupplierOrderIdList.Contains(x.SupplierOrderId!))
            .ToListAsync();

        foreach (var supplierOrderId in input.SupplierOrderIdList)
        {
            var orderItems = orderInfo.Where(x => x.SupplierOrderId == supplierOrderId)
                .Select(x => new RelatedSupplierBaseOrderInfo
                {
                    BaseOrderId = x.BaseOrderId,
                    SupplierOrderId = x.SupplierOrderId
                })
                .ToList();
            result.RelatedBaseOrderInfos.AddRange(orderItems);
        }

        return result;
    }

    /// <summary>
    /// 记录跟踪日志
    /// </summary>
    private async Task PushTraceRecord(long baseOrderId,
        OrderType orderType,
        decimal? orderAmount,
        string? resourceName,
        string? productName,
        TraceBehaviorType traceBehaviorType)
    {
        //上送跟踪日志
        var shareInfo = await _dbContext.OrderShareInfos.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == baseOrderId);
        if (shareInfo?.TraceId is > 0)
        {
            var visitTargetName = orderType switch
            {
                OrderType.Ticket => productName,
                OrderType.Mail => productName,
                OrderType.Hotel => resourceName,
                OrderType.ScenicTicket => resourceName,
                OrderType.TravelLineOrder => productName,
                OrderType.CarHailing => productName,
                OrderType.StoredValueCard => productName
            };

            await _capPublisher.PublishAsync(CapTopics.Marketing.PushPromotionTraceRecord,
                new PushPromotionTraceRecordMessage
                {
                    PromotionTraceId = shareInfo.TraceId.Value,
                    CustomerId = shareInfo.BuyerId,
                    BehaviorType = traceBehaviorType,
                    OrderType = orderType,
                    OrderId = baseOrderId,
                    OrderAmount = orderAmount,
                    VisitTargetName = visitTargetName
                });
        }
    }

    /// <summary>
    /// 获取分销商订单最近购买时间
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<List<SyncAgencyRecencyOrderOutput>> SearchAgencyOrderRecencyTime(SearchAgencyOrderRecencyTimeInput input)
    {
        var result = await _dbContext.BaseOrders.AsNoTracking().IgnoreQueryFilters()
            .Where(x => x.Status != BaseOrderStatus.Closed && x.CreateTime >= input.BeginTime)
            .WhereIF(input.EndTime.HasValue, x => x.CreateTime < input.EndTime.Value)
            .GroupBy(x => new { x.AgencyId })
            .Select(x => new SyncAgencyRecencyOrderOutput
            {
                AgencyId = x.Key.AgencyId,
                RecencyTime = x.Max(o => o.CreateTime),
                BaseOrderId = x.Max(o => o.Id),
            })
            .ToListAsync();

        if (result.Count <= 0)
            return result;

        var baseOrderIds = result.Select(x => x.BaseOrderId).ToList();
        var baseOrders = await _dbContext.BaseOrders.AsNoTracking()
            .Where(x => baseOrderIds.Contains(x.Id))
            .Select(x => new
            {
                Id = x.Id,
                TotalAmount = x.TotalAmount,
                TenantId = x.TenantId,
            }).ToListAsync();

        result.ForEach(x =>
        {
            var baseOrder = baseOrders.FirstOrDefault(o => o.Id == x.BaseOrderId);
            x.TotalAmount = baseOrders.FirstOrDefault(o => o.Id == x.BaseOrderId).TotalAmount;
            x.TenantId = baseOrder.TenantId;
        });

        return result;
    }

    /// <summary>
    /// 设置跟单人
    /// </summary>
    /// <param name="orderId"></param>
    /// <param name="userId"></param>
    /// <returns></returns>
    public async Task<bool> SetTrackingUserId(SetTrackingUserIdInput input)
    {
        var baseOrderIds = input.Trackings.Where(x => x.IsChannelAbnormal == false).Select(x => x.BaseOrderId);
        var orders = await _dbContext.BaseOrders.Where(x => baseOrderIds.Contains(x.Id)).ToListAsync();

        var abnormalbaseOrderIds = input.Trackings.Where(x => x.IsChannelAbnormal == true).Select(x => x.BaseOrderId);
        var abnormalOrders = await _dbContext.OpenChannelSyncFailOrders.Where(x => abnormalbaseOrderIds.Contains(x.Id)).ToListAsync();

        var allbaseOrderIds = input.Trackings.Select(x => x.BaseOrderId);
        var oldLogs = await _dbContext.OrderLogs.Where(x => allbaseOrderIds.Contains(x.OrderId))
                                      .Where(x => x.OperationType == OrderOperationType.SetTrackingUser)
                                      .AsNoTracking()
                                      .ToListAsync();

        var logs = new List<OrderLogs>();
        foreach (var tracking in input.Trackings)
        {
            OrderLogType orderLogType = OrderLogType.None;

            if (tracking.IsChannelAbnormal == true)
            {
                var abnormalOrder = abnormalOrders.FirstOrDefault(x => x.Id == tracking.BaseOrderId);
                abnormalOrder.TrackingUserId = tracking.TrackingUserId ?? input.OperationUser.UserId;
                orderLogType = OrderLogType.ChannelAbnormal;
            }
            else
            {
                var order = orders.FirstOrDefault(x => x.Id == tracking.BaseOrderId);
                order.TrackingUserId = tracking.TrackingUserId ?? input.OperationUser.UserId;
                orderLogType = order.OrderType switch
                {
                    OrderType.Ticket => OrderLogType.Ticket,
                    OrderType.CarHailing => OrderLogType.CarHailing,
                    OrderType.Hotel => OrderLogType.Hotel,
                    OrderType.ScenicTicket => OrderLogType.ScenicTicket,
                    OrderType.TravelLineOrder => OrderLogType.TravelLine,
                    OrderType.CarProduct => OrderLogType.CarProduct,
                    _ => OrderLogType.None,
                };
            }

            var operationType = OrderOperationType.SetTrackingUser;
            if (input.IsOPEdit is true) // op更改可以无数次修改
            {
                operationType = OrderOperationType.SetTrackingUserEdit;
            }
            else // op认领只能修改一次
            {
                var oldLogCount = oldLogs.Where(x => x.OrderId == tracking.BaseOrderId).Count();
                if (oldLogCount >= 2)
                    throw new BusinessException(ErrorTypes.Order.OPSetTrackingUserExcess);
            }
            OperationUserDto operationUser = input.OperationUser;
            var log = new OrderLogs
            {
                OperationRole = operationUser.UserType,
                OperationType = operationType,
                OrderId = tracking.BaseOrderId,
                UserId = operationUser.UserId,
                UserName = operationUser.Name
            };
            log.OrderLogType = orderLogType;
            logs.Add(log);
        }
        await _dbContext.AddRangeAsync(logs);
        await _dbContext.SaveChangesAsync();

        return true;
    }

    [UnitOfWork]
    public async Task UpdateVccPaymentStatus(UpdateVccPaymentStatusMessage receive)
    {
        var baseOrder = await _dbContext.BaseOrders.IgnoreQueryFilters()
            .Where(x => x.Id == receive.BaseOrderId
                        && x.OrderType == receive.OrderType)
            .FirstOrDefaultAsync();

        if (baseOrder != null)
        {
            baseOrder.VccPaymentStatus = receive.VccPaymentStatus;
        }
    }


    public async Task<BeInvoiceCountDto> GetBeInvoiceCountAsync(GetListByCanBeInvoiceInput input)
    {
        var timeCondition = DateTime.Today.AddMonths(-6);

        var result = await GetCanBeInvoiceQuery(input).ToListAsync();

        var receiptSettlementOrderIds = result.Select(x => x.ReceiptSettlementOrderId).ToList();
        var baseOrderIds = result.Select(x => x.BaseOrderId).ToList();
        var invoiceRecords = await _dbContext.InvoiceRecords.AsNoTracking()
            .Where(x => baseOrderIds.Contains(x.OrderId) || receiptSettlementOrderIds.Contains(x.OrderId))
            .OrderByDescending(x => x.CreateTime)
            .Select(x => new { x.InvoiceSerialNo, x.Status, x.OrderId, x.CreatorId })
            .ToListAsync();
        //是否支持挂账支付
        var creditReceiptOrderInvoiceIsSupport = _dbContext.InvoiceTitles
            .AsNoTracking()
            .Any(x => input.AgencyId == x.UserId && x.CreditReceiptOrderInvoiceIsSupport.Equals(true));
        foreach (var item in result)
        {
            if (item.PaymentType is PayType.AgencyCreditPay && !creditReceiptOrderInvoiceIsSupport)
                item.IsCanInvoice = false;
            var invoiceRecord = invoiceRecords.FirstOrDefault(x => x.OrderId.Equals(item.BaseOrderId) || x.OrderId.Equals(item.ReceiptSettlementOrderId));
            if (invoiceRecord is null) continue;
            item.InvoiceStatus = invoiceRecord.Status;
            if (invoiceRecord.Status is InvoiceStatus.Processing || invoiceRecord.Status is InvoiceStatus.Success)
                item.IsCanInvoice = false;

            if (item.OrderAmount <= 0)
                item.IsCanInvoice = false;
        }

        var beInvoiceCountDto = new BeInvoiceCountDto
        {
            Count = result.Count(x => x.IsCanInvoice),
            AgencyId = input.AgencyId.GetValueOrDefault()
        };

        return beInvoiceCountDto;
    }
    public async Task<PagingModel<CanBeInvoiceDto>> GetListByCanBeInvoice(GetListByCanBeInvoiceInput input)
    {
        var result = new PagingModel<CanBeInvoiceDto>();

        switch (input.SourceChannel)
        {
            case InvoiceSourceChannel.WechatMall:
                //支付金额 > 0 2.在线支付 3.完成时间6个月内
                break;
            case InvoiceSourceChannel.B2B:
                result = await GetCanBeInvoiceQuery(input).PagingAsync(input.PageIndex, input.PageSize);
                break;
            case InvoiceSourceChannel.SettlementOrder:
                break;
            case InvoiceSourceChannel.Vebk:
                break;
            default:
                break;
        }

        await InoviceRecordCheck(result.Data, input);

        return result;
    }

    /// <summary>
    /// 获取子订单所有开票信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<List<CanBeInvoiceDto>> CanIssueInvoiceList(GetListByCanBeInvoiceInput input)
    {
        var result = new List<CanBeInvoiceDto>();

        switch (input.SourceChannel)
        {
            case InvoiceSourceChannel.WechatMall:
                //支付金额 > 0 2.在线支付 3.完成时间6个月内
                break;
            case InvoiceSourceChannel.B2B:
                result = await GetCanBeInvoiceQuery(input).ToListAsync();
                break;
            case InvoiceSourceChannel.SettlementOrder:
                break;
            case InvoiceSourceChannel.Vebk:
                break;
            default:
                break;
        }

        await InoviceRecordCheck(result, input);

        return result;
    }

    /// <summary>
    /// 检查发票记录是否支持开票
    /// </summary>
    /// <param name="result"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    private async Task InoviceRecordCheck(IEnumerable<CanBeInvoiceDto> result, GetListByCanBeInvoiceInput input)
    {
        var receiptSettlementOrderIds = result.Select(x => x.ReceiptSettlementOrderId).ToList();
        var baseOrderIds = result.Select(x => x.BaseOrderId).ToList();
        var invoiceRecords = await _dbContext.InvoiceRecords.AsNoTracking()
            .Where(x => baseOrderIds.Contains(x.OrderId) || receiptSettlementOrderIds.Contains(x.OrderId))
            .OrderByDescending(x => x.CreateTime)
            .Select(x => new { x.InvoiceSerialNo, x.Status, x.OrderId, x.CreatorId })
            .ToListAsync();
        //是否支持挂账支付
        var creditReceiptOrderInvoiceIsSupport = _dbContext.InvoiceTitles
            .AsNoTracking()
            .Any(x => input.AgencyId.Equals(x.UserId) && x.CreditReceiptOrderInvoiceIsSupport.Equals(true));

        var notCanStatus = new InvoiceStatus[] { InvoiceStatus.Processing, InvoiceStatus.Success, InvoiceStatus.InReview };

        foreach (var item in result)
        {
            if (item.PaymentType is PayType.AgencyCreditPay && !creditReceiptOrderInvoiceIsSupport)
                item.IsCanInvoice = false;

            var invoiceRecord = invoiceRecords.FirstOrDefault(x => x.OrderId.Equals(item.BaseOrderId) || x.OrderId.Equals(item.ReceiptSettlementOrderId));
            if (invoiceRecord is null)
                continue;

            item.InvoiceStatus = invoiceRecord.Status;
            if (notCanStatus.Contains(invoiceRecord.Status))
                item.IsCanInvoice = false;

            if (item.OrderAmount <= 0)
                item.IsCanInvoice = false;

            if (!invoiceRecord.CreatorId.Equals(input.AgencyId) && !invoiceRecord.CreatorId.Equals(input.UserId)) continue;
            item.InvoiceSerialNo = invoiceRecord.InvoiceSerialNo;
        }
    }

    public async Task<bool> AddOrderPaymentCard(CreditCardGuaranteeInput payInput)
    {
        var hotelOrder = await _dbContext.HotelOrders.FirstOrDefaultAsync(x => x.BaseOrderId == payInput.BaseOrderId);

        if (hotelOrder.Status != HotelOrderStatus.WaitingForPay)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        var card = await _dbContext.OrderPaymentCard.FirstOrDefaultAsync(x => x.BaseOrderId == payInput.BaseOrderId);
        if (card != null)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }
        card = _mapper.Map<OrderPaymentCard>(payInput);
        await _dbContext.AddAsync(card);
        await _dbContext.SaveChangesAsync();
        return true;
    }

    private IQueryable<CanBeInvoiceDto> GetCanBeInvoiceQuery(GetListByCanBeInvoiceInput input)
    {
        var timeCondition = DateTime.Today.AddMonths(-6);

        var payTypes = new List<PayType>
        {
            PayType.YeePay,
            PayType.AdvancePayment,
            PayType.AgencyCreditPay
        };

        var orderLogQueryable = _dbContext.OrderLogs.AsNoTracking()
            .Where(l => l.OperationType.Equals(OrderOperationType.Finished) || l.OperationType.Equals(OrderOperationType.CheckedOut));

        var baseOrderIdsCheck = input.BaseOrderIds is not null && input.BaseOrderIds.Any();

        //支付金额>0 2.在线支付 3.完成时间6个月内 4.分销商勾选了“额度收款订单支持开票”
        var result = _dbContext.BaseOrders.AsNoTracking()
                .Where(x => x.AgencyId.Equals(input.AgencyId))
                .Where(x => x.Status == BaseOrderStatus.Finished)
                .Where(x => x.UpdateTime > timeCondition)
                .Where(x => x.PaymentAmount > 0)
                .Where(x => payTypes.Contains(x.PaymentType))
                .WhereIF(baseOrderIdsCheck, x => input.BaseOrderIds.Contains(x.Id))  // 多个订单id查询
                .WhereIF(input.BaseOrderId.HasValue, x => input.BaseOrderId.Equals(x.Id)) // 单个订单id查询
                .WhereIF(!string.IsNullOrWhiteSpace(input.ProductName),
                        x => x.ProductName.Contains(input.ProductName)
                          || x.ResourceName.Contains(input.ProductName)
                          || x.ProductSkuName.Contains(input.ProductName))
                .WhereIF(input.OrderType.HasValue, x => x.OrderType.Equals(input.OrderType))
                .GroupJoin(_dbContext.ReceiptSettlementOrderDetails.AsNoTracking(), o => o.Id, d => d.BaseOrderId, (o, d) => new { o, d })
                .SelectMany(x => x.d.DefaultIfEmpty(), (x, d) => new { d, x.o })
                .WhereIF(input.InvoiceStatus.Any() && !input.IsToBeInvoiced, x => input.InvoiceStatus.Contains(_dbContext.InvoiceRecords.AsNoTracking().OrderByDescending(r => r.Id).FirstOrDefault(r => r.OrderId.Equals(x.o.Id)).Status))
                .WhereIF(input.IsToBeInvoiced && !input.InvoiceStatus.Any(), x => !_dbContext.InvoiceRecords.AsNoTracking().OrderByDescending(r => r.Id).Any(r => r.OrderId.Equals(x.o.Id)))
                .Join(orderLogQueryable, x => x.o.Id, l => l.OrderId, (x, l) => new { x.o, x.d, l })
                .WhereIF(input.PaymentType.Any(), x => input.PaymentType.Contains(x.o.PaymentType))
                .WhereIF(input.FinishBeginTime.HasValue, x => x.l.CreateTime >= input.FinishBeginTime)
                .WhereIF(input.FinishEndTime.HasValue, x => x.l.CreateTime < input.FinishEndTime.Value.AddDays(1))
                .OrderByDescending(x => x.l.CreateTime)
                .ThenByDescending(x => x.o.CreateTime)
                .Select(x => new CanBeInvoiceDto
                {
                    BaseOrderId = x.o.Id,
                    ResourceName = x.o.ResourceName,
                    ProductName = x.o.ProductName,
                    ProductSkuName = x.o.ProductSkuName,
                    PaymentType = x.o.PaymentType,
                    OrderAmount = x.o.TotalAmount,
                    ReceiptSettlementOrderId = x.d.SettlementOrderId,
                    ReceiptSettlementOrderDetailsId = x.d.Id
                });

        return result;
    }

    public async Task<bool> SetOrderOpUserId(SetOrderOpUserIdInput input)
    {
        var order = await _dbContext.BaseOrders.Where(x => x.Id == input.BaseOrderId).FirstOrDefaultAsync();

        if (order == null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        OrderLogType orderLogType = order.OrderType switch
        {
            OrderType.Ticket => OrderLogType.Ticket,
            OrderType.CarHailing => OrderLogType.CarHailing,
            OrderType.Hotel => OrderLogType.Hotel,
            OrderType.ScenicTicket => OrderLogType.ScenicTicket,
            OrderType.TravelLineOrder => OrderLogType.TravelLine,
            OrderType.CarProduct => OrderLogType.CarProduct,
            _ => OrderLogType.None,
        };
        var operationType = input.OrderOperationType;
        switch (input.OrderOperationType)
        {
            case OrderOperationType.SetDevelopUserId:
                order.DevelopUserId = input.UserId;
                break;
            case OrderOperationType.SetOperatorUserId:
                order.OperatorUserId = input.UserId;
                break;
            case OrderOperationType.SetOperatorAssistantUserId:
                order.OperatorAssistantUserId = input.UserId;
                break;
        }
        order.UpdateTime = DateTime.Now;

        OperationUserDto operationUser = input.OperationUser;
        var log = new OrderLogs
        {
            OperationRole = operationUser.UserType,
            OperationType = operationType,
            OrderId = input.BaseOrderId,
            UserId = operationUser.UserId,
            UserName = operationUser.Name,
            OrderLogType = orderLogType
        };

        await _dbContext.AddAsync(log);
        await _dbContext.SaveChangesAsync();

        return true;
    }
}
