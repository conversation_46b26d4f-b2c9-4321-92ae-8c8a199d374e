using AutoMapper;
using Cit.OpenAPI.GDS;
using Cit.OpenAPI.GDS.Client;
using Cit.OpenAPI.GDS.Models;
using Cit.OpenAPI.GDS.Models.CreatePNR;
using Cit.OpenAPI.GDS.Models.HotelPriceCheck;
using Cit.OpenAPI.GDS.Models.Order;
using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common.Order.DTOs.GDSOrder;
using Contracts.Common.Order.DTOs.HotelOrder.GDS;
using Contracts.Common.Order.DTOs.MessageNotify;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Resource.DTOs.GDSHotel;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Tenant.DTOs.SupplierApiFields;
using Contracts.Common.Tenant.DTOs.SupplierApiSetting;
using DotNetCore.CAP;
using Hangfire;
using HangfireClient.Jobs.Order;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Order.Api.Extensions;
using Order.Api.Services.Interfaces;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.Abstract;
using Contracts.Common.Tenant.Enums;

namespace Order.Api.Services;

public class GDSOrderService : IGDSOrderService
{
    private readonly IMapper _mapper;
    private readonly CustomDbContext _dbContext;

    private readonly IGDSClientFactory _gDSClientFactory;
    private readonly ILogger<GDSOrderService> _logger;
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly ServicesAddress _servicesAddress;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IMessageNotifyService _messageNotifyService;

    public GDSOrderService(IMapper mapper,
        CustomDbContext dbContext,

        IGDSClientFactory gDSClientFactory,
        IBackgroundJobClient backgroundJobClient,
        IOptions<ServicesAddress> servicesAddressOptions,
        IHttpClientFactory httpClientFactory,
        IMessageNotifyService messageNotifyService,
         ILogger<GDSOrderService> logger)
    {
        _mapper = mapper;
        _dbContext = dbContext;
        _gDSClientFactory = gDSClientFactory;
        _logger = logger;
        _backgroundJobClient = backgroundJobClient;
        _httpClientFactory = httpClientFactory;
        _servicesAddress = servicesAddressOptions.Value;
        _messageNotifyService = messageNotifyService;
    }

    public async Task<GetPriceCheckInfoOutput> GetPriceCheckInfo(string bookingKey)
    {
        var data = await _dbContext.HotelSupplierOrderRecord
             .Where(x => x.ResultIdKey == bookingKey && x.RecordType == HotelSupplierOrderRecordType.PriceCheck)
             .OrderByDescending(x => x.CreateTime)
             .FirstOrDefaultAsync();
        if (data == null)
        {
            throw new BusinessException(ErrorTypes.Common.ResourceInvalid);
        }
        var info = JsonConvert.DeserializeObject<GDSHotelPriceCheckOutput>(data.Result!);
        var output = new GetPriceCheckInfoOutput()
        {
            CreateTime = data.CreateTime,
            RateKey = data.IdKey,
            PriceCheckInfo = info,
        };
        return output;
    }

    public async Task<GDSHotelPriceCheckOutput> PriceCheck(GDSHotelPriceCheckInput input)
    {
        input.IsOriginalJson = true;
        var res = await GDSHotelPriceCheck(input);
        var record = new HotelSupplierOrderRecord()
        {
            BaseOrderId = 0,
            IdKey = input.RateKey,
            RecordType = HotelSupplierOrderRecordType.PriceCheck,
        };
        record.ResultIdKey = res.BookingKey;
        record.Result = JsonConvert.SerializeObject(res);
        record.ResultCode = res.ResultCode;
        await _dbContext.AddAsync(record);
        await _dbContext.SaveChangesAsync();
        return res;
    }

    public async Task<PagingModel<SearchGDSOrderListByUserOutput>> SearchGDSOrderListByUser(SearchGDSOrderListByUserInput input)
    {
        var gdsSupplierApiTypes = new List<SupplierApiType>
        {
            SupplierApiType.Youxia,
            SupplierApiType.GDS
        };

        var hotelQuery = _dbContext.BaseOrders.AsNoTracking()
            .Join(_dbContext.HotelOrders.AsNoTracking(), baseOrder => baseOrder.Id, hotelOrder => hotelOrder.BaseOrderId,
                (baseOrder, hotelOrder) => new { baseOrder, hotelOrder })
            .Where(x => x.baseOrder.UserId == input.UserId)
            .Where(x => x.baseOrder.OrderType == OrderType.Hotel && gdsSupplierApiTypes.Contains(x.hotelOrder.SupplierApiType))
            .WhereIF(input.Status.HasValue, x => x.hotelOrder.Status == input.Status.Value)
            .WhereIF(!string.IsNullOrEmpty(input.HotelName), x => x.hotelOrder.Status == input.Status.Value)
            .WhereIF(input.CreateTime.HasValue, x => x.baseOrder.CreateTime >= input.CreateTime!.Value.Date)
            .WhereIF(input.CreateTimeEnd.HasValue, x => x.baseOrder.CreateTime < input.CreateTimeEnd!.Value.Date.AddDays(1))
            .WhereIF(input.CheckInDate.HasValue,
                x => x.hotelOrder.CheckInDate >= input.CheckInDate!.Value.Date)
            .WhereIF(input.CheckInDateEnd.HasValue,
                x => x.hotelOrder.CheckInDate < input.CheckInDateEnd!.Value.Date.AddDays(1))
            .WhereIF(input.CheckOutDate.HasValue,
                x => x.hotelOrder.CheckOutDate.Date >= input.CheckOutDate!.Value.Date)
            .WhereIF(input.CheckOutDateEnd.HasValue,
                x => x.hotelOrder.CheckOutDate < input.CheckOutDateEnd!.Value.Date.AddDays(1))
            .Select(x => x.baseOrder);

        var query = hotelQuery;
        if (!input.CheckInDate.HasValue && !input.CheckOutDate.HasValue
            && string.IsNullOrEmpty(input.HotelName) && !input.Status.HasValue)
        {
            var storedValueCardQuery = _dbContext.BaseOrders.AsNoTracking()
                .WhereIF(input.CreateTime.HasValue, x => x.CreateTime >= input.CreateTime!.Value.Date)
                .WhereIF(input.CreateTimeEnd.HasValue, x => x.CreateTime < input.CreateTimeEnd!.Value.Date.AddDays(1))
                .Where(x => x.UserId == input.UserId && x.OrderType == OrderType.StoredValueCard)
                .Select(x => x);

            query = query.Union(storedValueCardQuery);
        }

        var datas = await query
            .OrderByDescending(x => x.CreateTime)
            .PagingAsync(input.PageIndex, input.PageSize);

        var result = new PagingModel<SearchGDSOrderListByUserOutput>();
        result.Total = datas.Total;
        result.PageSize = datas.PageSize;
        result.PageIndex = datas.PageIndex;

        var hotelIds = datas.Data.Where(x => x.OrderType == OrderType.Hotel).Select(x => x.Id);
        var storedValueCardIds = datas.Data.Where(x => x.OrderType == OrderType.StoredValueCard).Select(x => x.Id);

        var hotelOrders = new List<HotelOrder>();
        var storedValueCardOrders = new List<StoredValueCardOrder>();
        if (hotelIds.Any())
            hotelOrders = await _dbContext.HotelOrders.AsNoTracking()
                .Where(x => hotelIds.Contains(x.BaseOrderId))
                .ToListAsync();
        if (storedValueCardIds.Any())
            storedValueCardOrders = await _dbContext.StoredValueCardOrders.AsNoTracking()
                .Where(x => storedValueCardIds.Contains(x.BaseOrderId))
                .ToListAsync();

        var items = new List<SearchGDSOrderListByUserOutput>();
        foreach (var data in datas.Data)
        {
            var item = new SearchGDSOrderListByUserOutput
            {
                BaseOrderId = data.Id,
                Status = data.Status,
                ProductSkuName = data.ProductSkuName,
                ProductName = data.ProductName,
                PaymentAmount = data.PaymentAmount,
                CreateTime = data.CreateTime,
                DiscountAmount = data.DiscountAmount,
                OrderType = data.OrderType,
                ResourceName = data.ResourceName,
                TotalAmount = data.TotalAmount,
                PaymentCurrencyCode = data.PaymentCurrencyCode,
            };
            if (item.OrderType == OrderType.Hotel)
            {
                var hotelOrder = hotelOrders.FirstOrDefault(x => x.BaseOrderId == data.Id);
                item.OrderDetails.Add(new SubGDSOrderDetailOutput
                {
                    OrderId = hotelOrder.Id,
                    ProductName = hotelOrder.HotelRoomName,
                    ProductSkuName = hotelOrder.PriceStrategyName,
                    Quantity = hotelOrder.PriceStrategyRoomsCount,
                    ImagePath = hotelOrder.HotelRoomImgPath,
                    HotelOrderExt = new SubGDSHotelOrderExt
                    {
                        CheckInDate = hotelOrder.CheckInDate,
                        CheckOutDate = hotelOrder.CheckOutDate,
                        Nights = hotelOrder.PriceStrategyNightsCount,
                        Rooms = hotelOrder.PriceStrategyRoomsCount,
                        Status = hotelOrder.Status,
                    }
                });
            }
            else if (item.OrderType == OrderType.StoredValueCard)
            {
                var storedValueCardOrder = storedValueCardOrders.FirstOrDefault(x => x.BaseOrderId == data.Id);
                item.OrderDetails.Add(new SubGDSOrderDetailOutput
                {
                    OrderId = storedValueCardOrder.Id,
                    ProductName = storedValueCardOrder.StoredValueCardInfo.CardName,
                    Quantity = 1,
                    ImagePath = storedValueCardOrder.StoredValueCardInfo.Cover,
                    ProductSkuName = $"{storedValueCardOrder.StoredValueCardGearInfo.Price:N2}"
                });
            }
            items.Add(item);
        }
        result.Data = items;

        return result;
    }

    public async Task CreatePNR(HotelSupplierOrderCreateMessage input)
    {
        var baseOrder = await _dbContext.BaseOrders.IgnoreQueryFilters()
                         .FirstOrDefaultAsync(x => x.Id == input.BaseOrderId && x.TenantId == input.TenantId);
        var hotelOrder = await _dbContext.HotelOrders.IgnoreQueryFilters()
                       .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId && x.TenantId == input.TenantId);

        if (hotelOrder.SupplierApiType != Contracts.Common.Tenant.Enums.SupplierApiType.GDS)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        var orderPayCard = await _dbContext.OrderPaymentCard.IgnoreQueryFilters()
                        .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        var guests = await _dbContext.HotelOrderGuests.IgnoreQueryFilters()
                    .Where(x => x.BaseOrderId == input.BaseOrderId)
                    .ToListAsync();

        var gdsHotelOrder = await _dbContext.GDSHotelOrder.IgnoreQueryFilters()
                        .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);

        var hotelApiOrder = await _dbContext.HotelApiOrders.IgnoreQueryFilters()
                       .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);

        //api订单状态检查
        if (hotelApiOrder.Status != Contracts.Common.Resource.Enums.SupplierApiOrderStatus.WaitForOrder)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }
        var gdsConfig = await GetConfig(hotelOrder.PriceStrategySupplierId);

        var personNames = new List<PersonNameItemRQ>();
        var roomItems = new List<RoomItemRQ>();
        var phone = baseOrder.ContactsPhoneNumber.Replace("+", "").Replace("[", "").Replace("]", "-");
        var contactNumberItems = new List<ContactNumberItemRQ>() {
          new ContactNumberItemRQ() {
             PhoneUseType="H",
             Phone=phone,
          },
        };

        var adults = guests.Where(x => x.HotelOrderGuestType == HotelOrderGuestType.Adult).ToList();
        var childs = guests.Where(x => x.HotelOrderGuestType == HotelOrderGuestType.Child).ToList();
        var adultCount = adults.Count();
        var childCount = childs.Count();
        var adultAvgCount = adultCount / hotelOrder.PriceStrategyRoomsCount;
        if ((adultCount % hotelOrder.PriceStrategyRoomsCount) > 0)
        {
            adultAvgCount += 1;
        }
        var childAvgCount = childCount / hotelOrder.PriceStrategyRoomsCount;
        if ((childCount % hotelOrder.PriceStrategyRoomsCount) > 0)
        {
            childAvgCount += 1;
        }
        bool leadGuest = true;
        int guestIndex = 0;
        for (int i = 0; i < hotelOrder.PriceStrategyRoomsCount; i++)
        {
            var room = new RoomItemRQ()
            {
                RoomIndex = i + 1,
                Guests = new GuestsRQ()
                {
                    Guest = new List<GuestItemRQ>()
                },
            };
            var roomGuests = new List<GuestItemRQ>();
            for (int j = 0; j < adultAvgCount; j++)
            {
                if (guestIndex >= adultCount)
                    continue;

                var adult = adults[guestIndex];
                roomGuests.Add(new GuestItemRQ()
                {
                    Index = j + 1,
                    Type = 10,
                    FirstName = adult.FirstName,
                    LastName = adult.LastName,
                    LeadGuest = leadGuest, // 必须得有一个是主要人
                });
                leadGuest = false;
                guestIndex++;
            }
            room.Guests = new GuestsRQ() { Guest = roomGuests };

            roomItems.Add(room);
        }
        var adultFirst = adults.FirstOrDefault();
        personNames.Add(new PersonNameItemRQ()
        {
            PassengerType = "ADT",
            Surname = adultFirst.LastName,
            GivenName = adultFirst.FirstName,
        });

        var paymentCard = new PaymentCard()
        {
            PaymentType = "CC",
            CardNumber = orderPayCard.CardNumber,
            CardCode = orderPayCard.CardCode,
            ExpiryMonth = orderPayCard.ExpiryMonth,
            ExpiryYear = orderPayCard.ExpiryYear.ToString(),
            FullCardHolderName = new FullCardHolderName()
            {
                FirstName = orderPayCard.CardHolderFirstName,
                LastName = orderPayCard.CardHolderLastName,
            }
        };

        var requst = new CreatePassengerNameRecordRequest()
        {
            CreatePassengerNameRecordRQ = new CreatePassengerNameRecordRQ()
            {
                version = "2.5.0",
                haltOnHotelBookError = true,
                TravelItineraryAddInfo = new TravelItineraryAddInfo()
                {
                    AgencyInfo = new AgencyInfo()
                    {
                        Address = new AgencyInfoAddress()
                        {
                            AddressLine = gdsConfig.AddressLine,
                            CityName = gdsConfig.CityName,
                            CountryCode = gdsConfig.CountryCode,
                            PostalCode = gdsConfig.PostalCode,
                            StateCountyProv = new StateCountyProv()
                            {
                                StateCode = gdsConfig.StateCode,
                            },
                            StreetNmbr = gdsConfig.StreetNmbr,
                        },
                    },
                    CustomerInfo = new CustomerInfoRQ()
                    {
                        ContactNumbers = new ContactNumbersRQ
                        {
                            ContactNumber = contactNumberItems,
                        },
                        PersonName = personNames
                    },
                },
                HotelBook = new HotelBook()
                {
                    bookGDSviaCSL = true,
                    legacyIURforCSL = false,
                    BookingInfo = new BookingInfoRQ
                    {
                        BookingKey = gdsHotelOrder.BookingKey,
                    },
                    Rooms = new RoomsRQ
                    {
                        Room = roomItems,
                    },
                    PaymentInformation = new PaymentInformationRQ()
                    {
                        Type = "GUARANTEE", // 担保
                        FormOfPayment = new FormOfPayment
                        {
                            PaymentCard = paymentCard
                        }
                    },

                },
                PostProcessing = new PostProcessing()
                {
                    RedisplayReservation = new RedisplayReservation()
                    {
                        waitInterval = 5 * 1000,
                    },
                    EndTransaction = new EndTransaction()
                    {
                        Source = new EndTransactionSource()
                        {
                            ReceivedFrom = "CIT",
                        },
                    },
                }
            }
        };

        if (!string.IsNullOrEmpty(baseOrder.Message))
        {
            requst.CreatePassengerNameRecordRQ.SpecialReqDetails = new SpecialReqDetails()
            {
                AddRemark = new AddRemark()
                {
                    RemarkInfo = new RemarkInfo()
                    {
                        Remark = new List<SpecialReqDetailsRemarkItem>() {
                         new SpecialReqDetailsRemarkItem(){
                              Type="General",
                               Text=baseOrder.Message
                         }
                        }
                    }
                }
            };
        }
        var client = _gDSClientFactory.Create(gdsConfig);
        _logger.LogInformation("CreatePassengerNameRecord requst: {@requst}", requst);
        var response = await client.CreatePassengerNameRecord(requst);
        _logger.LogInformation("CreatePassengerNameRecord response: {@response}", response);

        var record = new HotelSupplierOrderRecord()
        {
            BaseOrderId = baseOrder.Id,
            IdKey = gdsHotelOrder.BookingKey,
            RecordType = HotelSupplierOrderRecordType.Create,
        };
        record.Result = response.SourceDataJson;
        if (response.Success)
        {
            record.ResultCode = response.Data?.CreatePassengerNameRecordRS?.ApplicationResults?.Status;

            if (response.Data?.CreatePassengerNameRecordRS?.ApplicationResults?.Status.Equals("Complete", StringComparison.CurrentCultureIgnoreCase) == true)
            {
                var pnrRs = response.Data?.CreatePassengerNameRecordRS;

                record.ResultIdKey = pnrRs.ItineraryRef.ID;

                baseOrder.Status = BaseOrderStatus.UnFinished;
                hotelOrder.Status = HotelOrderStatus.WaitingForConfirm;
                //hotelOrder.ConfirmCode = pnrRs.ItineraryRef.ID;
                hotelOrder.SupplierOrderId = pnrRs.ItineraryRef.ID;
                hotelApiOrder.SupplierOrderId = pnrRs.ItineraryRef.ID;
                hotelApiOrder.Status = SupplierApiOrderStatus.WaitForConfirm;
                hotelApiOrder.UpdateTime = DateTime.Now;
                hotelOrder.UpdateTime = DateTime.Now;
                baseOrder.UpdateTime = DateTime.Now;

                _backgroundJobClient.Schedule<HangfireClient.Jobs.Order.IHotelOrderJob>(s =>
                         s.GDSAutoConfirmed(hotelOrder.Id, baseOrder.TenantId, (int)hotelOrder.SupplierApiType),
                         TimeSpan.FromSeconds(30)
                     );
            }
            else
            {
                var systemSpecificResult = response.Data?.CreatePassengerNameRecordRS.
                                                   ApplicationResults.Warning
                                                   .LastOrDefault()?
                                                   .SystemSpecificResults?.LastOrDefault();
                var msg = systemSpecificResult.Message.LastOrDefault()?.content;
                hotelApiOrder.Message = msg;
                hotelApiOrder.Status = SupplierApiOrderStatus.OrderError;
                hotelApiOrder.UpdateTime = DateTime.Now;

                record.ResultMessage = msg;
            }
        }
        else
        {
            hotelApiOrder.Status = SupplierApiOrderStatus.OrderError;
            hotelApiOrder.UpdateTime = DateTime.Now;
            record.ResultMessage = "下单失败";
        }
        record.SetTenantId(baseOrder.TenantId);
        await _dbContext.AddAsync(record);
        await _dbContext.SaveChangesAsync();
    }

    public async Task GetBookingConfirmed(GDSGetBookingMessage input)
    {
        var hotelOrder = await _dbContext.HotelOrders.IgnoreQueryFilters()
                     .FirstOrDefaultAsync(x => x.Id == input.HotelOrderId);

        var baseOrder = await _dbContext.BaseOrders.IgnoreQueryFilters()
                    .FirstOrDefaultAsync(x => x.Id == hotelOrder.BaseOrderId);

        if (hotelOrder.SupplierApiType != Contracts.Common.Tenant.Enums.SupplierApiType.GDS)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }
        var hotelApiOrder = await _dbContext.HotelApiOrders.IgnoreQueryFilters()
                       .FirstOrDefaultAsync(x => x.BaseOrderId == hotelOrder.BaseOrderId);
        var gdsConfig = await GetConfig(hotelOrder.PriceStrategySupplierId);
        var requst = new GetBookingRequest()
        {
            confirmationId = hotelApiOrder.SupplierOrderId,
        };
        var client = _gDSClientFactory.Create(gdsConfig);

        _logger.LogInformation("GetBooking requst: {@requst}", requst);
        var response = await client.GetBooking(requst);
        _logger.LogInformation("GetBooking response: {@response}", response);
        var record = new HotelSupplierOrderRecord()
        {
            BaseOrderId = hotelOrder.BaseOrderId,
            IdKey = hotelApiOrder.SupplierOrderId,
            RecordType = HotelSupplierOrderRecordType.GetBooking,
        };
        record.Result = response.SourceDataJson;
        if (response.Success)
        {
            record.ResultCode = "Incomplete";
            if (response.Data?.hotels != null && response.Data?.hotels.Any() == true)
            {
                record.ResultCode = "Complete";
                // Confirmed, Waitlisted, On Request, Pending, Cancelled, Infant/No Seat, Priority Waitlist, Quote, Space Available, Unconfirmed, Pending Quote, No Seat, Standby, Unknown
                var hotel = response.Data?.hotels.FirstOrDefault();
                // 对saas系统来说，就只有已确认、已取消，其它都应该是待确认状态
                switch (hotel.hotelStatusName.ToLower())
                {
                    case "confirmed":
                        hotelOrder.Status = HotelOrderStatus.WaitingForCheckIn;
                        hotelApiOrder.Status = SupplierApiOrderStatus.Confirmed;
                        hotelOrder.ConfirmCode = hotel.confirmationId;
                        hotelOrder.ConfirmTime = DateTime.Now;
                        hotelApiOrder.UpdateTime = DateTime.Now;
                        hotelOrder.UpdateTime = DateTime.Now;
                        var guests = await _dbContext.HotelOrderGuests.IgnoreQueryFilters()
                                    .Where(x => x.HotelOrderId == hotelOrder.Id)
                                    .Select(x => x.GuestName)
                                    .ToListAsync();
                        OrderNotifyDto<HotelOrderConfirmNotifyDto> orderNotifyDto = new()
                        {
                            BaseOrder = _mapper.Map<BaseOrderNotify>(baseOrder),
                            NotifyDto = new HotelOrderConfirmNotifyDto
                            {
                                HotelName = baseOrder.ResourceName,
                                HotelRoomName = baseOrder.ProductName,
                                SkuName = baseOrder.ProductSkuName,
                                CheckInDate = hotelOrder.CheckInDate,
                                CheckOutDate = hotelOrder.CheckOutDate,
                                NightsCount = hotelOrder.PriceStrategyNightsCount,
                                NumberOfBreakfast = hotelOrder.PriceStrategyNumberOfBreakfast,
                                RoomsCount = hotelOrder.PriceStrategyRoomsCount,
                                Guests = guests.ToArray(),
                                IsGroupBooking = hotelOrder.IsGroupBooking,
                                ConfirmCode = hotelOrder.ConfirmCode,
                                BoardCount = hotelOrder.BoardCount,
                                BoardCodeType = hotelOrder.BoardCodeType
                            }
                        };
                        await _messageNotifyService.HotelOrderConfirmNotify(orderNotifyDto);

                        //入住日22:00后自动变为已入住
                        var checkInTime = hotelOrder.CheckInDate.Date.AddHours(22);
                        var ts = checkInTime.Subtract(DateTime.Now);
                        _backgroundJobClient?.Schedule<IHotelOrderJob>(x => x.AutoCheckIn(hotelOrder.Id, hotelOrder.TenantId), ts);

                        break;
                }
                hotelOrder.UpdateTime = DateTime.Now;
                hotelApiOrder.UpdateTime = DateTime.Now;
            }
            else
            {
                record.ResultCode = "Incomplete";
                record.ResultMessage = "获取确认失败";
            }
        }
        else
        {
            record.ResultCode = "Incomplete";
            record.ResultMessage = "获取确认失败";
        }
        await _dbContext.AddAsync(record);
        await _dbContext.SaveChangesAsync();
    }

    public async Task<bool> CancelBooking(GDSCancelBookingMessage input)
    {
        var hotelOrder = await _dbContext.HotelOrders.IgnoreQueryFilters()
                     .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);

        if (hotelOrder.SupplierApiType != Contracts.Common.Tenant.Enums.SupplierApiType.GDS)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }
        var hotelApiOrder = await _dbContext.HotelApiOrders.IgnoreQueryFilters()
                       .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);

        //api订单状态检查
        var status = new List<SupplierApiOrderStatus>() {
            SupplierApiOrderStatus.WaitForConfirm,
            SupplierApiOrderStatus.Confirmed,
        };
        if (!status.Contains(hotelApiOrder.Status))
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        var gdsConfig = await GetConfig(hotelOrder.PriceStrategySupplierId);

        bool success = false;
        var requst = new CancelBookingRequest()
        {
            cancelAll = true,
            retrieveBooking = true,
            confirmationId = hotelApiOrder.SupplierOrderId,
        };
        var client = _gDSClientFactory.Create(gdsConfig);
        _logger.LogInformation("CancelBooking requst: {@requst}", requst);
        var response = await client.CancelBooking(requst);
        _logger.LogInformation("CancelBooking response: {@response}", response);

        var record = new HotelSupplierOrderRecord()
        {
            BaseOrderId = hotelOrder.BaseOrderId,
            IdKey = hotelApiOrder.SupplierOrderId,
            RecordType = HotelSupplierOrderRecordType.Cancel,
        };
        record.Result = response.SourceDataJson;
        if (response.Success)
        {
            record.ResultCode = "Incomplete";
            if (response.Data?.booking != null
                || response.Data?.errors?.Any(x => x.Type.Equals("NO_ITEMS_CANCELLED", StringComparison.CurrentCultureIgnoreCase)) is true) // 已经取消或者不存在
            {
                record.ResultCode = "Complete";
                hotelApiOrder.Status = SupplierApiOrderStatus.Closed;
                success = true;
            }
            else if (response.Data?.errors != null && response.Data.errors.Any())
            {
                string msg = string.Empty;
                response.Data.errors.ForEach(e =>
                {
                    msg += $"{e.Description} ,";
                });
                hotelApiOrder.Message = msg;
            }
            hotelApiOrder.UpdateTime = DateTime.Now;
        }
        else
        {
            record.ResultCode = "Incomplete";
            record.ResultMessage = "取消失败";
        }
        await _dbContext.AddAsync(record);
        await _dbContext.SaveChangesAsync();
        return success;
    }

    private async Task<GDSHotelPriceCheckOutput> GDSHotelPriceCheck(GDSHotelPriceCheckInput input)
    {
        var httpContent = new StringContent(JsonConvert.SerializeObject(input), Encoding.UTF8, "application/json");
        var output = await _httpClientFactory.InternalPostAsync<GDSHotelPriceCheckOutput>(
            requestUri: _servicesAddress.Resource_GDSHotelPriceCheck(), httpContent: httpContent);
        return output;
    }

    private async Task<GDSConfigEx> GetConfig(long supplierId)
    {
        var supplier = (await TenantSupplierGetByIds(new List<long>() { supplierId })).FirstOrDefault();
        var gdsConfig = FieldToConfig(supplier?.SupplierApiSetting);
        return gdsConfig;
    }

    private async Task<IEnumerable<Contracts.Common.Tenant.DTOs.Supplier.GetSupplierOutput>> TenantSupplierGetByIds(List<long> supplierIds)
    {
        var httpContent = new StringContent(JsonConvert.SerializeObject(supplierIds),
            Encoding.UTF8, "application/json");
        var response = await _httpClientFactory.InternalPostAsync<IEnumerable<Contracts.Common.Tenant.DTOs.Supplier.GetSupplierOutput>>(
            requestUri: _servicesAddress.Tenant_Supplier_GetByIds(),
            httpContent: httpContent);
        return response;
    }

    private GDSConfigEx FieldToConfig(SupplierApiSettingDto supplierApiSetting)
    {
        var gdsConfig = new GDSConfigEx();
        gdsConfig.APIID = supplierApiSetting?.SupplierApiSettingFields?
                   .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.GDSCode.GDSApiID)?.FieldValue;
        gdsConfig.PCC = supplierApiSetting?.SupplierApiSettingFields?
                  .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.GDSCode.GDSPCC)?.FieldValue;
        gdsConfig.Password = supplierApiSetting?.SupplierApiSettingFields?
                  .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.GDSCode.GDSPassword)?.FieldValue;
        gdsConfig.Domain = supplierApiSetting?.SupplierApiSettingFields?
                  .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.GDSCode.GDSDomain)?.FieldValue;
        gdsConfig.Host = supplierApiSetting?.SupplierApiSettingFields?
                  .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.GDSCode.GDSHost)?.FieldValue;
        gdsConfig.AddressLine = supplierApiSetting?.SupplierApiSettingFields?
                  .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.GDSCode.GDSAddressLine)?.FieldValue;
        gdsConfig.CityName = supplierApiSetting?.SupplierApiSettingFields?
                  .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.GDSCode.GDSCityName)?.FieldValue;
        gdsConfig.CountryCode = supplierApiSetting?.SupplierApiSettingFields?
                  .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.GDSCode.GDSCountryCode)?.FieldValue;
        gdsConfig.PostalCode = supplierApiSetting?.SupplierApiSettingFields?
                  .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.GDSCode.GDSPostalCode)?.FieldValue;
        gdsConfig.StateCode = supplierApiSetting?.SupplierApiSettingFields?
                  .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.GDSCode.GDSStateCode)?.FieldValue;
        gdsConfig.StreetNmbr = supplierApiSetting?.SupplierApiSettingFields?
                  .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.GDSCode.GDSStreetNmbr)?.FieldValue;
        return gdsConfig;
    }
}
