using AutoMapper;
using Cit.Storage.Redis;
using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common.Inventory.Messages;
using Contracts.Common.Marketing.Enums;
using Contracts.Common.Marketing.Messages;
using Contracts.Common.Notify.DTOs.CustomerNotify;
using Contracts.Common.Notify.Enums;
using Contracts.Common.Notify.Messages;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.DTOs.MessageNotify;
using Contracts.Common.Order.DTOs.OrderFieldInformation;
using Contracts.Common.Order.DTOs.OrderLog;
using Contracts.Common.Order.DTOs.TravelLineOrder;
using Contracts.Common.Order.DTOs.TravelLineOrder.OTA;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Product.DTOs.Fields.Group;
using Contracts.Common.Product.DTOs.OpenSupplier;
using Contracts.Common.Product.Enums;
using Contracts.Common.Product.Messages;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Scenic.Enums;
using Contracts.Common.Tenant.DTOs.Agency;
using Contracts.Common.Tenant.DTOs.Supplier;
using Contracts.Common.Tenant.Enums;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Hangfire;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Order.Api.Extensions;
using Order.Api.Requests;
using Order.Api.Services.Interfaces;
using Order.Api.Services.OpenPlatform.Contracts.Supplier;
using Order.Api.Services.OpenPlatform.Interfaces;
using UserType = Contracts.Common.Order.Enums.UserType;

namespace Order.Api.Services;

public class TravelLineOrderService : BaseOrderSeriesNumberService, ITravelLineOrderService
{
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly ICapPublisher _capPublisher;
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IOptions<ServicesAddress> _servicesAddress;
    private readonly IMessageNotifyService _messageNotifyService;
    private readonly IMediator _mediator;
    private readonly IRedisClient _redisClient;
    private readonly ITravelLineOTAService _travelLineOtaService;
    private readonly IOrderFieldInformationService _orderFieldInformationService;
    private readonly IOpenPlatformBaseService _openPlatformBaseService;
    private readonly IOpenSupplierService _openSupplierService;
    private readonly IAgencyService _agencyService;
    private readonly ITravelLineOrderMessageService _travelLineOrderMessageService;

    public TravelLineOrderService(CustomDbContext dbContext,
        IMapper mapper,
        ICapPublisher capPublisher,
        IBackgroundJobClient backgroundJobClient,
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> servicesAddress,
        IMessageNotifyService messageNotifyService,
        IMediator mediator,
        IRedisClient redisClient,
        ITravelLineOTAService travelLineOtaService,
        IOrderFieldInformationService orderFieldInformationService,
        IOpenPlatformBaseService openPlatformBaseService,
        IOpenSupplierService openSupplierService,
        IAgencyService agencyService,
        ITravelLineOrderMessageService travelLineOrderMessageService) : base(dbContext)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _capPublisher = capPublisher;
        _backgroundJobClient = backgroundJobClient;
        _httpClientFactory = httpClientFactory;
        _servicesAddress = servicesAddress;
        _messageNotifyService = messageNotifyService;
        _mediator = mediator;
        _redisClient = redisClient;
        _travelLineOtaService = travelLineOtaService;
        _orderFieldInformationService = orderFieldInformationService;
        _openPlatformBaseService = openPlatformBaseService;
        _openSupplierService = openSupplierService;
        _agencyService = agencyService;
        _travelLineOrderMessageService = travelLineOrderMessageService;
    }

    public async Task<PagingModel<SearchOutput, OrderStatusStatOutput>> Search(SearchInput input)
    {
        var query = _dbContext.TravelLineOrder
            .Join(_dbContext.BaseOrders, t => t.BaseOrderId, b => b.Id, (t, b) => new
            {
                TravelLineOrder = t,
                BaseOrder = b
            })
            .WhereIF(input.CreateBeginDate.HasValue, x => x.BaseOrder.CreateTime >= input.CreateBeginDate!.Value)
            .WhereIF(input.CreateEndDate.HasValue, x => x.BaseOrder.CreateTime < input.CreateEndDate!.Value.AddDays(1))
            .WhereIF(input.TravelBeginDate.HasValue, x => x.TravelLineOrder.TravelBeginDate >= input.TravelBeginDate!.Value)
            .WhereIF(input.TravelEndDate.HasValue, x => x.TravelLineOrder.TravelBeginDate < input.TravelEndDate!.Value.AddDays(1))
            .WhereIF(input.SupplierId.HasValue, x => x.TravelLineOrder.SupplierId == input.SupplierId!.Value)
            .WhereIF(input.SellingPlatform.HasValue, x => x.BaseOrder.SellingPlatform == input.SellingPlatform!.Value)
            .WhereIF(input.ClaimantId.HasValue, x => x.TravelLineOrder.ClaimantId == input.ClaimantId)
            .WhereIF(input.AgencyId is > 0, x => x.BaseOrder.AgencyId == input.AgencyId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.ProductName), x => x.BaseOrder.ProductName.Contains(input.ProductName!))
            .WhereIF(input.OrderId.HasValue, x => x.BaseOrder.Id == input.OrderId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.ContactsPhoneNumber), x => x.BaseOrder.ContactsPhoneNumber.Contains(input.ContactsPhoneNumber!))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ContactsName), x => x.BaseOrder.ContactsName.Contains(input.ContactsName!))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ChannelOrderNo), x => x.BaseOrder.ChannelOrderNo.Contains(input.ChannelOrderNo!))
            .WhereIF(input.ProductId.HasValue, x => x.TravelLineOrder.LineProductId == input.ProductId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.SupplierOrderId), x => x.TravelLineOrder.SupplierOrderId == input.SupplierOrderId);

        //保险购买状态
        if (input.InsureOrderStatus.HasValue)
        {
            query = query.Join(_dbContext.InsureOrderRelation.AsNoTracking()
                .Where(insure => insure.Status == input.InsureOrderStatus)
                .Select(order => new { order.BaseOrderId }),
                a => a.TravelLineOrder.BaseOrderId, insure => insure.BaseOrderId, (a, insure) => a);
        }

        var dataQuery = query;
        switch (input.Status)
        {
            //B2B查询的待确认状态 = 待认领状态+待确认状态
            case SearchStatus.WaitingForPending:
                var travelLineOrderStatus = new List<TravelLineOrderStatus>
                {
                    TravelLineOrderStatus.WaitingForClaim,
                    TravelLineOrderStatus.WaitingForConfirm
                };
                dataQuery = dataQuery.Where(x => travelLineOrderStatus.Contains(x.TravelLineOrder.Status));
                break;
            case SearchStatus.WaitingForClaim:
                dataQuery = dataQuery.Where(x => x.TravelLineOrder.Status == TravelLineOrderStatus.WaitingForClaim);
                break;
            case SearchStatus.WaitingForConfirm:
                dataQuery = dataQuery.Where(x => x.TravelLineOrder.Status == TravelLineOrderStatus.WaitingForConfirm);
                break;
            case SearchStatus.UnFinished:
                dataQuery = dataQuery.Where(x => x.TravelLineOrder.Status == TravelLineOrderStatus.Confirmed);
                break;
            case SearchStatus.Finished:
                dataQuery = dataQuery.Where(x => x.BaseOrder.Status == BaseOrderStatus.Finished);
                break;
            case SearchStatus.Closed:
                dataQuery = dataQuery.Where(x => x.BaseOrder.Status == BaseOrderStatus.Closed);
                break;
            case SearchStatus.WaitingForPay:
                dataQuery = dataQuery.Where(x => x.BaseOrder.Status == BaseOrderStatus.WaitingForPay);
                break;
        }

        var pagingModel = await dataQuery
            .OrderByDescending(x => x.BaseOrder.CreateTime)
            .PagingAsync(input.PageIndex, input.PageSize, x => new SearchOutput
            {
                BaseOrderId = x.BaseOrder.Id,
                CreateTime = x.BaseOrder.CreateTime,
                UserNickName = x.BaseOrder.UserNickName,
                ContactsName = x.BaseOrder.ContactsName,
                ContactsPhoneNumber = x.BaseOrder.ContactsPhoneNumber,
                ContactsEmail = x.BaseOrder.ContactsEmail,
                PaymentAmount = x.BaseOrder.PaymentAmount,
                PaymentCurrencyCode = x.BaseOrder.PaymentCurrencyCode,
                SellingPlatform = x.BaseOrder.SellingPlatform,
                Status = x.BaseOrder.Status,
                ProductName = x.BaseOrder.ProductName,
                ProductSkuName = x.BaseOrder.ProductSkuName,
                ChannelOrderNos = x.BaseOrder.ChannelOrderNo,
                TravelBeginDate = x.TravelLineOrder.TravelBeginDate,
                Days = x.TravelLineOrder.Days,
                Nights = x.TravelLineOrder.Nights,
                TravelLineOrderStatus = x.TravelLineOrder.Status,
                SupplierId = x.TravelLineOrder.SupplierId,
                PurchaseSourceType = x.TravelLineOrder.PurchaseSourceType
            });
        var statusStats = await query
            .GroupBy(x => new
            {
                BaseOrderStatus = x.BaseOrder.Status,
                TravelLineOrderStatus = x.TravelLineOrder.Status
            })
             .Select(x => new
             {
                 Count = x.Count(),
                 x.Key.BaseOrderStatus,
                 x.Key.TravelLineOrderStatus
             })
             .ToListAsync();
        OrderStatusStatOutput statusStatOutput = new()
        {
            TotalCount = statusStats.Sum(x => x.Count),
            WaitingForClaimCount = statusStats
                .Where(x => x.TravelLineOrderStatus == TravelLineOrderStatus.WaitingForClaim)
                .Sum(x => x.Count),
            WaitingForConfirmCount = statusStats
                .Where(x => x.TravelLineOrderStatus == TravelLineOrderStatus.WaitingForConfirm)
                .Sum(x => x.Count),
            WaitingForPayCount = statusStats
                .Where(x => x.BaseOrderStatus == BaseOrderStatus.WaitingForPay)
                .Sum(x => x.Count),
            ClosedCount = statusStats
                .Where(x => x.BaseOrderStatus == BaseOrderStatus.Closed)
                .Sum(x => x.Count),
            FinishedCount = statusStats
                .Where(x => x.BaseOrderStatus == BaseOrderStatus.Finished)
                .Sum(x => x.Count),
            UnFinishedCount = statusStats
                .Where(x => x.TravelLineOrderStatus == TravelLineOrderStatus.Confirmed)
                .Sum(x => x.Count),
        };

        if (pagingModel.Data.Any())
        {
            var baseOrderIds = pagingModel.Data
                .Where(x => x.PurchaseSourceType == LineProductPurchaseSourceType.InterfaceDock)
                .Select(x => x.BaseOrderId).Distinct().ToArray();
            var supplierDeliveryResultMap = await GetSupplierDeliveryResult(baseOrderIds);
            foreach (var pageItem in pagingModel.Data)
            {
                if (supplierDeliveryResultMap.TryGetValue(pageItem.BaseOrderId, out var supplierDeliveryResult))
                {
                    pageItem.DeliverStatus = supplierDeliveryResult.deliveryStatus;
                    pageItem.DeliveryErrorMsg = supplierDeliveryResult.msg;
                    pageItem.DeliveryErrorCode = supplierDeliveryResult.errorCode;
                }
            }
        }

        PagingModel<SearchOutput, OrderStatusStatOutput> output = new()
        {
            PageSize = pagingModel.PageSize,
            PageIndex = pagingModel.PageIndex,
            Total = pagingModel.Total,
            Data = pagingModel.Data,
            Supplement = statusStatOutput
        };
        return output;
    }

    public async Task Claim(ClaimInput input)
    {
        var travelLineOrder = await _dbContext.TravelLineOrder
            .Where(x => x.BaseOrderId == input.BaseOrderId)
            .WhereIF(input.SupplierId.HasValue, x => x.SupplierId == input.SupplierId!)
            .FirstOrDefaultAsync();
        if (travelLineOrder?.Status != TravelLineOrderStatus.WaitingForClaim)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var operationUser = input.OperationUser;

        travelLineOrder.ClaimantId = operationUser.UserId;
        travelLineOrder.ClaimantName = operationUser.Name;
        travelLineOrder.Status = TravelLineOrderStatus.WaitingForConfirm;//待确认
        travelLineOrder.UpdateTime = DateTime.Now;

        OrderLogs logs = new()
        {
            OrderId = travelLineOrder.BaseOrderId,
            OperationType = OrderOperationType.Claimed,
            OrderLogType = OrderLogType.TravelLine,
            OperationRole = operationUser.UserType,
            UserId = operationUser.UserId,
            UserName = operationUser.Name
        };
        await _dbContext.AddAsync(logs);

        await _dbContext.SaveChangesAsync();
    }

    [UnitOfWork]
    public async Task Confirm(ConfirmInput input)
    {
        var operationUser = input.OperationUser;
        var travelLineOrder = await _dbContext.TravelLineOrder
           .Where(x => x.BaseOrderId == input.BaseOrderId)
           .WhereIF(input.SupplierId.HasValue, x => x.SupplierId == input.SupplierId!)
           .FirstOrDefaultAsync();
        if (travelLineOrder?.Status != TravelLineOrderStatus.WaitingForConfirm)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var baseOrder = await _dbContext.BaseOrders
            .Where(x => x.Id == input.BaseOrderId)
            .FirstOrDefaultAsync();

        //校验份数与出行人数量是否一致
        var productTemplateTypes = new List<ProductTemplateType>
        {
            ProductTemplateType.EachAdult,
            ProductTemplateType.EachChild,
            ProductTemplateType.EachBaby,
            ProductTemplateType.EachElderly,
            ProductTemplateType.EachOther
        };
        var orderFieldInformationTypes = await _dbContext.OrderFieldInformationType
            .IgnoreQueryFilters()
            .AsNoTracking()
            .Where(x => x.BaseOrderId == baseOrder.Id && x.OrderId == travelLineOrder.Id)
            .Where(x => x.TemplateType == TemplateType.Travel && productTemplateTypes.Contains(x.ProductTemplateType))
            .Select(x => new
            {
                x.ProductTemplateType,
                x.TemplateType,
                x.Id
            })
            .ToListAsync();

        var travelLineOrderPrices = await _dbContext.OrderPrices
            .AsNoTracking()
            .Where(x => x.SubOrderId == travelLineOrder.Id &&
                        x.OrderSubType != (int)LineSkuPriceType.RoomPriceDifference)
            .Select(x => new { x.PriceType, x.Price, x.Quantity, x.OrderSubType })
            .ToListAsync();
        var travelLineOrderGroupPrices = travelLineOrderPrices.GroupBy(x => x.OrderSubType).Select(x => new
        {
            x.FirstOrDefault().PriceType,
            x.FirstOrDefault().Price,
            x.FirstOrDefault().OrderSubType,
            Quantity = x.Sum(r => r.Quantity),
        });
        var matchTravellerSuccess = !(from item in travelLineOrderGroupPrices
                                      let productTemplateType = item.OrderSubType switch
                                      {
                                          (int)LineSkuPriceType.Adult => ProductTemplateType.EachAdult,
                                          (int)LineSkuPriceType.Child => ProductTemplateType.EachChild,
                                          (int)LineSkuPriceType.Baby => ProductTemplateType.EachBaby,
                                          (int)LineSkuPriceType.Elderly => ProductTemplateType.EachElderly,
                                          (int)LineSkuPriceType.Other => ProductTemplateType.EachOther,
                                      }
                                      let travelerCount = orderFieldInformationTypes.Count(x => x.ProductTemplateType == productTemplateType)
                                      where item.Quantity != travelerCount
                                      select item).Any();

        //总数量匹配
        var peopleCount = travelLineOrderPrices.Sum(x => x.Quantity);
        var totalQuantity = orderFieldInformationTypes.Count;
        matchTravellerSuccess &= totalQuantity == peopleCount;

        if (!matchTravellerSuccess)
            throw new BusinessException(ErrorTypes.Order.TravelerQuantityError);

        if (travelLineOrder.PurchaseSourceType == LineProductPurchaseSourceType.InterfaceDock)
        {
            //API对接产品采购成功才确认
            var apiOrderPreCheckResult = await PreCheckApiOrder(
                baseOrderId: input.BaseOrderId,
                tenantId: travelLineOrder.TenantId,
                supplierActivityId: travelLineOrder.SupplierActivityId,
                supplierPackageId: travelLineOrder.SupplierPackageId,
                supplierSkuId: travelLineOrder.SupplierSkuId,
                supplierType: travelLineOrder.OpenSupplierType!.Value);
            if (!apiOrderPreCheckResult.IsSuccess)
            {
                throw new BusinessException(apiOrderPreCheckResult.ErrorType);
            }
            else
            {
                //发起创建供应商订单事件
                await _capPublisher.PublishAsync(CapTopics.Order.TravelLineSupplierOrderCreate,
                    new CreateSupplierApiOrderMessage
                    {
                        BaseOrderId = baseOrder.Id,
                        TenantId = baseOrder.TenantId
                    });
            }
        }
        else
        {
            travelLineOrder.Status = TravelLineOrderStatus.Confirmed;
            travelLineOrder.UpdateTime = DateTime.Now;

            OrderLogs logs = new()
            {
                OrderId = travelLineOrder.BaseOrderId,
                OperationType = OrderOperationType.Confirmed,
                OrderLogType = OrderLogType.TravelLine,
                OperationRole = operationUser.UserType,
                UserId = operationUser.UserId,
                UserName = operationUser.Name
            };
            await _dbContext.AddAsync(logs);

            //OTA订单同步确认
            await OtaSyncConfirm(new TravelLineOrderSyncConfirmInput
            {
                BaseOrderId = input.BaseOrderId,
                SellingPlatform = baseOrder.SellingPlatform,
                ChannelOrderNo = baseOrder.ChannelOrderNo,
                TravelLineOrderId = travelLineOrder.Id,
                TenantId = baseOrder.TenantId,
                AgencyId = baseOrder.AgencyId,
                IsChannelTimeliness = travelLineOrder.IsChannelTimeliness,
                TimelinessChannelTypes = travelLineOrder.TimelinessChannelTypes,
                ProductId = travelLineOrder.LineProductId,
                TimelinessTriggerType = OpenChannelTimelinessTriggerType.SupplierCreatedOrder
            });

            await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess, new NotifyMessageProcessMessage
            {
                NotifyEventSubType = NotifyEventSubType.TravelLine_Confirmed,
                NotifyMode = NotifyMode.SiteMessage,
                SendToTheRole = SendToTheRole.AgencyStaff,
                TenantId = baseOrder.TenantId,
                Variables = new
                {
                    BaseOrderId = baseOrder.Id,
                    AgencyId = baseOrder.AgencyId,
                    UserId = baseOrder.UserId,
                    baseOrder.ProductName,
                    baseOrder.ResourceName,
                    baseOrder.ProductSkuName,
                }
            });
        }
    }

    [UnitOfWork]
    public async Task Finish(FinishInput input)
    {
        var order = await _dbContext.TravelLineOrder
            .Join(_dbContext.BaseOrders, t => t.BaseOrderId, b => b.Id, (t, b) => new
            {
                TravelLineOrder = t,
                BaseOrder = b
            })
            .Where(x => x.TravelLineOrder.BaseOrderId == input.BaseOrderId)
            .WhereIF(input.SupplierId.HasValue, x => x.TravelLineOrder.SupplierId == input.SupplierId!)
            .FirstOrDefaultAsync();
        var travelLineOrder = order.TravelLineOrder;
        var baseOrder = order.BaseOrder;
        var insureOrderRelation = await _dbContext.InsureOrderRelation.AsNoTracking()
            .Where(x => x.BaseOrderId == input.BaseOrderId)
            .FirstOrDefaultAsync();
        if (travelLineOrder?.Status != TravelLineOrderStatus.Confirmed)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        if (insureOrderRelation is not null)
        {
            if (insureOrderRelation.Status == InsureOrderStatus.NotPurchased
                && DateTime.Now < travelLineOrder.TravelEndDate.AddDays(1))//算一整天
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }
        travelLineOrder.Status = TravelLineOrderStatus.Finished;
        travelLineOrder.UpdateTime = DateTime.Now;
        baseOrder.Status = BaseOrderStatus.Finished;
        baseOrder.UpdateTime = DateTime.Now;


        var operationUser = input.OperationUser;
        OrderLogs logs = new()
        {
            OrderId = travelLineOrder.BaseOrderId,
            OperationType = OrderOperationType.Finished,
            OrderLogType = OrderLogType.TravelLine,
            OperationRole = operationUser.UserType,
            UserId = operationUser.UserId,
            UserName = operationUser.Name
        };
        await _dbContext.AddAsync(logs);

        if (baseOrder.SellingPlatform == SellingPlatform.B2BWeb || baseOrder.SellingPlatform == SellingPlatform.B2BApplet)
        {
            var orderPrice = await _dbContext.OrderPrices
                     .Where(x => x.BaseOrderId == baseOrder.Id)
                     .Select(x => new { x.ExchangeRate, x.OrgPriceCurrencyCode })
                     .FirstOrDefaultAsync();
            await _capPublisher.PublishAsync(CapTopics.Tenant.SyncOrderChangeGrowUpValue, new Contracts.Common.Tenant.DTOs.AgencyLevelDetail.SyncOrderChangeGrowUpValueInput
            {
                AgencyId = baseOrder.AgencyId,
                BusinessType = Contracts.Common.Tenant.Enums.AgencyLevelBusinessType.Line,
                OrderAmout = baseOrder.PaymentAmount / orderPrice.ExchangeRate,
                OrderNo = baseOrder.Id.ToString(),
                Title = baseOrder.ProductName,
            });
        }

        //记录跟踪日志
        var shareInfo = await _dbContext.OrderShareInfos.AsNoTracking()
            .FirstOrDefaultAsync(x => x.BaseOrderId == baseOrder.Id);
        if (shareInfo?.TraceId is > 0)
        {
            await _capPublisher.PublishAsync(CapTopics.Marketing.PushPromotionTraceRecord,
                new PushPromotionTraceRecordMessage
                {
                    PromotionTraceId = shareInfo.TraceId.Value,
                    CustomerId = shareInfo.BuyerId,
                    BehaviorType = TraceBehaviorType.CompleteOrder,
                    OrderType = baseOrder.OrderType,
                    OrderId = baseOrder.Id,
                    VisitTargetName = baseOrder.ProductName
                });
        }

        /*
         * Saas订单状态为已确认/完成，提示飞猪渠道插旗为红色
         */
        await _capPublisher.PublishAsync(CapTopics.Order.ChannelOrderFlagModify, new ChannelOrderFlagModifyMessage
        {
            ChannelOrderNo = baseOrder.ChannelOrderNo,
            SellingPlatform = baseOrder.SellingPlatform,
            FlagType = OpenChannelOrderFlagType.Red,
            AgencyId = baseOrder.AgencyId,
            TenantId = baseOrder.TenantId
        });

        // await _dbContext.SaveChangesAsync();
    }

    public async Task<OrderDetailOutput> GetDetail(OrderDetailInput input)
    {
        var order = await _dbContext.TravelLineOrder
           .Join(_dbContext.BaseOrders, t => t.BaseOrderId, b => b.Id, (t, b) => new
           {
               TravelLineOrder = t,
               BaseOrder = b
           })
           .AsNoTracking()
           .Where(x => x.TravelLineOrder.BaseOrderId == input.BaseOrderId)
           .WhereIF(input.UserId.HasValue, x => x.BaseOrder.UserId == input.UserId!.Value)
           .WhereIF(input.AgencyId.HasValue, x => x.BaseOrder.AgencyId == input.AgencyId!.Value)
           .WhereIF(input.SupplierId.HasValue, x => x.TravelLineOrder.SupplierId == input.SupplierId!.Value)
           .FirstOrDefaultAsync();

        if (order is null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        var travelers = await _dbContext.TravelLineOrderTravelers
            .Where(x => x.TravelLineOrderId == order.TravelLineOrder.Id)
            .ToListAsync();
        var orderTravelers = _mapper.Map<List<OrderTravelerOutput>>(travelers);

        var orderPrices = await _dbContext.OrderPrices
            .Where(x => x.SubOrderId == order.TravelLineOrder.Id)
            .Select(x => new TravelLineOrderPriceOutput
            {
                Type = (LineSkuPriceType)x.OrderSubType,
                OrderSubItemId = x.OrderSubItemId,
                PriceType = x.PriceType,
                PaymentCurrencyCode = x.PaymentCurrencyCode,
                Price = x.Price,
                CostPriceType = x.CostPriceType,
                CostCurrencyCode = x.CostCurrencyCode,
                CostPrice = x.CostPrice,
                CostDiscountRate = x.CostDiscountRate,
                OrgCostPrice = x.OrgCostPrice,
                OrgPrice = x.OrgPrice,
                OrgPriceCurrencyCode = x.OrgPriceCurrencyCode,
                Quantity = x.Quantity,
                ExchangeRate = x.ExchangeRate,
            })
            .ToListAsync();
        var skuTypeItems = await _dbContext.TravelLineOrderSkuTypeItems.AsNoTracking()
            .Where(x => x.BaseOrderId == order.BaseOrder.Id)
            .ToListAsync();
        foreach (var orderPrice in orderPrices)
        {
            var skuItem = skuTypeItems.FirstOrDefault(x => x.Id == orderPrice.OrderSubItemId);
            orderPrice.OrderSubItemName = skuItem?.SkuTypeItemName;
        }
        var travelLineOrder = _mapper.Map<TravelLineOrderDetailOutput>(order.TravelLineOrder);
        travelLineOrder.Latitude = order.TravelLineOrder.RallyPointLocation?.Y;
        travelLineOrder.Longitude = order.TravelLineOrder.RallyPointLocation?.X;
        travelLineOrder.CoordinateType = order.TravelLineOrder.CoordinateType;

        var orderFieldTypes = await _dbContext.OrderFieldInformationType
                            .Where(x => x.BaseOrderId == order.BaseOrder.Id)
                            .ToListAsync();
        var orderFieldTypesOut = _mapper.Map<List<OrderFieldInformationTypeOutput>>(orderFieldTypes);
        var orderFieldTypeIds = orderFieldTypes.Select(x => x.Id).ToList();
        var orderFields = await _dbContext.OrderFieldInformations
                           .Where(x => orderFieldTypeIds.Contains(x.OrderFieldInformationTypeId))
                           .ToListAsync();
        var orderFieldsOut = _mapper.Map<List<OrderFieldInformationOutput>>(orderFields);
        orderFieldTypesOut.ForEach(type =>
        {
            var fields = orderFieldsOut.Where(x => x.OrderFieldInformationTypeId == type.Id).OrderBy(x => x.Sort).ToList();
            type.Fields = fields;
        });

        var orderDetail = new OrderDetailOutput
        {
            BaseOrder = _mapper.Map<BaseOrderDetailOutput>(order.BaseOrder),
            TravelLineOrder = travelLineOrder,
            OrderTravelers = orderTravelers,
            OrderPrices = orderPrices,
            OrderFields = orderFieldTypesOut
        };
        orderDetail.BaseOrder.ChannelOrderNo = order.BaseOrder.ChannelOrderNo?.Split(",", StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
                                               ?? Array.Empty<string>();

        var settlementOrder = await _dbContext.SettlementOrderDetails
                                 .Join(_dbContext.SettlementOrders, detail => detail.SettlementOrderId, settl => settl.Id, (detail, settl) => new { detail, settl })
                                 .Where(s => s.detail.BaseOrderId == order.BaseOrder.Id)
                                 // .Where(x => x.settl.Status == SettlementOrderStatus.PaymentCompleted) //付款结算单是否已付款
                                 .Select(x => new { x.detail.BaseOrderId, x.settl.Status }).FirstOrDefaultAsync();

        orderDetail.BaseOrder.ExistSettlementOrder = settlementOrder != null;
        orderDetail.BaseOrder.SettlementPayed = settlementOrder != null;

        #region 基础分销信息

        if (orderDetail.BaseOrder.AgencyId is > 0)
        {
            orderDetail.DistributionChannelInfo = new DistributionChannelInfo
            {
                AgencyId = orderDetail.BaseOrder.AgencyId,
                AgencyName = orderDetail.BaseOrder.AgencyName,
                SellingChannels = orderDetail.BaseOrder.SellingChannels,
                SellingPlatform = orderDetail.BaseOrder.SellingPlatform,
                ChannelOrderNo = orderDetail.BaseOrder.ChannelOrderNo
            };
        }
        else
        {
            orderDetail.DistributionChannelInfo = new DistributionChannelInfo
            {
                SellingChannels = orderDetail.BaseOrder.SellingChannels,
                SellingPlatform = orderDetail.BaseOrder.SellingPlatform
            };
        }

        #endregion

        #region 基础供货信息

        orderDetail.SupplyChannelInfo = new SupplyChannelInfo
        {
            SupplierId = orderDetail.TravelLineOrder.SupplierId
        };
        if (orderDetail.TravelLineOrder.SupplierId > 0)
        {
            var supplierInfo = await _httpClientFactory.InternalGetAsync<GetSupplierOutput>(
                requestUri: _servicesAddress.Value.Tenant_GetSupplier(orderDetail.TravelLineOrder.SupplierId));

            orderDetail.SupplyChannelInfo.SupplierName = supplierInfo?.FullName;
            orderDetail.SupplyChannelInfo.SupplierOrderId = orderDetail.TravelLineOrder.SupplierOrderId;
            orderDetail.SupplyChannelInfo.SupplierType = supplierInfo?.SupplierType;
            orderDetail.SupplyChannelInfo.SupplierApiType = supplierInfo?.SupplierApiSetting?.SupplierApiType;
        }

        #endregion

        #region 自动确认失败提示

        if (orderDetail.TravelLineOrder is { Status: TravelLineOrderStatus.WaitingForConfirm, AutoConfirm: true })
        {
            var logs = await _dbContext.OrderLogs.AsNoTracking()
                .Where(x => x.OrderId == input.BaseOrderId)
                .ToListAsync();
            var autoConfirmFailed = logs.Any(x => x is
            { OrderLogType: OrderLogType.TravelLine, OperationType: OrderOperationType.AutoConfirmFailed });

            if (autoConfirmFailed)
            {
                orderDetail.DeliveryResult = new DeliveryResult
                {
                    DeliveryErrorCode = (int)OrderBusinessErrorCodeType.LineAutoConfirmFailed
                };
            }
        }

        #endregion

        #region 发货信息处理

        if (travelLineOrder.PurchaseSourceType == LineProductPurchaseSourceType.InterfaceDock)
        {
            var deliveryResultMap = await GetSupplierDeliveryResult(input.BaseOrderId);
            //发货结果
            if (deliveryResultMap.TryGetValue(input.BaseOrderId, out var deliveryResult))
            {
                orderDetail.DeliveryResult = new DeliveryResult
                {
                    DeliveryErrorMsg = deliveryResult.msg,
                    DeliverStatus = deliveryResult.deliveryStatus,
                    DeliveryErrorCode = deliveryResult.errorCode
                };
                orderDetail.SupplyChannelInfo.SupplierOrderStatus = deliveryResult.supplierOrderStatus;
            }
        }
        else
        {
            var deliveryResultMap = await GetOfflinePurchaseChannelDeliveryResult(input.BaseOrderId);
            //发货结果
            if (deliveryResultMap.TryGetValue(input.BaseOrderId, out var deliveryResult))
            {
                orderDetail.DeliveryResult = new DeliveryResult
                {
                    DeliveryErrorMsg = deliveryResult.msg,
                    DeliverStatus = deliveryResult.deliveryStatus,
                    DeliveryErrorCode = deliveryResult.errorCode
                };
            }
        }

        #endregion

        #region OTA订单

        var otaSellingPlatform = _travelLineOtaService.OtaSellingPlatforms;
        GetAgenciesByIdsOutput? agencyInfo = null;
        if (orderDetail.BaseOrder.AgencyId is > 0)
        {
            agencyInfo = await _agencyService.GetAgencyDetail(orderDetail.BaseOrder.AgencyId, check: false);
        }
        //判断手工单是否对接分销商
        if (orderDetail.BaseOrder.SellingPlatform == SellingPlatform.System)
        {
            if (agencyInfo != null)
            {
                var systemPlatformOrderCheck = _openPlatformBaseService.CheckSystemPlatformOrder(agencyInfo.AgencyType,
                    agencyInfo.AgencyApiType,
                    otaSellingPlatform);
                otaSellingPlatform = systemPlatformOrderCheck.otaPlatform;
            }
        }
        if (otaSellingPlatform.Contains(orderDetail.BaseOrder.SellingPlatform))
        {
            orderDetail.DistributionChannelInfo.AgencyApiType = agencyInfo?.AgencyApiType;

            var otaOrder = await _dbContext.TravelLineOtaOrders.AsNoTracking()
                .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);

            orderDetail.DistributionChannelInfo.ChannelOrderStatus = orderDetail.DeliveryResult?.DeliverStatus == TravelLineOrderDeliveryStatus.Success
                ? TravelLineOtaOrderStatus.Delivered
                : otaOrder?.Status ?? TravelLineOtaOrderStatus.WaitingForDeliver;

        }

        #endregion

        #region 确认时间&完成时间

        var finishedOperationTypes = new[]
        {
            OrderOperationType.Finished, OrderOperationType.FinishedByManual, OrderOperationType.FinishedByOverdue
        };
        var orderLogs = await _dbContext.OrderLogs.AsNoTracking()
            .Where(x => x.OrderId == input.BaseOrderId &&
                        x.OrderLogType == OrderLogType.TravelLine &&
                        (x.OperationType == OrderOperationType.Confirmed ||
                         finishedOperationTypes.Contains(x.OperationType)))
            .ToListAsync();

        orderDetail.TravelLineOrder.ConfirmTime =
            orderLogs.Where(x => x.OperationType == OrderOperationType.Confirmed)
                .MaxBy(x => x.Id)?.CreateTime;
        orderDetail.TravelLineOrder.FinishTime = orderLogs
                .Where(x => finishedOperationTypes.Contains(x.OperationType))
                .MaxBy(x => x.Id)?.CreateTime;

        #endregion

        #region 判断渠道单号是否可以编辑

        //渠道订单号不支持修改
        if (_travelLineOtaService.OtaSellingPlatforms.Contains(order.BaseOrder.SellingPlatform))
        {
            var channelOrderNos = await EditChannelOrderNoCheck(order.BaseOrder.Id);
            foreach (var item in orderDetail.BaseOrder.ChannelOrderNo)
            {
                orderDetail.EditableStatusOfChannelOrderNos.Add(new EditableStatusOfChannelOrderNo
                {
                    ChannelOrderNo = item,
                    CanEdit = !channelOrderNos.Contains(item)
                });
            }
        }

        #endregion

        return orderDetail;
    }

    public async Task<GetLineChannelOrderInfoOutput> GetChannelOrderInfo(string channelOrderNo)
    {
        var baseOrder = await _dbContext.BaseOrders.AsNoTracking()
            .Where(x => x.ChannelOrderNo == channelOrderNo)
            .OrderByDescending(x => x.Id)
            .FirstOrDefaultAsync();
        if (baseOrder == null) return null;

        var result = new GetLineChannelOrderInfoOutput()
        {
            ChannelOrderNo = channelOrderNo,
            ContactsName = baseOrder.ContactsName,
            ContactsEmail = baseOrder.ContactsEmail,
            ContactsPhoneNumber = baseOrder.ContactsPhoneNumber
        };

        result.OrderPrices = await _dbContext.OrderPrices
            .Where(x => x.BaseOrderId == baseOrder.Id)
            .Select(x => new TravelLineOrderPriceOutput
            {
                Type = (LineSkuPriceType)x.OrderSubType,
                OrderSubItemId = x.OrderSubItemId,
                PriceType = x.PriceType,
                PaymentCurrencyCode = x.PaymentCurrencyCode,
                Price = x.Price,
                CostPriceType = x.CostPriceType,
                CostCurrencyCode = x.CostCurrencyCode,
                CostPrice = x.CostPrice,
                OrgCostPrice = x.OrgCostPrice,
                OrgPrice = x.OrgPrice,
                OrgPriceCurrencyCode = x.OrgPriceCurrencyCode,
                Quantity = x.Quantity,
                ExchangeRate = x.ExchangeRate,
            })
            .ToListAsync();

        var skuTypeItems = await _dbContext.TravelLineOrderSkuTypeItems.AsNoTracking()
            .Where(x => x.BaseOrderId == baseOrder.Id)
            .ToListAsync();
        if (skuTypeItems.Any())
        {
            foreach (var item in result.OrderPrices)
            {
                item.OrderSubItemName = skuTypeItems.FirstOrDefault(x => x.Id == item.OrderSubItemId)?.SkuTypeItemName ?? string.Empty;
            }
        }

        result.PaymentAmount = baseOrder.PaymentAmount;
        result.DiscountAmount = baseOrder.DiscountAmount;

        var orderFieldTypes = await _dbContext.OrderFieldInformationType
            .Where(x => x.BaseOrderId == baseOrder.Id)
            .ToListAsync();
        var orderFieldTypesOut = _mapper.Map<List<OrderFieldInformationTypeOutput>>(orderFieldTypes);
        var orderFieldTypeIds = orderFieldTypes.Select(x => x.Id).ToList();
        var orderFields = await _dbContext.OrderFieldInformations
            .Where(x => orderFieldTypeIds.Contains(x.OrderFieldInformationTypeId))
            .ToListAsync();
        var orderFieldsOut = _mapper.Map<List<OrderFieldInformationOutput>>(orderFields);
        orderFieldTypesOut.ForEach(type =>
        {
            var fields = orderFieldsOut.Where(x => x.OrderFieldInformationTypeId == type.Id).OrderBy(x => x.Sort).ToList();
            type.Fields = fields;
        });
        result.OrderFields = orderFieldTypesOut;

        return result;
    }

    public async Task<OrderRefundableOutput> OrderRefundable(OrderRefundableInput input)
    {
        OperationUserDto operationUser = input.OperationUser;
        var baseOrder = await _dbContext.BaseOrders
            .Where(x => x.Id == input.BaseOrderId)
            .WhereIF(operationUser.UserType == UserType.Customer, x => x.UserId == operationUser.UserId)
            .WhereIF(operationUser.UserType == UserType.Agency, x => x.AgencyId == operationUser.AgencyId)
            .Select(x => new { x.Id, x.PaymentAmount, x.PaymentCurrencyCode, x.Status, x.OrderCategory })
            .FirstOrDefaultAsync();
        if (baseOrder?.Status != BaseOrderStatus.UnFinished)
            throw new BusinessException(ErrorTypes.Order.OrderCannotRefund);

        var existsOffsetOrder = await _dbContext.OffsetOrders
            .AnyAsync(x => x.BaseOrderId == input.BaseOrderId);
        if (existsOffsetOrder)
            throw new BusinessException(ErrorTypes.Order.RefundRefunseByOffsetOrder);

        var travelLineOrder = await _dbContext.TravelLineOrder
          .Where(x => x.BaseOrderId == input.BaseOrderId)
          .Select(x => new
          {
              TravelLineOrderId = x.Id,
              x.IsSupportRefund,
              x.RefundRate,
              x.RefundBeforeTravelDateDay,
              x.RefundTravelDateTime,
              x.TravelBeginDate,
              x.Status
          })
          .FirstOrDefaultAsync();

        var orderPrices = await _dbContext.OrderPrices
           .Where(x => x.SubOrderId == travelLineOrder.TravelLineOrderId)
           .Select(x => new OrderPriceOutput
           {
               Type = (LineSkuPriceType)x.OrderSubType,
               Quantity = x.Quantity
           })
           .ToListAsync();
        OrderRefundableOutput output = new()
        {
            IsSupportRefund = travelLineOrder.IsSupportRefund,
            RefundRate = travelLineOrder.RefundRate,
            RefundBeforeTravelDateDay = travelLineOrder.RefundBeforeTravelDateDay,
            RefundTravelDateTime = travelLineOrder.RefundTravelDateTime,
            TravelBeginDate = travelLineOrder.TravelBeginDate,
            PaymentAmount = baseOrder.PaymentAmount,
            PaymentCurrencyCode = baseOrder.PaymentCurrencyCode,
            RefundableAmount = operationUser.UserType switch
            {
                UserType.Merchant => baseOrder.PaymentAmount,
                _ => Math.Round(baseOrder.PaymentAmount * (travelLineOrder.RefundRate ?? 0), 2)
            },
            OrderPrices = orderPrices,
            OperatorUserType = operationUser.UserType,
            OrderCategory = baseOrder.OrderCategory
        };
        return output;
    }

    [UnitOfWork]
    public async Task Refund(OrderRefundInput input)
    {
        var operationUser = input.OperationUser;
        var orderRefundableInfo = await OrderRefundable(new OrderRefundableInput
        {
            BaseOrderId = input.BaseOrderId,
            OperationUser = operationUser
        });

        if (orderRefundableInfo.Refundable is not true)
            throw new BusinessException(ErrorTypes.Order.OrderCannotRefund);
        if (input.RefundAmount.HasValue && orderRefundableInfo.RefundableAmount < input.RefundAmount.Value)
            throw new BusinessException(ErrorTypes.Order.RefundAmountInvalid);
        var refundAmount = input.RefundAmount ?? orderRefundableInfo.RefundableAmount;
        var travelLineOrder = await _dbContext.TravelLineOrder
            .Where(x => x.BaseOrderId == input.BaseOrderId)
            .FirstOrDefaultAsync();
        if (travelLineOrder.Status == TravelLineOrderStatus.Refunding || travelLineOrder.Status == TravelLineOrderStatus.Refunded)
            throw new BusinessException(ErrorTypes.Order.OrderCannotRefund);

        var orderPrices = await _dbContext.OrderPrices
            .Where(x => x.SubOrderId == travelLineOrder.Id)
            .ToListAsync();

        travelLineOrder.Status = TravelLineOrderStatus.Refunding;

        var quantity = orderPrices.Where(x => x.OrderSubType != (int)LineSkuPriceType.RoomPriceDifference)
            .Sum(s => s.Quantity);
        var cost = orderPrices.Sum(x => x.CostPrice * x.Quantity);
        var costCurrencyCode = orderPrices.First().CostCurrencyCode;
        RefundOrderApplyMessage command = new()
        {
            TenantId = travelLineOrder.TenantId,
            BaseOrderId = travelLineOrder.BaseOrderId,
            SubOrdeId = travelLineOrder.Id,
            SupplierId = travelLineOrder.SupplierId,
            UserType = operationUser.UserType,
            UserId = operationUser.UserId,
            UserName = operationUser.Name,
            Quantity = quantity,
            Cost = cost,
            CostCurrencyCode = costCurrencyCode,
            TotalAmount = refundAmount,
            PaymentCurrencyCode = orderRefundableInfo.PaymentCurrencyCode,
            HasReviewed = operationUser.UserType == UserType.Merchant,
            ProofImgs = string.Join(",", input.ProofImgs),
            Reason = input.Reason,
            RefundOrderType = RefundOrderType.TravelLine,
        };

        await _capPublisher.PublishAsync(CapTopics.Order.OrderRefundApply, command);

        // 如果是补差主单.关联的子单需要全部走退款流程
        if ((orderRefundableInfo.OrderCategory & OrderCategory.CompensationMainOrder) != 0)
        {
            var compensationOrderIds = await _dbContext.CompensationOrderBindItems.AsNoTracking()
                .Where(x => x.BaseOrderId == input.BaseOrderId)
                .Select(x => x.CompensationBaseOrderId)
                .ToListAsync();
            if (compensationOrderIds.Any())
            {
                var capHeaders = new Dictionary<string, string?> { { "Tenant", travelLineOrder.TenantId.ToString() } };
                await _capPublisher.PublishAsync(CapTopics.Order.CompensationOrderRefund,
                    new CompensationOrderRefundMessage
                    {
                        BaseOrderIds = compensationOrderIds,
                        Reason = input.Reason,
                        OrderType = OrderType.TravelLineOrder,
                        OperationUserDto = operationUser
                    },
                    headers: capHeaders);
            }
        }
    }

    [UnitOfWork]
    public async Task<CreateLineOrderOutput> Create(CreateDto dto)
    {
        var result = new CreateLineOrderOutput();
        var contactFields = dto.OrderFields.FirstOrDefault(x => x.TemplateType == TemplateType.Contacts)?.Fields;
        if (contactFields is not null)
        {
            var contactOrderField = _orderFieldInformationService.ChangeContact(contactFields);
            dto.ContactsEmail = contactOrderField.ContactsEmail;
            dto.ContactsName = contactOrderField.ContactsName;
            dto.ContactsPhoneNumber = contactOrderField.ContactsPhoneNumber;
        }

        OperationUserDto operationUser = dto.OperationUser;
        var orderUserInfo = dto.OrderUserInfo;
        var lineProductResponse = dto.LineProduct;
        var lineProductSkuResponse = dto.LineProductSku;
        var otaSellingPlatform = _travelLineOtaService.OtaSellingPlatforms;
        //非OTA渠道需要算价 和 异常订单处理需要算价 
        if (!otaSellingPlatform.Contains(dto.SellingPlatform) || dto.SyncFailOrderId.HasValue)
        {
            dto.TotalAmount = dto.TravelLineOrderMultPrices.Sum(x => x.OrderMultPrice.Price * x.Quantity);//总价
            dto.DiscountAmount = dto.OrderDiscountItem?.DiscountAmount ?? 0;                                                                 //计算订单金额
            dto.PaymentAmount = dto.TotalAmount - dto.DiscountAmount;
        }
        BaseOrder baseOrder = new()
        {
            UserId = orderUserInfo.UserId,
            UserNickName = orderUserInfo.UserNickName,
            VipLevelId = orderUserInfo.VipLevelId,
            VipLevelName = orderUserInfo.VipLevelName,
            AgencyId = orderUserInfo.AgencyId,
            AgencyName = orderUserInfo.AgencyName,
            SalespersonId = orderUserInfo.SalespersonId,
            SalespersonName = orderUserInfo.SalespersonName,
            ContactsName = dto.ContactsName ?? "",
            ContactsPhoneNumber = dto.ContactsPhoneNumber ?? "",
            ContactsEmail = dto.ContactsEmail,
            ProductName = lineProductResponse.Title,
            EnProductName = lineProductResponse.EnTitle,
            ProductSkuName = lineProductSkuResponse.LineProductSkuName,
            OrderType = OrderType.TravelLineOrder,
            PaymentCurrencyCode = dto.PaymentCurrencyCode,
            PaymentAmount = dto.PaymentAmount,
            TotalAmount = dto.TotalAmount,
            DiscountAmount = dto.DiscountAmount,
            Status = BaseOrderStatus.WaitingForPay,
            SellingPlatform = dto.SellingPlatform,
            SellingChannels = dto.SellingChannel,
            ChannelOrderNo = dto.ChannelOrderNo,
            Message = dto.Message,
            DevelopUserId = lineProductResponse.DevelopUserId,
            OrderCategory = OrderCategory.RegularOrder
        };

        if (dto.LineProduct.IsCompensation)
        {
            baseOrder.OrderCategory = OrderCategory.CompensationOrder;//补差单
        }

        //异常订单创单
        if (dto.SyncFailOrderId.HasValue)
        {
            //则将异常订单的订单号赋值给主订单
            baseOrder.Id = dto.SyncFailOrderId.Value;

            //赋值跟单人id
            var syncFailOrder = await _dbContext.OpenChannelSyncFailOrders.AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == dto.SyncFailOrderId.Value);
            baseOrder.TrackingUserId = syncFailOrder?.TrackingUserId;
        }

        var platform = dto.SellingPlatform;
        // 手工单下单时按照所选“来源渠道”给订单赋值运营人
        if (dto.SellingPlatform == SellingPlatform.System)
        {
            platform = baseOrder.SellingChannels switch
            {
                SellingChannels.Ctrip => SellingPlatform.Ctrip,
                SellingChannels.Meituan => SellingPlatform.Meituan,
                SellingChannels.Fliggy => SellingPlatform.Fliggy,
                SellingChannels.B2b => SellingPlatform.B2BWeb,
                _ => SellingPlatform.System,
            };
        }
        var productOperatorUser = lineProductResponse.ProductOperatorUser?.FirstOrDefault(x => x.SellingPlatform == platform);
        baseOrder.OperatorUserId = productOperatorUser?.OperatorUserId;
        baseOrder.OperatorAssistantUserId = productOperatorUser?.OperatorAssistantUserId;

        var RallyPoint = lineProductResponse.RallyPoints
            .FirstOrDefault(x => x.Id == dto.LineProductRallyPointId);
        //存在上车点&&未设置上车点
        if (RallyPoint is null && lineProductResponse.RallyPoints?.Any() is true)
        {
            throw new BusinessException(ErrorTypes.Order.LineRallyPointNotExists);
        }
        var feeIncludes = lineProductSkuResponse.FeeIncludes
            .Select(x => new Model.FeeInclude { FeeType = x.FeeType, TouristType = x.TouristType, Description = x.Description })
            .ToList();
        OpenSupplierType? openSupplierType = null;
        if (lineProductResponse.PurchaseSourceType == LineProductPurchaseSourceType.InterfaceDock)
        {
            var supplierApiType =
                _openPlatformBaseService.MapPriceInventorySourceToSupplierApiType(lineProductResponse.PriceInventorySource);
            openSupplierType = _openPlatformBaseService.MapSupplierApiTypeToOpenSupplierType(supplierApiType);
        }


        TravelLineOrder travelLineOrder = new()
        {
            BaseOrderId = baseOrder.Id,
            NumberOfRooms = dto.NumberOfRooms,
            TravelBeginDate = dto.TravelDate.Date,
            TravelEndDate = dto.TravelDate.AddDays(lineProductResponse.Days - 1),
            RallyPointTime = RallyPoint?.Time,
            RallyPointAddress = RallyPoint?.Address,
            Status = TravelLineOrderStatus.WaitingForPay,
            LineProductId = lineProductResponse.Id,
            LineProductSkuId = lineProductSkuResponse.LineProductSkuId,
            Days = lineProductResponse.Days,
            Nights = lineProductResponse.Nights,
            SupplierId = lineProductResponse.SupplierId,
            SupplierOrderId = dto.SupplierOrderId,//采购单号
            ImagePath = lineProductResponse.ProductPictures.FirstOrDefault(),
            OtherNote = lineProductResponse.OtherNote,
            FeeNote = lineProductResponse.FeeNote,
            FeeNotNote = lineProductResponse.FeeNotNote,
            FeeIncludes = feeIncludes,
            IsSupportRefund = lineProductResponse.IsSupportRefund,
            RefundRate = lineProductResponse.RefundRate ?? 0,
            RefundBeforeTravelDateDay = lineProductResponse.RefundBeforeTravelDateDay,
            RefundTravelDateTime = lineProductResponse.RefundTravelDateTime,
            AdultsStandard = lineProductResponse.AdultsStandard,
            ChildrenStandard = lineProductResponse.ChildrenStandard,
            BabyStandard = lineProductResponse.BabyStandard,
            ElderlyStandard = lineProductResponse.ElderlyStandard,
            CoordinateType = RallyPoint?.CoordinateType ?? CoordinateType.BD09,
            AutoConfirm = lineProductResponse.AutoConfirm,
            PurchaseSourceType = lineProductResponse.PurchaseSourceType,
            SupplierActivityId = lineProductResponse.OpenSupplierSettingInfo?.ActivityId,
            SupplierPackageId = lineProductResponse.OpenSupplierSettingInfo?.PackageId,
            SupplierSkuId = lineProductResponse.OpenSupplierSettingInfo?.SkuId,
            OpenSupplierType = openSupplierType,
            TimeSlotName = lineProductSkuResponse.TimeSlotName,
            IsChannelTimeliness = lineProductResponse.IsChannelTimeliness
        };
        if (lineProductResponse.TimelinessChannelSettingInfos.Any())
        {
            travelLineOrder.TimelinessChannelTypes = lineProductResponse.TimelinessChannelSettingInfos
                .Select(x => x.TimelinessChannelType)
                .Aggregate(new PriceInventorySyncChannelType(), (current, item) => current | item);
        }
        if (RallyPoint is not null && RallyPoint.Longitude.HasValue && RallyPoint.Latitude.HasValue)
            travelLineOrder.SetLocation(RallyPoint.Longitude.Value, RallyPoint.Latitude.Value);

        //记录套餐票种项数据
        var skuTypeItems = dto.LineProductSkuTypeItems.Select(item => new TravelLineOrderSkuTypeItem
        {
            BaseOrderId = baseOrder.Id,
            TravelLineOrderId = travelLineOrder.Id,
            SkuTypeItemId = item.SkuTypeItemId,
            SkuTypeItemName = item.SkuTypeItemName,
            ActivityId = item.ActivityId,
            PackageId = item.PackageId,
            SkuId = item.SkuId,
            SkuPriceType = item.SkuPriceType
        })
            .ToList();

        // 记录供应商-订单附加信息
        if (openSupplierType.HasValue && dto.OrderExtraInfos.Any())
        {
            var extraInfos = dto.OrderExtraInfos
                .Where(x => !string.IsNullOrEmpty(x.OptionValue))
                .Select(x => new OpenSupplierOrderExtraInfo
                {
                    BaseOrderId = baseOrder.Id,
                    OrderType = baseOrder.OrderType,
                    OpenSupplierType = openSupplierType!.Value,
                    DataType = x.DataType,
                    OptionKey = x.OptionKey,
                    OptionValue = x.OptionValue
                })
                .ToList();
            await _dbContext.AddRangeAsync(extraInfos);
        }

        var orderFieldTypes = new List<OrderFieldInformationType>();
        var fields = new List<OrderFieldInformation>();
        foreach (var orderField in dto.OrderFields)
        {
            var typeInfo = _mapper.Map<OrderFieldInformationType>(orderField);
            typeInfo.BaseOrderId = travelLineOrder.BaseOrderId;
            typeInfo.OrderId = travelLineOrder.Id;
            typeInfo.OrderType = baseOrder.OrderType;
            var typeFields = _mapper.Map<List<OrderFieldInformation>>(orderField.Fields);
            typeFields.ForEach(x =>
            {
                x.OrderFieldInformationTypeId = typeInfo.Id;
                x.ProductTemplateType = typeInfo.ProductTemplateType;
            });
            fields.AddRange(typeFields);
            orderFieldTypes.Add(typeInfo);
        }


        List<OrderPrice> orderPrices = new();
        var travellerOrderMultPrices = dto.TravelLineOrderMultPrices;
        foreach (var travelLineOrderMultPrice in travellerOrderMultPrices)
        {
            var orderMultPrice = travelLineOrderMultPrice.OrderMultPrice;
            var orderPriceSkuItem = dto.LineProductSkuTypeItems.FirstOrDefault(x => x.SkuTypeItemId == travelLineOrderMultPrice.LineSkuTypeItemId);
            var costDiscountRate = orderPriceSkuItem?.CostDiscountRate ?? dto.LineProductSku.CostDiscountRate; // 非API取套餐层级,API取关联票种层级
            OrderPrice orderPrice = new()
            {
                BaseOrderId = travelLineOrder.BaseOrderId,
                SubOrderId = travelLineOrder.Id,
                OrderType = OrderType.TravelLineOrder,
                OrderSubType = (int)travelLineOrderMultPrice.Type,
                OrderSubItemId = travelLineOrderMultPrice.LineSkuTypeItemId,
                OrgPrice = orderMultPrice.OrgPrice,
                OrgCostPrice = orderMultPrice.OrgCostPrice,
                PriceType = orderMultPrice.PriceType,
                Price = orderMultPrice.Price,
                CostPrice = orderMultPrice.CostPrice,
                CostPriceType = orderMultPrice.CostPriceType,
                Quantity = orderMultPrice.Quantity,
                PaymentCurrencyCode = orderMultPrice.PaymentCurrencyCode,
                CostCurrencyCode = orderMultPrice.CostCurrencyCode,
                OrgPriceCurrencyCode = orderMultPrice.SaleCurrencyCode,
                CostExchangeRate = orderMultPrice.CostExchangeRate,
                ExchangeRate = orderMultPrice.ExchangeRate,
                CostDiscountRate = costDiscountRate,
            };
            orderPrices.Add(orderPrice);
        }

        //计算总折扣总额
        baseOrder.CostDiscountAmount =
            Math.Round(orderPrices.Sum(x => x.CostPrice * x.Quantity * x.CostDiscountRate / 100), 2);

        if (dto.OrderDiscountItem?.DiscountId > 0)
        {
            //订单优惠
            await _dbContext.BaseOrderDiscounts.AddAsync(new BaseOrderDiscount
            {
                BaseOrderId = baseOrder.Id,
                DiscountType = OrderDiscountType.UserCoupon,
                DiscountAmount = dto.OrderDiscountItem.DiscountAmount,
                DiscountId = dto.OrderDiscountItem.DiscountId,
                Title = dto.OrderDiscountItem.Title
            });
        }

        //订单分享信息
        if (dto.TraceId is > 0)
        {
            await _dbContext.OrderShareInfos.AddAsync(new OrderShareInfo
            {
                BaseOrderId = baseOrder.Id,
                OrderType = baseOrder.OrderType,
                BuyerId = baseOrder.UserId,
                TraceId = dto.TraceId,
                CreateTime = baseOrder.CreateTime
            });

            //上送跟踪日志
            await _capPublisher.PublishAsync(CapTopics.Marketing.PushPromotionTraceRecord,
                new PushPromotionTraceRecordMessage
                {
                    PromotionTraceId = dto.TraceId.Value,
                    CustomerId = baseOrder.UserId,
                    BehaviorType = TraceBehaviorType.CreateOrder,
                    OrderType = baseOrder.OrderType,
                    OrderId = baseOrder.Id,
                    VisitTargetName = baseOrder.ProductName
                });
        }
        OrderLogs orderLog = new()
        {
            OperationRole = operationUser.UserType,
            UserId = operationUser.UserId,
            UserName = operationUser.Name,
            OperationType = OrderOperationType.Created,
            OrderLogType = OrderLogType.TravelLine,
            OrderId = baseOrder.Id,
        };

        #region 订单备注

        if (!string.IsNullOrEmpty(dto.Remark))
        {
            var orderRemark = new BaseOrderRemark
            {
                BaseOrderId = baseOrder.Id,
                Remark = dto.Remark,
                CreatorId = operationUser.UserId,
                CreatorName = operationUser.Name
            };

            await _dbContext.AddAsync(orderRemark);
        }

        if (string.IsNullOrEmpty(dto.SupplierOrderRemark) is false)
        {
            var supplierOrderRemark = new BaseOrderRemark
            {
                BaseOrderId = baseOrder.Id,
                Remark = dto.SupplierOrderRemark,
                CreatorId = operationUser.UserId,
                CreatorName = operationUser.Name,
                RemarkType = BaseOrderRemarkType.SupplierOrderRemark
            };

            await _dbContext.AddAsync(supplierOrderRemark);
        }

        #endregion

        //ota渠道
        if (otaSellingPlatform.Contains(baseOrder.SellingPlatform))
        {
            var otaOrder = new TravelLineOtaOrder()
            {
                BaseOrderId = baseOrder.Id,
                SubOrderId = travelLineOrder.Id,
                ChannelOrderNo = baseOrder.ChannelOrderNo,
                ChannelMasterOrderNo = dto.ChannelMasterOrderNo,
                Status = TravelLineOtaOrderStatus.WaitingForConfirm
            };
            await _dbContext.AddAsync(otaOrder);
        }

        await _dbContext.AddAsync(baseOrder);
        await _dbContext.AddAsync(travelLineOrder);
        await _dbContext.AddRangeAsync(orderPrices);
        await _dbContext.AddAsync(orderLog);
        await _dbContext.OrderFieldInformationType.AddRangeAsync(orderFieldTypes);
        await _dbContext.AddRangeAsync(fields);
        await _dbContext.AddRangeAsync(skuTypeItems);

        //非API对接扣除库存
        if (dto.LineProduct.PurchaseSourceType == LineProductPurchaseSourceType.OfflinePurchase && dto.LineProduct.IsCompensation == false)
        {
            //冻结日历库存
            var frozenCalendarInventoryCommand = new FrozenCalendarInventoryMessage
            {
                TenantId = dto.TenantId,
                OrderId = travelLineOrder.Id,
                ProductId = travelLineOrder.LineProductId,
                ItemId = travelLineOrder.LineProductSkuId,
                BeginDate = travelLineOrder.TravelBeginDate,
                EndDate = travelLineOrder.TravelBeginDate,
                FrozenQuantity = orderPrices
                    .Where(x => x.OrderSubType != (int)LineSkuPriceType.RoomPriceDifference)
                    .Sum(x => x.Quantity)
            };
            await _capPublisher.PublishAsync(CapTopics.Inventory.FrozenCalendarInventory, frozenCalendarInventoryCommand);
        }

        //使用优惠券                
        if (dto.OrderDiscountItem?.DiscountId > 0)
        {
            var UserCouponUsedCommand = new UserCouponUsedMessage
            {
                TenantId = dto.TenantId,
                UserCouponId = dto.OrderDiscountItem.DiscountId,
                BaseOrderId = baseOrder.Id,
                OrderType = OrderType.TravelLineOrder
            };
            await _capPublisher.PublishAsync(CapTopics.Marketing.UserCouponUsed, UserCouponUsedCommand);
        }
        await _dbContext.SaveChangesAsync();

        //提交事务前 促销活动扣活动库存并记录订单信息
        if (dto.FlashSale?.FlashSaleId > 0)
        {
            await _mediator.Send(new FlashSaleOrderRecordRequest
            {
                FlashSaleId = dto.FlashSale.FlashSaleId,
                UserId = baseOrder.UserId,
                RecordItems = dto.FlashSale.FlashSaleItems.Select(x => new FlashSaleRecordItem
                {
                    FlashSaleItemId = x.FlashSaleItemId,
                    OrderId = travelLineOrder.Id,
                    Quantity = x.Quantity
                }).ToList()
            });
        }

        //更新渠道同步失败订单状态
        if (dto.SyncFailOrderId.HasValue)
        {
            await _capPublisher.PublishAsync(CapTopics.Order.OpenChannelSyncFailOrderStatusUpdate,
                new OpenChannelSyncFailOrderStatusUpdateMessage
                {
                    SyncFailOrderId = dto.SyncFailOrderId.Value,
                    TenantId = dto.TenantId,
                    OrderType = OrderType.TravelLineOrder,
                    RecordSource = OpenChannelSyncFailOrderRecordSource.ManualCreate,
                    OperatorId = dto.OperationUser.UserId,
                    Operator = dto.OperationUser.Name
                });
        }

        await _capPublisher.PublishAsync(CapTopics.Product.BytePlusItemAttributesUpdate, new BytePlusItemAttributesUpdateMessage
        {
            TenantId = dto.TenantId,
            OrderItems = new List<BytePlusOrderItemDto>() {
                new ()
                {
                    Id=$"{baseOrder.Id}_{travelLineOrder.LineProductSkuId}",
                    BeginDate=travelLineOrder.TravelBeginDate.ToString("yyyy-MM-dd"),
                    EndDate=travelLineOrder.TravelEndDate.ToString("yyyy-MM-dd"),
                    ItemCategory= BytePlusItemCategory.TravelLine,
                    ItemSubCategory= BytePlusItemSubCategory.TravelLine,
                    ItemId=travelLineOrder.LineProductId ,
                    ItemName=dto.LineProduct.Title,
                    ItemVariant=$"{dto.LineProductSku.LineProductSkuName}",
                    Quantity = orderPrices.Sum(x=>x.Quantity),
                    Price=Math.Round(baseOrder.TotalAmount/orderPrices.Sum(x=>x.Quantity),2),
                }
            }
        });

        //非OTA来源渠道需要自动关闭处理
        if (!otaSellingPlatform.Contains(baseOrder.SellingPlatform))
        {
            //超时自动关闭
            _backgroundJobClient?.Schedule<HangfireClient.Jobs.IOrderJob>(s => s.CloseTimeoutOrder(baseOrder.Id, baseOrder.TenantId),
                TimeSpan.FromMinutes(30));
        }

        //保险产品与订单触发设置
        var createInsureOrderRelationMessage = new CreateInsureOrderRelationMessage
        {
            TenantId = dto.TenantId,
            OrderId = travelLineOrder.Id,
            BaseOrderId = travelLineOrder.BaseOrderId,
            ProductId = travelLineOrder.LineProductId,
            ProductSkuId = travelLineOrder.LineProductSkuId,
        };
        await _capPublisher.PublishAsync(CapTopics.Order.CreateInsureOrderRelation, createInsureOrderRelationMessage);

        result.BaseOrderId = baseOrder.Id;
        result.LineProductOrderId = travelLineOrder.Id;
        return result;
    }

    public async Task SendConfirmSms(SendConfirmSmsInput input)
    {
        var travelLineOrder = await _dbContext.TravelLineOrder.IgnoreQueryFilters()
            .AsNoTracking()
            .Where(x => x.BaseOrderId == input.BaseOrderId)
            .WhereIF(input.SupplierId.HasValue, x => x.SupplierId == input.SupplierId!)
            .FirstOrDefaultAsync();
        if (travelLineOrder.Status != TravelLineOrderStatus.Confirmed)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        #region 判断是否开启短信通知

        var notifyRequest = new GetCustomerNotifySwitchesInput
        {
            NotifyChannel = NotifyChannel.WechatMall
        };
        var httpContent =
            new StringContent(JsonConvert.SerializeObject(notifyRequest), Encoding.UTF8, "application/json");
        var notifyResult = await _httpClientFactory.InternalPostAsync<List<GetCustomerNotifySwitchesOutput>>(
            requestUri: _servicesAddress.Value.CustomerNotify_GetSwitch(),
            httpContent: httpContent);
        if (notifyResult is null) throw new BusinessException(ErrorTypes.Notify.NotifyNotOpen);

        var travelLineNotify = notifyResult
            .Where(x => x.NotifyEventType == NotifyEventType.TravelLine)
            .SelectMany(x => x.Items)
            .FirstOrDefault(x => x.NotifyEventSubType == NotifyEventSubType.TravelLine_Confirmed);
        if (travelLineNotify is null) throw new BusinessException(ErrorTypes.Notify.NotifyNotOpen);
        if (travelLineNotify.SmsNotifyIsOpen is false) throw new BusinessException(ErrorTypes.Notify.NotifyNotOpen);

        #endregion

        //发送确认短信
        await _travelLineOrderMessageService.SendConfirmedMessageAsync(new TravelLineOrderSendConfirmedMessageInput
        {
            BaseOrderId = input.BaseOrderId,
            SupplierId = input.SupplierId
        });
    }

    public async Task UpdateTourGuide(UpdateTourGuideInput input)
    {
        var travelLineOrder = await _dbContext.TravelLineOrder
            .Where(x => x.BaseOrderId == input.BaseOrderId)
            .FirstOrDefaultAsync();

        if (travelLineOrder == null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        // 修改订单确认
        var commfields = input.OrderFields.FirstOrDefault(x => x.TemplateType == TemplateType.Order
                             && x.ProductTemplateType == ProductTemplateType.OrderCommon)?.Fields;
        if (commfields != null && commfields.Count > 0)
        {
            var commonFields = _mapper.Map<List<OrderFieldInformationDto>>(commfields);
            var commonInfo = _orderFieldInformationService.ChangeOldTravelCommon(commonFields);
            if (commonInfo.TourGuideCarNumber != null)
                travelLineOrder.TourGuideCarNumber = commonInfo.TourGuideCarNumber;
            if (commonInfo.TourGuideName != null)
                travelLineOrder.TourGuideName = commonInfo.TourGuideName;
            if (commonInfo.TourGuidePhoneNumber != null)
                travelLineOrder.TourGuidePhoneNumber = commonInfo.TourGuidePhoneNumber;

            var orderFieldIds = commfields.Select(x => x.Id);
            var orderFields = await _dbContext.OrderFieldInformations.Where(x => orderFieldIds.Contains(x.Id))
                             .ToListAsync();
            foreach (var item in orderFields)
            {
                var field = commfields.FirstOrDefault(x => x.Id == item.Id);
                if (field != null)
                {
                    item.FieldValue = field.FieldValue;
                }
            }
        }
        travelLineOrder.UpdateTime = DateTime.Now;

        OperationUserDto operationUser = input.OperationUser;

        OrderLogs orderLog = new()
        {
            OperationRole = operationUser.UserType,
            UserId = operationUser.UserId,
            UserName = operationUser.Name,
            OperationType = OrderOperationType.UpdateTourGuide,
            OrderLogType = OrderLogType.TravelLine,
            OrderId = travelLineOrder.BaseOrderId,
        };
        await _dbContext.AddAsync(orderLog);
        await _dbContext.SaveChangesAsync();
    }

    public async Task<List<long>> AutoTimeReminder()
    {
        List<long> notifyTravelLineOrderIds = new();

        var threeDaysAfterTheDate = DateTime.Now.AddDays(3).Date;
        var sevenDaysAfterTheDate = DateTime.Now.AddDays(7).Date;
        var travelLineOrderInfos = await _dbContext.TravelLineOrder.IgnoreQueryFilters()
                   .Join(_dbContext.BaseOrders, t => t.BaseOrderId, b => b.Id, (t, b) => new { t, b })
                   .Where(x => x.b.Status.Equals(BaseOrderStatus.UnFinished)
                   && (x.t.TravelBeginDate.Date.Equals(threeDaysAfterTheDate)
                   || x.t.TravelBeginDate.Date.Equals(sevenDaysAfterTheDate)))
                   .Select(x => new
                   {
                       ProductName = x.b.ProductName,
                       SkuName = x.b.ProductSkuName,
                       x.t.TravelBeginDate,
                       x.t.TravelEndDate,
                       x.b.ResourceName,
                       BaseOrder = new BaseOrderNotify
                       {
                           UserId = x.b.UserId,
                           Id = x.b.Id,
                           SellingPlatform = x.b.SellingPlatform,
                           TenantId = x.b.TenantId
                       },
                       TravelLineOrderId = x.t.Id
                   })
               .ToListAsync();

        if (travelLineOrderInfos is null || !travelLineOrderInfos.Any()) return notifyTravelLineOrderIds;

        //提交前判断队列是否已存在该条记录
        string _travelLineTimeReminderKey = $"openapi:order:travelline_time_reminder_{DateTime.Now.Date:yyyy-MM-dd}";
        var cacheTravelLineOrderIds = await _redisClient
            .ListRangeAsync<long>(_travelLineTimeReminderKey);
        travelLineOrderInfos = travelLineOrderInfos
            .Where(x => !cacheTravelLineOrderIds.Contains(x.TravelLineOrderId))
            .ToList();
        if (!travelLineOrderInfos.Any()) return notifyTravelLineOrderIds;

        //查询成人和儿童数量
        var subOrderIds = travelLineOrderInfos.Select(x => x.TravelLineOrderId).ToList();
        var quantity = await _dbContext.OrderPrices.IgnoreQueryFilters().Where(x => subOrderIds.Contains(x.SubOrderId)
        && (x.OrderSubType.Equals((int)LineSkuPriceType.Adult) || x.OrderSubType.Equals((int)LineSkuPriceType.Child)))
        .Select(x => new { x.OrderSubType, x.Quantity, x.SubOrderId }).ToListAsync();

        //消息通知
        foreach (var item in travelLineOrderInfos)
        {
            var adultQuantity = quantity.Where(x => x.SubOrderId.Equals(item.TravelLineOrderId) && x.OrderSubType.Equals((int)LineSkuPriceType.Adult))
                .Select(x => x.Quantity)
                .FirstOrDefault();

            var childQuantity = quantity.Where(x => x.SubOrderId.Equals(item.TravelLineOrderId) && x.OrderSubType.Equals((int)LineSkuPriceType.Child))
                .Select(x => x.Quantity)
                .FirstOrDefault();
            var orderNotifyDto = new OrderNotifyDto<TravelLineTimeReminderNotifyDto>()
            {
                BaseOrder = item.BaseOrder,
                NotifyDto = new TravelLineTimeReminderNotifyDto
                {
                    ProductName = item.ProductName,
                    SkuName = item.SkuName,
                    TravelBeginDate = item.TravelBeginDate,
                    TravelEndDate = item.TravelEndDate,
                    AdultQuantity = adultQuantity,
                    ChildQuantity = childQuantity
                }
            };
            await _messageNotifyService.TravelLineTimeReminderNotify(orderNotifyDto);
            notifyTravelLineOrderIds.Add(item.TravelLineOrderId);
            await _redisClient.ListLeftPushAsync(_travelLineTimeReminderKey, item.TravelLineOrderId);
        }
        await _redisClient.KeyExpireAsync(_travelLineTimeReminderKey, TimeSpan.FromDays(1));
        return notifyTravelLineOrderIds;
    }

    /// <summary>
    /// 查询OTA携程发货结果
    /// </summary>
    /// <param name="baseOrderIds"></param>
    /// <returns></returns>
    public async Task<Dictionary<long, (TravelLineOrderDeliveryStatus deliveryStatus, string msg, int? errorCode)>>
        GetOfflinePurchaseChannelDeliveryResult(params long[] baseOrderIds)
    {
        var result = new Dictionary<long, (TravelLineOrderDeliveryStatus deliveryStatus, string msg, int? errorCode)>();
        var supplierOrderRecords = await _dbContext.TravelLineSupplierOrderRecords.AsNoTracking()
            .Where(x => baseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();

        if (supplierOrderRecords.Any() is false) return result;

        foreach (var baseOrderId in baseOrderIds)
        {
            var msg = string.Empty;
            int? errorCode = null;
            var deliveryStatus = new TravelLineOrderDeliveryStatus();
            var record = supplierOrderRecords
                .Where(x => x.BaseOrderId == baseOrderId)
                .MaxBy(x => x.Id);

            if (record == null) continue;

            if (record.IsSuccess)
            {
                deliveryStatus = TravelLineOrderDeliveryStatus.Success;
            }
            else
            {
                //2.发货失败
                switch (record.RecordType)
                {
                    //同步API对接的分销商时，同步失败显示
                    case LineProductSupplierOrderRecordType.SyncDelivery:

                        deliveryStatus = TravelLineOrderDeliveryStatus.SyncFailed;
                        errorCode = (int)OrderBusinessErrorCodeType.SyncFailed;

                        break;
                    default:

                        //支付失败.订单退款,开放平台同步凭证失败,Saas处理图片失败===>发货失败,
                        deliveryStatus = TravelLineOrderDeliveryStatus.DeliveryFail;
                        msg = record.ErrorMsg;
                        errorCode = record.ErrorCode;

                        break;
                }
            }

            result.Add(baseOrderId, (deliveryStatus, msg, errorCode));
        }

        return result;
    }

    record SupplierDeliveryRecords(
        long RecordId,
        bool IsSuccess, LineProductSupplierOrderRecordType RecordType, string? ErrorMsg,
        int? ErrorCode, long BaseOrderId, LineProductSupplierOrderStatus OrderStatus);
    /// <summary>
    /// 查询OTA供应商发货结果
    /// </summary>
    /// <param name="baseOrderIds"></param>
    /// <returns></returns>
    public async Task<Dictionary<long, (LineProductSupplierOrderStatus supplierOrderStatus, TravelLineOrderDeliveryStatus deliveryStatus, string msg, int? errorCode)>>
        GetSupplierDeliveryResult(params long[] baseOrderIds)
    {
        var result = new Dictionary<long, (LineProductSupplierOrderStatus supplierOrderStatus, TravelLineOrderDeliveryStatus deliveryStatus, string msg, int? errorCode)>();
        if (baseOrderIds.Any() is false)
            return result;

        var deliveryRecords = new List<SupplierDeliveryRecords>();

        //供货方订单
        var supplierOrders = await _dbContext.TravelLineSupplierOrders.AsNoTracking()
            .Where(x => baseOrderIds.Contains(x.BaseOrderId))
            .Select(x => new { x.BaseOrderId, x.OrderStatus })
            .ToListAsync();
        if (supplierOrders.Any())
        {
            var supplierOrderRecords = await _dbContext.TravelLineSupplierOrderRecords.AsNoTracking()
                .Where(x => baseOrderIds.Contains(x.BaseOrderId))
                .Select(x => new
                {
                    x.Id,
                    x.BaseOrderId,
                    x.IsSuccess,
                    x.RecordType,
                    x.ErrorMsg,
                    x.ErrorCode,
                })
                .ToListAsync();

            var supplierDeliveryRecords = (from record in supplierOrderRecords
                                           let supplierOrder = supplierOrders.FirstOrDefault(x => x.BaseOrderId == record.BaseOrderId)
                                           select new SupplierDeliveryRecords(
                                               record.Id,
                                               record.IsSuccess,
                                               record.RecordType,
                                               record.ErrorMsg,
                                               record.ErrorCode,
                                               record.BaseOrderId,
                                               supplierOrder.OrderStatus))
                .ToList();

            deliveryRecords.AddRange(supplierDeliveryRecords);
        }

        if (deliveryRecords.Any() is false)
            return result;

        foreach (var baseOrderId in baseOrderIds)
        {
            var msg = string.Empty;
            int? errorCode = null;
            var deliveryStatus = new TravelLineOrderDeliveryStatus();
            var record = deliveryRecords
                .Where(x => x.BaseOrderId == baseOrderId)
                .MaxBy(x => x.RecordId);

            if (record == null) continue;

            switch (record.OrderStatus)
            {
                case LineProductSupplierOrderStatus.WaitingForDeliver:

                    //1.发货中(支付成功后)
                    deliveryStatus = TravelLineOrderDeliveryStatus.WaitingForDeliver;
                    errorCode = (int)OrderBusinessErrorCodeType.WaitingForDeliver;

                    break;
                case LineProductSupplierOrderStatus.CreateFail:
                case LineProductSupplierOrderStatus.WaitingForPay:
                case LineProductSupplierOrderStatus.Refunded:
                case LineProductSupplierOrderStatus.DeliveryFail:

                    //支付失败.订单退款,发货失败
                    deliveryStatus = TravelLineOrderDeliveryStatus.DeliveryFail;
                    msg = record.ErrorMsg;
                    errorCode = record.ErrorCode;

                    break;
                case LineProductSupplierOrderStatus.Delivered:

                    if (record.IsSuccess)
                    {
                        //发货成功
                        switch (record.RecordType)
                        {
                            case LineProductSupplierOrderRecordType.InCombination:
                                deliveryStatus = TravelLineOrderDeliveryStatus.WaitingForDeliver;
                                errorCode = (int)OrderBusinessErrorCodeType.InCombination;
                                break;
                            default:
                                deliveryStatus = TravelLineOrderDeliveryStatus.Success;
                                break;
                        }
                    }
                    else
                    {
                        //2.发货失败
                        switch (record.RecordType)
                        {
                            //同步API对接的分销商时，同步失败显示
                            case LineProductSupplierOrderRecordType.SyncDelivery:

                                deliveryStatus = TravelLineOrderDeliveryStatus.SyncFailed;
                                errorCode = (int)OrderBusinessErrorCodeType.SyncFailed;

                                break;
                            default:

                                //支付失败.订单退款,开放平台同步凭证失败,Saas处理图片失败===>发货失败,
                                deliveryStatus = TravelLineOrderDeliveryStatus.DeliveryFail;
                                msg = record.ErrorMsg;
                                errorCode = record.ErrorCode;

                                break;
                        }
                    }

                    break;
                case LineProductSupplierOrderStatus.Reject:

                    //供应端拒单
                    deliveryStatus = TravelLineOrderDeliveryStatus.Rejected;
                    errorCode = (int)OrderBusinessErrorCodeType.Rejected;

                    break;
                case LineProductSupplierOrderStatus.Purchasing:

                    //等待供应端采购
                    deliveryStatus = TravelLineOrderDeliveryStatus.Purchasing;
                    errorCode = (int)OrderBusinessErrorCodeType.TicketPurchasing;

                    break;
                case LineProductSupplierOrderStatus.PayProcessing:

                    //供应端支付处理中
                    deliveryStatus = TravelLineOrderDeliveryStatus.PayProcessing;
                    errorCode = (int)OrderBusinessErrorCodeType.PayProcessing;

                    break;
            }

            result.Add(record.BaseOrderId, (record.OrderStatus, deliveryStatus, msg, errorCode));
        }

        return result;
    }

    public async Task<bool> UpdateSupplierOrderId(UpdateSupplierOrderIdInput input)
    {
        var baseOrder = await _dbContext.BaseOrders
           .Where(x => x.Id == input.BaseOrderId)
           .FirstOrDefaultAsync();

        if (baseOrder.Status == BaseOrderStatus.WaitingForPay || baseOrder.Status == BaseOrderStatus.Closed)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var travelLineOrder =
          await _dbContext.TravelLineOrder.FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);

        if (travelLineOrder == null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        //不是线下采购产品的订单不可以修改
        if (travelLineOrder.PurchaseSourceType != LineProductPurchaseSourceType.OfflinePurchase)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        var settlementorderdetail = await _dbContext.SettlementOrderDetails
                                   .Join(_dbContext.SettlementOrders, detail => detail.SettlementOrderId, settl => settl.Id, (detail, settl) => new { detail, settl })
                                   .Where(x => x.detail.BaseOrderId == input.BaseOrderId)
                                   .CountAsync();
        // 采购单财务核销后不能改
        if (settlementorderdetail > 0)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        if (travelLineOrder.SupplierId > 0)
        {
            var supplierInfo = await _httpClientFactory.InternalGetAsync<GetSupplierOutput>(
                             requestUri: _servicesAddress.Value.Tenant_GetSupplier(travelLineOrder.SupplierId));
            if (supplierInfo.SupplierType == SupplierType.Api)
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        travelLineOrder.SupplierOrderId = input.SupplierOrderId;


        var orderLog = new OrderLogs
        {
            OrderId = input.BaseOrderId,
            OperationRole = input.OpUser.UserType,
            OperationType = OrderOperationType.SupplierOrderIdUpdated,
            OrderLogType = OrderLogType.TravelLine,
            UserId = input.OpUser.UserId,
            UserName = input.OpUser.Name,
        };
        await _dbContext.AddAsync(orderLog);
        await _dbContext.SaveChangesAsync();

        return true;
    }

    [UnitOfWork]
    public async Task UpdateChannelOrderNo(UpdateChannelOrderNoInput input)
    {
        var baseOrder = await _dbContext.BaseOrders
            .Where(x => x.Id == input.BaseOrderId)
            .FirstOrDefaultAsync();

        var travelLineOrder =
            await _dbContext.TravelLineOrder.FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);

        if (travelLineOrder == null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        if (baseOrder.Status is not (BaseOrderStatus.WaitingForPay or BaseOrderStatus.UnFinished))
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        if (travelLineOrder.Status != TravelLineOrderStatus.WaitingForPay
            && travelLineOrder.Status != TravelLineOrderStatus.WaitingForClaim
            && travelLineOrder.Status != TravelLineOrderStatus.WaitingForConfirm)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        //渠道订单号不支持修改
        if (_travelLineOtaService.OtaSellingPlatforms.Contains(baseOrder.SellingPlatform))
        {
            var channelOrderNos = await EditChannelOrderNoCheck(baseOrder.Id);
            foreach (var item in channelOrderNos)
            {
                if (!input.ChannelOrderNo.Contains(item))
                {
                    throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
                }
            }
        }

        baseOrder.ChannelOrderNo = string.Join(",", input.ChannelOrderNo);
        var orderLog = new OrderLogs
        {
            OrderId = input.BaseOrderId,
            OperationRole = input.OpUser.UserType,
            OperationType = OrderOperationType.ChannelOrderNoUpdated,
            OrderLogType = OrderLogType.TravelLine,
            UserId = input.OpUser.UserId,
            UserName = input.OpUser.Name,
        };
        await _dbContext.AddAsync(orderLog);
    }

    /// <summary>
    /// 修改采购价
    /// </summary>
    /// <param name="input"></param>
    /// <param name="operationUser"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    public async Task EditCost(UpdateCostInput input)
    {
        var baseOrder = await _dbContext.BaseOrders
         .Where(a => a.Id == input.BaseOrderId)
         .FirstOrDefaultAsync();
        var order = await _dbContext.TravelLineOrder
            .Where(a => a.BaseOrderId == input.BaseOrderId)
            .FirstOrDefaultAsync();
        var price = await _dbContext.OrderPrices
            .FirstOrDefaultAsync(a => a.BaseOrderId == input.BaseOrderId && a.OrderSubType == (int)input.OrderSubType!.Value);
        if (order is null || price is null) throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        var statusArr = new List<TravelLineOrderStatus>() {
         TravelLineOrderStatus.WaitingForClaim,
          TravelLineOrderStatus.WaitingForPay,
           TravelLineOrderStatus.WaitingForConfirm,
            TravelLineOrderStatus.Confirmed,
        };

        if (!statusArr.Contains(order.Status))
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var settlementOrderDetail = await _dbContext.SettlementOrderDetails
           .Where(s => s.BaseOrderId == input.BaseOrderId)
           .FirstOrDefaultAsync();
        if (settlementOrderDetail != null)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        price.CostPrice = input.Cost;
        //计算总折扣金额
        var orderPrices = await _dbContext.OrderPrices
            .Where(a => a.BaseOrderId == input.BaseOrderId)
            .ToListAsync();
        baseOrder.CostDiscountAmount =
            orderPrices.Sum(x => (Math.Round(x.CostPrice * x.Quantity * x.CostDiscountRate / 100, 2)));

        OperationUserDto operationUser = input.OperationUser;
        var logs = new OrderLogs
        {
            OperationRole = operationUser.UserType,
            OperationType = OrderOperationType.CostPriceUpdated,
            OrderLogType = OrderLogType.TravelLine,
            OrderId = order.BaseOrderId,
            UserId = operationUser.UserId,
            UserName = operationUser.Name
        };
        await _dbContext.AddAsync(logs);
        await _dbContext.SaveChangesAsync();
    }


    /// <summary>
    /// 修改联系人信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> UpdateContact(UpdateContactInput input)
    {
        var travelLineOrder = await _dbContext.TravelLineOrder
          .Where(x => x.BaseOrderId == input.BaseOrderId)
          .FirstOrDefaultAsync();

        if (travelLineOrder == null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        var baseOrder = await _dbContext.BaseOrders
            .Where(x => x.Id == travelLineOrder.BaseOrderId)
            .FirstOrDefaultAsync();

        if (baseOrder.Status == BaseOrderStatus.Finished || baseOrder.Status == BaseOrderStatus.Closed)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        OrderOperationType orderOperationType;

        var fields = input.OrderFields.FirstOrDefault(x => x.TemplateType == TemplateType.Contacts).Fields;
        var contactFields = _mapper.Map<List<OrderFieldInformationDto>>(fields);
        var contactOrderField = _orderFieldInformationService.ChangeContact(contactFields);

        if (!string.IsNullOrEmpty(contactOrderField.ContactsName))
            baseOrder.ContactsName = contactOrderField.ContactsName;
        if (!string.IsNullOrEmpty(contactOrderField.ContactsPhoneNumber))
            baseOrder.ContactsPhoneNumber = contactOrderField.ContactsPhoneNumber;
        if (!string.IsNullOrEmpty(contactOrderField.ContactsEmail))
            baseOrder.ContactsEmail = contactOrderField.ContactsEmail;

        var contactFieldIds = fields.Select(x => x.Id);
        var orderFields = await _dbContext.OrderFieldInformationType
               .Join(_dbContext.OrderFieldInformations, type => type.Id, field => field.OrderFieldInformationTypeId, (type, field) => new { field, type })
               .Where(x => contactFieldIds.Contains(x.field.Id))
               .Where(x => x.type.BaseOrderId == baseOrder.Id)
               .Select(x => x.field)
               .ToListAsync();

        foreach (var item in orderFields)
        {
            var field = fields.FirstOrDefault(x => x.Id == item.Id);
            if (field != null)
            {
                item.FieldValue = field.FieldValue;
            }
        }
        travelLineOrder.UpdateTime = DateTime.Now;
        OperationUserDto operationUser = input.OperationUser;
        var logs = new OrderLogs
        {
            OperationRole = operationUser.UserType,
            OperationType = OrderOperationType.UpdateContact,
            OrderLogType = OrderLogType.TravelLine,
            OrderId = baseOrder.Id,
            UserId = operationUser.UserId,
            UserName = operationUser.Name
        };
        await _dbContext.AddAsync(logs);

        await CompensationOrderDataUpdateCap(
            orderCategory: baseOrder.OrderCategory,
            baseOrderId: baseOrder.Id,
            tenantId: baseOrder.TenantId,
            orderType: baseOrder.OrderType);

        var count = await _dbContext.SaveChangesAsync();

        return count > 0 ? true : false;
    }

    public async Task<bool> UpdateTravelInfo(UpdateTravelInfoInput input)
    {
        var travelLineOrder = await _dbContext.TravelLineOrder.Where(x => x.BaseOrderId == input.BaseOrderId)
            .FirstOrDefaultAsync();

        if (travelLineOrder == null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        var baseOrder = await _dbContext.BaseOrders.Where(x => x.Id == travelLineOrder.BaseOrderId)
         .FirstOrDefaultAsync();

        if (baseOrder.Status == BaseOrderStatus.Finished || baseOrder.Status == BaseOrderStatus.Closed)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var travelOrderFields = input.OrderFields.Where(x => x.TemplateType == TemplateType.Travel).ToList();
        List<long> travelerFieldIds = new List<long>();
        var inputOrderFieldInformationTypeIds = travelOrderFields
            .Where(x => x.Id > 0)
            .Select(x => x.Id).ToList();
        travelOrderFields.ForEach(orderField =>
        {
            orderField.Fields.ForEach(x =>
            {
                travelerFieldIds.Add(x.Id);
            });
        });

        //查询订单的出行信息模板
        var orderFieldInformationTypes = await _dbContext.OrderFieldInformationType
            .Where(x => x.BaseOrderId == baseOrder.Id
                        && x.OrderId == travelLineOrder.Id)
            .Where(x => x.TemplateType == TemplateType.Travel)
            .ToListAsync();
        var orderFieldInformationTypeIds = orderFieldInformationTypes.Select(x => x.Id).ToList();
        var removeTypeIds = orderFieldInformationTypeIds.Except(inputOrderFieldInformationTypeIds).ToList();//移除的模板id
        var intersectTypeIds = inputOrderFieldInformationTypeIds.Intersect(orderFieldInformationTypeIds).ToList();//需要更新的模板id

        //移除出行人信息
        if (removeTypeIds.Any())
        {
            var removeOrderFieldTypes = orderFieldInformationTypes.Where(x => removeTypeIds.Contains(x.Id)).ToList();
            _dbContext.RemoveRange(removeOrderFieldTypes);

            //移除对应字段信息
            var removeOrderFields = await _dbContext.OrderFieldInformations
                .Where(x => removeTypeIds.Contains(x.OrderFieldInformationTypeId))
                .Where(x => travelerFieldIds.Contains(x.Id))
                .ToListAsync();
            _dbContext.RemoveRange(removeOrderFields);
        }

        //修改出行人信息
        var orderFields = await _dbContext.OrderFieldInformations
            .Where(x => intersectTypeIds.Contains(x.OrderFieldInformationTypeId))
            .Where(x => travelerFieldIds.Contains(x.Id))
            .ToListAsync();
        if (orderFields.Any())
        {
            var addFields = new List<OrderFieldInformation>();
            var updateOrderFields = travelOrderFields
                                            .Where(x => intersectTypeIds.Contains(x.Id));
            foreach (var inputItemType in updateOrderFields)
            {
                var orderType = orderFieldInformationTypes.FirstOrDefault(x => x.Id == inputItemType.Id);
                if (orderType != null)
                {
                    foreach (var inputField in inputItemType.Fields)
                    {
                        var orderField = orderFields.FirstOrDefault(x => x.Id == inputField.Id);
                        if (orderField != null)
                        {
                            orderField.FieldValue = inputField.FieldValue;
                        }
                        else // 模板新增字段了，那就得保存
                        {
                            var newOrderFieldInformation = new OrderFieldInformation()
                            {
                                OrderFieldInformationTypeId = orderType.Id,
                                ProductTemplateType = inputField.ProductTemplateType,
                                FieldName = inputField.FieldName,
                                FieldCode = inputField.FieldCode,
                                FieldType = inputField.FieldType,
                                FieldValue = inputField.FieldValue,
                                IsRequired = inputField.IsRequired,
                                Sort = inputField.Sort,
                                Group = inputField.Group,
                                GroupName = inputField.GroupName,
                                GroupSort = inputField.GroupSort,
                            };
                            addFields.Add(newOrderFieldInformation);
                        }
                    }
                }
            }
            await _dbContext.AddRangeAsync(addFields);
        }

        //新增的出行人信息保存
        var newTravelerFields = travelOrderFields
            .Where(x => x.Id == 0)
            .ToList();
        foreach (var item in newTravelerFields)
        {
            //字段模板类型,不存在新增
            var newOrderFieldInformationTypeItem = new OrderFieldInformationType
            {
                BaseOrderId = baseOrder.Id,
                OrderId = travelLineOrder.Id,
                OrderType = baseOrder.OrderType,
                TemplateId = item.TemplateId,
                TemplateType = item.TemplateType,
                ProductTemplateType = item.ProductTemplateType
            };
            await _dbContext.AddAsync(newOrderFieldInformationTypeItem);

            foreach (var fieldItem in item.Fields)
            {
                //保存对应字段信息
                var newOrderFieldInformation = new OrderFieldInformation()
                {
                    OrderFieldInformationTypeId = newOrderFieldInformationTypeItem.Id,
                    ProductTemplateType = fieldItem.ProductTemplateType,
                    FieldName = fieldItem.FieldName,
                    FieldCode = fieldItem.FieldCode,
                    FieldType = fieldItem.FieldType,
                    FieldValue = fieldItem.FieldValue,
                    IsRequired = fieldItem.IsRequired,
                    Sort = fieldItem.Sort,
                    Group = fieldItem.Group,
                    GroupName = fieldItem.GroupName,
                    GroupSort = fieldItem.GroupSort,
                };
                await _dbContext.AddAsync(newOrderFieldInformation);
            }
        }


        var commfields = input.OrderFields.FirstOrDefault(x => x.TemplateType == TemplateType.Travel
                               && x.ProductTemplateType == ProductTemplateType.OrderCommon)?.Fields;
        if (commfields != null && commfields.Count > 0)
        {
            var commonFields = _mapper.Map<List<OrderFieldInformationDto>>(commfields);
            var commonInfo = _orderFieldInformationService.ChangeOldTravelCommon(commonFields);
            if (commonInfo.TourGuideCarNumber != null)
                travelLineOrder.TourGuideCarNumber = commonInfo.TourGuideCarNumber;
            if (commonInfo.TourGuideName != null)
                travelLineOrder.TourGuideName = commonInfo.TourGuideName;
            if (commonInfo.TourGuidePhoneNumber != null)
                travelLineOrder.TourGuidePhoneNumber = commonInfo.TourGuidePhoneNumber;
        }

        if (input.LineProductRallyPointId.HasValue)
        {
            travelLineOrder.RallyPointTime = input.RallyPointTime;
            travelLineOrder.RallyPointAddress = input.RallyPointAddress;
            if (input.RallyPointLongitude.HasValue && input.RallyPointLatitude.HasValue)
                travelLineOrder.SetLocation(input.RallyPointLongitude.Value, input.RallyPointLatitude.Value);
        }

        OperationUserDto operationUser = input.OperationUser;
        var logs = new OrderLogs
        {
            OperationRole = operationUser.UserType,
            OperationType = OrderOperationType.UpdateTravelInfo,
            OrderLogType = OrderLogType.TravelLine,
            OrderId = travelLineOrder.BaseOrderId,
            UserId = operationUser.UserId,
            UserName = operationUser.Name
        };
        await _dbContext.AddAsync(logs);

        travelLineOrder.UpdateTime = DateTime.Now;

        await CompensationOrderDataUpdateCap(
            orderCategory: baseOrder.OrderCategory,
            baseOrderId: baseOrder.Id,
            tenantId: baseOrder.TenantId,
            orderType: baseOrder.OrderType);

        var count = await _dbContext.SaveChangesAsync();

        return count > 0 ? true : false;
    }

    [UnitOfWork]
    public async Task UpdateCostDiscountRate(UpdateCostDiscountRateInput input)
    {
        var baseOrder = await _dbContext.BaseOrders
            .Where(a => a.Id == input.BaseOrderId)
            .FirstOrDefaultAsync();

        //只支持在[待完成]状态下修改
        if (baseOrder.Status != BaseOrderStatus.UnFinished)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var lineOrder = await _dbContext.TravelLineOrder
            .Where(x => x.BaseOrderId == input.BaseOrderId)
            .FirstOrDefaultAsync();
        //只支持[手工发货]产品
        if (lineOrder.PurchaseSourceType != LineProductPurchaseSourceType.OfflinePurchase)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var orderPrices = await _dbContext.OrderPrices.Where(x => x.BaseOrderId == input.BaseOrderId)
            .ToListAsync();
        orderPrices.ForEach(x => x.CostDiscountRate = input.CostDiscountRate);//更新折扣比例

        //计算总折扣总额
        baseOrder.CostDiscountAmount =
            Math.Round(orderPrices.Sum(x => x.CostPrice * x.Quantity * x.CostDiscountRate / 100), 2);

        //增加操作日志
        OperationUserDto operationUser = input.OperationUser;
        var logs = new OrderLogs
        {
            OperationRole = operationUser.UserType,
            OperationType = OrderOperationType.CostDiscountRateUpdated,
            OrderLogType = OrderLogType.TravelLine,
            OrderId = baseOrder.Id,
            UserId = operationUser.UserId,
            UserName = operationUser.Name
        };
        await _dbContext.AddAsync(logs);
    }

    [UnitOfWork]
    public async Task OtaSyncConfirmSubscribed(TravelLineOrderSyncConfirmMessage message)
    {
        var travelLineOrder = await _dbContext.TravelLineOrder.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == message.TravelLineOrderId);
        if (travelLineOrder != null)
        {
            await OtaSyncConfirm(new TravelLineOrderSyncConfirmInput
            {
                BaseOrderId = message.BaseOrderId,
                SellingPlatform = message.SellingPlatform,
                ChannelOrderNo = message.ChannelOrderNo,
                TravelLineOrderId = message.TravelLineOrderId,
                TenantId = message.TenantId,
                AgencyId = message.AgencyId,
                IsChannelTimeliness = travelLineOrder.IsChannelTimeliness,
                TimelinessChannelTypes = travelLineOrder.TimelinessChannelTypes,
                ProductId = travelLineOrder.LineProductId,
                TimelinessTriggerType = OpenChannelTimelinessTriggerType.SupplierCreatedOrder
            });
        }
    }

    /// <summary>
    /// OTA同步确认信息
    /// </summary>
    /// <param name="input"></param>
    public async Task OtaSyncConfirm(TravelLineOrderSyncConfirmInput input)
    {
        var otaSellingPlatform = _travelLineOtaService.OtaSellingPlatforms;
        var systemOtaChannelType = new OtaChannelType();
        if (input.SellingPlatform == SellingPlatform.System)
        {
            var systemOrderCheckAgency = await _openPlatformBaseService.CheckSystemPlatformOrderByAgencyId(
                agencyId: input.AgencyId,
                tenantId: input.TenantId,
                otaPlatform: otaSellingPlatform);
            otaSellingPlatform = systemOrderCheckAgency.otaPlatform;
            systemOtaChannelType = systemOrderCheckAgency.otaChannelType;
        }

        if (otaSellingPlatform.Contains(input.SellingPlatform))
        {
            //多渠道单号判断
            var channelOrderNos = new List<string>();
            if (!string.IsNullOrEmpty(input.ChannelOrderNo))
            {
                channelOrderNos = input.ChannelOrderNo.Split(',').Where(x => !string.IsNullOrEmpty(x)).ToList();
            }

            //同步信息
            var deliveryResult = await _travelLineOtaService.Confirm(new TravelLineOTAOrderConfirmInput
            {
                OtaOrderIds = channelOrderNos,
                SellingPlatform = input.SellingPlatform,
                SystemOtaChannelType = systemOtaChannelType,
                TenantId = input.TenantId
            });

            var isSuccess = deliveryResult.Code == 200;
            if (deliveryResult.Code == (int)TravelLineOtaOrderErrorCode.FliggyAlreadyConfirm)
            {
                //飞猪履约单已确认
                isSuccess = true;
            }

            var otaOrder = await _dbContext.TravelLineOtaOrders
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
            if (otaOrder is not null)
            {
                otaOrder.Status = isSuccess
                    ? TravelLineOtaOrderStatus.WaitingForDeliver //已确认直接变成待发货
                    : TravelLineOtaOrderStatus.WaitingForConfirm;
                otaOrder.UpdateTime = DateTime.Now;

                var otaOrderRecord = new TravelLineOtaOrderRecord
                {
                    BaseOrderId = input.BaseOrderId,
                    SubOrderId = input.TravelLineOrderId,
                    TravelLineOtaOrderId = otaOrder.Id,
                    RecordType = TravelLineOtaOrderRecordType.SyncConfirm,
                    IsSuccess = isSuccess,
                    ErrorMsg = deliveryResult.Msg,
                    ErrorCode = isSuccess ? null : (int)OrderBusinessErrorCodeType.LineSyncConfirmFailed
                };
                otaOrderRecord.SetTenantId(otaOrder.TenantId);
                await _dbContext.AddAsync(otaOrderRecord);
            }

            //时效订单.需要发送时效凭证到渠道(不影响正常流程)
            if (input is { IsChannelTimeliness: true, TimelinessChannelTypes: not null })
            {
                await _capPublisher.PublishAsync(CapTopics.Order.ChannelTimelinessVoucherSync,
                    new ChannelTimelinessVoucherSyncMessage
                    {
                        TenantId = input.TenantId,
                        BaseOrderId = input.BaseOrderId,
                        SubOrderId = input.TravelLineOrderId,
                        OrderType = OrderType.TravelLineOrder,
                        ChannelOrderNo = input.ChannelOrderNo,
                        SellingPlatform = input.SellingPlatform,
                        AgencyId = input.AgencyId,
                        TimelinessChannelTypes = input.TimelinessChannelTypes,
                        NeedConfirm = false,
                        ProductId = input.ProductId,
                        TimelinessTriggerType = input.TimelinessTriggerType
                    });
            }

            /*
             * Saas订单状态为已确认/完成，提示飞猪渠道插旗为红色
             */
            await _capPublisher.PublishAsync(CapTopics.Order.ChannelOrderFlagModify, new ChannelOrderFlagModifyMessage
            {
                ChannelOrderNo = input.ChannelOrderNo,
                SellingPlatform = input.SellingPlatform,
                FlagType = OpenChannelOrderFlagType.Red,
                AgencyId = input.AgencyId,
                TenantId = input.TenantId
            });
        }
    }

    public async Task<List<GetOrderCountOutput>> GetOrderCount(GetOrderCountInput input)
    {
        var beginDate = input.BeginDate ?? DateTime.Today.AddDays(-30);
        var endDate = input.EndDate ?? DateTime.Today;
        var baseOrderStatus = new[] { BaseOrderStatus.UnFinished, BaseOrderStatus.Finished };

        var orderCount = await _dbContext.BaseOrders.AsNoTracking()
            .Join(_dbContext.TravelLineOrder.AsNoTracking(), b => b.Id,
                h => h.BaseOrderId,
                (b, h) => new { b, h })
            .Where(x => x.b.OrderType == OrderType.TravelLineOrder
                        && baseOrderStatus.Contains(x.b.Status)
                        && x.b.CreateTime >= beginDate
                        && x.b.CreateTime <= endDate
                        && input.ProductIds.Contains(x.h.LineProductId))
            .Select(x => new
            {
                x.h.LineProductId,
            })
            .ToListAsync();

        var result = orderCount.GroupBy(x => x.LineProductId)
            .Select(x => new GetOrderCountOutput
            {
                ProductId = x.Key,
                OrderCount = x.Count()
            })
            .ToList();
        return result;
    }

    /// <summary>
    /// API下单预校验
    /// </summary>
    public async Task<PreCheckApiOrderOutput> PreCheckApiOrder(long baseOrderId,
        long tenantId,
        string supplierActivityId,
        string supplierPackageId,
        string supplierSkuId,
        OpenSupplierType supplierType)
    {
        var locationCheck = false;
        var contactsCheck = false;
        var errorType = ErrorTypes.Order.OpenSupplierOrderPreCheckError;

        try
        {
            var orderExtraInfos = await _dbContext.OpenSupplierOrderExtraInfos.IgnoreQueryFilters()
                .AsNoTracking()
                .Where(x => x.BaseOrderId == baseOrderId)
                .ToListAsync();
            var orderFieldTypes = await _dbContext.OrderFieldInformationType
                .IgnoreQueryFilters()
                .AsNoTracking()
                .Where(x => x.BaseOrderId == baseOrderId)
                .ToListAsync();
            var orderFieldTypesOut = _mapper.Map<List<OrderFieldInformationTypeOutput>>(orderFieldTypes);
            var orderFieldTypeIds = orderFieldTypes.Select(x => x.Id).ToList();
            var orderFields = await _dbContext.OrderFieldInformations
                .IgnoreQueryFilters()
                .AsNoTracking()
                .Where(x => orderFieldTypeIds.Contains(x.OrderFieldInformationTypeId))
                .ToListAsync();
            var orderFieldsOut = _mapper.Map<List<OrderFieldInformationOutput>>(orderFields);
            orderFieldTypesOut.ForEach(type =>
            {
                var fields = orderFieldsOut.Where(x => x.OrderFieldInformationTypeId == type.Id).OrderBy(x => x.Sort)
                    .ToList();
                type.Fields = fields;
            });

            var travelFieldInfo = orderFieldTypesOut.Where(x => x.TemplateType == TemplateType.Travel)
                .ToList();
            var contactsFieldInfo = orderFieldTypesOut.Where(x => x.TemplateType == TemplateType.Contacts)
                .ToList();

            if (orderExtraInfos.Any())
            {
                // 判断订单保存的附加信息
                var lineOrderSkuItems = await _dbContext.TravelLineOrderSkuTypeItems.IgnoreQueryFilters()
                    .AsNoTracking()
                    .Where(x => x.BaseOrderId == baseOrderId)
                    .ToListAsync();

                var extraRequest = new QueryOpenSupplierSkuExtraInfoInput
                {
                    OpenSupplierTypes = new List<OpenSupplierType> { supplierType },
                    ProductIds = lineOrderSkuItems.Select(x => x.ActivityId).Distinct().ToList(),
                    OptionIds = lineOrderSkuItems.Where(x => !string.IsNullOrEmpty(x.PackageId)).Select(x => x.PackageId!)
                            .Distinct().ToList(),
                    SkuIds = lineOrderSkuItems.Where(x => !string.IsNullOrEmpty(x.SkuId)).Select(x => x.SkuId!)
                        .Distinct().ToList()
                };
                var productContent = new StringContent(JsonConvert.SerializeObject(extraRequest),
                    Encoding.UTF8, "application/json");
                var extraInfo = await _httpClientFactory.InternalPostAsync<List<QueryOpenSupplierSkuExtraInfoOutput>>(
                    requestUri: _servicesAddress.Value.Product_QueryOpenSupplierSkuExtraInfo(),
                    httpContent: productContent);

                if (extraInfo.Any())
                {
                    var groupDataTypes = extraInfo.First().Sub.SelectMany(x => x.ExtraInfos)
                        .GroupBy(g => g.DataType)
                        .ToList();
                    var extraInfoCheck = true;
                    foreach (var dataType in groupDataTypes)
                    {
                        // 判断是否必填 
                        var isRequired = dataType.First().IsRequired;
                        if (!isRequired) continue; // 非必须不校验

                        var orderExtraInfoItem =
                            orderExtraInfos.FirstOrDefault(x => x.DataType == dataType.Key);
                        if (orderExtraInfoItem == null)
                        {
                            extraInfoCheck = false;
                            errorType = ErrorTypes.Order.OpenSupplierOrderExtraInfoError;
                        }
                        else
                        {
                            if (!string.IsNullOrEmpty(orderExtraInfoItem.OptionKey))
                            {
                                // 判断下拉选项key
                                var manyOptions = dataType.Where(x => x.ValueType == ApiSkuExtraInfoValueType.Select)
                                    .SelectMany(x => x.ValueOptions).ToList();
                                if (manyOptions.All(x => x.Key != orderExtraInfoItem.OptionKey))
                                {
                                    extraInfoCheck = false;
                                    errorType = ErrorTypes.Order.OpenSupplierOrderExtraInfoError;
                                }
                            }
                        }
                    }

                    locationCheck = extraInfoCheck;
                }
            }
            else
            {
                #region 接送地点校验

                /*
                 * 校验逻辑
                 * 1.供应端扩展数据大于一条才做判断
                 * 2.字段模板 上车点/送达点 + 详细地址 == 供应端扩展数据取值 才算匹配成功
                 * 2.1：供应端要求选则的，则只需要用下拉框里面的内容去匹配下单
                 * 2.2：供应端要求是输入的，则需要拼接详细地址传过去下单
                 */

                var supplierProduct = await _openSupplierService.SupplierProductDetail(
                    new SupplierProductDetailRequest
                    {
                        OutProductId = supplierActivityId,
                        SupplierType = supplierType.ToString().ToLowerInvariant()
                    }, tenantId);
                var lineSkuTypeItems = await _dbContext.TravelLineOrderSkuTypeItems.IgnoreQueryFilters().AsNoTracking()
                    .Where(x => x.BaseOrderId == baseOrderId)
                    .ToListAsync(); //套餐子项列表
                var filterSkuIds = lineSkuTypeItems.Select(x => x.SkuId!).Distinct().ToList();
                var filterOptionIds = lineSkuTypeItems.Select(x => x.PackageId!).Distinct().ToList();
                var supplierSkuList = supplierProduct.Data.SkuList
                    .Where(x => filterSkuIds.Contains(x.OutSkuId))
                    .Where(x => filterOptionIds.Contains(x.OutProductOptionId))
                    .ToList();
                if (supplierSkuList.Any())
                {
                    //供应商产品扩展信息
                    var pickUpLocationValue = new List<string>(); //供应端出发点信息数据
                    var deliveryLocationValue = new List<string>(); //供应端到达地点信息数据
                    var hasPickUpLocation = false; //有上车点取上车点,没有再取出发酒店
                    var pickUpLocationValueType = ApiSkuExtraInfoValueType.Text; //供应端出发点信息数据值类型
                    var deliveryLocationValueType = ApiSkuExtraInfoValueType.Text; //供应端到达地点信息数据值类型
                    foreach (var skuItem in supplierSkuList)
                    {
                        foreach (var extraInfoItem in skuItem.ExtraInfos)
                        {
                            var extraInfoKeyType =
                                _openPlatformBaseService
                                    .GetSaasEnumValue<ApiSkuExtraInfoDataType>(extraInfoItem.Key); //附加信息标识符类型
                            var extraInfoValueType =
                                _openPlatformBaseService.GetSaasEnumValue<ApiSkuExtraInfoValueType>(extraInfoItem
                                    .ValueType); //值类型
                            var optionsValueList = extraInfoItem.ValueOptions.Select(x => x.Value).ToList();
                            if (optionsValueList.Any())
                            {
                                switch (extraInfoKeyType)
                                {
                                    case ApiSkuExtraInfoDataType.PickUpLocation:
                                        pickUpLocationValue = optionsValueList;
                                        hasPickUpLocation = true;
                                        pickUpLocationValueType = extraInfoValueType;
                                        break;
                                    case ApiSkuExtraInfoDataType.DepartureHotel:
                                        if (!hasPickUpLocation)
                                        {
                                            pickUpLocationValue = optionsValueList;
                                            pickUpLocationValueType = extraInfoValueType;
                                        }

                                        break;
                                    case ApiSkuExtraInfoDataType.DeliveryLocation:
                                        deliveryLocationValue = optionsValueList;
                                        deliveryLocationValueType = extraInfoValueType;
                                        break;
                                }
                            }
                        }
                    }

                    //出行地点字段模板信息
                    var travelOrderFieldInfos = travelFieldInfo
                        .Where(x => x.ProductTemplateType == ProductTemplateType.OrderCommon)
                        .SelectMany(x => x.Fields)
                        .ToList();
                    //出发地字段模板信息
                    var departureCheck = true;
                    var departureJson = travelOrderFieldInfos.FirstOrDefault(x => x.FieldCode == "DepartureJson")
                        ?.FieldValue;
                    if (!string.IsNullOrEmpty(departureJson) || pickUpLocationValue.Any())
                    {
                        var departureValue = string.Empty;
                        if (!string.IsNullOrEmpty(departureJson))
                        {
                            var departureData = JsonConvert.DeserializeObject<AddressDto>(departureJson);
                            /*
                             * 1：供应端要求选则的，则只需要用下拉框里面的内容去匹配下单
                             * 2：供应端要求是输入的，则需要拼接详细地址传过去下单(一致去重)
                             */
                            var resourceZhName = departureData?.Detail?.ResourceZhName;
                            var address = departureData?.Address;

                            departureValue = pickUpLocationValueType switch
                            {
                                ApiSkuExtraInfoValueType.Text => string.Equals(resourceZhName, address)
                                    ? resourceZhName
                                    : $"{resourceZhName}{address}",
                                ApiSkuExtraInfoValueType.Select => resourceZhName,
                                _ => throw new ArgumentOutOfRangeException(nameof(pickUpLocationValueType),
                                    $"未处理的枚举值: {pickUpLocationValueType}")
                            };
                        }

                        if (pickUpLocationValue.Any())
                        {
                            if (string.IsNullOrEmpty(departureValue))
                            {
                                departureCheck = false;
                            }
                            else
                            {
                                if (pickUpLocationValue.Count > 1)
                                {
                                    departureCheck = pickUpLocationValue.Any(x => x == departureValue);
                                }
                            }
                        }
                    }

                    if (departureCheck is false)
                        errorType = ErrorTypes.Order.OpenSupplierOrderPreCheckDepartureError;

                    //目的地字段模板信息
                    var destinationCheck = true;
                    var destinationJson = travelOrderFieldInfos.FirstOrDefault(x => x.FieldCode == "DestinationJson")
                        ?.FieldValue;
                    if (!string.IsNullOrEmpty(destinationJson) || deliveryLocationValue.Any())
                    {
                        var destinationValue = string.Empty;
                        if (!string.IsNullOrEmpty(destinationJson))
                        {
                            var destinationData = JsonConvert.DeserializeObject<AddressDto>(destinationJson);
                            /*
                             * 1：供应端要求选则的，则只需要用下拉框里面的内容去匹配下单
                             * 2：供应端要求是输入的，则需要拼接详细地址传过去下单(一致去重)
                             */
                            var resourceZhName = destinationData?.Detail?.ResourceZhName;
                            var address = destinationData?.Address;
                            destinationValue = deliveryLocationValueType switch
                            {
                                ApiSkuExtraInfoValueType.Text => string.Equals(resourceZhName, address)
                                    ? resourceZhName
                                    : $"{resourceZhName}{address}",
                                ApiSkuExtraInfoValueType.Select => resourceZhName,
                                _ => throw new ArgumentOutOfRangeException()
                            };
                        }

                        if (deliveryLocationValue.Any())
                        {
                            if (string.IsNullOrEmpty(destinationValue))
                            {
                                destinationCheck = false;
                            }
                            else
                            {
                                if (deliveryLocationValue.Count > 1)
                                {
                                    destinationCheck = deliveryLocationValue.Any(x => x == destinationValue);
                                }
                            }
                        }
                    }

                    if (destinationCheck is false)
                        errorType = ErrorTypes.Order.OpenSupplierOrderPreCheckDestinationError;

                    locationCheck = departureCheck && destinationCheck;
                }

                #endregion
            }

            #region 联系人校验

            var contactsOrderFieldInfos = contactsFieldInfo
                .SelectMany(x => x.Fields)
                .ToList();
            var contactEmailFieldValue = contactsOrderFieldInfos
                .FirstOrDefault(x => x.FieldCode == "ContactEmail")?.FieldValue;
            contactsCheck = !string.IsNullOrEmpty(contactEmailFieldValue);

            if (contactsCheck is false)
                errorType = ErrorTypes.Order.OpenSupplierOrderPreCheckContactEmailError;

            #endregion
        }
        catch (Exception e)
        {
            // ignored
        }

        var result = new PreCheckApiOrderOutput
        {
            IsSuccess = contactsCheck && locationCheck,
            ErrorType = errorType
        };

        return result;
    }

    /// <summary>
    /// 修改出行时间
    /// </summary>
    /// <returns></returns>
    public async Task UpdateTravelTime(UpdateTravelTimeInput input)
    {
        var travelLineOrder = await _dbContext.TravelLineOrder.FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        if (travelLineOrder == null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);
        if (travelLineOrder.PurchaseSourceType != LineProductPurchaseSourceType.OfflinePurchase)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        var lastDate = travelLineOrder.TravelBeginDate.AddDays(-1);
        var lastTime = new DateTime(lastDate.Year, lastDate.Month, lastDate.Day, 23, 0, 0);
        if (DateTime.Now >= lastTime)
            throw new BusinessException(ErrorTypes.Order.ExceededUpdateTime);
        var insureOrder = await _dbContext.InsureOrderRelation.FirstOrDefaultAsync(x => x.BaseOrderId == travelLineOrder.BaseOrderId);
        if (insureOrder?.Status == InsureOrderStatus.Purchased)
            throw new BusinessException(ErrorTypes.Order.ExceededUpdateTime);

        var days = travelLineOrder.TravelEndDate.Subtract(travelLineOrder.TravelBeginDate).Days;
        travelLineOrder.TravelBeginDate = input.TravelDate;
        travelLineOrder.TravelEndDate = input.TravelDate.AddDays(days);
        travelLineOrder.UpdateTime = DateTime.Now;

        OperationUserDto operationUser = input.OperationUser;
        var logs = new OrderLogs
        {
            OperationRole = operationUser.UserType,
            OperationType = OrderOperationType.UpdateTravelTime,
            OrderLogType = OrderLogType.TravelLine,
            OrderId = travelLineOrder.BaseOrderId,
            UserId = operationUser.UserId,
            UserName = operationUser.Name
        };
        await _dbContext.AddAsync(logs);
        await _dbContext.SaveChangesAsync();
    }

    public async Task<QueryTravelLineOrderSkuTypeItemOutput> QuerySkuTypeItems(QueryTravelLineOrderSkuTypeItemInput input)
    {
        var output = new QueryTravelLineOrderSkuTypeItemOutput();
        var skuItems = await _dbContext.TravelLineOrderSkuTypeItems.AsNoTracking()
            .Where(x => input.BaseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();
        var travelLineOrders = await _dbContext.TravelLineOrder.Where(x => input.BaseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();
        foreach (var baseOrderId in input.BaseOrderIds)
        {
            var travelLineOrder = travelLineOrders.FirstOrDefault(x => x.BaseOrderId == baseOrderId);

            var baseOrderSkuItems = skuItems.Where(x => x.BaseOrderId == baseOrderId)
                .Select(x => new QueryTravelLineOrderSkuTypeItem
                {
                    BaseOrderId = x.BaseOrderId,
                    OpenSupplierType = travelLineOrder.OpenSupplierType,
                    TravelLineOrderId = x.TravelLineOrderId,
                    SkuTypeItemId = x.SkuTypeItemId,
                    SkuTypeItemName = x.SkuTypeItemName,
                    SkuPriceType = x.SkuPriceType,
                    ActivityId = x.ActivityId,
                    PackageId = x.PackageId,
                    SkuId = x.SkuId
                })
                .ToList();
            output.SkuTypeItems.AddRange(baseOrderSkuItems);
        }
        return output;
    }

    [UnitOfWork]
    public async Task ReplaceOrderProduct(ReplaceOrderProductInput input)
    {
        /*
         * 1.手工发货，Saas订单状态为【待确认】时（即未发货前）支持更换产品；
         * 2.接口对接的产品，采购状态为【待支付】【供应商拒单】【已退款】【创单失败】时支持更换产品
         * 3.年龄段类型限制.以产品传入的为准
         * 4.实收总额计算
         */

        var baseOrder = await _dbContext.BaseOrders.FirstOrDefaultAsync(x => x.Id == input.BaseOrderId);
        var lineOrder = await _dbContext.TravelLineOrder.FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        var supplierOrder = await _dbContext.TravelLineSupplierOrders.FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        var supplierRecord = await _dbContext.TravelLineSupplierOrderRecords.Where(x => x.BaseOrderId == input.BaseOrderId)
            .ToListAsync();
        var lineOrderSkuTypeItems = await _dbContext.TravelLineOrderSkuTypeItems
            .Where(x => x.BaseOrderId == input.BaseOrderId)
            .ToListAsync();
        var orderPrices = await _dbContext.OrderPrices.Where(x => x.BaseOrderId == input.BaseOrderId).ToListAsync();

        if (baseOrder == null || lineOrder == null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        if (baseOrder.Status != BaseOrderStatus.UnFinished)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        if (lineOrder.Status != TravelLineOrderStatus.WaitingForConfirm && lineOrder.Status != TravelLineOrderStatus.Confirmed) // [待确认]&[已确认]才支持
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        if (lineOrder.PurchaseSourceType == LineProductPurchaseSourceType.InterfaceDock)
        {
            var supplierOrderStatus = new[]
            {
                LineProductSupplierOrderStatus.WaitingForPay,
                LineProductSupplierOrderStatus.Reject,
                LineProductSupplierOrderStatus.CreateFail,
                LineProductSupplierOrderStatus.Refunded
            };

            if (supplierOrder != null)
            {
                if (!supplierOrderStatus.Contains(supplierOrder.OrderStatus)) //供应端订单状态不支持
                    throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
            }
        }

        //订单数据处理
        if (supplierOrder != null)
            _dbContext.Remove(supplierOrder);

        if (supplierRecord.Any())
            _dbContext.RemoveRange(supplierRecord);

        if (lineOrderSkuTypeItems.Any())
            _dbContext.RemoveRange(lineOrderSkuTypeItems);

        if (orderPrices.Any())
            _dbContext.RemoveRange(orderPrices);


        var lineProductName = input.LineProductDetail.Title;
        var lineProductSkuName = input.LineProductSkuDetail.Name;
        //记录变更日志扩展数据
        var extensionData = new OrderLogExtensionDataDto
        {
            OldProductId = lineOrder.LineProductId,
            OldProductName = baseOrder.ProductName,
            OldProductSkuId = lineOrder.LineProductSkuId,
            OldProductSkuName = baseOrder.ProductSkuName,

            NewProductId = input.LineProductId,
            NewProductName = lineProductName,
            NewProductSkuId = input.LineProductSkuId,
            NewProductSkuName = lineProductSkuName
        };

        //baseorder
        baseOrder.ProductName = lineProductName;
        baseOrder.ProductSkuName = lineProductSkuName;
        baseOrder.TotalAmount = input.SkuTypeItems.Sum(x => x.Price * x.Quantity);
        baseOrder.DevelopUserId = input.LineProductDetail.DevelopUserId;
        var productOperatorUser = input.LineProductDetail.ProductOperatorUser?.FirstOrDefault(x => x.SellingPlatform == baseOrder.SellingPlatform);
        baseOrder.OperatorUserId = productOperatorUser?.OperatorUserId;
        baseOrder.OperatorAssistantUserId = productOperatorUser?.OperatorAssistantUserId;


        //lineOrder
        lineOrder.LineProductId = input.LineProductId;
        lineOrder.LineProductSkuId = input.LineProductSkuId;
        lineOrder.SupplierId = input.SupplierId;
        lineOrder.PurchaseSourceType = input.LineProductDetail.PurchaseSourceType;
        lineOrder.SupplierActivityId = input.LineProductDetail.OpenSupplierSettingInfo?.ActivityId;
        lineOrder.OpenSupplierType = input.SupplierApiType.HasValue
            ? _openPlatformBaseService.MapSupplierApiTypeToOpenSupplierType(input.SupplierApiType.Value)
            : null; ;
        lineOrder.TimeSlotName = input.LineProductSkuDetail.TimeSlotName;
        if (input.TravelDate.HasValue)
        {
            lineOrder.TravelBeginDate = input.TravelDate.Value;
            lineOrder.TravelEndDate = input.TravelDate.Value.AddDays(input.LineProductDetail.Days - 1);
        }

        //SkuTypeItems 
        var paymentCurrencyCode = orderPrices.First().PaymentCurrencyCode;
        var saleCurrencyCode = orderPrices.First().OrgPriceCurrencyCode;
        var costCurrencyCode = orderPrices.First().CostCurrencyCode;
        var exchangeRate = orderPrices.First().ExchangeRate;
        var costExchangeRate = orderPrices.First().CostExchangeRate;
        var newOrderPrices = new List<OrderPrice>();
        var newLineOrderSkuTypeItems = new List<TravelLineOrderSkuTypeItem>();
        foreach (var item in input.SkuTypeItems)
        {
            if (item.CostCurrencyCode != costCurrencyCode)
                costExchangeRate = item.CostExchangeRate;

            //orderPrice
            OrderPrice orderPrice = new()
            {
                BaseOrderId = input.BaseOrderId,
                SubOrderId = lineOrder.Id,
                OrderType = OrderType.TravelLineOrder,
                OrderSubType = (int)item.SkuPriceType,
                OrderSubItemId = item.SkuTypeItemId,
                OrgPrice = item.OrgPrice,
                OrgCostPrice = item.OrgCostPrice,
                PriceType = OrderPriceType.Default,
                Price = item.Price,
                CostPrice = item.CostPrice,
                CostPriceType = OrderPriceType.Default,
                Quantity = item.Quantity,
                PaymentCurrencyCode = paymentCurrencyCode,
                CostCurrencyCode = item.CostCurrencyCode,
                OrgPriceCurrencyCode = saleCurrencyCode,
                CostExchangeRate = costExchangeRate,
                ExchangeRate = exchangeRate,
                CostDiscountRate = input.LineProductDetail.CostDiscountRate, //非API对接=>取产品侧配置的数据
            };
            newOrderPrices.Add(orderPrice);

            //lineOrderSkuTypeItems
            if (item.SkuTypeItemId.HasValue)
            {
                TravelLineOrderSkuTypeItem lineOrderSkuTypeItem = new()
                {
                    BaseOrderId = input.BaseOrderId,
                    TravelLineOrderId = lineOrder.Id,
                    SkuTypeItemId = item.SkuTypeItemId!.Value,
                    SkuTypeItemName = item.SkuTypeItemName,
                    SkuPriceType = item.SkuPriceType,
                    ActivityId = item.ActivityId,
                    PackageId = item.PackageId,
                    SkuId = item.SkuId
                };
                newLineOrderSkuTypeItems.Add(lineOrderSkuTypeItem);
            }
        }

        if (newOrderPrices.Any())
            await _dbContext.AddRangeAsync(newOrderPrices);

        if (newLineOrderSkuTypeItems.Any())
            await _dbContext.AddRangeAsync(newLineOrderSkuTypeItems);

        //日志记录
        var orderLog = new OrderLogs
        {
            OrderId = baseOrder.Id,
            UserId = input.OperationUserDto.UserId,
            UserName = input.OperationUserDto.Name,
            OperationRole = input.OperationUserDto.UserType,
            OperationType = OrderOperationType.ReplaceOrderProduct,
            OrderLogType = OrderLogType.TravelLine,
            ExtensionData = JsonConvert.SerializeObject(extensionData)
        };
        await _dbContext.AddAsync(orderLog);

        //订单状态[已确认]并且切换产品[API对接].需要自动进行供应端采购
        if (lineOrder.Status == TravelLineOrderStatus.Confirmed && input.LineProductDetail.PurchaseSourceType ==
            LineProductPurchaseSourceType.InterfaceDock)
        {
            await _capPublisher.PublishAsync(CapTopics.Order.TravelLineSupplierOrderCreate,
                new CreateSupplierApiOrderMessage
                {
                    BaseOrderId = baseOrder.Id,
                    TenantId = baseOrder.TenantId
                });
        }

        await CompensationOrderDataUpdateCap(
            orderCategory: baseOrder.OrderCategory,
            baseOrderId: baseOrder.Id,
            tenantId: baseOrder.TenantId,
            orderType: baseOrder.OrderType);
    }

    #region private

    /// <summary>
    /// 渠道单号编辑校验
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    private async Task<string[]> EditChannelOrderNoCheck(long baseOrderId)
    {
        //包含正常渠道单和异常渠道单.查出渠道的渠道订单号列表
        var channelOrderNo = await _dbContext.OpenChannelSyncFailOrders.AsNoTracking()
            .Where(x => x.Id == baseOrderId)
            .Select(x => x.ChannelOrderNo)
            .FirstOrDefaultAsync();

        if (string.IsNullOrEmpty(channelOrderNo))
        {
            channelOrderNo = await _dbContext.TravelLineOtaOrders.AsNoTracking()
                .Where(x => x.BaseOrderId == baseOrderId)
                .Select(x => x.ChannelOrderNo)
                .FirstOrDefaultAsync();
        }

        var channelOrderNos =
            channelOrderNo?.Split(",", StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries) ??
            Array.Empty<string>();

        return channelOrderNos;
    }


    /// <summary>
    ///  补差单数据更新事件
    /// </summary>
    /// <param name="orderCategory"></param>
    /// <param name="baseOrderId"></param>
    /// <param name="tenantId"></param>
    private async Task CompensationOrderDataUpdateCap(OrderCategory orderCategory, long baseOrderId, long tenantId, OrderType orderType)
    {
        if ((orderCategory & OrderCategory.CompensationMainOrder) != 0)
        {
            var capHeaders = new Dictionary<string, string?> { { "Tenant", tenantId.ToString() } };
            await _capPublisher.PublishAsync(CapTopics.Order.CompensationOrderDataUpdate,
                new CompensationOrderDataUpdateMessage
                {
                    CompensationMasterOrderId = baseOrderId,
                    OrderType = orderType
                }, capHeaders);
        }
    }

    #endregion
}
