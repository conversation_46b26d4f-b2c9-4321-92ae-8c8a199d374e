using AutoMapper;
using Cit.Storage.Redis;
using Common.GlobalException;
using Common.Jwt;
using Common.ServicesHttpClient;
using Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Inventory.Messages;
using Contracts.Common.Marketing.DTOs.UserCoupon;
using Contracts.Common.Marketing.Enums;
using Contracts.Common.Marketing.Messages;
using Contracts.Common.Notify.Messages;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.DTOs.HotelOrder;
using Contracts.Common.Order.DTOs.MessageNotify;
using Contracts.Common.Order.DTOs.OrderCommission;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.Messages;
using Contracts.Common.Resource.DTOs.GDSHotel;
using Contracts.Common.Resource.DTOs.Hotel;
using Contracts.Common.Resource.DTOs.ThirdHotel;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Scenic.Messages;
using Contracts.Common.Tenant.DTOs.AgencyCredit;
using Contracts.Common.Tenant.DTOs.AgencyLevelConfigHotel;
using Contracts.Common.Tenant.DTOs.B2BWebConfiguration;
using Contracts.Common.Tenant.DTOs.Supplier;
using Contracts.Common.Tenant.Enums;
using Contracts.Common.User.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Hangfire;
using HangfireClient.Jobs.Order;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Order.Api.Extensions;
using Order.Api.Model;
using Order.Api.Notification;
using Order.Api.Requests;
using Order.Api.Requests.HotelOrder;
using Order.Api.Services.Interfaces;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using System.Text.RegularExpressions;
using UserType = Contracts.Common.Order.Enums.UserType;

namespace Order.Api.Services;

public class HotelOrderService : BaseOrderSeriesNumberService, IHotelOrderService
{
    private readonly IMapper _mapper;
    private readonly ICapPublisher _capPublisher;
    private readonly CustomDbContext _dbContext;
    private readonly IUserCouponOrderService _userCouponOrderService;
    private readonly IMultPriceCalculateService _multPriceCalculateService;
    private readonly IMessageNotifyService _messageNotifyService;
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly IMediator _mediator;
    private readonly ServicesAddress _servicesAddress;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IAgencyCreditPayService _agencyCreditPayService;
    private readonly IAgencyService _agencyService;
    private readonly ICurrencyExchangeRateService _currencyExchangeRateService;
    private readonly IHotelApiOrderService _hotelApiOrderService;
    private readonly IRedisClient _redisClient;
    private readonly ILogger<HotelOrderService> _logger;
    private readonly IBaseOrderDiscountService _baseOrderDiscountService;
    private readonly ISupplierService _supplierService;
    private readonly IGDSOrderService _gDSOrderService;
    private readonly IHotelSupplierOrderDomainService _hotelSupplierOrderDomainService;

    public HotelOrderService(IMapper mapper,
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> servicesAddress,
        ICapPublisher capPublisher,
        CustomDbContext dbContext,
        IUserCouponOrderService userCouponOrderService,
        IMultPriceCalculateService multPriceCalculateService,
        IMessageNotifyService messageNotifyService,
        IBackgroundJobClient backgroundJobClient,
        IMediator mediator,
        IHttpContextAccessor httpContextAccessor,
        IAgencyCreditPayService agencyCreditPayService,
        IAgencyService agencyService,
        ICurrencyExchangeRateService currencyExchangeRateService,
        IHotelApiOrderService hotelApiOrderService,
        IRedisClient redisClient,
        ILogger<HotelOrderService> logger,
        IBaseOrderDiscountService baseOrderDiscountService,
        ISupplierService supplierService,
        IHotelSupplierOrderDomainService hotelSupplierOrderDomainService,
        IGDSOrderService gDSOrderService) : base(dbContext)
    {
        _mapper = mapper;
        _capPublisher = capPublisher;
        _dbContext = dbContext;
        _userCouponOrderService = userCouponOrderService;
        _multPriceCalculateService = multPriceCalculateService;
        _messageNotifyService = messageNotifyService;
        _backgroundJobClient = backgroundJobClient;
        _mediator = mediator;
        _httpClientFactory = httpClientFactory;
        _servicesAddress = servicesAddress.Value;
        _httpContextAccessor = httpContextAccessor;
        _agencyCreditPayService = agencyCreditPayService;
        _agencyService = agencyService;
        _currencyExchangeRateService = currencyExchangeRateService;
        _hotelApiOrderService = hotelApiOrderService;
        _redisClient = redisClient;
        _logger = logger;
        _baseOrderDiscountService = baseOrderDiscountService;
        _supplierService = supplierService;
        _gDSOrderService = gDSOrderService;
        _hotelSupplierOrderDomainService = hotelSupplierOrderDomainService;
    }

    /// <summary>
    /// C端用户创建订单
    /// </summary>
    public async Task<CreateHotelOrderOutput> CreateByCustomer(CreateByCustomerInput input, CurrentUser user)
    {
        var salesChannel = SellingChannels.WechatMall;

        var priceRequest = new CheckSaleInput()
        {
            SupplierApiType = input.SupplierApiType,
            HotelId = input.HotelId,
            PriceStrategyId = input.StrategyId,
            BeginDate = input.DateBegin,
            EndDate = input.DateEnd,
            Quantity = input.Num,
            SalesChannel = SellingChannels.WechatMall
        };
        var priceReponse = await PreOrderCheckSale(priceRequest);
        var tenantId = _httpContextAccessor.HttpContext.GetTenantId();
        var room = priceReponse.Data.Room;
        var priceStrategy = priceReponse.Data.PriceStrategy;

        HotelApiOrderInfo? hotelApiOrderInfo = null;
        if (input.SupplierApiType != SupplierApiType.None)
        {
            var checkAvailabilityInput = new CheckAvailabilityInput
            {
                TenantId = tenantId,
                SupplierApiType = input.SupplierApiType,
                ResourceHotelId = priceReponse.Data.ResourceHotelId,
                ResourceRoomId = room.Id,
                PricestrategyId = priceStrategy.Id,
                AdultNum = input.HotelGuests.Count(),
                CheckIn = input.DateBegin,
                CheckOut = input.DateEnd,
                RoomNum = input.Num,
                CalendarPrices = priceStrategy.DatePrice.Select(x => new Contracts.Common.Resource.DTOs.ThirdHotel.HotelOrderCalendarPriceDto
                {
                    CostPrice = x.Cost!.Value,
                    Date = x.Date
                })
            };
            hotelApiOrderInfo = await GetHotelApiOrderInfo(checkAvailabilityInput);
        }

        //多币种
        string paymentCurrencyCode = Currency.CNY.ToString();
        var priceExchangeRate = await _currencyExchangeRateService.GetOrderPriceExchange(new OrderPriceExchangeRateInput
        {
            CostCurrencyCode = priceStrategy.CostCurrencyCode,
            SaleCurrencyCode = priceStrategy.SaleCurrencyCode,
            PaymentCurrencyCode = paymentCurrencyCode,
        });
        //每日价格
        var calendarPrices = new List<HotelCalendarPriceDto>();
        IEnumerable<HotelDateMultPrice> dateMultPrices = await _multPriceCalculateService.GetHotelDateMultPrices(new GetHotelMultPriceInput
        {
            SupplierApiType = input.SupplierApiType,
            HotelId = input.HotelId,
            PriceStragyId = priceStrategy.Id,
            DatePrices = priceStrategy.DatePrice
                .Select(d => new HotelDatePrice
                {
                    Date = d.Date,
                    Price = d.Price
                })
                .ToList()
        });
        var totalAmount = 0m;//总房价
        List<decimal> datePrices = new();
        foreach (var price in priceStrategy.DatePrice)
        {
            var multPrice = dateMultPrices.First(d => d.Date == price.Date);
            HotelCalendarPriceDto calendarPriceDto = new()
            {
                Date = price.Date,
                PriceType = multPrice.VipPrice.HasValue ? OrderPriceType.VipPrice : OrderPriceType.Default,
                CostCurrencyCode = priceStrategy.CostCurrencyCode,
                CostPrice = price.Cost ?? 0,
                OrgPrice = multPrice.RealPrice,
                OrgPriceCurrencyCode = priceStrategy.SaleCurrencyCode,
                PaymentCurrencyCode = paymentCurrencyCode,
                SalePrice = multPrice.RealPrice * priceExchangeRate.ExchangeRate,
                CostExchangeRate = priceExchangeRate.CostExchangeRate,
                ExchangeRate = priceExchangeRate.ExchangeRate
            };
            calendarPrices.Add(calendarPriceDto);
            var datePrice = calendarPriceDto.SalePrice * input.Num;
            totalAmount += datePrice;
            datePrices.Add(datePrice);
        }

        var discountItems = new List<OrderDiscountItemDto>();
        //验证优惠券
        if (input.UserCouponId > 0 && input.SupplierApiType == SupplierApiType.None)
        {
            var getOrderUserCouponRequest = new GetOrderUserCouponsInput()
            {
                UserId = user.userid,
                UserCouponId = input.UserCouponId,
                ProductType = Contracts.Common.Marketing.Enums.LimitProductType.Hotel,//酒店
                OrderProductInfos = new List<OrderProductInfo>
                    {
                        new OrderProductInfo()
                        {
                           ProductId=input.HotelId,
                           ItemId=long.Parse( priceStrategy.Id),
                           OrderAmount=totalAmount,
                           OrderHotelInfo = new OrderHotelInfo { NumberOfRoom = input.Num, DatePrices = datePrices }
                        }
                    }
            };
            var orderUserCoupon = await _userCouponOrderService.GetOrderUserCoupon(getOrderUserCouponRequest);
            if (orderUserCoupon?.IsEnable is not true)
            {
                throw new BusinessException(ErrorTypes.Marketing.UserCouponDisabled);//优惠券不可用
            }

            discountItems.Add(new OrderDiscountItemDto
            {
                DiscountType = OrderDiscountType.UserCoupon,
                DiscountId = orderUserCoupon.UserCouponId,
                DiscountAmount = orderUserCoupon.Discount,
                Title = orderUserCoupon.CouponName
            });
        }
        var discountAmount = discountItems.Sum(x => x.DiscountAmount);//优惠金额
        var paymentAmount = totalAmount - discountAmount;
        if (paymentCurrencyCode == Currency.CNY.ToString())//CNY 抹零
        {
            var diff = paymentAmount - Math.Floor(paymentAmount);
            if (diff > 0)
            {
                discountItems.Add(new OrderDiscountItemDto
                {
                    DiscountType = OrderDiscountType.IgnoreDecimals,
                    DiscountAmount = diff,
                });
                discountAmount = discountItems.Sum(x => x.DiscountAmount);
                paymentAmount -= diff;
            }
        }

        var customerInfo = await _multPriceCalculateService.GetCustomerUserInfo();
        var createInput = new CreateHotelOrderInput()
        {
            TenantId = _httpContextAccessor.HttpContext.GetTenantId(),
            SellingPlatform = input.SellingPlatform,
            SellingChannel = salesChannel,
            ChannelOrderNo = "",
            CheckIn = input.DateBegin,
            CheckOut = input.DateEnd,
            RoomCount = input.Num,
            Adults = input.HotelGuests.Count(),
            GuestInfos = input.HotelGuests,
            ContactsName = input.ContactsName,
            ContactsPhoneNumber = input.ContactsPhoneNumber,
            DiscountItems = discountItems,
            TotalAmount = totalAmount,
            DiscountAmount = discountAmount,
            PaymentAmount = paymentAmount,
            PaymentCurrencyCode = paymentCurrencyCode,
            CalendarPrices = calendarPrices,
            SupplierApiType = input.SupplierApiType,
            HotelApiOrderInfo = hotelApiOrderInfo,
            HotelId = input.HotelId,
            HotelName = priceReponse.Data.HotelName,
            IsAutoConfirmRoomStatus = priceReponse.Data.IsAutoConfirmRoomStatus,
            Room = _mapper.Map<CheckSaleRoomDto>(room),
            PriceStrategy = _mapper.Map<CheckSalePriceStrategyDto>(priceStrategy),
            UserInfo = new OrderUserInfo()
            {
                UserId = user.userid,
                NickName = user.nickname,
                VipLevelId = customerInfo?.VipLevel?.VipLevelId ?? 0,
                VipLevelName = customerInfo?.VipLevel?.Name ?? "",
                UserType = UserType.Customer,
            },
        };

        var result = await Create(createInput);
        return result;
    }

    /// <summary>
    /// 分销商创建订单
    /// </summary>
    public async Task<CreateHotelOrderOutput> CreateByAgency(CreateByAgencyInput input)
    {
        var isExist = await _dbContext.BaseOrders
            .AnyAsync(x => x.SellingChannels == input.SellingChannel
                && x.UserId == input.AgencyId
                && x.ChannelOrderNo == input.ChannelOrderNo);
        if (isExist)
            throw new BusinessException(ErrorTypes.Order.OrderIsExist);

        //检验酒店库存价格房态
        var priceRequest = new CheckSaleInput()
        {
            SupplierApiType = SupplierApiType.None,
            HotelId = input.HotelId,
            PriceStrategyId = input.PriceStrategyId.ToString(),
            BeginDate = input.CheckIn,
            EndDate = input.CheckOut,
            Quantity = input.RoomCount,
            SalesChannel = input.SellingChannel
        };
        var priceReponse = await PreOrderCheckSale(priceRequest);

        var room = priceReponse.Data.Room;
        var priceStrategy = priceReponse.Data.PriceStrategy;
        //多币种
        string paymentCurrencyCode = Currency.CNY.ToString();
        var priceExchangeRate = await _currencyExchangeRateService.GetOrderPriceExchange(new OrderPriceExchangeRateInput
        {
            CostCurrencyCode = priceStrategy.CostCurrencyCode,
            SaleCurrencyCode = priceStrategy.SaleCurrencyCode,
            PaymentCurrencyCode = paymentCurrencyCode,
        });

        //订单日历价格
        var calendarPrices = priceStrategy.DatePrice.Select(x => new HotelCalendarPriceDto()
        {
            Date = x.Date,
            CostCurrencyCode = priceStrategy.CostCurrencyCode,
            CostPrice = x.Cost ?? 0,
            OrgPrice = x.Price,
            OrgPriceCurrencyCode = priceStrategy.SaleCurrencyCode,
            SalePrice = Math.Round(x.Price * priceExchangeRate.ExchangeRate, 2),
            PaymentCurrencyCode = paymentCurrencyCode,
            CostExchangeRate = priceExchangeRate.CostExchangeRate,
            ExchangeRate = priceExchangeRate.ExchangeRate,
        }).ToList();

        var totalAmount = calendarPrices.Sum(x => x.SalePrice) * input.RoomCount;
        if (totalAmount != input.Amount)
            throw new BusinessException(ErrorTypes.Order.ProductPriceChange);

        var createInput = new CreateHotelOrderInput()
        {
            TenantId = _httpContextAccessor.HttpContext.GetTenantId(),
            SellingPlatform = input.SellingPlatform,
            SellingChannel = input.SellingChannel,
            ChannelOrderNo = input.ChannelOrderNo,
            CheckIn = input.CheckIn,
            CheckOut = input.CheckOut,
            RoomCount = input.RoomCount,
            Adults = input.Adults,
            GuestInfos = new List<HotelGuestsInfo>() { new HotelGuestsInfo { LastName = input.ContactsName[..1], FirstName = input.ContactsName[1..] } },
            ContactsName = input.ContactsName,
            ContactsPhoneNumber = input.ContactsPhoneNumber,
            Message = input.Message,
            DiscountAmount = 0,
            PaymentAmount = input.Amount,
            TotalAmount = input.Amount,
            CalendarPrices = calendarPrices,
            HotelId = input.HotelId,
            HotelName = priceReponse.Data.HotelName,
            IsAutoConfirmRoomStatus = priceReponse.Data.IsAutoConfirmRoomStatus,
            Room = _mapper.Map<CheckSaleRoomDto>(room),
            PriceStrategy = _mapper.Map<CheckSalePriceStrategyDto>(priceStrategy),
            UserInfo = new OrderUserInfo()
            {
                AgencyId = input.AgencyId,
                AgencyName = input.AgencyName,
                UserType = UserType.Agency
            }
        };

        return await Create(createInput);
    }

    /// <summary>
    /// 酒店手工单创建
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<CreateHotelOrderOutput> CreateByManual(CreateByManualInput input, OperationUserDto operationUser)
    {
        #region 检验酒店库存价格房态

        var salesChannel = SellingChannels.B2b;
        var priceRequest = new CheckPriceStrategySaleInput()
        {
            HotelId = input.HotelId,
            //RoomId = input.RoomId,
            PriceStrategyId = input.StrategyId,
            BeginDate = input.DateBegin,
            EndDate = input.DateEnd,
            Quantity = input.Num,
            SalesChannel = SellingChannels.B2b
        };
        var checkPriceResponse = await CheckPriceStrategySaleByManual(priceRequest);
        var room = checkPriceResponse.Data.Room;
        var priceStrategy = checkPriceResponse.Data.PriceStrategy;

        //多币种
        var agency = await _agencyService.GetAgencyDetail(input.AgencyId);
        string paymentCurrencyCode = agency.CurrencyCode;
        var priceExchangeRate = await _currencyExchangeRateService.GetOrderPriceExchange(new OrderPriceExchangeRateInput
        {
            CostCurrencyCode = priceStrategy.CostCurrencyCode,
            SaleCurrencyCode = priceStrategy.SaleCurrencyCode,
            PaymentCurrencyCode = paymentCurrencyCode,
        });
        var calendarPrices = input.CalendarPrices.Select(x => new HotelCalendarPriceDto
        {
            Date = x.Date,
            CostCurrencyCode = priceStrategy.CostCurrencyCode,
            CostPrice = x.CostPrice,
            OrgPrice = x.SalePrice / priceExchangeRate.ExchangeRate,
            OrgPriceCurrencyCode = paymentCurrencyCode,
            SalePrice = x.SalePrice,
            PaymentCurrencyCode = paymentCurrencyCode,
            CostExchangeRate = priceExchangeRate.CostExchangeRate,
            ExchangeRate = priceExchangeRate.ExchangeRate,
        }).ToList();
        var totalAmount = calendarPrices.Sum(x => x.SalePrice * input.Num);
        #endregion

        //创建手工订单(售卖渠道为：B2B，售卖平台是：手工单)
        var createInput = new CreateHotelOrderInput()
        {
            TenantId = _httpContextAccessor.HttpContext.GetTenantId(),
            SellingPlatform = SellingPlatform.System,
            SellingChannel = salesChannel,
            ChannelOrderNo = input.ChannelOrderNo,
            CheckIn = input.DateBegin,
            CheckOut = input.DateEnd,
            RoomCount = input.Num,
            Adults = input.HotelGuests.Count(),
            GuestInfos = input.HotelGuests,
            ContactsName = input.ContactsName,
            ContactsPhoneNumber = input.ContactsPhoneNumber,
            DiscountAmount = 0,
            PaymentAmount = totalAmount,
            TotalAmount = totalAmount,
            PaymentCurrencyCode = paymentCurrencyCode,
            CalendarPrices = calendarPrices,
            HotelId = input.HotelId,
            HotelName = checkPriceResponse.Data.HotelName,
            IsAutoConfirmRoomStatus = checkPriceResponse.Data.IsAutoConfirmRoomStatus,
            Room = _mapper.Map<CheckSaleRoomDto>(room),
            PriceStrategy = _mapper.Map<CheckSalePriceStrategyDto>(priceStrategy),
            Remark = input.Remark,
            UserInfo = new OrderUserInfo()
            {
                UserId = operationUser.UserId,
                NickName = operationUser.Name,
                AgencyId = input.AgencyId,
                AgencyName = input.AgencyName,
                UserType = UserType.Merchant,
                SalespersonId = agency.SalespersonId,
                SalespersonName = agency.SalespersonName,
                VipLevelId = agency.Level ?? 0,
                VipLevelName = agency.LevelName ?? string.Empty,
            },
            TrackingUserId = operationUser.UserId,
        };

        return await Create(createInput);
    }

    /// <summary>
    /// 酒店手工单编辑
    /// </summary>
    /// <param name="input"></param>
    public async Task EditByManual(EditByManualInput input, OperationUserDto operationUser)
    {
        var baseOrder = await _dbContext.BaseOrders.FirstOrDefaultAsync(x => x.Id == input.BaseOrderId);
        if (baseOrder is null) throw new BusinessException(ErrorTypes.Order.OrderNotFind);
        var hotelOrder = await _dbContext.HotelOrders.FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        if (hotelOrder is null) throw new BusinessException(ErrorTypes.Order.OrderNotFind);
        var hotelOrderCalendarPrices = await _dbContext.HotelOrderCalendarPrices
            .Where(x => x.HotelOrderId == hotelOrder.Id)
            .ToListAsync();

        #region 判断是否可以编辑

        var hotelOrderStatusType = new HotelOrderStatus[]
        {
            HotelOrderStatus.WaitingForPay,
            HotelOrderStatus.WaitingForConfirm,
            HotelOrderStatus.WaitingForCheckIn,
            HotelOrderStatus.CheckedIn
        };
        //只有手工单可以编辑订单，收款、付款操作后不允许编辑订单。订单状态未完成（待支付,待确认,待入住,已入住）时均可编辑。
        if (!(baseOrder.SellingPlatform == SellingPlatform.System
              && baseOrder.Status is BaseOrderStatus.WaitingForPay or BaseOrderStatus.UnFinished
              && hotelOrderStatusType.Contains(hotelOrder.Status)))
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        #endregion

        #region 检验日历库存

        string paymentCurrencyCode = baseOrder.PaymentCurrencyCode;
        string costCurrencyCode = hotelOrderCalendarPrices.Select(x => x.CostCurrencyCode).First();
        string saleCurrencyCode = hotelOrderCalendarPrices.Select(x => x.OrgPriceCurrencyCode).First();
        var priceExchangeRate = await _currencyExchangeRateService.GetOrderPriceExchange(new OrderPriceExchangeRateInput
        {
            CostCurrencyCode = costCurrencyCode,
            SaleCurrencyCode = saleCurrencyCode,
            PaymentCurrencyCode = paymentCurrencyCode,
        });

        var calendarPrices = input.CalendarPrices.Select(x => new HotelOrderCalendarPrice
        {
            Date = x.Date,
            CostPrice = x.CostPrice,
            SalePrice = x.SalePrice!.Value,
            HotelOrderId = hotelOrder.Id,
            OrgPrice = x.SalePrice!.Value / priceExchangeRate.ExchangeRate,
            PaymentCurrencyCode = paymentCurrencyCode,
            CostCurrencyCode = costCurrencyCode,
            OrgPriceCurrencyCode = saleCurrencyCode,
            ExchangeRate = priceExchangeRate.ExchangeRate,
            CostExchangeRate = priceExchangeRate.CostExchangeRate,
        }).ToList();
        var totalAmount = calendarPrices.Sum(x => x.SalePrice * input.Num);

        //日历价格是否变更
        var calendarUnChange = calendarPrices.Count == hotelOrderCalendarPrices.Count;
        //总金额是否变更
        var diffAmount = totalAmount - baseOrder.PaymentAmount;
        //间数是否变更
        var diffRoomCount = input.Num - hotelOrder.PriceStrategyRoomsCount;

        #endregion

        #region 更新订单信息

        //主订单
        baseOrder.ContactsName = input.ContactsName;
        baseOrder.ContactsPhoneNumber = input.ContactsPhoneNumber;
        baseOrder.ChannelOrderNo = input.ChannelOrderNo;
        baseOrder.TotalAmount = totalAmount;
        baseOrder.PaymentAmount = totalAmount;
        baseOrder.UpdateTime = DateTime.Now;

        //子订单
        hotelOrder.CheckInDate = input.DateBegin;
        hotelOrder.CheckOutDate = input.DateEnd;
        hotelOrder.PriceStrategyRoomsCount = input.Num;
        hotelOrder.PriceStrategyNightsCount = input.DateEnd.Subtract(input.DateBegin).Days;
        hotelOrder.UpdateTime = DateTime.Now;

        #endregion

        #region 更新日历价格

        _dbContext.RemoveRange(hotelOrderCalendarPrices);
        await _dbContext.AddRangeAsync(calendarPrices);

        #endregion

        #region 订单编辑日志

        var createLogs = new OrderLogs
        {
            OperationType = OrderOperationType.Updated,
            OrderLogType = OrderLogType.Hotel,
            OrderId = baseOrder.Id,
            UserId = operationUser.UserId,
            UserName = operationUser.Name,
            OperationRole = operationUser.UserType
        };
        await _dbContext.OrderLogs.AddAsync(createLogs);

        #endregion

        #region 订单备注

        if (string.IsNullOrEmpty(input.Remark) is false)
        {
            var orderRemark = new BaseOrderRemark
            {
                BaseOrderId = baseOrder.Id,
                Remark = input.Remark,
                CreatorId = operationUser.UserId,
                CreatorName = operationUser.Name
            };

            await _dbContext.AddAsync(orderRemark);
        }

        #endregion

        //金额变更执行授信额度支付
        if (baseOrder.Status == BaseOrderStatus.UnFinished && (!calendarUnChange || diffAmount != 0))
        {
            var editPayInput = new OrderEditPayRecordInput
            {
                AgencyId = baseOrder.AgencyId,
                OrderType = OrderType.Hotel,
                OrderId = baseOrder.Id,
                OrderAmount = baseOrder.PaymentAmount
            };
            await _agencyCreditPayService.OrderEditPay(editPayInput);

            //支付成功变更订单状态
            baseOrder.Status = BaseOrderStatus.UnFinished;
            hotelOrder.Status = hotelOrder.PriceStrategyIsAutoConfirm
                ? HotelOrderStatus.WaitingForCheckIn
                : HotelOrderStatus.WaitingForConfirm;
            if (hotelOrder.Status == HotelOrderStatus.WaitingForCheckIn)
            {
                if (!hotelOrder.ConfirmTime.HasValue) hotelOrder.ConfirmTime = DateTime.Now;
            }
            //待入住 && 自动入住
            if (hotelOrder.Status == HotelOrderStatus.WaitingForCheckIn && hotelOrder.HotelIsAutoConfirmRoomStatus)
            {
                //入住日22:00后自动变为已入住
                var checkInTime = hotelOrder.CheckInDate.Date.AddHours(22);
                var ts = checkInTime.Subtract(DateTime.Now);
                _backgroundJobClient?.Schedule<IHotelOrderJob>(x => x.AutoCheckIn(hotelOrder.Id, hotelOrder.TenantId), ts);
            }
        }
        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 验证价格
    /// </summary>
    public async Task<CheckSaleOutput> PreOrderCheckSale(CheckSaleInput request)
    {
        var searchHttpContent = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
        var response = await _httpClientFactory.InternalPostAsync<CheckSaleOutput>(
            _servicesAddress.Hotel_PreOrderCheckSale(), httpContent: searchHttpContent);
        if (response is null || response.Code != CheckPriceStrategySaleCode.Success)
            throw new BusinessException(ErrorTypes.Order.SomeDatesAreUnavailable);

        return response;
    }

    /// <summary>
    /// 校验手工单库存
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    private async Task<CheckPriceStrategySaleOutput> CheckPriceStrategySaleByManual(CheckPriceStrategySaleInput request)
    {
        var searchHttpContent = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
        var response = await _httpClientFactory.InternalPostAsync<CheckPriceStrategySaleOutput>(
            _servicesAddress.Hotel_CheckPriceStrategySaleByManual(), httpContent: searchHttpContent);
        if (response is null)
            throw new BusinessException(ErrorTypes.Inventory.ProductInventoryNotEnough);

        return response.Code switch
        {
            0 => response,
            CheckPriceStrategySaleCode.CalendarNotEnable => throw new BusinessException(ErrorTypes.Order.CalendarNotEnable),
            CheckPriceStrategySaleCode.NumberOfRoomsNotEnough => throw new BusinessException(ErrorTypes.Order.NumberOfRoomsNotEnough),
            _ => throw new BusinessException(ErrorTypes.Inventory.ProductInventoryNotEnough)
        };
    }

    public async Task<List<AgencyLevelConfigHotelOutput>> GetLevelConfigHotelByHotelIds(AgencyLevelConfigHotelInput request)
    {
        var searchHttpContent = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
        var response = await _httpClientFactory.InternalPostAsync<List<AgencyLevelConfigHotelOutput>>(
            _servicesAddress.Tenant_GetLevelConfigHotelByHotelIds(), httpContent: searchHttpContent);
        return response;
    }

    /// <summary>
    /// 创建订单公共方法
    /// </summary>
    [UnitOfWork]
    public async Task<CreateHotelOrderOutput> Create(CreateHotelOrderInput input)
    {
        //主订单
        var baseOrder = new BaseOrder
        {
            ContactsName = input.ContactsName,
            ContactsPhoneNumber = input.ContactsPhoneNumber,
            ContactsEmail = input.ContactsEmail,
            OrderType = OrderType.Hotel,
            ProductName = input.Room.Name,
            ResourceName = input.HotelName,
            EnResourceName = input.HotelEnName,
            ProductSkuName = input.PriceStrategy.Name,
            EnProductSkuName = input.PriceStrategy.EnName,
            UserId = input.UserInfo.UserId,
            UserNickName = input.UserInfo.NickName,
            VipLevelId = input.UserInfo.VipLevelId,
            VipLevelName = input.UserInfo.VipLevelName,
            AgencyId = input.UserInfo.AgencyId,
            AgencyName = input.UserInfo.AgencyName,
            SalespersonId = input.UserInfo.SalespersonId,
            SalespersonName = input.UserInfo.SalespersonName,
            SellingPlatform = input.SellingPlatform,
            SellingChannels = input.SellingChannel,
            ChannelOrderNo = input.ChannelOrderNo,
            GroupNo = input.GroupNo,
            PaymentType = PayType.None,
            Status = BaseOrderStatus.WaitingForPay,
            TotalAmount = input.TotalAmount,
            DiscountAmount = input.DiscountAmount,
            PaymentAmount = input.PaymentAmount,
            PaymentCurrencyCode = input.PaymentCurrencyCode,
            Message = input.Message,
            CommissionRate = input.CommissionRate,
            CommissionFee = input.CommissionFee,
            ExpireStartTime = input.ExpireStartTime,
        };
        await _dbContext.BaseOrders.AddAsync(baseOrder);

        //订单优惠
        if (input.DiscountItems.Any())
        {
            await _dbContext.BaseOrderDiscounts.AddRangeAsync(input.DiscountItems.Select(x => new BaseOrderDiscount
            {
                BaseOrderId = baseOrder.Id,
                DiscountType = x.DiscountType,
                DiscountAmount = x.DiscountAmount,
                DiscountId = x.DiscountId,
                Title = x.Title
            }));
        }
        var user = input.UserInfo;
        //订单创建日志
        var createLogs = new OrderLogs
        {
            OperationType = OrderOperationType.Created,
            OrderLogType = OrderLogType.Hotel,
            OrderId = baseOrder.Id,
            UserId = user.UserId,
            UserName = user.NickName,
            OperationRole = user.UserType
        };
        await _dbContext.OrderLogs.AddAsync(createLogs);

        //子订单
        _ = long.TryParse(input.PriceStrategy.Id, out long priceStrategyId);
        var hotelOrder = new HotelOrder
        {
            SupplierApiType = input.SupplierApiType,
            BaseOrderId = baseOrder.Id,
            PriceStrategyId = priceStrategyId,
            PriceStrategyName = input.PriceStrategy.Name,
            PriceStrategyEnName = input.PriceStrategy.EnName,
            PriceStrategyType = input.PriceStrategy.PriceStrategyType,
            PriceStrategyRoomsCount = input.RoomCount,
            PriceStrategyNightsCount = input.CheckOut.Subtract(input.CheckIn).Days,
            PriceStrategyNumberOfBreakfast = input.PriceStrategy.NumberOfBreakfast,
            BoardCodeType = input.PriceStrategy.BoardCodeType,
            BoardCount = input.PriceStrategy.BoardCount,
            PriceStrategyMaximumOccupancy = input.PriceStrategy.MaximumOccupancy,
            HotelId = input.HotelId,
            HotelName = input.HotelName,
            HotelEnName = input.HotelEnName,
            HotelRoomId = input.Room.Id,
            HotelRoomName = input.Room.Name,
            HotelRoomEnName = input.Room.EnName,
            HotelRoomImgPath = input.Room.HotelRoomImgPath,
            BedTypeJson = input.Room.BedTypes?.Count is > 0 ? JsonConvert.SerializeObject(input.Room.BedTypes) : string.Empty,
            HotelIsAutoConfirmRoomStatus = input.IsAutoConfirmRoomStatus ?? false,
            CheckInDate = input.CheckIn,
            CheckOutDate = input.CheckOut,
            PriceStrategySupplierId = input.PriceStrategy.SupplierId ?? 0,
            PriceStrategyIsAutoConfirm = input.PriceStrategy.IsAutoConfirm ?? false,
            ConfirmByMins = input.PriceStrategy.ConfirmByMins,
            IsDirect = input.PriceStrategy.IsDirect,
            Tag = input.PriceStrategy.Tag,
            TaxDescription = input.PriceStrategy.TaxDescription,
            ArrivalTaxFeesJson = input.PriceStrategy?.ArrivalTaxFees?.Length is > 0 ? JsonConvert.SerializeObject(input.PriceStrategy.ArrivalTaxFees) : null,
            BookingBenefits = input.PriceStrategy.BookingBenefits,
            Status = HotelOrderStatus.WaitingForPay,
            SupplierOrderId = input.SupplierOrderId,
            IsGroupBooking = input.GroupBookingId.HasValue,
            IsOverSaleable = false,
            NationalityJson = input.Nationality is not null ? JsonConvert.SerializeObject(input.Nationality) : null,
        };
        if (hotelOrder.BoardCodeType is null)
        {
            if (hotelOrder.PriceStrategyNumberOfBreakfast is > 0)
            {
                hotelOrder.BoardCodeType = BoardCodeType.BB;
                if (hotelOrder.BoardCount <= 0)
                    hotelOrder.BoardCount = hotelOrder.PriceStrategyNumberOfBreakfast;
            }
            else
                hotelOrder.BoardCodeType = BoardCodeType.RO;
        }
        if (input.PriceStrategy.OverSaleable && input.CalendarPrices.Any(x => x.Stock < hotelOrder.PriceStrategyRoomsCount))
        {
            hotelOrder.IsOverSaleable = true;
        }
        await _dbContext.HotelOrders.AddAsync(hotelOrder);

        if (input.HotelApiOrderInfo is not null)
        {
            HotelApiOrder hotelApiOrder = new()
            {
                BaseOrderId = baseOrder.Id,
                HotelOrderId = hotelOrder.Id,
                SupplierApiType = input.HotelApiOrderInfo.SupplierApiType,
                ResourceHotelId = input.HotelApiOrderInfo.ResourceHotelId,
                HotelId = input.HotelApiOrderInfo.HotelId,
                RoomId = input.HotelApiOrderInfo.RoomId,
                PriceStrategyId = input.HotelApiOrderInfo.PriceStrategyId,
                MinNumberOfRooms = input.PriceStrategy.NumberOfRooms,
                RoomsCount = input.RoomCount,
                ChildNum = input.ChildrenAges?.Any() is true ? input.ChildrenAges!.Count() : null,
                ChildrenAges = input.ChildrenAges?.Any() is true ? string.Join(",", input.ChildrenAges) : null,
                Status = Contracts.Common.Resource.Enums.SupplierApiOrderStatus.WaitForOrder,
                UnionOrderId = input.GroupBookingId,//团房申请单id
            };

            await _dbContext.AddAsync(hotelApiOrder);
        }

        //价格日历
        await _dbContext.HotelOrderCalendarPrices.AddRangeAsync(
            input.CalendarPrices.Select(x => new HotelOrderCalendarPrice
            {
                HotelOrderId = hotelOrder.Id,
                Date = x.Date,
                CostCurrencyCode = x.CostCurrencyCode,
                CostPrice = x.CostPrice,
                PaymentCurrencyCode = x.PaymentCurrencyCode,
                SalePrice = x.SalePrice,
                VipPrice = x.PriceType == OrderPriceType.VipPrice ? x.SalePrice : null,
                OrgPrice = x.OrgPrice,
                OrgPriceCurrencyCode = x.OrgPriceCurrencyCode,
                CostExchangeRate = x.CostExchangeRate,
                ExchangeRate = x.ExchangeRate,
            }));
        //入住人
        var pattern = @"^[\u4e00-\u9fa5·]+";
        var guests = input.GuestInfos.Select(x =>
        {
            var delimiter = Regex.IsMatch(x.LastName!, pattern) && Regex.IsMatch(x.FirstName!, pattern) ? "" : " ";
            var guestName = $"{x.LastName}{delimiter}{x.FirstName}";
            return new HotelOrderGuest
            {
                RoomNumber = x.RoomNumber,
                BaseOrderId = baseOrder.Id,
                HotelOrderId = hotelOrder.Id,
                GuestName = guestName,
                FirstName = x.FirstName,
                LastName = x.LastName,
                HotelOrderGuestType = x.HotelOrderGuestType,
                Age = x.Age,
                Gender = x.Gender,
                CountryCode = x.CountryCode,
                CountryName = x.CountryName,
                EnCountryName = x.EnCountryName,
                IsoCode = x.IsoCode,
            };
        });
        await _dbContext.AddRangeAsync(guests);
        //取消规则
        if (input.PriceStrategy.CancelRule is not null)
        {
            var rule = input.PriceStrategy.CancelRule;
            var hotelOrderCancelRule = new HotelOrderCancelRule()
            {
                CancelRuleId = rule.CancelRuleId,
                BeforeCheckInDays = rule.BeforeCheckInDays,
                BeforeCheckInTime = rule.BeforeCheckInTime,
                CancelChargeType = rule.CancelChargeType,
                CancelRulesType = rule.CancelRulesType,
                ChargeValue = rule.ChargeValue,
                CheckInDateTime = rule.CheckInDateTime,
                HotelOrderId = hotelOrder.Id,
                Description = rule.Description,
            };
            await _dbContext.AddAsync(hotelOrderCancelRule);
        }

        //订单备注
        if (!string.IsNullOrWhiteSpace(input.Remark))
        {
            var orderRemark = new BaseOrderRemark
            {
                BaseOrderId = baseOrder.Id,
                Remark = input.Remark,
                CreatorId = user.UserId,
                CreatorName = user.NickName
            };

            await _dbContext.AddAsync(orderRemark);
        }

        if (input.OrderMultPrices is not null)
        {
            var prices = _mapper.Map<List<HotelOrderPrice>>(input.OrderMultPrices);
            prices.ForEach(pr =>
            {
                pr.BaseOrderId = baseOrder.Id;
                pr.SubOrderId = hotelOrder.Id;
            });
            await _dbContext.AddRangeAsync(prices);
        }

        if (input.OrderCommission is not null)
        {
            var commission = _mapper.Map<OrderCommission>(input.OrderCommission);
            commission.BaseOrderId = baseOrder.Id;
            commission.CreateTime = DateTime.Now;
            await _dbContext.AddRangeAsync(commission);
        }

        /// 高定订单相关
        if (input.GDSHotelRateInfo is not null)
        {
            var gdsHotelOrder = _mapper.Map<GDSHotelOrder>(input.GDSHotelRateInfo);
            gdsHotelOrder.BaseOrderId = baseOrder.Id;
            gdsHotelOrder.ExclusivePrivileges = input.GDSHotelRateInfo.ExclusivePrivileges?.Any() is true ?
                                                   JsonConvert.SerializeObject(input.GDSHotelRateInfo.ExclusivePrivileges) : null;
            await _dbContext.AddAsync(gdsHotelOrder);
        }

        //订单分享信息
        if (input.TraceId is > 0)
        {
            var orderShareInfo = new OrderShareInfo
            {
                BaseOrderId = baseOrder.Id,
                OrderType = baseOrder.OrderType,
                BuyerId = baseOrder.UserId,
                TraceId = input.TraceId,
                CreateTime = baseOrder.CreateTime
            };
            await _dbContext.OrderShareInfos.AddAsync(orderShareInfo);

            //上送跟踪日志
            await _capPublisher.PublishAsync(CapTopics.Marketing.PushPromotionTraceRecord,
                new PushPromotionTraceRecordMessage
                {
                    PromotionTraceId = orderShareInfo.TraceId.Value,
                    CustomerId = orderShareInfo.BuyerId,
                    BehaviorType = TraceBehaviorType.CreateOrder,
                    OrderType = baseOrder.OrderType,
                    OrderId = baseOrder.Id,
                    VisitTargetName = baseOrder.ResourceName
                });
        }

        var tenantId = input.TenantId;
        //冻结库存
        if (input.SupplierApiType == SupplierApiType.None)
        {
            var frozenCalendarInventoryCommand = new FrozenCalendarInventoryMessage
            {
                TenantId = tenantId,
                BeginDate = input.CheckIn,
                EndDate = input.CheckOut.AddDays(-1),
                ProductId = input.Room.Id,
                ItemId = long.Parse(input.PriceStrategy.Id),
                OrderId = hotelOrder.Id,
                FrozenQuantity = input.RoomCount
            };
            await _capPublisher.PublishAsync(CapTopics.Inventory.FrozenCalendarInventory, frozenCalendarInventoryCommand);
        }

        //使用优惠券
        UserCouponUsedMessage couponUsedCommand = null;
        var discountItem = input.DiscountItems.FirstOrDefault(x => x.DiscountType == OrderDiscountType.UserCoupon);
        if (discountItem is not null)
        {
            couponUsedCommand = new UserCouponUsedMessage
            {
                TenantId = tenantId,
                UserCouponId = discountItem.DiscountId,
                BaseOrderId = baseOrder.Id,
                OrderType = OrderType.Hotel //票券
            };
        }
        if (couponUsedCommand is not null)
            await _capPublisher.PublishAsync(CapTopics.Marketing.UserCouponUsed, couponUsedCommand);
        await _dbContext.SaveChangesAsync();

        //byteplus 订单数据推送
        BytePlusItemSubCategory bytePlusItemSubCategory = BytePlusItemSubCategory.Hotel;
        if (input.SupplierApiType is > SupplierApiType.None)
        {
            if (input.PriceStrategy.IsDirect is true)
                bytePlusItemSubCategory = BytePlusItemSubCategory.DirectApiHotel;
            else
            {
                bytePlusItemSubCategory = input.PriceStrategy.Tag switch
                {
                    Contracts.Common.Resource.Enums.SellHotelTag.O => BytePlusItemSubCategory.OApiHotel,
                    Contracts.Common.Resource.Enums.SellHotelTag.A => BytePlusItemSubCategory.AApiHotel,
                    Contracts.Common.Resource.Enums.SellHotelTag.B => BytePlusItemSubCategory.BApiHotel,
                    Contracts.Common.Resource.Enums.SellHotelTag.C => BytePlusItemSubCategory.CApiHotel,
                    Contracts.Common.Resource.Enums.SellHotelTag.D => BytePlusItemSubCategory.DApiHotel,
                    Contracts.Common.Resource.Enums.SellHotelTag.E => BytePlusItemSubCategory.EApiHotel,
                    _ => bytePlusItemSubCategory,
                };
            }
            if (input.GroupBookingId.HasValue)
            {
                bytePlusItemSubCategory = BytePlusItemSubCategory.GroupBookingHotel;
            }
        }
        await _capPublisher.PublishAsync(CapTopics.Product.BytePlusItemAttributesUpdate, new BytePlusItemAttributesUpdateMessage
        {
            TenantId = tenantId,
            OrderItems = new List<BytePlusOrderItemDto>() {
                new ()
                {
                     Id=$"{baseOrder.Id}_{input.PriceStrategy.Id}",
                     BeginDate=hotelOrder.CheckInDate.ToString("yyyy-MM-dd"),
                     EndDate=hotelOrder.CheckOutDate.ToString("yyyy-MM-dd"),
                     ItemCategory= BytePlusItemCategory.Hotel,
                     ItemSubCategory= bytePlusItemSubCategory,
                     ItemId=hotelOrder.HotelId,
                     ItemName=hotelOrder.HotelName,
                     ItemVariant=$"{hotelOrder.HotelRoomName}/{hotelOrder.PriceStrategyName}",
                     Quantity=hotelOrder.PriceStrategyNightsCount*hotelOrder.PriceStrategyRoomsCount,
                     Price=Math.Round(baseOrder.TotalAmount/hotelOrder.PriceStrategyNightsCount*hotelOrder.PriceStrategyRoomsCount,2)
                }
            }
        });

        if (input.AutoCloseTimeoutOrder is true)
        {
            var time = TimeSpan.FromMinutes(30);
            if (input.ExpireStartTime.HasValue)
            {
                var totleSeconds = (int)input.ExpireStartTime.Value.Subtract(DateTime.Now).TotalSeconds;
                if (totleSeconds <= 0) totleSeconds = 0;
                time = TimeSpan.FromSeconds(totleSeconds);
            }
            _backgroundJobClient.Schedule<HangfireClient.Jobs.IOrderJob>(s => s.CloseTimeoutOrder(baseOrder.Id, tenantId), time);
        }
        return new CreateHotelOrderOutput()
        {
            BaseOrderId = baseOrder.Id,
            HotelOrderId = hotelOrder.Id,
            PaymentAmount = baseOrder.PaymentAmount,
            PaymentCurrencyCode = baseOrder.PaymentCurrencyCode,
            Status = hotelOrder.Status
        };
    }

    #region 第三方平台酒店试单

    /// <summary>
    /// 第三方平台酒店试单信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    public async Task<HotelApiOrderInfo> GetHotelApiOrderInfo(CheckAvailabilityInput input)
    {
        //第三方平台试单接口
        var checkAvailabilityOutput = await ThirdHotelCheckAvailability(input);
        switch (checkAvailabilityOutput.CheckCode)
        {
            case CheckPriceStrategySaleCode.Success:
                return new HotelApiOrderInfo
                {
                    HotelId = checkAvailabilityOutput.HotelId,
                    RoomId = checkAvailabilityOutput.RoomId,
                    PriceStrategyId = checkAvailabilityOutput.PricestrategyId,
                    ResourceHotelId = input.ResourceHotelId,
                    SupplierApiType = input.SupplierApiType
                };
                break;
            default:
                throw new BusinessException(ErrorTypes.Order.SomeDatesAreUnavailable);
                break;
        }
    }

    private async Task<CheckAvailabilityOutput> ThirdHotelCheckAvailability(CheckAvailabilityInput input)
    {
        var requestUri = _servicesAddress.Resource_ThirdHotelCheckAvailability();
        using var content = new StringContent(JsonConvert.SerializeObject(input), Encoding.UTF8, "application/json");
        var result = await _httpClientFactory.InternalPostAsync<CheckAvailabilityOutput>(requestUri, httpContent: content);
        return result;
    }

    #endregion

    /// <summary>
    /// 获取Hop 酒店订单最新信息
    /// </summary>
    /// <param name="hotelOrderId"></param>
    /// <returns></returns>
    public async Task<HopHotelOrderUpdatedDto> GetHotelHopUpdatedInfoAsync(HopHotelOrderUpdatedInput hopHotelOrderUpdatedInput)
    {
        var result = new HopHotelOrderUpdatedDto();

        var query = _dbContext.BaseOrders.AsNoTracking()
           .Join(_dbContext.HotelOrders.AsNoTracking(), bo => bo.Id, ho => ho.BaseOrderId,
           (bo, ho) => new HotelOrderQueryDto
           {
               BaseOrder = bo,
               HotelOrder = ho
           })
           .Where(x => x.HotelOrder.Id == hopHotelOrderUpdatedInput.HotelOrderId);

        var order = await query.FirstOrDefaultAsync();

        if (order is null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        var hotelOrder = order.HotelOrder;

        if (order.BaseOrder.Status == BaseOrderStatus.UnFinished)
        {
            if (hotelOrder.SupplierApiType == SupplierApiType.Hop
                && !string.IsNullOrWhiteSpace(hotelOrder.SupplierOrderId))
            {
                try
                {
                    //汇智酒店下单后，每当进入订单详情时（VEBK、B2B），需要进行一次订单信息同步
                    await _hotelApiOrderService.QueryAndHandleResult(new QueryHotelApiOrderMessage
                    {
                        BaseOrderId = order.BaseOrder.Id,
                        TenantId = hotelOrder.TenantId
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "查询同步汇智酒店订单信息异常,BaseOrderId:{@BaseOrderId}", order.BaseOrder.Id);
                }
                //再次查询订单详细信息
                order = await query.FirstOrDefaultAsync();
            }
        }

        result.ConfirmCode = order.HotelOrder.ConfirmCode;
        result.BaseOrderId = order.HotelOrder.BaseOrderId;
        result.Id = order.HotelOrder.Id;
        result.Status = order.HotelOrder.Status;
        result.SupplierOrderId = order.HotelOrder.SupplierOrderId;

        return result;

    }

    /// <summary>
    /// 订单详情
    /// </summary>
    public async Task<DetailOutput> Detail(DetailInput input)
    {
        //AgentId = 1为HOP，其他分销商需判断订单是否为该分销商所有
        if (input.AgencyId > 1 && input.TenantId.HasValue)
        {
            var baseOrder = await GetBaseOrderByChannel(input.AgencyId.Value, input.TenantId!.Value,
                input.SellingChannel!.Value, input.BaseOrderId, input.ChannelOrderNo!);
            input.BaseOrderId = baseOrder.Id;
        }

        var query = _dbContext.BaseOrders.AsNoTracking()
            .Join(_dbContext.HotelOrders.AsNoTracking(), bo => bo.Id, ho => ho.BaseOrderId,
            (bo, ho) => new
            {
                BaseOrder = bo,
                HotelOrder = ho
            })
            .Where(x => x.BaseOrder.Id == input.BaseOrderId)
            .WhereIF(input.UserId.HasValue, x => x.BaseOrder.UserId == input.UserId!.Value)
            .WhereIF(input.AgencyId.HasValue, x => x.BaseOrder.AgencyId == input.AgencyId!.Value)
            .WhereIF(input.SupplierId.HasValue, x => x.HotelOrder.PriceStrategySupplierId == input.SupplierId!.Value);

        var order = await query.FirstOrDefaultAsync();
        if (order is null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        DetailOutput output = _mapper.Map<DetailOutput>(order.BaseOrder);
        output.ChannelOrderNo = !string.IsNullOrWhiteSpace(order.BaseOrder.ChannelOrderNo)
            ? order.BaseOrder.ChannelOrderNo?.Split(",") : Array.Empty<string>();
        var hotelOrder = order.HotelOrder;
        output.OrderDetail = _mapper.Map<HotelOrderDetail>(hotelOrder);
        output.OrderDetail.HotelEnName ??= output.OrderDetail.HotelName;
        output.OrderDetail.HotelRoomEnName ??= output.OrderDetail.HotelRoomName;
        output.OrderDetail.PriceStrategyEnName ??= output.OrderDetail.PriceStrategyName;
        output.OrderDetail.NationalityInfo = !string.IsNullOrWhiteSpace(hotelOrder.NationalityJson)
            ? JsonConvert.DeserializeObject<NationalityInfo>(hotelOrder.NationalityJson)
            : null;
        output.OrderDetail.ArrivalTaxFees = !string.IsNullOrWhiteSpace(hotelOrder.ArrivalTaxFeesJson)
            ? JsonConvert.DeserializeObject<CheckAvailabilityArrivalTaxFeeOutput[]>(hotelOrder.ArrivalTaxFeesJson)
            : null;
        var gdsSupplierApiTypes = new List<SupplierApiType>() {
          SupplierApiType.GDS,
           SupplierApiType.Youxia
        };
        //床型 string.Empty代表没有床型信息
        if (hotelOrder.BedTypeJson != null)
        {
            output.OrderDetail.BedTypes = !string.IsNullOrWhiteSpace(hotelOrder.BedTypeJson)
                ? JsonConvert.DeserializeObject<List<Contracts.Common.Hotel.DTOs.Hotel.BedType>>(hotelOrder.BedTypeJson)
                : new List<Contracts.Common.Hotel.DTOs.Hotel.BedType>();
        }
        if (hotelOrder.SupplierApiType != SupplierApiType.None)
        {
            var apiOrder = await _dbContext.HotelApiOrders
                .Where(x => x.HotelOrderId == hotelOrder.Id)
                .Select(x => new { x.SupplierApiType, x.SupplierOrderId, x.PriceStrategyId, x.ResourceHotelId, x.Status })
                .FirstOrDefaultAsync();
            if (apiOrder is not null)
            {
                output.OrderDetail.SupplierOrderId = apiOrder.SupplierOrderId;
                output.OrderDetail.SupplierApiOrderStatus = apiOrder.Status;
                output.OrderDetail.PriceStrategyId = apiOrder.PriceStrategyId;
                output.OrderDetail.ResourceHotelId = apiOrder.ResourceHotelId;
            }
        }
        var leftStartTime = order.BaseOrder.ExpireStartTime.HasValue ? order.BaseOrder.ExpireStartTime!.Value
            : output.CreateTime.AddMinutes(30);// 默认30分钟
        if (gdsSupplierApiTypes.Contains(hotelOrder.SupplierApiType))
        {
            var gdsOrder = await _dbContext.GDSHotelOrder.FirstOrDefaultAsync(x => x.BaseOrderId == order.BaseOrder.Id);
            if (!string.IsNullOrEmpty(gdsOrder.GuaranteeJson))
                output.Guarantee = JsonConvert.DeserializeObject<GDSGuaranteeInfo>(gdsOrder.GuaranteeJson);
            if (!string.IsNullOrEmpty(gdsOrder.BedTypeJson))
            {
                output.BedType = JsonConvert.DeserializeObject<List<Contracts.Common.Resource.DTOs.Hotel.BedType>>(gdsOrder.BedTypeJson);
                // 因为旧的数据在 hotelOrder.BedTypeJson 没值，所以使用 gdsOrder.BedTypeJson 进行赋值
                if (output.OrderDetail.BedTypes?.Any() is false)
                {
                    // 因为两个对象字段一样，所以直接json化转换
                    output.OrderDetail.BedTypes = !string.IsNullOrWhiteSpace(gdsOrder.BedTypeJson)
                           ? JsonConvert.DeserializeObject<List<Contracts.Common.Hotel.DTOs.Hotel.BedType>>(gdsOrder.BedTypeJson)
                           : new List<Contracts.Common.Hotel.DTOs.Hotel.BedType>();
                }
            }


            if (!string.IsNullOrEmpty(gdsOrder.ExclusivePrivileges))
                output.ExclusivePrivileges = JsonConvert.DeserializeObject<List<GDSHotelExclusivePrivilegeOutput>>(gdsOrder.ExclusivePrivileges);

            output.OrgAmount = gdsOrder.OrgAmount;
            output.OrgCurrencyCode = gdsOrder.OrgCurrencyCode;
            output.RoomDescribe = gdsOrder.RoomDescribe;
            var record = await _dbContext.HotelSupplierOrderRecord
                              .FirstOrDefaultAsync(x => x.ResultIdKey == gdsOrder.BookingKey && x.RecordType == HotelSupplierOrderRecordType.PriceCheck);
            if (record is not null && hotelOrder.SupplierApiType == SupplierApiType.GDS)
            {
                leftStartTime = record.CreateTime.AddMinutes(5);//超时时间5分钟 ， booking 只有5分钟有效期
            }
        }

        var totleSeconds = (int)leftStartTime.Subtract(DateTime.Now).TotalSeconds;
        output.LeftTime = totleSeconds > 0 ? totleSeconds : 0;

        output.CalendarPrices = await _dbContext.HotelOrderCalendarPrices
            .Where(x => x.HotelOrderId == hotelOrder.Id)
            .Select(x => new CalendarPrice
            {
                Date = x.Date,
                PaymentCurrencyCode = x.PaymentCurrencyCode,
                SalePrice = x.SalePrice,
                VipPrice = x.VipPrice,
                CostCurrencyCode = x.CostCurrencyCode,
                CostPrice = x.CostPrice,
                ExchangeRate = x.ExchangeRate,
                OrgPriceCurrencyCode = x.OrgPriceCurrencyCode,
                OrgPrice = x.OrgPrice
            })
            .ToListAsync();
        //默认1对1
        output.CancelRule = await _dbContext.HotelOrderCancelRules
            .Where(x => x.HotelOrderId == hotelOrder.Id)
            .Select(x => new OrderCancelRule
            {
                BeforeCheckInDays = x.BeforeCheckInDays,
                BeforeCheckInTime = x.BeforeCheckInTime,
                CancelChargeType = x.CancelChargeType,
                CancelRuleId = x.CancelRuleId,
                CancelRulesType = x.CancelRulesType,
                ChargeValue = x.ChargeValue,
                CheckInDateTime = x.CheckInDateTime,
                Description = x.Description
            })
            .FirstOrDefaultAsync();

        if (gdsSupplierApiTypes.Contains(hotelOrder.SupplierApiType)
            && output.CancelRule.Amount == null
            && output.CancelRule.ChargeValue > 0)
        {

            output.CancelRule.Amount = decimal.Round((output.CancelRule.ChargeValue / 100m) * order.BaseOrder.PaymentAmount,
                2, MidpointRounding.AwayFromZero);
        }
        //酒店入住人
        var guests = await _dbContext.HotelOrderGuests.AsNoTracking()
        .Where(x => x.BaseOrderId == order.BaseOrder.Id
                    && x.HotelOrderId == hotelOrder.Id)
        .Select(x => new HotelOrderGuestInfo
        {
            GuestName = x.GuestName,
            FirstName = x.FirstName,
            LastName = x.LastName,
            HotelOrderGuestType = x.HotelOrderGuestType,
            Age = x.Age,
            RoomNumber = x.RoomNumber,
            CountryCode = x.CountryCode,
            CountryName = x.CountryName,
            IsoCode = x.IsoCode,
            EnCountryName = x.EnCountryName,
            Gender = x.Gender,
        })
        .OrderBy(x => x.HotelOrderGuestType)
        .ToListAsync();
        output.HotelGuests = guests.GroupBy(s => s.RoomNumber)
            .Select(s => new HotelRoomGuestsDto() { RoomNumber = s.Key, GuestInfos = s.ToList() });

        var hotelOrderPrices = await _dbContext.HotelOrderPrices
                 .Where(x => x.BaseOrderId == order.BaseOrder.Id)
                  .Select(x => _mapper.Map<HotelOrderPriceDto>(x))
                .ToListAsync();
        output.HotelOrderPrices = hotelOrderPrices;

        var orderCommission = await _dbContext.OrderCommission
               .Where(x => x.BaseOrderId == order.BaseOrder.Id)
               .Select(x => _mapper.Map<OrderCommissionDto>(x))
               .FirstOrDefaultAsync();
        output.OrderCommission = orderCommission;

        if (hotelOrder.IsGroupBooking is true)
        {
            var groupBookingOrderId = await _dbContext.GroupBookingOrderItems
                .Where(x => x.BaseOrderId == order.BaseOrder.Id)
                .Select(x => x.GroupBookingOrderId)
                .FirstOrDefaultAsync();
            //团房子单处理
            if (groupBookingOrderId > 0)
            {
                output.GroupBookingOrderId = groupBookingOrderId;

                var orderPaymentAdjusts = await _dbContext.OrderPaymentAdjusts.AsNoTracking()
                  .Where(x => x.BaseOrderId == order.BaseOrder.Id)
                  .OrderBy(x => x.Id)
                  .ToListAsync();
                output.OrderPaymentAdjusts = orderPaymentAdjusts.Select(s => new HotelOrderPaymentAdjustOutput
                {
                    HotelName = hotelOrder.HotelName,
                    HotelEnName = hotelOrder.HotelEnName,
                    HotelRoomName = hotelOrder.HotelRoomName,
                    HotelRoomEnName = hotelOrder.HotelRoomEnName,
                    PriceStrategyName = hotelOrder.PriceStrategyName,
                    PriceStrategyEnName = hotelOrder.PriceStrategyEnName,
                    BaseOrderId = s.BaseOrderId,
                    OrderId = s.OrderId,
                    AdjustAmount = s.AdjustAmount,
                    PaymentCurrencyCode = s.PaymentCurrencyCode,
                    Operator = s.Operator,
                    OperatorId = s.OperatorId,
                    Remark = s.Remark,
                    CreateTime = s.CreateTime,
                }).ToList();

                var gboPayments = await _dbContext.GroupBookingOrderPayments
                    .Where(x => x.GroupBookingOrderId == groupBookingOrderId)
                    .Select(x => new
                    {
                        x.PaymentCurrencyCode,
                        x.LatestPaymentTime,
                        x.PaymentRatioType,
                        x.PaymentRatio,
                        x.PaymentAmount,
                        x.PayStatus,
                        x.PayType,
                        x.PaymentTime,
                    })
                    .ToListAsync();
                var paymentAmount = order.BaseOrder.PaymentAmount;
                var groupPaymentAmount = gboPayments.Sum(s => s.PaymentAmount);
                var downPaymentAmount = gboPayments.FirstOrDefault(x => x.PaymentRatioType == PaymentRatioType.DownPayment)?.PaymentAmount ?? 0;
                //计算各个子单首付比例 金额
                var groupOrderItemBaseOrderIds = await _dbContext.GroupBookingOrderItems
                    .Where(x => x.GroupBookingOrderId == groupBookingOrderId)
                    .Select(x => x.BaseOrderId)
                    .ToListAsync();
                var groupBaseOrderDownPayments = await _dbContext.BaseOrders
                    .Where(s => groupOrderItemBaseOrderIds.Contains(s.Id))
                    .OrderByDescending(s => s.PaymentAmount)
                    .ThenBy(s => s.Id)
                    .Select(s => new GroupBaseOrderDownPaymentDto { BaseOrderId = s.Id, PaymentAmount = s.PaymentAmount })
                    .ToListAsync();
                foreach (var item in groupBaseOrderDownPayments)
                {
                    item.DownPaymentRadio = Math.Round(item.PaymentAmount / groupPaymentAmount, 2);
                    item.DownPaymentAmount = (int)Math.Round(item.DownPaymentRadio * downPaymentAmount, 0);
                }
                if (groupBaseOrderDownPayments.Count > 1)
                {
                    var lastGroupBaseOrderDownPayment = groupBaseOrderDownPayments.LastOrDefault();
                    var lastDownPaymentAmount = downPaymentAmount -
                        groupBaseOrderDownPayments.Where(s => s.BaseOrderId != lastGroupBaseOrderDownPayment.BaseOrderId)
                        .Sum(s => s.DownPaymentAmount);
                    lastGroupBaseOrderDownPayment.DownPaymentAmount = (int)lastDownPaymentAmount;
                }
                var downAmount = groupBaseOrderDownPayments.FirstOrDefault(s => s.BaseOrderId == order.BaseOrder.Id)?.DownPaymentAmount ?? 0;
                var downPayment = gboPayments.Where(x => x.PaymentRatioType == PaymentRatioType.DownPayment)
                    .Select(x => new GroupBookingOrderPaymentOutput
                    {
                        LatestPaymentTime = x.LatestPaymentTime,
                        PaymentRatioType = x.PaymentRatioType,
                        PaymentRatio = x.PaymentRatio,
                        PaymentCurrencyCode = x.PaymentCurrencyCode,
                        PayType = x.PayType,
                        PaymentTime = x.PaymentTime,
                        PayStatus = x.PayStatus,
                        PaymentAmount = downAmount,
                    })
                    .First();
                var finnalPayment = gboPayments.Where(x => x.PaymentRatioType == PaymentRatioType.FinalPayment)
                    .Select(x => new GroupBookingOrderPaymentOutput
                    {
                        LatestPaymentTime = x.LatestPaymentTime,
                        PaymentRatioType = x.PaymentRatioType,
                        PaymentRatio = x.PaymentRatio,
                        PaymentCurrencyCode = x.PaymentCurrencyCode,
                        PayType = x.PayType,
                        PaymentTime = x.PaymentTime,
                        PayStatus = x.PayStatus,
                        PaymentAmount = paymentAmount - downPayment.PaymentAmount,
                    })
                    .First();
                output.GroupBookingOrderPayments =
                    new List<GroupBookingOrderPaymentOutput> { downPayment, finnalPayment };
            }

            output.OrderAdditions = await _dbContext.GroupBookingOrderAdditions.AsNoTracking()
                    .Where(x => x.BaseOrderId == hotelOrder.BaseOrderId)
                    .Select(x => new GroupBookingOrderAdditionOutput
                    {
                        GroupBookingOrderId = x.GroupBookingOrderId,
                        BaseOrderId = x.BaseOrderId,
                        SupplierId = x.SupplierId,
                        AdditionName = x.AdditionName,
                        Amount = x.Amount,
                        CurrencyCode = x.CurrencyCode,
                        Cost = x.Cost,
                        CostCurrencyCode = x.CostCurrencyCode,
                        Quantity = x.Quantity,
                        Remark = x.Remark
                    })
                    .ToListAsync();

        }

        #region 微商城或分销商请求处理
        //主单优惠项信息
        if (input.UserId.HasValue || input.AgencyId.HasValue)
            output.DiscountItems = await _baseOrderDiscountService.GetOrderDiscounts(input.BaseOrderId);
        #endregion

        #region 供应商请求处理
        //供应商处理
        if (input.SupplierId.HasValue)
        {
            var hasOperationRight = await _supplierService.HasOperationRight(input.SupplierId.Value);
            //无运营权
            if (hasOperationRight is false)
            {
                output.TotalAmount = null;
                output.DiscountAmount = null;
                output.PaymentAmount = null;
                output.PaymentCurrencyCode = null;
                var calendarPrices = output.CalendarPrices.ToList();
                foreach (var calendarPrice in calendarPrices)
                {
                    calendarPrice.VipPrice = null;
                    calendarPrice.SalePrice = null;
                    calendarPrice.PaymentCurrencyCode = null;
                }
                output.CalendarPrices = calendarPrices;
            }
        }
        #endregion

        return output;
    }

    public async Task<PagingModel<Contracts.Common.Order.DTOs.HotelOrder.SearchOutput, IList<HotelStatusCountOutput>>> Search(Contracts.Common.Order.DTOs.HotelOrder.SearchInput input)
    {
        var baseOrderIds = new List<long>();
        if (input.BaseOrderId.HasValue)
            baseOrderIds.Add(input.BaseOrderId!.Value);
        if (!string.IsNullOrWhiteSpace(input.SupplierOrderId))
        {
            //采购单号关联的主单
            var baseOrderId = await _dbContext.HotelApiOrders
                .Where(x => x.SupplierOrderId == input.SupplierOrderId)
                .Select(x => x.BaseOrderId)
                .FirstOrDefaultAsync();
            if (baseOrderId == 0)//不存在对应采购单的订单直接返回结果
            {
                return new PagingModel<Contracts.Common.Order.DTOs.HotelOrder.SearchOutput, IList<HotelStatusCountOutput>>
                {
                    Data = Enumerable.Empty<Contracts.Common.Order.DTOs.HotelOrder.SearchOutput>(),
                    PageIndex = input.PageIndex,
                    PageSize = input.PageSize,
                    Supplement = new List<HotelStatusCountOutput>()
                };
            }
            baseOrderIds.Add(baseOrderId);
        }

        var queryable = HotelOrderQuery(new HotelOrderQueryInput
        {
            BaseOrderIds = baseOrderIds,
            SellingPlatform = input.SellingPlatform,
            SellingChannels = input.SellingChannels,
            SupplierId = input.SupplierId,
            HotelName = input.HotelName,
            ContactsName = input.ContactsName,
            ConfirmCode = input.ConfirmCode,
            ChannelOrderNo = input.ChannelOrderNo,
            GroupNo = input.GroupNo,
            CheckInDate = input.CheckInDate,
            CheckInDateEnd = input.CheckInDateEnd,
            CheckOutDate = input.CheckOutDate,
            CheckOutDateEnd = input.CheckOutDateEnd,
            CreateTime = input.CreateTime,
            CreateTimeEnd = input.CreateTimeEnd,
            AgencyId = input.AgencyId,
            SalespersonId = input.SalespersonId,
            PriceSourceType = input.PriceSourceType,
            AgencyUserId = input.AgencyUserId,
            GuestName = input.GuestName,
            PaymentType = input.PaymentType,
            IsDelayedOrder = input.IsDelayedOrder,
            UserId = input.UserId,
            IsGDSOrder = input.IsGDSOrder,
        });

        var dataQueryable = queryable
            .WhereIF(input.Status.HasValue && input.Status != HotelOrderStatus.WaitingForPay,
                a => a.HotelOrder.Status == input.Status!.Value)
            .WhereIF(input.Status == HotelOrderStatus.WaitingForPay,
                a => a.HotelOrder.Status == input.Status!.Value
                     || (a.BaseOrder.DelayedPayStatus == false && a.BaseOrder.Status != BaseOrderStatus.Closed));

        var result = await dataQueryable.PagingAsync(input.PageIndex, input.PageSize,
            x => new Contracts.Common.Order.DTOs.HotelOrder.SearchOutput
            {
                BaseOrderId = x.BaseOrder.Id,
                UserNickName = x.BaseOrder.UserNickName,
                ContactsName = x.BaseOrder.ContactsName,
                ContactsPhoneNumber = x.BaseOrder.ContactsPhoneNumber,
                ContactsEmail = x.BaseOrder.ContactsEmail,
                PaymentAmount = x.BaseOrder.PaymentAmount,
                PaymentCurrencyCode = x.BaseOrder.PaymentCurrencyCode,
                ChannelOrderNo = x.BaseOrder.ChannelOrderNo,
                Status = x.HotelOrder.Status,
                RoomsCount = x.HotelOrder.PriceStrategyRoomsCount,
                NightsCount = x.HotelOrder.PriceStrategyNightsCount,
                HotelName = x.HotelOrder.HotelName,
                HotelEnName = x.HotelOrder.HotelEnName ?? x.HotelOrder.HotelName,
                HotelRoomName = x.HotelOrder.HotelRoomName,
                HotelRoomEnName = x.HotelOrder.HotelRoomEnName ?? x.HotelOrder.HotelRoomName,
                CheckInDate = x.HotelOrder.CheckInDate,
                CheckOutDate = x.HotelOrder.CheckOutDate,
                HotelOrderId = x.HotelOrder.Id,
                PriceStrategyType = x.HotelOrder.PriceStrategyType,
                PriceStrategyName = x.HotelOrder.PriceStrategyName,
                PriceStrategyEnName = x.HotelOrder.PriceStrategyEnName ?? x.HotelOrder.PriceStrategyName,
                SupplierApiType = x.HotelOrder.SupplierApiType,
                IsDirect = x.HotelOrder.IsDirect,
                CreateTime = x.BaseOrder.CreateTime,
                SupplierOrderId = x.HotelOrder.SupplierOrderId,
                DelayedPayStatus = x.BaseOrder.DelayedPayStatus,
                DelayedPayDeadline = x.BaseOrder.DelayedPayDeadline,
            });
        if (input.IsDisplayPayment is false)
        {
            foreach (var searchOutput in result.Data)
            {
                searchOutput.PaymentAmount = null;
                searchOutput.PaymentCurrencyCode = null;
            }
        }
        if (input.IsDisplayCost is true)
        {
            var searchOutputs = result.Data;
            var hotelOrderIds = searchOutputs.Select(x => x.HotelOrderId);
            var hotelOrderPrices = await _dbContext.HotelOrderCalendarPrices
                .Where(x => hotelOrderIds.Contains(x.HotelOrderId))
                .GroupBy(x => new { x.HotelOrderId, x.CostCurrencyCode })
                .Select(x => new
                {
                    x.Key.HotelOrderId,
                    x.Key.CostCurrencyCode,
                    CostPrice = x.Sum(s => s.CostPrice)
                })
                .ToListAsync();
            foreach (var searchOutput in searchOutputs)
            {
                var hotelOrderPrice = hotelOrderPrices.FirstOrDefault(x => x.HotelOrderId == searchOutput.HotelOrderId);
                if (hotelOrderPrice is not null)
                {
                    searchOutput.CostCurrencyCode = hotelOrderPrice.CostCurrencyCode;
                    searchOutput.CostAmount = hotelOrderPrice.CostPrice * searchOutput.RoomsCount;
                }
            }
        }

        var hotelStatusCountOutputs = await queryable
            .GroupBy(x => x.HotelOrder.Status)
            .Select(x => new HotelStatusCountOutput { Status = x.Key, Count = x.Count() })
            .ToListAsync();
        var gdsSupplierApiTypes = new List<SupplierApiType>() {
          SupplierApiType.GDS,
           SupplierApiType.Youxia
        };
        var commisionBaseOrderIds = result.Data.Where(x => gdsSupplierApiTypes.Contains(x.SupplierApiType)).Select(x => x.BaseOrderId).ToList();
        if (commisionBaseOrderIds.Any())
        {
            var commision = await _dbContext.OrderCommission
                .Where(x => commisionBaseOrderIds.Contains(x.BaseOrderId))
                .ToListAsync();
            foreach (var searchOutput in result.Data)
            {
                var commisionInfo = commision.FirstOrDefault(x => x.BaseOrderId == searchOutput.BaseOrderId);
                if (commisionInfo is not null)
                {
                    searchOutput.AgencyCommissionFee = commisionInfo.AgencyCommissionFee;
                    searchOutput.AgencyCommissionRate = commisionInfo.AgencyCommissionRate;
                    searchOutput.AgencyCommisionStatus = commisionInfo.AgencyCommisionStatus;
                }
            }
        }
        return new PagingModel<Contracts.Common.Order.DTOs.HotelOrder.SearchOutput, IList<HotelStatusCountOutput>>
        {
            Data = result.Data,
            PageIndex = result.PageIndex,
            PageSize = result.PageSize,
            Supplement = hotelStatusCountOutputs,
            Total = result.Total
        };
    }

    public async Task<IList<HotelStatusCountOutput>> GetHotelOrderStatusCounts(GetHotelStatusCountInput input)
    {
        var queryable = HotelOrderQuery(new HotelOrderQueryInput
        {
            AgencyId = input.AgencyId,
            AgencyUserId = input.AgencyUserId,
            SellingPlatform = input.SellingPlatform,
            Status = input.Status
        });
        var hotelStatusCountOutputs = await queryable
            .GroupBy(x => x.HotelOrder.Status)
            .Select(x => new HotelStatusCountOutput { Status = x.Key, Count = x.Count() })
            .ToListAsync();
        return hotelStatusCountOutputs;
    }

    /// <summary>
    /// 使用时间范围统计订单数
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public Task<List<GetHotelOrderCheckCountDto>> GetHotelOrderCheckCounts(Contracts.Common.Order.DTOs.HotelOrder.SearchInput input)
    {
        var queryable = HotelOrderQuery(new HotelOrderQueryInput
        {
            AgencyId = input.AgencyId,
            CheckInDate = input.CheckInDate,
            CheckOutDate = input.CheckOutDate,
            MultipleStatus = new List<HotelOrderStatus> { HotelOrderStatus.WaitingForConfirm, HotelOrderStatus.WaitingForCheckIn }
        });

        var hotelStatusCountOutputs = queryable
            .Select(x => new GetHotelOrderCheckCountDto
            {
                Id = x.HotelOrder.Id,
                BaseOrderId = x.HotelOrder.BaseOrderId,
                CheckInDate = x.HotelOrder.CheckInDate,
                CheckOutDate = x.HotelOrder.CheckOutDate,
                Status = x.HotelOrder.Status
            })
            .ToListAsync();

        return hotelStatusCountOutputs;
    }

    public IQueryable<HotelOrderQueryDto> HotelOrderQuery(HotelOrderQueryInput input)
    {
        var gdsSupplierApiTypes = new List<SupplierApiType>
        {
            SupplierApiType.Youxia,
            SupplierApiType.GDS
        };
        var hotelOrderQuery = _dbContext.HotelOrders.AsNoTracking()
            .WhereIF(!string.IsNullOrEmpty(input.HotelName),
                a => a.HotelName.Contains(input.HotelName))
            .WhereIF(!string.IsNullOrEmpty(input.ConfirmCode),
                a => a.ConfirmCode.Contains(input.ConfirmCode))
            .WhereIF(input.SupplierId.HasValue,
                a => a.PriceStrategySupplierId == input.SupplierId!.Value)
             .WhereIF(input.Status.HasValue,
                a => a.Status == input.Status!.Value)
            .WhereIF(input.BaseOrderIds?.Count is > 0,
                a => input.BaseOrderIds.Contains(a.BaseOrderId))
            .WhereIF(input.CheckInDate.HasValue,
                a => a.CheckInDate >= input.CheckInDate!.Value.Date)
            .WhereIF(input.CheckInDateEnd.HasValue,
                a => a.CheckInDate < input.CheckInDateEnd!.Value.Date.AddDays(1))
            .WhereIF(input.CheckOutDate.HasValue,
                a => a.CheckOutDate.Date >= input.CheckOutDate!.Value.Date)
            .WhereIF(input.CheckOutDateEnd.HasValue,
                a => a.CheckOutDate < input.CheckOutDateEnd!.Value.Date.AddDays(1))
            .WhereIF(input.MultipleStatus is not null && input.MultipleStatus.Count > 0,
               a => input.MultipleStatus.Contains(a.Status))
            .WhereIF(input.IsGDSOrder is true, x => gdsSupplierApiTypes.Contains(x.SupplierApiType))     /// 是否GDS订单 ， true - GDS订单 ,false - 非GDS订单 , null - 全部
             .WhereIF(input.IsGDSOrder is false, x => !gdsSupplierApiTypes.Contains(x.SupplierApiType));  /// 因为 b2b 区分高定订单和非高定订单，所以这里需要区分
                                                                                                          /// 小程序却不需要区分，所以这里需要全部
        //入住人筛选
        if (string.IsNullOrEmpty(input.GuestName) is false)
        {
            hotelOrderQuery = hotelOrderQuery
                .Join(_dbContext.HotelOrderGuests, x => x.Id, g => g.HotelOrderId, (x, g) => new { x, g })
                .Where(x => x.g.GuestName == input.GuestName)
                .Select(x => x.x);
        }

        switch (input.PriceSourceType)
        {
            case HotelOrderPriceSourceType.LocalHotel:
                hotelOrderQuery = hotelOrderQuery.Where(x => x.SupplierApiType == SupplierApiType.None);
                break;
            case HotelOrderPriceSourceType.StaffTagHotel:
                hotelOrderQuery = hotelOrderQuery.Where(x => x.SupplierApiType == SupplierApiType.Hop
                    && x.IsDirect == true);
                break;
            case HotelOrderPriceSourceType.NoStaffTagHotel:
                hotelOrderQuery = hotelOrderQuery.Where(x => x.SupplierApiType == SupplierApiType.Hop
                    && x.IsDirect == false);
                break;
            default:
                break;
        }

        var baseOrderQuery = _dbContext.BaseOrders.AsNoTracking()
            .Where(x => x.OrderType == OrderType.Hotel)
            .WhereIF(input.SellingPlatform?.Length is > 0, a => input.SellingPlatform!.Contains(a.SellingPlatform))
            .WhereIF(input.SellingChannels.HasValue, a => a.SellingChannels == input.SellingChannels!.Value)
            .WhereIF(input.CreateTime.HasValue, a => a.CreateTime >= input.CreateTime!.Value.Date)
            .WhereIF(input.CreateTimeEnd.HasValue, a => a.CreateTime < input.CreateTimeEnd!.Value.Date.AddDays(1))
            .WhereIF(!string.IsNullOrEmpty(input.ContactsName), a => a.ContactsName.Contains(input.ContactsName))
            .WhereIF(!string.IsNullOrEmpty(input.ChannelOrderNo), a => a.ChannelOrderNo.Contains(input.ChannelOrderNo!))
            .WhereIF(!string.IsNullOrWhiteSpace(input.GroupNo), a => a.GroupNo.Contains(input.GroupNo!))
            .WhereIF(input.AgencyId is > 0, a => a.AgencyId == input.AgencyId)
            .WhereIF(input.SalespersonId.HasValue, a => a.SalespersonId == input.SalespersonId)
            .WhereIF(input.AgencyUserId is not null, x => input.AgencyUserId.Equals(x.UserId))
            .WhereIF(input.PaymentType.HasValue, x => x.PaymentType == input.PaymentType!.Value)
            .WhereIF(input.IsDelayedOrder is true, x => x.DelayedPayStatus != null)
            .WhereIF(input.UserId.HasValue, a => a.UserId == input.UserId!.Value)
            .OrderByDescending(x => x.CreateTime);

        var queryable = baseOrderQuery
            .Join(hotelOrderQuery, x => x.Id, y => y.BaseOrderId, (x, y) => new HotelOrderQueryDto
            {
                BaseOrder = x,
                HotelOrder = y
            });

        return queryable;
    }

    public class HotelOrderQueryDto
    {
        public BaseOrder BaseOrder { get; set; }
        public HotelOrder HotelOrder { get; set; }
    }

    public async Task<List<ExportDataOutput>> ExportData(ExportDataInput input)
    {
        var baseOrderIds = new List<long>();
        if (input.BaseOrderId.HasValue)
            baseOrderIds.Add(input.BaseOrderId!.Value);
        if (!string.IsNullOrWhiteSpace(input.SupplierOrderId))
        {
            //采购单号关联的主单
            var baseOrderId = await _dbContext.HotelApiOrders
                .Where(x => x.SupplierOrderId == input.SupplierOrderId)
                .Select(x => x.BaseOrderId)
                .FirstOrDefaultAsync();
            if (baseOrderId == 0)//不存在对应采购单的订单直接返回结果
            {
                return new List<ExportDataOutput>();
            }
            baseOrderIds.Add(baseOrderId);
        }
        var gdsSupplierApiTypes = new List<SupplierApiType>() {
          SupplierApiType.GDS,
           SupplierApiType.Youxia
        };
        var hotelOrderQuery = _dbContext.HotelOrders.AsNoTracking()
            .WhereIF(!string.IsNullOrEmpty(input.HotelName),
                a => a.HotelName.Contains(input.HotelName))
            .WhereIF(!string.IsNullOrEmpty(input.ConfirmCode),
                a => a.ConfirmCode.Contains(input.ConfirmCode))
            .WhereIF(input.SupplierId.HasValue,
                a => a.PriceStrategySupplierId == input.SupplierId!.Value)
            .WhereIF(baseOrderIds.Count > 0,
                a => baseOrderIds.Contains(a.BaseOrderId))
            .WhereIF(input.CheckInDate.HasValue,
                a => a.CheckInDate >= input.CheckInDate!.Value.Date)
            .WhereIF(input.CheckInDateEnd.HasValue,
                a => a.CheckInDate < input.CheckInDateEnd!.Value.Date.AddDays(1))
            .WhereIF(input.CheckOutDate.HasValue,
                a => a.CheckOutDate.Date >= input.CheckOutDate!.Value.Date)
            .WhereIF(input.CheckOutDateEnd.HasValue,
                a => a.CheckOutDate < input.CheckOutDateEnd!.Value.Date.AddDays(1))
            .WhereIF(input.IsGDSOrder is true, x => gdsSupplierApiTypes.Contains(x.SupplierApiType))     /// 是否GDS订单 ， true - GDS订单 ,false - 非GDS订单 , null - 全部
             .WhereIF(input.IsGDSOrder is false, x => !gdsSupplierApiTypes.Contains(x.SupplierApiType));

        //入住人筛选
        if (string.IsNullOrEmpty(input.GuestName) is false)
        {
            hotelOrderQuery = hotelOrderQuery
                .Join(_dbContext.HotelOrderGuests, x => x.Id, g => g.HotelOrderId, (x, g) => new { x, g })
                .Where(x => x.g.GuestName == input.GuestName)
                .Select(x => x.x);
        }

        switch (input.PriceSourceType)
        {
            case HotelOrderPriceSourceType.LocalHotel:
                hotelOrderQuery = hotelOrderQuery.Where(x => x.SupplierApiType == SupplierApiType.None);
                break;
            case HotelOrderPriceSourceType.StaffTagHotel:
                hotelOrderQuery = hotelOrderQuery.Where(x => x.SupplierApiType == SupplierApiType.Hop
                    && x.IsDirect == true);
                break;
            case HotelOrderPriceSourceType.NoStaffTagHotel:
                hotelOrderQuery = hotelOrderQuery.Where(x => x.SupplierApiType == SupplierApiType.Hop
                    && x.IsDirect == false);
                break;
            default:
                break;
        }

        var baseOrderQuery = _dbContext.BaseOrders.AsNoTracking()
            .Where(x => x.OrderType == OrderType.Hotel)
            .WhereIF(input.SellingPlatforms.Any(), a => input.SellingPlatforms.Contains(a.SellingPlatform))
            .WhereIF(input.SellingChannels.HasValue, a => input.SellingChannels.Equals(a.SellingChannels))
            .WhereIF(input.CreateTime.HasValue, a => a.CreateTime >= input.CreateTime!.Value.Date)
            .WhereIF(input.CreateTimeEnd.HasValue, a => a.CreateTime < input.CreateTimeEnd!.Value.Date.AddDays(1))
            .WhereIF(!string.IsNullOrEmpty(input.ContactsName), a => a.ContactsName.Contains(input.ContactsName))
            .WhereIF(!string.IsNullOrEmpty(input.ChannelOrderNo), a => a.ChannelOrderNo.Contains(input.ChannelOrderNo!))
            .WhereIF(!string.IsNullOrWhiteSpace(input.GroupNo), a => a.GroupNo.Contains(input.GroupNo!))
            .WhereIF(input.AgencyId is > 0, a => a.AgencyId == input.AgencyId)
            .WhereIF(input.SalespersonId.HasValue, a => a.SalespersonId == input.SalespersonId)
            .WhereIF(input.AgencyUserId is not null, x => input.AgencyUserId.Equals(x.UserId))
            .OrderByDescending(x => x.CreateTime);

        var queryable = baseOrderQuery
            .Join(hotelOrderQuery, x => x.Id, y => y.BaseOrderId, (x, y) => new
            {
                baseOrder = x,
                hotelOrder = y
            });

        var dataQueryable = queryable
            .WhereIF(input.Status.HasValue, a => a.hotelOrder.Status == input.Status!.Value);

        var result = await dataQueryable.Select(
            x => new ExportDataOutput
            {
                CreateTime = x.baseOrder.CreateTime,
                CheckInDate = x.hotelOrder.CheckInDate,
                CheckOutDate = x.hotelOrder.CheckOutDate,
                BaseOrderId = x.baseOrder.Id,
                HotelOrderId = x.hotelOrder.Id,
                HotelName = x.hotelOrder.HotelName,
                HotelRoomName = x.hotelOrder.HotelRoomName,
                ChannelOrderNo = x.baseOrder.ChannelOrderNo,
                RoomsCount = x.hotelOrder.PriceStrategyRoomsCount,
                NightsCount = x.hotelOrder.PriceStrategyNightsCount,
                PaymentAmount = x.baseOrder.PaymentAmount,
                PaymentCurrencyCode = x.baseOrder.PaymentCurrencyCode,
                PaymentMode = x.baseOrder.PaymentMode,
                UserNickName = x.baseOrder.UserNickName,
                ContactsName = x.baseOrder.ContactsName,
                ConfirmCode = x.hotelOrder.ConfirmCode,
                GroupNo = x.baseOrder.GroupNo,
                Status = x.hotelOrder.Status,
                PaymentType = x.baseOrder.PaymentType,
            }).ToListAsync();
        //补充结算单状态
        baseOrderIds = result.Select(x => x.BaseOrderId).ToList();
        var receiptSettlementOrderStatus = await _dbContext.ReceiptSettlementOrderDetails.AsNoTracking()
            .Join(_dbContext.ReceiptSettlementOrders.AsNoTracking(), rsd => rsd.SettlementOrderId, rs => rs.Id, (rsd, rs) => new { rsd, rs })
            .Where(x => baseOrderIds.Contains(x.rsd.BaseOrderId) && x.rsd.BusinessType.Equals(ReceiptSettlementBusinessType.HotelOrder))
            .Select(x => new { x.rsd.BaseOrderId, x.rs.Status }).ToListAsync();

        var hotelOrderGuests = await _dbContext.HotelOrderGuests.AsNoTracking()
            .Where(x => baseOrderIds.Contains(x.BaseOrderId))
            .Select(x => new
            {
                GuestName = x.GuestName,
                FirstName = x.FirstName,
                LastName = x.LastName,
                HotelOrderId = x.HotelOrderId,
            })
            .ToListAsync();
        foreach (var item in result)
        {
            item.SettlementOrderStatus = receiptSettlementOrderStatus
                .Where(x => x.BaseOrderId.Equals(item.BaseOrderId))
                .Select(x => x.Status)
                .FirstOrDefault();
            item.HotelGuestNames = hotelOrderGuests?
                .Where(x => x.HotelOrderId == item.HotelOrderId)
                .Select(x => new HotelOrderGuestInfo
                {
                    GuestName = x.GuestName,
                    FirstName = x.FirstName,
                    LastName = x.LastName,
                })
                .ToList();
        }

        return result;
    }

    [UnitOfWork]
    public async Task Confirm(HotelOrderConfirmInput input, OperationUserDto operationUser)
    {
        var hotelOrder = await _dbContext.HotelOrders
            .Where(x => x.Id == input.Id)
            .Select(x => new
            {
                x.Id,
                x.Status,
            })
            .FirstOrDefaultAsync();
        if (hotelOrder.Status != HotelOrderStatus.WaitingForConfirm)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        await _mediator.Send(new OrderConfirmRequest
        {
            TenantId = input.TenantId,
            HotelOrderId = input.Id,
            ConfirmCode = input.ConfirmCode,
            OperationUser = operationUser
        });
    }

    [UnitOfWork]
    public async Task Refuse(HotelOrderInput input, OperationUserDto operationUser)
    {
        var order = await _dbContext.HotelOrders
            .Join(_dbContext.BaseOrders.AsNoTracking(), h => h.BaseOrderId, b => b.Id,
               (h, b) => new
               {
                   BaseOrder = b,
                   b.PaymentAmount,
                   BaseOrderId = b.Id,
                   HotelOrderId = h.Id,
                   PriceStrategySupplierId = h.PriceStrategySupplierId,
                   Status = h.Status,
                   HotelOrder = h
               })
            .AsNoTracking()
            .Where(x => x.HotelOrderId == input.Id)
            .WhereIF(operationUser.UserType == Contracts.Common.Order.Enums.UserType.Supplier, x => x.PriceStrategySupplierId == input.SupplierId.Value)
            .FirstOrDefaultAsync();
        if (order is null)
            throw new BusinessException(ErrorTypes.Order.OrderNotFind);

        if (order.Status != HotelOrderStatus.WaitingForConfirm)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var logs = new OrderLogs
        {
            OperationType = OrderOperationType.Rejected,
            OrderLogType = OrderLogType.Hotel,
            OrderId = order.BaseOrderId,
            UserId = operationUser.UserId,
            UserName = operationUser.Name,
            OperationRole = operationUser.UserType
        };
        await _dbContext.AddAsync(logs);

        //系统消息通知
        var baseOrder = order.BaseOrder;
        if (baseOrder.SellingPlatform == SellingPlatform.B2BWeb || baseOrder.SellingPlatform == SellingPlatform.B2BApplet)
        {
            await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess, new NotifyMessageProcessMessage
            {
                NotifyEventSubType = Contracts.Common.Notify.Enums.NotifyEventSubType.Hotel_OrderRefused,
                NotifyMode = Contracts.Common.Notify.Enums.NotifyMode.SiteMessage,
                SendToTheRole = Contracts.Common.Notify.Enums.SendToTheRole.AgencyStaff,
                TenantId = baseOrder.TenantId,
                Variables = new
                {
                    BaseOrderId = order.BaseOrderId,
                    AgencyId = baseOrder.AgencyId,
                    UserId = baseOrder.UserId,
                    HotelName = baseOrder.ResourceName,
                    HotelRoomName = baseOrder.ProductName,
                    PriceStrategyName = baseOrder.ProductSkuName
                }
            });

            var hotelOrder = order.HotelOrder;

            if (string.IsNullOrEmpty(baseOrder.ContactsPhoneNumber) is false)
            {
                await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess, new NotifyMessageProcessMessage
                {
                    NotifyEventSubType = Contracts.Common.Notify.Enums.NotifyEventSubType.Hotel_OrderRefused,
                    NotifyMode = Contracts.Common.Notify.Enums.NotifyMode.Sms,
                    SendToTheRole = Contracts.Common.Notify.Enums.SendToTheRole.Customer,
                    NotifyChannel = Contracts.Common.Notify.Enums.NotifyChannel.B2b,
                    TenantId = baseOrder.TenantId,
                    Variables = new
                    {
                        BaseOrderId = baseOrder.Id,
                        hotelOrder.HotelName,
                        hotelOrder.HotelRoomName,
                        Quantity = hotelOrder.PriceStrategyRoomsCount,
                        hotelOrder.CheckInDate,
                        hotelOrder.CheckOutDate,
                        PhoneNumber = baseOrder.ContactsPhoneNumber
                    }
                });
            }

            if (string.IsNullOrEmpty(baseOrder.ContactsEmail) is false)
            {
                var b2bWebConfiguration = await TenantB2bWebConfigurationGet();
                var brandName = b2bWebConfiguration.B2BTitleConfigs.FirstOrDefault(x => x.Language.Equals("zh")).BrandName;
                await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess, new NotifyMessageProcessMessage
                {
                    NotifyEventSubType = Contracts.Common.Notify.Enums.NotifyEventSubType.Hotel_OrderRefused,
                    NotifyMode = Contracts.Common.Notify.Enums.NotifyMode.Email,
                    SendToTheRole = Contracts.Common.Notify.Enums.SendToTheRole.Customer,
                    NotifyChannel = Contracts.Common.Notify.Enums.NotifyChannel.B2b,
                    TenantId = baseOrder.TenantId,
                    Variables = new
                    {
                        BaseOrderId = baseOrder.Id,
                        hotelOrder.HotelName,
                        hotelOrder.HotelRoomName,
                        Quantity = hotelOrder.PriceStrategyRoomsCount,
                        hotelOrder.CheckInDate,
                        hotelOrder.CheckOutDate,
                        AgencyUserName = baseOrder.UserNickName,
                        Addressee = baseOrder.ContactsEmail,
                        BrandName = brandName
                    }
                });
            }

            //微信通知
            await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess, new NotifyMessageProcessMessage
            {
                NotifyEventSubType = Contracts.Common.Notify.Enums.NotifyEventSubType.Hotel_OrderRefused,
                NotifyMode = Contracts.Common.Notify.Enums.NotifyMode.Wechat,
                SendToTheRole = Contracts.Common.Notify.Enums.SendToTheRole.Customer,
                NotifyChannel = Contracts.Common.Notify.Enums.NotifyChannel.B2b,
                TenantId = baseOrder.TenantId,
                Variables = new
                {
                    baseOrder.UserId,
                    BaseOrderId = baseOrder.Id,
                    hotelOrder.HotelName,
                    RoomName = hotelOrder.HotelRoomName,
                    Quantity = hotelOrder.PriceStrategyRoomsCount,
                    hotelOrder.CheckInDate,
                    hotelOrder.CheckOutDate,
                    RefundAmount = baseOrder.PaymentAmount,
                    baseOrder.PaymentCurrencyCode
                }
            });


        }
        //延时订单未实付
        if (baseOrder.DelayedPayStatus.HasValue && baseOrder.PaymentType == PayType.None)
        {
            var response = await _mediator.Send(new OrderDelayedCancelRequest
            {
                BaseOrderId = baseOrder.Id,
                CancelSupplierApiOrder = false,
                OperationUser = operationUser
            });
        }
        else
        {
            await Refund(new RefundHotelOrderInput()
            {
                BaseOrderId = order.BaseOrderId,
                RefundAmount = order.PaymentAmount,
                Message = "商户拒单退款"
            }, operationUser);
        }
    }

    public async Task CheckIn(HotelOrderInput input, OperationUserDto operationUser)
    {
        var order = await _dbContext.HotelOrders
            .Where(x => x.Id == input.Id)
            .WhereIF(operationUser.UserType == UserType.Supplier, x => x.PriceStrategySupplierId == input.SupplierId!.Value)
            .FirstOrDefaultAsync();
        if (order is null)
            throw new BusinessException(ErrorTypes.Order.OrderNotFind);

        if (order.Status != HotelOrderStatus.WaitingForCheckIn)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var baseOrder = await _dbContext.BaseOrders
               .Where(x => x.Id == order.BaseOrderId)
               .FirstOrDefaultAsync();
        //未到入住日期
        if (order.CheckInDate.Date > DateTime.Today)
        {
            //手工单&&自动确认房态 return无需处理
            if (baseOrder.SellingPlatform == SellingPlatform.System && order.HotelIsAutoConfirmRoomStatus)
                return;

            throw new BusinessException(ErrorTypes.Order.CheckInDateNotReached);
        }


        order.Status = HotelOrderStatus.CheckedIn;
        order.UpdateTime = DateTime.Now;
        var logs = new OrderLogs
        {
            OperationType = OrderOperationType.CheckedIn,
            OrderLogType = OrderLogType.Hotel,
            OrderId = order.BaseOrderId,
            UserId = operationUser.UserId,
            UserName = operationUser.Name,
            OperationRole = operationUser.UserType
        };
        await _dbContext.AddAsync(logs);

        //团房
        if (order.IsGroupBooking)
        {
            await _mediator.Send(new GroupBookingOrderStatusRequest
            {
                BaseOrderId = order.BaseOrderId,
                HotelOrderStatus = order.Status
            });
        }

        await _dbContext.SaveChangesAsync();
        //自动离店
        if (order.HotelIsAutoConfirmRoomStatus || order.SupplierApiType == SupplierApiType.GDS || order.SupplierApiType == SupplierApiType.Youxia)
        {
            //离店日14:00后自动变为已入住
            var checkOutTime = order.CheckOutDate.Date.AddHours(14);
            var ts = checkOutTime.Subtract(DateTime.Now);
            _backgroundJobClient.Schedule<IHotelOrderJob>(x => x.AutoFinished(order.Id, order.TenantId), ts);
        }
    }

    [UnitOfWork]
    public async Task CheckOut(HotelOrderInput input, OperationUserDto operationUser)
    {
        var orderInfo = await _dbContext.HotelOrders
            .Join(_dbContext.BaseOrders, a => a.BaseOrderId, b => b.Id,
            (a, b) => new
            {
                HotelOrder = a,
                BaseOrder = b
            })
            .Where(x => x.HotelOrder.Id == input.Id)
            .WhereIF(operationUser.UserType == Contracts.Common.Order.Enums.UserType.Supplier, x => x.HotelOrder.PriceStrategySupplierId == input.SupplierId.Value)
            .FirstOrDefaultAsync();
        if (orderInfo is null)
            throw new BusinessException(ErrorTypes.Order.OrderNotFind);
        var order = orderInfo.HotelOrder;
        var baseOrder = orderInfo.BaseOrder;

        if (order.Status != HotelOrderStatus.CheckedIn)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        if (order.CheckOutDate.Date > DateTime.Today)
            throw new BusinessException(ErrorTypes.Order.CheckOutDateNotReached);

        order.Status = HotelOrderStatus.Finished;
        order.UpdateTime = DateTime.Now;
        baseOrder.Status = BaseOrderStatus.Finished;
        baseOrder.UpdateTime = DateTime.Now;
        var logs = new OrderLogs
        {
            OperationType = OrderOperationType.CheckedOut,
            OrderLogType = OrderLogType.Hotel,
            OrderId = order.BaseOrderId,
            UserId = operationUser.UserId,
            UserName = operationUser.Name,
            OperationRole = operationUser.UserType
        };
        await _dbContext.AddAsync(logs);

        //团房
        if (order.IsGroupBooking)
        {
            await _mediator.Send(new GroupBookingOrderStatusRequest
            {
                BaseOrderId = order.BaseOrderId,
                HotelOrderStatus = order.Status
            });
        }

        //达人奖金待提现
        await _capPublisher.PublishAsync(CapTopics.User.BonusRelease,
           new BonusReleaseMessage
           {
               TenantId = baseOrder.TenantId,
               BaseOrderId = baseOrder.Id,
               UserNickName = baseOrder.UserNickName,
               Status = 1//1-完成
           });
        if (baseOrder.SellingPlatform == SellingPlatform.B2BWeb || baseOrder.SellingPlatform == SellingPlatform.B2BApplet)
        {
            var priceInfo = await _dbContext.HotelOrderCalendarPrices
                .Where(x => x.HotelOrderId == order.Id)
                .Select(x => new { x.ExchangeRate, x.OrgPriceCurrencyCode })
                .FirstOrDefaultAsync();
            await _capPublisher.PublishAsync(CapTopics.Tenant.SyncOrderChangeGrowUpValue, new Contracts.Common.Tenant.DTOs.AgencyLevelDetail.SyncOrderChangeGrowUpValueInput
            {
                AgencyId = baseOrder.AgencyId,
                BusinessType = order.SupplierApiType switch
                {
                    SupplierApiType.Hop => AgencyLevelBusinessType.HuiZhiHotel,
                    _ => AgencyLevelBusinessType.Hotel
                },
                OrderAmout = baseOrder.PaymentAmount / priceInfo.ExchangeRate,
                OrderNo = baseOrder.Id.ToString(),
                Title = baseOrder.ResourceName,
            });
        }

        //记录跟踪日志
        var shareInfo = await _dbContext.OrderShareInfos.AsNoTracking()
            .FirstOrDefaultAsync(x => x.BaseOrderId == baseOrder.Id);
        if (shareInfo?.TraceId is > 0)
        {
            await _capPublisher.PublishAsync(CapTopics.Marketing.PushPromotionTraceRecord,
                new PushPromotionTraceRecordMessage
                {
                    PromotionTraceId = shareInfo.TraceId.Value,
                    CustomerId = shareInfo.BuyerId,
                    BehaviorType = TraceBehaviorType.CompleteOrder,
                    OrderType = baseOrder.OrderType,
                    OrderId = baseOrder.Id,
                    VisitTargetName = baseOrder.ResourceName
                });
        }

        // 发放佣金
        if (order.SupplierApiType == SupplierApiType.GDS || order.SupplierApiType == SupplierApiType.Youxia)
        {
            // TODO:发放佣金
        }
    }

    public async Task EditCost(EditCostInput input, OperationUserDto operationUser)
    {
        var order = await _dbContext.HotelOrders
            .Where(a => a.Id == input.OrderId)
            .WhereIF(operationUser.UserType == Contracts.Common.Order.Enums.UserType.Supplier, a => a.PriceStrategySupplierId == input.SupplierId.Value)
            .FirstOrDefaultAsync();
        var price = await _dbContext.HotelOrderCalendarPrices
            .FirstOrDefaultAsync(a => a.HotelOrderId == input.OrderId && a.Date.Date.Equals(input.Date.Date));
        if (order is null || price is null) throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        price.CostPrice = input.Cost;

        var logs = new OrderLogs
        {
            OperationRole = operationUser.UserType,
            OperationType = OrderOperationType.CostPriceUpdated,
            OrderLogType = OrderLogType.Hotel,
            OrderId = order.BaseOrderId,
            UserId = operationUser.UserId,
            UserName = operationUser.Name
        };
        await _dbContext.AddAsync(logs);
        await _dbContext.SaveChangesAsync();
    }

    public async Task EditConfirmCode(EditConfirmCodeDto input, OperationUserDto operationUser)
    {
        await _mediator.Send(new EditConfirmCodeRequest
        {
            ConfirmCode = input.ConfirmCode,
            OrderId = input.OrderId,
            SupplierId = input.SupplierId,
            TenantId = input.TenantId,
            OperationUser = operationUser
        });
    }

    /// <summary>
    /// 申请退款
    /// </summary>
    [UnitOfWork]
    public async Task<RefundHotelOrderOutput> Refund(RefundHotelOrderInput input, OperationUserDto operationUser)
    {
        var userType = operationUser.UserType;
        var entity = await _dbContext.HotelOrders
             .Join(_dbContext.BaseOrders, h => h.BaseOrderId, b => b.Id,
                (h, b) => new { HotelOrder = h, BaseOrder = b })
             .Where(x => x.BaseOrder.Id == input.BaseOrderId)
             .WhereIF(userType == UserType.Supplier, x => x.HotelOrder.PriceStrategySupplierId == operationUser.SupplierId.Value)
             .WhereIF(userType == UserType.Customer, x => x.BaseOrder.UserId == operationUser.UserId)
             .WhereIF(userType == UserType.Agency, x => x.BaseOrder.AgencyId == operationUser.AgencyId.Value)
             .FirstOrDefaultAsync();

        if (entity is null)
            throw new BusinessException(ErrorTypes.Order.OrderNotFind);
        //延时未实付订单 不能操作退款
        if (entity.BaseOrder.DelayedPayStatus == false)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        //当前订单已关联售后单，不可退款，需提交工单处理
        var hasOffset = await _dbContext.OffsetOrders
            .Where(x => x.BaseOrderId == input.BaseOrderId && x.OffsetType == OffsetOrderType.Receipt)
            .AnyAsync();
        if (hasOffset)
            throw new BusinessException(ErrorTypes.Order.OrderHasReceiptOffset);

        var hotelOrder = entity.HotelOrder;
        if (hotelOrder.IsGroupBooking is true)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        if (hotelOrder.Status == HotelOrderStatus.Refunding || hotelOrder.Status == HotelOrderStatus.Refunded)
            throw new BusinessException(ErrorTypes.Order.OrderCannotRefund);

        var baseOrder = entity.BaseOrder;
        //获取订单成本
        var calendarPrices = await _dbContext.HotelOrderCalendarPrices
               .AsNoTracking()
               .Where(a => a.HotelOrderId == entity.HotelOrder.Id)
               .ToListAsync();

        //计算退款成本
        var refundCost = 0m;
        var costCurrencyCode = calendarPrices.FirstOrDefault()?.CostCurrencyCode;
        switch (userType)
        {
            case UserType.Merchant:
            case UserType.Supplier:
                {
                    var refundableStatus = new HotelOrderStatus[] {
                        HotelOrderStatus.WaitingForConfirm,
                        HotelOrderStatus.WaitingForCheckIn,
                        HotelOrderStatus.CheckedIn,
                    };
                    if (refundableStatus.Contains(hotelOrder.Status) is false)
                    {
                        throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
                    }
                    //商家退款金额判断
                    if (input.RefundAmount > baseOrder.PaymentAmount)
                        throw new BusinessException(ErrorTypes.Order.RefundAmountInvalid);

                    var totalCost = calendarPrices.Sum(x => x.CostPrice) * entity.HotelOrder.PriceStrategyRoomsCount;
                    if (entity.BaseOrder.PaymentAmount == 0)
                        refundCost = totalCost;
                    else
                        refundCost = Math.Round(input.RefundAmount / entity.BaseOrder.PaymentAmount * totalCost, 2);
                    if (input.RefundCost.HasValue)
                    {
                        if (totalCost < input.RefundCost.Value)
                            throw new BusinessException(ErrorTypes.Order.RefundCostInvalid);
                        refundCost = input.RefundCost.Value;
                    }
                }
                break;
            default:
                {
                    var refundableStatus = new HotelOrderStatus[] {
                        HotelOrderStatus.WaitingForConfirm,
                        HotelOrderStatus.WaitingForCheckIn,
                    };
                    if (refundableStatus.Contains(hotelOrder.Status) is false)
                    {
                        throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
                    }

                    var calculateRefundDto = await CalculateRefundAmount(hotelOrder, baseOrder, calendarPrices);
                    if (calculateRefundDto.Refundable is not true)
                        throw new BusinessException(ErrorTypes.Order.OrderCannotRefund);
                    costCurrencyCode = calendarPrices.First().CostCurrencyCode;
                    if (calculateRefundDto.RefundAmount < 0)
                        throw new BusinessException(ErrorTypes.Order.RefundAmountInvalid);

                    input.RefundAmount = calculateRefundDto.RefundAmount;
                    refundCost = calculateRefundDto.Cost;
                }
                break;
        }

        var hotelApiOrder = await _dbContext.HotelApiOrders.IgnoreQueryFilters()
                      .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        OrderOperationType orderOperationType = OrderOperationType.RefundApplied;
        if (hotelOrder.SupplierApiType == SupplierApiType.GDS || hotelOrder.SupplierApiType == SupplierApiType.Youxia)
        {
            if (input.IsPassive != true &&
                (hotelApiOrder.Status == SupplierApiOrderStatus.WaitForConfirm
                  || hotelApiOrder.Status == SupplierApiOrderStatus.Confirmed)
                )
            {
                var cancelResult = await _hotelSupplierOrderDomainService.CancelBooking(new GDSCancelBookingMessage()
                {
                    BaseOrderId = baseOrder.Id,
                    SupplierApiType = hotelOrder.SupplierApiType,
                    TenantId = baseOrder.TenantId
                });
                if (cancelResult is false)
                    throw new BusinessException(ErrorTypes.Order.OrderCancelFail);
            }

            //修改订单状态,对于高定订单没有退款说法，都属于关闭
            hotelOrder.Status = HotelOrderStatus.Closed;
            hotelApiOrder.Status = SupplierApiOrderStatus.Closed;
            orderOperationType = OrderOperationType.Closed;
            baseOrder.Status = BaseOrderStatus.Closed;
            hotelOrder.UpdateTime = DateTime.Now;
            baseOrder.UpdateTime = DateTime.Now;
            hotelApiOrder.UpdateTime = DateTime.Now;

            var guests = await _dbContext.HotelOrderGuests.IgnoreQueryFilters()
                                   .Where(x => x.HotelOrderId == hotelOrder.Id)
                                   .Select(x => x.GuestName)
                                   .ToListAsync();
            //订单消息通知
            OrderNotifyDto<HotelRefundSucceededNotifyDto> orderNotifyDto = new()
            {
                BaseOrder = _mapper.Map<BaseOrderNotify>(baseOrder),
                NotifyDto = new HotelRefundSucceededNotifyDto
                {
                    HotelName = hotelOrder.HotelName,
                    HotelRoomName = hotelOrder.HotelRoomName,
                    CheckInDate = hotelOrder.CheckInDate,
                    CheckOutDate = hotelOrder.CheckOutDate,
                    PriceStrategyNumberOfBreakfast = hotelOrder.PriceStrategyNumberOfBreakfast,
                    PriceStrategySupplierId = hotelOrder.PriceStrategySupplierId,
                    PriceStrategyRoomsCount = hotelOrder.PriceStrategyRoomsCount,
                    Guests = guests.ToArray()!,
                    RefundAmount = baseOrder.TotalAmount,
                    BoardCount = hotelOrder.BoardCount,
                    BoardCodeType = hotelOrder.BoardCodeType
                }
            };
            await _messageNotifyService.HotelRefundSucceededNotify(orderNotifyDto);
        }
        else
        {
            //取消API酒店订单 && 非商户vebk操作
            if (hotelOrder.SupplierApiType != SupplierApiType.None && operationUser.UserType != UserType.Merchant)
            {
                var cancelResult = await _hotelApiOrderService.Cancel(new Contracts.Common.Order.DTOs.HotelApiOrder.HotelApiOrderCancelInput
                {
                    BaseOrderId = hotelOrder.BaseOrderId,
                    TenantId = hotelOrder.TenantId
                });
                if (cancelResult is false)
                    throw new BusinessException(ErrorTypes.Order.OrderCannotRefund);
            }

            var command = new RefundOrderApplyMessage
            {
                RefundOrderType = RefundOrderType.Hotel,
                TenantId = baseOrder.TenantId,
                BaseOrderId = baseOrder.Id,
                SubOrdeId = hotelOrder.Id,
                UserType = userType,
                UserId = operationUser.UserId,
                UserName = operationUser.Name,
                PaymentCurrencyCode = baseOrder.PaymentCurrencyCode,
                TotalAmount = input.RefundAmount,
                PostageAmount = 0,
                ProofImgs = input.Images is null ? "" : string.Join(",", input.Images),
                Quantity = hotelOrder.PriceStrategyRoomsCount,
                Reason = input.Message,
                HasReviewed = true,
                SupplierId = hotelOrder.PriceStrategySupplierId,
                Cost = refundCost,
                CostCurrencyCode = costCurrencyCode
            };

            //发起退款申请
            await _capPublisher.PublishAsync(CapTopics.Order.OrderRefundApply, command);
            //修改订单状态
            hotelOrder.Status = HotelOrderStatus.Refunding;
        }

        var orderCommission = await _dbContext.OrderCommission
          .Where(x => x.BaseOrderId == baseOrder.Id)
          .FirstOrDefaultAsync();
        if (orderCommission != null)
        {
            orderCommission.AgencyCommisionStatus = CommisionStatus.Cancel;
            orderCommission.SupplierCommisionStatus = CommisionStatus.Cancel;
        }

        //记录操作日志
        var logs = new OrderLogs
        {
            OperationType = orderOperationType,
            OrderLogType = OrderLogType.Refund,
            OperationRole = userType,
            OrderId = baseOrder.Id,
            UserId = operationUser.UserId,
            UserName = operationUser.Name
        };
        await _dbContext.AddAsync(logs);

        return new RefundHotelOrderOutput()
        {
            BaseOrderId = input.BaseOrderId,
            ChannelOrderNo = baseOrder.ChannelOrderNo,
            RefundAmount = input.RefundAmount
        };
    }

    public async Task<GetRefundableDto> GetRefundable(GetRefundableInput input)
    {
        long baseOrderId = input.BaseOrderId;
        OperationUserDto operationUser = input.OperationUser;
        var orderInfo = await _dbContext.HotelOrders
            .Join(_dbContext.BaseOrders, h => h.BaseOrderId, b => b.Id, (h, b) => new
            {
                HotelOrder = h,
                BaseOrder = b
            })
            .Where(x => x.BaseOrder.Id == baseOrderId)
            .WhereIF(operationUser.UserType == UserType.Customer, x => x.BaseOrder.UserId == operationUser.UserId)
            .WhereIF(operationUser.UserType == UserType.Agency, x => x.BaseOrder.AgencyId == operationUser.AgencyId.Value)
            .FirstOrDefaultAsync();
        if (orderInfo is null)
            throw new BusinessException(ErrorTypes.Order.OrderNotFind);

        var existsOffsetOrder = await _dbContext.OffsetOrders
           .AnyAsync(x => x.BaseOrderId == input.BaseOrderId);
        if (existsOffsetOrder)
            throw new BusinessException(ErrorTypes.Order.RefundRefunseByOffsetOrder);

        var order = orderInfo.HotelOrder;
        var baseOrder = orderInfo.BaseOrder;
        //微商城、B2B：待确认、待入住状态并且满足取消政策时间要求的，可申请退款
        var refundableStatus = new List<HotelOrderStatus> {
            HotelOrderStatus.WaitingForConfirm,
            HotelOrderStatus.WaitingForCheckIn
        };
        switch (operationUser.UserType)
        {
            case UserType.Merchant:
                refundableStatus.Add(HotelOrderStatus.CheckedIn);
                break;
        }
        if (refundableStatus.Contains(order.Status) is false)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        //获取订单成本
        var calendarPrices = await _dbContext.HotelOrderCalendarPrices
               .AsNoTracking()
               .Where(a => a.HotelOrderId == order.Id)
               .ToListAsync();

        var calculateRefundDto = await CalculateRefundAmount(order, baseOrder, calendarPrices);
        return new GetRefundableDto
        {
            PaymentType = baseOrder.PaymentType,
            PaymentAmount = baseOrder.PaymentAmount,
            PaymentCurrencyCode = baseOrder.PaymentCurrencyCode,
            CostCurrencyCode = calendarPrices.First().CostCurrencyCode,
            TotalCost = calendarPrices.Sum(x => x.CostPrice) * order.PriceStrategyRoomsCount,
            Cost = calculateRefundDto.Cost,
            RefundAmount = calculateRefundDto.Refundable ? calculateRefundDto.RefundAmount : 0m,
            Refundable = calculateRefundDto.Refundable
        };
    }

    /// <summary>
    /// 获取可退款金额
    /// </summary>
    public async Task<decimal> GetRefundAmount(long baseOrderId, OperationUserDto operationUser)
    {
        var orderInfo = await _dbContext.HotelOrders
            .Join(_dbContext.BaseOrders, h => h.BaseOrderId, b => b.Id, (h, b) => new
            {
                HotelOrder = h,
                BaseOrder = b
            })
            .Where(x => x.BaseOrder.Id == baseOrderId)
            .WhereIF(operationUser.UserType == UserType.Customer, x => x.BaseOrder.UserId == operationUser.UserId)
            .WhereIF(operationUser.UserType == UserType.Agency, x => x.BaseOrder.AgencyId == operationUser.AgencyId.Value)
            .FirstOrDefaultAsync();
        if (orderInfo is null)
            throw new BusinessException(ErrorTypes.Order.OrderNotFind);
        var order = orderInfo.HotelOrder;
        var baseOrder = orderInfo.BaseOrder;
        //微商城、B2B：待确认、待入住状态并且满足取消政策时间要求的，可申请退款
        var refundableStatus = new HotelOrderStatus[] { HotelOrderStatus.WaitingForConfirm, HotelOrderStatus.WaitingForCheckIn };
        if (refundableStatus.Contains(order.Status) is false)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }
        //获取订单成本
        var calendarPrices = await _dbContext.HotelOrderCalendarPrices
               .AsNoTracking()
               .Where(a => a.HotelOrderId == order.Id)
               .ToListAsync();
        var calculateRefundDto = await CalculateRefundAmount(order, baseOrder, calendarPrices);
        return calculateRefundDto.Refundable ? calculateRefundDto.RefundAmount : 0m;
    }

    /// <summary>
    /// 渠道订单号获取订单信息
    /// </summary>
    public async Task<BaseOrder> GetBaseOrderByChannel(long agencyId, long tenantId,
        SellingChannels channel, long baseOrderId, string channelOrderNo)
    {
        var baseOrder = await _dbContext.BaseOrders
            .IgnoreQueryFilters()
            .Where(x => x.SellingChannels == channel && x.AgencyId == agencyId)
            .WhereIF(tenantId > 0, x => x.TenantId == tenantId)
            .WhereIF(baseOrderId > 0, x => x.Id == baseOrderId)
            .WhereIF(string.IsNullOrWhiteSpace(channelOrderNo), x => x.ChannelOrderNo == channelOrderNo)
            .FirstOrDefaultAsync();

        if (baseOrder is null)
            throw new BusinessException(ErrorTypes.Order.OrderNotFind);

        return baseOrder;
    }

    /// <summary>
    /// 入住时间提醒
    /// </summary>
    /// <returns></returns>
    public async Task<List<long>> CheckInTimeReminder()
    {
        List<long> notifyHotelOrderIds = new();

        var threeDaysAfterTheDate = DateTime.Now.AddDays(3).Date;
        var sevenDaysAfterTheDate = DateTime.Now.AddDays(7).Date;
        var hotelOrderInfos = await _dbContext.HotelOrders.IgnoreQueryFilters()
                   .Join(_dbContext.BaseOrders, h => h.BaseOrderId, b => b.Id, (h, b) => new { h, b })
                   .Where(x => x.b.Status.Equals(BaseOrderStatus.UnFinished)
                   && (x.h.CheckInDate.Date.Equals(threeDaysAfterTheDate)
                   || x.h.CheckInDate.Date.Equals(sevenDaysAfterTheDate)))
                   .Select(x => new
                   {
                       HotelName = x.b.ResourceName,
                       RoomName = x.b.ProductName,
                       SkuName = x.b.ProductSkuName,
                       x.h.CheckInDate,
                       x.h.CheckOutDate,
                       BaseOrder = new BaseOrderNotify
                       {
                           UserId = x.b.UserId,
                           Id = x.b.Id,
                           SellingPlatform = x.b.SellingPlatform,
                           TenantId = x.b.TenantId
                       },
                       HotelOrderId = x.h.Id
                   })
               .ToListAsync();
        if (hotelOrderInfos is null || !hotelOrderInfos.Any()) return notifyHotelOrderIds;

        //提交前判断队列是否已存在该条记录
        string _hotelCheckInTimeReminderKey = $"openapi:order:hotelcheckin_time_reminder_{DateTime.Now.Date:yyyy-MM-dd}";
        var cacheHotelOrderIds = await _redisClient
            .ListRangeAsync<long>(_hotelCheckInTimeReminderKey);
        hotelOrderInfos = hotelOrderInfos
            .Where(x => !cacheHotelOrderIds.Contains(x.HotelOrderId))
            .ToList();
        if (!hotelOrderInfos.Any()) return notifyHotelOrderIds;

        //消息通知
        foreach (var item in hotelOrderInfos)
        {
            var orderNotifyDto = new OrderNotifyDto<HotelCheckInTimeReminderNotifyDto>()
            {
                BaseOrder = item.BaseOrder,
                NotifyDto = new HotelCheckInTimeReminderNotifyDto
                {
                    HotelName = item.HotelName,
                    RoomName = item.RoomName,
                    SkuName = item.SkuName,
                    CheckInDate = item.CheckInDate,
                    CheckOutDate = item.CheckOutDate
                }
            };
            await _messageNotifyService.HotelCheckInTimeReminderNotify(orderNotifyDto);
            notifyHotelOrderIds.Add(item.HotelOrderId);
            await _redisClient.ListLeftPushAsync(_hotelCheckInTimeReminderKey, item.HotelOrderId);
        }
        await _redisClient.KeyExpireAsync(_hotelCheckInTimeReminderKey, TimeSpan.FromDays(1));
        return notifyHotelOrderIds;
    }

    public async Task<IEnumerable<CountByGroupOutput>> CountByGroup(CountByGroupInput input)
    {
        var result = await _dbContext.HotelOrders.AsNoTracking()
            .Join(_dbContext.BaseOrders.AsNoTracking(), h => h.BaseOrderId, b => b.Id, (h, b) => new { h, b })
            .Where(x => x.b.OrderType == OrderType.Hotel)
            .WhereIF(input.UserId.HasValue, x => x.b.UserId == input.UserId.Value)
            .WhereIF(input.StartTime.HasValue, x => x.b.CreateTime >= input.StartTime.Value)
            .WhereIF(input.EndTime.HasValue, x => x.b.CreateTime <= input.EndTime.Value)
            .GroupBy(x => new { x.h.HotelId, x.h.HotelName, x.h.HotelEnName })
            .Select(x => new CountByGroupOutput
            {
                HotelId = x.Key.HotelId,
                HotelName = x.Key.HotelName,
                HotelEnName = x.Key.HotelEnName,
                Count = x.Count()
            })
            .OrderByDescending(x => x.Count)
            .PagingToListAsync(input.PageIndex, input.PageSize);

        return result;
    }

    public async Task<bool> UpdateSupplierOrderId(UpdateSupplierOrderIdInput input)
    {
        var baseOrder = await _dbContext.BaseOrders
           .Where(x => x.Id == input.BaseOrderId)
           .FirstOrDefaultAsync();

        var hotelOrder =
          await _dbContext.HotelOrders.FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);

        if (hotelOrder == null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        if (hotelOrder.SupplierApiType != SupplierApiType.None)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        if (!(baseOrder.Status == BaseOrderStatus.WaitingForPay
              || baseOrder.Status == BaseOrderStatus.UnFinished
             )
             )
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        if (!(hotelOrder.Status == HotelOrderStatus.WaitingForPay ||
            hotelOrder.Status == HotelOrderStatus.WaitingForConfirm ||
            hotelOrder.Status == HotelOrderStatus.WaitingForCheckIn ||
            hotelOrder.Status == HotelOrderStatus.CheckedIn
            ))
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        if (hotelOrder.PriceStrategySupplierId > 0)
        {
            var supplierInfo = await _httpClientFactory.InternalGetAsync<GetSupplierOutput>(
                requestUri: _servicesAddress.Tenant_GetSupplier(hotelOrder.PriceStrategySupplierId));
            if (supplierInfo.SupplierType == SupplierType.Api)
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        hotelOrder.SupplierOrderId = input.SupplierOrderId;

        var orderLog = new OrderLogs
        {
            OrderId = input.BaseOrderId,
            OperationRole = input.OpUser.UserType,
            OperationType = OrderOperationType.SupplierOrderIdUpdated,
            OrderLogType = OrderLogType.Hotel,
            UserId = input.OpUser.UserId,
            UserName = input.OpUser.Name,
        };
        await _dbContext.AddAsync(orderLog);
        await _dbContext.SaveChangesAsync();

        return true;
    }

    public async Task<List<GetHotelOrderCountOutput>> GetHotelOrderCount(GetHotelOrderCountInput input)
    {
        var beginDate = input.BeginDate ?? DateTime.Today.AddDays(-30);
        var endDate = input.EndDate ?? DateTime.Today.AddDays(1);
        var baseOrderStatus = new[] { BaseOrderStatus.UnFinished, BaseOrderStatus.Finished };

        var hotelOrderCountQuery = _dbContext.BaseOrders.AsNoTracking()
            .Join(_dbContext.HotelOrders.AsNoTracking(), b => b.Id,
                h => h.BaseOrderId,
                (b, h) => new { b, h })
            .Where(x => x.b.OrderType == OrderType.Hotel
                                                         && baseOrderStatus.Contains(x.b.Status)
                                                         && x.b.CreateTime >= beginDate
                                                         && x.b.CreateTime <= endDate);

        hotelOrderCountQuery = hotelOrderCountQuery
            .WhereIF(input.AgencyId.HasValue, x => x.b.AgencyId == input.AgencyId!.Value)
            .WhereIF(input.SellingPlatforms.Any(), x => input.SellingPlatforms.Contains(x.b.SellingPlatform))
            .WhereIF(input.HotelIds.Any(), x => input.HotelIds.Contains(x.b.Id));

        var hotelOrderCount = await hotelOrderCountQuery
            .Select(x => new
            {
                x.h.HotelId
            })
            .ToListAsync();

        var result = hotelOrderCount.GroupBy(x => x.HotelId)
            .Select(x => new GetHotelOrderCountOutput
            {
                HotelId = x.Key,
                OrderCount = x.Count()
            })
            .ToList();
        return result;
    }

    public async Task EditGroupNo(EditGroupNoInput input, OperationUserDto operationUser)
    {
        var order = await _dbContext.HotelOrders.AsNoTracking()
            .Where(x => x.Id == input.OrderId)
            .FirstOrDefaultAsync();
        if (order is null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        var entity = await _dbContext.BaseOrders
            .Where(x => x.Id == order.BaseOrderId)
            .FirstOrDefaultAsync();

        entity.GroupNo = input.GroupNo;
        entity.UpdateTime = DateTime.Now;

        //记录操作日志
        var logs = new OrderLogs
        {
            OperationType = OrderOperationType.EditHotelGroupNo,
            OrderLogType = OrderLogType.Hotel,
            OperationRole = operationUser.UserType,
            OrderId = entity.Id,
            UserId = operationUser.UserId,
            UserName = operationUser.Name
        };
        await _dbContext.AddAsync(logs);

        await _dbContext.SaveChangesAsync();
    }

    public async Task EditChannelOrderNo(EditChannelOrderNoInput input, OperationUserDto operationUser)
    {
        var order = await _dbContext.HotelOrders.AsNoTracking()
            .Where(x => x.Id == input.OrderId)
            .FirstOrDefaultAsync();
        if (order is null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        var entity = await _dbContext.BaseOrders
            .Where(x => x.Id == order.BaseOrderId)
            .FirstOrDefaultAsync();

        entity.ChannelOrderNo = input.ChannelOrderNo;
        entity.UpdateTime = DateTime.Now;

        //记录操作日志
        var logs = new OrderLogs
        {
            OperationType = OrderOperationType.EditHotelChannelOrderNo,
            OrderLogType = OrderLogType.Hotel,
            OperationRole = operationUser.UserType,
            OrderId = entity.Id,
            UserId = operationUser.UserId,
            UserName = operationUser.Name
        };
        await _dbContext.AddAsync(logs);

        await _dbContext.SaveChangesAsync();
    }

    #region Private

    private async Task<B2BWebConfigurationDto> TenantB2bWebConfigurationGet()
    {
        var response = await _httpClientFactory.InternalGetAsync<B2BWebConfigurationDto>(
            requestUri: _servicesAddress.Tenant_B2bWebConfiguration_Get());
        return response;
    }

    /// <summary>
    /// 计算可退款信息
    /// </summary>
    /// <param name="Refundable">是否可退</param>
    /// <param name="RefundAmount">退款金额</param>
    /// <param name="Cost">供应商退款采购价</param>
    /// <param name="CostCurrencyCode">采购币种</param>
    record CalculateRefundDto(bool Refundable, decimal RefundAmount = 0, decimal Cost = 0);

    /// <summary>
    /// 计算可退款金额
    /// </summary>
    private async Task<CalculateRefundDto> CalculateRefundAmount(HotelOrder order, BaseOrder baseOrder, List<HotelOrderCalendarPrice> calendarPrices)
    {
        //单条取消规则
        var cancelRule = await _dbContext.HotelOrderCancelRules
            .AsNoTracking()
            .Where(x => x.HotelOrderId == order.Id)
            .FirstOrDefaultAsync();

        if (cancelRule is null)//无取消规则
            return new CalculateRefundDto(false);
        var paymentAmount = baseOrder.PaymentAmount;
        //获取订单成本
        var totalCost = calendarPrices.Sum(x => x.CostPrice) * order.PriceStrategyRoomsCount;

        //未确认的订单支持无损取消
        if (order.Status == HotelOrderStatus.WaitingForConfirm)
            return new CalculateRefundDto(true, paymentAmount, totalCost);

        if (cancelRule.CancelRulesType == CancelRulesType.FreeCancel)
        {
            //免费取消
            if (DateTime.Now > order.CheckInDate.AddDays(1))
                return new CalculateRefundDto(false);

            return new CalculateRefundDto(true, paymentAmount, totalCost);
        }
        else if (cancelRule.CancelRulesType == CancelRulesType.LimitedTimeCancel)
        {
            var beforeCheckDayTime = order.CheckInDate.AddDays(-cancelRule.BeforeCheckInDays);
            if (cancelRule.BeforeCheckInTime.HasValue)
                beforeCheckDayTime = beforeCheckDayTime.Add(cancelRule.BeforeCheckInTime!.Value);

            //限时取消
            if (DateTime.Now > order.CheckInDate.Add(cancelRule.CheckInDateTime))
            {
                //不可取消
                return new CalculateRefundDto(false);
            }
            else if (DateTime.Now < beforeCheckDayTime)
            {
                //免费取消
                return new CalculateRefundDto(true, paymentAmount, totalCost);
            }
            else
            {
                //限时取消
                switch (cancelRule.CancelChargeType)
                {
                    case CancelChargeType.Nights:
                        {
                            var chargeNights = cancelRule.ChargeValue;
                            if (chargeNights >= order.PriceStrategyNightsCount)
                                return new CalculateRefundDto(true, 0, 0);

                            decimal amount = 0, cost = 0;
                            var rate = paymentAmount / baseOrder.TotalAmount;
                            calendarPrices.Take(cancelRule.ChargeValue).ToList()
                                .ForEach(a =>
                                    {
                                        var price = a.VipPrice ?? a.SalePrice;
                                        amount += Math.Floor(price * rate) * order.PriceStrategyRoomsCount;
                                        cost += a.CostPrice * order.PriceStrategyRoomsCount;
                                    });
                            return new CalculateRefundDto(true, amount, cost);
                        }
                    case CancelChargeType.Order:
                        {
                            var amount = Math.Floor(paymentAmount * (1 - cancelRule.ChargeValue / 100M));
                            var cost = decimal.Round(totalCost * (1 - cancelRule.ChargeValue / 100M), 2);
                            return new CalculateRefundDto(true, amount, cost);
                        }
                    default:
                        return new CalculateRefundDto(true, 0, 0);
                }
            }
        }
        else
        {
            //不可取消
            return new CalculateRefundDto(false);
        }
    }
    #endregion

}