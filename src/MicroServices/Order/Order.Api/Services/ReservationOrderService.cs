using AutoMapper;
using Cit.Storage.Redis;
using Common.GlobalException;
using Common.Jwt;
using Common.ServicesHttpClient;
using Contracts.Common.Inventory.Messages;
using Contracts.Common.Marketing.Enums;
using Contracts.Common.Marketing.Messages;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.MessageNotify;
using Contracts.Common.Order.DTOs.ReservationOrder;
using Contracts.Common.Order.DTOs.TicketOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.DTOs.SkuCalendarPrice;
using Contracts.Common.Product.Enums;
using Contracts.Common.Tenant.DTOs.Supplier;
using Contracts.Common.User.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Hangfire;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Order.Api.Extensions;
using Order.Api.Services.Interfaces;

namespace Order.Api.Services;

public class ReservationOrderService : IReservationOrderService
{
    private readonly CustomDbContext _dbContext;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IMapper _mapper;
    private readonly ICapPublisher _capPublisher;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ServicesAddress _servicesAddress;
    private readonly ILogger<ReservationOrderService> _logger;
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly IMediator _mediator;
    private readonly IMessageNotifyService _messageNotifyService;
    private readonly ICurrencyExchangeRateService _currencyExchangeRateService;
    private readonly IRedisClient _redisClient;

    public ReservationOrderService(
        CustomDbContext dbContext,
        IHttpContextAccessor httpContextAccessor,
        IMapper mapper,
        ICapPublisher capPublisher,
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> options,
        ILogger<ReservationOrderService> logger,
        IBackgroundJobClient backgroundJobClient,
        IMediator mediator,
        IMessageNotifyService messageNotifyService,
        ICurrencyExchangeRateService currencyExchangeRateService,
        IRedisClient redisClient)
    {
        _dbContext = dbContext;
        _httpContextAccessor = httpContextAccessor;
        _mapper = mapper;
        _capPublisher = capPublisher;
        _httpClientFactory = httpClientFactory;
        _servicesAddress = options.Value;
        _logger = logger;
        _backgroundJobClient = backgroundJobClient;
        _mediator = mediator;
        _messageNotifyService = messageNotifyService;
        _currencyExchangeRateService = currencyExchangeRateService;
        _redisClient = redisClient;
    }

    public async Task<PaymentInfoOutput> GetPaymentInfo(long orderId)
    {
        var result = await _dbContext.ReservationOrders
            .Join(_dbContext.BaseOrders,
                r => r.BaseOrderId, b => b.Id, (r, b) => new { b, r })
            .Where(s => s.r.Id == orderId)
            .Select(s => new PaymentInfoOutput
            {
                BaseOrderId = s.b.Id,
                ProductName = s.b.ProductName,
                ProductSkuName = s.b.ProductSkuName,
                ResourceName = s.b.ResourceName,
                OrderType = s.b.OrderType,
                CurrencyCode = s.r.PaymentCurrencyCode,
                Amount = s.r.PaymentAmount,
                UserId = s.b.UserId,
                UserNickName = s.b.UserNickName,
                AgencyName = s.b.AgencyName,
                AgencyId = s.b.AgencyId,
                SellingChannels = s.b.SellingChannels,
                PaymentType = s.b.PaymentType,
                SellingPlatform = s.b.SellingPlatform,
                OrderStatus = GetOrderStatus(s.r.Status),
                CreateTime = s.r.CreateTime,
                BaseOrderStatus = s.b.Status,
                CommissionFee = s.b.CommissionFee,
            })
            .FirstOrDefaultAsync();

        var ticketOrder = await _dbContext.TicketOrders
            .Where(s => s.BaseOrderId == result.BaseOrderId)
            .Select(x => new { x.Id, x.ProductTicketBusinessType })
            .FirstOrDefaultAsync();
        if (ticketOrder is not null)
        {
            result.ProductBusinessType = ticketOrder.ProductTicketBusinessType switch
            {
                TicketBusinessType.HotelPackages => ProductBusinessType.Ticket_HotelPackages,
                TicketBusinessType.RoomVoucher => ProductBusinessType.Ticket_RoomVoucher,
                TicketBusinessType.Catering => ProductBusinessType.Ticket_Catering,
                _ => null
            };
            result.SubOrderIds = new List<long> { ticketOrder.Id };
        }
        return result;
    }

    private static OrderStatus GetOrderStatus(ReservationStatus reservationStatus)
    {
        return reservationStatus switch
        {
            ReservationStatus.WaitingForPay => OrderStatus.WaitingForPay,
            ReservationStatus.Canceled => OrderStatus.Closed,
            _ => OrderStatus.Paid
        };
    }

    #region [订阅] 预约单支付成功

    [UnitOfWork]
    public async Task ReservationOrderPaySuccess(OrderStatusChangeByPaySuccessMessage receive)
    {
        var order = await _dbContext.ReservationOrders
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(s => s.Id == receive.OrderId);

        //订单非待支付状态 订单无效支付退款 (如订单关闭、重复支付等场景)
        if (order.Status != ReservationStatus.WaitingForPay)
        {
            //throw new BusinessException($"预约单支付成功处理，预约单状态不符，当前OrderStatus：{order.Status}");
            await _capPublisher.PublishDelayAsync(TimeSpan.FromSeconds(10),//延时10秒处理，避免订单状态未更新
            CapTopics.Payment.OrderPaymentUselessRefund, new Contracts.Common.Payment.Messages.OrderPaymentUselessRefundMessage
            {
                OrderPaymentId = receive.OrderPaymentId,
                OrderPaymentType = receive.OrderPaymentType,
                OrderId = receive.OrderId,
            });
            return;
        }

        var baseOrder = await _dbContext.BaseOrders.AsNoTracking()
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.Id == order.BaseOrderId);

        #region 预约单信息

        var ticketOrder = await _dbContext.TicketOrders
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.Id == order.TicketOrderId);

        order.Status = ticketOrder.ProductNeedConfirmReservation
                ? ReservationStatus.WaitingForClaim
                : ReservationStatus.Confirmed;
        order.UpdateTime = DateTime.Now;
        order.PaymentType = receive.PaymentType;
        order.PaymentChannel = receive.PaymentChannel;
        order.PaymentMode = receive.PaymentMode;
        order.PaymentExternalNo = receive.PaymentExternalNo;

        #endregion

        #region 券码状态

        var ticketCodes = await _dbContext.TicketCodes.IgnoreQueryFilters()
            .Join(_dbContext.ReservationOrderTicketCodes.IgnoreQueryFilters(),
                t => t.Id,
                rt => rt.TicketCodeId,
                (t, rt) => new
                {
                    t,
                    rt
                })
            .Where(s => s.rt.ReservationOrderId == receive.OrderId)
            .ToListAsync();

        if (ticketCodes.Count < 1 || !ticketCodes.All(s => s.t.Status == TicketCodeStatus.Reservation))
            throw new BusinessException($"预约单支付成功处理，券码状态不符，{JsonConvert.SerializeObject(ticketCodes)}");
        foreach (var item in ticketCodes)
        {
            //修改券码状态
            item.t.Status = TicketCodeStatus.WaitingForUse;
            item.t.UpdateTime = DateTime.Now;
            //修改预约单券码状态
            item.rt.Status = ReservationTicketCodeStatus.WaitingForUse;
            item.rt.UpdateTime = DateTime.Now;
        }

        #endregion

        #region 日志

        //支付成功日志
        var orderPayLog = new OrderLogs
        {
            OrderId = receive.OrderId,
            OperationRole = receive.PaymentType switch
            {
                PayType.YeePay => Contracts.Common.Order.Enums.UserType.Customer,
                PayType.Offline => Contracts.Common.Order.Enums.UserType.Merchant,
                _ => Contracts.Common.Order.Enums.UserType.None
            },
            OperationType = OrderOperationType.Paid,
            OrderLogType = OrderLogType.Reservation,
            UserId = order.ClaimantId,
            UserName = order.ClaimantName,
        };
        await _dbContext.AddAsync(orderPayLog);

        //认领日志
        if (order.ClaimantId > 0)
        {
            var orderClaimantLog = new OrderLogs
            {
                OrderId = receive.OrderId,
                OperationRole = Contracts.Common.Order.Enums.UserType.Merchant,
                OperationType = OrderOperationType.Claimed,
                OrderLogType = OrderLogType.Reservation,
                UserId = order.ClaimantId,
                UserName = order.ClaimantName,
            };
            await _dbContext.AddAsync(orderClaimantLog);
        }

        #endregion

        #region 库存扣减

        //发起预约 消息通知
        if (order.Status == ReservationStatus.Confirmed)
        {

            var ticketOrderTravelers = await _dbContext.TicketOrderTravelers
                .IgnoreQueryFilters()
                .Where(x => x.TicketOrderId == ticketOrder.Id && x.ReservationOrderId == order.Id)
                .Select(x => x.Name)
                .ToListAsync();

            await _messageNotifyService.TicketReservationNotify(new OrderNotifyDto<TicketReservationNotifyDto>
            {
                BaseOrder = _mapper.Map<BaseOrderNotify>(baseOrder),
                NotifyDto = new TicketReservationNotifyDto
                {
                    ProductName = ticketOrder.ProductTitle,
                    SkuName = ticketOrder.SkuName,
                    SupplierId = ticketOrder.ProductSupplierId,
                    TravelDateBegin = order.TravelDateBegin,
                    TravelDateEnd = order.TravelDateEnd,
                    Traveler = ticketOrderTravelers?.ToArray() ?? Array.Empty<string>(),
                    Amount = order.PaymentAmount,
                    Nights = ticketOrder.SkuNumberOfNights,
                    Quantity = ticketOrder.Quantity
                }
            });
        }
        await _capPublisher.PublishAsync(CapTopics.Inventory.DeductInventory,
            new DeductInventoryMessage { TenantId = order.TenantId, OrderId = receive.OrderId });
        _dbContext.SetTenantId(order.TenantId);

        #endregion

        #region 自动确认

        if (ticketOrder.ProductNeedConfirmReservation)
        {
            return;
        }
        ReservationOrder reservationOrder = await _dbContext.ReservationOrders
                .IgnoreQueryFilters()
                .Where(x => x.TicketOrderId == ticketOrder.Id)
                .FirstAsync();
        var afterSalePhone = "";
        if (ticketOrder is not null)
        {
            var productSupportStaff = await _mediator.Send(new Requests.ProductSupportStaffRequest
            {
                TenantId = ticketOrder.TenantId,
                ProductType = ProductType.Ticket,
                ProductId = ticketOrder.ProductId
            });
            afterSalePhone = productSupportStaff?.AfterSalePhone;
        }
        OrderNotifyDto<TicketReservationConfirmNotifyDto> orderConfirmNotifyDto = new()
        {
            BaseOrder = _mapper.Map<BaseOrderNotify>(baseOrder),
            NotifyDto = new TicketReservationConfirmNotifyDto
            {
                ProductTicketBusinessType = ticketOrder.ProductTicketBusinessType,
                ProductNeedReservation = ticketOrder.ProductNeedReservation,
                ProductNeedWriteOff = ticketOrder.ProductNeedWriteOff,
                ResourceName = reservationOrder.ResourceName,
                ProductName = baseOrder.ProductName,
                SkuName = baseOrder.ProductSkuName,
                Quantity = reservationOrder.Quantity,
                TravelDateBegin = reservationOrder.TravelDateBegin,
                TravelDateEnd = reservationOrder.TravelDateEnd,
                TicketSaleType = ticketOrder.TicketSaleType,
                AfterSalePhone = afterSalePhone,
                Traveler = reservationOrder.Traveler,
                Amount = reservationOrder.PaymentAmount,
                Nights = ticketOrder.SkuNumberOfNights
            }
        };
        await _messageNotifyService.TicketReservationConfirmNotify(orderConfirmNotifyDto);

        #endregion

        #region 上送跟踪日志

        //上送跟踪日志
        var shareInfo = await _dbContext.OrderShareInfos.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == baseOrder.Id);
        if (shareInfo?.TraceId is > 0)
        {
            await _capPublisher.PublishAsync(CapTopics.Marketing.PushPromotionTraceRecord,
                new PushPromotionTraceRecordMessage
                {
                    PromotionTraceId = shareInfo.TraceId.Value,
                    CustomerId = shareInfo.BuyerId,
                    BehaviorType = TraceBehaviorType.Payment,
                    OrderType = baseOrder.OrderType,
                    OrderId = order.Id,
                    OrderAmount = order.PaymentAmount,
                    IsReservationOrder = true,
                    VisitTargetName = reservationOrder.ResourceName
                });
        }

        #endregion
    }

    #endregion

    #region 待支付订单取消

    /// <summary>
    /// 预约取消支付
    /// </summary>
    [UnitOfWork]
    public async Task MallCancelPay(long reservationOrderId)
    {
        var user = _httpContextAccessor.HttpContext.GetCurrentUser();
        var reservationOrder = await _dbContext.ReservationOrders
            .Where(x => x.Id == reservationOrderId)
            .FirstOrDefaultAsync();

        //是否为当前用户订单
        await IsMyOrder(reservationOrder.BaseOrderId, user.userid);

        await Closed(reservationOrder);
    }

    /// <summary>
    /// 关闭超时支付预约单
    /// </summary>
    [UnitOfWork]
    public async Task CloseTimeOut(long reservationOrderId)
    {
        var reservationOrder = await _dbContext.ReservationOrders
            .Where(x => x.Id == reservationOrderId)
            .FirstOrDefaultAsync();

        await Closed(reservationOrder);
    }

    public async Task Closed(ReservationOrder reservationOrder)
    {
        if (reservationOrder.Status != ReservationStatus.WaitingForPay)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        reservationOrder.Status = ReservationStatus.Canceled;
        reservationOrder.UpdateTime = DateTime.Now;

        //获取券码
        var ticketCodes = await _dbContext.ReservationOrderTicketCodes
           .Join(_dbContext.TicketCodes,
               x => x.TicketCodeId, y => y.Id, (x, y) => new { rt = x, t = y })
           .Where(x => x.rt.ReservationOrderId == reservationOrder.Id)
           .ToListAsync();

        ticketCodes.ForEach(x =>
        {
            //修改券码状态 ->待预约
            x.t.Status = TicketCodeStatus.WaitingForReservation;
            x.t.UpdateTime = DateTime.Now;
            //修改预约单券码状态 ->已取消
            x.rt.Status = ReservationTicketCodeStatus.Cancel;
            x.rt.UpdateTime = DateTime.Now;
        });

        //取消日历房冻结库存
        var unfrozenInventoryCommand = new UnfrozenInventoryMessage
        {
            TenantId = _httpContextAccessor.HttpContext.GetTenantId(),
            OrderId = reservationOrder.Id
        };

        await _capPublisher.PublishAsync(CapTopics.Inventory.UnfrozenInventory, unfrozenInventoryCommand);
    }

    #endregion

    #region Detail

    public async Task<ReservationOrderDetailOutput> Detail(ReservationOrderDetailInput input)
    {
        var reservationOrder = await _dbContext.ReservationOrders
          .Where(x => x.Id == input.ReservationOrderId)
          .Select(x => new ReservationOrderOutput
          {
              Id = x.Id,
              TicketOrderId = x.TicketOrderId,
              ResourceName = x.ResourceName,
              PaymentAmount = x.PaymentAmount,
              PaymentCurrencyCode = x.PaymentCurrencyCode,
              TravelDateBegin = x.TravelDateBegin,
              TravelDateEnd = x.TravelDateEnd,
              Status = x.Status,
              Quantity = x.Quantity,
              CreateTime = x.CreateTime,
              Message = x.Message,
              ConfirmNumber = x.ConfirmNumber,
              ClaimantName = x.ClaimantName,
              IsSendPDF = x.IsSendPDF,
              Traveler = x.Traveler
          })
          .FirstOrDefaultAsync();

        //获取出行人信息
        reservationOrder.TicketOrderTravelers = await _dbContext.TicketOrderTravelers.AsNoTracking()
            .Where(x => x.TicketOrderId.Equals(reservationOrder.TicketOrderId))
            .Where(x => x.ReservationOrderId.Equals(reservationOrder.Id))
            .Select(x => new TicketOrderTravelerDto
            {
                Name = x.Name,
                IDCard = x.IDCard,
                PhoneNumber = x.PhoneNumber,
                ReservationOrderId = x.ReservationOrderId
            }).ToListAsync();

        var ticketOrder = await _dbContext.TicketOrders
            .Join(_dbContext.OrderPrices, x => x.Id, y => y.SubOrderId, (x, y) => new
            {
                TicketOrder = x,
                OrderPrice = y
            })
            .Where(x => x.TicketOrder.Id == reservationOrder.TicketOrderId)
            .WhereIF(input.SupplierId.HasValue, x => x.TicketOrder.ProductSupplierId == input.SupplierId.Value)
            .Select(x => new TicketOrderOutput
            {
                Id = x.TicketOrder.Id,
                BaseOrderId = x.TicketOrder.BaseOrderId,
                TicketSaleType = x.TicketOrder.TicketSaleType,
                ProductTicketBusinessType = x.TicketOrder.ProductTicketBusinessType,
                ProductId = x.TicketOrder.ProductId,
                ProductTitle = x.TicketOrder.ProductTitle,
                SkuId = x.TicketOrder.SkuId,
                SkuName = x.TicketOrder.SkuName,
                ProductSupplierId = x.TicketOrder.ProductSupplierId,
                SkuNumberOfNights = x.TicketOrder.SkuNumberOfNights,
                SkuValidityBegin = x.TicketOrder.SkuValidityBegin,
                SkuValidityEnd = x.TicketOrder.SkuValidityEnd,
                MultPrice = new MultPriceOutput
                {
                    OrgPriceCurrencyCode = x.OrderPrice.OrgPriceCurrencyCode,
                    OrgPrice = x.OrderPrice.OrgPrice,
                    PriceType = x.OrderPrice.PriceType,
                    PaymentCurrencyCode = x.OrderPrice.PaymentCurrencyCode,
                    Price = x.OrderPrice.Price,
                    CostCurrencyCode = x.OrderPrice.CostCurrencyCode,
                    OrgCostPrice = x.OrderPrice.OrgCostPrice,
                    CostPrice = x.OrderPrice.CostPrice,
                    CostPriceType = x.OrderPrice.CostPriceType,
                    Quantity = x.OrderPrice.Quantity
                }
            })
            .FirstOrDefaultAsync();
        //包含晚数 结束日期更新为离店日期
        if (ticketOrder.SkuNumberOfNights > 0)
        {
            reservationOrder.TravelDateEnd = reservationOrder.TravelDateBegin.AddDays(ticketOrder.SkuNumberOfNights);
        }
        var baseOrder = await _dbContext.BaseOrders
            .Where(x => x.Id == ticketOrder.BaseOrderId)
            .Select(x => new BaseOrderOutput
            {
                Id = x.Id,
                ContactsName = x.ContactsName,
                ContactsPhoneNumber = x.ContactsPhoneNumber,
                ContactsEmail = x.ContactsEmail,
                AgencyId = x.AgencyId,
                AgencyName = x.AgencyName,
                UserId = x.UserId,
                UserNickName = x.UserNickName,
                VipLevelId = x.VipLevelId,
                VipLevelName = x.VipLevelName,
                SellingPlatform = x.SellingPlatform,
            })
            .FirstOrDefaultAsync();
        return new ReservationOrderDetailOutput
        {
            BaseOrder = baseOrder,
            TicketOrder = ticketOrder,
            ReservationOrder = reservationOrder
        };
    }

    public async Task<IEnumerable<ReservationOrderPaymentOutput>> GetReservationOrderPayments(long reservationOrderId)
    {
        var paymentInfos = await _dbContext.ReservationOrderPayments.AsNoTracking()
             .Where(x => x.ReservationOrderId == reservationOrderId)
             .Select(x => new ReservationOrderPaymentOutput
             {
                 Date = x.Date,
                 PaymentCurrencyCode = x.PaymentCurrencyCode,
                 Amount = x.Amount,
                 CostCurrencyCode = x.CostCurrencyCode,
                 Cost = x.Cost
             })
             .ToListAsync();
        return paymentInfos;
    }

    public async Task<IEnumerable<ReservationOrderTicketCodeOutput>> GetReservationOrderTicketCodes(long reservationOrderId)
    {
        var reservationOrderTicketCodes = await _dbContext.ReservationOrderTicketCodes
           .Where(x => x.ReservationOrderId == reservationOrderId)
           .Select(x => new ReservationOrderTicketCodeOutput
           {
               ReservationOrderId = x.ReservationOrderId,
               TicketCodeId = x.TicketCodeId,
               Code = x.Code,
               Status = x.Status,
               UpdateTime = x.UpdateTime
           })
           .ToListAsync();
        return reservationOrderTicketCodes;
    }

    #endregion

    #region Vebk

    /// <summary>
    /// 获取预约单
    /// </summary>
    public async Task<GetReservationOrderOutput> Get(ReservationOrderDetailInput input)
    {
        var detailOutput = await Detail(input);
        var payments = await GetReservationOrderPayments(input.ReservationOrderId);
        var codes = await GetReservationOrderTicketCodes(input.ReservationOrderId);
        GetReservationOrder_Detail detail = new()
        {
            Id = detailOutput.ReservationOrder.Id,
            ClaimantName = detailOutput.ReservationOrder.ClaimantName,
            ConfirmNumber = detailOutput.ReservationOrder.ConfirmNumber,
            CreateTime = detailOutput.ReservationOrder.CreateTime,
            IsSendPDF = detailOutput.ReservationOrder.IsSendPDF,
            Message = detailOutput.ReservationOrder.Message,
            PaymentAmount = detailOutput.ReservationOrder.PaymentAmount,
            PaymentCurrencyCode = detailOutput.ReservationOrder.PaymentCurrencyCode,
            Quantity = detailOutput.ReservationOrder.Quantity,
            ResourceName = detailOutput.ReservationOrder.ResourceName,
            Status = detailOutput.ReservationOrder.Status,
            TravelDateBegin = detailOutput.ReservationOrder.TravelDateBegin,
            TravelDateEnd = detailOutput.ReservationOrder.TravelDateEnd,
            TicketOrderTravelers = detailOutput.ReservationOrder.TicketOrderTravelers,
            TravelDateCount = detailOutput.ReservationOrder.TravelDateCount,
            PaymentInfos = payments,
            TicketCodes = string.Join(",", codes.Select(x => x.Code)),
            ReservationTicketCodes = codes.GroupBy(x => x.Status)
            .Select(x => new ReservationTicketCode()
            {
                Status = x.Key,
                Codes = x.Select(w => new TicketCodeDetail()
                {
                    Code = w.Code,
                    UpdateTime = w.UpdateTime
                })
            })
        };
        var multPrice = detailOutput.TicketOrder.MultPrice;
        GetReservationOrder_TicketOrderInfo ticketOrderInfo = new GetReservationOrder_TicketOrderInfo
        {
            AgencyId = detailOutput.BaseOrder.AgencyId,
            AgencyName = detailOutput.BaseOrder.AgencyName,
            UserId = detailOutput.BaseOrder.UserId,
            UserNickName = detailOutput.BaseOrder.UserNickName,
            VipLevelId = detailOutput.BaseOrder.VipLevelId,
            VipLevelName = detailOutput.BaseOrder.VipLevelName,
            BaseOrderId = detailOutput.TicketOrder.BaseOrderId,
            ContactsEmail = detailOutput.BaseOrder.ContactsEmail,
            ContactsName = detailOutput.BaseOrder.ContactsName,
            ContactsPhoneNumber = detailOutput.BaseOrder.ContactsPhoneNumber,
            ProductId = detailOutput.TicketOrder.ProductId,
            ProductName = detailOutput.TicketOrder.ProductTitle,
            ProductSkuId = detailOutput.TicketOrder.SkuId,
            ProductSkuName = detailOutput.TicketOrder.SkuName,
            SellingPlatform = detailOutput.BaseOrder.SellingPlatform,
            CostCurrencyCode = multPrice.CostCurrencyCode,
            SkuCostPrice = multPrice.CostPrice!.Value,
            PaymentCurrencyCode = multPrice.PaymentCurrencyCode,
            SkuSellingPrice = multPrice.Price!.Value,
            SkuNumberOfNights = detailOutput.TicketOrder.SkuNumberOfNights,
            ProductTicketBusinessType = detailOutput.TicketOrder.ProductTicketBusinessType,
            TicketOrderId = detailOutput.TicketOrder.Id,
        };
        if (detailOutput.TicketOrder.ProductSupplierId > 0)
        {
            var supplier = await _httpClientFactory.InternalGetAsync<GetSupplierOutput>(
                requestUri: _servicesAddress.Tenant_GetSupplier(detailOutput.TicketOrder.ProductSupplierId));
            ticketOrderInfo.SupplierName = supplier?.FullName ?? "";
            ticketOrderInfo.SupplierPhone = supplier?.ContactPhone ?? "";
            ticketOrderInfo.SupplierEmail = supplier?.ContactEmail ?? "";
        }
        var output = new GetReservationOrderOutput()
        {
            Detail = detail,
            TicketOrderInfo = ticketOrderInfo
        };
        return output;
    }

    /// <summary>
    /// 标记发单
    /// </summary>
    public async Task SetSendTag(long reservationOrderId)
    {
        var reservationOrder = await _dbContext.ReservationOrders
            .Where(r => r.Id == reservationOrderId)
            .FirstOrDefaultAsync();
        reservationOrder.IsSendPDF = true;
        reservationOrder.UpdateTime = DateTime.Now;

        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 认领
    /// </summary>
    public async Task Claimant(ClaimantInput input, OperationUserDto operationUser)
    {
        var reservationOrder = await _dbContext.ReservationOrders
            .Where(x => x.Id == input.ReservationOrderId)
            .FirstOrDefaultAsync();

        if (reservationOrder.Status != ReservationStatus.WaitingForClaim)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        reservationOrder.ClaimantId = operationUser.UserId;
        reservationOrder.ClaimantName = operationUser.Name;
        reservationOrder.Status = ReservationStatus.WaitingForConfirm;
        reservationOrder.UpdateTime = DateTime.Now;

        //认领预约单日志
        var claimantLog = new OrderLogs
        {
            OrderId = input.ReservationOrderId,
            OperationRole = operationUser.UserType,
            OperationType = OrderOperationType.Claimed,
            OrderLogType = OrderLogType.Reservation,
            UserId = operationUser.UserId,
            UserName = operationUser.Name,
        };
        await _dbContext.AddAsync(claimantLog);

        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 确认订单
    /// </summary>
    [UnitOfWork]
    public async Task Confirm(ConfirmReservationOrderInput input, OperationUserDto operationUser)
    {
        var reservationOrder = await _dbContext.ReservationOrders
            .Where(x => x.Id == input.ReservationOrderId)
            .FirstOrDefaultAsync();

        if (reservationOrder.Status != ReservationStatus.WaitingForConfirm)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        reservationOrder.ConfirmNumber = input.ConfirmNumber;
        reservationOrder.Status = ReservationStatus.Confirmed;
        reservationOrder.UpdateTime = DateTime.Now;

        //确认预约单日志
        var confirmLog = new OrderLogs
        {
            OrderId = input.ReservationOrderId,
            OperationRole = operationUser.UserType,
            OperationType = OrderOperationType.Confirmed,
            OrderLogType = OrderLogType.Reservation,
            UserId = operationUser.UserId,
            UserName = operationUser.Name,
        };
        await _dbContext.AddAsync(confirmLog);

        var baseOrder = await _dbContext.BaseOrders
            .Where(o => o.Id == reservationOrder.BaseOrderId)
            .FirstOrDefaultAsync();
        var ticketOrder = await _dbContext.TicketOrders
            .Where(t => t.Id == reservationOrder.TicketOrderId)
            .WhereIF(operationUser.UserType == Contracts.Common.Order.Enums.UserType.Supplier, x => x.ProductSupplierId == operationUser.SupplierId)
            .Select(t => new { t.TenantId, t.ProductId, t.SkuNumberOfNights, t.TicketSaleType, t.ProductNeedReservation, t.ProductNeedWriteOff, t.ProductTicketBusinessType })
        .FirstOrDefaultAsync();


        var afterSalePhone = "";
        if (ticketOrder is not null)
        {
            var productSupportStaff = await _mediator.Send(new Requests.ProductSupportStaffRequest
            {
                TenantId = ticketOrder.TenantId,
                ProductType = ProductType.Ticket,
                ProductId = ticketOrder.ProductId
            });
            afterSalePhone = productSupportStaff?.AfterSalePhone;
        }
        OrderNotifyDto<TicketReservationConfirmNotifyDto> orderNotifyDto = new()
        {
            BaseOrder = _mapper.Map<BaseOrderNotify>(baseOrder),
            NotifyDto = new TicketReservationConfirmNotifyDto
            {
                ProductTicketBusinessType = ticketOrder.ProductTicketBusinessType,
                ProductNeedReservation = ticketOrder.ProductNeedReservation,
                ProductNeedWriteOff = ticketOrder.ProductNeedWriteOff,
                ResourceName = reservationOrder.ResourceName,
                ProductName = baseOrder.ProductName,
                SkuName = baseOrder.ProductSkuName,
                Quantity = reservationOrder.Quantity,
                TravelDateBegin = reservationOrder.TravelDateBegin,
                TravelDateEnd = reservationOrder.TravelDateEnd,
                TicketSaleType = ticketOrder.TicketSaleType,
                AfterSalePhone = afterSalePhone,
                Traveler = reservationOrder.Traveler,
                Amount = reservationOrder.PaymentAmount,
                Nights = ticketOrder.SkuNumberOfNights
            }
        };

        await _messageNotifyService.TicketReservationConfirmNotify(orderNotifyDto);
    }

    /// <summary>
    /// 修改确认号
    /// </summary>
    public async Task UpdateConfirmNumber(UpdateReservationOrderConfirmNumberInput input)
    {
        var reservationOrder = await _dbContext.ReservationOrders
            .Where(r => r.Id == input.ReservationOrderId)
            .FirstOrDefaultAsync();

        reservationOrder.ConfirmNumber = input.ConfirmNumber;
        reservationOrder.UpdateTime = DateTime.Now;

        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 搜索预约单
    /// </summary>
    public async Task<PagingModel<ReservationOrderInfo, ReservationOrderStatDto>> Search(SearchReservationOrderInput input)
    {
        ReservationOrderQueryInput queryInput = new()
        {
            SearchType = input.SearchType,
            KeyWord = input.KeyWord,
            TicketBusinessType = input.TicketBusinessType,
            TravelDateBegin_Begin = input.TravelDateBegin_Begin,
            TravelDateBegin_End = input.TravelDateBegin_End,
            TravelDateEnd_Begin = input.TravelDateEnd_Begin,
            TravelDateEnd_End = input.TravelDateEnd_End,
            SupplierId = input.SupplierId,
            MyClaimant = input.MyClaimant,
        };
        //券码搜索
        if (input.SearchType == ReservationOrderSearchType.TicketCode && !string.IsNullOrWhiteSpace(input.KeyWord))
        {
            _ = long.TryParse(input.KeyWord, out var ticketCode);

            queryInput.ReservationOrderId = (await GetReservationOrderByTicketCode(ticketCode))?.ReservationOrderId ?? 0;
        }
        var query = _dbContext.ReservationOrders
            .Join(_dbContext.TicketOrders,
                x => x.TicketOrderId, y => y.Id, (x, y) => new { rOrder = x, tOrder = y })
            .Join(_dbContext.BaseOrders,
                x => x.rOrder.BaseOrderId, y => y.Id, (x, y) => new
                {
                    x.rOrder.Id,
                    x.rOrder.Status,
                    x.rOrder.Quantity,
                    x.rOrder.TravelDateBegin,
                    TravelDateEnd = x.rOrder.TravelDateBegin.AddDays(x.tOrder.SkuNumberOfNights),
                    x.rOrder.ClaimantId,
                    x.rOrder.ConfirmNumber,
                    x.rOrder.IsSendPDF,
                    x.rOrder.UpdateTime,

                    x.tOrder.ProductSupplierId,
                    x.tOrder.TicketSaleType,
                    x.tOrder.ProductTicketBusinessType,
                    x.tOrder.ProductTitle,
                    x.tOrder.SkuName,
                    x.tOrder.SkuNumberOfNights,

                    BaseOrderId = y.Id,
                    y.SellingPlatform,
                    y.ContactsName,
                    y.ContactsPhoneNumber
                })
            .Where(x => x.TicketSaleType == TicketSaleType.GroupPurchase)//团购 - 预约
            .WhereIF(queryInput.Status.HasValue, x => x.Status != queryInput.Status.Value)
            .WhereIF(queryInput.TravelDateBegin_Begin.HasValue, x => x.TravelDateBegin >= queryInput.TravelDateBegin_Begin.Value)
            .WhereIF(queryInput.TravelDateBegin_End.HasValue, x => x.TravelDateBegin <= queryInput.TravelDateBegin_End.Value)
            .WhereIF(queryInput.TravelDateEnd_Begin.HasValue, x => x.TravelDateEnd >= queryInput.TravelDateEnd_Begin.Value)
            .WhereIF(queryInput.TravelDateEnd_End.HasValue, x => x.TravelDateEnd <= queryInput.TravelDateEnd_End.Value)
            .WhereIF(queryInput.SupplierId > 0, x => x.ProductSupplierId == queryInput.SupplierId)
            .WhereIF(queryInput.TicketBusinessType != TicketBusinessType.None, x => x.ProductTicketBusinessType == queryInput.TicketBusinessType)
            .WhereIF(queryInput.MyClaimant, x => x.ClaimantId == _httpContextAccessor.HttpContext.GetCurrentUser().userid)
            .WhereIF(queryInput.ReservationOrderId.HasValue, x => x.Id == queryInput.ReservationOrderId.Value);
        //关键字搜索
        if (!string.IsNullOrWhiteSpace(queryInput.KeyWord))
        {
            var keyword = queryInput.KeyWord.Trim();
            switch (queryInput.SearchType)
            {
                case ReservationOrderSearchType.OrderId:
                    _ = long.TryParse(keyword, out var orderId);
                    query = query.Where(x => x.BaseOrderId == orderId);
                    break;
                case ReservationOrderSearchType.ContactsName:
                    query = query.Where(x => x.ContactsName.Contains(keyword));
                    break;
                case ReservationOrderSearchType.ContactsPhoneNumber:
                    query = query.Where(x => x.ContactsPhoneNumber.Contains(keyword));
                    break;
                case ReservationOrderSearchType.ProductName:
                    query = query.Where(x => x.ProductTitle.Contains(keyword));
                    break;
                case ReservationOrderSearchType.ConfirmNumber:
                    query = query.Where(x => x.ConfirmNumber.Contains(keyword));
                    break;
            }
        }

        var pagingModel = await query
            .WhereIF(input.Status.HasValue, x => x.Status == input.Status!.Value)
            .WhereIF(input.Id.HasValue, x => x.Id == input.Id!.Value)
            .OrderByDescending(x => x.UpdateTime)
            .ThenByDescending(x => x.Id)
            .PagingAsync(input.PageIndex, input.PageSize, x => new ReservationOrderInfo()
            {
                Id = x.Id,
                Status = x.Status,
                SellingPlatform = x.SellingPlatform,
                ProductName = x.ProductTitle,
                ProductSkuName = x.SkuName,
                ProductTicketBusinessType = x.ProductTicketBusinessType,
                IsSendPDF = x.IsSendPDF,
                SkuNumberOfNights = x.SkuNumberOfNights,
                TravelDateBegin = x.TravelDateBegin,
                TravelDateEnd = x.TravelDateEnd,
                Quantity = x.Quantity,
                ContactsName = x.ContactsName,
                ContactsPhoneNumber = x.ContactsPhoneNumber
            });

        var statStatus = new[] { ReservationStatus.WaitingForClaim, ReservationStatus.WaitingForConfirm };
        var stats = await query.Where(x => statStatus.Contains(x.Status))
            .GroupBy(x => x.Status)
            .Select(x => new { Status = x.Key, Count = x.Count() })
            .ToListAsync();
        var statDto = new ReservationOrderStatDto()
        {
            WaitingForClaimCount = stats.FirstOrDefault(x => x.Status == ReservationStatus.WaitingForClaim)?.Count ?? 0,
            WaitingForConfirmCount = stats.FirstOrDefault(x => x.Status == ReservationStatus.WaitingForConfirm)?.Count ?? 0,
        };

        return new PagingModel<ReservationOrderInfo, ReservationOrderStatDto>
        {
            Data = pagingModel.Data,
            PageIndex = pagingModel.PageIndex,
            PageSize = pagingModel.PageSize,
            Total = pagingModel.Total,
            Supplement = statDto
        };
    }

    private async Task<ReservationOrderTicketCode> GetReservationOrderByTicketCode(long ticketCode)
    {
        return await _dbContext.ReservationOrderTicketCodes.AsNoTracking()
                .Where(x => x.Code == ticketCode)
                .FirstOrDefaultAsync();
    }

    #endregion

    #region Mall

    /// <summary>
    /// 根据主订单获取预约单
    /// </summary>
    public async Task<IEnumerable<GetByBaseOrderIdOutput>> GetByBaseOrderId(long baseOrderId)
    {
        var user = _httpContextAccessor.HttpContext.GetCurrentUser();
        await IsMyOrder(baseOrderId, user.userid);

        var reservationOrders = await _dbContext.ReservationOrders
            .Where(x => x.BaseOrderId.Equals(baseOrderId)).ToListAsync();

        var ticketOrders = await _dbContext.TicketOrders.WhereIF(reservationOrders is not null
            , x => reservationOrders!.Select(r => r.TicketOrderId).Contains(x.Id)).ToListAsync();

        //查询出行人信息
        var ticketOrderTravelers = await _dbContext.TicketOrderTravelers.AsNoTracking()
            .Where(x => x.BaseOrderId.Equals(baseOrderId))
            .WhereIF(reservationOrders is not null, x => reservationOrders!.Select(r => r.Id).Contains(x.ReservationOrderId))
            .Select(x => new TicketOrderTravelerDto
            {
                Name = x.Name,
                PhoneNumber = x.PhoneNumber,
                IDCard = x.IDCard,
                ReservationOrderId = x.ReservationOrderId
            }).ToListAsync();

        var result = new List<GetByBaseOrderIdOutput>();
        foreach (var reservationOrder in reservationOrders)
        {
            var skuNumberOfNights = ticketOrders.Where(t => t.Id.Equals(reservationOrder.TicketOrderId))
                .Select(t => t.SkuNumberOfNights).FirstOrDefault();
            var getByBaseOrderIdOutput = new GetByBaseOrderIdOutput
            {
                Id = reservationOrder.Id,
                BaseOrderId = reservationOrder.BaseOrderId,
                Quantity = reservationOrder.Quantity,
                Status = reservationOrder.Status,
                SkuNumberOfNights = skuNumberOfNights,
                TravelDateBegin = reservationOrder.TravelDateBegin,
                TravelDateEnd = reservationOrder.TravelDateBegin.AddDays(skuNumberOfNights),
                Traveler = reservationOrder.Traveler,
                CreateTime = reservationOrder.CreateTime,
                TicketOrderTravelers = ticketOrderTravelers
                .Where(t => t.ReservationOrderId.Equals(reservationOrder.Id)).ToList()
            };
            result.Add(getByBaseOrderIdOutput);
        }

        return result;
    }

    /// <summary>
    /// 获取预约单
    /// </summary>
    public async Task<MallGetReservationOrderOutput> MallGet(long reservationOrderId)
    {
        var user = _httpContextAccessor.HttpContext.GetCurrentUser();
        var reservationOrder = await _dbContext.ReservationOrders
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == reservationOrderId);

        await IsMyOrder(reservationOrder.BaseOrderId, user.userid);

        //获取出行人信息
        var ticketOrderTravelers = await _dbContext.TicketOrderTravelers.AsNoTracking()
            .Where(x => x.TicketOrderId.Equals(reservationOrder.TicketOrderId))
            .Where(x => x.ReservationOrderId.Equals(reservationOrder.Id))
            .Select(x => new TicketOrderTravelerDto
            {
                Name = x.Name,
                IDCard = x.IDCard,
                PhoneNumber = x.PhoneNumber,
                ReservationOrderId = x.ReservationOrderId
            }).ToListAsync();

        var result = _mapper.Map<MallGetReservationOrderOutput>(reservationOrder);
        result.TicketOrderTravelers = ticketOrderTravelers;

        result.Status = result.Status == ReservationStatus.WaitingForClaim
                    ? ReservationStatus.WaitingForConfirm
                    : result.Status;

        var ticketOrder = await _dbContext.TicketOrders
            .Select(x => new
            {
                x.BaseOrderId,
                x.ProductTicketBusinessType,
                x.SkuNumberOfNights
            })
            .FirstOrDefaultAsync(x => x.BaseOrderId == reservationOrder.BaseOrderId);
        result.ProductTicketBusinessType = ticketOrder.ProductTicketBusinessType;

        //包含晚数 结束日期更新为离店日期
        if (ticketOrder.SkuNumberOfNights > 0)
        {
            result.TravelDateEnd =
                result.TravelDateBegin.AddDays(ticketOrder.SkuNumberOfNights);
        }
        result.TravelDateCount = result.TravelDateEnd.Subtract(result.TravelDateBegin).Days;

        //券码按状态分组
        var reservationedTicketCodes = await _dbContext.ReservationOrderTicketCodes
                   .Where(x => x.ReservationOrderId == reservationOrderId)
                   .Select(x => new
                   {
                       x.Status,
                       x.Code,
                       x.UpdateTime
                   })
                   .ToListAsync();

        result.TicketCodes = reservationedTicketCodes.GroupBy(x => x.Status)
             .Select(x => new ReservationTicketCode
             {
                 Status = x.Key,
                 Codes = x.Select(w => new TicketCodeDetail
                 {
                     Code = w.Code,
                     UpdateTime = w.UpdateTime
                 })
             });

        var paymentInfos = await _dbContext.ReservationOrderPayments.AsNoTracking()
            .Where(x => x.ReservationOrderId == reservationOrderId)
            .ToListAsync();
        if (paymentInfos.Any())
        {
            result.PaymentAmount = paymentInfos.Sum(x => x.Amount * reservationOrder.Quantity);
            result.PaymentInfos = paymentInfos.Select(x => new ReservationOrderPaymentInfo()
            {
                Date = x.Date,
                PaymentCurrencyCode = x.PaymentCurrencyCode,
                Amount = x.Amount * reservationOrder.Quantity
            })
            .ToList();
        }

        return result;
    }

    #endregion

    /// <summary>
    /// 取消预约
    /// </summary>
    [UnitOfWork]
    public async Task Cancel(CancelReservationOrderInput input, OperationUserDto operationUser)
    {
        var reservationOrder = await _dbContext.ReservationOrders
            .Where(x => x.Id == input.ReservationOrderId)
            .FirstOrDefaultAsync();

        //判断预约单状态是否支持取消
        if (operationUser.UserType != Contracts.Common.Order.Enums.UserType.Merchant)
        {
            //用户退款，判断是否为当前用户预约单
            await IsMyOrder(baseOrderId: reservationOrder.BaseOrderId,
                userId: operationUser.UserType == Contracts.Common.Order.Enums.UserType.Customer ? operationUser.UserId : null,
                agencyId: operationUser.UserType == Contracts.Common.Order.Enums.UserType.Agency ? operationUser.AgencyId : null,
                supplierId: operationUser.UserType == Contracts.Common.Order.Enums.UserType.Supplier ? operationUser.SupplierId : null);
        }
        //是否是商户 或供应商
        var isOperator = operationUser.UserType is Contracts.Common.Order.Enums.UserType.Merchant or Contracts.Common.Order.Enums.UserType.Supplier;
        if (isOperator is false)
        {
            if (reservationOrder.Status != ReservationStatus.WaitingForClaim
                    && reservationOrder.Status != ReservationStatus.WaitingForConfirm)
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        //获取可退券码（预约单包含的待使用票券）
        var ticketCodes = await _dbContext.ReservationOrderTicketCodes
              .Join(_dbContext.TicketCodes, x => x.TicketCodeId, y => y.Id, (x, y) => new
              {
                  rt = x,
                  t = y
              })
              .Where(x => x.rt.ReservationOrderId == input.ReservationOrderId
                  && x.rt.Status == ReservationTicketCodeStatus.WaitingForUse
                  && x.t.Status == TicketCodeStatus.WaitingForUse)
              .ToListAsync();

        if (ticketCodes.Count() == 0 || ticketCodes.Count() != reservationOrder.Quantity)
            throw new BusinessException(ErrorTypes.Order.TicketCodeNotEnough);

        RefundOrderApplyMessage refundOrderApplyCommand = null;
        RenewInventoryMessage renewInventoryCommand = null;
        //是否存在支付，支付金额大于0 && 非无支付方式
        var hasPayment = reservationOrder.PaymentAmount > 0 && reservationOrder.PaymentType != PayType.None;
        if (hasPayment)
        {
            var ticketOrder = await _dbContext.TicketOrders
                .Where(x => x.Id == reservationOrder.TicketOrderId)
                .Select(x => new { x.Id, x.ProductSupplierId })
                .FirstOrDefaultAsync();

            var reservationOrderPayments = await _dbContext.ReservationOrderPayments
               .Where(x => x.ReservationOrderId == reservationOrder.Id)
               .ToListAsync();
            var totalCost = reservationOrderPayments.Sum(x => x.Cost);

            //退款申请
            refundOrderApplyCommand = new RefundOrderApplyMessage()
            {
                TenantId = reservationOrder.TenantId,
                RefundOrderType = RefundOrderType.ReservationOrder,
                BaseOrderId = reservationOrder.BaseOrderId,
                SubOrdeId = reservationOrder.Id,
                UserType = operationUser.UserType,
                UserId = operationUser.UserId,
                UserName = operationUser.Name,
                HasReviewed = true,
                ReviewTime = DateTime.Now,
                TotalAmount = reservationOrder.PaymentAmount / reservationOrder.Quantity * ticketCodes.Count(),
                PaymentCurrencyCode = reservationOrder.PaymentCurrencyCode,
                ProofImgs = input.Imgs is null ? "" : string.Join("", input.Imgs),
                Reason = input.Message,
                Quantity = ticketCodes.Count(),
                SupplierId = ticketOrder.ProductSupplierId,
                Cost = totalCost * ticketCodes.Count(),
                CostCurrencyCode = reservationOrderPayments.First().CostCurrencyCode,
            };

            //预约单状态修改为退款中
            reservationOrder.Status = ReservationStatus.Refunding;
            reservationOrder.UpdateTime = DateTime.Now;

            ticketCodes.ForEach(x =>
            {
                //修改券码状态 →取消中
                x.t.Status = TicketCodeStatus.Canceling;
                x.t.UpdateTime = DateTime.Now;

                //修改预约单券码状态 →取消中
                x.rt.Status = ReservationTicketCodeStatus.Canceling;
                x.rt.UpdateTime = DateTime.Now;
            });
        }
        else
        {
            //返还库存（无加价付款或线下付款）
            renewInventoryCommand = new RenewInventoryMessage()
            {
                TenantId = reservationOrder.TenantId,
                OrderId = reservationOrder.Id,
                Quantity = ticketCodes.Count()
            };

            //预约单状态修改为已取消
            reservationOrder.Status = ReservationStatus.Canceled;
            reservationOrder.UpdateTime = DateTime.Now;

            ticketCodes.ForEach(x =>
            {
                //修改券码状态 →待预约
                x.t.Status = TicketCodeStatus.WaitingForReservation;
                x.t.UpdateTime = DateTime.Now;
                //修改预约单关联券码状态 →已取消
                x.rt.Status = ReservationTicketCodeStatus.Cancel;
                x.rt.UpdateTime = DateTime.Now;
            });
        }
        var baseOrder = await _dbContext.BaseOrders.AsNoTracking()
           .FirstOrDefaultAsync(x => x.Id == reservationOrder.BaseOrderId);

        if (!string.IsNullOrWhiteSpace(input.Message))
        {
            //记录取消操作备注               
            var remark = new BaseOrderRemark()
            {
                BaseOrderId = reservationOrder.BaseOrderId,
                Remark = input.Message,
                Imgs = input.Imgs is null ? "" : string.Join(",", input.Imgs),
                CreatorId = operationUser.UserId,
                CreatorName = operationUser.Name
            };
            await _dbContext.AddAsync(remark);
        }

        //取消预约单日志
        var cancelLog = new OrderLogs
        {
            OrderId = reservationOrder.Id,
            OperationRole = operationUser.UserType,
            OperationType = OrderOperationType.Canceled,
            OrderLogType = OrderLogType.Reservation,
            UserId = operationUser.UserId,
            UserName = operationUser.Name,
        };
        await _dbContext.AddAsync(cancelLog);

        //商家主动退款 发送短信通知
        if (isOperator)
        {
            await _messageNotifyService.TicketReservationCanceledNotify(new OrderNotifyDto<TicketReservationCanceledNotifyDto>
            {
                BaseOrder = _mapper.Map<BaseOrderNotify>(baseOrder),
                NotifyDto = new TicketReservationCanceledNotifyDto
                {
                    ProductName = baseOrder.ProductName,
                    SkuName = baseOrder.ProductSkuName,
                    Quantity = reservationOrder.Quantity,
                    TravelDateBegin = reservationOrder.TravelDateBegin,
                    TravelDateEnd = reservationOrder.TravelDateEnd
                }
            });
        }
        if (refundOrderApplyCommand != null)
            await _capPublisher.PublishAsync(CapTopics.Order.OrderRefundApply, refundOrderApplyCommand);
        if (renewInventoryCommand != null)
            await _capPublisher.PublishAsync(CapTopics.Inventory.RenewInventory, renewInventoryCommand);

    }

    /// <summary>
    /// 通过主订单id查询关联预约单信息
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    public async Task<(IEnumerable<ReservationOrder> Orders,
        IEnumerable<ReservationOrderPayment> Payments,
        IEnumerable<ReservationOrderTicketCode> TicketCodes,
        List<TicketOrderTravelerDto> TicketOrderTravelers)>
        GetFullInfoByBaseOrderId(long baseOrderId)
    {
        var orders = await _dbContext.ReservationOrders.AsNoTracking()
            .Where(x => x.BaseOrderId == baseOrderId)
            .ToListAsync();

        //查询预约单包含晚数
        var ticketOrderIds = orders.Select(x => x.TicketOrderId).Distinct();
        var tickerOrders = await _dbContext.TicketOrders.AsNoTracking()
            .Where(x => ticketOrderIds.Contains(x.Id))
            .Select(x => new
            {
                x.Id,
                x.SkuNumberOfNights
            })
            .ToListAsync();

        //查询出行人信息
        var ticketOrderTravelers = await _dbContext.TicketOrderTravelers.AsNoTracking()
            .Where(x => x.BaseOrderId.Equals(baseOrderId))
            .WhereIF(orders is not null, x => orders!.Select(r => r.Id).Contains(x.ReservationOrderId))
            .Select(x => new TicketOrderTravelerDto
            {
                Name = x.Name,
                PhoneNumber = x.PhoneNumber,
                IDCard = x.IDCard,
                ReservationOrderId = x.ReservationOrderId
            }).ToListAsync();

        foreach (var item in orders)
        {
            var skuNumberOfNights = tickerOrders.First(x => x.Id == item.TicketOrderId).SkuNumberOfNights;
            //包含晚数 结束日期更新为离店日期
            if (skuNumberOfNights > 0)
            {
                item.TravelDateEnd = item.TravelDateBegin.AddDays(skuNumberOfNights);
            }
        }

        var orderIds = orders.Select(x => x.Id);

        var prices = await _dbContext.ReservationOrderPayments.AsNoTracking()
            .Where(x => orderIds.Contains(x.ReservationOrderId))
            .ToListAsync();

        var tickerCode = await _dbContext.ReservationOrderTicketCodes.AsNoTracking()
            .Where(x => orderIds.Contains(x.ReservationOrderId))
            .ToListAsync();

        return new ValueTuple<IEnumerable<ReservationOrder>, IEnumerable<ReservationOrderPayment>,
                    IEnumerable<ReservationOrderTicketCode>, List<TicketOrderTravelerDto>>
                (orders, prices, tickerCode, ticketOrderTravelers);
    }

    #region 公共方法

    [UnitOfWork]
    public async Task<long> Create(CreateDto dto, OperationUserDto operationUser)
    {
        var baseOrder = await _dbContext.BaseOrders.AsNoTracking()
            .Where(x => x.Id == dto.BaseOrderId)
            .WhereIF(operationUser.UserType == Contracts.Common.Order.Enums.UserType.Customer, x => x.UserId == operationUser.UserId)
            .WhereIF(operationUser.UserType == Contracts.Common.Order.Enums.UserType.Agency, x => x.AgencyId == operationUser.AgencyId)
            .FirstOrDefaultAsync();

        var ticketOrder = await _dbContext.TicketOrders
            .Where(x => x.BaseOrderId == dto.BaseOrderId)
            .Select(x => new
            {
                x.Id,
                x.TenantId,
                x.BaseOrderId,
                x.ProductId,
                x.SkuId,
                x.ProductSupplierId,
                x.ProductTitle,
                x.SkuName,
                x.ProductNeedConfirmReservation,
                x.ProductReservationDaysInAdvance,
                x.SkuValidityEnd,
                x.SkuValidityBegin,
                x.SkuNumberOfNights,
                x.Quantity
            })
            .FirstOrDefaultAsync();

        #region 券类订单与有效期校验
        if (ticketOrder is null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        //校验有效期范围
        if (ticketOrder.SkuValidityEnd.Date.AddDays(1) < DateTime.Now)
            throw new BusinessException(ErrorTypes.Order.OutOfTravelDate);

        if (dto.TravelDateBegin < ticketOrder.SkuValidityBegin
            || dto.TravelDateEnd > ticketOrder.SkuValidityEnd)
            throw new BusinessException(ErrorTypes.Order.OutOfValidityDate);

        //提前几天预约
        var advanceDay = DateTime.Today.AddDays(ticketOrder.ProductReservationDaysInAdvance);
        if (advanceDay > dto.TravelDateBegin)
            throw new BusinessException(ErrorTypes.Order.OutOfTravelDate);//未在预约时间段内
        #endregion

        //日历加价和库存验证
        var paymentAmount = dto.PaymentInfos?.Sum(x => x.Amount * dto.Quantity) ?? 0;
        var paymentCurrencyCode = baseOrder.PaymentCurrencyCode;
        var reservationOrderPayments = await CheckCalendarPriceAndInventory(ticketOrder.SkuId,
            dto.TravelDateBegin, dto.TravelDateEnd, dto.Quantity, paymentAmount, paymentCurrencyCode);

        var reservationOrder = _mapper.Map<ReservationOrder>(dto);
        reservationOrder.TicketOrderId = ticketOrder.Id;
        reservationOrder.PaymentAmount = paymentAmount;
        reservationOrder.PaymentCurrencyCode = paymentCurrencyCode;
        reservationOrder.Status = ReservationStatus.WaitingForPay;
        if (operationUser.UserType == Contracts.Common.Order.Enums.UserType.Merchant)
        {
            reservationOrder.ClaimantId = operationUser.UserId;
            reservationOrder.ClaimantName = operationUser.Name;
            if (baseOrder.SellingChannels == SellingChannels.WechatMall)//微商城 商户操作预约
            {
                reservationOrder.Status = ReservationStatus.WaitingForConfirm;
                reservationOrder.PaymentType = PayType.None;//按无支付方式处理 不会调用退款
            }
        }

        if (paymentAmount == 0)
        {
            reservationOrder.Status = ticketOrder.ProductNeedConfirmReservation
                ? ReservationStatus.WaitingForClaim
                : ReservationStatus.Confirmed;
        }
        await _dbContext.AddAsync(reservationOrder);

        //加价明细
        if (reservationOrderPayments.Any())
        {
            reservationOrderPayments.ForEach(x => x.ReservationOrderId = reservationOrder.Id);
            await _dbContext.ReservationOrderPayments.AddRangeAsync(reservationOrderPayments);
        }

        //关联预约单和券码
        await ReservationOrderTicketCode(dto.BaseOrderId, reservationOrder.Id, dto.Quantity, reservationOrder.Status == ReservationStatus.WaitingForPay);

        //发布事件
        DeductCalendarInventoryMessage deductCalendarInventoryCommand = null;
        FrozenCalendarInventoryMessage frozenCalendarInventoryCommand = null;
        if (reservationOrder.Status == ReservationStatus.WaitingForPay)
        {
            //冻结日历库存
            frozenCalendarInventoryCommand = new FrozenCalendarInventoryMessage
            {
                TenantId = ticketOrder.TenantId,
                OrderId = reservationOrder.Id,
                ProductId = ticketOrder.ProductId,
                ItemId = ticketOrder.SkuId,
                BeginDate = reservationOrder.TravelDateBegin,
                EndDate = reservationOrder.TravelDateEnd,
                FrozenQuantity = reservationOrder.Quantity
            };
        }
        else
        {
            //扣除日历库存（无加价付款）
            deductCalendarInventoryCommand = new DeductCalendarInventoryMessage()
            {
                TenantId = ticketOrder.TenantId,
                OrderId = reservationOrder.Id,
                ProductId = ticketOrder.ProductId,
                ItemId = ticketOrder.SkuId,
                BeginDate = reservationOrder.TravelDateBegin,
                EndDate = reservationOrder.TravelDateEnd,
                Quantity = reservationOrder.Quantity
            };
        }

        //创建预约单日志
        var addLog = new OrderLogs
        {
            OrderId = reservationOrder.Id,
            OperationType = OrderOperationType.Created,
            OrderLogType = OrderLogType.Reservation,
            OperationRole = operationUser.UserType,
            UserId = operationUser.UserId,
            UserName = operationUser.Name
        };
        await _dbContext.AddAsync(addLog);

        //出行人信息
        List<TicketOrderTraveler> ticketOrderTravelers = new();
        foreach (var travelerInput in dto.TicketOrderTravelers)
        {
            TicketOrderTraveler ticketOrderTraveler = new()
            {
                BaseOrderId = baseOrder.Id,
                TicketOrderId = ticketOrder.Id,
                ReservationOrderId = reservationOrder.Id,
                Name = travelerInput.Name,
                IDCard = travelerInput.IDCard,
                PhoneNumber = travelerInput.PhoneNumber
            };
            ticketOrderTravelers.Add(ticketOrderTraveler);
        }
        await _dbContext.AddRangeAsync(ticketOrderTravelers);
        //消息通知
        if (reservationOrder.Status == ReservationStatus.WaitingForClaim)
        {
            await _messageNotifyService.TicketReservationNotify(new OrderNotifyDto<TicketReservationNotifyDto>
            {
                BaseOrder = _mapper.Map<BaseOrderNotify>(baseOrder),
                NotifyDto = new TicketReservationNotifyDto
                {
                    ProductName = ticketOrder.ProductTitle,
                    SkuName = ticketOrder.SkuName,
                    SupplierId = ticketOrder.ProductSupplierId,
                    TravelDateBegin = reservationOrder.TravelDateBegin,
                    TravelDateEnd = reservationOrder.TravelDateEnd,
                    Nights = ticketOrder.SkuNumberOfNights,
                    Quantity = ticketOrder.Quantity,
                    Traveler = ticketOrderTravelers?.Select(x => x.Name)?.ToArray() ?? Array.Empty<string>(),
                }
            });
        }
        if (frozenCalendarInventoryCommand is not null)
            await _capPublisher.PublishAsync(CapTopics.Inventory.FrozenCalendarInventory, frozenCalendarInventoryCommand);
        if (deductCalendarInventoryCommand is not null)
            await _capPublisher.PublishAsync(CapTopics.Inventory.DeductCalendarInventory, deductCalendarInventoryCommand);

        //上送跟踪日志
        var shareInfo = await _dbContext.OrderShareInfos.AsNoTracking()
            .FirstOrDefaultAsync(x => x.BaseOrderId == baseOrder.Id);
        if (shareInfo?.TraceId is > 0)
        {
            await _capPublisher.PublishAsync(CapTopics.Marketing.PushPromotionTraceRecord,
                new PushPromotionTraceRecordMessage
                {
                    PromotionTraceId = shareInfo.TraceId.Value,
                    CustomerId = shareInfo.BuyerId,
                    BehaviorType = TraceBehaviorType.CreateOrder,
                    OrderType = OrderType.Ticket,
                    OrderId = reservationOrder.Id,
                    IsReservationOrder = true,
                    VisitTargetName = reservationOrder.ResourceName
                });
        }

        await _dbContext.SaveChangesAsync();
        if (reservationOrder.Status == ReservationStatus.WaitingForPay)
        {
            //超时自动关闭
            _backgroundJobClient.Schedule<HangfireClient.Jobs.IOrderJob>(s => s.CloseTimeoutReservationOrder(reservationOrder.Id, reservationOrder.TenantId), TimeSpan.FromMinutes(30));
        }
        return reservationOrder.Id;
    }

    /// <summary>
    /// 日历库存价格检验
    /// </summary>
    private async Task<List<ReservationOrderPayment>> CheckCalendarPriceAndInventory(
        long skuId, DateTime beginDate, DateTime endDate, int quantity, decimal paymentAmount, string paymentCurrencyCode)
    {
        var request = new GetProductSkuCalendarPriceInput()
        {
            ProductSkuId = skuId,
            StartDate = beginDate,
            EndDate = endDate
        };
        var content = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
        var checkReponse = await _httpClientFactory.InternalPostAsync<GetProductSkuCalendarPriceOutput>(
          requestUri: _servicesAddress.Product_GetProductSkuCalendarPrice(),
          httpContent: content);

        //库存是否可用
        if (checkReponse.SkuCalendarPrices.Any() is false
            || checkReponse.SkuCalendarPrices.Any(x => !x.Enabled || x.AvailableQuantity < quantity))
            throw new BusinessException(ErrorTypes.Inventory.ProductInventoryNotEnough);

        //多币种
        var priceExchangeRate = await _currencyExchangeRateService.GetOrderPriceExchange(new OrderPriceExchangeRateInput
        {
            CostCurrencyCode = checkReponse.CostCurrencyCode,
            SaleCurrencyCode = checkReponse.SaleCurrencyCode,
            PaymentCurrencyCode = paymentCurrencyCode,
        });

        //是否变价
        var totalAmount = checkReponse.SkuCalendarPrices
            .Where(x => x.Price.HasValue)
            .Sum(x => Math.Round(x.Price!.Value * priceExchangeRate.ExchangeRate, 2) * quantity);
        if (totalAmount != paymentAmount)
            throw new BusinessException(ErrorTypes.Order.ProductPriceChange);

        var reservationOrderPayments = new List<ReservationOrderPayment>();
        if (checkReponse.SkuCalendarPrices.Any())
        {
            reservationOrderPayments = checkReponse.SkuCalendarPrices.Select(x => new ReservationOrderPayment()
            {
                Date = x.Date,
                CostCurrencyCode = checkReponse.CostCurrencyCode,
                OrgPriceCurrencyCode = checkReponse.SaleCurrencyCode,
                ExchangeRate = priceExchangeRate.ExchangeRate,
                OrgPrice = x.Price ?? 0,
                PaymentCurrencyCode = paymentCurrencyCode,
                Amount = (x.Price ?? 0) * priceExchangeRate.ExchangeRate,
                Cost = x.CostPrice ?? 0
            }).ToList();
        }
        return reservationOrderPayments;
    }

    /// <summary>
    /// 关联券码和预约单
    /// </summary>
    /// <param name="hasPayment">是否有线上付款</param>
    private async Task ReservationOrderTicketCode(long baseOrderId, long reservationOrderId, int quantity, bool hasPayment)
    {
        //获取可用券码（待预约券码）
        var ticketCodes = await _dbContext.TicketCodes
            .Where(x => x.BaseOrderId == baseOrderId
                && x.Status == TicketCodeStatus.WaitingForReservation)
            .Take(quantity)
            .ToListAsync();

        if (ticketCodes.Count < quantity)
            throw new BusinessException(ErrorTypes.Order.TicketCodeNotEnough);

        //关联券码和预约单
        var reservationOrderTicketCodes = ticketCodes.Select(x => new ReservationOrderTicketCode()
        {
            ReservationOrderId = reservationOrderId,
            TicketCodeId = x.Id,
            Code = x.Code,
            Status = hasPayment ? ReservationTicketCodeStatus.Reservation : ReservationTicketCodeStatus.WaitingForUse,
            UpdateTime = DateTime.Now
        });
        await _dbContext.ReservationOrderTicketCodes.AddRangeAsync(reservationOrderTicketCodes);

        //将券码状态修改为预约中
        ticketCodes.ForEach(x =>
        {
            x.Status = hasPayment ? TicketCodeStatus.Reservation : TicketCodeStatus.WaitingForUse;
            x.UpdateTime = DateTime.Now;
        });
    }

    /// <summary>
    /// 是否为当前用户订单
    /// </summary>
    private async Task IsMyOrder(long baseOrderId,
        long? userId = default,
        long? agencyId = default,
        long? supplierId = default)
    {
        var isMyOrder = await _dbContext.BaseOrders
            .Join(_dbContext.TicketOrders, x => x.Id, y => y.BaseOrderId, (x, y) => new
            {
                x.Id,
                x.UserId,
                x.AgencyId,
                y.ProductSupplierId
            })
            .Where(x => x.Id == baseOrderId)
            .WhereIF(userId.HasValue, x => x.UserId == userId.Value)
            .WhereIF(agencyId.HasValue, x => x.AgencyId == agencyId.Value)
            .WhereIF(supplierId.HasValue, x => x.ProductSupplierId == supplierId.Value)
            .AnyAsync();

        if (!isMyOrder)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
    }

    #endregion

    /// <summary>
    /// 预约单过期自动完结
    /// </summary>
    [UnitOfWork]
    public async Task<List<FinishExpirationReservationOrderOutput>> FinishExpirationReservationOrder()
    {
        var list = new List<FinishExpirationReservationOrderOutput>();

        //已过预约时间 未完结 的预约订单
        var reservationStatuses = new ReservationStatus[] {
            ReservationStatus.Confirmed,
            ReservationStatus.WaitingForClaim,
            ReservationStatus.WaitingForConfirm
        };
        var reservationOrders = await _dbContext.ReservationOrders
            .IgnoreQueryFilters()
            .Where(x => reservationStatuses.Contains(x.Status) && x.TravelDateEnd.AddDays(1) < DateTime.Now)
            .OrderBy(x => x.TenantId)
            .ToListAsync();

        if (!reservationOrders.Any())
            return list;
        var ticketOrderIds = reservationOrders.Select(x => x.TicketOrderId);
        var ticketOrders = await _dbContext.TicketOrders
            .IgnoreQueryFilters()
            .Where(x => ticketOrderIds.Contains(x.Id))
            .Select(x => new { x.Id, x.BaseOrderId, x.TenantId, x.ProductNeedWriteOff, x.TicketSaleType, x.ProductTicketBusinessType })
            .ToListAsync();
        var baseOrderIds = ticketOrders.Select(x => x.BaseOrderId);
        var baseOrders = await _dbContext.BaseOrders.IgnoreQueryFilters()
            .Where(x => baseOrderIds.Contains(x.Id))
            .ToListAsync();
        var ticketCodes = await _dbContext.TicketCodes.IgnoreQueryFilters()
            .Where(x => baseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();
        var reservationOrderIds = reservationOrders.Select(x => x.Id);
        var reservationOrderTicketCodes = await _dbContext.ReservationOrderTicketCodes.IgnoreQueryFilters()
            .Where(x => reservationOrderIds.Contains(x.ReservationOrderId))
            .ToListAsync();
        var shareInfoList = await _dbContext.OrderShareInfos.IgnoreQueryFilters()
            .Where(x => baseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();

        List<OrderLogs> orderLogs = new();
        List<BonusReleaseMessage> bonusReleaseMessages = new();
        foreach (var reservationOrder in reservationOrders)
        {
            var ticketOrder = ticketOrders.FirstOrDefault(x => x.Id == reservationOrder.TicketOrderId);
            if (ticketOrder is null) continue;

            //1.仅预约的团购预约订单过了预约日期自动结束
            //2.包含核销的团购预约订单不根据预约日期自动结束,需要核销后预约单才能结束
            switch (ticketOrder.TicketSaleType)
            {
                case TicketSaleType.GroupPurchase when !ticketOrder.ProductNeedWriteOff:
                    {
                        //修改预约单状态
                        reservationOrder.Status = ReservationStatus.Finished;
                        reservationOrder.UpdateTime = DateTime.Now;
                        //预约单完结日志
                        var orderLog = new OrderLogs()
                        {
                            OrderId = reservationOrder.Id,
                            OperationRole = Contracts.Common.Order.Enums.UserType.None,
                            OperationType = OrderOperationType.FinishedByOverdue,
                            OrderLogType = OrderLogType.Reservation,
                            UserId = 0,
                            UserName = "系统"
                        };
                        orderLog.SetTenantId(reservationOrder.TenantId);
                        orderLogs.Add(orderLog);
                        //不需要核销，将券码状态修改为已完成
                        if (!ticketOrder.ProductNeedWriteOff)
                        {
                            //待使用的预约单券码
                            var roTicketCodes = reservationOrderTicketCodes
                                .Where(x => x.ReservationOrderId == reservationOrder.Id
                                && x.Status == ReservationTicketCodeStatus.WaitingForUse);
                            foreach (var roTicketCode in roTicketCodes)
                            {
                                var ticketCode = ticketCodes
                                    .FirstOrDefault(x => x.Id == roTicketCode.TicketCodeId && x.Status == TicketCodeStatus.WaitingForUse);
                                //修改券码状态为已完结
                                ticketCode.Status = TicketCodeStatus.Finished;
                                ticketCode.UpdateTime = DateTime.Now;
                                //修改预约单券码状态为已完结
                                roTicketCode.Status = ReservationTicketCodeStatus.Finished;
                                roTicketCode.UpdateTime = DateTime.Now;
                            }

                            //获取主订单对应的券码
                            var baseOrderTicketCodes = ticketCodes.Where(x => x.BaseOrderId == ticketOrder.BaseOrderId);
                            //券码为退款和完成则订单为完成
                            if (baseOrderTicketCodes.All(x => x.Status == TicketCodeStatus.Refunded || x.Status == TicketCodeStatus.Finished))
                            {
                                var baseOrder = baseOrders.Where(x => x.Id == ticketOrder.BaseOrderId).FirstOrDefault();
                                baseOrder.Status = BaseOrderStatus.Finished;
                                baseOrder.UpdateTime = DateTime.Now;

                                //订单完结日志
                                var baseOrderLog = new OrderLogs()
                                {
                                    OrderId = baseOrder.Id,
                                    OperationRole = Contracts.Common.Order.Enums.UserType.None,
                                    OperationType = OrderOperationType.Finished,
                                    OrderLogType = OrderLogType.Ticket,
                                    UserId = 0,
                                    UserName = "系统"
                                };
                                baseOrderLog.SetTenantId(reservationOrder.TenantId);
                                orderLogs.Add(baseOrderLog);

                                bonusReleaseMessages.Add(new BonusReleaseMessage
                                {
                                    TenantId = baseOrder.TenantId,
                                    BaseOrderId = baseOrder.Id,
                                    UserNickName = baseOrder.UserNickName,
                                    Status = 1//1-完成
                                });
                                if (baseOrder.SellingPlatform == SellingPlatform.B2BWeb || baseOrder.SellingPlatform == SellingPlatform.B2BApplet)
                                {
                                    var orderPrice = await _dbContext.OrderPrices
                                        .Where(x => x.BaseOrderId == baseOrder.Id)
                                        .Select(x => new { x.ExchangeRate, x.OrgPriceCurrencyCode })
                                        .FirstOrDefaultAsync();
                                    await _capPublisher.PublishAsync(CapTopics.Tenant.SyncOrderChangeGrowUpValue, new Contracts.Common.Tenant.DTOs.AgencyLevelDetail.SyncOrderChangeGrowUpValueInput
                                    {
                                        AgencyId = baseOrder.AgencyId,
                                        BusinessType = ticketOrder.ProductTicketBusinessType switch
                                        {
                                            TicketBusinessType.HotelPackages => Contracts.Common.Tenant.Enums.AgencyLevelBusinessType.Ticket_HotelPackages,
                                            TicketBusinessType.RoomVoucher => Contracts.Common.Tenant.Enums.AgencyLevelBusinessType.Ticket_RoomVoucher,
                                            TicketBusinessType.Catering => Contracts.Common.Tenant.Enums.AgencyLevelBusinessType.Ticket_Catering,
                                        },
                                        OrderAmout = baseOrder.PaymentAmount / orderPrice.ExchangeRate,
                                        OrderNo = baseOrder.Id.ToString(),
                                        Title = baseOrder.ProductName,
                                    });
                                }

                                var shareInfo = shareInfoList.FirstOrDefault(x => x.BaseOrderId == baseOrder.Id);
                                if (shareInfo?.TraceId is > 0)
                                {
                                    await _capPublisher.PublishAsync(CapTopics.Marketing.PushPromotionTraceRecord,
                                        new PushPromotionTraceRecordMessage
                                        {
                                            PromotionTraceId = shareInfo.TraceId.Value,
                                            CustomerId = shareInfo.BuyerId,
                                            BehaviorType = TraceBehaviorType.CompleteOrder,
                                            OrderType = baseOrder.OrderType,
                                            OrderId = baseOrder.Id,
                                            VisitTargetName = baseOrder.ProductName
                                        });
                                }
                            }
                        }
                    }
                    break;
                case TicketSaleType.Booking:
                    {
                        //修改预约单状态
                        reservationOrder.Status = ReservationStatus.Finished;
                        reservationOrder.UpdateTime = DateTime.Now;
                    }
                    break;
                default: continue;
            }
            list.Add(new FinishExpirationReservationOrderOutput()
            {
                TenantId = reservationOrder.TenantId,
                BaseOrderId = ticketOrder.BaseOrderId,
                TicketOrderId = reservationOrder.TicketOrderId,
                ReservationOrderId = reservationOrder.Id,
                ProductNeedWriteOff = ticketOrder.ProductNeedWriteOff,
                Result = true
            });
        }
        await _dbContext.OrderLogs.AddRangeAsync(orderLogs);
        foreach (var bonusReleaseMessage in bonusReleaseMessages)
        {
            await _capPublisher.PublishAsync(CapTopics.User.BonusRelease, bonusReleaseMessage);
        }

        return list;
    }

    /// <summary>
    /// 预约时间提醒
    /// </summary>
    /// <returns></returns>
    public async Task<List<long>> AutoTimeReminder()
    {
        List<long> notifyReservationOrderIds = new();

        var threeDaysAfterTheDate = DateTime.Now.AddDays(3).Date;
        var sevenDaysAfterTheDate = DateTime.Now.AddDays(7).Date;

        var reservationOrderInfos = await _dbContext.ReservationOrders.IgnoreQueryFilters()
               .Join(_dbContext.BaseOrders, r => r.BaseOrderId, b => b.Id, (r, b) => new { r, b })
               .Join(_dbContext.TicketOrders, x => x.b.Id, t => t.BaseOrderId, (x, t) => new { x.r, x.b, t })
               .Where(x => x.b.Status.Equals(BaseOrderStatus.UnFinished)
               && x.t.TicketSaleType.Equals(TicketSaleType.GroupPurchase)
               && (x.r.TravelDateBegin.Date.Equals(threeDaysAfterTheDate)
               || x.r.TravelDateBegin.Date.Equals(sevenDaysAfterTheDate)))
               .Select(x => new
               {
                   x.b.ProductName,
                   SkuName = x.b.ProductSkuName,
                   x.r.TravelDateBegin,
                   x.r.TravelDateEnd,
                   x.r.ResourceName,
                   x.t.ProductTicketBusinessType,
                   BaseOrder = new BaseOrderNotify
                   {
                       UserId = x.b.UserId,
                       Id = x.b.Id,
                       SellingPlatform = x.b.SellingPlatform,
                       TenantId = x.b.TenantId
                   },
                   ReservationOrderId = x.t.Id
               })
               .ToListAsync();
        if (reservationOrderInfos is null || !reservationOrderInfos.Any()) return notifyReservationOrderIds;

        //提交前判断队列是否已存在该条记录
        string _ticketReservationTimeReminderKey = $"openapi:order:ticketreservation_time_reminder_{DateTime.Now.Date:yyyy-MM-dd}";
        var cacheReservationOrderIds = await _redisClient
            .ListRangeAsync<long>(_ticketReservationTimeReminderKey);
        reservationOrderInfos = reservationOrderInfos
            .Where(x => !cacheReservationOrderIds.Contains(x.ReservationOrderId))
            .ToList();
        if (!reservationOrderInfos.Any()) return notifyReservationOrderIds;

        //消息通知
        foreach (var item in reservationOrderInfos)
        {
            var orderNotifyDto = new OrderNotifyDto<TicketReservationTimeReminderNotifyDto>()
            {
                BaseOrder = item.BaseOrder,
                NotifyDto = new TicketReservationTimeReminderNotifyDto
                {
                    ProductName = item.ProductName,
                    SkuName = item.SkuName,
                    TravelDateBegin = item.TravelDateBegin,
                    TravelDateEnd = item.TravelDateEnd,
                    ResourceName = item.ResourceName,
                    ProductTicketBusinessType = item.ProductTicketBusinessType
                }
            };
            await _messageNotifyService.TicketReservationTimeReminderNotify(orderNotifyDto);
            notifyReservationOrderIds.Add(item.ReservationOrderId);
            await _redisClient.ListLeftPushAsync(_ticketReservationTimeReminderKey, item.ReservationOrderId);
        }
        await _redisClient.KeyExpireAsync(_ticketReservationTimeReminderKey, TimeSpan.FromDays(1));
        return notifyReservationOrderIds;
    }
}

