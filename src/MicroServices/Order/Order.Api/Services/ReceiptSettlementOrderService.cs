using AutoMapper;
using Cit.Storage.Redis;
using Common.GlobalException;
using Common.Jwt;
using Common.ServicesHttpClient;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.ReceiptSettlementOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Payment.DTOs.TenantBankAccount;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Payment.Messages;
using Contracts.Common.Tenant.DTOs.Agency;
using Contracts.Common.Tenant.DTOs.AgencyCredit;
using Contracts.Common.Tenant.DTOs.AgencyCreditCharge;
using Contracts.Common.Tenant.DTOs.FinancialSetting;
using Contracts.Common.Tenant.DTOs.Tenant;
using Contracts.Common.Tenant.Enums;
using Contracts.Common.Tenant.Messages;
using Contracts.Common.User.DTOs.OperationLog;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Order.Api.Extensions;
using Order.Api.Services.Interfaces;
using Extensions;
using Contracts.Common.User.Enums;
using Contracts.Common.Reflection;

namespace Order.Api.Services;

public class ReceiptSettlementOrderService : IReceiptSettlementOrderService
{
    private readonly string _remindKey = "order:receiptsettlementorderremind:{0}:{1}";
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly IAgencyCreditPayService _agencyCreditPayService;
    private readonly ServicesAddress _servicesAddress;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ICapPublisher _capPublisher;
    private readonly IRedisClient _redisClient;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ReceiptSettlementOrderService(
        CustomDbContext dbContext,
        IMapper mapper,
        IAgencyCreditPayService agencyCreditPayService,
        IOptions<ServicesAddress> options,
        IHttpClientFactory httpClientFactory,
        ICapPublisher capPublisher,
        IRedisClient redisClient,
        IHttpContextAccessor httpContextAccessor)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _agencyCreditPayService = agencyCreditPayService;
        _servicesAddress = options.Value;
        _httpClientFactory = httpClientFactory;
        _capPublisher = capPublisher;
        _redisClient = redisClient;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<PagingModel<SearchOutput>> Search(SearchInput input)
    {
        var pageData = await SearchOrderQuery(input)
            .PagingAsync(input.PageIndex, input.PageSize);
        var result = _mapper.Map<PagingModel<SearchOutput>>(pageData);
        if (result.Data.Any())
        {
            var settlementOrderIds = result.Data.Select(x => x.Id);
            var invoiceRecords = await _dbContext.InvoiceRecords.AsNoTracking()
                .Where(x => settlementOrderIds.Contains(x.OrderId))
                .ToListAsync();
            if (invoiceRecords.Any())
            {
                foreach (var item in result.Data)
                {
                    var invoiceRecord = invoiceRecords.Where(x => x.OrderId == item.Id).MaxBy(x => x.Id);
                    item.InvoiceSerialNo = invoiceRecord?.InvoiceSerialNo;
                    item.InvoiceStatus = invoiceRecord?.Status;
                }
            }
        }
        return result;
    }


    public async Task<List<long>> SearchReceiptSettlementIds(SearchInput input)
    {
        var result = await SearchOrderQuery(input).Select(x => x.Id).ToListAsync();
        return result;
    }

    public async Task<IEnumerable<ExportOrderOutput>> ExportOrder(ExportOrderInput input)
    {
        var queryData = await SearchOrderQuery(new SearchInput
        {
            TenantId = input.TenantId,
            AgencyId = input.AgencyId,
            BeginTime = input.BeginTime,
            EndTime = input.EndTime,
            Status = input.Status,
            Id = input.Id,
            TenantDepartmentId = input.TenantDepartmentId,
            SalespersonId = input.SalespersonId
        })
            .ToListAsync();
        var exportData = _mapper.Map<List<ExportOrderOutput>>(queryData);
        if (exportData.Any())
        {
            var settlementOrderIds = exportData.Select(x => x.Id);
            var invoiceRecords = await _dbContext.InvoiceRecords.AsNoTracking()
                .Where(x => settlementOrderIds.Contains(x.OrderId))
                .ToListAsync();
            if (invoiceRecords.Any())
            {
                foreach (var item in exportData)
                {
                    var invoiceRecord = invoiceRecords.Where(x => x.OrderId == item.Id).MaxBy(x => x.OrderId);
                    item.InvoiceSerialNo = invoiceRecord?.InvoiceSerialNo;
                    item.InvoiceStatus = invoiceRecord?.Status;
                }
            }
        }
        return exportData;
    }

    public async Task<PagingModel<UnReceiptByEarlyWarningOutput>> UnReceiptByEarlyWarning(UnReceiptByEarlyWarningInput input)
    {
        var now = DateTime.Now;

        // 先计算最小时间并存储到临时变量
        var minDatesQuery = _dbContext.ReceiptSettlementOrders
            .AsNoTracking()
            .Where(x => x.CreatTime.AddDays(7) <= now && x.Status != ReceiptSettlementOrderStatus.ReceiptComplete)
            .GroupBy(x => x.AgencyId)
            .Select(g => new
            {
                AgencyId = g.Key,
                BillingCycleEnd = g.Min(x => x.BillingCycleEnd),
            })
            .OrderBy(x => x.BillingCycleEnd);

        var datas = await minDatesQuery.Skip((input.PageIndex - 1) * input.PageSize).Take(input.PageSize)
            .Join(_dbContext.ReceiptSettlementOrders.AsNoTracking(), x => new { x.BillingCycleEnd, x.AgencyId }, y => new { y.BillingCycleEnd, y.AgencyId },
                (x, y) => y)
            .ToListAsync();

        var paging = await minDatesQuery.PagingAsync(input.PageIndex, input.PageSize);

        var result = new PagingModel<UnReceiptByEarlyWarningOutput>();
        result.Total = paging.Total;
        result.PageIndex = paging.PageIndex;
        result.PageSize = paging.PageSize;

        var outputs = new List<UnReceiptByEarlyWarningOutput>();
        foreach (var item in paging.Data)
        {
            var data = datas.FirstOrDefault(x => x.AgencyId == item.AgencyId);
            var output = _mapper.Map<UnReceiptByEarlyWarningOutput>(data);
            outputs.Add(output);
        }
        result.Data = outputs;
        return result;
    }

    public async Task<PagingModel<UnReceiptDetailsByEarlyWarningOutput>> UnReceiptDetailsByEarlyWarning(UnReceiptDetailsByEarlyWarningInput input)
    {
        var now = DateTime.Now;
        var paging = await _dbContext.ReceiptSettlementOrders.AsNoTracking()
            .Where(x => x.CreatTime.AddDays(7) <= now && x.Status != ReceiptSettlementOrderStatus.ReceiptComplete)
            .Where(x => x.AgencyId == input.AgencyId)
            .OrderBy(x => x.BillingCycleEnd)
            .PagingAsync(input.PageIndex, input.PageSize);

        var result = _mapper.Map<PagingModel<UnReceiptDetailsByEarlyWarningOutput>>(paging);
        return result;
    }

    private IQueryable<ReceiptSettlementOrder> SearchOrderQuery(SearchInput input)
    {
        var query = _dbContext.ReceiptSettlementOrders
            .AsNoTracking()
            .IgnoreQueryFilters()
            .WhereIF(input.TenantId.HasValue, x => x.TenantId == input.TenantId)
            .WhereIF(input.Status.HasValue, x => x.Status == input.Status!.Value)
            .WhereIF(input.AgencyId is > 0, x => x.AgencyId == input.AgencyId)
            .WhereIF(input.BeginTime.HasValue, x => x.CreatTime >= input.BeginTime)
            .WhereIF(input.EndTime.HasValue, x => x.CreatTime < input.EndTime!.Value.AddDays(1))
            .WhereIF(input.Id is > 0, x => x.Id == input.Id)
            .WhereIF(input.TenantDepartmentId is > 0, x => x.TenantDepartmentId == input.TenantDepartmentId)
            .WhereIF(input.SalespersonId is > 0, x => x.SalespersonId == input.SalespersonId)
            .OrderByDescending(x => x.Id);
        return query;
    }

    private IQueryable<ReceiptSettlementOrder> ReceiptSettlementOrdersForRemindQuery(SearchInput input)
    {
        //提醒信息 设置为8天后才可显示
        var now = DateTime.Now;
        var query = _dbContext.ReceiptSettlementOrders.AsNoTracking()
            .Where(x => x.SendEmailTime.HasValue && x.SendEmailTime.Value.AddDays(8) <= now)
            .WhereIF(input.Status.HasValue, x => x.Status == input.Status!.Value)
            .WhereIF(input.AgencyId.HasValue, x => x.AgencyId == input.AgencyId!.Value)
            .OrderByDescending(x => x.Id);
        return query;
    }

    /// <summary>
    /// 确认对账单
    /// </summary>
    /// <param name="input"></param>
    public async Task Confirm(ConfirmInput input)
    {
        var settlementOrder = await _dbContext.ReceiptSettlementOrders
            .FirstOrDefaultAsync(x => x.Id == input.SettlementOrderId);

        if (settlementOrder.Status != ReceiptSettlementOrderStatus.Checking)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        settlementOrder.Status = ReceiptSettlementOrderStatus.ReceiptPending;
        settlementOrder.UpdateTime = DateTime.Now;
        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 收款
    /// </summary>
    /// <param name="input"></param>
    [UnitOfWork]
    public async Task Receipt(ReceiptInput input, CurrentUser user)
    {
        var settlementOrder = await _dbContext.ReceiptSettlementOrders
            .IgnoreQueryFilters()
            .WhereIF(user.tenant is > 0, x => x.TenantId == user.tenant!.Value)
            .FirstAsync(x => x.Id == input.SettlementOrderId);

        if (settlementOrder.Status != ReceiptSettlementOrderStatus.ReceiptPending
             && settlementOrder.Status != ReceiptSettlementOrderStatus.Receipting)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        //查询商户币种
        var tenantConfig = (await GetTenantSysConfig(settlementOrder.TenantId)).FirstOrDefault();
        //当应收款为0时,这时实际收款金额不允许修改，只能填写0，币种不可修改，固定为商户基础货币币种。
        if (settlementOrder.TotalAmount == 0)
        {
            if (tenantConfig.CurrencyCode != settlementOrder.TotalAmountCurrencyCode)
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

            input.ReceivedAmount = settlementOrder.TotalAmount;
            input.ReceivedAmountCurrencyCode = tenantConfig.CurrencyCode;
        }
        var records = await _dbContext.ReceiptSettlementOrderRecord.AsNoTracking()
            .Where(x => x.SettlementOrderId == input.SettlementOrderId)
            .ToListAsync();
        if (records.Any() is false || records.Sum(x => x.Amount) != settlementOrder.TotalAmount)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        settlementOrder = _mapper.Map(input, settlementOrder);
        settlementOrder.PayeeId = user.userid;
        settlementOrder.PayeeName = user.nickname;
        settlementOrder.PayeeTime = input.PayeeTime;
        settlementOrder.Status = ReceiptSettlementOrderStatus.ReceiptComplete;
        settlementOrder.ReceiptCompleteTime = DateTime.Now;
        settlementOrder.ReceivedAmount = records.Sum(x => x.ReceivedAmount);
        settlementOrder.ReceivedAmountCurrencyCode = records.FirstOrDefault().ReceivedAmountCurrencyCode;
        _dbContext.SetTenantId(settlementOrder.TenantId);
    }

    /// <summary>
    /// 更新
    /// </summary>
    public async Task Update(UpdateInput input)
    {
        var settlementOrder = await _dbContext.ReceiptSettlementOrders
            .FirstOrDefaultAsync(x => x.Id == input.SettlementOrderId);

        if (settlementOrder.Status != ReceiptSettlementOrderStatus.ReceiptComplete)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        settlementOrder.PayeeTime = input.PayeeTime;
        settlementOrder.ProofImg = input.ProofImg;
        settlementOrder.Remark = input.Remark;
        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 取消
    /// </summary>
    /// <param name="input"></param>
    public async Task Cancel(CurrentUser currentUser, CancelInput input)
    {
        var settlementOrder = await _dbContext.ReceiptSettlementOrders
            .FirstOrDefaultAsync(x => x.Id == input.SettlementOrderId);

        if (settlementOrder.Status == ReceiptSettlementOrderStatus.ReceiptComplete)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var details = await _dbContext.ReceiptSettlementOrderDetails
            .Where(x => x.SettlementOrderId == input.SettlementOrderId)
            .ToListAsync();
        var dates = await _dbContext.ReceiptSettlementOrderDates
            .Where(x => x.SettlementOrderId == input.SettlementOrderId)
            .ToListAsync();
        var offsetOrderIds = details
            .Where(x => x.BusinessType == ReceiptSettlementBusinessType.OffsetOrder)
            .Select(x => x.BusinessOrderId);
        if (offsetOrderIds.Any())
        {
            var offsetOrders = await _dbContext.OffsetOrders
                .Where(x => offsetOrderIds.Contains(x.Id))
                .ToListAsync();
            foreach (var item in offsetOrders)
            {
                item.Status = OffsetOrderStatus.WaitingForSettlement;
                item.UpdaterId = currentUser.userid;
                item.UpdaterName = currentUser.nickname;
                item.UpdateTime = DateTime.Now;
            }
        }

        var reChargeOrderIds = details
            .Where(x => x.BusinessType == ReceiptSettlementBusinessType.AgencyCreditReChargeRecord)
            .Select(x => x.BusinessOrderId)
            .ToList();
        if (reChargeOrderIds.Any())
        {
            var unBindRequest = new UnBindReceiptSettlementOrderInput
            {
                ChargeRecordIds = reChargeOrderIds
            };
            var httpContent = new StringContent(JsonConvert.SerializeObject(unBindRequest),
                Encoding.UTF8, "application/json");
            await _httpClientFactory.InternalPostAsync(
                requestUri: _servicesAddress.Tenant_AgencyCreditCharge_UnBindReceiptSettlementOrder(),
                httpContent: httpContent);
        }
        //收款结算单关联流水记录取消结算
        await _agencyCreditPayService.ReceiptSettlementOrderCancel(new ReceiptSettlementOrderCancelInput
        {
            AgencyId = settlementOrder.AgencyId,
            OrderId = settlementOrder.Id
        });

        _dbContext.Remove(settlementOrder);
        _dbContext.RemoveRange(dates);
        _dbContext.RemoveRange(details);
        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 导出
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<ExportDetailOutput> Export(ExportDetailInput input, long tenantId)
    {
        var result = new ExportDetailOutput();
        var settlementOrder = await _dbContext.ReceiptSettlementOrders.AsNoTracking()
            .IgnoreQueryFilters()
            .WhereIF(tenantId > 0, x => x.TenantId == tenantId)
            .Where(x => x.Id == input.SettlementOrderId)
            .FirstOrDefaultAsync();

        result.SettlementOrderId = settlementOrder.Id;
        result.AgencyId = settlementOrder.AgencyId;
        result.AccountName = settlementOrder.AccountName;
        result.BankAccount = settlementOrder.BankAccount;
        result.BankCode = settlementOrder.BankCode;
        result.BankName = settlementOrder.BankName;
        result.BillingCycleBegin = settlementOrder.BillingCycleBegin;
        result.BillingCycleEnd = settlementOrder.BillingCycleEnd;
        result.OrderCount = settlementOrder.OrderCount;
        result.TotalAmount = settlementOrder.TotalAmount;
        result.OrderAmount = settlementOrder.OrderAmount;
        result.RechargeAmount = settlementOrder.RechargeAmount;
        result.TotalAmountCurrencyCode = settlementOrder.TotalAmountCurrencyCode;
        result.ReceivedAmount = settlementOrder.ReceivedAmount;
        result.ReceivedAmountCurrencyCode = settlementOrder.ReceivedAmountCurrencyCode;
        result.Status = settlementOrder.Status;

        var settlementDates = await _dbContext.ReceiptSettlementOrderDates.AsNoTracking()
            .IgnoreQueryFilters()
            .Where(x => x.SettlementOrderId == input.SettlementOrderId)
            .ToListAsync();

        var orderTypes = new List<OrderType>() {
            OrderType.Hotel,
            OrderType.TravelLineOrder,
            OrderType.ScenicTicket,
        };

        foreach (var businessType in settlementDates.GroupBy(x => x.BusinessType))
        {
            var dateType = businessType.First().OrderDateType;
            switch (businessType.Key)
            {
                case ReceiptSettlementBusinessType.HotelOrder:
                    result.Hotel = await GetBusiness<ReceivablesHotelOrderOutput>(input.SettlementOrderId,
                        dateType, businessType.Key);
                    break;
                case ReceiptSettlementBusinessType.TicketOrder:
                    result.Ticket = await GetBusiness<ReceivablesTicketOrderOutput>(input.SettlementOrderId,
                        dateType, businessType.Key);
                    break;
                case ReceiptSettlementBusinessType.ReservationOrder:
                    result.Reservation = await GetBusiness<ReceivablesReservationOrderOutput>(input.SettlementOrderId,
                        dateType, businessType.Key);
                    break;
                case ReceiptSettlementBusinessType.ScenicTicketOrder:
                    result.ScenicTicket = await GetBusiness<ReceivablesScenicOrderOutput>(input.SettlementOrderId,
                        dateType, businessType.Key);
                    break;
                case ReceiptSettlementBusinessType.LineOrder:
                    result.Line = await GetBusiness<ReceivablesLineOrderOutput>(input.SettlementOrderId,
                        dateType, businessType.Key);
                    break;
                case ReceiptSettlementBusinessType.CarProduct:
                    result.CarProduct = await GetBusiness<ReceivablesCarProductOrderOutput>(input.SettlementOrderId,
                        dateType, businessType.Key);
                    break;
                case ReceiptSettlementBusinessType.RefundOrder:
                    result.Refund = await GetBusiness<ReceivablesRefundOrderOutput>(input.SettlementOrderId,
                        dateType, businessType.Key);
                    break;
                case ReceiptSettlementBusinessType.OffsetOrder:
                    result.OffsetOrder = await GetBusiness<ReceivablesOffsetOrderOutput>(input.SettlementOrderId,
                        dateType, businessType.Key);
                    break;
                case ReceiptSettlementBusinessType.AgencyCreditReChargeRecord:
                    result.CreditRechargeOrder = await GetBusiness<ReceivablesAgencyCreditRechargeOutput>(input.SettlementOrderId,
                        dateType, businessType.Key);
                    break;
                default:
                    throw new ArgumentException($"SettlementBusinessType {businessType.Key} is not valid");
            }
        }

        //只做hotel，抵充单,退款单 显示在酒店日历房详情里（当酒店主订单不在该结算单内），但不影响其汇总信息
        var hotelBaseOrderIds = result.Hotel.Order.Select(x => x.BaseOrderId).ToList();
        var hotelRefundOrders = result.Refund.Order
            .Where(x => x.OrderType == RefundOrderType.Hotel);
        var hotelOffsetOrders = result.OffsetOrder.Order
            .Where(x => x.OffsetOrderBusinessType == OffsetOrderBusinessType.HotelOrder);
        if (hotelBaseOrderIds.Any())
        {
            hotelRefundOrders = hotelRefundOrders.Where(x => !hotelBaseOrderIds.Contains(x.BaseOrderId));
            hotelOffsetOrders = hotelOffsetOrders.Where(x => !hotelBaseOrderIds.Contains(x.BaseOrderId));
        }
        if (hotelRefundOrders.Any() || hotelOffsetOrders.Any())
        {
            var outHotelBaseOrderIds = new List<long>();
            if (hotelRefundOrders.Any())
                outHotelBaseOrderIds.AddRange(hotelRefundOrders.Select(x => x.BaseOrderId).ToList());
            if (hotelOffsetOrders.Any())
                outHotelBaseOrderIds.AddRange(hotelOffsetOrders.Select(x => x.BaseOrderId).ToList());
            outHotelBaseOrderIds = outHotelBaseOrderIds.Distinct().ToList();
            if (outHotelBaseOrderIds.Any())
            {
                var hotelQuery = from hotelOrder in _dbContext.HotelOrders.IgnoreQueryFilters()
                                 join baseOrder in _dbContext.BaseOrders.IgnoreQueryFilters() on hotelOrder.BaseOrderId
                                     equals baseOrder.Id
                                 where outHotelBaseOrderIds.Contains(hotelOrder.BaseOrderId)
                                 select new { hotelOrder, baseOrder };

                var hotelOrders = await hotelQuery.Select(x => new ReceivablesHotelOrderOutput
                {
                    BaseOrderId = x.baseOrder.Id,
                    UserId = x.baseOrder.UserId,
                    UserNickName = x.baseOrder.UserNickName,
                    AgencyId = x.baseOrder.AgencyId,
                    BuyerName = x.baseOrder.AgencyName,
                    TotalAmount = 0,//默认应收为0
                    CreateOrderDate = x.baseOrder.CreateTime,
                    Traveler = x.baseOrder.ContactsName,
                    ChannelOrderNo = x.baseOrder.ChannelOrderNo,
                    GroupNo = x.baseOrder.GroupNo,
                    OrderId = x.hotelOrder.Id,
                    HotelName = x.hotelOrder.HotelName,
                    ProductName = x.hotelOrder.HotelRoomName,
                    ProductSkuName = x.hotelOrder.PriceStrategyName,
                    Quantity = x.hotelOrder.PriceStrategyRoomsCount,
                    NightsCount = x.hotelOrder.PriceStrategyNightsCount,
                    CheckInDate = x.hotelOrder.CheckInDate,
                    CheckOutDate = x.hotelOrder.CheckOutDate,
                    ConfirmCode = x.hotelOrder.ConfirmCode,
                    Status = x.hotelOrder.Status,
                    IsStatistics = false,
                }).ToListAsync();

                if (hotelOrders.Any())
                {
                    hotelOrders.ForEach(x =>
                    {
                        x.RefundTotalAmount = hotelRefundOrders.Where(o => o.BaseOrderId == x.BaseOrderId).Sum(o => o.TotalAmount);
                        x.OffsetTotalAmount = hotelOffsetOrders.Where(o => o.BaseOrderId == x.BaseOrderId).Sum(o => o.TotalAmount);
                        x.ReceivedAmount = hotelRefundOrders.Where(o => o.BaseOrderId == x.BaseOrderId).Sum(o => o.TotalAmount) +
                            hotelOffsetOrders.Where(o => o.BaseOrderId == x.BaseOrderId).Sum(o => o.TotalAmount);
                    });
                    result.Hotel.Order = result.Hotel.Order.Concat(hotelOrders);
                }
            }
        }

        return result;
    }

    private async Task<Business<T>> GetBusiness<T>(long settlementOrderId,
        ReceiptDateTye dateTye,
        ReceiptSettlementBusinessType businessType) where T : ReceivablesInfo
    {
        IEnumerable<T> order = Enumerable.Empty<T>();

        var settlementOrderDetails = _dbContext.ReceiptSettlementOrderDetails.IgnoreQueryFilters()
            .Where(x => x.SettlementOrderId == settlementOrderId)
            .Where(x => x.BusinessType == businessType);

        switch (businessType)
        {
            case ReceiptSettlementBusinessType.HotelOrder:
                var hotelQuery = from detail in settlementOrderDetails
                                 join hotelOrder in _dbContext.HotelOrders.IgnoreQueryFilters() on detail.BusinessOrderId
                                     equals hotelOrder.Id
                                 join baseOrder in _dbContext.BaseOrders.IgnoreQueryFilters() on detail.BaseOrderId
                                     equals baseOrder.Id
                                 select new { detail, hotelOrder, baseOrder };

                var hotelOrders = await hotelQuery.Select(x => new ReceivablesHotelOrderOutput
                {
                    BaseOrderId = x.baseOrder.Id,
                    UserId = x.baseOrder.UserId,
                    UserNickName = x.baseOrder.UserNickName,
                    AgencyId = x.baseOrder.AgencyId,
                    BuyerName = x.baseOrder.AgencyName,
                    TotalAmount = x.baseOrder.PaymentAmount,
                    CreateOrderDate = x.baseOrder.CreateTime,
                    Traveler = x.baseOrder.ContactsName,
                    ChannelOrderNo = x.baseOrder.ChannelOrderNo,
                    GroupNo = x.baseOrder.GroupNo,

                    OrderId = x.hotelOrder.Id,
                    HotelName = x.hotelOrder.HotelName,
                    ProductName = x.hotelOrder.HotelRoomName,
                    ProductSkuName = x.hotelOrder.PriceStrategyName,
                    Quantity = x.hotelOrder.PriceStrategyRoomsCount,
                    NightsCount = x.hotelOrder.PriceStrategyNightsCount,
                    CheckInDate = x.hotelOrder.CheckInDate,
                    CheckOutDate = x.hotelOrder.CheckOutDate,
                    ConfirmCode = x.hotelOrder.ConfirmCode,
                    Status = x.hotelOrder.Status,
                    PriceSourceType = x.hotelOrder.SupplierApiType == SupplierApiType.None ? HotelPriceSourceType.HotelLocal :
                                    (x.hotelOrder.SupplierApiType == SupplierApiType.Hop ?
                                                (x.hotelOrder.IsDirect == true ? HotelPriceSourceType.HotelHopMain : HotelPriceSourceType.HotelHopNonMain)
                                                : null
                                     ),
                }).ToListAsync();

                //查询订单的入住人信息
                var baseOrderIds = hotelOrders.Select(x => x.BaseOrderId).ToList();
                var hotelGuests = await _dbContext.HotelOrderGuests.IgnoreQueryFilters()
                    .Where(x => baseOrderIds.Contains(x.BaseOrderId))
                    .Select(x => new
                    {
                        x.BaseOrderId,
                        x.GuestName
                    }).ToListAsync();
                foreach (var item in hotelOrders)
                {
                    //多个入住人逗号分隔
                    var guestNames = hotelGuests
                        .Where(x => x.BaseOrderId == item.BaseOrderId)
                        .Select(x => x.GuestName)
                        .ToList();
                    item.HotelGuestNames = guestNames.Any() ? string.Join(",", guestNames) : string.Empty;
                }

                order = (IEnumerable<T>)hotelOrders;

                break;
            case ReceiptSettlementBusinessType.TicketOrder:
                var ticketQuery = from detail in settlementOrderDetails
                                  join ticketOrder in _dbContext.TicketOrders.IgnoreQueryFilters() on detail.BusinessOrderId
                                      equals ticketOrder.Id
                                  join baseOrder in _dbContext.BaseOrders.IgnoreQueryFilters() on detail.BaseOrderId
                                      equals baseOrder.Id
                                  select new { detail, ticketOrder, baseOrder };

                order = (IEnumerable<T>)await ticketQuery.Select(x => new ReceivablesTicketOrderOutput
                {
                    BaseOrderId = x.baseOrder.Id,
                    UserId = x.baseOrder.UserId,
                    UserNickName = x.baseOrder.UserNickName,
                    AgencyId = x.baseOrder.AgencyId,
                    BuyerName = x.baseOrder.AgencyName,
                    ProductName = x.baseOrder.ProductName,
                    ProductSkuName = x.baseOrder.ProductSkuName,
                    CreateOrderDate = x.baseOrder.CreateTime,
                    Status = x.baseOrder.Status,
                    TotalAmount = x.baseOrder.PaymentAmount,
                    UpdateTime = x.baseOrder.UpdateTime,
                    Traveler = x.baseOrder.ContactsName,

                    OrderId = x.ticketOrder.Id,
                    Quantity = x.ticketOrder.Quantity,
                    TicketBusinessType = x.ticketOrder.ProductTicketBusinessType
                }).ToListAsync();
                break;
            case ReceiptSettlementBusinessType.ReservationOrder:
                var reservationQuery = from detail in settlementOrderDetails
                                       join reservationOrder in _dbContext.ReservationOrders.IgnoreQueryFilters() on detail.BusinessOrderId
                                           equals reservationOrder.Id
                                       join baseOrder in _dbContext.BaseOrders.IgnoreQueryFilters() on detail.BaseOrderId
                                           equals baseOrder.Id
                                       select new { detail, reservationOrder, baseOrder };

                order = (IEnumerable<T>)await reservationQuery.Select(x => new ReceivablesReservationOrderOutput
                {
                    BaseOrderId = x.baseOrder.Id,
                    UserId = x.baseOrder.UserId,
                    UserNickName = x.baseOrder.UserNickName,
                    AgencyId = x.baseOrder.AgencyId,
                    BuyerName = x.baseOrder.AgencyName,
                    ProductName = x.baseOrder.ProductName,
                    ProductSkuName = x.baseOrder.ProductSkuName,
                    TotalAmount = x.detail.TotalAmount,
                    Quantity = x.detail.Quantity,

                    OrderId = x.reservationOrder.Id,
                    UpdateTime = x.reservationOrder.UpdateTime,
                    ConfirmCode = x.reservationOrder.ConfirmNumber,
                    CreateOrderDate = x.reservationOrder.CreateTime,
                    Status = x.reservationOrder.Status,
                    Traveler = x.reservationOrder.Traveler
                })
                    .ToListAsync();
                break;
            case ReceiptSettlementBusinessType.ScenicTicketOrder:
                var scenicTicketQuery = from detail in settlementOrderDetails
                                        join scenicTicketOrder in _dbContext.ScenicTicketOrders.IgnoreQueryFilters() on detail.BusinessOrderId
                                            equals scenicTicketOrder.Id
                                        join baseOrder in _dbContext.BaseOrders.IgnoreQueryFilters() on detail.BaseOrderId
                                            equals baseOrder.Id
                                        select new { detail, scenicTicketOrder, baseOrder };

                order = (IEnumerable<T>)await scenicTicketQuery.Select(x => new ReceivablesScenicOrderOutput
                {
                    BaseOrderId = x.baseOrder.Id,
                    UserId = x.baseOrder.UserId,
                    UserNickName = x.baseOrder.UserNickName,
                    AgencyId = x.baseOrder.AgencyId,
                    BuyerName = x.baseOrder.AgencyName,
                    ProductName = x.baseOrder.ProductName,
                    ProductSkuName = x.baseOrder.ProductSkuName,
                    ResourceName = x.baseOrder.ResourceName,
                    TotalAmount = x.baseOrder.PaymentAmount,
                    CreateOrderDate = x.baseOrder.CreateTime,
                    UpdateTime = x.baseOrder.UpdateTime,
                    Status = x.baseOrder.Status,
                    ContactsName = x.baseOrder.ContactsName,
                    ChannelOrderNo = x.baseOrder.ChannelOrderNo,

                    OrderId = x.scenicTicketOrder.Id,
                    Quantity = x.scenicTicketOrder.Quantity
                })
                    .ToListAsync();
                break;
            case ReceiptSettlementBusinessType.LineOrder:
                var lineQuery = from detail in settlementOrderDetails
                                join lineOrder in _dbContext.TravelLineOrder.IgnoreQueryFilters() on detail.BusinessOrderId equals lineOrder.Id
                                join baseOrder in _dbContext.BaseOrders.IgnoreQueryFilters() on detail.BaseOrderId equals baseOrder.Id
                                select new { detail, lineOrder, baseOrder };
                order = (IEnumerable<T>)await lineQuery
                    .Select(x => new ReceivablesLineOrderOutput
                    {
                        BaseOrderId = x.detail.BaseOrderId,
                        UserId = x.baseOrder.UserId,
                        UserNickName = x.baseOrder.UserNickName,
                        OrderId = x.detail.BusinessOrderId,
                        AgencyId = x.detail.AgencyId,
                        TotalAmount = x.detail.TotalAmount,
                        Quantity = x.detail.Quantity,

                        Status = x.baseOrder.Status,
                        CreateOrderDate = x.baseOrder.CreateTime,
                        UpdateTime = x.baseOrder.UpdateTime,
                        ContactsName = x.baseOrder.ContactsName,
                        ProductName = x.baseOrder.ProductName,
                        ProductSkuName = x.baseOrder.ProductSkuName,
                        ChannelOrderNo = x.baseOrder.ChannelOrderNo,

                        TravelBeginDate = x.lineOrder.TravelBeginDate
                    })
                    .ToListAsync();
                break;
            case ReceiptSettlementBusinessType.CarProduct:
                var carProductQuery = from detail in settlementOrderDetails
                                      join carProductOrder in _dbContext.CarProductOrders.IgnoreQueryFilters() on detail.BusinessOrderId equals carProductOrder.Id
                                      join baseOrder in _dbContext.BaseOrders.IgnoreQueryFilters() on detail.BaseOrderId equals baseOrder.Id
                                      select new { detail, carProductOrder, baseOrder };
                order = (IEnumerable<T>)await carProductQuery
                    .Select(x => new ReceivablesCarProductOrderOutput
                    {
                        BaseOrderId = x.detail.BaseOrderId,
                        UserId = x.baseOrder.UserId,
                        UserNickName = x.baseOrder.UserNickName,
                        OrderId = x.detail.BusinessOrderId,
                        AgencyId = x.detail.AgencyId,
                        TotalAmount = x.detail.TotalAmount,
                        Quantity = x.detail.Quantity,

                        Status = x.baseOrder.Status,
                        CreateOrderDate = x.baseOrder.CreateTime,
                        UpdateTime = x.baseOrder.UpdateTime,
                        ContactsName = x.baseOrder.ContactsName,
                        ProductName = x.baseOrder.ProductName,
                        ProductSkuName = x.baseOrder.ProductSkuName,
                        ChannelOrderNo = x.baseOrder.ChannelOrderNo,

                        TravelDate = x.carProductOrder.TravelDate
                    })
                    .ToListAsync();
                break;
            case ReceiptSettlementBusinessType.RefundOrder:
                var refundQuery = from detail in settlementOrderDetails
                                  join refundOrder in _dbContext.RefundOrders.IgnoreQueryFilters() on detail.BusinessOrderId
                                      equals refundOrder.Id
                                  join baseOrder in _dbContext.BaseOrders.IgnoreQueryFilters() on detail.BaseOrderId
                                      equals baseOrder.Id
                                  select new { detail, refundOrder, baseOrder };

                order = (IEnumerable<T>)await refundQuery.Select(x => new ReceivablesRefundOrderOutput
                {
                    BaseOrderId = x.baseOrder.Id,
                    UserId = x.baseOrder.UserId,
                    UserNickName = x.baseOrder.UserNickName,
                    AgencyId = x.baseOrder.AgencyId,
                    BuyerName = x.baseOrder.AgencyName,
                    ProductName = x.baseOrder.ProductName,
                    ProductSkuName = x.baseOrder.ProductSkuName,
                    Traveler = x.baseOrder.ContactsName,

                    OrderId = x.refundOrder.Id,
                    Quantity = x.refundOrder.Quantity,
                    TotalAmount = -x.refundOrder.TotalAmount,
                    CreateOrderDate = x.refundOrder.CreateTime,
                    UpdateTime = x.refundOrder.UpdateTime,
                    OrderType = x.refundOrder.OrderType
                })
                    .ToListAsync();
                break;
            case ReceiptSettlementBusinessType.OffsetOrder:
                var offsetOrderQuery = from detail in settlementOrderDetails
                                       join offsetOrder in _dbContext.OffsetOrders.IgnoreQueryFilters() on detail.BusinessOrderId
                                           equals offsetOrder.Id
                                       select new { detail, offsetOrder };

                order = (IEnumerable<T>)await offsetOrderQuery.Select(x => new ReceivablesOffsetOrderOutput
                {
                    AgencyId = x.detail.AgencyId,
                    TotalAmount = x.detail.TotalAmount,
                    BaseOrderId = x.offsetOrder.BaseOrderId,
                    OrderId = x.offsetOrder.Id,
                    OffsetOrderBusinessType = x.offsetOrder.BusinessType,
                    ProductName = x.offsetOrder.ProductName,
                    ProductSkuName = x.offsetOrder.ProductSkuName,
                    CreatorName = x.offsetOrder.CreatorName,
                    UpdaterName = x.offsetOrder.UpdaterName,
                    CreateOrderDate = x.offsetOrder.CreatTime
                })
                    .ToListAsync();
                break;
            case ReceiptSettlementBusinessType.AgencyCreditReChargeRecord:
                order = (IEnumerable<T>)await settlementOrderDetails
                    .Select(x => new ReceivablesAgencyCreditRechargeOutput
                    {
                        AgencyId = x.AgencyId,
                        TotalAmount = x.TotalAmount,
                        BaseOrderId = x.BaseOrderId,
                        OrderId = x.BusinessOrderId
                    })
                    .ToListAsync();
                break;
            default:
                throw new ArgumentException($"SettlementBusinessType {businessType} is not valid");
        }

        return new Business<T>
        {
            BusinessType = businessType,
            OrderDateType = dateTye,
            OrderCount = order.Count(),
            TotalAmount = order.Sum(x => x.TotalAmount),
            Order = order
        };
    }

    /// <summary>
    /// 通过订单id查询应收结算单数据
    /// </summary>
    /// <param name="orderId"></param>
    /// <returns></returns>
    public async Task<IEnumerable<GetReceivableOrderDetailOutput>> GetByOrderId(GetReceivableOrderDetailInput input)
    {
        var result = new List<GetReceivableOrderDetailOutput>();
        var settlementOrderDetailList = await _dbContext.ReceiptSettlementOrderDetails.AsNoTracking()
            .WhereIF(input.SettlementOrderId.HasValue, x => x.SettlementOrderId == input.SettlementOrderId!.Value)
            .WhereIF(input.BusinessOrderIds.Any(), x => input.BusinessOrderIds.Contains(x.BusinessOrderId))
            .WhereIF(input.BaseOrderIds.Any(), x => input.BaseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();

        if (settlementOrderDetailList.Any() is false) return result;

        var settlementOrderIds = settlementOrderDetailList.Select(x => x.SettlementOrderId);
        var settlementOrderList = await _dbContext.ReceiptSettlementOrders.AsNoTracking()
            .Where(x => settlementOrderIds.Contains(x.Id))
            .ToListAsync();

        foreach (var detail in settlementOrderDetailList)
        {
            var settlementOrder = settlementOrderList.First(x => x.Id == detail.SettlementOrderId);
            var resultItem = new GetReceivableOrderDetailOutput();
            //结算单收款已完成
            if (settlementOrder.Status == ReceiptSettlementOrderStatus.ReceiptComplete)
            {
                if (settlementOrder.TotalAmount == 0)
                {
                    //当应收款为0时，分销商结算货币与商户基础货币相同时，各订单的实收金额=应收金额
                    resultItem.ReceivedAmount = detail.TotalAmount;
                    resultItem.ReceivedAmountCurrencyCode = settlementOrder.ReceivedAmountCurrencyCode;
                }
                else
                {
                    //计算订单的实际入账金额(暂时处理)
                    var rate = (settlementOrder.ReceivedAmount is null || settlementOrder.ReceivedAmount == 0)
                               || settlementOrder.TotalAmount == 0
                        ? 0
                        : settlementOrder.ReceivedAmount / settlementOrder.TotalAmount;
                    resultItem.ReceivedAmount = Math.Round((decimal)(detail.TotalAmount * rate), 2);
                    resultItem.ReceivedAmountCurrencyCode = settlementOrder.ReceivedAmountCurrencyCode;
                }
            }

            resultItem.SettlementOrderId = detail.SettlementOrderId;
            resultItem.BusinessOrderId = detail.BusinessOrderId;
            resultItem.TotalAmount = detail.TotalAmount;
            resultItem.BusinessType = detail.BusinessType;
            resultItem.BaseOrderId = detail.BaseOrderId;

            resultItem.TotalAmountCurrencyCode = settlementOrder.TotalAmountCurrencyCode;
            resultItem.Status = settlementOrder.Status;
            resultItem.AgencyId = settlementOrder.AgencyId;

            result.Add(resultItem);
        }

        return result;
    }

    /// <summary>
    /// 收款结算单导出明细
    /// </summary>
    /// <param name="input"></param>
    public async Task<List<SearchDetailExportOutput>> SearchDetailExport(SearchDetailExportInput input)
    {
        //查询出抵充单和退款单
        var settlementOrderDetails = await _dbContext.ReceiptSettlementOrderDetails.AsNoTracking()
            .Join(_dbContext.BaseOrders, r => r.BaseOrderId, o => o.Id, (r, o) => new { r, o })
            .WhereIF(input.ReceiptSettlementOrderId.Any(), x => input.ReceiptSettlementOrderId.Contains(x.r.SettlementOrderId))
            .Where(x => x.r.BusinessType == ReceiptSettlementBusinessType.RefundOrder || x.r.BusinessType == ReceiptSettlementBusinessType.OffsetOrder)
            .WhereIF(input.OrderTypes.Any(), x => input.OrderTypes.Contains(x.o.OrderType))
            .Select(x => x.r)
            .ToListAsync();

        var result = _mapper.Map<List<SearchDetailExportOutput>>(settlementOrderDetails);
        return result;
    }
    private async Task<List<GetTenantSysConfigOutput>> GetTenantSysConfig(params long[] tenantIds)
    {
        var request = tenantIds.ToList();
        var content = new StringContent(JsonConvert.SerializeObject(tenantIds), Encoding.UTF8, "application/json");
        var response = await _httpClientFactory.InternalPostAsync<List<GetTenantSysConfigOutput>>(
            requestUri: _servicesAddress.Tenant_Tenant_GetSysConfigByTenantIds(),
            httpContent: content);
        return response;
    }

    public async Task<IEnumerable<ReceiptSettlementOrderRemindOutput>> GetRemindDatas(SearchInput input, CurrentUser user)
    {
        var remindKey = string.Format(_remindKey, user.GetAgencyId(), user.userid);
        if (_redisClient.KeyExists(remindKey))
            return new List<ReceiptSettlementOrderRemindOutput>();

        var result = await ReceiptSettlementOrdersForRemindQuery(input)
            .Select(x => new ReceiptSettlementOrderRemindOutput
            {
                CreateTime = x.CreatTime,
                AgencyId = x.AgencyId,
                ReceiptSettlementOrderId = x.Id,
            })
            .ToListAsync();

        return result;
    }

    public async Task<IEnumerable<GetDisableAgencyOutput>> GetDisableAgency(GetDisableAgencyInput input)
    {
        var result = await _dbContext.ReceiptSettlementOrders.IgnoreQueryFilters().AsNoTracking()
            .Where(x => x.Status == ReceiptSettlementOrderStatus.ReceiptPending)
            .Where(x => x.CreatTime <= input.CreateTime)
            .GroupBy(x => x.AgencyId)
            .Select(x => new GetDisableAgencyOutput
            {
                AgencyId = x.Key
            })
            .ToListAsync();

        return result;
    }

    public async Task ReadRemindData(ReadRemindDataInput input)
    {
        var remindKey = string.Format(_remindKey, input.AgencyId, input.UserId);
        if (_redisClient.KeyExists(remindKey))
            return;
        var timeSpan = DateTime.Today.AddDays(1) - DateTime.Now;
        _redisClient.StringSet(remindKey, true, timeSpan);
    }

    #region 收款记录
    public async Task<IEnumerable<GetReceiptSettlementOrderRecordsOutput>> GetReceiptSettlementOrderRecords(GetReceiptSettlementOrderRecordsInput input)
    {
        var datas = await _dbContext.ReceiptSettlementOrderRecord.AsNoTracking()
            .Where(x => input.SettlementOrderIds.Contains(x.SettlementOrderId))
            .ToListAsync();

        var result = _mapper.Map<List<GetReceiptSettlementOrderRecordsOutput>>(datas);
        return result;
    }

    [UnitOfWork]
    public async Task<long> AddReceiptSettlementOrderRecord(AddReceiptSettlementOrderRecordInput input, CurrentUser user)
    {
        var settlementOrder = await _dbContext.ReceiptSettlementOrders
            .Where(x => x.Id == input.SettlementOrderId)
            .FirstOrDefaultAsync();
        var receiptSettlementOrderRecords = await _dbContext.ReceiptSettlementOrderRecord.AsNoTracking()
            .Where(x => x.SettlementOrderId == input.SettlementOrderId)
            .ToListAsync();
        if (receiptSettlementOrderRecords.Sum(x => x.Amount) + input.Amount > settlementOrder.TotalAmount)
            throw new BusinessException(ErrorTypes.Order.ReceivedAmountNotMoreThan);
        if (receiptSettlementOrderRecords.Count > 0)
        {
            if (input.ReceivedAmountCurrencyCode != receiptSettlementOrderRecords.First().ReceivedAmountCurrencyCode)
            {
                throw new BusinessException(ErrorTypes.Order.ReceivedAmountCurrencyCodeChanged);
            }
        }
        var receiptSettlementOrderRecord = _mapper.Map<ReceiptSettlementOrderRecord>(input);
        receiptSettlementOrderRecord.PayeeId = user.userid;
        receiptSettlementOrderRecord.PayeeName = user.nickname;
        receiptSettlementOrderRecord.CreatTime = DateTime.Now;

        settlementOrder.ReceivedAmount = settlementOrder.ReceivedAmount.HasValue ?
            settlementOrder.ReceivedAmount + receiptSettlementOrderRecord.ReceivedAmount :
            receiptSettlementOrderRecord.ReceivedAmount;
        settlementOrder.ReceivedAmountCurrencyCode = receiptSettlementOrderRecord.ReceivedAmountCurrencyCode;
        settlementOrder.PayeeId = user.userid;
        settlementOrder.PayeeName = user.nickname;
        settlementOrder.PayeeTime = input.PayeeTime;
        settlementOrder.Status = ReceiptSettlementOrderStatus.Receipting;

        await _dbContext.AddAsync(receiptSettlementOrderRecord);

        //款结算单确认收款后，需要增加授信额度流水
        AgencyCreditReceiptSettlementOrderPayMessage agencyCreditReceiptSettlementOrderPayMessage = new()
        {
            UniqueOrderId = 0,
            AgencyId = settlementOrder.AgencyId,
            CreditBusinessType = CreditBusinessType.ReceiptSettlement,
            OrderType = OrderType.None,
            OrderId = settlementOrder.Id,
            OrderAmount = receiptSettlementOrderRecord.ReceivedAmount.Value,
            OperationUser = new Contracts.Common.Tenant.DTOs.OperationUserDto
            {
                Name = user.nickname,
                UserId = user.userid,
                UserType = Contracts.Common.Tenant.Enums.UserType.Merchant
            }
        };
        await _capPublisher.PublishAsync(CapTopics.Tenant.AgencyCreditReceiptSettlementOrderPay, agencyCreditReceiptSettlementOrderPayMessage);

        //收款流水
        await _capPublisher.PublishAsync(CapTopics.Payment.TenantReceiptFlow, new TenantReceiptFlowMessage
        {
            TenantId = settlementOrder.TenantId,
            AgencyId = settlementOrder.AgencyId,
            Amount = receiptSettlementOrderRecord.ReceivedAmount.Value,
            BusinessOrderId = settlementOrder.Id,
            CreateTime = receiptSettlementOrderRecord.CreatTime,
            PayType = PayType.Offline,
            TenantReceiptFlowType = TenantReceiptFlowType.ReceiptSettlementOrder,
            TenantBankAccount = new Contracts.Common.Payment.DTOs.TenantReceiptFlow.TenantBankAccountDto
            {
                AccountNo = receiptSettlementOrderRecord?.BankAccount,
                BankName = receiptSettlementOrderRecord?.BankName
            }
        });

        var log = await CreateLog(input);
        log.OperationType = OperationType.Edit;
        log.TabLogType = TabLogType.ReceiptSettlementOrder;
        log.KeyId = settlementOrder.Id;
        log.TenantId = settlementOrder.TenantId;
        log.Content = $"账单收款，收款金额：{receiptSettlementOrderRecord.ReceivedAmount} {receiptSettlementOrderRecord.ReceivedAmountCurrencyCode}" +
            $",收款账户：{receiptSettlementOrderRecord.BankName} {receiptSettlementOrderRecord.BankAccount}";
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent()
        {
            NewData = input
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);

        return receiptSettlementOrderRecord.Id;
    }

    #endregion

    #region 发送邮件
    public async Task<ExportDetailOutput> ExportBySendEmail(SendEmailReceiptSettlementOrderInput message)
    {
        //查询出抵充单和退款单
        var orderTypes = new List<OrderType> {
            OrderType.Hotel,
            OrderType.TravelLineOrder,
            OrderType.ScenicTicket,
            OrderType.CarProduct
        };
        var settlementOrderDetails = await _dbContext.ReceiptSettlementOrderDetails.IgnoreQueryFilters().AsNoTracking()
            .Join(_dbContext.BaseOrders, r => r.BaseOrderId, o => o.Id, (r, o) => new { r, o })
            .Where(x => x.r.BusinessType == ReceiptSettlementBusinessType.RefundOrder || x.r.BusinessType == ReceiptSettlementBusinessType.OffsetOrder)
            .Where(x => orderTypes.Contains(x.o.OrderType) && x.o.TenantId == message.TenantId)
            .Select(x => x.r)
            .ToListAsync();

        var exportResponse = await Export(new ExportDetailInput
        {
            SettlementOrderId = message.SettlementOrderId,
        }, message.TenantId);

        if (exportResponse.Status != ReceiptSettlementOrderStatus.ReceiptComplete)
        {
            //默认展示配置的收款账户
            var receivableAccount = await GetReceivableAccount(message.TenantId);
            exportResponse.BankName = receivableAccount.BankName;
            exportResponse.BankCode = receivableAccount.BankCode;
            exportResponse.BankAccount = receivableAccount.AccountNo;
            exportResponse.AccountName = receivableAccount.AccountName;
            exportResponse.OpeningBankCode = receivableAccount.OpeningBankCode;
            exportResponse.BranchName = receivableAccount.BranchName;
            exportResponse.TenantBankAccountType = receivableAccount.TenantBankAccountType;
            exportResponse.BankAccountType = receivableAccount.BankAccountType;
        }
        else
        {
            var receivableAccounts = await GetTenantBankByAccount(new GetTenantBankAccountInput()
            {
                AccountName = exportResponse.AccountName,
                AccountNo = exportResponse.BankAccount,
                BankCode = exportResponse.BankCode,
            }, message.TenantId);
            var receivableAccount = receivableAccounts?.FirstOrDefault();
            exportResponse.TenantBankAccountType = receivableAccount?.TenantBankAccountType ?? 0;
            exportResponse.BankAccountType = receivableAccount?.BankAccountType ?? 0;
        }

        var reChargeOrderIds = exportResponse.CreditRechargeOrder?.Order.Select(x => x.OrderId).ToArray();
        if (reChargeOrderIds != null && reChargeOrderIds.Any())
        {
            var reChargeOrders = (await GetAgencyCreditDetails(reChargeOrderIds, message.TenantId)).ToList();
            foreach (var item in exportResponse.CreditRechargeOrder.Order)
            {
                var reChargeOrder = reChargeOrders.FirstOrDefault(x => x.Id == item.OrderId);
                if (reChargeOrder == null) continue;
                item.CreateOrderDate = reChargeOrder.CreateTime;
                item.PayType = reChargeOrder.PayType;
                item.PayChannel = reChargeOrder.PayChannel;
                item.FinishTime = reChargeOrder.ConfirmTime;
                item.PaymentCurrencyCode = reChargeOrder.CurrencyCode;
                item.AgencyName = reChargeOrder.AgencyName;
            }
        }

        var exportDetail = settlementOrderDetails.Where(x => x.SettlementOrderId == exportResponse.SettlementOrderId).ToList();
        exportResponse.Hotel.Order = FillOrderByExport<ReceivablesHotelOrderOutput>(exportResponse.Hotel.Order, exportDetail);
        exportResponse.Line.Order = FillOrderByExport<ReceivablesLineOrderOutput>(exportResponse.Line.Order, exportDetail);
        exportResponse.ScenicTicket.Order = FillOrderByExport<ReceivablesScenicOrderOutput>(exportResponse.ScenicTicket.Order, exportDetail);
        exportResponse.CarProduct.Order = FillOrderByExport<ReceivablesCarProductOrderOutput>(exportResponse.CarProduct.Order, exportDetail);
        return exportResponse;
        //发送邮件
    }

    private IEnumerable<T> FillOrderByExport<T>(IEnumerable<T> orders, List<ReceiptSettlementOrderDetail> exportDetail) where T : ReceivablesInfo
    {
        if (orders.Any() is false)
            return orders;

        foreach (var order in orders)
        {
            if (typeof(T) == typeof(ReceivablesHotelOrderOutput))
            {
                var hotelOrder = order as ReceivablesHotelOrderOutput;
                if (!hotelOrder.IsStatistics)
                    continue;
            }
            order.ReceivedAmount = order.TotalAmount;
            var detail = exportDetail.Where(x => x.BaseOrderId == order.BaseOrderId).ToList();
            if (detail.Any())
            {
                order.RefundTotalAmount = detail
                    .Where(x => x.BusinessType == ReceiptSettlementBusinessType.RefundOrder)
                    .Sum(x => x.TotalAmount);

                order.OffsetTotalAmount = detail
                    .Where(x => x.BusinessType == ReceiptSettlementBusinessType.OffsetOrder)
                    .Sum(x => x.TotalAmount);
            }
            order.ReceivedAmount = order.TotalAmount + order.RefundTotalAmount + order.OffsetTotalAmount;
        }

        return orders;
    }


    private async Task<GetTenantBankAccountOutput> GetReceivableAccount(long tenantId)
    {
        var receivableAccount = new GetTenantBankAccountOutput();
        //查询B2B设置增加默认收款账号信息
        var financialSetting = await GetFinancialSetting(tenantId);
        if (long.TryParse(financialSetting?.ReceivablesAccounts, out long receivablesAccountId))
        {
            //查询收款账号基础信息
            receivableAccount = await GetTenantBankAccount(receivablesAccountId, tenantId);
        }
        return receivableAccount;
    }

    private async Task<List<GetCreditChargeDetailsOutput>> GetAgencyCreditDetails(long[] agencyIds, long tenantId)
    {
        var httpContent = new StringContent(JsonConvert.SerializeObject(agencyIds),
            Encoding.UTF8, "application/json");
        var headers = new List<KeyValuePair<string, string>>(1) {
            new("tenant",tenantId.ToString())
        };
        var agencyCredits = await _httpClientFactory.InternalPostAsync<List<GetCreditChargeDetailsOutput>>(
            requestUri: _servicesAddress.Tenant_AgencyCredit_Details(),
            httpContent: httpContent, headers: headers);
        return agencyCredits;
    }

    private async Task<List<GetAgenciesByIdsOutput>> GetAgencyDetails(GetAgenciesByIdsInput input, long tenantId)
    {
        var headers = new List<KeyValuePair<string, string>>(1) {
            new("tenant",tenantId.ToString())
        };

        var httpContent = new StringContent(JsonConvert.SerializeObject(input),
            Encoding.UTF8, "application/json");
        var agencies = await _httpClientFactory.InternalPostAsync<List<GetAgenciesByIdsOutput>>(
            requestUri: _servicesAddress.Tenant_Agency_GetByIds(),
            httpContent: httpContent, headers: headers);
        return agencies;
    }

    private async Task<FinancialSettingDto> GetFinancialSetting(long tenantId)
    {
        var headers = new List<KeyValuePair<string, string>>(1) {
            new("tenant",tenantId.ToString())
        };
        var result = await _httpClientFactory.InternalGetAsync<FinancialSettingDto>(
            requestUri: _servicesAddress.Tenant_GetFinancialSetting(), headers: headers);
        return result;
    }
    private async Task<GetTenantBankAccountOutput> GetTenantBankAccount(long id, long tenantId)
    {
        var headers = new List<KeyValuePair<string, string>>(1) {
            new("tenant",tenantId.ToString())
        };
        var result = await _httpClientFactory.InternalGetAsync<GetTenantBankAccountOutput>(
            requestUri: _servicesAddress.Payment_GetTenantBankAccount(id), headers: headers);
        return result;
    }

    private async Task<List<GetTenantBankAccountOutput>> GetTenantBankByAccount(GetTenantBankAccountInput input, long tenantId)
    {
        var headers = new List<KeyValuePair<string, string>>(1) {
            new("tenant",tenantId.ToString())
        };
        var httpContent = new StringContent(JsonConvert.SerializeObject(input),
            Encoding.UTF8, "application/json");
        var result = await _httpClientFactory.InternalPostAsync<List<GetTenantBankAccountOutput>>(
            requestUri: _servicesAddress.Payment_GetTenantBankByAccount(),
            httpContent: httpContent, headers: headers);
        return result;
    }

    #endregion

    private async Task<OperationLogDto> CreateLog(object input)
    {
        CurrentUser currentUser = _httpContextAccessor?.HttpContext?.GetCurrentUser();
        var request = _httpContextAccessor?.HttpContext?.Request;
        var ip = _httpContextAccessor?.HttpContext?.GetRemotingIp();
        var log = new OperationLogDto()
        {
            System = SystemType.Vebk,
            Host = request?.Host.ToString(),
            Url = request?.Path.ToString(),
            Agent = request?.Headers?.UserAgent,
            Ip = ip,
            Query = request == null ? null : JsonConvert.SerializeObject(request.Query),
            Body = JsonConvert.SerializeObject(input),
            OperationUserId = currentUser?.userid,
            OperationUserName = currentUser?.nickname,
            TenantId = currentUser?.tenant
        };
        return log;
    }
}