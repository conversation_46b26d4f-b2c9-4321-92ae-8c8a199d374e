using Contracts.Common.Notify.Enums;
using Contracts.Common.Notify.Messages;
using Contracts.Common.Order.DTOs.GroupBooking;
using Contracts.Common.Order.DTOs.MessageNotify;
using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;
using DotNetCore.CAP;
using Extensions;
using Newtonsoft.Json;
using Order.Api.Services.Interfaces;
using System.Text.RegularExpressions;

namespace Order.Api.Services;

public class MessageNotifyService : IMessageNotifyService
{
    private readonly ICapPublisher _capPublisher;

    public MessageNotifyService(ICapPublisher capPublisher)
    {
        _capPublisher = capPublisher;
    }
    #region hotel

    public async Task HotelCreateOrderNotify(OrderNotifyDto<HotelCreateOrderNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var notifyDto = orderNotifyDto.NotifyDto;
        NotifyMessageProcess processCommand = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        switch (baseOrder.SellingPlatform)
        {
            case SellingPlatform.WeChatMP:
            case SellingPlatform.WeChatApplet:
                await SendHotelCreateOrderSms(processCommand, baseOrder, notifyDto);
                break;
            case SellingPlatform.System:
                await SendHotelCreateOrderSms(processCommand, baseOrder, notifyDto);
                break;
            case SellingPlatform.B2BWeb:
                {
                    var addressee = baseOrder.ContactsEmail;
                    if (!string.IsNullOrWhiteSpace(addressee))
                    {
                        processCommand.SetMessage(SendToTheRole.Customer,
                            NotifyEventSubType.Hotel_CreateOrder,
                            NotifyMode.Email,
                            new
                            {
                                Addressee = addressee,
                                OrderId = baseOrder.Id,
                                notifyDto.HotelName,
                                RoomName = notifyDto.HotelRoomName,
                                NumberOfBreakfast = notifyDto.PriceStrategyNumberOfBreakfast,
                                notifyDto.CheckInDate,
                                notifyDto.CheckOutDate,
                                Quantity = notifyDto.RoomsCount,
                                RoomGuests = notifyDto.Guests,
                                OrderContact = baseOrder.ContactsName,
                                baseOrder.ChannelOrderNo,
                                Remark = baseOrder.Message,
                                baseOrder.PaymentAmount,
                                baseOrder.PaymentCurrencyCode,
                                notifyDto.BoardCodeType,
                                notifyDto.BoardCount
                            });
                        await PublishMessageProcess(processCommand);
                    }

                    await SendHotelCreateOrderSms(processCommand, baseOrder, notifyDto);

                    processCommand.SetMessage(SendToTheRole.Customer,
                           NotifyEventSubType.Hotel_CreateOrder,
                           NotifyMode.Wechat,
                           new
                           {
                               baseOrder.UserId,
                               BaseOrderId = baseOrder.Id,
                               notifyDto.HotelName,
                               RoomName = notifyDto.HotelRoomName,
                               notifyDto.CheckInDate,
                               notifyDto.CheckOutDate,
                               Quantity = notifyDto.RoomsCount,
                               baseOrder.PaymentAmount,
                               baseOrder.PaymentCurrencyCode
                           });
                    await PublishMessageProcess(processCommand);
                }
                break;
        }
        processCommand.SetMessage(SendToTheRole.TenantStaff,
            NotifyEventSubType.Hotel_CreateOrder,
            NotifyMode.Wechat,
            new
            {
                baseOrder.AgencyId,
                SupplierId = notifyDto.PriceStrategySupplierId,
                notifyDto.HotelName,
                RoomName = notifyDto.HotelRoomName,
                notifyDto.CheckInDate,
                notifyDto.CheckOutDate,
                HasComfirmed = notifyDto.Status != HotelOrderStatus.WaitingForConfirm,
                BaseOrderId = baseOrder.Id
            });
        await PublishMessageProcess(processCommand);

        processCommand.SetMessage(SendToTheRole.TenantStaff,
                           NotifyEventSubType.Hotel_CreateOrder,
                           NotifyMode.Email,
                           new
                           {
                               //使用员工通知绑定人员的邮箱
                               Addressee = string.Empty,
                               OrderId = baseOrder.Id,
                               notifyDto.HotelName,
                               RoomName = notifyDto.HotelRoomName,
                               NumberOfBreakfast = notifyDto.PriceStrategyNumberOfBreakfast,
                               notifyDto.CheckInDate,
                               notifyDto.CheckOutDate,
                               Quantity = notifyDto.RoomsCount,
                               RoomGuests = notifyDto.Guests,
                               OrderContact = baseOrder.ContactsName,
                               baseOrder.ChannelOrderNo,
                               Remark = baseOrder.Message,
                               baseOrder.PaymentAmount,
                               baseOrder.PaymentCurrencyCode,
                               notifyDto.BoardCodeType,
                               notifyDto.BoardCount
                           });
        await PublishMessageProcess(processCommand);
    }

    public async Task HotelRefundSucceededNotify(OrderNotifyDto<HotelRefundSucceededNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var notifyDto = orderNotifyDto.NotifyDto;
        NotifyMessageProcess processCommand = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        switch (baseOrder.SellingPlatform)
        {
            case SellingPlatform.WeChatMP:
            case SellingPlatform.WeChatApplet:
                {
                    processCommand.SetMessage(SendToTheRole.Customer,
                        NotifyEventSubType.Hotel_RefundSucceeded,
                        NotifyMode.Wechat, new
                        {
                            baseOrder.UserId,
                            BaseOrderId = baseOrder.Id,
                            notifyDto.HotelName,
                            RoomName = notifyDto.HotelRoomName,
                            baseOrder.PaymentAmount,
                            notifyDto.RefundAmount,
                            CreateOrderTime = baseOrder.CreateTime,
                            notifyDto.CheckInDate,
                            notifyDto.CheckOutDate
                        });
                    await PublishMessageProcess(processCommand);
                    await SendHotelRefundSucceededSms(processCommand, baseOrder, notifyDto);
                }
                break;
            case SellingPlatform.System:
                await SendHotelRefundSucceededSms(processCommand, baseOrder, notifyDto);
                break;
            case SellingPlatform.B2BWeb:
                {
                    var addressee = baseOrder.ContactsEmail;
                    if (!string.IsNullOrWhiteSpace(addressee))
                    {
                        processCommand.SetMessage(SendToTheRole.Customer,
                            NotifyEventSubType.Hotel_RefundSucceeded,
                            NotifyMode.Email,
                            new
                            {
                                Addressee = addressee,
                                OrderId = baseOrder.Id,
                                notifyDto.HotelName,
                                RoomName = notifyDto.HotelRoomName,
                                NumberOfBreakfast = notifyDto.PriceStrategyNumberOfBreakfast,
                                notifyDto.CheckInDate,
                                notifyDto.CheckOutDate,
                                Quantity = notifyDto.PriceStrategyRoomsCount,
                                RoomGuests = notifyDto.Guests,
                                OrderContact = baseOrder.ContactsName,
                                baseOrder.ChannelOrderNo,
                                Remark = baseOrder.Message,
                                notifyDto.RefundAmount,
                                RefundCurrencyCode = baseOrder.PaymentCurrencyCode,
                                notifyDto.BoardCodeType,
                                notifyDto.BoardCount
                            });
                        await PublishMessageProcess(processCommand);
                    }
                    await SendHotelRefundSucceededSms(processCommand, baseOrder, notifyDto);

                    processCommand.SetMessage(SendToTheRole.Customer,
                       NotifyEventSubType.Hotel_RefundSucceeded,
                       NotifyMode.Wechat, new
                       {
                           baseOrder.UserId,
                           BaseOrderId = baseOrder.Id,
                           notifyDto.HotelName,
                           RoomName = notifyDto.HotelRoomName,
                           notifyDto.RefundAmount,
                           baseOrder.PaymentCurrencyCode,
                           Quantity = notifyDto.PriceStrategyRoomsCount,
                           notifyDto.CheckInDate,
                           notifyDto.CheckOutDate
                       });
                    await PublishMessageProcess(processCommand);
                }
                break;
        }
        processCommand.SetMessage(SendToTheRole.TenantStaff,
            NotifyEventSubType.Hotel_RefundSucceeded,
            NotifyMode.Wechat,
            new
            {
                baseOrder.AgencyId,
                SupplierId = notifyDto.PriceStrategySupplierId,
                baseOrder.TenantId,
                notifyDto.HotelName,
                RoomName = notifyDto.HotelRoomName,
                notifyDto.RefundAmount,
                BaseOrderId = baseOrder.Id
            });
        await PublishMessageProcess(processCommand);

        processCommand.SetMessage(SendToTheRole.TenantStaff,
                            NotifyEventSubType.Hotel_RefundSucceeded,
                            NotifyMode.Email,
                            new
                            {
                                //使用员工通知绑定人员的邮箱
                                Addressee = string.Empty,
                                OrderId = baseOrder.Id,
                                notifyDto.HotelName,
                                RoomName = notifyDto.HotelRoomName,
                                NumberOfBreakfast = notifyDto.PriceStrategyNumberOfBreakfast,
                                notifyDto.CheckInDate,
                                notifyDto.CheckOutDate,
                                Quantity = notifyDto.PriceStrategyRoomsCount,
                                RoomGuests = notifyDto.Guests,
                                OrderContact = baseOrder.ContactsName,
                                baseOrder.ChannelOrderNo,
                                Remark = baseOrder.Message,
                                notifyDto.RefundAmount,
                                RefundCurrencyCode = baseOrder.PaymentCurrencyCode,
                                notifyDto.BoardCodeType,
                                notifyDto.BoardCount
                            });
        await PublishMessageProcess(processCommand);
    }

    public async Task HotelOrderConfirmNotify(OrderNotifyDto<HotelOrderConfirmNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var notifyDto = orderNotifyDto.NotifyDto;
        NotifyMessageProcess processCommand = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        switch (baseOrder.SellingPlatform)
        {
            case SellingPlatform.WeChatMP:
            case SellingPlatform.WeChatApplet:
                {
                    processCommand.SetMessage(SendToTheRole.Customer,
                        NotifyEventSubType.Hotel_OrderConfirmed,
                        NotifyMode.Wechat, new
                        {
                            baseOrder.UserId,
                            BaseOrderId = baseOrder.Id,
                            notifyDto.HotelName,
                            RoomName = notifyDto.HotelRoomName,
                            notifyDto.SkuName,
                            notifyDto.CheckInDate,
                            notifyDto.CheckOutDate,
                            Quantity = notifyDto.RoomsCount
                        });
                    await PublishMessageProcess(processCommand);
                    if (!string.IsNullOrEmpty(baseOrder.ContactsPhoneNumber))
                    {
                        processCommand.SetMessage(SendToTheRole.Customer,
                           NotifyEventSubType.Hotel_OrderConfirmed,
                           NotifyMode.Sms, new
                           {
                               PhoneNumber = baseOrder.ContactsPhoneNumber,
                               HotelName = notifyDto.HotelName,
                               RoomName = notifyDto.HotelRoomName,
                               CheckInDate = notifyDto.CheckInDate,
                               CheckOutDate = notifyDto.CheckOutDate,
                               Quantity = notifyDto.RoomsCount,
                               ConfirmCode = notifyDto.ConfirmCode,
                               BaseOrderId = baseOrder.Id
                           });
                        await PublishMessageProcess(processCommand);
                    }
                }
                break;
            case SellingPlatform.System:
                await SendHotelOrderConfirmSms(processCommand, baseOrder, notifyDto);
                break;
            case SellingPlatform.B2BWeb:
            case SellingPlatform.B2BApplet:
                {
                    var addressee = baseOrder.ContactsEmail;
                    if (!string.IsNullOrWhiteSpace(addressee))
                    {
                        processCommand.SetMessage(SendToTheRole.Customer,
                            NotifyEventSubType.Hotel_OrderConfirmed,
                            NotifyMode.Email,
                            new
                            {
                                Addressee = addressee,
                                OrderId = baseOrder.Id,
                                notifyDto.HotelName,
                                RoomName = notifyDto.HotelRoomName,
                                notifyDto.NumberOfBreakfast,
                                notifyDto.CheckInDate,
                                notifyDto.CheckOutDate,
                                Quantity = notifyDto.RoomsCount,
                                RoomGuests = notifyDto.Guests,
                                OrderContact = baseOrder.ContactsName,
                                baseOrder.ChannelOrderNo,
                                Remark = baseOrder.Message,
                                baseOrder.PaymentAmount,
                                baseOrder.PaymentCurrencyCode,
                                notifyDto.BoardCodeType,
                                notifyDto.BoardCount,
                            });
                        await PublishMessageProcess(processCommand);
                    }
                    await SendHotelOrderConfirmSms(processCommand, baseOrder, notifyDto);
                    //系统消息通知
                    if (!notifyDto.IsGroupBooking)
                    {
                        processCommand.SetMessage(SendToTheRole.AgencyStaff, NotifyEventSubType.Hotel_OrderConfirmed, NotifyMode.SiteMessage,
                        new
                        {
                            BaseOrderId = baseOrder.Id,
                            AgencyId = baseOrder.AgencyId,
                            UserId = baseOrder.UserId,
                            HotelName = notifyDto.HotelName,
                            HotelRoomName = notifyDto.HotelRoomName,
                            PriceStrategyName = notifyDto.SkuName,
                        });
                        await PublishMessageProcess(processCommand);
                    }

                    processCommand.SetMessage(SendToTheRole.Customer,
                       NotifyEventSubType.Hotel_OrderConfirmed,
                       NotifyMode.Wechat, new
                       {
                           baseOrder.UserId,
                           BaseOrderId = baseOrder.Id,
                           notifyDto.HotelName,
                           RoomName = notifyDto.HotelRoomName,
                           notifyDto.CheckInDate,
                           notifyDto.CheckOutDate,
                           Quantity = notifyDto.RoomsCount
                       });
                    await PublishMessageProcess(processCommand);
                }
                break;
        }
    }
    public async Task HotelCheckInTimeReminderNotify(OrderNotifyDto<HotelCheckInTimeReminderNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var notifyDto = orderNotifyDto.NotifyDto;
        NotifyMessageProcess processCommand = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        var notifyEventSubType = NotifyEventSubType.Hotel_CheckIn_TimeReminder;
        processCommand.SetMessage(SendToTheRole.Customer, notifyEventSubType, NotifyMode.Wechat,
                new
                {
                    baseOrder.UserId,
                    BaseOrderId = baseOrder.Id,
                    notifyDto.HotelName,
                    notifyDto.RoomName,
                    notifyDto.SkuName,
                    notifyDto.CheckInDate,
                    notifyDto.CheckOutDate
                });
        await PublishMessageProcess(processCommand);
    }

    /// <summary>
    /// 发送酒店订单创建通知短信
    /// </summary>
    /// <param name="processCommand"></param>
    /// <param name="baseOrder"></param>
    /// <param name="notifyDto"></param>
    /// <returns></returns>
    private async Task SendHotelCreateOrderSms(NotifyMessageProcess processCommand, BaseOrderNotify baseOrder, HotelCreateOrderNotifyDto notifyDto)
    {
        if (string.IsNullOrWhiteSpace(baseOrder.ContactsPhoneNumber)) return;
        processCommand.SetMessage(SendToTheRole.Customer,
                        NotifyEventSubType.Hotel_CreateOrder,
                        NotifyMode.Sms,
                        new
                        {
                            PhoneNumber = baseOrder.ContactsPhoneNumber,
                            notifyDto.HotelName,
                            RoomName = notifyDto.HotelRoomName,
                            notifyDto.CheckInDate,
                            notifyDto.CheckOutDate,
                            Quantity = notifyDto.RoomsCount,
                            Nights = notifyDto.NightsCount
                        });
        await PublishMessageProcess(processCommand);
    }

    /// <summary>
    /// 发送酒店退款成功通知短信
    /// </summary>
    /// <param name="processCommand"></param>
    /// <param name="baseOrder"></param>
    /// <param name="notifyDto"></param>
    /// <returns></returns>
    private async Task SendHotelRefundSucceededSms(NotifyMessageProcess processCommand,
        BaseOrderNotify baseOrder,
        HotelRefundSucceededNotifyDto notifyDto)
    {
        if (string.IsNullOrEmpty(baseOrder.ContactsPhoneNumber)) return;
        processCommand.SetMessage(SendToTheRole.Customer,
           NotifyEventSubType.Hotel_RefundSucceeded,
           NotifyMode.Sms, new
           {
               PhoneNumber = baseOrder.ContactsPhoneNumber,
               notifyDto.HotelName,
               BaseOrderId = baseOrder.Id,
               RoomName = notifyDto.HotelRoomName,
               notifyDto.CheckInDate,
               notifyDto.CheckOutDate,
               Quantity = notifyDto.PriceStrategyRoomsCount
           });
        await PublishMessageProcess(processCommand);
    }

    /// <summary>
    /// 发送酒店订单确认通知短信
    /// </summary>
    /// <param name="processCommand"></param>
    /// <param name="baseOrder"></param>
    /// <param name="notifyDto"></param>
    /// <returns></returns>
    private async Task SendHotelOrderConfirmSms(NotifyMessageProcess processCommand,
        BaseOrderNotify baseOrder,
        HotelOrderConfirmNotifyDto notifyDto)
    {
        if (string.IsNullOrEmpty(baseOrder.ContactsPhoneNumber)) return;
        processCommand.SetMessage(SendToTheRole.Customer,
               NotifyEventSubType.Hotel_OrderConfirmed,
               NotifyMode.Sms, new
               {
                   PhoneNumber = baseOrder.ContactsPhoneNumber,
                   notifyDto.HotelName,
                   RoomName = notifyDto.HotelRoomName,
                   notifyDto.CheckInDate,
                   notifyDto.CheckOutDate,
                   Quantity = notifyDto.RoomsCount,
                   ConfirmCode = notifyDto.ConfirmCode,
                   BaseOrderId = baseOrder.Id
               });
        await PublishMessageProcess(processCommand);
    }
    #endregion

    #region ticket

    public async Task GroupPurchaseTicketCreateOrderNotify(OrderNotifyDto<GroupPurchaseTicketCreateOrderNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var ticketOrder = orderNotifyDto.NotifyDto;
        NotifyEventSubType? notifyEventSubType = null;
        if (ticketOrder.ProductNeedWriteOff && !ticketOrder.ProductNeedReservation)//仅核销
        {
            notifyEventSubType = NotifyEventSubType.Ticket_CreateOrder_NeedWriteOff;
        }
        else if (ticketOrder.ProductNeedReservation)//需要预约
        {
            notifyEventSubType = NotifyEventSubType.Ticket_CreateOrder_NeedReservation;
        }
        if (notifyEventSubType is null)
            return;

        NotifyMessageProcess processCommand = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        switch (baseOrder.SellingPlatform)
        {
            case SellingPlatform.WeChatMP:
            case SellingPlatform.WeChatApplet:
                {
                    processCommand.SetMessage(SendToTheRole.Customer,
                       notifyEventSubType.Value,
                       NotifyMode.Wechat,
                       new
                       {
                           baseOrder.UserId,
                           BaseOrderId = baseOrder.Id,
                           ticketOrder.ProductName,
                           ticketOrder.SkuName,
                           ticketOrder.Quantity,
                           baseOrder.PaymentAmount,
                           ticketOrder.SkuValidityBegin,
                           ticketOrder.SkuValidityEnd
                       });
                    await PublishMessageProcess(processCommand);

                    if (!string.IsNullOrWhiteSpace(baseOrder.ContactsPhoneNumber))
                    {
                        processCommand.SetMessage(SendToTheRole.Customer,
                           notifyEventSubType.Value,
                           NotifyMode.Sms,
                           notifyEventSubType.Value switch
                           {
                               NotifyEventSubType.Ticket_CreateOrder_NeedWriteOff =>
                               new
                               {
                                   PhoneNumber = baseOrder.ContactsPhoneNumber,
                                   BaseOrderId = baseOrder.Id,
                                   ticketOrder.ProductName,
                                   ticketOrder.SkuName,
                                   ticketOrder.Quantity,
                                   Code = ticketOrder.Code.ToString(),
                                   ValidityBegin = ticketOrder.SkuValidityBegin,
                                   ValidityEnd = ticketOrder.SkuValidityEnd
                               },
                               NotifyEventSubType.Ticket_CreateOrder_NeedReservation =>
                               new
                               {
                                   PhoneNumber = baseOrder.ContactsPhoneNumber,
                                   BaseOrderId = baseOrder.Id,
                                   ticketOrder.ProductName,
                                   ticketOrder.SkuName,
                                   ticketOrder.Quantity,
                                   ValidityBegin = ticketOrder.SkuValidityBegin,
                                   ValidityEnd = ticketOrder.SkuValidityEnd
                               }
                           });
                        await PublishMessageProcess(processCommand);
                    }
                }
                break;
            case SellingPlatform.B2BWeb:
                {
                    var addressee = baseOrder.ContactsEmail;
                    if (!string.IsNullOrWhiteSpace(addressee))
                    {
                        processCommand.SetMessage(SendToTheRole.Customer,
                            notifyEventSubType.Value,
                            NotifyMode.Email,
                            new
                            {
                                Addressee = addressee,
                                OrderId = baseOrder.Id,
                                ProductName = ticketOrder.ProductName,
                                SkuName = ticketOrder.SkuName,
                                Indate = ticketOrder.SkuValidityEnd,//
                                NeedWriteOff = ticketOrder.ProductNeedWriteOff,
                                NeedReservation = ticketOrder.ProductNeedReservation,
                                OrderContact = baseOrder.ContactsName,
                                Remark = baseOrder.Message,
                                Quantity = ticketOrder.Quantity,
                                PaymentAmount = baseOrder.PaymentAmount,
                                CreateOrderTime = baseOrder.CreateTime
                            });
                        await PublishMessageProcess(processCommand);
                    }
                }
                break;
        }
    }

    public async Task BookingTicketCreateOrderNotify(OrderNotifyDto<BookingTicketCreateOrderNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var ticketOrder = orderNotifyDto.NotifyDto;
        NotifyMessageProcess processCommand = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        switch (baseOrder.SellingPlatform)
        {
            case SellingPlatform.WeChatMP:
            case SellingPlatform.WeChatApplet:
                if (ticketOrder.ReservationOrderStatus == ReservationStatus.Confirmed)
                {
                    processCommand.SetMessage(SendToTheRole.Customer,
                        NotifyEventSubType.Ticket_Booking_Confirmed,
                        NotifyMode.Wechat,
                        new
                        {
                            ticketOrder.ProductTicketBusinessType,
                            UserId = baseOrder.UserId,
                            BaseOrderId = baseOrder.Id,
                            ProductName = ticketOrder.ProductName,
                            SkuName = ticketOrder.SkuName,
                            TravelDateBegin = ticketOrder.TravelDateBegin,
                            TravelDateEnd = ticketOrder.TravelDateEnd,
                            Quantity = ticketOrder.Quantity,
                            CustomerServicePhoneNumber = ticketOrder.AfterSalePhone,
                            ticketOrder.ResourceName
                        });
                    await PublishMessageProcess(processCommand);
                    if (!string.IsNullOrWhiteSpace(baseOrder.ContactsPhoneNumber))
                    {
                        processCommand.SetMessage(SendToTheRole.Customer,
                           NotifyEventSubType.Ticket_Booking_Confirmed,
                           NotifyMode.Sms,
                           new
                           {
                               PhoneNumber = baseOrder.ContactsPhoneNumber,
                               BaseOrderId = baseOrder.Id,
                               ProductName = ticketOrder.ProductName,
                               SkuName = ticketOrder.SkuName,
                               TravelDateBegin = ticketOrder.TravelDateBegin,
                               TravelDateEnd = ticketOrder.TravelDateEnd,
                               Quantity = ticketOrder.Quantity
                           });
                        await PublishMessageProcess(processCommand);
                    }
                }
                break;
            case SellingPlatform.B2BWeb:
                var addressee = baseOrder.ContactsEmail;
                if (!string.IsNullOrWhiteSpace(addressee))
                {
                    processCommand.SetMessage(SendToTheRole.Customer,
                       NotifyEventSubType.Ticket_Booking_CreateOrder,
                       NotifyMode.Email,
                       new
                       {
                           Addressee = addressee,
                           OrderId = baseOrder.Id,
                           ProductName = ticketOrder.ProductName,
                           SkuName = ticketOrder.SkuName,
                           Indate = ticketOrder.TravelDateEnd,//
                           NeedWriteOff = ticketOrder.ProductNeedWriteOff,
                           NeedReservation = ticketOrder.ProductNeedReservation,
                           OrderContact = baseOrder.ContactsName,
                           Remark = baseOrder.Message,
                           Quantity = ticketOrder.Quantity,
                           PaymentAmount = baseOrder.PaymentAmount,
                           CreateOrderTime = baseOrder.CreateTime
                       });
                    await PublishMessageProcess(processCommand);
                }
                break;
        }
        if (ticketOrder.ReservationOrderStatus == ReservationStatus.Confirmed)
        {
            processCommand.SetMessage(SendToTheRole.TenantStaff,
                NotifyEventSubType.Ticket_Booking_CreateOrder,
                NotifyMode.Wechat,
                new
                {
                    baseOrder.AgencyId,
                    SupplierId = ticketOrder.ProductSupplierId,
                    BaseOrderId = baseOrder.Id,
                    ticketOrder.ProductName,
                    ticketOrder.SkuName,
                    ticketOrder.TravelDateBegin,
                    ticketOrder.TravelDateEnd
                });
            await PublishMessageProcess(processCommand);

            processCommand.SetMessage(SendToTheRole.TenantStaff,
               NotifyEventSubType.Ticket_Booking_CreateOrder,
               NotifyMode.Email,
               new
               {
                   //使用员工通知绑定人员的邮箱
                   Addressee = string.Empty,
                   OrderId = baseOrder.Id,
                   ProductName = ticketOrder.ProductName,
                   SkuName = ticketOrder.SkuName,
                   SkuValidityEnd = ticketOrder.SkuValidityEnd,
                   SkuValidityBegin = ticketOrder.SkuValidityBegin,
                   NeedWriteOff = ticketOrder.ProductNeedWriteOff,
                   NeedReservation = ticketOrder.ProductNeedReservation,
                   OrderContact = baseOrder.ContactsName,
                   Remark = baseOrder.Message,
                   Quantity = ticketOrder.Quantity,
                   PaymentAmount = baseOrder.PaymentAmount,
                   CreateOrderTime = baseOrder.CreateTime
               });
            await PublishMessageProcess(processCommand);
        }
    }

    public async Task TicketReservationConfirmNotify(OrderNotifyDto<TicketReservationConfirmNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var notifyDto = orderNotifyDto.NotifyDto;
        NotifyMessageProcess processCommand = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        NotifyEventSubType notifyEventSubType = NotifyEventSubType.Ticket_ReservationConfirm;
        switch (baseOrder.SellingPlatform)
        {
            case SellingPlatform.WeChatMP:
            case SellingPlatform.WeChatApplet:
                processCommand.SetMessage(SendToTheRole.Customer,
                    notifyEventSubType,
                    NotifyMode.Wechat,
                    new
                    {
                        notifyDto.ProductTicketBusinessType,
                        baseOrder.UserId,
                        BaseOrderId = baseOrder.Id,
                        notifyDto.ResourceName,
                        notifyDto.ProductName,
                        notifyDto.SkuName,
                        notifyDto.TravelDateBegin,
                        notifyDto.TravelDateEnd,
                        notifyDto.Quantity,
                        CustomerServicePhoneNumber = notifyDto.AfterSalePhone
                    });
                await PublishMessageProcess(processCommand);
                if (!string.IsNullOrWhiteSpace(baseOrder.ContactsPhoneNumber))
                {
                    processCommand.SetMessage(SendToTheRole.Customer,
                    notifyEventSubType,
                    NotifyMode.Sms,
                    new
                    {
                        PhoneNumber = baseOrder.ContactsPhoneNumber,
                        BaseOrderId = baseOrder.Id,
                        ProductName = notifyDto.ProductName,
                        SkuName = notifyDto.SkuName,
                        TravelDateBegin = notifyDto.TravelDateBegin,
                        TravelDateEnd = notifyDto.TravelDateEnd,
                        Quantity = notifyDto.Quantity
                    });
                    await PublishMessageProcess(processCommand);
                }
                break;
            case SellingPlatform.B2BWeb:
                var addressee = baseOrder.ContactsEmail;
                if (!string.IsNullOrWhiteSpace(addressee))
                {
                    processCommand.SetMessage(SendToTheRole.Customer,
                        notifyEventSubType,
                        NotifyMode.Email,
                        new
                        {
                            Addressee = addressee,
                            OrderId = baseOrder.Id,
                            ProductName = notifyDto.ProductName,
                            SkuName = notifyDto.SkuName,
                            TravelDateBegin = notifyDto.TravelDateBegin,
                            TravelDateEnd = notifyDto.TravelDateEnd,
                            Nights = notifyDto.Nights,
                            Quantity = notifyDto.Quantity,
                            Travelers = new string[] { notifyDto.Traveler },
                            Amount = notifyDto.Amount
                        });
                    await PublishMessageProcess(processCommand);
                }
                break;
        }
    }

    public async Task TicketCodeUsedNotify(OrderNotifyDto<TicketCodeUsedNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var notifyDto = orderNotifyDto.NotifyDto;
        NotifyMessageProcess command = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        command.SetMessage(SendToTheRole.Customer,
            NotifyEventSubType.Ticket_CodeUsed,
            NotifyMode.Wechat,
            new
            {
                baseOrder.UserId,
                BaseOrderId = baseOrder.Id,
                notifyDto.ProductName,
                notifyDto.SkuName,
                OrderContact = baseOrder.ContactsName,
                notifyDto.Quantity,
                CreateOrderTime = DateTime.Now,
                notifyDto.ResourceName
            });
        await PublishMessageProcess(command);
    }

    public async Task TicketReservationCanceledNotify(OrderNotifyDto<TicketReservationCanceledNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var notifyDto = orderNotifyDto.NotifyDto;
        NotifyMessageProcess command = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        command.SetMessage(SendToTheRole.Customer,
            NotifyEventSubType.Ticket_ReservationCanceled,
            NotifyMode.Sms,
            new
            {
                PhoneNumber = baseOrder.ContactsPhoneNumber,
                BaseOrderId = baseOrder.Id,
                ProductName = notifyDto.ProductName,
                SkuName = notifyDto.SkuName,
                TravelDateBegin = notifyDto.TravelDateBegin,
                TravelDateEnd = notifyDto.TravelDateEnd,
                Quantity = notifyDto.Quantity
            });
        await PublishMessageProcess(command);
    }

    public async Task TicketReservationNotify(OrderNotifyDto<TicketReservationNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var notifyDto = orderNotifyDto.NotifyDto;
        NotifyMessageProcess command = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        command.SetMessage(SendToTheRole.TenantStaff, NotifyEventSubType.Ticket_Reservation,
            NotifyMode.Wechat,
            new
            {
                baseOrder.AgencyId,
                notifyDto.SupplierId,
                BaseOrderId = baseOrder.Id,
                notifyDto.ProductName,
                notifyDto.SkuName,
                notifyDto.TravelDateBegin,
                notifyDto.TravelDateEnd
            });
        await PublishMessageProcess(command);

        command.SetMessage(SendToTheRole.TenantStaff, NotifyEventSubType.Ticket_Reservation,
            NotifyMode.Email,
            new
            {
                //使用员工通知绑定人员的邮箱
                Addressee = string.Empty,
                OrderId = baseOrder.Id,
                ProductName = notifyDto.ProductName,
                SkuName = notifyDto.SkuName,
                TravelDateBegin = notifyDto.TravelDateBegin,
                TravelDateEnd = notifyDto.TravelDateEnd,
                Nights = notifyDto.Nights,
                Quantity = notifyDto.Quantity,
                Travelers = notifyDto.Traveler,
                Amount = notifyDto.Amount
            });
        await PublishMessageProcess(command);
    }

    public async Task TicketOrderTimeReminderNotify(OrderNotifyDto<TicketOrderTimeReminderNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var notifyDto = orderNotifyDto.NotifyDto;
        NotifyMessageProcess processCommand = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        var notifyEventSubType = notifyDto.TicketSaleType.Equals(TicketSaleType.GroupPurchase)
            ? NotifyEventSubType.Ticket_GroupPurchase_TimeReminder : NotifyEventSubType.Ticket_Booking_TimeReminder;
        processCommand.SetMessage(SendToTheRole.Customer, notifyEventSubType, NotifyMode.Wechat,
                new
                {
                    baseOrder.UserId,
                    BaseOrderId = baseOrder.Id,
                    notifyDto.ProductName,
                    notifyDto.SkuName,
                    notifyDto.SkuValidityBegin,
                    notifyDto.SkuValidityEnd,
                    notifyDto.Quantity,
                    notifyDto.TicketSaleType,
                });
        await PublishMessageProcess(processCommand);

    }

    public async Task TicketReservationTimeReminderNotify(OrderNotifyDto<TicketReservationTimeReminderNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var notifyDto = orderNotifyDto.NotifyDto;
        NotifyMessageProcess processCommand = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        var notifyEventSubType = NotifyEventSubType.Ticket_Reservation_TimeReminder;
        processCommand.SetMessage(SendToTheRole.Customer, notifyEventSubType, NotifyMode.Wechat,
                new
                {
                    baseOrder.UserId,
                    BaseOrderId = baseOrder.Id,
                    notifyDto.ProductName,
                    notifyDto.SkuName,
                    notifyDto.TravelDateBegin,
                    notifyDto.TravelDateEnd,
                    notifyDto.ResourceName,
                    notifyDto.ProductTicketBusinessType
                });
        await PublishMessageProcess(processCommand);
    }

    #endregion

    #region refund

    public async Task OrderRefundApplicationNotify(OrderNotifyDto<OrderRefundApplicationNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var notifyDto = orderNotifyDto.NotifyDto;
        NotifyEventSubType? notifyEventSubType = notifyDto.RefundOrderType switch
        {
            RefundOrderType.Ticket => NotifyEventSubType.Ticket_RefundApplication,
            RefundOrderType.Mail => NotifyEventSubType.Mail_RefundApplication,
            RefundOrderType.TravelLine => NotifyEventSubType.TravelLine_RefundApplication,
            RefundOrderType.ScenicTicket => NotifyEventSubType.ScenicTicket_RefundApplication,
            _ => null
        };
        if (notifyEventSubType.HasValue is false)
            return;
        NotifyMessageProcess processCommand = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        processCommand.SetMessage(SendToTheRole.TenantStaff,
            notifyEventSubType.Value,
            NotifyMode.Wechat,
            new
            {
                baseOrder.AgencyId,
                notifyDto.SupplierId,
                notifyDto.ProductName,
                notifyDto.SkuName,
                notifyDto.RefundAmount,
                notifyDto.BaseOrderId
            });
        await PublishMessageProcess(processCommand);

        processCommand.SetMessage(SendToTheRole.TenantStaff,
           notifyEventSubType.Value,
           NotifyMode.Email,
           new
           {
               //使用员工通知绑定人员的邮箱
               Addressee = string.Empty,
               OrderId = baseOrder.Id,
               ProductName = notifyDto.ProductName,
               SkuName = notifyDto.SkuName,
               RefundAmount = notifyDto.RefundAmount,
               Reason = notifyDto.Reason
           });
        await PublishMessageProcess(processCommand);
    }

    public async Task OrderRefundSucceededNotify(OrderNotifyDto<OrderRefundSucceededNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var notifyDto = orderNotifyDto.NotifyDto;
        NotifyEventSubType? notifyEventSubType = null;
        switch (notifyDto.OrderType)
        {
            case OrderType.Ticket:
                notifyEventSubType = NotifyEventSubType.Ticket_RefundSucceeded;
                break;
            case OrderType.Mail:
                notifyEventSubType = NotifyEventSubType.Mail_RefundSucceeded;
                break;
            case OrderType.TravelLineOrder:
                notifyEventSubType = NotifyEventSubType.TravelLine_RefundSucceeded;
                break;
            case OrderType.ScenicTicket:
                notifyEventSubType = NotifyEventSubType.ScenicTicket_RefundSucceded;
                break;
        }
        if (notifyEventSubType.HasValue is false)
            return;
        NotifyMessageProcess processCommand = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        switch (baseOrder.SellingPlatform)
        {
            case SellingPlatform.WeChatMP:
            case SellingPlatform.WeChatApplet:
                processCommand.SetMessage(SendToTheRole.Customer, notifyEventSubType.Value, NotifyMode.Wechat,
                    new
                    {
                        baseOrder.UserId,
                        BaseOrderId = baseOrder.Id,
                        notifyDto.RefundOrderId,
                        notifyDto.ProductName,
                        notifyDto.SkuName,
                        RefundUserType = notifyDto.UserType.GetDescription(),
                        notifyDto.RefundAmount,
                        RefundStatus = notifyDto.RefundStatus.GetDescription()
                    });
                await PublishMessageProcess(processCommand);
                if (!string.IsNullOrWhiteSpace(baseOrder.ContactsPhoneNumber))
                {
                    processCommand.SetMessage(SendToTheRole.Customer,
                        notifyEventSubType.Value,
                        NotifyMode.Sms,
                        new
                        {
                            PhoneNumber = baseOrder.ContactsPhoneNumber,
                            ProductName = notifyDto.ProductName,
                            SkuName = notifyDto.SkuName,
                            Quantity = notifyDto.Quantity,
                            RefundAmount = notifyDto.RefundAmount
                        });
                    await PublishMessageProcess(processCommand);
                }
                break;
            case SellingPlatform.B2BWeb:
                var addressee = baseOrder.ContactsEmail;
                if (!string.IsNullOrWhiteSpace(addressee))
                {
                    processCommand.SetMessage(SendToTheRole.Customer,
                        notifyEventSubType.Value,
                        NotifyMode.Email,
                        notifyEventSubType.Value switch
                        {
                            NotifyEventSubType.ScenicTicket_RefundSucceded => new
                            {
                                Addressee = addressee,
                                OrderId = baseOrder.Id,
                                ScenicSpotName = notifyDto.ProductName,
                                TicketName = notifyDto.SkuName,
                                RefundAmount = notifyDto.RefundAmount,
                                Reason = notifyDto.Reason
                            },
                            _ => new
                            {
                                Addressee = baseOrder.ContactsEmail,
                                OrderId = baseOrder.Id,
                                ProductName = notifyDto.ProductName,
                                SkuName = notifyDto.SkuName,
                                RefundAmount = notifyDto.RefundAmount,
                                Reason = notifyDto.Reason
                            }
                        });
                    await PublishMessageProcess(processCommand);
                }
                break;
        }
    }

    public async Task OrderRefundRejectedNotify(OrderNotifyDto<OrderRefundRejectedNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var notifyDto = orderNotifyDto.NotifyDto;
        NotifyEventSubType? notifyEventSubType = null;
        switch (notifyDto.OrderType)
        {
            case OrderType.Ticket:
                notifyEventSubType = NotifyEventSubType.Ticket_RefundRejected;
                break;
            case OrderType.Mail:
                notifyEventSubType = NotifyEventSubType.Mail_RefundRejected;
                break;
            case OrderType.TravelLineOrder:
                notifyEventSubType = NotifyEventSubType.TravelLine_RefundRejected;
                break;
            case OrderType.ScenicTicket:
                notifyEventSubType = NotifyEventSubType.ScenicTicket_RefundRejected;
                break;
        }
        if (notifyEventSubType.HasValue is false)
            return;
        NotifyMessageProcess processCommand = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        switch (baseOrder.SellingPlatform)
        {
            case SellingPlatform.WeChatMP:
            case SellingPlatform.WeChatApplet:
                processCommand.SetMessage(SendToTheRole.Customer,
                    notifyEventSubType.Value,
                    NotifyMode.Wechat,
                    new
                    {
                        baseOrder.UserId,
                        BaseOrderId = baseOrder.Id,
                        notifyDto.RefundOrderId,
                        notifyDto.ProductName,
                        notifyDto.SkuName,
                        RefundUserType = notifyDto.UserType.GetDescription(),
                        notifyDto.RefundAmount,
                        RefundStatus = notifyDto.RefundStatus.GetDescription(),
                        notifyDto.RejectedReason
                    });
                await PublishMessageProcess(processCommand);
                if (!string.IsNullOrWhiteSpace(baseOrder.ContactsPhoneNumber))
                {
                    processCommand.SetMessage(SendToTheRole.Customer,
                        notifyEventSubType.Value,
                        NotifyMode.Sms,
                        new
                        {
                            PhoneNumber = baseOrder.ContactsPhoneNumber,
                            notifyDto.RefundOrderId,
                            notifyDto.ProductName,
                            notifyDto.SkuName,
                            notifyDto.Quantity
                        });
                    await PublishMessageProcess(processCommand);
                }
                break;
            case SellingPlatform.B2BWeb:
                var addressee = baseOrder.ContactsEmail;
                if (!string.IsNullOrWhiteSpace(addressee))
                {
                    processCommand.SetMessage(SendToTheRole.Customer,
                        notifyEventSubType.Value,
                        NotifyMode.Email,
                        notifyEventSubType.Value switch
                        {
                            NotifyEventSubType.ScenicTicket_RefundRejected => new
                            {
                                Addressee = addressee,
                                OrderId = baseOrder.Id,
                                ScenicSpotName = notifyDto.ProductName,
                                TicketName = notifyDto.SkuName,
                                RejectedReason = notifyDto.RejectedReason,
                                RefundAmount = notifyDto.RefundAmount
                            },
                            _ => new
                            {
                                Addressee = baseOrder.ContactsEmail,
                                OrderId = baseOrder.Id,
                                ProductName = notifyDto.ProductName,
                                SkuName = notifyDto.SkuName,
                                RejectedReason = notifyDto.RejectedReason,
                                RefundAmount = notifyDto.RefundAmount,
                                Reason = notifyDto.Reason
                            }
                        });
                    await PublishMessageProcess(processCommand);
                }
                break;
        }
    }

    #endregion

    #region sceinc

    public async Task ScenicTicketCreateOrderNotify(OrderNotifyDto<ScenicTicketCreateOrderNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var scenicTicketOrder = orderNotifyDto.NotifyDto;
        NotifyMessageProcess processCommand = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        switch (baseOrder.SellingPlatform)
        {
            case SellingPlatform.WeChatMP:
            case SellingPlatform.WeChatApplet:
                processCommand.SetMessage(SendToTheRole.Customer,
                    NotifyEventSubType.ScenicTicket_CreateOrder,
                    NotifyMode.Wechat,
                    new
                    {
                        baseOrder.UserId,
                        BaseOrderId = baseOrder.Id.ToString(),
                        ProductName = scenicTicketOrder.ScenicSpotName,
                        SkuName = scenicTicketOrder.TicketName,
                        scenicTicketOrder.Quantity,
                        baseOrder.PaymentAmount,
                        scenicTicketOrder.ValidityBegin,
                        scenicTicketOrder.ValidityEnd,
                    });
                await PublishMessageProcess(processCommand);
                if (!string.IsNullOrWhiteSpace(baseOrder.ContactsPhoneNumber))
                {
                    processCommand.SetMessage(SendToTheRole.Customer,
                   NotifyEventSubType.ScenicTicket_CreateOrder,
                   NotifyMode.Sms,
                   new
                   {
                       PhoneNumber = baseOrder.ContactsPhoneNumber,
                       BaseOrderId = baseOrder.Id.ToString(),
                       ProductName = scenicTicketOrder.ScenicSpotName,
                       SkuName = scenicTicketOrder.TicketName,
                       Quantity = scenicTicketOrder.Quantity,
                       ValidityBegin = scenicTicketOrder.ValidityBegin,
                       ValidityEnd = scenicTicketOrder.ValidityEnd
                   });
                    await PublishMessageProcess(processCommand);
                }
                break;
            case SellingPlatform.System:
                if (!string.IsNullOrWhiteSpace(baseOrder.ContactsPhoneNumber))
                {
                    processCommand.SetMessage(SendToTheRole.Customer,
                   NotifyEventSubType.ScenicTicket_CreateOrder,
                   NotifyMode.Sms,
                   new
                   {
                       PhoneNumber = baseOrder.ContactsPhoneNumber,
                       BaseOrderId = baseOrder.Id.ToString(),
                       ProductName = scenicTicketOrder.ScenicSpotName,
                       SkuName = scenicTicketOrder.TicketName,
                       Quantity = scenicTicketOrder.Quantity,
                       ValidityBegin = scenicTicketOrder.ValidityBegin,
                       ValidityEnd = scenicTicketOrder.ValidityEnd
                   });
                    await PublishMessageProcess(processCommand);
                }
                if (!string.IsNullOrWhiteSpace(baseOrder.ContactsEmail))
                {
                    processCommand.SetMessage(SendToTheRole.Customer,
                        NotifyEventSubType.ScenicTicket_CreateOrder,
                        NotifyMode.Email,
                        new
                        {
                            Addressee = baseOrder.ContactsEmail,
                            OrderId = scenicTicketOrder.Id,
                            scenicTicketOrder.ScenicSpotName,
                            scenicTicketOrder.TicketName,
                            scenicTicketOrder.ValidityBegin,
                            scenicTicketOrder.ValidityEnd,
                            TicketCodeUrl = $"/verification?baseOrderId={baseOrder.Id}&orderId={scenicTicketOrder.Id}&type=1",
                            OrderContact = baseOrder.ContactsName,
                            Remark = baseOrder.Message,
                            scenicTicketOrder.Travelers,
                            scenicTicketOrder.Quantity,
                            baseOrder.PaymentAmount,
                            CreateOrderTime = baseOrder.CreateTime,
                            scenicTicketOrder.CredentialSourceType,
                            scenicTicketOrder.AttachmentFilePaths,
                            scenicTicketOrder.OpeningTime,
                            scenicTicketOrder.ScenicSpotAddress,
                            baseOrder.SellingPlatform,
                            scenicTicketOrder.OrderFields
                        });
                    await PublishMessageProcess(processCommand);
                }
                break;
            case SellingPlatform.B2BWeb:
            case SellingPlatform.Fliggy:
            case SellingPlatform.Meituan:
            case SellingPlatform.Ctrip:
            case SellingPlatform.TikTok:
                var addressee = baseOrder.ContactsEmail;
                if (!string.IsNullOrWhiteSpace(addressee))
                {
                    processCommand.SetMessage(SendToTheRole.Customer,
                        NotifyEventSubType.ScenicTicket_CreateOrder,
                        NotifyMode.Email,
                        new
                        {
                            Addressee = addressee,
                            OrderId = scenicTicketOrder.Id,
                            scenicTicketOrder.ScenicSpotName,
                            scenicTicketOrder.TicketName,
                            scenicTicketOrder.ValidityBegin,
                            scenicTicketOrder.ValidityEnd,
                            TicketCodeUrl = $"/verification?baseOrderId={baseOrder.Id}&orderId={scenicTicketOrder.Id}&type=1",
                            OrderContact = baseOrder.ContactsName,
                            Remark = baseOrder.Message,
                            scenicTicketOrder.Travelers,
                            scenicTicketOrder.Quantity,
                            baseOrder.PaymentAmount,
                            CreateOrderTime = baseOrder.CreateTime,
                            scenicTicketOrder.CredentialSourceType,
                            scenicTicketOrder.AttachmentFilePaths,
                            scenicTicketOrder.OpeningTime,
                            scenicTicketOrder.ScenicSpotAddress,
                            baseOrder.SellingPlatform,
                            scenicTicketOrder.OrderFields
                        });
                    await PublishMessageProcess(processCommand);
                }
                break;
        }
        //已发货
        if (baseOrder.AgencyId > 0 && scenicTicketOrder.Status == ScenicTicketOrderStatus.Delivered)
        {
            processCommand.SetMessage(SendToTheRole.AgencyStaff, NotifyEventSubType.ScenicTicket_Delivered, NotifyMode.SiteMessage,
                new
                {
                    BaseOrderId = baseOrder.Id,
                    AgencyId = baseOrder.AgencyId,
                    UserId = baseOrder.UserId,
                    baseOrder.ResourceName,
                    baseOrder.ProductName,
                    baseOrder.ProductSkuName,
                });
            await PublishMessageProcess(processCommand);
        }
    }

    public async Task ScenicTicketCodeUsedNotify(OrderNotifyDto<ScenicTicketCodeUsedNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var notifyDto = orderNotifyDto.NotifyDto;
        NotifyMessageProcess command = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        command.SetMessage(SendToTheRole.Customer,
            NotifyEventSubType.ScenicTicket_CodeUsed,
            NotifyMode.Wechat,
            new
            {
                baseOrder.UserId,
                BaseOrderId = baseOrder.Id,
                notifyDto.ProductName,
                notifyDto.SkuName,
                OrderContact = baseOrder.ContactsName,
                notifyDto.Quantity,
                CreateOrderTime = DateTime.Now
            });
        await PublishMessageProcess(command);
    }

    public async Task ScenicTicketTimeReminderNotify(OrderNotifyDto<ScenicTicketTimeReminderNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var notifyDto = orderNotifyDto.NotifyDto;
        NotifyMessageProcess processCommand = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        var notifyEventSubType = NotifyEventSubType.ScenicTicket_TimeReminder;
        processCommand.SetMessage(SendToTheRole.Customer, notifyEventSubType, NotifyMode.Wechat,
                new
                {
                    baseOrder.UserId,
                    BaseOrderId = baseOrder.Id,
                    notifyDto.ProductName,
                    notifyDto.SkuName,
                    notifyDto.ValidityBegin,
                    notifyDto.ValidityEnd,
                    notifyDto.ResourceName
                });
        await PublishMessageProcess(processCommand);
    }
    #endregion

    #region travelline

    public async Task TravelLineOrderCreatedNotify(OrderNotifyDto<TravelLineOrderCreatedNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var travelLineOrder = orderNotifyDto.NotifyDto;
        NotifyMessageProcess processCommand = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        switch (baseOrder.SellingPlatform)
        {
            case SellingPlatform.WeChatMP:
            case SellingPlatform.WeChatApplet:
                break;
            case SellingPlatform.B2BWeb:
                var addressee = baseOrder.ContactsEmail;
                if (!string.IsNullOrWhiteSpace(addressee))
                {
                    processCommand.SetMessage(SendToTheRole.Customer,
                        NotifyEventSubType.TravelLine_CreateOrder,
                        NotifyMode.Email,
                        new
                        {
                            Addressee = addressee,
                            OrderId = baseOrder.Id,
                            travelLineOrder.ProductName,
                            travelLineOrder.SkuName,
                            TravelDateBegin = travelLineOrder.TravelBeginDate,
                            TravelDateEnd = travelLineOrder.TravelEndDate,
                            travelLineOrder.Days,
                            travelLineOrder.Nights,
                            travelLineOrder.RallyPointTime,
                            travelLineOrder.RallyPointAddress,
                            Remark = baseOrder.Message,
                            baseOrder.PaymentAmount,
                            CreateOrderTime = baseOrder.CreateTime,
                            travelLineOrder.OrderFields,
                            travelLineOrder.ChildQuantity,
                            travelLineOrder.AdultQuantity,
                            travelLineOrder.BabyQuantity,
                            travelLineOrder.ElderlyQuantity,
                            travelLineOrder.RoomPriceDifferenceQuantity
                        });
                    await PublishMessageProcess(processCommand);
                }
                break;
        }
        processCommand.SetMessage(SendToTheRole.TenantStaff,
                  NotifyEventSubType.TravelLine_CreateOrder,
                  NotifyMode.Wechat,
                  new
                  {
                      baseOrder.AgencyId,
                      travelLineOrder.SupplierId,
                      BaseOrderId = baseOrder.Id,
                      travelLineOrder.ProductName,
                      travelLineOrder.SkuName,
                      TravelDateBegin = travelLineOrder.TravelBeginDate,
                      TravelDateEnd = travelLineOrder.TravelEndDate
                  });
        await PublishMessageProcess(processCommand);
    }

    public async Task TravelLineConfirmedNotify(OrderNotifyDto<TravelLineConfirmedNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var travelLineOrder = orderNotifyDto.NotifyDto;
        NotifyMessageProcess processCommand = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        switch (baseOrder.SellingPlatform)
        {
            case SellingPlatform.WeChatMP:
            case SellingPlatform.WeChatApplet:
                processCommand.SetMessage(SendToTheRole.Customer,
                    NotifyEventSubType.TravelLine_Confirmed,
                    NotifyMode.Wechat,
                    new
                    {
                        baseOrder.UserId,
                        BaseOrderId = baseOrder.Id,
                        travelLineOrder.ProductName,
                        SkuName = travelLineOrder.ProductSkuName,
                        TravelDateBegin = travelLineOrder.TravelBeginDate,
                        TravelDateEnd = travelLineOrder.TravelEndDate,
                        travelLineOrder.AdultQuantity,
                        travelLineOrder.ChildQuantity
                    });
                await PublishMessageProcess(processCommand);
                if (!string.IsNullOrWhiteSpace(baseOrder.ContactsPhoneNumber))
                {
                    processCommand.SetMessage(SendToTheRole.Customer,
                        NotifyEventSubType.TravelLine_Confirmed,
                        NotifyMode.Sms,
                        new
                        {
                            PhoneNumber = baseOrder.ContactsPhoneNumber,
                            BaseOrderId = baseOrder.Id,
                            ProductName = travelLineOrder.ProductName,
                            SkuName = travelLineOrder.ProductSkuName,
                            PeopleCount = travelLineOrder.OrderFields.Count(x => x.TemplateType is TemplateType.Travel),
                            TravelDateBegin = travelLineOrder.TravelBeginDate,
                            RallyPointTime = travelLineOrder.RallyPointTime,
                            RallyPointAddress = travelLineOrder.RallyPointAddress,
                            TourGuidePhoneNumber = travelLineOrder.TourGuidePhoneNumber,
                            TourGuideName = travelLineOrder.TourGuideName
                        });
                    await PublishMessageProcess(processCommand);
                }
                break;
            case SellingPlatform.B2BWeb:
            case SellingPlatform.System:
            case SellingPlatform.Ctrip:
            case SellingPlatform.Meituan:
            case SellingPlatform.TikTok:
            case SellingPlatform.Fliggy:
                var addressee = baseOrder.ContactsEmail;
                if (!string.IsNullOrWhiteSpace(addressee))
                {
                    processCommand.SetMessage(SendToTheRole.Customer,
                        NotifyEventSubType.TravelLine_Confirmed,
                        NotifyMode.Email,
                        new
                        {
                            Addressee = addressee,
                            travelLineOrder.ProductName,
                            OrderId = baseOrder.SellingChannels == SellingChannels.B2b
                            ? baseOrder.Id.ToString() : baseOrder.ChannelOrderNo,
                            SkuName = travelLineOrder.ProductSkuName,
                            TravelDateBegin = travelLineOrder.TravelBeginDate,
                            TravelDateEnd = travelLineOrder.TravelEndDate,
                            travelLineOrder.Days,
                            travelLineOrder.Nights,
                            travelLineOrder.RallyPointTime,
                            travelLineOrder.RallyPointAddress,
                            travelLineOrder.TourGuideCarNumber,
                            travelLineOrder.TourGuideName,
                            travelLineOrder.TourGuidePhoneNumber,
                            CreateOrderTime = baseOrder.CreateTime,
                            baseOrder.SellingChannels,
                            travelLineOrder.UserNickName,
                            travelLineOrder.OrderFields,
                            travelLineOrder.ChildQuantity,
                            travelLineOrder.AdultQuantity,
                            travelLineOrder.BabyQuantity,
                            travelLineOrder.ElderlyQuantity,
                            travelLineOrder.RoomPriceDifferenceQuantity,
                            travelLineOrder.AttachmentFilePaths
                        });
                    await PublishMessageProcess(processCommand);
                }
                break;
        }
        //系统消息通知
        processCommand.SetMessage(SendToTheRole.AgencyStaff, NotifyEventSubType.TravelLine_Confirmed, NotifyMode.SiteMessage,
            new
            {
                BaseOrderId = baseOrder.Id,
                baseOrder.AgencyId,
                baseOrder.UserId,
                baseOrder.ResourceName,
                baseOrder.ProductName,
                baseOrder.ProductSkuName,
            });
        await PublishMessageProcess(processCommand);
    }

    public async Task TravelLineTimeReminderNotify(OrderNotifyDto<TravelLineTimeReminderNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var notifyDto = orderNotifyDto.NotifyDto;
        NotifyMessageProcess processCommand = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        processCommand.SetMessage(SendToTheRole.Customer,
                    NotifyEventSubType.TravelLine_TimeReminder,
                    NotifyMode.Wechat,
                    new
                    {
                        baseOrder.UserId,
                        BaseOrderId = baseOrder.Id,
                        notifyDto.ProductName,
                        notifyDto.SkuName,
                        notifyDto.TravelBeginDate,
                        notifyDto.TravelEndDate,
                        notifyDto.AdultQuantity,
                        notifyDto.ChildQuantity
                    });
        await PublishMessageProcess(processCommand);
    }

    #endregion

    #region mail

    public async Task MailOrderCreatedNotify(OrderNotifyDto<MailOrderCreatedNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var notifyDto = orderNotifyDto.NotifyDto;
        NotifyMessageProcess command = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        switch (baseOrder.SellingPlatform)
        {
            case SellingPlatform.WeChatMP:
            case SellingPlatform.WeChatApplet:
                var productName = notifyDto.SubOrders.First().ProductName;
                var skuName = notifyDto.SubOrders.First().SkuName;
                var quantity = notifyDto.SubOrders.First().Quantity;
                if (!string.IsNullOrWhiteSpace(baseOrder.ContactsPhoneNumber))
                {
                    command.SetMessage(SendToTheRole.Customer,
                         NotifyEventSubType.Mail_CreateOrder,
                         NotifyMode.Sms,
                         new
                         {
                             PhoneNumber = baseOrder.ContactsPhoneNumber,
                             BaseOrderId = baseOrder.Id,
                             ProductName = productName,
                             SkuName = skuName,
                             Quantity = quantity,
                         });
                    await PublishMessageProcess(command);
                }
                break;
        }
        var groups = notifyDto.SubOrders.GroupBy(x => x.SupplierId);
        foreach (var g in groups)
        {
            command.SetMessage(SendToTheRole.TenantStaff, NotifyEventSubType.Mail_CreateOrder,
                 NotifyMode.Wechat, new
                 {
                     baseOrder.AgencyId,
                     SupplierId = g.Key,
                     BaseOrderId = baseOrder.Id,
                     g.First().ProductName,
                     g.First().SkuName
                 });
            await PublishMessageProcess(command);
        }
    }

    public async Task MailDeliverNotify(OrderNotifyDto<MailDeliverNotifyDto> orderNotifyDto)
    {
        var baseOrder = orderNotifyDto.BaseOrder;
        var notifyDto = orderNotifyDto.NotifyDto;
        NotifyMessageProcess command = new(baseOrder.SellingPlatform, baseOrder.TenantId);
        switch (baseOrder.SellingPlatform)
        {
            case SellingPlatform.WeChatMP:
            case SellingPlatform.WeChatApplet:
                command.SetMessage(SendToTheRole.Customer,
                    NotifyEventSubType.Mail_Deliver,
                    NotifyMode.Wechat,
                    new
                    {
                        UserId = baseOrder.UserId,
                        BaseOrderId = baseOrder.Id,
                        ProductName = notifyDto.ProductName,
                        SkuName = notifyDto.SkuName,
                        LogisticsCompanyName = notifyDto.LogisticsCompanyName,
                        TrackingNumber = notifyDto.TrackingNumber
                    });
                await PublishMessageProcess(command);
                if (!string.IsNullOrWhiteSpace(baseOrder.ContactsPhoneNumber))
                {
                    command.SetMessage(SendToTheRole.Customer,
                        NotifyEventSubType.Mail_Deliver,
                        NotifyMode.Sms,
                        new
                        {
                            PhoneNumber = baseOrder.ContactsPhoneNumber,
                            BaseOrderId = baseOrder.Id,
                            ProductName = notifyDto.ProductName,
                            SkuName = notifyDto.SkuName,
                            Quantity = notifyDto.Quantity,
                            LogisticsCompanyName = notifyDto.LogisticsCompanyName,
                            TrackingNumber = notifyDto.TrackingNumber
                        });
                    await PublishMessageProcess(command);
                }
                break;
        }
    }

    #endregion

    #region GroupBookingApplicationOrder
    public async Task GroupBookingApplicationFormNotify(GroupBookingApplicationFormNotifyDto notifyDto,
        NotifyEventSubType notifyEventSubType = NotifyEventSubType.Hotel_GroupRoom_Application_NotAssign)
    {
        NotifyMessageProcess command = new(notifyDto.SellingPlatform, notifyDto.TenantId);
        //过滤不必发送的申请单
        switch (notifyDto.SendToTheRole)
        {
            case SendToTheRole.TenantStaff:
                if (notifyDto.Status is not GroupBookingApplicationFormStatus.NewApplication
                    && notifyDto.Status is not GroupBookingApplicationFormStatus.Inquiried
                    && notifyDto.Status is not GroupBookingApplicationFormStatus.WaitForAuditPreOrder)
                    return;
                break;
            case SendToTheRole.ManagerStaff:
                //只发送待询价/确认报价/预订单
                if (notifyDto.Status is not GroupBookingApplicationFormStatus.WaitForInquiry
                    && notifyDto.Status is not GroupBookingApplicationFormStatus.QuotationConfirmed
                    && notifyDto.Status is not GroupBookingApplicationFormStatus.PreOrdered)
                    return;
                break;
        }
        command.SetMessage(notifyDto.SendToTheRole, notifyEventSubType, notifyDto.NotifyMode, new
        {
            notifyDto.Users,
            notifyDto.AgencyId,
            notifyDto.AgencyName,
            OrderId = notifyDto.Id,
            notifyDto.CountryName,
            notifyDto.CountryCode,
            OrderCreateTime = notifyDto.ApplicationTime,
            notifyDto.Status,
            notifyDto.HotelName,
            notifyDto.CheckInDate,
            notifyDto.CheckOutDate,
            notifyDto.OtherRequirements,
            notifyDto.BrandName,
            PhoneNumber = notifyDto.ContactPhone
        });
        await PublishMessageProcess(command);
        //系统消息通知
        if (notifyDto.ApplicantId.HasValue)
        {
            switch (notifyDto.Status)
            {
                case GroupBookingApplicationFormStatus.Quoted:
                    command.SetMessage(SendToTheRole.AgencyStaff,
                        NotifyEventSubType.Hotel_GroupRoom_QuotationConfirmed,
                        NotifyMode.SiteMessage,
                        new
                        {
                            ApplicationFormId = notifyDto.Id,
                            AgencyId = notifyDto.AgencyId,
                            UserId = notifyDto.ApplicantId!.Value,
                        });
                    await PublishMessageProcess(command);
                    break;
                case GroupBookingApplicationFormStatus.PreOrdered:
                    command.SetMessage(SendToTheRole.AgencyStaff,
                        NotifyEventSubType.Hotel_GroupRoom_PreOrder,
                        NotifyMode.SiteMessage,
                        new
                        {
                            ApplicationFormId = notifyDto.Id,
                            AgencyId = notifyDto.AgencyId,
                            UserId = notifyDto.ApplicantId!.Value,
                        });
                    await PublishMessageProcess(command);
                    break;
            }
        }
    }

    /// <summary>
    /// 发送团房付款提醒
    /// </summary>
    /// <param name="notifyDto"></param>
    /// <returns></returns>
    public async Task GroupRoomPaymentReminderNotify(GroupRoomPaymentReminderNotifyDto notifyDto)
    {
        NotifyMessageProcess command = new(notifyDto.SellingPlatform, notifyDto.TenantId);
        //客户通知
        command.SetMessage(SendToTheRole.Customer, NotifyEventSubType.Hotel_GroupRoom_Payment_Reminder, NotifyMode.Sms, new
        {
            notifyDto.TenantId,
            PhoneNumber = notifyDto.ContactPhone,
            notifyDto.SellingPlatform,
            notifyDto.HotelName,
            notifyDto.CheckInDate,
            notifyDto.CheckOutDate,
            notifyDto.RoomCount,
            notifyDto.NightCount,
            notifyDto.NumberOfBreakfast,
            notifyDto.InitialPaymentAmount,
            InitialPaymentDate = notifyDto.InitialPaymentDate?.Date,
            notifyDto.FinalPaymentAmount,
            FinalPaymentDate = notifyDto.FinalPaymentDate?.Date,
            notifyDto.PaymentCurrencyCode,
            notifyDto.RoomName,
            notifyDto.BrandName,
            notifyDto.GroupBookingApplicationFormId
        });
        await PublishMessageProcess(command);
        //租户员工通知
        command.SetMessage(SendToTheRole.TenantStaff, NotifyEventSubType.Hotel_GroupRoom_Payment_Reminder, NotifyMode.Wechat, new
        {
            notifyDto.Users,
            notifyDto.TenantId,
            notifyDto.AgencyId,
            SupplierId = notifyDto.SupplierId ?? 0,
            notifyDto.InitialPaymentDate,
            notifyDto.FinalPaymentDate,
            notifyDto.GroupBookingApplicationFormId,
            notifyDto.AgencyName
        });
        await PublishMessageProcess(command);
        command.SetMessage(SendToTheRole.TenantStaff, NotifyEventSubType.Hotel_GroupRoom_Payment_Reminder, NotifyMode.DingTalkRobot, new
        {
            StaffUserIds = notifyDto.Users.Where(s => s.Role == SendToTheRole.TenantStaff).Select(s => s.Id).ToArray(),
            notifyDto.TenantId,
            notifyDto.AgencyId,
            notifyDto.InitialPaymentDate,
            notifyDto.FinalPaymentDate,
            notifyDto.GroupBookingApplicationFormId,
            notifyDto.AgencyName,
            RegionCodes = notifyDto.RegionCodes,
        });
        await PublishMessageProcess(command);
        //财务团房付款通知
        command.SetMessage(SendToTheRole.TenantStaff, NotifyEventSubType.Financial_Hotel_GroupRoom_Payment_Reminder, NotifyMode.Wechat, new
        {
            notifyDto.TenantId,
            notifyDto.AgencyId,
            SupplierId = notifyDto.SupplierId ?? 0,
            notifyDto.InitialPaymentDate,
            notifyDto.FinalPaymentDate,
            notifyDto.GroupBookingApplicationFormId,
            notifyDto.AgencyName
        });
        await PublishMessageProcess(command);
        //B2B系统通知
        if (notifyDto.ApplicantId.HasValue)
        {
            command.SetMessage(SendToTheRole.AgencyStaff,
                NotifyEventSubType.Hotel_GroupRoom_Payment_Reminder,
                NotifyMode.SiteMessage,
            new
            {
                AgencyId = notifyDto.AgencyId,
                UserId = notifyDto.ApplicantId!.Value,
                ApplicationFormId = notifyDto.GroupBookingApplicationFormId,
                HotelName = notifyDto.HotelName,
                CheckInDate = notifyDto.CheckInDate?.ToString("yyyy-MM-dd"),
                CheckOutDate = notifyDto.CheckOutDate?.ToString("yyyy-MM-dd"),
                InitialPaymentAmount = notifyDto.InitialPaymentAmount,
                InitialPaymentDate = notifyDto.InitialPaymentDate?.ToString("yyyy-MM-dd HH:mm"),
                FinalPaymentAmount = notifyDto.FinalPaymentAmount,
                FinalPaymentDate = notifyDto.FinalPaymentDate?.ToString("yyyy-MM-dd HH:mm"),
            });
            await PublishMessageProcess(command);
        }
    }

    public async Task GroupRoomPaidReminderNotify(GroupRoomPaidReminderNotifyDto notifyDto)
    {
        NotifyMessageProcess command = new(notifyDto.SellingPlatform, notifyDto.TenantId);
        //财务
        command.SetMessage(SendToTheRole.TenantStaff, NotifyEventSubType.Financial_Hotel_GroupRoom_Paid_Reminder, NotifyMode.Wechat, notifyDto);
        await PublishMessageProcess(command);
        //商户员工
        command.SetMessage(SendToTheRole.TenantStaff, NotifyEventSubType.Hotel_GroupRoom_Paid_Reminder, NotifyMode.Wechat, notifyDto);
        await PublishMessageProcess(command);
        command.SetMessage(SendToTheRole.TenantStaff, NotifyEventSubType.Hotel_GroupRoom_Paid_Reminder, NotifyMode.DingTalkRobot,
            new
            {
                AgencyName = notifyDto.AgencyName,
                PaymentRatioType = notifyDto.PaymentRatioType,
                GroupBookingApplicationFormId = notifyDto.GroupBookingApplicationFormId,
                FirstHotelName = notifyDto.FirstHotelName,
                InitialPaymentAmount = notifyDto.InitialPaymentAmount,
                FinalPaymentAmount = notifyDto.FinalPaymentAmount,
                RegionCodes = notifyDto.RegionCodes,
                StaffUserIds = notifyDto.Users.Where(s => s.Role == SendToTheRole.TenantStaff).Select(s => s.Id).ToArray(),
            });
        await PublishMessageProcess(command);
    }

    public async Task GroupRoomGuestsReminderNotify(HotelGroupRoomGuestsReminderNotifyDto notifyDto)
    {
        NotifyMessageProcess command = new(notifyDto.SellingPlatform, notifyDto.TenantId);
        //客户通知
        command.SetMessage(SendToTheRole.Customer, NotifyEventSubType.Hotel_GroupRoom_Guests_Reminder, NotifyMode.Sms, new
        {
            notifyDto.TenantId,
            notifyDto.SellingPlatform,
            notifyDto.HotelName,
            notifyDto.CheckInDate,
            notifyDto.CheckOutDate,
            notifyDto.RemindAdvanceTime,
            notifyDto.BrandName,
            notifyDto.UserType,
            PhoneNumber = notifyDto.ContactPhone,
            notifyDto.GroupBookingApplicationFormId,
        });
        await PublishMessageProcess(command);
    }

    public async Task GroupRoomQuotationNoConfirmNotify(GroupRoomQuotationNoConfirmNotifyDto notifyDto)
    {
        NotifyMessageProcess command = new(notifyDto.SellingPlatform, notifyDto.TenantId);
        //客户通知
        command.SetMessage(SendToTheRole.Customer, NotifyEventSubType.Hotel_GroupRoom_Quotation_NoConfirm, NotifyMode.Sms, new
        {
            notifyDto.TenantId,
            notifyDto.SellingPlatform,
            notifyDto.CheckInDate,
            notifyDto.CheckOutDate,
            notifyDto.HotelName,
            notifyDto.BrandName,
            notifyDto.UserType,
            PhoneNumber = notifyDto.Phone,
            notifyDto.GroupBookingApplicationFormId,
        });

        await PublishMessageProcess(command);
    }

    public async Task GroupRoomFinalPaymentNotify(GroupRoomFinalPaymentNotifyDto notifyDto)
    {
        NotifyMessageProcess command = new(notifyDto.SellingPlatform, notifyDto.TenantId);
        //客户通知
        command.SetMessage(SendToTheRole.Customer, NotifyEventSubType.Hotel_GroupRoom_FinalPayment, NotifyMode.Sms, new
        {
            notifyDto.TenantId,
            notifyDto.SellingPlatform,
            notifyDto.CheckInDate,
            notifyDto.CheckOutDate,
            notifyDto.HotelName,
            notifyDto.BrandName,
            PhoneNumber = notifyDto.Phone,
        });
        await PublishMessageProcess(command);
    }

    public async Task GroupBookingHotelInquiryNotify(GroupBookingHotelInquiryNotifyDto notifyDto)
    {
        List<NotifyMessageProcessMessage> notifyMessageProcessMessages = new();
        foreach (var item in notifyDto.Items)
        {
            var bedTypeDtos = !string.IsNullOrWhiteSpace(item.BedTypeJson) ?
                JsonConvert.DeserializeObject<GroupBookingApplicationFormBedTypeDto[]>(item.BedTypeJson!) : Array.Empty<GroupBookingApplicationFormBedTypeDto>();
            var meetingTimeDtos = !string.IsNullOrWhiteSpace(item.MeetingTimeJson) ?
                JsonConvert.DeserializeObject<GroupBookingApplicationFormMeetingTimeDto[]>(item.MeetingTimeJson) : Array.Empty<GroupBookingApplicationFormMeetingTimeDto>();
            notifyMessageProcessMessages.Add(new NotifyMessageProcessMessage
            {
                NotifyChannel = NotifyChannel.Hotel,
                NotifyEventSubType = notifyDto.NotifyEventSubType,
                NotifyMode = NotifyMode.Email,
                SendToTheRole = SendToTheRole.Customer,
                TenantId = notifyDto.TenantId,
                Variables = new
                {
                    item.Id,
                    item.HotelInquiryRecordId,
                    item.ApplicationFormId,
                    UserEmail = item.UserEmail,
                    Link = item.Link,
                    Addressee = item.Email,
                    FirstName = item.FirstName,
                    ENName = item.HotelENName,
                    OrderNumber = item.ApplicationFormId,
                    CheckInTime = $"{item.CheckInDate:yyyy-MM-dd}",
                    CheckOutTime = $"{item.CheckOutDate:yyyy-MM-dd}",
                    BedRooms = bedTypeDtos?.Select(s => new { s.BedType, s.Num }),
                    MeetingTimes = meetingTimeDtos?.Select(s => new { s.Date, s.FromTime, s.ToTime }),
                    NumberOfAttendees = item.MeetingsNum,
                    UserId = item.HotelInquiryEmailUserId
                },
                MessageId = item.Id,
            });
        }
        foreach (var message in notifyMessageProcessMessages)
        {
            await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess, message, CapTopics.Order.HotelInquiryMailMessageCallback);
        }
    }

    public async Task HotelGroupHotelQuotationReminder(HotelGroupHotelQuotationReminderDto dto)
    {
        foreach (var item in dto.Reminders)
        {
            NotifyMessageProcessMessage message = new()
            {
                NotifyMode = NotifyMode.Wechat,
                SendToTheRole = dto.SendToTheRole,
                NotifyEventSubType = dto.NotifyEventSubType,
                TenantId = dto.TenantId,
                Variables = new
                {
                    AgencyId = dto.AgencyId,
                    AgencyName = dto.AgencyName,
                    HotelName = item.HotelName,
                    Users = item.Users.Where(s => s.Role == dto.SendToTheRole).ToList(),
                    OrderId = dto.ApplicationFormId,
                    NotifyEventSubType = dto.NotifyEventSubType,
                }
            };
            await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess, message);

            var staffUserIds = item.Users
                   .Where(s => s.Role == dto.SendToTheRole)
                   .Select(u => u.Id)
                   .ToList();

            await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess, new NotifyMessageProcessMessage
            {
                NotifyEventSubType = dto.NotifyEventSubType,
                NotifyMode = NotifyMode.DingTalkRobot,
                SendToTheRole = dto.SendToTheRole,
                TenantId = dto.TenantId,
                Variables = new
                {
                    dto.ApplicationFormId,
                    dto.NotifyEventSubType,
                    dto.AgencyName,
                    item.HotelName,
                    StaffUserIds = staffUserIds,
                    RegionCodes = new GroupBookingMessageNotifyRegionDto[] { new() { CountryCode = item.CountryCode, ProvinceCode = item.ProvinceCode } },
                },
            });

        }
    }

    #endregion

    #region ReceiptOrder

    public async Task ReceiptOrderCreateNotify(ReceiptOrderNotifyDto notifyDto)
    {
        NotifyMessageProcess processCommand = new(SellingPlatform.B2BWeb, notifyDto.TenantId);
        processCommand.SetMessage(SendToTheRole.Customer,
            NotifyEventSubType.Financial_OrderStatements,
            NotifyMode.Email, new
            {
                AgencyName = notifyDto.AgencyName,
                Addressee = notifyDto.Addressee,
                CcAddressee = notifyDto.CcAddress,
                BillingCycleBegin = notifyDto.BillingCycleBegin.ToString("yyyy-MM-dd"),
                BillingCycleEnd = notifyDto.BillingCycleEnd.ToString("yyyy-MM-dd"),
                notifyDto.AttachmentFilePaths,
                notifyDto.TotalAmount,
                notifyDto.TotalAmountCurrencyCode,
                notifyDto.PaymentDate
            });
        await PublishMessageProcess(processCommand);
    }

    #endregion

    #region

    /// <summary>
    /// 发送消息
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    private async Task PublishMessageProcess(NotifyMessageProcess process)
    {
        var message = process.GetMessage();
        await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess, message);
    }

    #endregion

    #region 邮箱验证

    /// <summary>
    /// 简单邮箱验证
    /// </summary>
    /// <param name="email"></param>
    /// <code> (?!.*\.@)(?!.*@\.) :负向前瞻,确保`@`符号前后都不能有`.`符号 </code>
    /// <code> [a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4} : 匹配邮箱格式</code>
    /// <returns></returns>
    public bool IsValidEmail(string? email)
    {
        if (string.IsNullOrEmpty(email)) return false;
        var pattern = @"^(?!.*\.@)(?!.*@\.)([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4})$";
        Regex regex = new Regex(pattern);
        return regex.IsMatch(email);
    }

    #endregion

    class NotifyMessageProcess
    {
        public NotifyMessageProcess(SellingPlatform sellingPlatform, long tenantId)
        {
            NotifyChannel = sellingPlatform switch
            {
                SellingPlatform.WeChatMP => NotifyChannel.WechatMall,
                SellingPlatform.WeChatApplet => NotifyChannel.WechatMall,
                SellingPlatform.System => NotifyChannel.Manual,
                SellingPlatform.B2BWeb => NotifyChannel.B2b,
                SellingPlatform.Fliggy => NotifyChannel.B2b,
                SellingPlatform.Meituan => NotifyChannel.B2b,
                SellingPlatform.Ctrip => NotifyChannel.B2b,
                SellingPlatform.TikTok => NotifyChannel.B2b,
                SellingPlatform.Manager => NotifyChannel.Hotel,
                _ => 0
            };
            TenantId = tenantId;
        }

        public long TenantId { get; private set; }

        /// <summary>
        /// 通知给谁
        /// </summary>
        public SendToTheRole SendToTheRole { get; set; }

        /// <summary>
        /// 通知方式
        /// </summary>
        public NotifyMode NotifyMode { get; set; }

        /// <summary>
        /// 通知业务子类型
        /// </summary>
        public NotifyEventSubType NotifyEventSubType { get; set; }

        /// <summary>
        /// 指令来源渠道, SendToTheRole为Customer时必须指定
        /// </summary>
        public NotifyChannel NotifyChannel { get; private set; }

        public object Variables { get; set; }

        /// <summary>
        /// 设置消息内容
        /// </summary>
        /// <param name="sendToTheRole"></param>
        /// <param name="notifyEventSubType"></param>
        /// <param name="notifyMode"></param>
        /// <param name="variables"></param>
        public void SetMessage(SendToTheRole sendToTheRole, NotifyEventSubType notifyEventSubType, NotifyMode notifyMode, object variables)
        {
            SendToTheRole = sendToTheRole;
            NotifyEventSubType = notifyEventSubType;
            NotifyMode = notifyMode;
            Variables = variables;
        }

        public NotifyMessageProcessMessage GetMessage()
        {
            return new NotifyMessageProcessMessage
            {
                NotifyChannel = NotifyChannel,
                NotifyEventSubType = NotifyEventSubType,
                NotifyMode = NotifyMode,
                SendToTheRole = SendToTheRole,
                TenantId = TenantId,
                Variables = Variables
            };
        }
    }
}
