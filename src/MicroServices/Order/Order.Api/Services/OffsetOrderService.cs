using AspectCore.DynamicProxy;
using AutoMapper;
using Cit.Storage.Redis;
using Common.GlobalException;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.OffsetOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Payment.Messages;
using Contracts.Common.Tenant.DTOs.AgencyCredit;
using Contracts.Common.Tenant.Enums;
using Contracts.Common.Tenant.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Order.Api.ConfigModel;
using Order.Api.Services.Interfaces;
using static Contracts.Common.Tenant.DTOs.DingtalkApiConfig.SaasFieldMapDingtalkManager;

namespace Order.Api.Services;

public class OffsetOrderService : IOffsetOrderService
{
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly IRedisClient _redisClient;
    private readonly IAgencyCreditPayService _agencyCreditPayService;
    private readonly IAfterSaleFinancialHandleOrderService _afterSaleFinancialHandleOrderService;
    private readonly ICapPublisher _capPublisher;
    private readonly ILogger _logger;
    private readonly IOptions<OssConfig> _ossConfig;

    //抵冲单生成结算单并发锁
    private const string OFFSET_ORDER_SETTLEMENT_CREATE_LOCK = "offsetorder:settlement:cratelock";

    public OffsetOrderService(
        CustomDbContext dbContext,
        IMapper mapper,
        IRedisClient redisClient,
        IAgencyCreditPayService agencyCreditPayService,
        IAfterSaleFinancialHandleOrderService afterSaleFinancialHandleOrderService,
        ICapPublisher capPublisher,
         ILoggerFactory loggerFactory,
           IOptions<OssConfig> ossConfig)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _redisClient = redisClient;
        _agencyCreditPayService = agencyCreditPayService;
        _afterSaleFinancialHandleOrderService = afterSaleFinancialHandleOrderService;
        _capPublisher = capPublisher;
        _logger = loggerFactory.CreateLogger<OffsetOrderService>();
        _ossConfig = ossConfig;
    }

    [UnitOfWork]
    public async Task<long> Add(AddOffsetOrderInput input)
    {
        OperationUserDto operationUser = input.OperationUser;
        var amount = input.MoneyType switch
        {
            OffsetOrderMoneyType.Additional => Math.Abs(input.Amount),
            OffsetOrderMoneyType.AdditionalCredit => Math.Abs(input.Amount),
            OffsetOrderMoneyType.Refund => -Math.Abs(input.Amount),
            OffsetOrderMoneyType.RefundOffline => -Math.Abs(input.Amount),
            OffsetOrderMoneyType.Compensate => -Math.Abs(input.Amount),
            OffsetOrderMoneyType.CompensateCredit => -Math.Abs(input.Amount),
            _ => throw new NotImplementedException()
        };
        var checkResult = await Check(new CheckBusinessOrderInput
        {
            BaseOrderId = input.BaseOrderId,
            BusinessOrderId = input.BusinessOrderId,
            BusinessType = input.BusinessType,
            OffsetType = input.OffsetType,
            Amount = amount,
            OffsetOrderMoneyType = input.MoneyType
        });
        var offsetOrder = new OffsetOrder
        {
            BaseOrderId = input.BaseOrderId,
            BusinessOrderId = input.BusinessOrderId,
            AgencyId = checkResult.AgencyId,
            SupplierId = checkResult.SupplierId,
            WorkOrderId = input.WorkOrderId,
            CurrencyCode = checkResult.CurrencyCode,
            OffsetAmountType = input.MoneyType,
            Amount = amount,
            ProductId = checkResult.ProductId,
            ProductName = checkResult.ProductName,
            ProductSkuId = checkResult.ProductSkuId,
            ProductSkuName = checkResult.ProductSkuName,
            BusinessType = input.BusinessType,
            OffsetType = input.OffsetType,
            Status = OffsetOrderStatus.WaitingForSettlement,
            Remark = input.Remark,
            Images = input.Images,
            CreatorId = operationUser.UserId,
            CreatorName = operationUser.Name,
            BatchNumber = input.BatchNumber,
            ProcessStatus = OffsetOrderProcessingStatus.Processing,
        };
        var dingApplyMoneyTypes = new List<OffsetOrderMoneyType>() {
            OffsetOrderMoneyType.Refund,
            OffsetOrderMoneyType.Compensate,
             OffsetOrderMoneyType.RefundOffline,
              OffsetOrderMoneyType.CompensateCredit,
        };
        bool hasAudit = false;
        if (input.BusinessType == OffsetOrderBusinessType.HotelOrder && input.OffsetType == OffsetOrderType.Receipt
            && input.DingtalkApply != null && dingApplyMoneyTypes.Contains(input.MoneyType)
            && !string.IsNullOrEmpty(input.DingtalkApply.ProcessCode))
        {
            hasAudit = true;
            List<string> images = new List<string>();
            if (offsetOrder.Images?.Any() is true)
            {
                foreach (var item in offsetOrder.Images)
                {
                    var img = $"{_ossConfig.Value.DomainName}/{item}";
                    images.Add(img);
                }
            }
            Contracts.Common.Tenant.DTOs.DingtalkApiConfig.SaasFieldMapDingtalkManager.SetCreateAfterMapField(input.DingtalkApply,
                new SetCreateAfterMapFieldDto(offsetOrder.WorkOrderId,
                offsetOrder.OffsetAmountType,
                offsetOrder.OffsetType,
                offsetOrder.Amount,
                offsetOrder.CurrencyCode,
                offsetOrder.CreatTime,
                offsetOrder.Id,
                offsetOrder.Remark,
                images,
                offsetOrder.CreatorName));

            var apply = new OffsetOrderDingtalkApply()
            {
                AuditStatus = OffsetOrderDingtalkApplyAuditStatus.Wait,
                BaseOrderId = offsetOrder.BaseOrderId,
                CreatorId = operationUser.UserId,
                CreatorName = operationUser.Name,
                OffsetOrderId = offsetOrder.Id,
                ProcessCode = input.DingtalkApply.ProcessCode,
                Title = $"{operationUser.Name}提交的{input.DingtalkApply.Name}",
                SchemaContent = JsonConvert.SerializeObject(input.DingtalkApply)
            };
            await _dbContext.OffsetOrderDingtalkApply.AddAsync(apply);

            // 触发钉钉审批申请提交
            await _capPublisher.PublishAsync(CapTopics.Tenant.DingtalkApplySubmit,
               new DingtalkApplySubmitMessage
               {
                   ProcessCode = input.DingtalkApply.ProcessCode,
                   OffsetOrderDingtalkApplyId = apply.Id,
                   TenantId = checkResult.TenantId,
                   UserId = operationUser.UserId,
                   Items = input.DingtalkApply.Items,
               });
        }

        // 判断是否需要钉钉审批的抵冲单
        // 如果是酒店抵充单，则需要钉钉审批
        // 如果是其他抵充单，则不需要钉钉审批
        // 如果是酒店抵充单，并且是线下的，则需要售后处理后才能改状态
        if (offsetOrder.OffsetType == OffsetOrderType.Receipt)
        {
            switch (checkResult.PayType)
            {
                case PayType.YeePay:
                case PayType.AdvancePayment:
                case PayType.AgencyCreditPay:
                case PayType.Offline: //线下支付支持抵充单的添加
                    switch (offsetOrder.OffsetAmountType)
                    {
                        case OffsetOrderMoneyType.Additional:
                            offsetOrder.SettlementType = OffsetOrderSettlementType.AfterSaleFinancialHandleOrder;
                            if (offsetOrder.BusinessType != OffsetOrderBusinessType.HotelOrder)
                            {
                                offsetOrder.ProcessStatus = OffsetOrderProcessingStatus.Success;
                            }
                            await _afterSaleFinancialHandleOrderService.Create(new Contracts.Common.Order.DTOs.AfterSaleFinancialHandleOrder.CreateInput
                            {
                                AgencyId = checkResult.AgencyId,
                                AgencyName = checkResult.AgencyName,
                                Amount = offsetOrder.Amount,
                                CurrencyCode = offsetOrder.CurrencyCode,
                                BusinessType = offsetOrder.BusinessType,
                                OffsetOrderId = offsetOrder.Id,
                                TenantId = checkResult.TenantId,
                                HandleType = AfterSaleFinancialHandleType.Additional,
                            });
                            break;
                        case OffsetOrderMoneyType.Compensate:
                        case OffsetOrderMoneyType.RefundOffline:
                            offsetOrder.SettlementType = OffsetOrderSettlementType.AfterSaleFinancialHandleOrder;
                            if (offsetOrder.BusinessType != OffsetOrderBusinessType.HotelOrder)
                            {
                                offsetOrder.ProcessStatus = OffsetOrderProcessingStatus.Success;
                            }
                            if (hasAudit == false)
                            {
                                await _afterSaleFinancialHandleOrderService.Create(new Contracts.Common.Order.DTOs.AfterSaleFinancialHandleOrder.CreateInput
                                {
                                    AgencyId = checkResult.AgencyId,
                                    AgencyName = checkResult.AgencyName,
                                    Amount = offsetOrder.Amount,
                                    CurrencyCode = offsetOrder.CurrencyCode,
                                    BusinessType = offsetOrder.BusinessType,
                                    OffsetOrderId = offsetOrder.Id,
                                    TenantId = checkResult.TenantId,
                                    HandleType = offsetOrder.OffsetAmountType switch
                                    {
                                        OffsetOrderMoneyType.Compensate => AfterSaleFinancialHandleType.Compensate,
                                        OffsetOrderMoneyType.RefundOffline => AfterSaleFinancialHandleType.Refund,
                                        _ => throw new NotImplementedException(),
                                    }
                                });
                            }
                            break;
                        case OffsetOrderMoneyType.Refund:
                            offsetOrder.SettlementType = OffsetOrderSettlementType.OrderRefund;
                            if (hasAudit == false)
                            {
                                // 等退款处理成功后，再修改 ProcessStatus 状态
                                await _capPublisher.PublishAsync(CapTopics.Payment.OrderRefund, new OrderRefundMessage
                                {
                                    OrderRefundType = OrderRefundType.OffsetOrder,
                                    RefundOrderId = offsetOrder.Id,//抵冲单id
                                    OrderId = offsetOrder.BaseOrderId,
                                    PayType = checkResult.PayType,
                                    RefundAmount = Math.Abs(offsetOrder.Amount),
                                    TenantId = checkResult.TenantId,
                                });
                            }
                            break;
                        case OffsetOrderMoneyType.AdditionalCredit:
                            //扣减分销商额度，并在应收款中新增一条抵充记录
                            try
                            {
                                await _agencyCreditPayService.OffsetOrderCreate(new OffsetOrderCreateRecordInput
                                {
                                    UniqueOrderId = 0,
                                    AgencyId = offsetOrder.AgencyId,
                                    CreditBusinessType = CreditBusinessType.OffsetOrder,
                                    OrderType = MapperOrderType(offsetOrder.BusinessType),
                                    OrderId = offsetOrder.Id,
                                    OrderAmount = -offsetOrder.Amount
                                }, checkResult.TenantId);
                                offsetOrder.ProcessStatus = OffsetOrderProcessingStatus.Success;
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError("OffsetOrderCreate -> AdditionalCredit error:{@BaseOrderId},{@offsetOrderId},{@ex}", offsetOrder.BaseOrderId, offsetOrder.Id, ex);
                                offsetOrder.ProcessErrorMsg = ex.Message;
                                offsetOrder.ProcessStatus = OffsetOrderProcessingStatus.Failed;
                            }
                            break;
                        case OffsetOrderMoneyType.CompensateCredit:
                            if (hasAudit == false)
                            {
                                //提交后增加分销商额度，并在应收款中新增一条抵充记录
                                try
                                {
                                    await _agencyCreditPayService.OffsetOrderCreate(new OffsetOrderCreateRecordInput
                                    {
                                        UniqueOrderId = 0,
                                        AgencyId = offsetOrder.AgencyId,
                                        CreditBusinessType = CreditBusinessType.OffsetOrder,
                                        OrderType = MapperOrderType(offsetOrder.BusinessType),
                                        OrderId = offsetOrder.Id,
                                        OrderAmount = Math.Abs(offsetOrder.Amount)
                                    }, offsetOrder.TenantId);
                                    offsetOrder.ProcessStatus = OffsetOrderProcessingStatus.Success;
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError("OffsetOrderCreate -> CompensateCredit error:{@BaseOrderId},{@offsetOrderId},{@ex}", offsetOrder.BaseOrderId, offsetOrder.Id, ex);
                                    offsetOrder.ProcessErrorMsg = ex.Message;
                                    offsetOrder.ProcessStatus = OffsetOrderProcessingStatus.Failed;
                                }
                            }
                            break;
                    }
                    break;
            }
        }

        if (offsetOrder.OffsetType == OffsetOrderType.Payable)
        {
            offsetOrder.ProcessStatus = OffsetOrderProcessingStatus.Success;
            //处理VCC交易异常场景
            await _capPublisher.PublishAsync(CapTopics.Order.UpdateVccSettlementOrder,
                new UpdateVccSettlementOrderMessage
                {
                    BaseOrderId = offsetOrder.BaseOrderId,
                    OffsetOrderId = offsetOrder.Id,
                    SupplierId = offsetOrder.SupplierId,
                    TenantId = checkResult.TenantId
                });
        }

        await _dbContext.OffsetOrders.AddAsync(offsetOrder);
        return offsetOrder.Id;
    }

    public async Task Edit(EditOffsetOrderInput input, OperationUserDto operationUser, long tenantId)
    {
        var lockSecret = Guid.NewGuid().ToString();
        var lockName = $"{OFFSET_ORDER_SETTLEMENT_CREATE_LOCK}:{tenantId}";
        await _redisClient.LockTakeWaitingAsync(lockName, lockSecret, TimeSpan.FromSeconds(2));
        try
        {
            var offsetOrder = await _dbContext.OffsetOrders.FirstOrDefaultAsync(x => x.Id == input.Id);
            await CheckSettlementOrder(input.Id, offsetOrder.OffsetType);
            offsetOrder.Remark = input.Remark;
            offsetOrder.UpdaterId = operationUser.UserId;
            offsetOrder.UpdaterName = operationUser.Name;
            offsetOrder.UpdateTime = DateTime.Now;
            offsetOrder.Amount = input.MoneyType switch
            {
                OffsetOrderMoneyType.Additional => input.Amount,
                OffsetOrderMoneyType.Refund => -input.Amount,
                _ => throw new NotImplementedException()
            };
            await _dbContext.SaveChangesAsync();
        }
        finally
        {
            await _redisClient.LockReleaseAsync(lockName, lockSecret);
        }
    }

    public async Task Delete(long id, long tenantId)
    {
        var lockSecret = Guid.NewGuid().ToString();
        var lockName = $"{OFFSET_ORDER_SETTLEMENT_CREATE_LOCK}:{tenantId}";
        await _redisClient.LockTakeWaitingAsync(lockName, lockSecret, TimeSpan.FromSeconds(2));
        try
        {
            var offsetOrder = await _dbContext.OffsetOrders.FirstOrDefaultAsync(x => x.Id == id);
            await CheckSettlementOrder(id, offsetOrder.OffsetType);
            _dbContext.OffsetOrders.Remove(offsetOrder);
            await _dbContext.SaveChangesAsync();
        }
        finally
        {
            await _redisClient.LockReleaseAsync(lockName, lockSecret);
        }
    }

    public async Task<PagingModel<SearchOffsetOrderOutput>> Search(SearchOffsetOrderInput input)
    {
        var query = BuildOffsetOrderQuery(input);
        var result = await query
            .OrderByDescending(x => x.UpdateTime)
            .PagingAsync(input.PageIndex, input.PageSize);
        
        await EnrichOffsetOrderData(result.Data);
        return result;
    }

    /// <summary>
    /// 抵冲单导出查询
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>抵冲单列表（不分页）</returns>
    public async Task<IEnumerable<SearchOffsetOrderOutput>> ExportSearch(SearchOffsetOrderInput input)
    {
        var query = BuildOffsetOrderQuery(input);
        var result = await query
            .OrderByDescending(x => x.UpdateTime)
            .ToListAsync();
        return result;
    }

    /// <summary>
    /// 构建抵冲单查询基础查询
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>基础查询</returns>
    private IQueryable<SearchOffsetOrderOutput> BuildOffsetOrderQuery(SearchOffsetOrderInput input)
    {
        DateTime? beginDate = input.BeginTime.HasValue ? input.BeginTime.Value.Date : null;
        DateTime? endDate = input.EndTime.HasValue ? input.EndTime.Value.Date.AddDays(1) : null;
        
        var query = from offset in _dbContext.OffsetOrders.AsNoTracking()
                    from work in _dbContext.WorkOrders.AsNoTracking().Where(w => w.Id == offset.WorkOrderId).DefaultIfEmpty()
                    select new
                    {
                        offset,
                        work
                    };

        return query
            .WhereIF(input.Id is > 0, x => x.offset.Id == input.Id)
            .WhereIF(input.BaseOrderId is > 0, x => x.offset.BaseOrderId == input.BaseOrderId)
            .WhereIF(input.BusinessOrderId is > 0, x => x.offset.BusinessOrderId == input.BusinessOrderId)
            .WhereIF(input.AgencyId is > 0, x => x.offset.AgencyId == input.AgencyId)
            .WhereIF(input.SupplierId is > 0, x => x.offset.SupplierId == input.SupplierId)
            .WhereIF(input.BusinessType is > 0, x => x.offset.BusinessType == input.BusinessType)
            .WhereIF(input.OffsetType is > 0, x => x.offset.OffsetType == input.OffsetType)
            .WhereIF(input.Status is > 0, x => x.offset.Status == input.Status)
            .WhereIF(beginDate.HasValue, x => x.offset.CreatTime >= beginDate)
            .WhereIF(endDate.HasValue, x => x.offset.CreatTime < endDate)
            .WhereIF(input.WorkOrderId.HasValue, x => x.offset.WorkOrderId == input.WorkOrderId!.Value)
            .WhereIF(!string.IsNullOrWhiteSpace(input.HopWorkOrderNumber), x => x.work.HopWorkOrderNumber == input.HopWorkOrderNumber)
            .Select(x => new SearchOffsetOrderOutput
            {
                Id = x.offset.Id,
                WorkOrderId = x.offset.WorkOrderId,
                AgencyId = x.offset.AgencyId,
                Amount = x.offset.Amount,
                BaseOrderId = x.offset.BaseOrderId,
                BusinessOrderId = x.offset.BusinessOrderId,
                BusinessType = x.offset.BusinessType,
                CreatorId = x.offset.CreatorId,
                CreatorName = x.offset.CreatorName,
                CreatTime = x.offset.CreatTime,
                UpdateTime = x.offset.UpdateTime,
                CurrencyCode = x.offset.CurrencyCode,
                HopWorkOrderNumber = x.work != null ? x.work.HopWorkOrderNumber : null,
                OffsetAmountType = x.offset.OffsetAmountType,
                OffsetType = x.offset.OffsetType,
                ProductId = x.offset.ProductId,
                ProductName = x.offset.ProductName,
                ProductSkuId = x.offset.ProductSkuId,
                ProductSkuName = x.offset.ProductSkuName,
                Remark = x.offset.Remark,
                Status = x.offset.Status,
                SupplierId = x.offset.SupplierId,
                UpdaterId = x.offset.UpdaterId,
                UpdaterName = x.offset.UpdaterName,
                ProcessErrorMsg = x.offset.ProcessErrorMsg,
                ProcessStatus = x.offset.ProcessStatus,
            });
    }

    /// <summary>
    /// 丰富抵冲单数据（添加钉钉审批信息）
    /// </summary>
    /// <param name="offsetOrders">抵冲单列表</param>
    private async Task EnrichOffsetOrderData(IEnumerable<SearchOffsetOrderOutput> offsetOrders)
    {
        if (!offsetOrders.Any()) return;

        var offsetOrderIds = offsetOrders.Select(x => x.Id).ToList();
        var offsetOrderDingtalkApplys = await _dbContext.OffsetOrderDingtalkApply.AsNoTracking()
            .Where(x => offsetOrderIds.Contains(x.OffsetOrderId))
            .ToListAsync();

        foreach (var item in offsetOrders)
        {
            var apply = offsetOrderDingtalkApplys.FirstOrDefault(x => x.OffsetOrderId == item.Id);
            item.AuditStatus = apply?.AuditStatus;
            item.DingtalkBusinessId = apply?.DingtalkBusinessId;
        }
    }

    public async Task<IEnumerable<GetOffsetOrderListOutput>> GetList(GetOffsetOrderListInput input)
    {
        var offsetOrders = await _dbContext.OffsetOrders.AsNoTracking()
            .WhereIF(input.BaseOrderIds.Any(), x => input.BaseOrderIds.Contains(x.BaseOrderId))
            .WhereIF(input.BusinessOrderIds.Any(), x => input.BusinessOrderIds.Contains(x.BusinessOrderId))
            .ToListAsync();
        var result = _mapper.Map<IEnumerable<GetOffsetOrderListOutput>>(offsetOrders);
        if (result.Any())
        {
            var offsetOrderIds = offsetOrders.Select(x => x.Id).ToList();
            var offsetOrderDingtalkApplys = await _dbContext.OffsetOrderDingtalkApply.AsNoTracking()
                .Where(x => offsetOrderIds.Contains(x.OffsetOrderId))
                .ToListAsync();
            foreach (var item in result)
            {
                var apply = offsetOrderDingtalkApplys.FirstOrDefault(x => x.OffsetOrderId == item.Id);
                item.AuditStatus = apply?.AuditStatus;
                item.DingtalkBusinessId = apply?.DingtalkBusinessId;
            }
        }
        return result;
    }

    /// <summary>
    /// 检查是否已经生成结算单
    /// </summary>
    private async Task CheckSettlementOrder(long id, OffsetOrderType offsetType)
    {
        //付款编码或收款编码为非空时，不允许操作删除、不允许操作编辑
        switch (offsetType)
        {
            case OffsetOrderType.Receipt:
                //查询是否已经生成收款结算单
                var receiptSettlementOrderDetail = await _dbContext.ReceiptSettlementOrderDetails.AsNoTracking()
                    .FirstOrDefaultAsync(x => x.BusinessOrderId == id
                                              && x.BusinessType == ReceiptSettlementBusinessType.OffsetOrder);
                if (receiptSettlementOrderDetail != null)
                    throw new BusinessException(ErrorTypes.Order.OffsetOrderInSettlement);

                break;
            case OffsetOrderType.Payable:
                //查询是否已经生成付款结算单
                var payableSettlementOrderDetail = await _dbContext.SettlementOrderDetails.AsNoTracking()
                    .FirstOrDefaultAsync(x => x.BusinessOrderId == id
                                              && x.SettlementBusinessType == SettlementBusinessType.OffsetOrder);
                if (payableSettlementOrderDetail != null)
                    throw new BusinessException(ErrorTypes.Order.OffsetOrderInSettlement);

                break;
            default:
                throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 抵冲单添加检查
    /// </summary>
    private async Task<CheckBusinessOrderOutput> Check(CheckBusinessOrderInput input)
    {
        var result = new CheckBusinessOrderOutput();
        var businessOrderInfo = await GetBusinessOrderInfo(input.BaseOrderId, input.BusinessOrderId, input.BusinessType);

        var hotelOrderStatus = new[]
        {
            HotelOrderStatus.WaitingForConfirm,
            HotelOrderStatus.WaitingForCheckIn,
            HotelOrderStatus.CheckedIn,
            HotelOrderStatus.Finished
        };

        switch (input.OffsetType)
        {
            case OffsetOrderType.Receipt:
                if (businessOrderInfo.AgencyId == 0)
                    throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

                if (input.BusinessType == OffsetOrderBusinessType.HotelOrder)
                {
                    //待确认、待入住、已入住、已完成 四个状态之一 && 非关闭状态赔付
                    if (!hotelOrderStatus.Contains(businessOrderInfo.HotelOrderStatus) &&
                        !(businessOrderInfo.BaseOrderStatus == BaseOrderStatus.Closed
                        && input.OffsetOrderMoneyType is OffsetOrderMoneyType.Compensate or OffsetOrderMoneyType.CompensateCredit))
                        throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
                }
                else
                {
                    //抵充单除了待支付，已关闭的状态都需要支持添加
                    if (businessOrderInfo.BaseOrderStatus == BaseOrderStatus.WaitingForPay
                        || businessOrderInfo.BaseOrderStatus == BaseOrderStatus.Closed)
                        throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
                    if (businessOrderInfo.AggregateOrderStatus == AggregateOrderStatus.WaitingForConfirm)
                        throw new BusinessException(ErrorTypes.Order.OffsetOrderNeedConfirmed);
                }

                result.AgencyId = businessOrderInfo.AgencyId;
                result.AgencyName = businessOrderInfo.AgencyName;
                //入账抵冲单中，币种与订单支付币种相同
                result.CurrencyCode = businessOrderInfo.PaymentCurrencyCode;

                var refundAmount = businessOrderInfo.BaseOrderStatus == BaseOrderStatus.Closed ?
                    (await _dbContext.RefundOrders
                    .Where(x => x.BaseOrderId == input.BaseOrderId && x.Status == RefundOrderStatus.Refunded)
                    .SumAsync(x => x.TotalAmount)) : 0;
                var receiptOffsets = await _dbContext.OffsetOrders
                    .Where(x => x.BaseOrderId == input.BaseOrderId && x.OffsetType == OffsetOrderType.Receipt)
                    .Where(x => (x.ProcessStatus != OffsetOrderProcessingStatus.Failed
                                   && !(x.OffsetType == OffsetOrderType.Receipt && x.OffsetAmountType == OffsetOrderMoneyType.Additional))
                      || (x.OffsetType == OffsetOrderType.Receipt && x.OffsetAmountType == OffsetOrderMoneyType.Additional && x.ProcessStatus == OffsetOrderProcessingStatus.Success)
                    ) // 1、过滤失败的抵充单，如审核不通过、撤销等的不参与后续计算，防止超额申请
                      // 2、销售总金额线下加收的时候，必须售后财务处理通过后才参与计算
                    .GroupBy(s => s.OffsetAmountType)
                    .Select(g => new { OffsetAmountType = g.Key, Amount = g.Sum(s => s.Amount) })
                    .ToListAsync();
                var receiptOffsetRefund = receiptOffsets.Where(x => x.OffsetAmountType == OffsetOrderMoneyType.Refund).Sum(x => x.Amount);
                var receiptOffsetAmount = receiptOffsets.Sum(x => x.Amount);
                var totalSaleAmount = businessOrderInfo.PaymentAmount - refundAmount + receiptOffsetAmount; // 总销售金额
                var totalRefundAmount = businessOrderInfo.PaymentAmount - refundAmount + receiptOffsetRefund;// 总可线上退款金额（支付可退金额）
                // 判断条件看 https://devops.aliyun.com/projex/project/44ac0ae9b062a474b824540a6e/bug/10045b87d61f2ce2cef93478e1 评论图
                switch (input.OffsetOrderMoneyType)
                {
                    case OffsetOrderMoneyType.Refund:
                        //var receiptOffsetRefund = receiptOffsets
                        //    .FirstOrDefault(s => s.OffsetAmountType == OffsetOrderMoneyType.Refund)?.Amount;
                        if ((totalRefundAmount + input.Amount) < 0)
                            throw new BusinessException(ErrorTypes.Order.ReceiptOffsetOrderRefundOutoffAmount);
                        if ((totalRefundAmount + input.Amount) < 0)
                            throw new BusinessException(ErrorTypes.Order.ReceiptOffsetOrderRefundOutTotalSaleAmount);
                        break;
                    case OffsetOrderMoneyType.RefundOffline:
                        if ((totalSaleAmount + input.Amount) < 0)
                            throw new BusinessException(ErrorTypes.Order.ReceiptOffsetOrderRefundOutTotalSaleAmount);

                        break;
                    case OffsetOrderMoneyType.Compensate:
                    case OffsetOrderMoneyType.CompensateCredit:
                        if (totalSaleAmount > 0) // 总销售金额大于0，还没退款完，不算赔付
                            throw new BusinessException(ErrorTypes.Order.ReceiptOffsetOrderCannotCompensated);
                        break;
                }
                //线上额度 检查分销商额度状态
                if (input.OffsetOrderMoneyType is OffsetOrderMoneyType.AdditionalCredit or OffsetOrderMoneyType.CompensateCredit)
                {
                    if (businessOrderInfo.PayType != PayType.AgencyCreditPay)
                        throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

                    var agencyCredit = await _agencyCreditPayService.GetByAgencyId(businessOrderInfo.AgencyId);
                    if (agencyCredit?.Status is not true)
                    {
                        throw new BusinessException(ErrorTypes.Tenant.AgencyCreditDisabled);
                    }
                    if (input.OffsetOrderMoneyType is OffsetOrderMoneyType.AdditionalCredit && agencyCredit.Balance < input.Amount)
                    {
                        throw new BusinessException(ErrorTypes.Tenant.CreditNotEnough);
                    }
                }
                break;
            case OffsetOrderType.Payable:
                //自营产品不允许添加出账抵冲
                if (businessOrderInfo.SupplierId == 0)
                    throw new BusinessException(ErrorTypes.Order.PayableOffsetOrderNeedAgencyProduct);

                if (input.BusinessType == OffsetOrderBusinessType.HotelOrder)
                {
                    //酒店订单-待确认、待入住、已入住、已完成 四个状态之一
                    if (!hotelOrderStatus.Contains(businessOrderInfo.HotelOrderStatus))
                    {
                        throw new BusinessException(ErrorTypes.Order.PayableOffsetOrderNeedAgencyProduct);
                    }
                }
                else
                {
                    //针对玩乐品类在VEBK系统上的抵冲单，在订单状态为【待确认】下，不能创建抵冲单
                    if (businessOrderInfo.AggregateOrderStatus == AggregateOrderStatus.WaitingForConfirm)
                        throw new BusinessException(ErrorTypes.Order.OffsetOrderNeedConfirmed);
                }

                result.SupplierId = businessOrderInfo.SupplierId;

                //出账抵冲单中，币种与供应商结算币种相同。
                result.CurrencyCode = businessOrderInfo.CostCurrencyCode;

                var payableOffsetAmount = await _dbContext.OffsetOrders
                    .Where(x => x.BaseOrderId == input.BaseOrderId && x.OffsetType == OffsetOrderType.Payable)
                    .SumAsync(x => x.Amount);
                switch (input.OffsetOrderMoneyType)
                {
                    case OffsetOrderMoneyType.Refund:
                    case OffsetOrderMoneyType.RefundOffline:
                        //if ((businessOrderInfo.CostAmount + input.Amount + payableOffsetAmount) < 0)
                        //    throw new BusinessException(ErrorTypes.Order.PayableOffsetOrderRefundOutoffAmount);
                        break;
                    case OffsetOrderMoneyType.Compensate:
                        if (businessOrderInfo.CostAmount + payableOffsetAmount > 0)
                            throw new BusinessException(ErrorTypes.Order.PayableOffsetOrderCannotCompensated);
                        break;
                }
                break;
            default:
                throw new NotImplementedException();
        }

        result.ProductId = businessOrderInfo.ProductId;
        result.ProductName = businessOrderInfo.ProductName;
        result.ProductSkuId = businessOrderInfo.ProductSkuId;
        result.ProductSkuName = businessOrderInfo.ProductSkuName;
        result.TenantId = businessOrderInfo.TenantId;
        result.PayType = businessOrderInfo.PayType;
        return result;
    }

    /// <summary>
    /// 查询业务订单信息
    /// </summary>
    private async Task<GetBusinessOrderOutput> GetBusinessOrderInfo(
            long baseOrderId,
            long businessOrderId,
            OffsetOrderBusinessType businessType)
    {
        var result = new GetBusinessOrderOutput();

        var baseOrder = await _dbContext.BaseOrders.AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == baseOrderId);
        if (baseOrder is null)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var aggregateOrder = await _dbContext.AggregateOrders.AsNoTracking()
            .Where(s => s.OrderStatus != AggregateOrderStatus.ChannelAbnormal)
            .Select(s => new { s.Id, s.BaseOrderId, s.OrderStatus, s.ConfirmStatus })
            .FirstOrDefaultAsync(x => x.BaseOrderId == baseOrder.Id);

        result.TenantId = baseOrder.TenantId;
        result.AgencyId = baseOrder.AgencyId;
        result.AgencyName = baseOrder.AgencyName;
        result.PaymentCurrencyCode = baseOrder.PaymentCurrencyCode;
        result.PaymentAmount = baseOrder.PaymentAmount;
        result.PayType = baseOrder.PaymentType;
        result.BaseOrderStatus = baseOrder.Status;
        result.AggregateOrderStatus = aggregateOrder?.OrderStatus;
        if (businessType == OffsetOrderBusinessType.HotelOrder)
        {
            var hotelOrder = await _dbContext.HotelOrders
                .Where(x => x.Id == businessOrderId)
                .Select(x => new { x.PriceStrategyRoomsCount })
                .FirstOrDefaultAsync();
            var hotelOrderPrice = await _dbContext.HotelOrderCalendarPrices.AsNoTracking()
                .Where(x => x.HotelOrderId == businessOrderId)
                .GroupBy(x => x.CostCurrencyCode)
                .Select(x => new { CostCurrencyCode = x.Key, CostAmount = x.Sum(s => s.CostPrice) })
                .FirstOrDefaultAsync();
            result.CostAmount = hotelOrderPrice.CostAmount * hotelOrder.PriceStrategyRoomsCount;
            result.CostCurrencyCode = hotelOrderPrice.CostCurrencyCode;
        }
        else
        {
            var baseOrderPrice = await _dbContext.OrderPrices.AsNoTracking()
                .Where(x => x.BaseOrderId == baseOrderId)
                .GroupBy(x => x.CostCurrencyCode)
                .Select(x => new { CostCurrencyCode = x.Key, CostAmount = x.Sum(s => s.CostPrice * s.Quantity) })
                .FirstOrDefaultAsync();
            if (baseOrderPrice != null)
            {
                result.CostAmount = baseOrderPrice.CostAmount;
                result.CostCurrencyCode = baseOrderPrice.CostCurrencyCode;
            }
        }

        switch (businessType)
        {
            case OffsetOrderBusinessType.TicketOrder:
                var ticketOrder = await _dbContext.TicketOrders.AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == businessOrderId
                                              && x.BaseOrderId == baseOrderId
                                              && x.ProductNeedReservation == false);
                if (ticketOrder is null)
                    throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

                result.IsFinished = baseOrder.Status == BaseOrderStatus.Finished;
                result.SupplierId = ticketOrder.ProductSupplierId;
                result.AgencyId = baseOrder.AgencyId;
                result.ProductId = ticketOrder.ProductId;
                result.ProductName = ticketOrder.ProductTitle;
                result.ProductSkuId = ticketOrder.SkuId;
                result.ProductSkuName = ticketOrder.SkuName;
                result.SellingPlatform = baseOrder.SellingPlatform;
                result.TenantId = baseOrder.TenantId;
                break;
            case OffsetOrderBusinessType.MailOrder:
                var mailOrder = await _dbContext.MailOrders.AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == businessOrderId
                                              && x.BaseOrderId == baseOrderId);
                if (mailOrder is null)
                    throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

                result.IsFinished = mailOrder.Status == MailOrderStatus.Finished;
                result.SupplierId = mailOrder.ProductSupplierId;
                result.AgencyId = baseOrder.AgencyId;
                result.ProductId = mailOrder.ProductId;
                result.ProductName = mailOrder.ProductName;
                result.ProductSkuId = mailOrder.ProductSkuId;
                result.ProductSkuName = mailOrder.ProductSkuName;
                result.SellingPlatform = baseOrder.SellingPlatform;
                result.TenantId = baseOrder.TenantId;

                break;
            case OffsetOrderBusinessType.HotelOrder:
                var hotelOrder = await _dbContext.HotelOrders.AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == businessOrderId
                                              && x.BaseOrderId == baseOrderId);
                if (hotelOrder is null)
                    throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

                result.IsFinished = hotelOrder.Status == HotelOrderStatus.Finished;
                result.SupplierId = hotelOrder.PriceStrategySupplierId;
                result.AgencyId = baseOrder.AgencyId;
                result.ProductId = hotelOrder.HotelId;
                result.ProductName = hotelOrder.HotelName;
                result.ProductSkuId = hotelOrder.PriceStrategyId;
                result.ProductSkuName = hotelOrder.PriceStrategyName;
                result.SellingPlatform = baseOrder.SellingPlatform;
                result.TenantId = baseOrder.TenantId;
                result.HotelOrderStatus = hotelOrder.Status;

                break;
            case OffsetOrderBusinessType.ReservationOrder:
                var reservationOrder = await _dbContext.ReservationOrders.AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == businessOrderId
                                              && x.BaseOrderId == baseOrderId);

                if (reservationOrder is null)
                    throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

                var reservationTicketOrder = await _dbContext.TicketOrders.AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == reservationOrder.TicketOrderId
                                              && x.BaseOrderId == baseOrderId
                                              && x.ProductNeedReservation == true);

                result.IsFinished = reservationOrder.Status == ReservationStatus.Finished;
                result.PayType = reservationOrder.PaymentType;
                result.SupplierId = reservationTicketOrder.ProductSupplierId;
                result.AgencyId = baseOrder.AgencyId;
                result.ProductId = reservationTicketOrder.ProductId;
                result.ProductName = reservationTicketOrder.ProductTitle;
                result.ProductSkuId = reservationTicketOrder.SkuId;
                result.ProductSkuName = reservationTicketOrder.SkuName;
                result.SellingPlatform = baseOrder.SellingPlatform;
                result.TenantId = baseOrder.TenantId;
                break;
            case OffsetOrderBusinessType.ScenicTicketOrder:
                var scenicOrder = await _dbContext.ScenicTicketOrders.AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == businessOrderId
                                              && x.BaseOrderId == baseOrderId);
                if (scenicOrder is null)
                    throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

                result.IsFinished = baseOrder.Status == BaseOrderStatus.Finished;
                result.SupplierId = scenicOrder.SupplierId;
                result.AgencyId = baseOrder.AgencyId;
                result.ProductId = scenicOrder.ScenicSpotId;
                result.ProductName = baseOrder.ResourceName;
                result.ProductSkuId = scenicOrder.ScenicTicketId;
                result.ProductSkuName = baseOrder.ProductName;
                result.SellingPlatform = baseOrder.SellingPlatform;
                result.TenantId = baseOrder.TenantId;
                break;
            case OffsetOrderBusinessType.LineOrder:
                var lineOrder = await _dbContext.TravelLineOrder.AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == businessOrderId
                                              && x.BaseOrderId == baseOrderId);
                if (lineOrder is null)
                    throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
                result.IsFinished = baseOrder.Status == BaseOrderStatus.Finished;
                result.SupplierId = lineOrder.SupplierId;
                result.AgencyId = baseOrder.AgencyId;
                result.ProductId = lineOrder.LineProductId;
                result.ProductName = baseOrder.ProductName;
                result.ProductSkuId = lineOrder.LineProductSkuId;
                result.ProductSkuName = baseOrder.ProductSkuName;
                result.SellingPlatform = baseOrder.SellingPlatform;
                result.TenantId = baseOrder.TenantId;
                break;
            case OffsetOrderBusinessType.CarProductOrder:
                var carProductOrder = await _dbContext.CarProductOrders.AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == businessOrderId
                                              && x.BaseOrderId == baseOrderId);
                if (carProductOrder is null)
                    throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

                result.IsFinished = baseOrder.Status == BaseOrderStatus.Finished;
                result.SupplierId = carProductOrder.SupplierId;
                result.AgencyId = baseOrder.AgencyId;
                result.ProductId = carProductOrder.CarProductId;
                result.ProductName = baseOrder.ProductName;
                result.ProductSkuId = carProductOrder.CarProductSkuId;
                result.ProductSkuName = baseOrder.ProductSkuName;
                result.SellingPlatform = baseOrder.SellingPlatform;
                result.TenantId = baseOrder.TenantId;

                break;
            default:
                throw new NotImplementedException();
        }

        return result;
    }

    /// <summary>
    /// 映射OrderType
    /// </summary>
    /// <param name="businessType"></param>
    /// <returns></returns>
    private OrderType MapperOrderType(OffsetOrderBusinessType businessType)
    {
        var orderType = businessType switch
        {
            OffsetOrderBusinessType.TicketOrder => OrderType.Ticket,
            OffsetOrderBusinessType.MailOrder => OrderType.Mail,
            OffsetOrderBusinessType.HotelOrder => OrderType.Hotel,
            OffsetOrderBusinessType.ReservationOrder => OrderType.Ticket,
            OffsetOrderBusinessType.ScenicTicketOrder => OrderType.ScenicTicket,
            OffsetOrderBusinessType.LineOrder => OrderType.TravelLineOrder,
            OffsetOrderBusinessType.CarProductOrder => OrderType.CarProduct,
            _ => throw new ArgumentOutOfRangeException(nameof(businessType), businessType, null)
        };
        return orderType;
    }

    public async Task RefundResult(RefundResultMessage receive)
    {
        var offsetOrder = await _dbContext.OffsetOrders.IgnoreQueryFilters()
            .Where(x => x.Id == receive.RefundOrderId)
            .FirstOrDefaultAsync();
        //offsetOrder.Status = OffsetOrderStatus.SettlementCompleted;
        //offsetOrder.UpdateTime = DateTime.Now;
        var baseOrder = await _dbContext.BaseOrders.IgnoreQueryFilters()
          .Where(x => x.Id == offsetOrder.BaseOrderId)
          .Select(x => new { x.PaymentType })
          .FirstOrDefaultAsync();
        if (receive.IsSuccess)
        {
            offsetOrder.ProcessStatus = OffsetOrderProcessingStatus.Success;

            if (offsetOrder.OffsetType == OffsetOrderType.Receipt
                && (baseOrder.PaymentType == PayType.YeePay || baseOrder.PaymentType == PayType.AdvancePayment))
            {
                offsetOrder.Status = OffsetOrderStatus.SettlementCompleted;
            }
        }
        else
        {
            offsetOrder.ProcessStatus = OffsetOrderProcessingStatus.Failed;
            offsetOrder.ProcessErrorMsg = receive.FailedReason;
        }
        offsetOrder.UpdateTime = DateTime.Now;

        if (baseOrder.PaymentType == PayType.YeePay)
        {
            //收款流水
            await _capPublisher.PublishAsync(CapTopics.Payment.TenantReceiptFlow, new TenantReceiptFlowMessage
            {
                TenantId = offsetOrder.TenantId,
                AgencyId = offsetOrder.AgencyId,
                Amount = offsetOrder.Amount,
                BusinessOrderId = offsetOrder.Id,
                CreateTime = DateTime.Now,
                PayType = baseOrder.PaymentType,
                TenantReceiptFlowType = TenantReceiptFlowType.OffsetOrderRefund,
            });
        }
        await _dbContext.SaveChangesAsync();
    }

    public async Task<GetOffsetOrderDetailOutput> Detail(long id)
    {
        var offsetOrderPages = await _dbContext.OffsetOrders.AsNoTracking()
            .Where(x => x.Id == id)
            .FirstOrDefaultAsync();

        return _mapper.Map<GetOffsetOrderDetailOutput>(offsetOrderPages);
    }

    /// <summary>
    /// 钉钉审批结果回调
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public async Task SyncOffsetOrderDingtalk(SyncOffsetOrderDingtalkMessage message)
    {

        var offsetOrderApply = await _dbContext.OffsetOrderDingtalkApply
            .Where(x => x.Id == message.OffsetOrderDingtalkApplyId)
            .Where(x => x.TenantId == message.TenantId)
            .FirstOrDefaultAsync();
        if (offsetOrderApply != null)
        {
            var offsetOrder = await _dbContext.OffsetOrders
                    .Where(x => x.Id == offsetOrderApply.OffsetOrderId)
                    .FirstOrDefaultAsync();

            offsetOrderApply.DingtalkInstanceId = message.InstanceId;
            offsetOrderApply.DingtalkBusinessId = message.BusinessId;
            offsetOrderApply.AuditStatus = message.AuditStatus;
            offsetOrderApply.ResultMsg = message.ErrorMsg;

            if (message.OperationRecords != null)
                offsetOrderApply.OperationRecords = JsonConvert.SerializeObject(message.OperationRecords);
            offsetOrderApply.UpdateTime = DateTime.Now;
            if (offsetOrderApply.AuditStatus != OffsetOrderDingtalkApplyAuditStatus.Wait
                && offsetOrderApply.AuditStatus != OffsetOrderDingtalkApplyAuditStatus.UnderApproval)
                offsetOrderApply.FinishTime = DateTime.Now;

            var baseOrder = await _dbContext.BaseOrders.FirstOrDefaultAsync(x => x.Id == offsetOrder.BaseOrderId);
            switch (offsetOrderApply.AuditStatus)
            {
                case OffsetOrderDingtalkApplyAuditStatus.Approved: // 审批通过,执行后续事件
                    switch (offsetOrder.OffsetAmountType)
                    {
                        case OffsetOrderMoneyType.Compensate:
                        case OffsetOrderMoneyType.RefundOffline:
                            await _afterSaleFinancialHandleOrderService.Create(new Contracts.Common.Order.DTOs.AfterSaleFinancialHandleOrder.CreateInput
                            {
                                AgencyId = baseOrder.AgencyId,
                                AgencyName = baseOrder.AgencyName,
                                Amount = offsetOrder.Amount,
                                CurrencyCode = offsetOrder.CurrencyCode,
                                BusinessType = offsetOrder.BusinessType,
                                OffsetOrderId = offsetOrder.Id,
                                TenantId = offsetOrder.TenantId,
                                HandleType = offsetOrder.OffsetAmountType switch
                                {
                                    OffsetOrderMoneyType.Compensate => AfterSaleFinancialHandleType.Compensate,
                                    OffsetOrderMoneyType.RefundOffline => AfterSaleFinancialHandleType.Refund,
                                    _ => throw new NotImplementedException(),
                                }
                            });
                            break;
                        case OffsetOrderMoneyType.Refund:
                            await _capPublisher.PublishAsync(CapTopics.Payment.OrderRefund, new OrderRefundMessage
                            {
                                OrderRefundType = OrderRefundType.OffsetOrder,
                                RefundOrderId = offsetOrder.Id,//抵冲单id
                                OrderId = offsetOrder.BaseOrderId,
                                PayType = baseOrder.PaymentType,
                                RefundAmount = Math.Abs(offsetOrder.Amount),
                                TenantId = baseOrder.TenantId,
                            });

                            break;
                        case OffsetOrderMoneyType.CompensateCredit:
                            //提交后增加分销商额度，并在应收款中新增一条抵充记录
                            try
                            {
                                await _agencyCreditPayService.OffsetOrderCreate(new OffsetOrderCreateRecordInput
                                {
                                    UniqueOrderId = 0,
                                    AgencyId = offsetOrder.AgencyId,
                                    CreditBusinessType = CreditBusinessType.OffsetOrder,
                                    OrderType = MapperOrderType(offsetOrder.BusinessType),
                                    OrderId = offsetOrder.Id,
                                    OrderAmount = Math.Abs(offsetOrder.Amount)
                                }, offsetOrder.TenantId);
                                offsetOrder.ProcessStatus = OffsetOrderProcessingStatus.Success;
                                offsetOrder.UpdateTime = DateTime.Now;
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError("OffsetOrderCreate -> CompensateCredit error:{@BaseOrderId},{@offsetOrderId},{@ex}", offsetOrder.BaseOrderId, offsetOrder.Id, ex);
                                offsetOrder.ProcessErrorMsg = ex.Message;
                                offsetOrder.ProcessStatus = OffsetOrderProcessingStatus.Failed;
                                offsetOrder.UpdateTime = DateTime.Now;
                            }
                            break;
                    }
                    break;
                case OffsetOrderDingtalkApplyAuditStatus.Fail:
                    offsetOrder.ProcessStatus = OffsetOrderProcessingStatus.Failed;
                    offsetOrder.ProcessErrorMsg = "审批申请失败";
                    offsetOrder.UpdateTime = DateTime.Now;
                    break;
                case OffsetOrderDingtalkApplyAuditStatus.Rejected:
                    offsetOrder.ProcessStatus = OffsetOrderProcessingStatus.Failed;
                    offsetOrder.ProcessErrorMsg = "审批拒绝";
                    offsetOrder.UpdateTime = DateTime.Now;
                    break;
                case OffsetOrderDingtalkApplyAuditStatus.Revoked:
                    offsetOrder.ProcessStatus = OffsetOrderProcessingStatus.Failed;
                    offsetOrder.ProcessErrorMsg = "审批撤销";
                    offsetOrder.UpdateTime = DateTime.Now;
                    break;
            }
            await _dbContext.SaveChangesAsync();
        }
    }
}