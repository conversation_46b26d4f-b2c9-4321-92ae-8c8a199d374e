using Common.ServicesHttpClient;
using Contracts.Common.Order.DTOs.OpenChannelOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Product.DTOs.LineProductOpenChannelSetting;
using Contracts.Common.Scenic.DTOs.TicketChannelSetting;
using Contracts.Common.Scenic.Enums;
using EfCoreExtensions.UOW;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Order.Api.ConfigModel;
using Order.Api.Extensions;
using Order.Api.Services.Interfaces;
using Order.Api.Services.OpenPlatform.Contracts;
using Order.Api.Services.OpenPlatform.Contracts.Channel;
using Order.Api.Services.OpenPlatform.Interfaces;

namespace Order.Api.Services;

public class OpenChannelOrderService : IOpenChannelOrderService
{
    private readonly CustomDbContext _dbContext;
    private readonly IOrderOssService _orderOssService;
    private readonly IOpenChannelService _openChannelService;
    private readonly IOptionsMonitor<OpenPlatformOrderConfig> _openPlatformOrderConfigMonitor;
    private readonly IOpenPlatformBaseService _openPlatformBaseService;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ServicesAddress _servicesAddress;
    
    private const int _successCode = 200;
    public OpenChannelOrderService(
        CustomDbContext dbContext,
        IOrderOssService orderOssService,
        IOpenChannelService openChannelService,
        IOptionsMonitor<OpenPlatformOrderConfig> openPlatformOrderConfigMonitor,
        IOpenPlatformBaseService openPlatformBaseService,
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> servicesAddressOptions)
    {
        _dbContext = dbContext;
        _orderOssService = orderOssService;
        _openChannelService = openChannelService;
        _openPlatformOrderConfigMonitor = openPlatformOrderConfigMonitor;
        _openPlatformBaseService = openPlatformBaseService;
        _httpClientFactory = httpClientFactory;
        _servicesAddress = servicesAddressOptions.Value;
    }


    public async Task<QueryChannelOrderDetailOutput> OrderDetail(QueryChannelOrderDetailInput input,long tenantId)
    {
        var result = new QueryChannelOrderDetailOutput();
        var request = new ChannelOrderDetailRequest
        {
            OtaType = input.SellingPlatform switch
            {
                SellingPlatform.Ctrip => OtaChannelType.Ctrip.ToString().ToLowerInvariant(),
                SellingPlatform.Meituan => OtaChannelType.Meituan.ToString().ToLowerInvariant(),
                SellingPlatform.Fliggy => OtaChannelType.AliTrip.ToString().ToLowerInvariant(),
                SellingPlatform.TikTok => OtaChannelType.DouYin.ToString().ToLowerInvariant(),
                _ => throw new ArgumentOutOfRangeException()
            },
            OtaOrderId = input.OtaOrderId,
        };
        var orderDetail = await _openChannelService.OrderDetail(request, tenantId);
        if (orderDetail.Code != _successCode)
        {
            return result;
        }

        result.SellingPlatform = input.SellingPlatform;
        result.ProductType = orderDetail.Data.ProductType;
        result.OtaOrderId = orderDetail.Data.OtaOrderId;
        result.OutSkuId = orderDetail.Data.OutSkuId;
        result.Passengers = orderDetail.Data
            .Passengers
            .Select(x =>
            {
                //处理空的saleType数据
                var saleType = x.SaleType;
                if (string.IsNullOrEmpty(x.SaleType))
                {
                    saleType = OpenChannelOrderSaleAgeType.Adu.ToString().ToLowerInvariant();
                }
                var passenger = new ChannelOrderPassengersItem
                {
                    SaleType = saleType,
                    OrderItemId = x.OrderItemId,
                    ExtendInfo = x.ExtendInfo,
                };
                return passenger;
            })
            .ToList();

        result.OrderItems = orderDetail.Data.OrderItems.Select(x =>
            {
                //处理空的saleType数据
                var saleType = x.SaleType;
                if (string.IsNullOrEmpty(x.SaleType))
                {
                    saleType = OpenChannelOrderSaleAgeType.Adu.ToString().ToLowerInvariant();
                }
                var orderPriceItem = new ChannelNewOrderPriceItem
                {
                    SaleType = saleType,
                    ItemId = x.ItemId,
                    OutSkuId = x.OutSkuId,
                    DiscountFee = x.DiscountFee,
                    DiscountFeeCurrency = x.DiscountFeeCurrency,
                    Price = x.Price,
                    PriceCurrency = x.PriceCurrency,
                    CostPrice = x.CostPrice,
                    CostCurrency = x.CostCurrency,
                    Quantity = x.Quantity
                };

                return orderPriceItem;
            })
            .ToList();
        
        var lineOrderContact = orderDetail.Data.Contacts.FirstOrDefault();
        result.ContactItem = new ChannelOrderContactItem
        {
            Name = lineOrderContact.Name,
            Mobile = lineOrderContact.Mobile,
            Email = lineOrderContact.Email,
            AreaCode = lineOrderContact.AreaCode,
            LocalPhone = lineOrderContact.LocalPhone,
            LocalAreaCode = lineOrderContact.LocalAreaCode,
            FirstName = lineOrderContact.FirstName,
            LastName = lineOrderContact.LastName,
            ContactSocialMedia = lineOrderContact.ContactSocialMedia,
            BirthDate = lineOrderContact.BirthDate,
            Card = lineOrderContact.Card,
            CardEndDate = lineOrderContact.CardEndDate,
            CardType = lineOrderContact.CardType,
            CardCountry = lineOrderContact.CardCountry,
            CardPlace = lineOrderContact.CardPlace,
            Nationality = lineOrderContact.Nationality,
            Gender = lineOrderContact.Gender,
        };
            
        return result;
    }
    
    [UnitOfWork]
    public async Task ChannelTimelinessVoucherSync(ChannelTimelinessVoucherSyncMessage receive)
    {
        if(string.IsNullOrEmpty(receive.ChannelOrderNo)) return;
        var channelOrderNos = receive.ChannelOrderNo.Split(',',StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries).ToList();
        if(channelOrderNos.Any() is false) return;
        channelOrderNos = channelOrderNos.Distinct().ToList();

        var otaChannelType = await ChannelTypeProcess(
            sellingPlatform: receive.SellingPlatform,
            agencyId: receive.AgencyId,
            tenantId: receive.TenantId);
        // 所属分销商不属于API渠道类型. 则不处理
        if(otaChannelType == default)
            return;
        
        var currentTimelinessChannelTypes = otaChannelType switch
        {
            OtaChannelType.AliTrip => PriceInventorySyncChannelType.Fliggy,
            OtaChannelType.Ctrip => PriceInventorySyncChannelType.Ctrip,
            OtaChannelType.Meituan => PriceInventorySyncChannelType.Meituan,
            OtaChannelType.DouYin => PriceInventorySyncChannelType.TikTok,
            _ => throw new ArgumentOutOfRangeException()
        };

        #region 触发时机判断

        var timelinessSettings = await GetProductTimelinessSetting(receive.OrderType, receive.ProductId, receive.TenantId);
        if (timelinessSettings.Any() is false) return;
        var timelinessTriggerCheckSuccess = timelinessSettings.Any(x =>
            x.TimelinessChannelType == currentTimelinessChannelTypes &&
            x.TimelinessTriggerType == receive.TimelinessTriggerType);
        if(timelinessTriggerCheckSuccess == false) return;
        

        #endregion

        #region 订单确认处理

        if (receive.NeedConfirm)
        {
            var confirmResponse = await OpenChannelConfirm(new OpenChannelConfirmInputData(
                ChannelOrderNoList : channelOrderNos,
                OtaChannelType : otaChannelType,
                TenantId : receive.TenantId
                ));
            if (receive.OrderType == OrderType.TravelLineOrder)
            {
                await UpdateLineConfirmStatus(receive.BaseOrderId,
                    receive.SubOrderId,
                    confirmResponse.Code,
                    confirmResponse.Msg);
            }
        }

        #endregion

        #region 时效凭证同步渠道

        if (receive.TimelinessChannelTypes != null)
        {
            var timelinessChannelTypesList = Enum.GetValues<PriceInventorySyncChannelType>()
                .Where(x => (x & receive.TimelinessChannelTypes) == x)
                .ToList();
            //判断是否需要时效同步
            if (timelinessChannelTypesList.Contains(currentTimelinessChannelTypes))
            {
                //发货处理
                var timelinessVoucherPdfPath = _orderOssService.GetAbsolutePath(_openPlatformOrderConfigMonitor.CurrentValue.ChannelTimelinessVoucherPdfPath);
                var timelinessVoucherImagePath = _orderOssService.GetAbsolutePath(_openPlatformOrderConfigMonitor.CurrentValue.ChannelTimelinessVoucherImagePath);
                var request = new ChannelOrderDeliveryRequest
                {
                    OtaType = otaChannelType.ToString().ToLowerInvariant(),
                    Pdfs = new List<string> {timelinessVoucherPdfPath},
                    Images = new List<string> {timelinessVoucherImagePath}
                };
                foreach (var channelOrderNo in channelOrderNos)
                {
                    request.OtaOrderId = channelOrderNo;
                    var response = await _openChannelService.OrderDeliver(request, receive.TenantId);
                }

                var aggregateOrder = await _dbContext.AggregateOrders.IgnoreQueryFilters()
                    .FirstOrDefaultAsync(x => x.BaseOrderId == receive.BaseOrderId);
                if (aggregateOrder != null)
                    aggregateOrder.ProcessingLevelTag = AggregateOrderProcessingLevelTag.Emergency; //紧急标签
                
                //订单标记修改[虚拟凭证]
                switch (receive.OrderType)
                {
                    case OrderType.ScenicTicket:
                        
                        var scenicTicketOrder = await _dbContext.ScenicTicketOrders
                            .IgnoreQueryFilters()
                            .FirstOrDefaultAsync(x => x.Id == receive.SubOrderId);
                        if (scenicTicketOrder != null)
                        {
                            scenicTicketOrder.IsVirtualVoucher = true;
                        }
                        
                        break;
                    case OrderType.TravelLineOrder:
                        
                        var travelLineOrder = await _dbContext.TravelLineOrder
                            .IgnoreQueryFilters()
                            .FirstOrDefaultAsync(x => x.Id == receive.SubOrderId);
                        if (travelLineOrder != null)
                        {
                            travelLineOrder.IsVirtualVoucher = true;
                        }
                        
                        break;
                }
            }
        }

        #endregion
    }
    
    public async Task ChannelOrderFlagModify(ChannelOrderFlagModifyMessage receive)
    {
        if(string.IsNullOrEmpty(receive.ChannelOrderNo)) return;
        var channelOrderNos = receive.ChannelOrderNo.Split(',',StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries).ToList();
        if(channelOrderNos.Any() is false) return;
        channelOrderNos = channelOrderNos.Distinct().ToList();

        var otaChannelType = await ChannelTypeProcess(
            sellingPlatform: receive.SellingPlatform,
            agencyId: receive.AgencyId,
            tenantId: receive.TenantId);

        //暂时只支持[飞猪]
        if (otaChannelType != OtaChannelType.AliTrip)
            return;
        
        foreach (var channelOrderNo in channelOrderNos)
        {
            var flagModifyRequest = new ChannelOrderModifyRequest
            {
                OtaType = otaChannelType.ToString().ToLowerInvariant(),
                OtaOrderId = channelOrderNo,
                Flag = (int)receive.FlagType
            };
            _ = _openChannelService.OrderModify(flagModifyRequest,receive.TenantId);
        }
    }

    public async Task Confirm(ConfirmChannelOrderInput input,long tenantId)
    {
        var otaChannelType = _openPlatformBaseService.MapSellingPlatformToOtaChannelType(input.SellingPlatform)
            .ToString().ToLowerInvariant();
        var confirmRequest = new ChannelOrderConfirmRequest
        {
            OtaType = otaChannelType,
            OtaOrderId = input.ChannelOrderNo
        };
        var confirmResponse =  await _openChannelService.OrderConfirm(confirmRequest,tenantId);
    }

    #region private

    record OpenChannelConfirmInputData(List<string> ChannelOrderNoList,OtaChannelType OtaChannelType,long TenantId);
    private async Task<ApiBaseResponse<ChannelOrderConfirmResponse>> OpenChannelConfirm(OpenChannelConfirmInputData data)
    {
        var result = new ApiBaseResponse<ChannelOrderConfirmResponse>();
        var request = new ChannelOrderConfirmRequest
        {
            OtaType = data.OtaChannelType.ToString().ToLowerInvariant()
        };

        var responseList = new List<ApiBaseResponse<ChannelOrderConfirmResponse>>();
        foreach (var otaOrderId in data.ChannelOrderNoList)
        {
            request.OtaOrderId = otaOrderId;
            var response = await _openChannelService.OrderConfirm(request,data.TenantId);
            responseList.Add(response);
        }
        
        //结果处理.只有全部成功才算成功.
        var isSuccess = responseList.All(x=> x.Code == _successCode);
        if (isSuccess)
        {
            result.Code = responseList.First().Code;
            result.Msg = responseList.First().Msg;
        }
        else
        {
            //失败处理
            var failedResponse = responseList.First(x => x.Code != _successCode);
            result.Code = failedResponse.Code;
            result.Msg = failedResponse.Msg;
        }
        return result;
    }
    
    private async Task UpdateLineConfirmStatus(long baseOrderId,
        long travelLineOrderId,
        int confirmResponseCode, 
        string confirmResponseMsg)
    {
        bool isSuccess = confirmResponseCode is _successCode or (int)TravelLineOtaOrderErrorCode.FliggyAlreadyConfirm;

        var otaOrder = await _dbContext.TravelLineOtaOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == baseOrderId);
        if (otaOrder is not null)
        {
            otaOrder.Status = isSuccess
                ? TravelLineOtaOrderStatus.WaitingForDeliver //已确认直接变成待发货
                : TravelLineOtaOrderStatus.WaitingForConfirm;
            otaOrder.UpdateTime = DateTime.Now;

            var otaOrderRecord = new TravelLineOtaOrderRecord
            {
                BaseOrderId = baseOrderId,
                SubOrderId = travelLineOrderId,
                TravelLineOtaOrderId = otaOrder.Id,
                RecordType = TravelLineOtaOrderRecordType.SyncConfirm,
                IsSuccess = isSuccess,
                ErrorMsg = confirmResponseMsg,
                ErrorCode = isSuccess ? null : (int)OrderBusinessErrorCodeType.LineSyncConfirmFailed
            };
            otaOrderRecord.SetTenantId(otaOrder.TenantId);
            await _dbContext.AddAsync(otaOrderRecord);
        }
    }

    /// <summary>
    /// 售卖平台换算渠道类型
    /// </summary>
    /// <param name="sellingPlatform"></param>
    /// <param name="agencyId"></param>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    private async Task<OtaChannelType> ChannelTypeProcess(SellingPlatform sellingPlatform,long? agencyId,long tenantId)
    {
        var systemOtaChannelType = new OtaChannelType();
        if (sellingPlatform == SellingPlatform.System && agencyId is not null)
        {
            //判断手工单是否API对接分销商
            var otaSellingPlatform = new[] { SellingPlatform.Ctrip, SellingPlatform.Meituan, SellingPlatform.Fliggy, SellingPlatform.TikTok };
            var systemOrderCheckAgency = await _openPlatformBaseService.CheckSystemPlatformOrderByAgencyId(
                agencyId: agencyId!.Value,
                tenantId: tenantId,
                otaPlatform: otaSellingPlatform);
            systemOtaChannelType = systemOrderCheckAgency.otaChannelType;
        }
        
        OtaChannelType otaChannelType = sellingPlatform switch
        {
            SellingPlatform.Ctrip => OtaChannelType.Ctrip,
            SellingPlatform.Meituan => OtaChannelType.Meituan,
            SellingPlatform.Fliggy => OtaChannelType.AliTrip,
            SellingPlatform.TikTok => OtaChannelType.DouYin,
            _ => systemOtaChannelType
        };

        return otaChannelType;
    }

    private async Task<List<TimelinessChannelSettingInfo>> GetProductTimelinessSetting(OrderType orderType, long productId,long tenantId)
    {
        var timelinessChannelSettingInfos = new List<TimelinessChannelSettingInfo>();
        var headers = new List<KeyValuePair<string, string>> { new("tenant", tenantId.ToString()) };
        switch (orderType)
        {
            case OrderType.ScenicTicket:

                var scenicTicketChannelSettings =
                    await _httpClientFactory.InternalGetAsync<GetTicketChannelSettingOutput>(
                        requestUri: _servicesAddress.Scenic_GetScenicTicketChannelSetting(productId),
                        headers: headers);
                timelinessChannelSettingInfos = scenicTicketChannelSettings.TimelinessChannelSettingInfos;
                
                break;
            case OrderType.TravelLineOrder:
                
                var lineProductChannelTimelinessSetting = await _httpClientFactory.InternalGetAsync<GetOpenChannelTimelinessSettingOutput>(
                    requestUri: _servicesAddress.Product_GetChannelTimelinessSetting(productId),
                    headers: headers);
                timelinessChannelSettingInfos = lineProductChannelTimelinessSetting.TimelinessChannelSettingInfos;
                
                break;
        }
        
        return timelinessChannelSettingInfos;
    }
    
    #endregion
}