using AutoMapper;
using Cit.Storage.Redis;
using Common.GlobalException;
using Common.Jwt;
using Common.ServicesHttpClient;
using Contracts.Common.Order.DTOs.SettlementOrder;
using Contracts.Common.Order.DTOs.SettlementPayables;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Payment.Messages;
using Contracts.Common.Product.Enums;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using IdGen;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NPOI.SS.UserModel;
using Order.Api.Extensions;
using Order.Api.Model;
using Order.Api.Services.Interfaces;
using System.Linq;
using System.Threading.Tasks;
using UserType = Contracts.Common.Order.Enums.UserType;

namespace Order.Api.Services
{
    public class SettlementPayablesService : ISettlementPayablesService
    {
        private readonly CustomDbContext _dbContext;
        private readonly IRedisClient _redisClient;
        private readonly IMapper _mapper;
        private readonly ServicesAddress _servicesAddress;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;
        private readonly ICapPublisher _capPublisher;

        private const string _offsetOrderSettlementCreateLock = "offsetorder:settlement:cratelock";
        private const string _systemOperatorName = "system";

        public SettlementPayablesService(CustomDbContext dbContext,
            IRedisClient redisClient,
            IMapper mapper, IOptions<ServicesAddress> options,
            IHttpClientFactory httpClientFactory,
            IConfiguration configuration,
            ICapPublisher capPublisher)
        {
            _dbContext = dbContext;
            _redisClient = redisClient;
            _mapper = mapper;
            _servicesAddress = options.Value;
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
            _capPublisher = capPublisher;
        }

        #region 酒店订单

        public async Task<PagingModel<PayablesHotelOrderInfo>> HotelOrder(PayablesSearchInput input)
        {
            var hotelQuery = HotelQuery(input);
            var result = await hotelQuery
                .OrderByDescending(s => s.OrderId)
                .PagingAsync(input.PageIndex, input.PageSize);

            result.Data = await HotelOrderStuff(result.Data.ToList());

            return result;
        }

        private async Task<List<PayablesHotelOrderInfo>> HotelOrderStuff(List<PayablesHotelOrderInfo> data)
        {
            //获取价格信息
            var prices = await GetHotelOrderCostPrices(data);

            foreach (var order in data)
            {
                var price = prices.First(x => x.orderId == order.OrderId);
                order.TotalAmount = price.totalCostPrice;
                order.CostCurrencyCode = price.costCurrencyCode;
            }

            return data;
        }

        private IQueryable<PayablesHotelOrderInfo> HotelQuery(PayablesSearchInput input)
        {
            var query = from hotelOrder in _dbContext.HotelOrders
                        join baseOrder in _dbContext.BaseOrders on hotelOrder.BaseOrderId equals baseOrder.Id
                        join settled in _dbContext.SettlementOrderDetails on hotelOrder.Id equals settled.BusinessOrderId into
                            temp
                        from settled in temp.DefaultIfEmpty()
                        where settled == null
                        select new { hotelOrder, baseOrder };
            //供应商
            query = query
                .Where(x => x.hotelOrder.IsGroupBooking == false)
                .WhereIF(input.SupplierIds.Any() == false, s => s.hotelOrder.PriceStrategySupplierId != 0)
                .WhereIF(input.SupplierIds.Any(), s => input.SupplierIds.Contains(s.hotelOrder.PriceStrategySupplierId))
                .WhereIF(input.PayablesOrderIds.Any(), x => input.PayablesOrderIds.Contains(x.hotelOrder.Id))
                .WhereIF(input.PayablesBaseOrderIds.Any(), x => input.PayablesBaseOrderIds.Contains(x.baseOrder.Id))
                .WhereIF(input.SupplierOrderIds.Any(), x => input.SupplierOrderIds.Contains(x.hotelOrder.SupplierOrderId));

            if (input is { BillingCycleBegin: not null, BillingCycleEnd: not null } &&
                input.PayablesOrderIds.Any() is false)
            {
                //日期
                switch (input.SettlementDateType)
                {
                    case PayablesDateType.Create:
                        query = query.Where(s => s.hotelOrder.Status != HotelOrderStatus.WaitingForPay
                                                 && s.hotelOrder.Status != HotelOrderStatus.Closed
                                                 && s.baseOrder.CreateTime >= input.BillingCycleBegin
                                                 && s.baseOrder.CreateTime < input.BillingCycleEnd);
                        break;
                    case PayablesDateType.CheckIn:
                        query = query.Where(s => s.hotelOrder.Status != HotelOrderStatus.WaitingForPay
                                                 && s.hotelOrder.Status != HotelOrderStatus.Closed
                                                 && s.hotelOrder.CheckInDate >= input.BillingCycleBegin
                                                 && s.hotelOrder.CheckInDate < input.BillingCycleEnd);
                        break;
                    case PayablesDateType.CheckOut:
                        query = query.Where(s => s.hotelOrder.Status != HotelOrderStatus.WaitingForPay
                                                 && s.hotelOrder.Status != HotelOrderStatus.Closed
                                                 && s.hotelOrder.CheckOutDate >= input.BillingCycleBegin
                                                 && s.hotelOrder.CheckOutDate < input.BillingCycleEnd);
                        break;
                    case PayablesDateType.Finish:
                        query = query.Where(s =>
                            (s.hotelOrder.Status == HotelOrderStatus.Finished ||
                             s.hotelOrder.Status == HotelOrderStatus.Refunded)
                            && s.baseOrder.UpdateTime >= input.BillingCycleBegin
                            && s.baseOrder.UpdateTime < input.BillingCycleEnd);
                        break;
                    default:
                        throw new BusinessException($"{nameof(input.SettlementDateType)} is error");
                        break;
                }
            }

            return query.Select(s => new PayablesHotelOrderInfo
            {
                CreateOrderDate = s.baseOrder.CreateTime,
                UpdateTime = s.baseOrder.UpdateTime,
                BuyerName = s.baseOrder.UserNickName,
                ChannelOrderNo = s.baseOrder.ChannelOrderNo,
                BaseOrderId = s.hotelOrder.BaseOrderId,
                OrderId = s.hotelOrder.Id,
                SupplierId = s.hotelOrder.PriceStrategySupplierId,
                CheckInDate = s.hotelOrder.CheckInDate,
                CheckOutDate = s.hotelOrder.CheckOutDate,
                ConfirmCode = s.hotelOrder.ConfirmCode,
                NightsCount = s.hotelOrder.PriceStrategyNightsCount,
                Quantity = s.hotelOrder.PriceStrategyRoomsCount,
                Status = s.hotelOrder.Status,
                HotelName = s.hotelOrder.HotelName,
                ProductName = s.hotelOrder.HotelRoomName,
                ProductSkuName = s.hotelOrder.PriceStrategyName,
                SupplierOrderId = s.hotelOrder.SupplierOrderId
            });
        }

        /// <summary>
        /// 批量获取酒店订单采购价格信息
        /// </summary>
        /// <param name="orders">OrderId、CheckInDate、CheckOutDate、Quantity 必传</param>
        /// <returns></returns>
        private async Task<IEnumerable<(long orderId, string costCurrencyCode, decimal totalCostPrice)>>
            GetHotelOrderCostPrices(IEnumerable<PayablesHotelOrderInfo> orders)
        {
            var result = new List<(long orderId, string costCurrencyCode, decimal totalCostPrice)>();
            var hotelOrderIds = orders.Select(s => s.OrderId).Distinct().ToList();
            if (hotelOrderIds.Any() == false)
                return result;
            var calendarPrices = await _dbContext.HotelOrderCalendarPrices.AsNoTracking()
                .Where(s => hotelOrderIds.Contains(s.HotelOrderId))
                .ToListAsync();
            result.AddRange(from order in orders
                                //晚数采购价之和
                            let totalCost = calendarPrices
                                .Where(s => s.HotelOrderId == order.OrderId && s.Date >= order.CheckInDate &&
                                            s.Date < order.CheckOutDate)
                                .Sum(s => s.CostPrice)
                            //间数 * 晚数采购价之和
                            let totalCostPrice = order.Quantity * totalCost
                            //采购币种
                            let costCurrencyCode = calendarPrices.First(x => x.HotelOrderId == order.OrderId)
                                .CostCurrencyCode
                            select new ValueTuple<long, string, decimal>(order.OrderId, costCurrencyCode, totalCostPrice));
            return result;
        }

        #endregion

        #region 邮寄订单

        public async Task<PagingModel<PayablesMailOrderInfo>> MailOrder(PayablesSearchInput input)
        {
            var result = await MailQuery(input)
                .OrderByDescending(s => s.OrderId)
                .PagingAsync(input.PageIndex, input.PageSize);

            result.Data = await MailOrderStuff(result.Data.ToList());

            return result;
        }

        private async Task<List<PayablesMailOrderInfo>> MailOrderStuff(List<PayablesMailOrderInfo> data)
        {
            //物流公司
            var trackingNumbers = data
                .Where(s => string.IsNullOrWhiteSpace(s.TrackingNumber))
                .Select(s => s.TrackingNumber)
                .Distinct()
                .ToList();
            if (trackingNumbers.Any() == false)
                return data;

            var logistics = await _dbContext.MailOrderLogistics
                .Where(s => trackingNumbers.Contains(s.TrackingNumber))
                .Select(s => new { s.TrackingNumber, s.LogisticsCompanyCode, s.LogisticsCompanyName })
                .ToListAsync();
            var prices = GetPriceAndQuantity(data.Select(s => s.OrderId)).ToList();
            foreach (var order in data)
            {
                var p = prices.First(s => s.subOrderId == order.OrderId);
                order.Quantity = p.quantity;
                order.CostPrice = p.cost;
                order.TotalAmount = p.total;
                order.LogisticsCompanyName = logistics
                    .FirstOrDefault(s => s.TrackingNumber == order.TrackingNumber)?.LogisticsCompanyCode;
                order.CostCurrencyCode = p.costCurrencyCode;
            }

            return data;
        }

        private IQueryable<PayablesMailOrderInfo> MailQuery(PayablesSearchInput input)
        {
            var query = from mailOrder in _dbContext.MailOrders
                        join baseOrder in _dbContext.BaseOrders on mailOrder.BaseOrderId equals baseOrder.Id
                        join settled in _dbContext.SettlementOrderDetails on mailOrder.Id equals settled.BusinessOrderId into
                            temp
                        from settled in temp.DefaultIfEmpty()
                        where settled == null
                        select new { mailOrder, baseOrder };
            query = query
                .WhereIF(input.SupplierIds.Any() == false, s => s.mailOrder.ProductSupplierId != 0)
                .WhereIF(input.SupplierIds.Any(), s => input.SupplierIds.Contains(s.mailOrder.ProductSupplierId))
                .WhereIF(input.PayablesOrderIds.Any(), x => input.PayablesOrderIds.Contains(x.mailOrder.Id))
                .WhereIF(input.PayablesBaseOrderIds.Any(), x => input.PayablesBaseOrderIds.Contains(x.baseOrder.Id));

            if (input is { BillingCycleBegin: not null, BillingCycleEnd: not null } &&
                input.PayablesOrderIds.Any() is false)
            {
                switch (input.SettlementDateType)
                {
                    case PayablesDateType.Create:
                        query = query.Where(s => s.mailOrder.Status != MailOrderStatus.WaitingForPay
                                                 && s.mailOrder.Status != MailOrderStatus.Closed
                                                 && s.baseOrder.CreateTime >= input.BillingCycleBegin
                                                 && s.baseOrder.CreateTime < input.BillingCycleEnd);
                        break;
                    case PayablesDateType.Finish:
                        query = query.Where(s =>
                            (s.mailOrder.Status == MailOrderStatus.Finished ||
                             s.mailOrder.Status == MailOrderStatus.Refunded)
                            && s.mailOrder.UpdateTime >= input.BillingCycleBegin
                            && s.mailOrder.UpdateTime < input.BillingCycleEnd);
                        break;
                    default:
                        throw new BusinessException($"{nameof(input.SettlementDateType)} is error");
                        break;
                }
            }

            return query.Select(s => new PayablesMailOrderInfo
            {
                CreateOrderDate = s.baseOrder.CreateTime,
                BuyerName = s.baseOrder.UserNickName,
                BaseOrderId = s.mailOrder.BaseOrderId,
                OrderId = s.mailOrder.Id,
                SupplierId = s.mailOrder.ProductSupplierId,
                ProductName = s.mailOrder.ProductName,
                ProductSkuName = s.mailOrder.ProductSkuName,
                UpdateTime = s.mailOrder.UpdateTime,
                Status = s.mailOrder.Status,
                TrackingNumber = s.mailOrder.TrackingNumber,
            });
        }

        private IEnumerable<GetPriceAndQuantityOutput>
            GetPriceAndQuantity(IEnumerable<long> subOrderIds)
        {
            var result = new List<GetPriceAndQuantityOutput>();
            if (subOrderIds.Any() == false)
                return result.AsEnumerable();
            var prices = _dbContext.OrderPrices.AsNoTracking()
                .Where(s => subOrderIds.Contains(s.SubOrderId))
                .Select(s => new
                {
                    s.SubOrderId,
                    s.BaseOrderId,
                    s.Quantity,
                    s.CostPrice,
                    s.OrderType,
                    s.OrderSubType,
                    s.CostCurrencyCode,
                    s.CostDiscountRate,
                })
                .ToList();
            //新增用车采购价的查询，根据baseOrderId查询
            var carProductBaseOrderIds = prices
                .Where(x => x.OrderType == OrderType.CarProduct)
                .Select(x => x.BaseOrderId)
                .Distinct();
            var carPrices = _dbContext.OrderPrices.AsNoTracking()
                .Where(x => carProductBaseOrderIds.Contains(x.BaseOrderId))
                .ToList()
                .GroupBy(x => x.BaseOrderId);
            var baseOrderIds = prices.Select(s => s.BaseOrderId).Distinct().ToList();
            var costDiscountAmounts = _dbContext.BaseOrders.AsNoTracking()
                .Where(x => baseOrderIds.Contains(x.Id) && x.CostDiscountAmount > 0)
                .Select(x=> new {
                    BaseOrderId = x.Id,
                    CostDiscountAmount = x.CostDiscountAmount
                })
                .ToList();

            foreach (var pr in prices.GroupBy(s => s.SubOrderId))
            {
                var costDiscountAmount = costDiscountAmounts
                    .FirstOrDefault(x => x.BaseOrderId == pr.First().BaseOrderId)?.CostDiscountAmount;
                if (pr.Any(s => s.OrderType == OrderType.TravelLineOrder))
                {
                    /**
                     * 线路特殊处理
                     * 1,数量不包含房差
                     * 2,采购价格总和(包房房差) = 采购价 * 数量
                     * **/
                    result.Add(new GetPriceAndQuantityOutput(
                        pr.Key,
                        pr.Where(s => s.OrderSubType != (int)LineSkuPriceType.RoomPriceDifference).Sum(s => s.Quantity),
                        pr.Sum(s => s.Quantity * s.CostPrice),
                        pr.First().CostCurrencyCode,
                        costDiscountAmount,
                        pr.Sum(s => s.Quantity * s.CostPrice * s.CostDiscountRate) / 100));
                }
                else if (pr.Any(s => s.OrderType == OrderType.ScenicTicket))
                {
                    //门票,用车特殊处理.因为有多批次,或者供应端出现差价.一个订单会有多个采购价
                    result.Add(new GetPriceAndQuantityOutput(
                        pr.Key,
                        pr.Sum(s => s.Quantity),
                        pr.Sum(s => s.Quantity * s.CostPrice),
                        pr.First().CostCurrencyCode,
                        costDiscountAmount,
                        pr.Sum(s => s.Quantity * s.CostPrice * s.CostDiscountRate) / 100));
                }
                else if (pr.Any(s => s.OrderType == OrderType.CarProduct))
                {
                    var matchCarPrice = carPrices.FirstOrDefault(x => x.Key == pr.First().BaseOrderId);
                    result.Add(new GetPriceAndQuantityOutput(
                        pr.Key,
                        matchCarPrice.Sum(s => s.Quantity),
                        pr.Sum(s => s.Quantity * s.CostPrice),
                        pr.First().CostCurrencyCode,
                        costDiscountAmount,
                        pr.Sum(s => s.Quantity * s.CostPrice * s.CostDiscountRate) / 100));
                }
                else
                {
                    result.Add(new GetPriceAndQuantityOutput(
                        pr.Key,
                        pr.First().Quantity,
                        pr.First().CostPrice,
                        pr.First().CostCurrencyCode,
                        costDiscountAmount, 
                        pr.First().Quantity * pr.First().CostPrice * pr.First().CostDiscountRate / 100));
                }
            }

            return result.AsEnumerable();
        }

        private class GetPriceAndQuantityOutput
        {

            public long subOrderId { get; set; }
            public int quantity { get; set; }
            public decimal cost { get; set; }
            public decimal total { get; set; }
            public string costCurrencyCode { get; set; }
            public decimal? costDiscountAmountsByBaseOrder { get; set; }
            public decimal costDiscountAmounts { get; set; }

            public GetPriceAndQuantityOutput(long subOrderId,
                int quantity, 
                decimal cost,
                string costCurrencyCode, 
                decimal? costDiscountAmountsByBaseOrder, 
                decimal costDiscountAmounts)
            {
                this.subOrderId = subOrderId;
                this.quantity = quantity;
                this.cost = cost;
                this.costCurrencyCode = costCurrencyCode;
                this.costDiscountAmountsByBaseOrder = costDiscountAmountsByBaseOrder;
                this.costDiscountAmounts = costDiscountAmounts;
                if(costDiscountAmountsByBaseOrder.HasValue)
                    this.total = Math.Round(cost - costDiscountAmountsByBaseOrder.Value,2);
                else
                    this.total = Math.Round(cost - costDiscountAmounts, 2);
            }
        }

        #endregion

        #region 券类-非预约

        public async Task<PagingModel<PayablesTicketOrderInfo>> TicketOrder(PayablesSearchInput input)
        {
            var result = await TicketQuery(input)
                .OrderByDescending(s => s.OrderId)
                .PagingAsync(input.PageIndex, input.PageSize);

            result.Data = await TicketOrderStuff(result.Data.ToList());

            return result;
        }

        private async Task<List<PayablesTicketOrderInfo>> TicketOrderStuff(List<PayablesTicketOrderInfo> data)
        {
            var prices = GetPriceAndQuantity(data.Select(s => s.OrderId)).ToList();
            foreach (var order in data)
            {
                var p = prices.First(s => s.subOrderId == order.OrderId);
                order.Quantity = p.quantity;
                order.CostPrice = p.cost;
                order.TotalAmount = p.total;
                order.CostCurrencyCode = p.costCurrencyCode;
            }

            return data;
        }

        private IQueryable<PayablesTicketOrderInfo> TicketQuery(PayablesSearchInput input)
        {
            var query = from ticketOrder in _dbContext.TicketOrders
                        join baseOrder in _dbContext.BaseOrders on ticketOrder.BaseOrderId equals baseOrder.Id
                        join settled in _dbContext.SettlementOrderDetails on ticketOrder.Id equals settled.BusinessOrderId into
                            temp
                        from settled in temp.DefaultIfEmpty()
                        where settled == null
                        select new { ticketOrder, baseOrder };
            query = query //不需预约 && 支付过
                .Where(s => s.ticketOrder.ProductNeedReservation == false && s.baseOrder.PaymentType != PayType.None)
                .WhereIF(input.SupplierIds.Any() == false, s => s.ticketOrder.ProductSupplierId != 0)
                .WhereIF(input.SupplierIds.Any(), s => input.SupplierIds.Contains(s.ticketOrder.ProductSupplierId))
                .WhereIF(input.PayablesOrderIds.Any(), x => input.PayablesOrderIds.Contains(x.ticketOrder.Id))
                .WhereIF(input.PayablesBaseOrderIds.Any(), x => input.PayablesBaseOrderIds.Contains(x.baseOrder.Id))
                .WhereIF(input.SupplierOrderIds.Any(), x => input.SupplierOrderIds.Contains(x.ticketOrder.SupplierOrderId));

            if (input is { BillingCycleBegin: not null, BillingCycleEnd: not null } &&
                input.PayablesOrderIds.Any() is false)
            {
                switch (input.SettlementDateType)
                {
                    case PayablesDateType.Create:
                        query = query.Where(s => s.baseOrder.CreateTime >= input.BillingCycleBegin
                                                 && s.baseOrder.CreateTime < input.BillingCycleEnd);
                        break;
                    case PayablesDateType.Finish:
                        query = query.Where(s =>
                            (s.baseOrder.Status == BaseOrderStatus.Finished ||
                             s.baseOrder.Status == BaseOrderStatus.Closed)
                            && s.baseOrder.UpdateTime >= input.BillingCycleBegin
                            && s.baseOrder.UpdateTime < input.BillingCycleEnd);
                        break;
                    default:
                        throw new BusinessException($"{nameof(input.SettlementDateType)} is error");
                        break;
                }
            }

            return query.Select(s => new PayablesTicketOrderInfo
            {
                CreateOrderDate = s.baseOrder.CreateTime,
                ProductName = s.baseOrder.ProductName,
                ProductSkuName = s.baseOrder.ProductSkuName,
                UpdateTime = s.baseOrder.UpdateTime,
                Status = s.baseOrder.Status,
                BuyerName = s.baseOrder.UserNickName,
                BaseOrderId = s.ticketOrder.BaseOrderId,
                OrderId = s.ticketOrder.Id,
                SupplierId = s.ticketOrder.ProductSupplierId,
                TicketBusinessType = s.ticketOrder.ProductTicketBusinessType,
                SupplierOrderId = s.ticketOrder.SupplierOrderId
            });
        }

        #endregion

        #region 券类-预约单

        //券类订单A 4张券 需预约  以下记录了5次预约场景
        //预约单1  2张券 无加价      用1张退1张 预约单状态：已完成 预约结算单(1张采购价)
        //预约单2  2张券 采购加价10  退2张 预约单状态：已退款
        //预约单3  2张券 采购加价10  用1张退1张 预约单状态：已完成 预约结算单(1张采购价+1张采购加价)
        //预约单4  1张券 采购加价10  退1张 预约单状态：已退款
        //预约单5  1张券 无加价      退1张 预约单状态：已取消
        //预约单5  1张券 无加价      用1张 预约单状态：已完成 预约结算单(1张采购价)
        //剩下一张 no show

        public async Task<PagingModel<PayablesReservationOrderInfo>> ReservationOrder(PayablesSearchInput input)
        {
            var result = await ReservationQuery(input)
                .OrderByDescending(s => s.OrderId)
                .PagingAsync(input.PageIndex, input.PageSize);

            result.Data = await ReservationOrderStuff(result.Data.ToList());

            return result;
        }

        private async Task<List<PayablesReservationOrderInfo>> ReservationOrderStuff(List<PayablesReservationOrderInfo> data)
        {
            List<(long ticketOrderId, long reservationOrderId)> rorder = data
                .Select(s => (s.TicketOrderId, s.OrderId))
                .ToList();
            var prices = GetReservationOrderPrice(rorder);
            foreach (var order in data)
            {
                var price = prices.First(s => s.reservationOrderId == order.OrderId);
                order.Quantity = price.quantity;
                order.CostPrice = price.cost;
                order.TotalAmount = price.total;
                order.CostCurrencyCode = price.costCurrencyCode;
            }

            return data;
        }

        private IQueryable<PayablesReservationOrderInfo> ReservationQuery(PayablesSearchInput input)
        {
            var query = from reservationOrder in _dbContext.ReservationOrders
                        join ticketOrder in _dbContext.TicketOrders on reservationOrder.TicketOrderId equals ticketOrder.Id
                        join baseOrder in _dbContext.BaseOrders on reservationOrder.BaseOrderId equals baseOrder.Id
                        join settled in _dbContext.SettlementOrderDetails on reservationOrder.Id equals settled.BusinessOrderId
                            into temp
                        from settled in temp.DefaultIfEmpty()
                        where settled == null
                        select new { reservationOrder, ticketOrder, baseOrder };
            query = query //只结算已完结预约单
                .Where(s => s.ticketOrder.ProductNeedReservation == true)
                .Where(s => s.reservationOrder.Status == ReservationStatus.Finished)
                .WhereIF(input.SupplierIds.Any() == false, s => s.ticketOrder.ProductSupplierId != 0)
                .WhereIF(input.SupplierIds.Any(), s => input.SupplierIds.Contains(s.ticketOrder.ProductSupplierId))
                .WhereIF(input.PayablesOrderIds.Any(), x => input.PayablesOrderIds.Contains(x.reservationOrder.Id))
                .WhereIF(input.PayablesBaseOrderIds.Any(), x => input.PayablesBaseOrderIds.Contains(x.baseOrder.Id));

            if (input is { BillingCycleBegin: not null, BillingCycleEnd: not null } &&
                input.PayablesOrderIds.Any() is false)
            {
                query = input.SettlementDateType switch
                {
                    PayablesDateType.Finish => query
                        .Where(x => x.reservationOrder.UpdateTime >= input.BillingCycleBegin &&
                                    x.reservationOrder.UpdateTime < input.BillingCycleEnd),

                    _ => throw new BusinessException($"{nameof(input.SettlementDateType)} is error")
                };
            }

            return query.Select(s => new PayablesReservationOrderInfo
            {
                ProductName = s.baseOrder.ProductName,
                ProductSkuName = s.baseOrder.ProductSkuName,
                BuyerName = s.baseOrder.UserNickName,
                OrderId = s.reservationOrder.Id,
                UpdateTime = s.reservationOrder.UpdateTime,
                Status = s.reservationOrder.Status,
                CreateOrderDate = s.reservationOrder.CreateTime,
                ConfirmCode = s.reservationOrder.ConfirmNumber,
                TicketOrderId = s.reservationOrder.TicketOrderId,
                BaseOrderId = s.ticketOrder.BaseOrderId,
                SupplierId = s.ticketOrder.ProductSupplierId,
                TicketBusinessType = s.ticketOrder.ProductTicketBusinessType
            });
        }

        private IEnumerable<(long reservationOrderId, int quantity, decimal cost, decimal total, string costCurrencyCode
            )> GetReservationOrderPrice(
            IEnumerable<(long ticketOrderId, long reservationOrderId)> reservationOrders)
        {
            List<(long reservationOrderId, int quantity, decimal cost, decimal total, string costCurrencyCode)> result =
                new();
            if (reservationOrders.Any() == false)
                return result;

            var reservationOrderIds = reservationOrders.Select(s => s.reservationOrderId);
            var ticketOrderIds = reservationOrders.Select(s => s.ticketOrderId);

            var ticketCostPrices = _dbContext.OrderPrices
                .Where(s => ticketOrderIds.Contains(s.SubOrderId))
                .Select(s => new { s.SubOrderId, s.CostPrice, s.CostCurrencyCode })
                .ToList();
            var codes = _dbContext.ReservationOrderTicketCodes.AsNoTracking()
                .Where(s => reservationOrderIds.Contains(s.ReservationOrderId))
                .ToList();
            var rops = _dbContext.ReservationOrderPayments.AsNoTracking()
                .Where(s => reservationOrderIds.Contains(s.ReservationOrderId))
                .ToList();

            foreach (var item in reservationOrders)
            {
                var price = ticketCostPrices.First(s => s.SubOrderId == item.ticketOrderId);
                var ticketCostPrice = price.CostPrice;
                var costMarkup = rops //预约加价采购价
                    .Where(s => s.ReservationOrderId == item.reservationOrderId)
                    .Sum(s => s.Cost);
                var quantity = codes //已完成的分数
                    .Where(s => s.ReservationOrderId == item.reservationOrderId &&
                                s.Status == ReservationTicketCodeStatus.Finished)
                    .Count();
                var cost = ticketCostPrice + costMarkup;
                var total = cost * quantity;
                var costCurrencyCode = price.CostCurrencyCode;
                result.Add(new(item.reservationOrderId, quantity, cost, total, costCurrencyCode));
            }

            return result;
        }

        #endregion

        #region 退款单

        public async Task<PagingModel<PayablesRefundOrderInfo>> RefundOrder(PayablesSearchInput input)
        {
            var result = await RefundQuery(input)
                .OrderByDescending(s => s.OrderId)
                .PagingAsync(input.PageIndex, input.PageSize);
            return result;
        }

        private IQueryable<PayablesRefundOrderInfo> RefundQuery(PayablesSearchInput input)
        {
            //原单已结算，并且退款单未结算，不管创建时间
            var businessType = new[]
            {
                SettlementBusinessType.TicketOrder, SettlementBusinessType.MailOrder,
                SettlementBusinessType.HotelOrder, SettlementBusinessType.LineOrder,
                SettlementBusinessType.ScenicTicketOrder,SettlementBusinessType.CarProductOrder,

            };
            var businessSettlementOrderDetails = _dbContext.SettlementOrderDetails
                .Where(x => businessType.Contains(x.SettlementBusinessType));
            var refundSettlementOrderDetails = _dbContext.SettlementOrderDetails
                .Where(x => x.SettlementBusinessType == SettlementBusinessType.RefundOrder);

            var query = from b in businessSettlementOrderDetails
                        join baseOrder in _dbContext.BaseOrders on b.BaseOrderId equals baseOrder.Id
                        join refundOrder in _dbContext.RefundOrders on b.BaseOrderId equals refundOrder.BaseOrderId
                        join settle in refundSettlementOrderDetails on refundOrder.Id equals settle.BusinessOrderId
                            into temp
                        from settle in temp.DefaultIfEmpty()
                        where settle == null
                        select new { baseOrder, refundOrder };

            var orderTypes = new RefundOrderType[]
            {
                RefundOrderType.Hotel,
                RefundOrderType.Mail,
                RefundOrderType.Ticket,
                RefundOrderType.ScenicTicket,
                RefundOrderType.TravelLine,
                RefundOrderType.CarProduct,
            };
            query = query
                .Where(s => s.refundOrder.Status == RefundOrderStatus.Refunded
                            && orderTypes.Contains(s.refundOrder.OrderType))
                .WhereIF(input.SupplierIds.Any() == false, s => s.refundOrder.SupplierId != 0)
                .WhereIF(input.SupplierIds.Any(), s => input.SupplierIds.Contains(s.refundOrder.SupplierId))
                .WhereIF(input.PayablesOrderIds.Any(), x => input.PayablesOrderIds.Contains(x.refundOrder.Id))
                .WhereIF(input.PayablesBaseOrderIds.Any(), x => input.PayablesBaseOrderIds.Contains(x.refundOrder.Id)
                                                                || input.PayablesBaseOrderIds.Contains(x.baseOrder.Id));

            return query.Select(s => new PayablesRefundOrderInfo
            {
                BaseOrderId = s.baseOrder.Id,
                ProductName = s.baseOrder.ProductName,
                ProductSkuName = s.baseOrder.ProductSkuName,
                BuyerName = s.baseOrder.UserNickName,
                OrderId = s.refundOrder.Id,
                SupplierId = s.refundOrder.SupplierId,
                CreateOrderDate = s.refundOrder.CreateTime,
                UpdateTime = s.refundOrder.UpdateTime,
                OrderType = s.refundOrder.OrderType,
                Quantity = s.refundOrder.Quantity,
                TotalAmount = -s.refundOrder.Cost,
                CostCurrencyCode = s.refundOrder.CostCurrencyCode
            });
        }

        #endregion

        #region 关联退款单

        public async Task<PagingModel<PayablesRefundOrderInfo>> RelatedRefundOrder(RelatedPayablesSearchInput input)
        {
            //获取账期内所有业务订单的BaseOrderIds
            var baseOrderIds = await GetBaseOrderIds(input);
            if (baseOrderIds.Any() is false)
                return new PagingModel<PayablesRefundOrderInfo>();

            var result = await RelatedRefundOrderQuery(input, baseOrderIds)
                .OrderByDescending(s => s.OrderId)
                .PagingAsync(input.PageIndex, input.PageSize);
            return result;
        }


        /// <summary>
        /// 关联退款单查询
        /// </summary>
        private IQueryable<PayablesRefundOrderInfo> RelatedRefundOrderQuery(RelatedPayablesSearchInput input,
            List<long> baseOrderIds)
        {
            //原单在账期内的搜索结果里，退款单未结算，全部可搜索出来，不用做筛选；
            var receiptBaseOrderIds = baseOrderIds;
            var query = from refundOrder in _dbContext.RefundOrders
                        join baseOrder in _dbContext.BaseOrders on refundOrder.BaseOrderId equals baseOrder.Id
                        join ticketOrder in _dbContext.TicketOrders on refundOrder.SubOrdeId equals ticketOrder.Id into tickOrderTemp
                        from ticketOrder in tickOrderTemp.DefaultIfEmpty()
                        join settled in _dbContext.SettlementOrderDetails on refundOrder.Id equals settled.BusinessOrderId into temp
                        from settled in temp.DefaultIfEmpty()
                        where settled == null
                        select new { refundOrder, baseOrder, ticketOrder.ProductNeedReservation };

            var orderTypes = new RefundOrderType[]
            {
                RefundOrderType.Hotel,
                RefundOrderType.Mail,
                RefundOrderType.Ticket,
                RefundOrderType.ScenicTicket,
                RefundOrderType.TravelLine,
                RefundOrderType.CarProduct,
            };
            query = query
                .Where(s => s.refundOrder.Status == RefundOrderStatus.Refunded
                            && orderTypes.Contains(s.refundOrder.OrderType))
                .Where(s => s.ProductNeedReservation != true)
                .WhereIF(input.SupplierIds.Any() == false, s => s.refundOrder.SupplierId != 0)
                .WhereIF(input.SupplierIds.Any(), s => input.SupplierIds.Contains(s.refundOrder.SupplierId))
                .WhereIF(input.PayablesOrderIds.Any(), s => input.PayablesOrderIds.Contains(s.refundOrder.Id))
                .WhereIF(receiptBaseOrderIds.Any(), x => receiptBaseOrderIds.Contains(x.baseOrder.Id));

            return query.Select(s => new PayablesRefundOrderInfo
            {
                BaseOrderId = s.baseOrder.Id,
                ProductName = s.baseOrder.ProductName,
                ProductSkuName = s.baseOrder.ProductSkuName,
                BuyerName = s.baseOrder.UserNickName,
                OrderId = s.refundOrder.Id,
                SupplierId = s.refundOrder.SupplierId,
                CreateOrderDate = s.refundOrder.CreateTime,
                UpdateTime = s.refundOrder.UpdateTime,
                OrderType = s.refundOrder.OrderType,
                Quantity = s.refundOrder.Quantity,
                TotalAmount = -s.refundOrder.Cost,
                CostCurrencyCode = s.refundOrder.CostCurrencyCode
            });
        }

        #endregion

        #region 景区门票

        public async Task<PagingModel<PayablesScenicTicketOrderInfo>> ScenicTicketOrder(PayablesSearchInput input)
        {
            var result = await ScenicTicketQuery(input)
                .OrderByDescending(s => s.OrderId)
                .PagingAsync(input.PageIndex, input.PageSize);

            result.Data = await ScenicTicketOrderStuff(result.Data.ToList());

            return result;
        }

        private async Task<List<PayablesScenicTicketOrderInfo>> ScenicTicketOrderStuff(
            List<PayablesScenicTicketOrderInfo> data)
        {
            var prices = GetPriceAndQuantity(data.Select(s => s.OrderId)).ToList();
            foreach (var order in data)
            {
                var p = prices.First(s => s.subOrderId == order.OrderId);
                order.Quantity = p.quantity;
                order.CostPrice = p.cost;
                order.TotalAmount = p.total;
                order.CostCurrencyCode = p.costCurrencyCode;
            }

            return data;
        }

        private IQueryable<PayablesScenicTicketOrderInfo> ScenicTicketQuery(PayablesSearchInput input)
        {
            var query = from scenicTicketOrder in _dbContext.ScenicTicketOrders
                        join baseOrder in _dbContext.BaseOrders on scenicTicketOrder.BaseOrderId equals baseOrder.Id
                        join settled in _dbContext.SettlementOrderDetails on scenicTicketOrder.Id equals settled.BusinessOrderId into temp
                        from settled in temp.DefaultIfEmpty()
                        where settled == null
                        select new { scenicTicketOrder, baseOrder };
            query = query
                .Where(s => s.baseOrder.PaymentType != PayType.None) //ScenicTicketOrder 子订单表没有状态判断是否已支付
                .WhereIF(input.SupplierIds.Any() == false, s => s.scenicTicketOrder.SupplierId != 0)
                .WhereIF(input.SupplierIds.Any(), s => input.SupplierIds.Contains(s.scenicTicketOrder.SupplierId))
                .WhereIF(input.PayablesOrderIds.Any(), x => input.PayablesOrderIds.Contains(x.scenicTicketOrder.Id))
                .WhereIF(input.PayablesBaseOrderIds.Any(), x => input.PayablesBaseOrderIds.Contains(x.baseOrder.Id))
                .WhereIF(input.SupplierOrderIds.Any(), x => input.SupplierOrderIds.Contains(x.scenicTicketOrder.SupplierOrderId));

            if (input is { BillingCycleBegin: not null, BillingCycleEnd: not null } &&
                input.PayablesOrderIds.Any() is false)
            {
                switch (input.SettlementDateType)
                {
                    case PayablesDateType.Create:
                        query = query.Where(s => s.baseOrder.CreateTime >= input.BillingCycleBegin
                                                 && s.baseOrder.CreateTime < input.BillingCycleEnd);
                        break;
                    case PayablesDateType.Finish:
                        query = query.Where(s => (s.baseOrder.Status == BaseOrderStatus.Finished || s.baseOrder.Status == BaseOrderStatus.Closed)
                                                 && s.baseOrder.UpdateTime >= input.BillingCycleBegin
                                                 && s.baseOrder.UpdateTime < input.BillingCycleEnd);
                        break;
                    default:
                        throw new BusinessException($"{nameof(input.SettlementDateType)} is error");
                        break;
                }
            }

            return query.Select(s => new PayablesScenicTicketOrderInfo
            {
                CreateOrderDate = s.baseOrder.CreateTime,
                ProductName = s.baseOrder.ProductName,
                ProductSkuName = s.baseOrder.ProductSkuName,
                UpdateTime = s.baseOrder.UpdateTime,
                ContactsName = s.baseOrder.ContactsName,
                ResourceName = s.baseOrder.ResourceName,
                Status = s.baseOrder.Status,
                ChannelOrderNo = s.baseOrder.ChannelOrderNo,

                BaseOrderId = s.scenicTicketOrder.BaseOrderId,
                OrderId = s.scenicTicketOrder.Id,
                SupplierId = s.scenicTicketOrder.SupplierId,
                SupplierOrderId = s.scenicTicketOrder.SupplierOrderId
            });
        }

        #endregion

        #region 线路

        public async Task<PagingModel<PayablesLineOrderInfo>> LineOrder(PayablesSearchInput input)
        {
            var result = await LineOrderQuery(input)
                .OrderByDescending(s => s.OrderId)
                .PagingAsync(input.PageIndex, input.PageSize);

            result.Data = await LineOrderStuff(result.Data.ToList());

            return result;
        }

        private async Task<List<PayablesLineOrderInfo>> LineOrderStuff(List<PayablesLineOrderInfo> data)
        {
            var lineOrderIds = data.Select(s => s.OrderId).ToList();
            var tmp = await LineOrderCostPrice(lineOrderIds);
            foreach (var item in data)
            {
                var quantityAndPrice = tmp.First(x => x.subOrderId == item.OrderId);
                item.Quantity = quantityAndPrice.quantity;
                item.TotalAmount = quantityAndPrice.total;
                item.CostCurrencyCode = quantityAndPrice.costCurrencyCode;
                item.CostPrice = quantityAndPrice.cost;
            }
            return data;
        }

        private IQueryable<PayablesLineOrderInfo> LineOrderQuery(PayablesSearchInput input)
        {
            var query = from lineOrder in _dbContext.TravelLineOrder
                        join baseOrder in _dbContext.BaseOrders on lineOrder.BaseOrderId equals baseOrder.Id
                        join settled in _dbContext.SettlementOrderDetails on lineOrder.Id equals settled.BusinessOrderId into temp
                        from settled in temp.DefaultIfEmpty()
                        where settled == null
                        select new { lineOrder, baseOrder };
            query = query
                .WhereIF(input.SupplierIds.Any() == false, s => s.lineOrder.SupplierId != 0)
                .WhereIF(input.SupplierIds.Any(), s => input.SupplierIds.Contains(s.lineOrder.SupplierId))
                .WhereIF(input.PayablesOrderIds.Any(), x => input.PayablesOrderIds.Contains(x.lineOrder.Id))
                .WhereIF(input.PayablesBaseOrderIds.Any(), x => input.PayablesBaseOrderIds.Contains(x.baseOrder.Id))
                .WhereIF(input.SupplierOrderIds.Any(), x => input.SupplierOrderIds.Contains(x.lineOrder.SupplierOrderId));

            if (input is { BillingCycleBegin: not null, BillingCycleEnd: not null } &&
                input.PayablesOrderIds.Any() is false)
            {
                switch (input.SettlementDateType)
                {
                    case PayablesDateType.Create:
                        query = query.Where(s => s.lineOrder.Status != TravelLineOrderStatus.WaitingForPay
                                                 && s.lineOrder.Status != TravelLineOrderStatus.Canceled
                                                 && s.baseOrder.CreateTime >= input.BillingCycleBegin
                                                 && s.baseOrder.CreateTime < input.BillingCycleEnd);
                        break;
                    case PayablesDateType.Finish:
                        query = query.Where(s => (s.lineOrder.Status == TravelLineOrderStatus.Finished || s.lineOrder.Status == TravelLineOrderStatus.Refunded)
                                                 && s.lineOrder.UpdateTime >= input.BillingCycleBegin
                                                 && s.lineOrder.UpdateTime < input.BillingCycleEnd);
                        break;
                    case PayablesDateType.TravelBeginDate:
                        query = query.Where(s => s.lineOrder.Status != TravelLineOrderStatus.WaitingForPay
                                                 && s.lineOrder.Status != TravelLineOrderStatus.Canceled
                                                 && s.lineOrder.TravelBeginDate >= input.BillingCycleBegin
                                                 && s.lineOrder.TravelBeginDate < input.BillingCycleEnd);
                        break;
                    default:
                        throw new BusinessException($"{nameof(input.SettlementDateType)} is error");
                        break;
                }
            }

            return query.Select(s => new PayablesLineOrderInfo
            {
                CreateOrderDate = s.baseOrder.CreateTime,
                ProductName = s.baseOrder.ProductName,
                ProductSkuName = s.baseOrder.ProductSkuName,
                UpdateTime = s.baseOrder.UpdateTime,
                ContactsName = s.baseOrder.ContactsName,
                Status = s.baseOrder.Status,
                ChannelOrderNo = s.baseOrder.ChannelOrderNo,

                BaseOrderId = s.lineOrder.BaseOrderId,
                OrderId = s.lineOrder.Id,
                SupplierId = s.lineOrder.SupplierId,
                TravelBeginDate = s.lineOrder.TravelBeginDate,
                SupplierOrderId = s.lineOrder.SupplierOrderId
            });
        }

        private async Task<List<GetPriceAndQuantityOutput>> LineOrderCostPrice(List<long> lineOrderIds)
        {
            if (lineOrderIds.Any() == false)
                return new List<GetPriceAndQuantityOutput>();
            var lineOrderPrices = await _dbContext.OrderPrices
                .Where(x => lineOrderIds.Contains(x.SubOrderId) && x.OrderType == OrderType.TravelLineOrder)
                .Select(x => new 
                {
                    TravelLineOrderId = x.SubOrderId,
                    x.Quantity,
                    x.OrderSubType,
                    x.CostPrice,
                    x.CostCurrencyCode,
                    x.CostDiscountRate,
                    x.BaseOrderId,
                })
                .ToListAsync();
            var baseOrderIds = lineOrderPrices.Select(s => s.BaseOrderId).Distinct().ToList();
            var costDiscountAmounts = _dbContext.BaseOrders.AsNoTracking()
                .Where(x => baseOrderIds.Contains(x.Id) && x.CostDiscountAmount > 0)
                .Select(x => new {
                    BaseOrderId = x.Id,
                    CostDiscountAmount = x.CostDiscountAmount
                })
                .ToList();
            //数量不包含房差
            //采购价格总和(包房房差) = 采购价 * 数量
            return lineOrderPrices
                .GroupBy(x => x.TravelLineOrderId)
                .Select(x => new GetPriceAndQuantityOutput(
                     x.Key,
                     x.Where(y => y.OrderSubType != (int)LineSkuPriceType.RoomPriceDifference).Sum(y => y.Quantity),
                     x.Sum(y => y.CostPrice * y.Quantity),
                     x.First().CostCurrencyCode,
                     costDiscountAmounts?.FirstOrDefault(y=> y.BaseOrderId == x.FirstOrDefault().BaseOrderId)?.CostDiscountAmount, 
                     x.Sum(y => y.CostPrice * y.Quantity * y.CostDiscountRate) / 100))
                .ToList();
        }

        #endregion

        #region 用车
        public async Task<PagingModel<PayablesCarProductOrderInfo>> CarProductOrder(PayablesSearchInput input)
        {
            var result = await CarProductOrderQuery(input)
                .OrderByDescending(s => s.OrderId)
                .PagingAsync(input.PageIndex, input.PageSize);

            result.Data = await CarProductOrderStuff(result.Data.ToList());

            return result;
        }
        private async Task<List<PayablesCarProductOrderInfo>> CarProductOrderStuff(List<PayablesCarProductOrderInfo> data)
        {
            var lineOrderIds = data.Select(s => s.OrderId).ToList();
            var tmp = await CarProductCostPrice(lineOrderIds);
            foreach (var item in data)
            {
                var quantityAndPrice = tmp.First(x => x.carProductId == item.OrderId);
                item.Quantity = quantityAndPrice.quantity;
                item.TotalAmount = quantityAndPrice.totalAmount;
                item.CostCurrencyCode = quantityAndPrice.costCurrencyCode;
            }
            return data;
        }

        private async Task<List<(long carProductId, int quantity, decimal cost,decimal totalAmount, string costCurrencyCode)>> CarProductCostPrice(List<long> carProductIds)
        {
            if (carProductIds.Any() == false)
                return new();
            var carProductBaseOrderIds = _dbContext.OrderPrices
                .Where(x => carProductIds.Contains(x.SubOrderId) && x.OrderType == OrderType.CarProduct)
                .Select(x => x.BaseOrderId)
                .Distinct();

            //新增用车采购价的查询，根据baseOrderId查询
            var carProductOrderPrices = await _dbContext.OrderPrices.AsNoTracking()
                .Where(x => carProductBaseOrderIds.Contains(x.BaseOrderId))
                .OrderBy(x => x.SubOrderId)
                .ToListAsync();

            return carProductOrderPrices
                .GroupBy(x => x.BaseOrderId)
                .Select(x => (
                     x.FirstOrDefault(y => carProductIds.Contains(y.SubOrderId)).SubOrderId,
                     x.First().Quantity,
                     x.Sum(y => y.CostPrice * y.Quantity),
                     x.Sum(y => y.CostPrice * y.Quantity * (100 - y.CostDiscountRate) / 100),
                     x.First().CostCurrencyCode))
                .ToList();
        }


        private IQueryable<PayablesCarProductOrderInfo> CarProductOrderQuery(PayablesSearchInput input)
        {
            var query = from carProductOrder in _dbContext.CarProductOrders
                        join baseOrder in _dbContext.BaseOrders on carProductOrder.BaseOrderId equals baseOrder.Id
                        join settled in _dbContext.SettlementOrderDetails on carProductOrder.Id equals settled.BusinessOrderId into temp
                        from settled in temp.DefaultIfEmpty()
                        where settled == null
                        select new { carProductOrder, baseOrder };
            query = query
                .WhereIF(input.SupplierIds.Any() == false, s => s.carProductOrder.SupplierId != 0)
                .WhereIF(input.SupplierIds.Any(), s => input.SupplierIds.Contains(s.carProductOrder.SupplierId))
                .WhereIF(input.PayablesOrderIds.Any(), x => input.PayablesOrderIds.Contains(x.carProductOrder.Id))
                .WhereIF(input.PayablesBaseOrderIds.Any(), x => input.PayablesBaseOrderIds.Contains(x.baseOrder.Id))
                .WhereIF(input.SupplierOrderIds.Any(), x => input.SupplierOrderIds.Contains(x.carProductOrder.SupplierOrderId));

            if (input is { BillingCycleBegin: not null, BillingCycleEnd: not null } &&
                input.PayablesOrderIds.Any() is false)
            {
                switch (input.SettlementDateType)
                {
                    case PayablesDateType.Create:
                        query = query.Where(s => s.carProductOrder.Status != CarProductOrderStatus.WaitingForPay
                                                 && s.carProductOrder.Status != CarProductOrderStatus.Closed
                                                 && s.baseOrder.CreateTime >= input.BillingCycleBegin
                                                 && s.baseOrder.CreateTime < input.BillingCycleEnd);
                        break;
                    case PayablesDateType.Finish:
                        query = query.Where(s => (s.carProductOrder.Status == CarProductOrderStatus.Finished || s.carProductOrder.Status == CarProductOrderStatus.Refunded)
                                                 && s.carProductOrder.UpdateTime >= input.BillingCycleBegin
                                                 && s.carProductOrder.UpdateTime < input.BillingCycleEnd);
                        break;
                    case PayablesDateType.TravelBeginDate:
                        query = query.Where(s => s.carProductOrder.Status != CarProductOrderStatus.WaitingForPay
                                                 && s.carProductOrder.Status != CarProductOrderStatus.Closed
                                                 && s.carProductOrder.TravelDate >= input.BillingCycleBegin
                                                 && s.carProductOrder.TravelDate < input.BillingCycleEnd);
                        break;
                    default:
                        throw new BusinessException($"{nameof(input.SettlementDateType)} is error");
                        break;
                }
            }

            return query.Select(s => new PayablesCarProductOrderInfo
            {
                CreateOrderDate = s.baseOrder.CreateTime,
                ProductName = s.baseOrder.ProductName,
                ProductSkuName = s.baseOrder.ProductSkuName,
                UpdateTime = s.baseOrder.UpdateTime,
                ContactsName = s.baseOrder.ContactsName,
                Status = s.baseOrder.Status,
                ChannelOrderNo = s.baseOrder.ChannelOrderNo,

                BaseOrderId = s.carProductOrder.BaseOrderId,
                OrderId = s.carProductOrder.Id,
                SupplierId = s.carProductOrder.SupplierId,
                TravelDate = s.carProductOrder.TravelDate,
                SupplierOrderId = s.carProductOrder.SupplierOrderId,
            });
        }
        #endregion

        #region 抵冲单

        public async Task<PagingModel<PayableOffsetOrderInfo>> OffsetOrder(PayablesSearchInput input)
        {
            var result = await OffsetOrderQuery(input)
                .OrderByDescending(x => x.OrderId)
                .PagingAsync(input.PageIndex, input.PageSize);
            return result;
        }

        private IQueryable<PayableOffsetOrderInfo> OffsetOrderQuery(PayablesSearchInput input)
        {
            //原单已结算，并且退款单未结算，不管创建时间。
            var businessType = new[]
            {
                SettlementBusinessType.TicketOrder,
                SettlementBusinessType.MailOrder,
                SettlementBusinessType.HotelOrder,
                SettlementBusinessType.LineOrder,
                SettlementBusinessType.ScenicTicketOrder,
                SettlementBusinessType.ReservationOrder
            };
            var businessSettlementOrderDetails = _dbContext.SettlementOrderDetails
                .Where(x => businessType.Contains(x.SettlementBusinessType));
            var offsetSettlementOrderDetails = _dbContext.SettlementOrderDetails
                .Where(x => x.SettlementBusinessType == SettlementBusinessType.OffsetOrder);

            var query = from b in businessSettlementOrderDetails
                        join baseOrder in _dbContext.BaseOrders on b.BaseOrderId equals baseOrder.Id
                        join offsetOrder in _dbContext.OffsetOrders on b.BaseOrderId equals offsetOrder.BaseOrderId
                        join settle in offsetSettlementOrderDetails on offsetOrder.Id equals settle.BusinessOrderId
                            into temp
                        from settle in temp.DefaultIfEmpty()
                        where settle == null
                        select new { baseOrder, offsetOrder };

            query = query
                .Where(x => x.offsetOrder.OffsetType == OffsetOrderType.Payable &&
                            x.offsetOrder.Status == OffsetOrderStatus.WaitingForSettlement)
                .Where(x => x.offsetOrder.Amount != 0)
                .Where(x => x.offsetOrder.ProcessStatus == null || x.offsetOrder.ProcessStatus == OffsetOrderProcessingStatus.Success)
                .WhereIF(input.SupplierIds.Any() is false, x => x.offsetOrder.SupplierId != 0)
                .WhereIF(input.SupplierIds.Any(), x => input.SupplierIds.Contains(x.offsetOrder.SupplierId))
                .WhereIF(input.PayablesOrderIds.Any(), x => input.PayablesOrderIds.Contains(x.offsetOrder.Id))
                .WhereIF(input.PayablesBaseOrderIds.Any(), x => input.PayablesBaseOrderIds.Contains(x.offsetOrder.Id)
                                                                || input.PayablesBaseOrderIds.Contains(x.offsetOrder
                                                                    .BaseOrderId));

            return query.Select(x => new PayableOffsetOrderInfo
            {
                BaseOrderId = x.offsetOrder.BaseOrderId,
                OrderId = x.offsetOrder.Id,
                SupplierId = x.offsetOrder.SupplierId,
                ProductName = x.offsetOrder.ProductName,
                ProductSkuName = x.offsetOrder.ProductSkuName,
                TotalAmount = x.offsetOrder.Amount,
                CostCurrencyCode = x.offsetOrder.CurrencyCode,
                CreateOrderDate = x.offsetOrder.CreatTime,
                BusinessType = x.offsetOrder.BusinessType,
                CreatorName = x.offsetOrder.CreatorName,
                UpdaterName = x.offsetOrder.UpdaterName
            });
        }

        #endregion

        #region 关联抵冲单

        public async Task<PagingModel<PayableOffsetOrderInfo>> RelatedOffsetOrder(RelatedPayablesSearchInput input)
        {
            //获取账期内所有业务订单的BaseOrderIds
            var baseOrderIds = await GetBaseOrderIds(input);
            if (baseOrderIds.Any() is false)
                return new PagingModel<PayableOffsetOrderInfo>();

            var result = await RelatedOffsetOrderQuery(input, baseOrderIds)
                .OrderByDescending(x => x.OrderId)
                .PagingAsync(input.PageIndex, input.PageSize);
            return result;
        }

        /// <summary>
        /// 关联抵冲单查询
        /// </summary>
        private IQueryable<PayableOffsetOrderInfo> RelatedOffsetOrderQuery(RelatedPayablesSearchInput input,
            List<long> baseOrderIds)
        {
            var receiptBaseOrderIds = baseOrderIds;
            var query = from offsetOrder in _dbContext.OffsetOrders
                        join settled in _dbContext.SettlementOrderDetails on offsetOrder.Id equals settled.BusinessOrderId into temp
                        from settled in temp.DefaultIfEmpty()
                        where settled == null
                        select new { offsetOrder };

            query = query
                .Where(x => x.offsetOrder.OffsetType == OffsetOrderType.Payable &&
                            x.offsetOrder.Status == OffsetOrderStatus.WaitingForSettlement)
                .Where(x => x.offsetOrder.Amount != 0)
                .Where(x => x.offsetOrder.ProcessStatus == null || x.offsetOrder.ProcessStatus == OffsetOrderProcessingStatus.Success)
                .WhereIF(input.SupplierIds.Any() is false, x => x.offsetOrder.SupplierId != 0)
                .WhereIF(input.SupplierIds.Any(), x => input.SupplierIds.Contains(x.offsetOrder.SupplierId))
                .WhereIF(input.PayablesOrderIds.Any(), x => input.PayablesOrderIds.Contains(x.offsetOrder.Id))
                .WhereIF(receiptBaseOrderIds.Any(), x => receiptBaseOrderIds.Contains(x.offsetOrder.BaseOrderId));

            return query.Select(x => new PayableOffsetOrderInfo
            {
                BaseOrderId = x.offsetOrder.BaseOrderId,
                OrderId = x.offsetOrder.Id,
                SupplierId = x.offsetOrder.SupplierId,
                ProductName = x.offsetOrder.ProductName,
                ProductSkuName = x.offsetOrder.ProductSkuName,
                TotalAmount = x.offsetOrder.Amount,
                CostCurrencyCode = x.offsetOrder.CurrencyCode,
                CreateOrderDate = x.offsetOrder.CreatTime,
                BusinessType = x.offsetOrder.BusinessType,
                CreatorName = x.offsetOrder.CreatorName,
                UpdaterName = x.offsetOrder.UpdaterName
            });

        }

        #endregion

        #region 账单预览

        public async Task<IEnumerable<PreCreateOutput>> PreCreate(PreCreateInput input)
        {
            var hotelOrder = await PreHotelOrder(input);
            var mailOrder = await PreMailOrder(input);
            var ticketOrder = await PreTicketOrder(input);
            var reservationOrder = await PreReservationOrder(input);
            var refundOrder = await PreRefundOrder(input);
            var scenicTicketOrder = await PreScenicTicketOrder(input);
            var lineOrder = await PreLineOrder(input);
            var carProductOrder = await PreCarProductOrder(input);
            var offsetOrder = await PreOffsetOrder(input);


            if (input is { BillingCycleBegin: not null, BillingCycleEnd: not null, SearchRelatedOrder: true })
            {
                //查询账期内业务订单的主订单Id
                var baseOrderIds = hotelOrder
                    .Concat(ticketOrder)
                    .Concat(reservationOrder)
                    .Concat(scenicTicketOrder)
                    .Concat(lineOrder)
                    .Concat(carProductOrder)
                    .Concat(mailOrder)
                    .Select(x => x.BaseOrderId).ToList();
                var relatedOrderInput = _mapper.Map<RelatedPayablesSearchInput>(input);
                //查询关联的退款单
                var relatedRefundOrder = await PreRelatedRefundOrder(relatedOrderInput, baseOrderIds);
                refundOrder = refundOrder.Concat(relatedRefundOrder);
                //查询关联的抵冲单
                var relatedOffsetOrder = await PreRelatedOffsetOrder(relatedOrderInput, baseOrderIds);
                offsetOrder = offsetOrder.Concat(relatedOffsetOrder);
            }


            var all = hotelOrder
                .Concat(mailOrder)
                .Concat(ticketOrder)
                .Concat(reservationOrder)
                .Concat(refundOrder)
                .Concat(scenicTicketOrder)
                .Concat(lineOrder)
                .Concat(carProductOrder)
                .Concat(offsetOrder);

            var result = all.GroupBy(s => s.SupplierId)
                .Select(s =>
                {
                    var beginDate = input.BillingCycleBegin ?? s.Min(m => m.OrderSettlementDateTypeTime).Date;
                    var endDate = input.BillingCycleEnd ?? s.Max(m => m.OrderSettlementDateTypeTime).Date;

                    var orderItem = new PreCreateOutput
                    {
                        SupplierId = s.Key,
                        OrderCount = s.Count(),
                        TotalAmount = s.Sum(d => d.TotalAmount),
                        OrderAmount = s.Sum(d => d.TotalAmount),
                        CostCurrencyCode = s.First().CostCurrencyCode,
                        BillingCycleBegin = beginDate,
                        BillingCycleEnd = endDate,
                        HotelOrderDateType = input.HotelOrderDateType,
                        MailOrderDateType = input.MailOrderDateType,
                        ReservationOrderDateType = input.ReservationOrderDateType,
                        TicketOrderDateType = input.TicketOrderDateType,
                        ScenicTicketOrderDateType = input.ScenicTicketOrderDateType,
                        LineOrderDateType = input.LineOrderDateType,
                        CarProductOrderDateType = input.CarProductOrderDateType,
                    };

                    foreach (var item in s.GroupBy(x => x.SettlementBusinessType))
                    {
                        switch (item.Key)
                        {
                            case SettlementBusinessType.HotelOrder:
                                orderItem.HotelOrderData.OrderCount = item.Count();
                                orderItem.HotelOrderData.TotalAmount = item.Sum(x => x.TotalAmount);
                                break;
                            case SettlementBusinessType.TicketOrder:
                                orderItem.TicketOrderData.OrderCount = item.Count();
                                orderItem.TicketOrderData.TotalAmount = item.Sum(x => x.TotalAmount);
                                break;
                            case SettlementBusinessType.ReservationOrder:
                                orderItem.ReservationOrderData.OrderCount = item.Count();
                                orderItem.ReservationOrderData.TotalAmount = item.Sum(x => x.TotalAmount);
                                break;
                            case SettlementBusinessType.ScenicTicketOrder:
                                orderItem.ScenicTicketOrderData.OrderCount = item.Count();
                                orderItem.ScenicTicketOrderData.TotalAmount = item.Sum(x => x.TotalAmount);
                                break;
                            case SettlementBusinessType.LineOrder:
                                orderItem.LineOrderOrderData.OrderCount = item.Count();
                                orderItem.LineOrderOrderData.TotalAmount = item.Sum(x => x.TotalAmount);
                                break;
                            case SettlementBusinessType.CarProductOrder:
                                orderItem.CarProductOrderData.OrderCount = item.Count();
                                orderItem.CarProductOrderData.TotalAmount = item.Sum(x => x.TotalAmount);
                                break;
                            case SettlementBusinessType.RefundOrder:
                                orderItem.RefundOrderData.OrderCount = item.Count();
                                orderItem.RefundOrderData.TotalAmount = item.Sum(x => x.TotalAmount);
                                break;
                            case SettlementBusinessType.OffsetOrder:
                                orderItem.OffsetOrderData.OrderCount = item.Count();
                                orderItem.OffsetOrderData.TotalAmount = item.Sum(x => x.TotalAmount);
                                break;
                            case SettlementBusinessType.MailOrder:
                                orderItem.MailOrderData.OrderCount = item.Count();
                                orderItem.MailOrderData.TotalAmount = item.Sum(x => x.TotalAmount);
                                break;
                            default:
                                break;
                        }
                    }

                    return orderItem;
                })
                .ToList();

            return result;
        }

        private async Task<IEnumerable<SettlementOrderDetailInfo>> PreHotelOrder(PreCreateInput input)
        {
            //结算周期为空或者没指定订单Id列表
            if (!input.BillingCycleBegin.HasValue && !input.BillingCycleEnd.HasValue && input.HotelOrderIds.Any() is false)
                return Enumerable.Empty<SettlementOrderDetailInfo>();

            //日历酒店
            var query = HotelQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.HotelOrderDateType,
                PayablesOrderIds = input.HotelOrderIds,
                PayablesBaseOrderIds = input.BusinessSearches
                    .Where(x => x.BusinessType == SettlementBusinessType.HotelOrder)
                    .Select(x => x.PayablesBaseOrderId),
                SupplierOrderIds = input.SupplierOrderIds,
            });
            var orders = await query
                .Select(s => new PayablesHotelOrderInfo
                {
                    BaseOrderId = s.BaseOrderId,
                    SupplierId = s.SupplierId,
                    OrderId = s.OrderId,
                    CheckInDate = s.CheckInDate,
                    CheckOutDate = s.CheckOutDate,
                    CreateOrderDate = s.CreateOrderDate,
                    Quantity = s.Quantity,
                    SupplierOrderId = s.SupplierOrderId,
                })
                .ToListAsync();
            var costPrices = await GetHotelOrderCostPrices(orders);

            var result = orders
                .Join(costPrices, h => h.OrderId, c => c.orderId, (h, c) => new { h, c })
                .Select(s =>
                {
                    var orderSettlementDateTypeTime = input.HotelOrderDateType switch
                    {
                        PayablesDateType.Create => s.h.CreateOrderDate,
                        PayablesDateType.CheckIn => s.h.CheckInDate,
                        PayablesDateType.CheckOut => s.h.CheckOutDate,
                        _ => s.h.CreateOrderDate
                    };
                    var o = new SettlementOrderDetailInfo
                    {
                        BaseOrderId = s.h.BaseOrderId,
                        BusinessOrderId = s.h.OrderId,
                        SettlementBusinessType = SettlementBusinessType.HotelOrder,
                        SupplierId = s.h.SupplierId,
                        Quantity = s.h.Quantity,
                        TotalAmount = s.c.totalCostPrice,
                        CostCurrencyCode = s.c.costCurrencyCode,
                        OrderSettlementDateTypeTime = orderSettlementDateTypeTime,
                        SupplierOrderId = s.h.SupplierOrderId,
                        OrderType = OrderType.Hotel,
                    };
                    return o;
                })
                .ToList();

            return result;
        }

        private async Task<IEnumerable<SettlementOrderDetailInfo>> PreMailOrder(PreCreateInput input)
        {
            //结算周期为空或者没指定订单Id列表
            if (!input.BillingCycleBegin.HasValue && !input.BillingCycleEnd.HasValue && input.MailOrderOrderIds.Any() is false)
                return Enumerable.Empty<SettlementOrderDetailInfo>();

            var query = MailQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.MailOrderDateType,
                PayablesOrderIds = input.MailOrderOrderIds,
                PayablesBaseOrderIds = input.BusinessSearches
                    .Where(x => x.BusinessType == SettlementBusinessType.MailOrder)
                    .Select(x => x.PayablesBaseOrderId)
            });

            var orders = await query.ToListAsync();
            var subOrderIds = orders.Select(x => x.OrderId);
            var prices = GetPriceAndQuantity(subOrderIds).ToList();
            var result = orders.Select(s =>
            {
                var p = prices.First(p => p.subOrderId == s.OrderId);
                var t = input.MailOrderDateType switch
                {
                    PayablesDateType.Create => s.CreateOrderDate,
                    PayablesDateType.Finish => s.UpdateTime,
                    _ => s.CreateOrderDate
                };
                var o = new SettlementOrderDetailInfo
                {
                    BaseOrderId = s.BaseOrderId,
                    BusinessOrderId = s.OrderId,
                    SettlementBusinessType = SettlementBusinessType.MailOrder,
                    SupplierId = s.SupplierId,
                    Quantity = p.quantity,
                    TotalAmount = p.total,
                    CostCurrencyCode = p.costCurrencyCode,
                    OrderSettlementDateTypeTime = t!.Value
                };
                return o;
            });

            return result;
        }

        private async Task<IEnumerable<SettlementOrderDetailInfo>> PreTicketOrder(PreCreateInput input)
        {
            //结算周期为空或者没指定订单Id列表
            if (!input.BillingCycleBegin.HasValue && !input.BillingCycleEnd.HasValue && input.TicketOrderIds.Any() is false)
                return Enumerable.Empty<SettlementOrderDetailInfo>();

            var query = TicketQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.TicketOrderDateType,
                PayablesOrderIds = input.TicketOrderIds,
                PayablesBaseOrderIds = input.BusinessSearches
                    .Where(x => x.BusinessType == SettlementBusinessType.TicketOrder)
                    .Select(x => x.PayablesBaseOrderId)
            });

            var orders = await query.ToListAsync();
            var subOrderIds = orders.Select(x => x.OrderId);
            var prices = GetPriceAndQuantity(subOrderIds).ToList();
            var result = orders.Select(s =>
            {
                var p = prices.First(p => p.subOrderId == s.OrderId);
                var t = input.TicketOrderDateType switch
                {
                    PayablesDateType.Create => s.CreateOrderDate,
                    PayablesDateType.Finish => s.UpdateTime,
                    _ => s.CreateOrderDate
                };
                var o = new SettlementOrderDetailInfo
                {
                    BaseOrderId = s.BaseOrderId,
                    BusinessOrderId = s.OrderId,
                    SettlementBusinessType = SettlementBusinessType.TicketOrder,
                    SupplierId = s.SupplierId,
                    Quantity = p.quantity,
                    TotalAmount = p.total,
                    CostCurrencyCode = p.costCurrencyCode,
                    OrderSettlementDateTypeTime = t!.Value,
                    SupplierOrderId = s.SupplierOrderId,
                    OrderType = OrderType.Ticket,
                };
                return o;
            });

            return result;
        }

        private async Task<IEnumerable<SettlementOrderDetailInfo>> PreReservationOrder(PreCreateInput input)
        {
            //结算周期为空或者没指定订单Id列表
            if (!input.BillingCycleBegin.HasValue && !input.BillingCycleEnd.HasValue && input.ReservationOrderIds.Any() is false)
                return Enumerable.Empty<SettlementOrderDetailInfo>();

            var query = ReservationQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.ReservationOrderDateType,
                PayablesOrderIds = input.ReservationOrderIds,
                PayablesBaseOrderIds = input.BusinessSearches
                    .Where(x => x.BusinessType == SettlementBusinessType.ReservationOrder)
                    .Select(x => x.PayablesBaseOrderId)
            });

            var orders = await query
                .Select(s => new PayablesReservationOrderInfo
                {
                    BaseOrderId = s.BaseOrderId,
                    TicketOrderId = s.TicketOrderId,
                    OrderId = s.OrderId,
                    SupplierId = s.SupplierId,
                    UpdateTime = s.UpdateTime
                })
                .ToListAsync();

            List<(long ticketOrderId, long reservationOrderId)> rorder = orders
                .Select(s => (s.TicketOrderId, s.OrderId))
                .ToList();
            var prices = GetReservationOrderPrice(rorder);
            foreach (var order in orders)
            {
                var price = prices.First(s => s.reservationOrderId == order.OrderId);
                order.Quantity = price.quantity;
                order.CostPrice = price.cost;
                order.TotalAmount = price.total;
                order.CostCurrencyCode = price.costCurrencyCode;
            }

            var result = orders
                .Select(s =>
                {
                    var t = input.ReservationOrderDateType switch
                    {
                        PayablesDateType.Create => s.CreateOrderDate,
                        PayablesDateType.Finish => s.UpdateTime,
                        _ => s.UpdateTime
                    };
                    var o = new SettlementOrderDetailInfo
                    {
                        BaseOrderId = s.BaseOrderId,
                        BusinessOrderId = s.OrderId,
                        SettlementBusinessType = SettlementBusinessType.ReservationOrder,
                        SupplierId = s.SupplierId,
                        Quantity = s.Quantity,
                        TotalAmount = s.TotalAmount,
                        CostCurrencyCode = s.CostCurrencyCode,
                        OrderSettlementDateTypeTime = t!.Value
                    };
                    return o;
                })
                .ToList();
            return result;
        }

        private async Task<IEnumerable<SettlementOrderDetailInfo>> PreRefundOrder(PreCreateInput input)
        {
            //结算周期为空或者没指定订单Id列表
            if (!input.BillingCycleBegin.HasValue && !input.BillingCycleEnd.HasValue && input.RefundOrderIds.Any() is false)
                return Enumerable.Empty<SettlementOrderDetailInfo>();

            var query =
                input.RefundOrderIds.Any()
                ? RelatedRefundOrderQuery(new RelatedPayablesSearchInput
                {
                    SupplierIds = input.SupplierIds,
                    PayablesOrderIds = input.RefundOrderIds,
                }, new List<long>())
                : RefundQuery(new PayablesSearchInput
                {
                    SupplierIds = input.SupplierIds,
                    BillingCycleBegin = input.BillingCycleBegin,
                    BillingCycleEnd = input.BillingCycleEnd,
                    SettlementDateType = PayablesDateType.Finish,
                    PayablesOrderIds = input.RefundOrderIds,
                    PayablesBaseOrderIds = input.BusinessSearches
                    .Where(x => x.BusinessType == SettlementBusinessType.RefundOrder)
                    .Select(x => x.PayablesBaseOrderId)
                });

            var result = await query
                .Select(s => new SettlementOrderDetailInfo
                {
                    BaseOrderId = s.BaseOrderId,
                    BusinessOrderId = s.OrderId,
                    SettlementBusinessType = SettlementBusinessType.RefundOrder,
                    SupplierId = s.SupplierId,
                    Quantity = s.Quantity,
                    TotalAmount = s.TotalAmount,
                    CostCurrencyCode = s.CostCurrencyCode,
                    OrderSettlementDateTypeTime = s.CreateOrderDate
                })
                .ToListAsync();
            return result;
        }

        private async Task<IEnumerable<SettlementOrderDetailInfo>> PreRelatedRefundOrder(RelatedPayablesSearchInput input,
            List<long> baseOrderIds)
        {
            if (baseOrderIds.Any() is false)
                return Enumerable.Empty<SettlementOrderDetailInfo>();

            var query =
                RelatedRefundOrderQuery(input, baseOrderIds);

            var result = await query
                .Select(s => new SettlementOrderDetailInfo
                {
                    BaseOrderId = s.BaseOrderId,
                    BusinessOrderId = s.OrderId,
                    SettlementBusinessType = SettlementBusinessType.RefundOrder,
                    SupplierId = s.SupplierId,
                    Quantity = s.Quantity,
                    TotalAmount = s.TotalAmount,
                    CostCurrencyCode = s.CostCurrencyCode,
                    OrderSettlementDateTypeTime = s.CreateOrderDate
                })
                .ToListAsync();
            return result;
        }

        private async Task<IEnumerable<SettlementOrderDetailInfo>> PreScenicTicketOrder(PreCreateInput input)
        {
            //结算周期为空或者没指定订单Id列表
            if (!input.BillingCycleBegin.HasValue && !input.BillingCycleEnd.HasValue && input.ScenicTicketOrderIds.Any() is false)
                return Enumerable.Empty<SettlementOrderDetailInfo>();

            var query = ScenicTicketQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.ScenicTicketOrderDateType,
                PayablesOrderIds = input.ScenicTicketOrderIds,
                PayablesBaseOrderIds = input.BusinessSearches
                    .Where(x => x.BusinessType == SettlementBusinessType.ScenicTicketOrder)
                    .Select(x => x.PayablesBaseOrderId),
                SupplierOrderIds = input.SupplierOrderIds,
            });

            var orders = await query.ToListAsync();
            var subOrderIds = orders.Select(x => x.OrderId);
            var prices = GetPriceAndQuantity(subOrderIds).ToList();
            var result = orders.Select(s =>
            {
                var p = prices.First(p => p.subOrderId == s.OrderId);
                var t = input.ScenicTicketOrderDateType switch
                {
                    PayablesDateType.Create => s.CreateOrderDate,
                    PayablesDateType.Finish => s.UpdateTime,
                    _ => s.CreateOrderDate
                };
                var o = new SettlementOrderDetailInfo
                {
                    BaseOrderId = s.BaseOrderId,
                    BusinessOrderId = s.OrderId,
                    SettlementBusinessType = SettlementBusinessType.ScenicTicketOrder,
                    SupplierId = s.SupplierId,
                    Quantity = p.quantity,
                    TotalAmount = p.total,
                    CostCurrencyCode = p.costCurrencyCode,
                    OrderSettlementDateTypeTime = t!.Value,
                    SupplierOrderId = s.SupplierOrderId,
                    OrderType = OrderType.ScenicTicket,
                    CostTotalAmount = p.cost,
                };
                return o;
            });

            return result;
        }

        private async Task<IEnumerable<SettlementOrderDetailInfo>> PreLineOrder(PreCreateInput input)
        {
            //结算周期为空或者没指定订单Id列表
            if (!input.BillingCycleBegin.HasValue && !input.BillingCycleEnd.HasValue && input.LineOrderIds.Any() is false)
                return Enumerable.Empty<SettlementOrderDetailInfo>();

            var query = LineOrderQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.LineOrderDateType,
                PayablesOrderIds = input.LineOrderIds,
                PayablesBaseOrderIds = input.BusinessSearches
                    .Where(x => x.BusinessType == SettlementBusinessType.LineOrder)
                    .Select(x => x.PayablesBaseOrderId),
                SupplierOrderIds = input.SupplierOrderIds,
            });

            var orders = await query.ToListAsync();
            var subOrderIds = orders.Select(x => x.OrderId).ToList();
            var prices = await LineOrderCostPrice(subOrderIds);
            var result = orders.Select(s =>
            {
                var p = prices.First(p => p.subOrderId == s.OrderId);
                var t = input.LineOrderDateType switch
                {
                    PayablesDateType.Create => s.CreateOrderDate,
                    PayablesDateType.Finish => s.UpdateTime,
                    _ => s.CreateOrderDate
                };
                var o = new SettlementOrderDetailInfo
                {
                    BaseOrderId = s.BaseOrderId,
                    BusinessOrderId = s.OrderId,
                    SettlementBusinessType = SettlementBusinessType.LineOrder,
                    SupplierId = s.SupplierId,
                    Quantity = p.quantity,
                    TotalAmount = p.total,
                    CostCurrencyCode = p.costCurrencyCode,
                    OrderSettlementDateTypeTime = t!.Value,
                    SupplierOrderId = s.SupplierOrderId,
                    OrderType = OrderType.TravelLineOrder,
                    CostTotalAmount = p.cost,
                };
                return o;
            });

            return result;
        }

        private async Task<IEnumerable<SettlementOrderDetailInfo>> PreCarProductOrder(PreCreateInput input)
        {
            //结算周期为空或者没指定订单Id列表
            if (!input.BillingCycleBegin.HasValue &&
                !input.BillingCycleEnd.HasValue &&
                input.CarProductOrderIds.Any() is false)
                return Enumerable.Empty<SettlementOrderDetailInfo>();

            var query = CarProductOrderQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.CarProductOrderDateType,
                PayablesOrderIds = input.CarProductOrderIds,
                PayablesBaseOrderIds = input.BusinessSearches
                    .Where(x => x.BusinessType == SettlementBusinessType.CarProductOrder)
                    .Select(x => x.PayablesBaseOrderId),
                SupplierOrderIds = input.SupplierOrderIds,
            });

            var orders = await query.ToListAsync();
            var subOrderIds = orders.Select(x => x.OrderId).ToList();
            var prices = await CarProductCostPrice(subOrderIds);
            var result = orders.Select(s =>
            {
                var p = prices.FirstOrDefault(x => x.carProductId == s.OrderId);
                var t = input.CarProductOrderDateType switch
                {
                    PayablesDateType.Create => s.CreateOrderDate,
                    PayablesDateType.Finish => s.UpdateTime,
                    _ => s.CreateOrderDate
                };
                var o = new SettlementOrderDetailInfo
                {
                    BaseOrderId = s.BaseOrderId,
                    BusinessOrderId = s.OrderId,
                    SettlementBusinessType = SettlementBusinessType.CarProductOrder,
                    SupplierId = s.SupplierId,
                    Quantity = s.Quantity,
                    TotalAmount = p.totalAmount,
                    CostCurrencyCode = p.costCurrencyCode,
                    OrderSettlementDateTypeTime = t!.Value,
                    SupplierOrderId = s.SupplierOrderId,
                    OrderType = OrderType.CarProduct,
                    CostTotalAmount = p.cost,
                };
                return o;
            });

            return result;
        }

        private async Task<IEnumerable<SettlementOrderDetailInfo>> PreOffsetOrder(PreCreateInput input)
        {
            //结算周期为空或者没指定订单Id列表
            if (!input.BillingCycleBegin.HasValue && !input.BillingCycleEnd.HasValue && input.OffsetOrderIds.Any() is false)
                return Enumerable.Empty<SettlementOrderDetailInfo>();

            var query = input.OffsetOrderIds.Any()
                ? RelatedOffsetOrderQuery(new RelatedPayablesSearchInput
                {
                    SupplierIds = input.SupplierIds,
                    PayablesOrderIds = input.OffsetOrderIds,
                }, new List<long>())
                : OffsetOrderQuery(new PayablesSearchInput
                {
                    SupplierIds = input.SupplierIds,
                    BillingCycleBegin = input.BillingCycleBegin,
                    BillingCycleEnd = input.BillingCycleEnd,
                    SettlementDateType = PayablesDateType.Create,
                    PayablesOrderIds = input.OffsetOrderIds,
                    PayablesBaseOrderIds = input.BusinessSearches
                    .Where(x => x.BusinessType == SettlementBusinessType.OffsetOrder)
                    .Select(x => x.PayablesBaseOrderId)
                });

            var result = await query
                .Select(s => new SettlementOrderDetailInfo
                {
                    BaseOrderId = s.BaseOrderId,
                    BusinessOrderId = s.OrderId,
                    SettlementBusinessType = SettlementBusinessType.OffsetOrder,
                    SupplierId = s.SupplierId,
                    TotalAmount = s.TotalAmount,
                    CostCurrencyCode = s.CostCurrencyCode,
                    OrderSettlementDateTypeTime = s.CreateOrderDate
                })
                .ToListAsync();

            return result;
        }


        private async Task<IEnumerable<SettlementOrderDetailInfo>> PreRelatedOffsetOrder(RelatedPayablesSearchInput input,
            List<long> baseOrderIds)
        {
            if (baseOrderIds.Any() is false)
                return Enumerable.Empty<SettlementOrderDetailInfo>();

            var query = RelatedOffsetOrderQuery(input, baseOrderIds);

            var result = await query
                .Select(s => new SettlementOrderDetailInfo
                {
                    BaseOrderId = s.BaseOrderId,
                    BusinessOrderId = s.OrderId,
                    SettlementBusinessType = SettlementBusinessType.OffsetOrder,
                    SupplierId = s.SupplierId,
                    Quantity = s.Quantity,
                    TotalAmount = s.TotalAmount,
                    CostCurrencyCode = s.CostCurrencyCode,
                    OrderSettlementDateTypeTime = s.CreateOrderDate
                })
                .ToListAsync();
            return result;
        }

        #endregion

        #region 创建账单

        public async Task<bool> Create(CurrentUser currentUser, PreCreateInput input)
        {
            var tenantId = currentUser.tenant!.Value;
            var offsetOrderLockSecret = Guid.NewGuid().ToString();
            var offsetOrderLockName = $"{_offsetOrderSettlementCreateLock}:{tenantId}";
            await _redisClient.LockTakeWaitingAsync(offsetOrderLockName, offsetOrderLockSecret, TimeSpan.FromSeconds(10));
            try
            {
                var hotelOrder = await PreHotelOrder(input);
                var mailOrder = await PreMailOrder(input);
                var ticketOrder = await PreTicketOrder(input);
                var reservationOrder = await PreReservationOrder(input);
                var refundOrder = await PreRefundOrder(input);
                var scenicTicketOrder = await PreScenicTicketOrder(input);
                var lineOrder = await PreLineOrder(input);
                var carProductOrder = await PreCarProductOrder(input);
                var offsetOrder = await PreOffsetOrder(input);

                if (input is { BillingCycleBegin: not null, BillingCycleEnd: not null, SearchRelatedOrder: true })
                {
                    //查询账期内业务订单的主订单Id
                    var baseOrderIds = hotelOrder
                        .Concat(ticketOrder)
                        .Concat(reservationOrder)
                        .Concat(scenicTicketOrder)
                        .Concat(lineOrder)
                        .Concat(carProductOrder)
                        .Concat(mailOrder)
                        .Select(x => x.BaseOrderId).ToList();
                    var relatedOrderInput = _mapper.Map<RelatedPayablesSearchInput>(input);
                    //查询关联的退款单
                    var relatedRefundOrder = await PreRelatedRefundOrder(relatedOrderInput, baseOrderIds);
                    refundOrder = refundOrder.Concat(relatedRefundOrder);
                    //查询关联的抵冲单
                    var relatedOffsetOrder = await PreRelatedOffsetOrder(relatedOrderInput, baseOrderIds);
                    offsetOrder = offsetOrder.Concat(relatedOffsetOrder);
                }

                var details = hotelOrder
                    .Concat(mailOrder)
                    .Concat(ticketOrder)
                    .Concat(reservationOrder)
                    .Concat(refundOrder)
                    .Concat(scenicTicketOrder)
                    .Concat(lineOrder)
                    .Concat(carProductOrder)
                    .Concat(offsetOrder)
                    .ToList();
                var supplierIds = details.Select(x => x.SupplierId).Distinct().ToList();
                var suppliers = await TenantSupplierGetByIds(supplierIds);

                List<SettlementOrder> settlementOrders = new();
                List<SettlementOrderDate> settlementOrdersDates = new();
                List<SettlementOrderDetail> settlementOrdersDetails = new();
                foreach (var g in details.GroupBy(s => s.SupplierId))
                {
                    var beginDate = input.BillingCycleBegin ?? g.Min(m => m.OrderSettlementDateTypeTime).Date;
                    var endDate = input.BillingCycleEnd ?? g.Max(m => m.OrderSettlementDateTypeTime).Date;
                    var supplier = suppliers.FirstOrDefault(m => m.SupplierId == g.Key);
                    var settlementOrder = new SettlementOrder
                    {
                        SupplierId = g.Key,
                        OrderCount = g.Count(),
                        TotalAmount = g.Sum(s => s.TotalAmount),
                        BillingCycleBegin = beginDate,
                        BillingCycleEnd = endDate,
                        CreatorId = currentUser.userid,
                        CreatorName = currentUser.nickname,
                        Status = SettlementOrderStatus.Checking,
                        TotalAmountCurrencyCode = g.First().CostCurrencyCode,
                        TenantDepartmentId = supplier?.TenantDepartmentId
                    };
                    settlementOrders.Add(settlementOrder);
                    settlementOrdersDates.AddRange(GetSettlementOrderDates(settlementOrder.Id, input));
                    settlementOrdersDetails.AddRange(g.Select(detail => new SettlementOrderDetail
                    {
                        SettlementOrderId = settlementOrder.Id,
                        BaseOrderId = detail.BaseOrderId,
                        BusinessOrderId = detail.BusinessOrderId,
                        SupplierId = detail.SupplierId,
                        SettlementBusinessType = detail.SettlementBusinessType,
                        Quantity = detail.Quantity,
                        TotalAmount = detail.TotalAmount
                    }));
                }

                if (settlementOrders.Any() == false)
                    return false;

                if (offsetOrder.Any())
                {
                    var offsetOrderIds = offsetOrder.Select(x => x.BusinessOrderId);
                    var offsetOrderList = await _dbContext.OffsetOrders
                        .Where(x => x.ProcessStatus == null || x.ProcessStatus == OffsetOrderProcessingStatus.Success)
                        .Where(x => offsetOrderIds.Contains(x.Id))
                        .ToListAsync();
                    foreach (var item in offsetOrderList)
                    {
                        item.Status = OffsetOrderStatus.SettlementCompleted;
                        item.UpdaterId = currentUser.userid;
                        item.UpdaterName = currentUser.nickname;
                        item.UpdateTime = DateTime.Now;
                    }
                }

                await _dbContext.SettlementOrders.AddRangeAsync(settlementOrders);
                await _dbContext.SettlementOrderDates.AddRangeAsync(settlementOrdersDates);
                await _dbContext.SettlementOrderDetails.AddRangeAsync(settlementOrdersDetails);
                var rows = await _dbContext.SaveChangesAsync();
                return rows > 0;
            }
            finally
            {
                await _redisClient.LockReleaseAsync(offsetOrderLockName, offsetOrderLockSecret);
            }
        }

        [UnitOfWork]
        public async Task VccCreateProcess(CreateVccSettlementOrderMessage message)
        {
            //目前只有线路和门票订单支持vcc开卡
            var baseOrder = await _dbContext.BaseOrders.IgnoreQueryFilters()
                .FirstOrDefaultAsync(x => x.Id == message.BaseOrderId);

            //查询是否已存在结算单
            var settlementDetail = await _dbContext.SettlementOrderDetails
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync(x => x.BaseOrderId == message.BaseOrderId
                                          && x.SettlementBusinessType == message.SettlementBusinessType);

            SettlementOrder? settlementOrder;
            if (settlementDetail != null)
            {
                //已存在结算单.判断结算状态
                var settlementOrderId = settlementDetail.SettlementOrderId;
                settlementOrder = await _dbContext.SettlementOrders.IgnoreQueryFilters()
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == settlementOrderId);

                baseOrder.VccPaymentStatus = settlementOrder.Status == SettlementOrderStatus.PaymentCompleted
                    ? OrderVirtualCreditCardPaymentStatus.Success
                    : OrderVirtualCreditCardPaymentStatus.Exception;

                //虚拟卡多次的交易处理
                //对比交易金额,如果币种不一致,需要进行汇率转换再对比(不需要进行余量的增加)
                var exchangeTotalAmount =
                    Math.Round(settlementDetail.TotalAmount * message.LocalToOriginalExchangeRate, 2);
                if (message.TransactionAmount > exchangeTotalAmount)
                {
                    //超额订单状态异常处理
                    baseOrder.VccPaymentStatus = OrderVirtualCreditCardPaymentStatus.Exception;
                }
            }
            else
            {
                //未存在结算单.自动生成且判断金额
                var orderDetailInfo = new SettlementOrderDetailInfo();
                var preCreateInput = new PreCreateInput
                {
                    HotelOrderDateType = PayablesDateType.Create,
                    TicketOrderDateType = PayablesDateType.Create,
                    MailOrderDateType = PayablesDateType.Create,
                    ReservationOrderDateType = PayablesDateType.Create,
                    ScenicTicketOrderDateType = PayablesDateType.Create,
                    LineOrderDateType = PayablesDateType.Create,
                    BillingCycleBegin = baseOrder.CreateTime,
                    BillingCycleEnd = baseOrder.CreateTime,
                    BusinessSearches = new List<PreCreateBusinessSearch>()
                };

                //获取结算订单信息
                switch (message.OrderType)
                {
                    case OrderType.ScenicTicket:

                        preCreateInput.BusinessSearches.Add(new PreCreateBusinessSearch
                        {
                            PayablesBaseOrderId = message.BaseOrderId,
                            BusinessType = SettlementBusinessType.ScenicTicketOrder
                        });
                        var scenicTicketOrder = await PreScenicTicketOrder(preCreateInput);
                        orderDetailInfo = scenicTicketOrder.FirstOrDefault();

                        break;
                    case OrderType.TravelLineOrder:

                        preCreateInput.BusinessSearches.Add(new PreCreateBusinessSearch
                        {
                            PayablesBaseOrderId = message.BaseOrderId,
                            BusinessType = SettlementBusinessType.LineOrder
                        });
                        var lineOrder = await PreLineOrder(preCreateInput);
                        orderDetailInfo = lineOrder.FirstOrDefault();

                        break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(message.OrderType), message.OrderType, "未支持的订单类型");
                }

                if (orderDetailInfo == null) return;

                //创建付款结算单
                var supplierIds = new List<long> { orderDetailInfo.SupplierId };
                var supplier = (await TenantSupplierGetByIds(supplierIds)).FirstOrDefault();
                settlementOrder = new SettlementOrder
                {
                    SupplierId = orderDetailInfo.SupplierId,
                    OrderCount = 1,
                    TotalAmount = orderDetailInfo.TotalAmount,
                    BillingCycleBegin = baseOrder.CreateTime,
                    BillingCycleEnd = baseOrder.CreateTime,
                    CreatorId = 0,
                    CreatorName = _systemOperatorName,
                    Status = SettlementOrderStatus.Checking,
                    TotalAmountCurrencyCode = orderDetailInfo.CostCurrencyCode,
                    TenantDepartmentId = supplier?.TenantDepartmentId
                };
                var settlementOrderDetail = new SettlementOrderDetail
                {
                    SettlementOrderId = settlementOrder.Id,
                    BaseOrderId = orderDetailInfo.BaseOrderId,
                    BusinessOrderId = orderDetailInfo.BusinessOrderId,
                    SupplierId = orderDetailInfo.SupplierId,
                    SettlementBusinessType = orderDetailInfo.SettlementBusinessType,
                    Quantity = orderDetailInfo.Quantity,
                    TotalAmount = orderDetailInfo.TotalAmount
                };
                var settlementOrderDates = GetSettlementOrderDates(settlementOrder.Id, preCreateInput);

                //对比交易金额,如果币种不一致,需要进行汇率转换再对比(不需要进行余量的增加)
                var exchangeTotalAmount =
                    Math.Round(orderDetailInfo.TotalAmount * message.LocalToOriginalExchangeRate, 2);
                baseOrder.VccPaymentStatus = message.TransactionAmount == exchangeTotalAmount
                    ? OrderVirtualCreditCardPaymentStatus.Success
                    : OrderVirtualCreditCardPaymentStatus.Exception;

                if (baseOrder.VccPaymentStatus == OrderVirtualCreditCardPaymentStatus.Success)
                {
                    //付款结算单自动确（已付款）默认扣款的vcc银行账户
                    settlementOrder.PaymentAmount = orderDetailInfo.TotalAmount;
                    settlementOrder.PaymentAmountCurrencyCode = orderDetailInfo.CostCurrencyCode;
                    settlementOrder.Status = SettlementOrderStatus.PaymentCompleted;
                    settlementOrder.PayTime = DateTime.Now;
                    settlementOrder.PayerName = _systemOperatorName;
                    settlementOrder.BankCode = message.BankCode;
                    settlementOrder.BankName = message.BankName;
                    settlementOrder.BranchName = message.BranchName;
                    settlementOrder.BankAccount = message.AccountNo;
                    settlementOrder.AccountName = message.AccountName;
                    settlementOrder.BankAccountType = TenantBankAccountType.VirtualCard;
                    settlementOrder.AbroadAccountCurrencyCode = message.AccountCurrencyCode;
                    settlementOrder.AbroadAccountAddress = message.AccountAddress;
                    settlementOrder.SwiftCode = message.SwiftCode;

                    //转账记录
                    var transferRecord = new SettlementOrderTransferRecord
                    {
                        SettlementOrderId = settlementOrder.Id,
                        TransferType = SettlementTransferType.VirtualCreditCard,
                        BankFee = Convert.ToDecimal(_configuration["Yeepay:TransferFee"]),
                        PayerName = "system",
                        Status = SettlementTransferStatus.Succeeded
                    };
                    await _dbContext.SettlementOrderTransferRecords.AddAsync(transferRecord);

                    //订单记录
                    var orderLog = new OrderLogs
                    {
                        OrderId = baseOrder.Id,
                        OperationType = OrderOperationType.Confirmed,
                        OrderLogType = OrderLogType.None,
                        OperationRole = UserType.None,
                        UserId = 0,
                        UserName = _systemOperatorName
                    };

                    //订单状态如果仍未确认变成已确认
                    switch (message.OrderType)
                    {
                        case OrderType.ScenicTicket:

                            var scenicTicketOrder = await _dbContext.ScenicTicketOrders.IgnoreQueryFilters()
                                .FirstOrDefaultAsync(x => x.BaseOrderId == message.BaseOrderId);
                            if (scenicTicketOrder.Status == ScenicTicketOrderStatus.WaitingForDeliver)
                            {
                                scenicTicketOrder.Status = ScenicTicketOrderStatus.Delivered;

                                //订单记录类型
                                orderLog.OrderLogType = OrderLogType.ScenicTicket;
                            }

                            break;
                        case OrderType.TravelLineOrder:

                            var travelLineOrder = await _dbContext.TravelLineOrder.IgnoreQueryFilters()
                                .FirstOrDefaultAsync(x => x.BaseOrderId == message.BaseOrderId);
                            if (travelLineOrder.Status == TravelLineOrderStatus.WaitingForConfirm)
                            {
                                travelLineOrder.Status = TravelLineOrderStatus.Confirmed;
                                travelLineOrder.UpdateTime = DateTime.Now;

                                //订单记录类型
                                orderLog.OrderLogType = OrderLogType.TravelLine;

                                if (travelLineOrder.PurchaseSourceType == LineProductPurchaseSourceType.InterfaceDock)
                                {
                                    //发起创建供应商订单事件
                                    await _capPublisher.PublishAsync(CapTopics.Order.TravelLineSupplierOrderCreate,
                                        new CreateSupplierApiOrderMessage
                                        {
                                            BaseOrderId = baseOrder.Id,
                                            TenantId = baseOrder.TenantId
                                        });
                                }
                                else
                                {
                                    //OTA线路订单确认.需要同步确认状态到渠道端
                                    await _capPublisher.PublishAsync(CapTopics.Order.TravelLineOrderSyncOtaConfirm,
                                        new TravelLineOrderSyncConfirmMessage
                                        {
                                            TravelLineOrderId = travelLineOrder.Id,
                                            BaseOrderId = baseOrder.Id,
                                            SellingPlatform = baseOrder.SellingPlatform,
                                            ChannelOrderNo = baseOrder.ChannelOrderNo,
                                            TenantId = message.TenantId,
                                            AgencyId = baseOrder.AgencyId
                                        });
                                }
                            }

                            break;
                        default:
                            throw new ArgumentOutOfRangeException(nameof(message.OrderType), message.OrderType,
                                "未支持的订单类型");
                    }

                    //没有指定订单类型.不插入数据
                    if (orderLog.OrderLogType != OrderLogType.None)
                    {
                        orderLog.SetTenantId(message.TenantId);
                        await _dbContext.OrderLogs.AddAsync(orderLog);
                    }
                }

                settlementOrder.SetTenantId(message.TenantId);
                await _dbContext.SettlementOrders.AddAsync(settlementOrder);
                await _dbContext.SettlementOrderDetails.AddAsync(settlementOrderDetail);
                await _dbContext.SettlementOrderDates.AddRangeAsync(settlementOrderDates);
            }

            //更新虚拟卡交易状态
            await _capPublisher.PublishAsync(CapTopics.Payment.UpdateVccPurchaseStatus,
                new UpdateVccPurchaseStatusMessage
                {
                    BaseOrderIds = new List<long> { message.BaseOrderId },
                    TenantId = baseOrder.TenantId,
                    SettlementOrderId = settlementOrder.Id,
                    SettlementTime = settlementOrder.PayTime,
                    Status = baseOrder.VccPaymentStatus switch
                    {
                        OrderVirtualCreditCardPaymentStatus.Success => VirtualCreditCardPurchaseStatus
                            .TransactionSuccess,
                        OrderVirtualCreditCardPaymentStatus.Exception => VirtualCreditCardPurchaseStatus
                            .TransactionException,
                        _ => throw new ArgumentOutOfRangeException()
                    }
                });
        }

        [UnitOfWork]
        public async Task UpdateVccSettlementOrder(UpdateVccSettlementOrderMessage message)
        {
            //查询订单vcc状态
            var baseOrder = await _dbContext.BaseOrders.IgnoreQueryFilters()
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == message.BaseOrderId);
            if (baseOrder.VccPaymentStatus != OrderVirtualCreditCardPaymentStatus.Exception)
            {
                //非交易异常的订单不更新
                return;
            }


            //查询主订单是否存在结算单
            var settlementOrderDetail = await _dbContext.SettlementOrderDetails.IgnoreQueryFilters()
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.BaseOrderId == message.BaseOrderId);
            if (settlementOrderDetail == null)
            {
                return;
            }

            var settlementOrder = await _dbContext.SettlementOrders.IgnoreQueryFilters()
                .FirstOrDefaultAsync(x => x.Id == settlementOrderDetail.SettlementOrderId);
            if (settlementOrder == null)
            {
                return;
            }

            //存在则添加到结算单
            var query = RelatedOffsetOrderQuery(
                new RelatedPayablesSearchInput
                {
                    SupplierIds = new List<long> { message.SupplierId },
                    PayablesOrderIds = new List<long> { message.OffsetOrderId },
                }, new List<long>());

            var offsetOrder = await query.FirstOrDefaultAsync();
            if (offsetOrder == null)
            {
                return;
            }

            //更新付款结算单数据
            settlementOrder.TotalAmount += offsetOrder.TotalAmount;
            settlementOrder.OrderCount += 1;

            //插入新的结算单明细
            var offsetSettlementDetail = new SettlementOrderDetail
            {
                SettlementOrderId = settlementOrder.Id,
                BaseOrderId = message.BaseOrderId,
                BusinessOrderId = message.OffsetOrderId,
                SupplierId = offsetOrder.SupplierId,
                SettlementBusinessType = SettlementBusinessType.OffsetOrder,
                Quantity = offsetOrder.Quantity,
                TotalAmount = offsetOrder.TotalAmount
            };
            await _dbContext.SettlementOrderDetails.AddAsync(offsetSettlementDetail);
        }

        private IEnumerable<SettlementOrderDate> GetSettlementOrderDates(long settlementOrderId, PreCreateInput input)
        {
            List<SettlementOrderDate> result = new()
            {
                new SettlementOrderDate
                {
                    SettlementOrderId = settlementOrderId,
                    SettlementBusinessType = SettlementBusinessType.HotelOrder,
                    OrderDateType = input.HotelOrderDateType
                },
                new SettlementOrderDate
                {
                    SettlementOrderId = settlementOrderId,
                    SettlementBusinessType = SettlementBusinessType.TicketOrder,
                    OrderDateType = input.TicketOrderDateType
                },
                new SettlementOrderDate
                {
                    SettlementOrderId = settlementOrderId,
                    SettlementBusinessType = SettlementBusinessType.MailOrder,
                    OrderDateType = input.MailOrderDateType
                },
                new SettlementOrderDate
                {
                    SettlementOrderId = settlementOrderId,
                    SettlementBusinessType = SettlementBusinessType.ReservationOrder,
                    OrderDateType = input.ReservationOrderDateType
                },
                new SettlementOrderDate
                {
                    SettlementOrderId = settlementOrderId,
                    SettlementBusinessType = SettlementBusinessType.RefundOrder,
                    OrderDateType = PayablesDateType.Refund
                },
                new SettlementOrderDate
                {
                    SettlementOrderId = settlementOrderId,
                    SettlementBusinessType = SettlementBusinessType.ScenicTicketOrder,
                    OrderDateType = input.ScenicTicketOrderDateType
                },
                new SettlementOrderDate
                {
                    SettlementOrderId = settlementOrderId,
                    SettlementBusinessType = SettlementBusinessType.LineOrder,
                    OrderDateType = input.LineOrderDateType
                },
                new SettlementOrderDate
                {
                    SettlementOrderId = settlementOrderId,
                    SettlementBusinessType = SettlementBusinessType.OffsetOrder,
                    OrderDateType = PayablesDateType.Create
                },
                new SettlementOrderDate
                {
                    SettlementOrderId = settlementOrderId,
                    SettlementBusinessType = SettlementBusinessType.CarProductOrder,
                    OrderDateType = input.CarProductOrderDateType
                }
            };
            return result;
        }

        #endregion

        #region 自动对账
        public async Task<AutoPreCreateSettlementPayablesOutput> AutoPreCreate(AutoSettlementPayablesInput input)
        {
            var excelDatas = GetAutoPreCreateData(input.File);

            if (excelDatas.Any() is false)
                return null;
            //去除重复采购单号数据
            excelDatas = excelDatas.GroupBy(x => x.SupplierOrderId).Select(x => new AutoSettlementPayablesExcelDto
            {
                SupplierOrderId = x.Key,
                TotalAmount = x.Sum(f => f.TotalAmount),
                CostTotalAmount = x.Sum(f => f.CostTotalAmount),
                CreateTime = x.FirstOrDefault().CreateTime
            }).ToList();

            var supplierOrderIds = excelDatas.Select(x => x.SupplierOrderId).ToList();
            if (supplierOrderIds.Any() is false)
                return null;
            //包含该采购单号的所有订单
            var autoPreCreateOrders = GetAutoPreCreateOrders(supplierOrderIds, input.SupplierId, input.Type);
            var baseOrderIds = autoPreCreateOrders.Select(x => x.BaseOrderId).Distinct().ToList();
            var searchInput = new PreCreateInput
            {
                SupplierIds = new[] { input.SupplierId },
                HotelOrderDateType = PayablesDateType.Create,
                LineOrderDateType = PayablesDateType.Create,
                ScenicTicketOrderDateType = PayablesDateType.Create,
                HotelOrderIds = autoPreCreateOrders
                    .Where(x => x.OrderType == OrderType.Hotel)
                    .Select(x => x.Id)
                    .ToList(),
                ScenicTicketOrderIds = autoPreCreateOrders
                    .Where(x => x.OrderType == OrderType.ScenicTicket)
                    .Select(x => x.Id)
                    .ToList(),
                LineOrderIds = autoPreCreateOrders
                    .Where(x => x.OrderType == OrderType.TravelLineOrder)
                    .Select(x => x.Id)
                    .ToList(),
                CarProductOrderIds = autoPreCreateOrders
                    .Where(x => x.OrderType == OrderType.CarProduct)
                    .Select(x => x.Id)
                    .ToList(),
            };

            //查询符合生成账单的订单和相关信息
            //酒店
            var hotelOrder = await PreHotelOrder(searchInput);
            //门票
            var scenicTicketOrder = await PreScenicTicketOrder(searchInput);
            //线路
            var lineOrder = await PreLineOrder(searchInput);
            //线路
            var carProductOrder = await PreCarProductOrder(searchInput);
            //查询账期内业务订单的主订单Id
            //var baseOrderIds = hotelOrder
            //    .Concat(scenicTicketOrder)
            //    .Concat(lineOrder)
            //    .Select(x => x.BaseOrderId).ToList();

            //清除查询条件
            searchInput.BusinessSearches = null;

            var relatedOrderInput = _mapper.Map<RelatedPayablesSearchInput>(searchInput);
            //查询关联的退款单
            var refundOrder = await PreRelatedRefundOrder(relatedOrderInput, baseOrderIds);
            //查询关联的抵冲单
            var offsetOrder = await PreRelatedOffsetOrder(relatedOrderInput, baseOrderIds);

            //汇总所有数据-未生成账单数据
            var allOrders = hotelOrder
                .Concat(scenicTicketOrder)
                .Concat(lineOrder)
                .Concat(carProductOrder)
                .Concat(refundOrder)
                .Concat(offsetOrder);

            var correctDetail = new AutoCreateSettlementPayablesOutput()
            {
                SupplierIds = new List<long> { input.SupplierId },
            };
            //处理数据
            foreach (var item in excelDatas)
            {
                var matchBaseOrderIds = autoPreCreateOrders.AsQueryable()
                    .WhereIF(input.Type == AutoSettlementPayableType.SupplierOrderId, x => x.SupplierOrderId == item.SupplierOrderId)
                    .WhereIF(input.Type == AutoSettlementPayableType.BaseOrderId, x => x.BaseOrderId.ToString() == item.SupplierOrderId)
                    .Select(x => x.BaseOrderId)
                    .ToList();
                var matchOrders = allOrders.Where(x => matchBaseOrderIds.Contains(x.BaseOrderId)).ToList();
                if (matchOrders.Any() is false)
                {
                    item.ErrorMsg = "无此单";
                    continue;
                }
                var relatedBaseOrderIds = matchOrders.Select(x => x.BaseOrderId).Distinct().ToList();

                item.CurrentTotalAmount = matchOrders.Sum(x => x.TotalAmount);
                item.BaseOrderIds = string.Join(",", relatedBaseOrderIds);
                item.OrderType = matchOrders.Where(x => x.OrderType.HasValue).Select(x => x.OrderType.Value).ToList();

                //应付货币与供应商货币不一致报异常
                var currencies = matchOrders.Select(x => x.CostCurrencyCode).Distinct().ToList();
                if (currencies.Any() is false
                    || currencies.Count() > 1
                    || (currencies.Count() == 1 && !currencies[0].Equals(input.SupplierCurrency)))
                    item.ErrorMsg = "订单货币与供应商货币不同";
                else
                {
                    //汇总金额
                    item.CurrentTotalAmount = matchOrders.Sum(x => x.TotalAmount);
                    if (item.CurrentTotalAmount != item.TotalAmount)
                        item.ErrorMsg = "结算金额不对 ";
                    item.CurrentCostTotalAmount = matchOrders
                        .Where(x=> x.CostTotalAmount.HasValue)
                        .Sum(x => x.CostTotalAmount);
                }

                if (item.IsError)
                    continue;

                //统计正确的数据，返回预生成的账单生成条件
                matchOrders.ForEach(o =>
                {
                    if (o.OrderType == OrderType.Hotel)
                        correctDetail.HotelOrderIds.Add(o.BusinessOrderId);
                    else if (o.OrderType == OrderType.ScenicTicket)
                        correctDetail.ScenicTicketOrderIds.Add(o.BusinessOrderId);
                    else if (o.OrderType == OrderType.TravelLineOrder)
                        correctDetail.LineOrderIds.Add(o.BusinessOrderId);
                    else if (o.OrderType == OrderType.CarProduct)
                        correctDetail.CarProductOrderIds.Add(o.BusinessOrderId);

                    var refundOrderIds = refundOrder.Where(f => relatedBaseOrderIds.Contains(f.BaseOrderId))
                        .Select(f => f.BusinessOrderId)
                        .ToList();
                    if (refundOrderIds.Any() is true)
                        correctDetail.RefundOrderIds.AddRange(refundOrderIds);

                    var offsetOrderIds = offsetOrder.Where(f => relatedBaseOrderIds.Contains(f.BaseOrderId))
                        .Select(f => f.BusinessOrderId)
                        .ToList();
                    if (offsetOrderIds.Any() is true)
                        correctDetail.OffsetOrderIds.AddRange(offsetOrderIds);
                });
            }

            var correctDataIds = correctDetail.HotelOrderIds
                .Concat(correctDetail.ScenicTicketOrderIds)
                .Concat(correctDetail.LineOrderIds)
                .Concat(correctDetail.CarProductOrderIds)
                .Concat(correctDetail.RefundOrderIds)
                .Concat(correctDetail.OffsetOrderIds)
                .ToList();
            var correctOrders = allOrders.Where(x => correctDataIds.Contains(x.BusinessOrderId)).ToList();
            if (correctOrders.Any() is true)
            {
                correctDetail.BillingCycleBegin = correctOrders?.Min(x => x.OrderSettlementDateTypeTime);
                correctDetail.BillingCycleEnd = correctOrders?.Max(x => x.OrderSettlementDateTypeTime);
            }

            var dataGroup = excelDatas.GroupBy(x => x.IsError);
            var errorDatas = dataGroup.FirstOrDefault(x => x.Key == true);
            var correctDatas = dataGroup.FirstOrDefault(x => x.Key == false);

            var result = new AutoPreCreateSettlementPayablesOutput();
            result.CorrectDetail = correctDetail;
            result.SummaryResults = new List<AutoSettlementPayablesSummaryResult>() {
                new AutoSettlementPayablesSummaryResult{
                    IsError = true,
                    Count = errorDatas == null ? 0 : errorDatas.Count(),
                    TotalAmount = errorDatas == null ? 0 : errorDatas.Sum(x => x.TotalAmount),
                },
                new AutoSettlementPayablesSummaryResult{
                    IsError = false,
                    Count = correctDatas == null ? 0 : correctDatas.Count(),
                    TotalAmount = correctDatas == null ? 0 : correctDatas.Sum(x => x.TotalAmount),
                }
            };
            result.ErrorDetails = errorDatas?.ToList();
            result.Count = correctDatas == null ? 0 : correctDatas.Count();
            result.TotalAmount = correctDatas == null ? 0 : correctDatas.Sum(x => x.TotalAmount);
            result.SupplierId = input.SupplierId;

            return result;
        }

        private List<AutoPreCreateSettlementPayableOrder> GetAutoPreCreateOrders(List<string> supplierOrderIds, long supplierId, AutoSettlementPayableType type)
        {
            var hotleQuery = _dbContext.HotelOrders
                .Join(_dbContext.BaseOrders, s => s.BaseOrderId, b => b.Id, (hotelOrder, baseOrder) => new { hotelOrder, baseOrder })
                .WhereIF(type == AutoSettlementPayableType.BaseOrderId, x => supplierOrderIds.Contains(x.hotelOrder.BaseOrderId.ToString()))
                .WhereIF(type == AutoSettlementPayableType.SupplierOrderId, x => supplierOrderIds.Contains(x.hotelOrder.SupplierOrderId))
                .Where(x => x.hotelOrder.Status != HotelOrderStatus.Closed
                    && x.hotelOrder.Status != HotelOrderStatus.WaitingForPay
                    && x.hotelOrder.PriceStrategySupplierId == supplierId
                    && x.hotelOrder.IsGroupBooking == false)
                .Select(x => new AutoPreCreateSettlementPayableOrder
                {
                    Id = x.hotelOrder.Id,
                    BaseOrderId = x.hotelOrder.BaseOrderId,
                    SupplierOrderId = x.hotelOrder.SupplierOrderId,
                    OrderType = OrderType.Hotel,
                    CreateTime = x.baseOrder.CreateTime,
                }).ToList();
            var scenicTicketOrders = _dbContext.ScenicTicketOrders
                .Join(_dbContext.BaseOrders, s => s.BaseOrderId, b => b.Id, (scenicTicketOrder, baseOrder) => new { scenicTicketOrder, baseOrder })
                .WhereIF(type == AutoSettlementPayableType.BaseOrderId, x => supplierOrderIds.Contains(x.scenicTicketOrder.BaseOrderId.ToString()))
                .WhereIF(type == AutoSettlementPayableType.SupplierOrderId, x => supplierOrderIds.Contains(x.scenicTicketOrder.SupplierOrderId))
                .Where(x => x.baseOrder.PaymentType != PayType.None && x.scenicTicketOrder.SupplierId == supplierId)
                .Select(x => new AutoPreCreateSettlementPayableOrder
                {
                    Id = x.scenicTicketOrder.Id,
                    BaseOrderId = x.scenicTicketOrder.BaseOrderId,
                    SupplierOrderId = x.scenicTicketOrder.SupplierOrderId,
                    OrderType = OrderType.ScenicTicket,
                    CreateTime = x.baseOrder.CreateTime,
                }).ToList();
            var travelLineOrderQuery = _dbContext.TravelLineOrder
                .WhereIF(type == AutoSettlementPayableType.BaseOrderId, x => supplierOrderIds.Contains(x.BaseOrderId.ToString()))
                .WhereIF(type == AutoSettlementPayableType.SupplierOrderId, x => supplierOrderIds.Contains(x.SupplierOrderId))
                .Where(x => x.Status != TravelLineOrderStatus.Canceled
                    && x.Status != TravelLineOrderStatus.WaitingForPay
                    && x.SupplierId == supplierId)
                .Select(x => new AutoPreCreateSettlementPayableOrder
                {
                    Id = x.Id,
                    BaseOrderId = x.BaseOrderId,
                    SupplierOrderId = x.SupplierOrderId,
                    OrderType = OrderType.TravelLineOrder,
                    CreateTime = x.CreateTime,
                }).ToList();
            var carProductOrderQuery = _dbContext.CarProductOrders
                .WhereIF(type == AutoSettlementPayableType.BaseOrderId, x => supplierOrderIds.Contains(x.BaseOrderId.ToString()))
                .WhereIF(type == AutoSettlementPayableType.SupplierOrderId, x => supplierOrderIds.Contains(x.SupplierOrderId))
                .Where(x => x.Status != CarProductOrderStatus.WaitingForPay
                    && x.Status != CarProductOrderStatus.Closed
                    && x.SupplierId == supplierId)
                .Select(x => new AutoPreCreateSettlementPayableOrder
                {
                    Id = x.Id,
                    BaseOrderId = x.BaseOrderId,
                    SupplierOrderId = x.SupplierOrderId,
                    OrderType = OrderType.CarProduct,
                    CreateTime = x.CreateTime,
                }).ToList();

            //全部关联到的订单，具体筛选条件由具体的查询接口过滤
            var baseOrders = hotleQuery
                .Union(travelLineOrderQuery)
                .Union(scenicTicketOrders)
                .Union(carProductOrderQuery);

            return baseOrders.ToList();
        }


        /// <summary>
        /// 获取自动对账excel数据
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        private List<AutoSettlementPayablesExcelDto> GetAutoPreCreateData(byte[] fileBytes)
        {
            using var memoryStream = new System.IO.MemoryStream(fileBytes);

            try
            {
                var workbook = WorkbookFactory.Create(memoryStream);
                var worksheet = workbook.GetSheetAt(0);
                if (worksheet == null)
                    return null;

                var result = new List<AutoSettlementPayablesExcelDto>();
                var columnCount = 3;
                for (int rowIndex = 1; rowIndex <= worksheet.LastRowNum; rowIndex++)
                {
                    var obj = new AutoSettlementPayablesExcelDto();
                    var row = worksheet.GetRow(rowIndex);
                    if (row == null) continue; // 跳过空行

                    var createTimeCellValue = GetCellValue(row.GetCell(0));
                    obj.CreateTime = createTimeCellValue == null ? null : Convert.ToDateTime(createTimeCellValue);
                    var SupplierOrderIdCellValue = GetCellValue(row.GetCell(1));
                    obj.SupplierOrderId = SupplierOrderIdCellValue?.ToString().Trim();
                    var totalAmountCellValue = GetCellValue(row.GetCell(2));
                    obj.TotalAmount = totalAmountCellValue == null ? 0 : Convert.ToDecimal(totalAmountCellValue);

                    if (string.IsNullOrEmpty(obj.SupplierOrderId))
                        continue;

                    result.Add(obj);
                }

                return result;
            }
            catch (Exception e)
            {
                throw new BusinessException(ErrorTypes.Order.AutoReconciliationExcelError);
            }
        }

        private object GetCellValue(ICell cell)
        {
            if (cell == null)
                return null;

            switch (cell.CellType)
            {
                case CellType.Numeric:
                    if (DateUtil.IsCellDateFormatted(cell))
                        return cell.DateCellValue;
                    return cell.NumericCellValue;
                case CellType.String:
                    return cell.StringCellValue;
                case CellType.Boolean:
                    return cell.BooleanCellValue;
                case CellType.Formula:
                    try
                    {
                        return cell.NumericCellValue;
                    }
                    catch (Exception)
                    {
                        return cell.StringCellValue;
                    }
                default:
                    return null;
            }
        }
        #endregion

        public async Task<PayablesOrderExportOutput> Export(PayablesOrderExportInput input)
        {
            var result = new PayablesOrderExportOutput();
            result.HotelOrder = await HotelQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.HotelOrderDateType,
                PayablesBaseOrderIds = input.BusinessSearches?
                    .Select(x => x.PayablesBaseOrderId)
            })
            .OrderByDescending(x => x.OrderId)
            .ToListAsync();
            if (result.HotelOrder.Any())
            {
                result.HotelOrder = await HotelOrderStuff(result.HotelOrder);
            }

            result.MailOrder = await MailQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.MailOrderDateType,
                PayablesBaseOrderIds = input.BusinessSearches?
                    .Select(x => x.PayablesBaseOrderId)
            })
            .OrderByDescending(x => x.OrderId)
            .ToListAsync();
            if (result.MailOrder.Any())
            {
                result.MailOrder = await MailOrderStuff(result.MailOrder);
            }

            result.TicketOrder = await TicketQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.TicketOrderDateType,
                PayablesBaseOrderIds = input.BusinessSearches?
                    .Select(x => x.PayablesBaseOrderId)
            })
            .OrderByDescending(x => x.OrderId)
            .ToListAsync();
            if (result.TicketOrder.Any())
            {
                result.TicketOrder = await TicketOrderStuff(result.TicketOrder);
            }

            result.ReservationOrder = await ReservationQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.ReservationOrderDateType,
                PayablesBaseOrderIds = input.BusinessSearches?
                    .Select(x => x.PayablesBaseOrderId)
            })
            .OrderByDescending(x => x.OrderId)
            .ToListAsync();
            if (result.ReservationOrder.Any())
            {
                result.ReservationOrder = await ReservationOrderStuff(result.ReservationOrder);
            }

            result.ScenicOrder = await ScenicTicketQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.ScenicTicketOrderDateType,
                PayablesBaseOrderIds = input.BusinessSearches?
                    .Select(x => x.PayablesBaseOrderId)
            })
            .OrderByDescending(x => x.OrderId)
            .ToListAsync();
            if (result.ScenicOrder.Any())
            {
                result.ScenicOrder = await ScenicTicketOrderStuff(result.ScenicOrder);
            }

            result.LineOrder = await LineOrderQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.LineOrderDateType,
                PayablesBaseOrderIds = input.BusinessSearches?
                    .Select(x => x.PayablesBaseOrderId)
            })
            .OrderByDescending(x => x.OrderId)
            .ToListAsync();
            if (result.LineOrder.Any())
            {
                result.LineOrder = await LineOrderStuff(result.LineOrder);
            }

            result.CarProductOrder = await CarProductOrderQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.LineOrderDateType,
                PayablesBaseOrderIds = input.BusinessSearches?
                    .Select(x => x.PayablesBaseOrderId)
            })
            .OrderByDescending(x => x.OrderId)
            .ToListAsync();
            if (result.CarProductOrder.Any())
            {
                result.CarProductOrder = await CarProductOrderStuff(result.CarProductOrder);
            }

            result.RefundOrder = await RefundQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = PayablesDateType.Refund,
                PayablesBaseOrderIds = input.BusinessSearches?
                    .Select(x => x.PayablesBaseOrderId)
            })
            .OrderByDescending(x => x.OrderId)
            .ToListAsync();
            result.OffsetOrder = await OffsetOrderQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                PayablesBaseOrderIds = input.BusinessSearches?
                    .Select(x => x.PayablesBaseOrderId)
            }).ToListAsync();

            if (input is { BillingCycleBegin: not null, BillingCycleEnd: not null })
            {
                var baseOrderIds = result.HotelOrder.Select(x => x.BaseOrderId)
                    .Concat(result.MailOrder.Select(x => x.BaseOrderId))
                    .Concat(result.TicketOrder.Select(x => x.BaseOrderId))
                    .Concat(result.ReservationOrder.Select(x => x.BaseOrderId))
                    .Concat(result.ScenicOrder.Select(x => x.BaseOrderId))
                    .Concat(result.LineOrder.Select(x => x.BaseOrderId))
                    .Concat(result.CarProductOrder.Select(x => x.BaseOrderId))
                    .ToList();
                if (baseOrderIds.Any())
                {
                    var relatedInput = new RelatedPayablesSearchInput { SupplierIds = input.SupplierIds };
                    var relatedRefundOrderData = await RelatedRefundOrderQuery(relatedInput, baseOrderIds).ToListAsync();
                    result.RefundOrder.AddRange(relatedRefundOrderData);
                    result.RefundOrder = result.RefundOrder.OrderByDescending(x => x.OrderId).ToList();
                    var relatedOffsetOrderData = await RelatedOffsetOrderQuery(relatedInput, baseOrderIds).ToListAsync();
                    result.OffsetOrder.AddRange(relatedOffsetOrderData);
                    result.OffsetOrder = result.OffsetOrder.OrderByDescending(x => x.OrderId).ToList();
                }
            }

            return result;
        }

        /// <summary>
        /// 查询应付款订单的金额(暂时不包括预约单)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<GetPayableOrderAmountOutput>> GetPayableOrderAmount(GetPayableOrderAmountInput input)
        {
            var result = new List<GetPayableOrderAmountOutput>();
            var subOrderIds = new List<long>();
            result = input.OrderType switch
            {
                OrderType.Ticket => await _dbContext.TicketOrders.AsNoTracking()
                    .Where(x => x.BaseOrderId == input.BaseOrderId)
                    .Select(x => new GetPayableOrderAmountOutput
                    {
                        SubOrderId = x.Id,
                        SupplierIid = x.ProductSupplierId
                    })
                    .ToListAsync(),
                OrderType.Mail => await _dbContext.MailOrders.AsNoTracking()
                    .Where(x => x.BaseOrderId == input.BaseOrderId)
                    .Select(x => new GetPayableOrderAmountOutput
                    {
                        SubOrderId = x.Id,
                        SupplierIid = x.ProductSupplierId
                    })
                    .ToListAsync(),
                OrderType.ScenicTicket => await _dbContext.ScenicTicketOrders.AsNoTracking()
                    .Where(x => x.BaseOrderId == input.BaseOrderId)
                    .Select(x => new GetPayableOrderAmountOutput
                    {
                        SubOrderId = x.Id,
                        SupplierIid = x.SupplierId
                    })
                    .ToListAsync(),
                OrderType.TravelLineOrder => await _dbContext.TravelLineOrder.AsNoTracking()
                    .Where(x => x.BaseOrderId == input.BaseOrderId)
                    .Select(x => new GetPayableOrderAmountOutput
                    {
                        SubOrderId = x.Id,
                        SupplierIid = x.SupplierId
                    })
                    .ToListAsync(),
                OrderType.CarHailing => await _dbContext.CarHailingOrders.AsNoTracking()
                    .Where(x => x.BaseOrderId == input.BaseOrderId)
                    .Select(x => new GetPayableOrderAmountOutput
                    {
                        SubOrderId = x.Id,
                        SupplierIid = x.SupplierId
                    })
                    .ToListAsync(),
                OrderType.CarProduct => await _dbContext.CarProductOrders.AsNoTracking()
                    .Where(x => x.BaseOrderId == input.BaseOrderId)
                    .Select(x => new GetPayableOrderAmountOutput
                    {
                        SubOrderId = x.Id,
                        SupplierIid = x.SupplierId
                    })
                    .ToListAsync(),
                _ => result
            };

            subOrderIds = result.Select(x => x.SubOrderId).ToList();
            if (input.OrderType == OrderType.Hotel)
            {
                var hotelOrders = await _dbContext.HotelOrders.AsNoTracking()
                    .Where(x => x.BaseOrderId == input.BaseOrderId)
                    .Select(x => new PayablesHotelOrderInfo
                    {
                        OrderId = x.Id,
                        CheckInDate = x.CheckInDate,
                        CheckOutDate = x.CheckOutDate,
                        Quantity = x.PriceStrategyRoomsCount,
                        SupplierId = x.PriceStrategySupplierId
                    })
                    .ToListAsync();

                var hotelPrices = await GetHotelOrderCostPrices(hotelOrders);
                result.AddRange(from item in hotelPrices
                                let hotelOrder = hotelOrders.First(x => x.OrderId == item.orderId)
                                select new GetPayableOrderAmountOutput
                                {
                                    SubOrderId = item.orderId,
                                    Total = item.totalCostPrice,
                                    CostCurrencyCode = item.costCurrencyCode,
                                    SupplierIid = hotelOrder.SupplierId,
                                    Quantity = hotelOrder.Quantity
                                });
            }
            else
            {
                var prices = GetPriceAndQuantity(subOrderIds);
                foreach (var item in result)
                {
                    var price = prices.First(x => x.subOrderId == item.SubOrderId);
                    item.Quantity = price.quantity;
                    item.CostPrice = price.cost;
                    item.CostCurrencyCode = price.costCurrencyCode;
                    item.Total = price.total;
                }
            }

            return result;
        }

        /// <summary>
        /// 查询账期内的baseOrderId列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<List<long>> GetBaseOrderIds(RelatedPayablesSearchInput input)
        {
            var baseOrderIds = new List<long>();

            #region 获取账期内的其他订单数据

            var hotelOrderBaseOrderIds = await HotelQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.HotelOrderDateType,
                PayablesBaseOrderIds = input.BusinessSearches?
                        .Select(x => x.PayablesBaseOrderId)
            })
                .Select(x => x.BaseOrderId)
                .ToListAsync();

            var ticketOrderBaseOrderIds = await TicketQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.TicketOrderDateType,
                PayablesBaseOrderIds = input.BusinessSearches?
                        .Select(x => x.PayablesBaseOrderId)
            })
                .Select(x => x.BaseOrderId)
                .ToListAsync();

            var reservationOrderBaseOrderIds = await ReservationQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.ReservationOrderDateType,
                PayablesBaseOrderIds = input.BusinessSearches?
                        .Select(x => x.PayablesBaseOrderId)
            })
                .Select(x => x.BaseOrderId)
                .ToListAsync();

            var scenicTicketBaseOrderIds = await ScenicTicketQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.ScenicTicketOrderDateType,
                PayablesBaseOrderIds = input.BusinessSearches?
                        .Select(x => x.PayablesBaseOrderId)
            })
                .Select(x => x.BaseOrderId)
                .ToListAsync();

            var lineOrderBaseOrderIds = await LineOrderQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.LineOrderDateType,
                PayablesBaseOrderIds = input.BusinessSearches?
                        .Select(x => x.PayablesBaseOrderId)
            })
                .Select(x => x.BaseOrderId)
                .ToListAsync();

            var carProductOrderIds = await CarProductOrderQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.LineOrderDateType,
                PayablesBaseOrderIds = input.BusinessSearches?
                    .Select(x => x.PayablesBaseOrderId)
            })
            .Select(x => x.BaseOrderId)
            .ToListAsync();

            var mailOrderBaseOrderIds = await MailQuery(new PayablesSearchInput
            {
                SupplierIds = input.SupplierIds,
                BillingCycleBegin = input.BillingCycleBegin,
                BillingCycleEnd = input.BillingCycleEnd,
                SettlementDateType = input.MailOrderDateType,
                PayablesBaseOrderIds = input.BusinessSearches?
                        .Select(x => x.PayablesBaseOrderId)
            })
                .Select(x => x.BaseOrderId)
                .ToListAsync();

            baseOrderIds.AddRange(hotelOrderBaseOrderIds);
            baseOrderIds.AddRange(ticketOrderBaseOrderIds);
            baseOrderIds.AddRange(reservationOrderBaseOrderIds);
            baseOrderIds.AddRange(scenicTicketBaseOrderIds);
            baseOrderIds.AddRange(lineOrderBaseOrderIds);
            baseOrderIds.AddRange(carProductOrderIds);
            baseOrderIds.AddRange(mailOrderBaseOrderIds);
            baseOrderIds = baseOrderIds.Distinct().ToList();

            #endregion

            return baseOrderIds;
        }

        private async Task<IEnumerable<Contracts.Common.Tenant.DTOs.Supplier.GetSupplierOutput>> TenantSupplierGetByIds(List<long> supplierIds)
        {
            var httpContent = new StringContent(JsonConvert.SerializeObject(supplierIds),
                Encoding.UTF8, "application/json");
            var response = await _httpClientFactory.InternalPostAsync<IEnumerable<Contracts.Common.Tenant.DTOs.Supplier.GetSupplierOutput>>(
                requestUri: _servicesAddress.Tenant_Supplier_GetByIds(),
                httpContent: httpContent);
            return response;
        }
    }
}
