using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 团房单
/// </summary>
public class GroupBookingOrder : TenantBase
{
    /// <summary>
    /// 关联预订单id
    /// </summary>
    public long GroupBookingPreOrderId { get; set; }

    /// <summary>
    /// 关联团房申请单id
    /// </summary>
    public long GroupBookingApplicationFormId { get; set; }

    #region 订单用户信息

    /// <summary>
    /// 下单用户Id
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// 下单用户昵称
    /// </summary>
    public string? UserNickName { get; set; }

    /// <summary>
    /// 会员等级id
    /// </summary>
    public long VipLevelId { get; set; }

    /// <summary>
    /// 会员等级
    /// </summary>
    public string? VipLevelName { get; set; }

    /// <summary>
    /// 联系人
    /// </summary>
    public string ContactsName { get; set; }

    /// <summary>
    /// 联系人手机号
    /// </summary>
    public string ContactsPhoneNumber { get; set; }

    /// <summary>
    /// 联系人邮箱
    /// </summary>
    public string? ContactsEmail { get; set; }

    /// <summary>
    /// 分销商id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 分销商
    /// </summary>
    public string? AgencyName { get; set; }

    /// <summary>
    /// 销售BD
    /// </summary>
    public long? SalespersonId { get; set; }

    /// <summary>
    /// 销售BD
    /// </summary>
    public string? SalespersonName { get; set; }

    #endregion

    /// <summary>
    /// 售卖平台
    /// </summary>
    public SellingPlatform SellingPlatform { get; set; }

    /// <summary>
    /// 售卖渠道
    /// </summary>
    public SellingChannels SellingChannels { get; set; }

    /// <summary>
    /// 售卖渠道单号
    /// </summary>
    public string? ChannelOrderNo { get; set; }

    /// <summary>
    /// 团号 可选项
    /// </summary>
    public string? GroupNo { get; set; }

    #region Obsolete

    [Obsolete]
    public int CityCode { get; set; }
    [Obsolete]
    public string CityName { get; set; }=string.Empty;

    /// <summary>
    /// 资源酒店id
    /// </summary>
    [Obsolete]
    public long ResourceHotelId { get; set; }
    [Obsolete]
    public string HotelZHName { get; set; } = string.Empty;
    [Obsolete]
    public string HotelENName { get; set; }=string.Empty;

    /// <summary>
    /// 入住日期
    /// </summary>
    [Obsolete]
    public DateTime CheckInDate { get; set; }

    /// <summary>
    /// 离店日期
    /// </summary>
    [Obsolete]
    public DateTime CheckOutDate { get; set; }

    #endregion

    /// <summary>
    /// 留言
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 总付款金额
    /// </summary>
    public decimal TotalPayment { get; set; }

    /// <summary>
    /// 付款币种
    /// </summary>
    public string PaymentCurrencyCode { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; }

    public HotelOrderStatus HotelOrderStatus { get; set; }

    /// <summary>
    /// 入住人信息excel文件路径
    /// </summary>
    public string? HotelOrderGuestFilePath { get; set; }
}

/// <summary>
/// 团房单支付 首款 尾款
/// </summary>
public class GroupBookingOrderPayment : TenantBase
{
    /// <summary>
    /// 团房单id
    /// </summary>
    public long GroupBookingOrderId { get; set; }

    /// <summary>
    /// 最晚付款时间
    /// </summary>
    public DateTime LatestPaymentTime { get; set; }

    /// <summary>
    /// 支付比例类型 1-首款 2-尾款
    /// </summary>
    public PaymentRatioType PaymentRatioType { get; set; }

    /// <summary>
    /// 支付比例
    /// </summary>
    public decimal PaymentRatio { get; set; }

    /// <summary>
    /// 付款金额
    /// </summary>
    public decimal PaymentAmount { get; set; }

    /// <summary>
    /// 付款币种
    /// </summary>
    public string PaymentCurrencyCode { get; set; }

    /// <summary>
    /// 支付方式
    /// </summary>
    public PayType? PayType { get; set; }

    /// <summary>
    /// 支付时间
    /// </summary>
    public DateTime? PaymentTime { get; set; }

    /// <summary>
    /// 支付状态
    /// </summary>
    public PayStatus PayStatus { get; set; }

    /// <summary>
    /// 外部支付单号 流水号
    /// </summary>
    public string PaymentExternalNo { get; set; } = string.Empty;

    /// <summary>
    /// 团房收款处理状态 线下转账有值
    /// </summary>
    public GroupBookingFinancialHandleOrderStatus? HandleOrderStatus { get; set; }
}

/// <summary>
/// 团房单项
/// </summary>
public class GroupBookingOrderItem : TenantBase
{
    /// <summary>
    /// 团房单id
    /// </summary>
    public long GroupBookingOrderId { get; set; }

    /// <summary>
    /// 预订单订单项id
    /// </summary>
    public long GroupBookingPreOrderItemId { get; set; }

    /// <summary>
    /// 主单id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 酒店单id
    /// </summary>
    public long HotelOrderId { get; set; }

    public long? SupplierId { get; set; }
    public SupplierApiType SupplierApiType { get; set; }
    public long HotelId { get; set; }

    public int CityCode { get; set; }

    public string CityName { get; set; }
    public long ResourceHotelId { get; set; }
    public string HotelZHName { get; set; }
    public string HotelENName { get; set; }

    public DateTime CheckInDate { get; set; }

    public DateTime CheckOutDate { get; set; }

    public long HotelRoomId { get; set; }
    public string? HotelRoomName { get; set; }
    public string? HotelRoomEnName { get; set; }
    public string? BedType { get; set; }
    public int MaximumOccupancy { get; set; }

    public string PriceStrategyId { get; set; }
    public string? PriceStrategyName { get; set; }
    public string? PriceStrategyEnName { get; set; }

    public int RoomCount { get; set; }
    public int NumberOfBreakfast { get; set; }

    public HotelOrderStatus HotelOrderStatus { get; set; }

    /// <summary>
    /// 入住人信息excel文件路径
    /// </summary>
    public string? GuestFilePath { get; set; }
}

public class GroupBookingOrderAddition : TenantBase
{
    /// <summary>
    /// 团房单id
    /// </summary>
    public long GroupBookingOrderId { get; set; }

    /// <summary>
    /// 关联团房单项id
    /// </summary>
    public long GroupBookingOrderItemId { get; set; }

    /// <summary>
    /// 关联主单id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 供应商Id
    /// </summary>
    public long? SupplierId { get; set; }

    /// <summary>
    /// 费用名称
    /// </summary>
    public string AdditionName { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal Amount { get; set; }

    public string CurrencyCode { get; set; }

    /// <summary>
    /// 采购价
    /// </summary>
    public decimal Cost { get; set; }

    /// <summary>
    /// 采购币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string Remark { get; set; }
}   

