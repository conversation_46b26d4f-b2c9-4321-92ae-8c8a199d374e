using Cit.Storage.Redis;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.Invoice;
using Contracts.Common.Order.DTOs.OffsetOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using Order.Api.Extensions;
using Order.Api.Services.Interfaces;

namespace Order.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class OffsetOrderController : ControllerBase
{
    private readonly IOffsetOrderService _offsetOrderService;
    private readonly IRedisClient _redisClient;

    public OffsetOrderController(
        IOffsetOrderService offsetOrderService,
        IRedisClient redisClient)
    {
        _offsetOrderService = offsetOrderService;
        _redisClient = redisClient;
    }

    [HttpPost]
    [ProducesResponseType(typeof(long), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation,
        ErrorTypes.Order.PayableOffsetOrderNeedAgencyProduct,
        ErrorTypes.Order.ReceiptOffsetOrderRefundOutoffAmount,
        ErrorTypes.Order.ReceiptOffsetOrderCannotCompensated,
        ErrorTypes.Order.PayableOffsetOrderRefundOutoffAmount,
        ErrorTypes.Order.PayableOffsetOrderCannotCompensated,
        ErrorTypes.Order.OffsetOrderNeedConfirmed)]
    public async Task<IActionResult> Add(AddOffsetOrderInput input)
    {
        var lockName = string.Format(Extensions.RedisLockName.BaseOrderKey, input.BaseOrderId);
        var lockSecret = Guid.NewGuid().ToString();
        try
        {
            await _redisClient.LockTakeWaitingAsync(lockName, lockSecret, TimeSpan.FromSeconds(10));
            var result = await _offsetOrderService.Add(input);
            return Ok(result);
        }
        finally
        {
            await _redisClient.LockReleaseAsync(lockName, lockSecret);
        }
    }
    [Obsolete]
    [HttpPost]
    [SwaggerResponseExt(default, ErrorTypes.Order.OffsetOrderInSettlement)]
    public async Task<IActionResult> Edit(EditOffsetOrderInput input)
    {
        /*1、抵冲单不可删除，不可编辑*/
        // var currentUser = HttpContext.GetCurrentUser();
        // OperationUserDto operationUser = new()
        // {
        //     UserType = UserType.Merchant,
        //     UserId = currentUser.userid,
        //     Name = currentUser.nickname
        // };
        // await _offsetOrderService.Edit(input,operationUser,currentUser.tenant!.Value);
        return Ok();
    }

    [Obsolete]
    [HttpPost]
    [SwaggerResponseExt(default, ErrorTypes.Order.OffsetOrderInSettlement)]
    public async Task<IActionResult> Delete(long id)
    {
        /*1、抵冲单不可删除，不可编辑*/
        // var tenantId = HttpContext.GetTenantId();
        // await _offsetOrderService.Delete(id,tenantId);
        return Ok();
    }

    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchOffsetOrderOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchOffsetOrderInput input)
    {
        var result = await _offsetOrderService.Search(input);
        return Ok(result);
    }
    
    [HttpPost]
    [ProducesResponseType(typeof(IEnumerable<SearchOffsetOrderOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> ExportSearch(SearchOffsetOrderInput input)
    {
        var result = await _offsetOrderService.ExportSearch(input);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(IEnumerable<GetOffsetOrderListOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetList(GetOffsetOrderListInput input)
    {
        var result = await _offsetOrderService.GetList(input);
        return Ok(result);
    }

    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<GetOffsetOrderDetailOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Detail(long id)
    {
        var result = await _offsetOrderService.Detail(id);
        return Ok(result);
    }

    #region CapSubscribe

    [NonAction]
    [CapSubscribe(CapTopics.Order.OffsetOrderRefundResult)]
    public async Task RefundResult(RefundResultMessage receive)
    {
        await _offsetOrderService.RefundResult(receive);
    }

    /// <summary>
    /// 钉钉审批结果回调
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    [NonAction]
    [CapSubscribe(CapTopics.Order.SyncOffsetOrderDingtalk)]
    public async Task SyncOffsetOrderDingtalk(SyncOffsetOrderDingtalkMessage receive)
    {
        await _offsetOrderService.SyncOffsetOrderDingtalk(receive);
    }
    #endregion
}