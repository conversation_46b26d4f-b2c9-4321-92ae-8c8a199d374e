using Common.Swagger;
using Contracts.Common.Order.DTOs.GroupBookingFinancialHandleOrder;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Order.Api.Services.Interfaces;

namespace Order.Api.Controllers;

/// <summary>
/// 团房收款审核处理
/// </summary>
[Route("[controller]/[action]")]
[ApiController]
public class GroupBookingFinancialHandleOrderController : ControllerBase
{

    private readonly IGroupBookingFinancialHandleOrderService _groupBookingFinancialHandleOrderService;

    public GroupBookingFinancialHandleOrderController(IGroupBookingFinancialHandleOrderService groupBookingFinancialHandleOrderService)
    {
        _groupBookingFinancialHandleOrderService = groupBookingFinancialHandleOrderService;
    }

    [HttpPost]
    public async Task<long> Create(FinancialHandleOrderCreateInput input)
    {
        var result = await _groupBookingFinancialHandleOrderService.Create(input);
        return result;
    }

    [HttpPost]
    public async Task<PagingModel<FinancialHandleOrderSearchOutput>> Search(FinancialHandleOrderSearchInput input)
    {
        var result = await _groupBookingFinancialHandleOrderService.Search(input);
        return result;
    }

    [HttpPost]
    public async Task<FinancialHandleOrderDetailOutput> Detail(FinancialHandleOrderDetailInput input)
    {
        var result = await _groupBookingFinancialHandleOrderService.Detail(input);
        return result;
    }

    [HttpPost]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Handle(FinancialHandleOrderHandleInput input)
    {
        await _groupBookingFinancialHandleOrderService.Handle(input);
        return Ok();
    }

}
