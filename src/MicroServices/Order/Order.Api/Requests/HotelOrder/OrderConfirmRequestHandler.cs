using AutoMapper;
using Common.GlobalException;
using Contracts.Common.Order.DTOs.MessageNotify;
using Contracts.Common.Order.Enums;
using DotNetCore.CAP;
using EfCoreExtensions.Extensions;
using Hangfire;
using HangfireClient.Jobs.Order;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Order.Api.Notification;
using Order.Api.Services.Interfaces;

namespace Order.Api.Requests.HotelOrder;

public class OrderConfirmRequestHandler : IRequestHandler<OrderConfirmRequest>
{
    private readonly CustomDbContext _dbContext;
    private readonly IMediator _mediator;
    private readonly IMapper _mapper;
    private readonly IMessageNotifyService _messageNotifyService;
    private readonly IBackgroundJobClient _backgroundJobClient;

    public OrderConfirmRequestHandler(CustomDbContext dbContext,
        IMediator mediator,
        IMapper mapper,
        IMessageNotifyService messageNotifyService,
        IBackgroundJobClient backgroundJobClient)
    {
        _dbContext = dbContext;
        _mediator = mediator;
        _mapper = mapper;
        _messageNotifyService = messageNotifyService;
        _backgroundJobClient = backgroundJobClient;
    }

    public async Task<Unit> Handle(OrderConfirmRequest request, CancellationToken cancellationToken)
    {
        var operationUser = request.OperationUser;

        var order = await _dbContext.HotelOrders
            .Join(_dbContext.BaseOrders, h => h.BaseOrderId, b => b.Id,
                (h, b) => new { hotelOrder = h, BaseOrder = b })
            .IgnoreQueryFilters()
            .Where(x => x.hotelOrder.Id == request.HotelOrderId && x.hotelOrder.TenantId == request.TenantId)
            .WhereIF(request.SupplierId.HasValue,
                x => x.hotelOrder.PriceStrategySupplierId == request.SupplierId!.Value)
            .FirstOrDefaultAsync();
        if (order is null)
            throw new BusinessException(ErrorTypes.Order.OrderNotFind);

        var tenantId = order.BaseOrder.TenantId;
        var hotelOrder = order.hotelOrder;
        var baseOrder = order.BaseOrder;
        if (!string.IsNullOrWhiteSpace(request.ConfirmCode))
            hotelOrder.ConfirmCode = request.ConfirmCode;
        var statusChanged = hotelOrder.Status != HotelOrderStatus.WaitingForCheckIn;
        hotelOrder.Status = HotelOrderStatus.WaitingForCheckIn;
        hotelOrder.ConfirmTime = DateTime.Now;
        hotelOrder.UpdateTime = DateTime.Now;

        var guests = await _dbContext.HotelOrderGuests.IgnoreQueryFilters().AsNoTracking()
            .Where(x => x.HotelOrderId == hotelOrder.Id && x.TenantId == request.TenantId)
            .Select(x => x.GuestName)
            .ToListAsync();

        var logs = new OrderLogs
        {
            OperationType = OrderOperationType.Confirmed,
            OrderLogType = OrderLogType.Hotel,
            OrderId = baseOrder.Id,
            UserId = operationUser.UserId,
            UserName = operationUser.Name,
            OperationRole = operationUser.UserType
        };
        await _dbContext.AddAsync(logs);
        //团房
        if (hotelOrder.IsGroupBooking)
        {
            await _mediator.Send(new GroupBookingOrderStatusRequest
            {
                BaseOrderId = hotelOrder.BaseOrderId,
                HotelOrderStatus = hotelOrder.Status
            });
        }

        OrderNotifyDto<HotelOrderConfirmNotifyDto> orderNotifyDto = new()
        {
            BaseOrder = _mapper.Map<BaseOrderNotify>(baseOrder),
            NotifyDto = new HotelOrderConfirmNotifyDto
            {
                HotelName = baseOrder.ResourceName,
                HotelRoomName = baseOrder.ProductName,
                SkuName = baseOrder.ProductSkuName,
                CheckInDate = hotelOrder.CheckInDate,
                CheckOutDate = hotelOrder.CheckOutDate,
                NightsCount = hotelOrder.PriceStrategyNightsCount,
                NumberOfBreakfast = hotelOrder.PriceStrategyNumberOfBreakfast,
                RoomsCount = hotelOrder.PriceStrategyRoomsCount,
                Guests = guests?.ToArray() ?? Array.Empty<string>(),
                IsGroupBooking = hotelOrder.IsGroupBooking,
                ConfirmCode = hotelOrder.ConfirmCode,
                BoardCodeType = hotelOrder.BoardCodeType,
                BoardCount = hotelOrder.BoardCount
            }
        };
        if (statusChanged && hotelOrder.Status == HotelOrderStatus.WaitingForCheckIn)
        {
            await _messageNotifyService.HotelOrderConfirmNotify(orderNotifyDto);
        }
        //自动入住
        if (hotelOrder.HotelIsAutoConfirmRoomStatus)
        {
            //入住日22:00后自动变为已入住
            var checkInTime = hotelOrder.CheckInDate.Date.AddHours(22);
            var ts = checkInTime.Subtract(DateTime.Now);
            _backgroundJobClient.Schedule<IHotelOrderJob>(x => x.AutoCheckIn(hotelOrder.Id, tenantId), ts);
        }

        //订单状态变更通知
        await _mediator.Publish(new HotelOrderChangeNotification
        {
            TenantId = tenantId,
            SellingChannel = baseOrder.SellingChannels,
            UserId = baseOrder.UserId,
            ChannelOrderNo = baseOrder.ChannelOrderNo,
            BaseOrderId = hotelOrder.BaseOrderId, //主单id
            OrderStatus = hotelOrder.Status,
            ConfirmCode = hotelOrder.ConfirmCode
        });
        return Unit.Value;
    }
}