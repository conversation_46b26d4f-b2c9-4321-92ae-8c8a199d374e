using Common.ServicesHttpClient;
using Contracts.Common.Tenant.Enums;

namespace Order.Api.Extensions
{
    public static class ServicesAddressExtensions
    {
        #region Hotel

        public static string Hotel_CheckPriceStrategySale(this ServicesAddress address)
            => $"{address.Hotel}/PriceStrategyCalendarPrice/CheckPriceStrategySaleHasCost";

        public static string Hotel_CheckPriceStrategySaleByManual(this ServicesAddress address)
            => $"{address.Hotel}/PriceStrategyCalendarPrice/CheckPriceStrategySaleByManual";

        public static string Hotel_PreOrderCheckSale(this ServicesAddress address)
            => $"{address.Hotel}/PriceStrategyCalendarPrice/PreOrderCheckSale";

        public static string Hotel_PushApiHotelWeightValue(this ServicesAddress address)
            => $"{address.Hotel}/ApiHotel/PushWeightValue";

        public static string Hotel_GetByIds(this ServicesAddress address)
            => $"{address.Hotel}/Hotel/GetByIds";

        public static string Hotel_GetApiHotelsByIds(this ServicesAddress address)
            => $"{address.Hotel}/ApiHotel/Detail";

        #endregion

        #region Product

        /// <summary>
        /// 获取票券产品和SKU信息
        /// </summary>
        public static string Product_TicketOrderGetProduct(this ServicesAddress address, long productId, long productSkuId)
                => $"{address.Product}/ProductSku/TicketOrderGetProduct?productId={productId}&productSkuId={productSkuId}";

        /// <summary>
        /// 获取产品使用明细
        /// </summary>
        public static string Product_GetUsedDetail(this ServicesAddress address, long productId)
                => $"{address.Product}/TicketProduct/GetUsedDetail?productId={productId}";

        /// <summary>
        /// 获取产品使用明细
        /// </summary>
        public static string Product_GetProductResourcesBySupplier(this ServicesAddress address)
                => $"{address.Product}/ProductResource/GetBySupplier";

        public static string Product_GetFirstProductResource(this ServicesAddress address)
                => $"{address.Product}/ProductResource/GetFirstResource";

        public static string Product_GetResource(this ServicesAddress address)
            => $"{address.Product}/ProductResource/GetResources";

        public static string Product_GetCarHailingProduct(this ServicesAddress address, long id)
          => $"{address.Product}/CarHailingProduct/Get?id={id}";

        public static string Product_GetCarProductSkus(this ServicesAddress address)
            => $"{address.Product}/CarProductSku/Details";

        public static string Product_GetCarProducts(this ServicesAddress address)
            => $"{address.Product}/CarProduct/Details";

        public static string Product_GetChannelTimelinessSetting(this ServicesAddress address, long lineProductId)
            => $"{address.Product}/LineProduct/GetChannelTimelinessSetting?lineProductId={lineProductId}";

        #endregion

        #region Tenant

        /// <summary>
        /// 获取产品客服
        /// </summary>
        public static string Tenant_GetProductSupportStaff(this ServicesAddress address)
               => $"{address.Tenant}/SupportStaff/GetProductSupportStaff";

        /// <summary>
        /// 获取供应商
        /// </summary>
        public static string Tenant_GetSupplier(this ServicesAddress address, long supplierId)
               => $"{address.Tenant}/Supplier/Get?supplierId={supplierId}";

        /// <summary>
        /// 获取供应商apisetting
        /// </summary>
        public static string Tenant_GetSupplierApiSetting(this ServicesAddress address)
               => $"{address.Tenant}/SupplierApiSetting/GetApiSettingInfos";

        /// <summary>
        /// 查询分销商简要信息
        /// </summary>
        public static string Tenant_GetAgencySimpleInfo(this ServicesAddress address, long agencyId)
            => $"{address.Tenant}/Agency/GetSimpleInfo?id={agencyId}";

        /// <summary>
        /// 获取分销商
        /// </summary>
        public static string Tenant_GetAgencyDetail(this ServicesAddress address, long agencyId)
            => $"{address.Tenant}/Agency/Details?id={agencyId}";

        public static string Tenant_Agency_GetByIds(this ServicesAddress address)
            => $"{address.Tenant}/Agency/GetByIds";

        public static string Tenant_GetAgencyApiSetting(this ServicesAddress address, AgencyApiType agencyApiType)
            => $"{address.Tenant}/Agency/GetApiSetting?agencyApiType={agencyApiType}";

        public static string Tenant_AgencyCredit_Get(this ServicesAddress address, long agencyId)
            => $"{address.Tenant}/AgencyCredit/GetByAgencyId?agencyId={agencyId}";

        public static string Tenant_AgencyCredit_EditPay(this ServicesAddress address)
            => $"{address.Tenant}/AgencyCreditRecord/OrderEditPay";

        public static string Tenant_GetApiSettingInfos(this ServicesAddress address)
               => $"{address.Tenant}/SupplierApiSetting/GetApiSettingInfos";

        public static string Tenant_AgencyCredit_OffsetOrder(this ServicesAddress address)
            => $"{address.Tenant}/AgencyCreditRecord/OffsetOrderCreate";

        public static string Tenant_AgencyCredit_ReceiptSettlementOrderPay(this ServicesAddress address)
            => $"{address.Tenant}/AgencyCreditRecord/ReceiptSettlementOrderPay";
        public static string Tenant_AgencyCredit_ReceiptSettlementOrderChecking(this ServicesAddress address)
            => $"{address.Tenant}/AgencyCreditRecord/ReceiptSettlementOrderChecking";

        public static string Tenant_AgencyCredit_ReceiptSettlementOrderCancel(this ServicesAddress address)
           => $"{address.Tenant}/AgencyCreditRecord/ReceiptSettlementOrderCancel";

        public static string Tenant_AgencyCreditCharge_SettlementDetailSearch(this ServicesAddress address)
            => $"{address.Tenant}/AgencyCreditCharge/SettlementDetailSearch";

        public static string Tenant_AgencyCreditCharge_BindReceiptSettlementOrder(this ServicesAddress address)
            => $"{address.Tenant}/AgencyCreditCharge/BindReceiptSettlementOrder";

        public static string Tenant_AgencyCreditCharge_UnBindReceiptSettlementOrder(this ServicesAddress address)
            => $"{address.Tenant}/AgencyCreditCharge/UnBindReceiptSettlementOrder";

        public static string Tenant_AgencyCredit_Details(this ServicesAddress address)
            => $"{address.Tenant}/AgencyCredit/Details";

        public static string Tenant_Tenant_GetSysConfigByTenantIds(this ServicesAddress address)
            => $"{address.Tenant}/Tenant/GetSysConfigByTenantIds";

        public static string Tenant_GetLevelConfigHotelByHotelIds(this ServicesAddress address)
           => $"{address.Tenant}/AgencyLevelConfigHotel/GetByHotelIds";

        public static string Tenant_GetLevelConfigOvertimeHotelByHotelIds(this ServicesAddress address)
          => $"{address.Tenant}/AgencyLevelConfigOvertimeHotel/GetByHotelIds";

        /// <summary>
        /// 获取供应商
        /// </summary>
        public static string Tenant_Supplier_GetByIds(this ServicesAddress address)
            => $"{address.Tenant}/Supplier/GetByIds";

        /// <summary>
        /// 获取分销商配置
        /// </summary>
        public static string Tenant_B2bWebConfiguration_Get(this ServicesAddress address)
            => $"{address.Tenant}/b2bwebconfiguration/get";

        /// <summary>
        /// 获取租户配置
        /// </summary>
        public static string Tenant_SysConfigByTenantId_Get(this ServicesAddress address, long tenantId)
            => $"{address.Tenant}/Tenant/GetSysConfigByTenantId?tenantId={tenantId}";

        /// <summary>
        /// 查询租户简要信息
        /// </summary>
        public static string Tenant_Tenant_GetSimpleInfo(this ServicesAddress address)
            => $"{address.Tenant}/Tenant/GetSimpleInfo";

        public static string Tenant_Tenant_GetCitOpenTenantConfigInfo(this ServicesAddress address)
            => $"{address.Tenant}/CitOpenTenantConfig/Get";

        public static string Tenant_AgencyCreditChargeDetails(this ServicesAddress address)
            => $"{address.Tenant}/AgencyCreditCharge/Details";

        public static string Tenant_AgencyDelayedCreditOrderDelayedPay(this ServicesAddress address)
            => $"{address.Tenant}/AgencyDelayedCreditRecord/OrderDelayedPay";

        public static string Tenant_GetAgencyDelayedCredit(this ServicesAddress address, long agencyId)
           => $"{address.Tenant}/AgencyDelayedCredit/Get?agencyId={agencyId}";

        public static string Tenant_GetFinancialSetting(this ServicesAddress address)
            => $"{address.Tenant}/FinancialSetting/Get";
        #endregion

        #region Inventory

        public static string Inventory_CalendarGetInventories(this ServicesAddress address)
            => $"{address.Inventory}/Calendar/GetInventories";

        public static string Inventory_GeneralGetInventories(this ServicesAddress address)
            => $"{address.Inventory}/General/GetInventories";

        public static string Inventory_TimeSlotCalendarGetInventories(this ServicesAddress address)
            => $"{address.Inventory}/TimeSlotCalendar/GetInventories";

        #endregion

        #region Product

        public static string Product_ProductSkuIsMailProductValidity(this ServicesAddress address)
            => $"{address.Product}/ProductSku/IsMailProductValidity";

        public static string Product_ProductSkuGetProductSkuPrice(this ServicesAddress address)
            => $"{address.Product}/ProductSku/GetProductSkuPrice";

        public static string Product_PostageTemplateCustomerFreight(this ServicesAddress address)
            => $"{address.Product}/PostageTemplate/CustomerFreight";

        public static string Product_GetProductSkuCalendarPrice(this ServicesAddress address)
            => $"{address.Product}/SkuCalendarPrice/GetProductSkuCalendarPrice";

        public static string Product_GetLineProductSimpleInfo(this ServicesAddress address)
            => $"{address.Product}/LineProduct/GetSimpleInfo";


        public static string Product_QueryAgencyChannelPrice(this ServicesAddress address)
            => $"{address.Product}/agencychannelpricesettings/query";

        public static string Product_GetCarHailingCalendarPrices(this ServicesAddress address)
            => $"{address.Product}/CarHailingCalendarPrices/Get";

        public static string Product_CarProductDetails(this ServicesAddress address)
            => $"{address.Product}/CarProduct/Details";

        public static string Product_TicketProductDetails(this ServicesAddress address)
            => $"{address.Product}/TicketProduct/Details";

        public static string Product_QueryOpenSupplierSkuExtraInfo(this ServicesAddress address)
            => $"{address.Product}/OpenSupplierProduct/QuerySkuExtraInfo";

        #endregion

        #region Marketing

        public static string Marketing_UserCouponGetByOrderProducts(this ServicesAddress address)
               => $"{address.Marketing}/UserCoupon/GetByOrderProducts";

        public static string Marketing_GetStoredValueCard(this ServicesAddress address, long storedValueCardId)
               => $"{address.Marketing}/StoredValueCard/GetStoredValueCard?storedValueCardId={storedValueCardId}";

        public static string Marketing_GetUserStoredValueCardsByOrderIds(this ServicesAddress address)
               => $"{address.Marketing}/UserStoredValueCard/GetUserStoredValueCardsByOrderIds";

        public static string Marketing_GetCouponBaseInfos(this ServicesAddress address)
               => $"{address.Marketing}/Coupon/GetCouponBaseInfos";

        public static string Marketing_GetFlashSalePreOrder(this ServicesAddress address)
               => $"{address.Marketing}/FlashSaleOrderRecord/GetPreOrder";

        public static string Marketing_FlashSaleOrderRecord(this ServicesAddress address)
              => $"{address.Marketing}/FlashSaleOrderRecord/Record";

        public static string Marketing_GetPromotionTrace(this ServicesAddress address, long id)
              => $"{address.Marketing}/PromotionTrace/Get?id={id}";

        #endregion

        #region User

        public static string User_GetCustomerInfo(this ServicesAddress address)
           => $"{address.User}/CustomerUser/GetCustomerInfo";

        public static string User_GetVipLevelById(this ServicesAddress address, long levelId)
          => $"{address.User}/VipLevel/GetById?levelId={levelId}";

        public static string User_GetDiscountRightsByProduct(this ServicesAddress address)
         => $"{address.User}/VipRights/GetDiscountRightsByProduct";

        public static string User_GetDiscountRightsByHotel(this ServicesAddress address)
         => $"{address.User}/VipRights/GetDiscountRightsByHotel";

        public static string User_GetDiscountRightsByScenic(this ServicesAddress address)
            => $"{address.User}/VipRights/GetDiscountRightsByScenic";

        public static string User_GetTenantUsers(this ServicesAddress address)
         => $"{address.User}/TenantUser/SearchUsers";

        public static string User_GetAgencyUsers(this ServicesAddress address)
      => $"{address.User}/AgencyUser/GetDetails";

        public static string User_GetCustomerUsers(this ServicesAddress address)
      => $"{address.User}/CustomerUser/GetByIds";

        public static string User_ManageUserSearch(this ServicesAddress address)
            => $"{address.User}/ManageUser/Search";

        public static string GetListByJobIds(this ServicesAddress address)
            => $"{address.User}/ManageUser/GetListByJobIds";

        public static string ManageUser_GetListByIds(this ServicesAddress address)
           => $"{address.User}/ManageUser/GetListByIds";


        #endregion

        #region Scenic

        public static string Scenic_GetDetail(this ServicesAddress address, long id)
            => $"{address.Scenic}/ScenicSpot/Detail?id={id}";

        public static string Scenic_GetScenicSpotByIds(this ServicesAddress address)
            => $"{address.Scenic}/ScenicSpot/GetByIds";

        public static string Scenic_GetTicketDetail(this ServicesAddress address, long id)
            => $"{address.Scenic}/Ticket/Detail?id={id}";

        public static string Scenic_SearchTicketCalendarPrice(this ServicesAddress address)
            => $"{address.Scenic}/TicketCalendarPrice/Search";

        public static string Scenic_GetTimeSlots(this ServicesAddress address)
            => $"{address.Scenic}/Ticket/GetTimeSlots";

        public static string Scenic_GetScenicTicketByIds(this ServicesAddress address)
            => $"{address.Scenic}/Ticket/GetByIds";

        public static string Scenic_GetScenicTicketChannelSetting(this ServicesAddress address, long id)
            => $"{address.Scenic}/Ticket/GetChannelSetting?id={id}";

        #endregion

        #region Notify

        public static string CustomerNotify_GetSwitch(this ServicesAddress address)
            => $"{address.Notify}/CustomerNotify/GetSwitches";


        /// <summary>
        /// 获取组合邮箱配置
        /// </summary>
        /// <param name="address"></param>
        /// <returns></returns>
        public static string TenantEmailBox_Get(this ServicesAddress address)
            => $"{address.Notify}/TenantEmailBox/Get";

        /// <summary>
        /// 同步发送邮箱
        /// </summary>
        /// <param name="address"></param>
        /// <returns></returns>
        public static string SendEmail_Synchronously(this ServicesAddress address)
            => $"{address.Notify}/Email/Send";

        #endregion

        #region Payment

        public static string Payment_GetCurrencyExchangeRate(this ServicesAddress address)
            => $"{address.Payment}/CurrencyExchangeRate/Get";

        public static string Payment_OrderPay(this ServicesAddress address)
            => $"{address.Payment}/order/pay";

        public static string Payment_SearchSupplierPrepaymentFlows(this ServicesAddress address)
            => $"{address.Payment}/SupplierPrepayment/SearchSupplierPrepaymentFlows";

        public static string Payment_QueryReceiptPrepaymentFlows(this ServicesAddress address)
            => $"{address.Payment}/ReceiptPrepayment/QueryFlows";

        public static string Payment_GetReceiptPrepaymentRefundList(this ServicesAddress address)
            => $"{address.Payment}/ReceiptPrepayment/GetRefundList";

        public static string Payment_AccountPaymentBillSearch(this ServicesAddress address)
            => $"{address.Payment}/AccountPaymentBill/Search";

        public static string Payment_OrderPaymentStatementExportData(this ServicesAddress address)
            => $"{address.Payment}/OrderPaymentDetail/OrderStatementExportData";

        public static string Payment_WithdrawOrderSearch(this ServicesAddress address)
           => $"{address.Payment}/WithdrawOrder/Search";

        public static string Payment_OrderRefundSearch(this ServicesAddress address)
            => $"{address.Payment}/OrderRefund/Search";

        public static string Payment_SearchOrderPaymentDetails(this ServicesAddress address)
            => $"{address.Payment}/OrderPayment/SearchOrderPaymentDetails";

        public static string Payment_FundAccountBillFlowsExport(this ServicesAddress address)
            => $"{address.Payment}/FundAccountBill/FlowsExport";

        public static string Payment_GetTenantBankAccount(this ServicesAddress address, long id)
            => $"{address.Payment}/TenantBankAccount/Get?id={id}";

        public static string Payment_GetTenantBankByAccount(this ServicesAddress address)
            => $"{address.Payment}/TenantBankAccount/GetByAccountNo";
        #endregion

        #region Resource

        public static string Resource_ThirdHotelCheckAvailability(this ServicesAddress address)
            => $"{address.Resource}/ThirdHotel/CheckAvailability";

        public static string Resource_ThirdHotelCreateOrder(this ServicesAddress address)
            => $"{address.Resource}/ThirdHotel/CreateOrder";

        public static string Resource_ThirdHotelQueryOrderDetail(this ServicesAddress address)
           => $"{address.Resource}/ThirdHotel/QueryOrderDetail";

        public static string Resource_ThirdHotelCancelOrder(this ServicesAddress address)
          => $"{address.Resource}/ThirdHotel/CancelOrder";

        public static string Resource_ThirdHotelUpdateOrderGuest(this ServicesAddress address)
           => $"{address.Resource}/ThirdHotel/UpdateOrderGuest";

        public static string Resource_FileDownloadOssObject(this ServicesAddress address, string path)
          => $"{address.Resource}/File/downloadossobject?path={path}";

        public static string Resource_HotelDetail(this ServicesAddress address, long hotelId)
            => $"{address.Resource}/Hotel/Detail?hotelId={hotelId}";

        public static string Resource_CityQuery(this ServicesAddress address)
         => $"{address.Resource}/City/Query";

        public static string Resource_QueryProvince(this ServicesAddress address, int countryCode)
            => $"{address.Resource}/Province/GetList?countryCode={countryCode}";

        public static string Resource_Country(this ServicesAddress address)
          => $"{address.Resource}/Country/GetAll";

        public static string Resource_HotelResourceSearch(this ServicesAddress address)
           => $"{address.Resource}/Hotel/ResourceSearch";

        public static string Resource_SearchHotelInquiryEmail(this ServicesAddress address)
            => $"{address.Resource}/Hotel/SearchHotelInquiryEmail";

        public static string Resource_SearchHotelUserInfo(this ServicesAddress address)
            => $"{address.Resource}/Hotel/SearchHotelUserInfo";

        public static string Resource_GDSHotelPriceCheck(this ServicesAddress address)
        => $"{address.Resource}/GDSHotel/PriceCheck";

        public static string Resource_SearchHopIds(this ServicesAddress address)
       => $"{address.Resource}/Hotel/GetHopIds";

        public static string Resource_GDSHotelDetails(this ServicesAddress address)
         => $"{address.Resource}/GDSHotel/Details";

        public static string Resource_HolidayList(this ServicesAddress address)
          => $"{address.Resource}/Holiday/HolidayList";

        #endregion

        #region Order
        public static string Order_RefundByJob(this ServicesAddress address)
         => $"{address.Order}/HotelOrder/RefundByJob";
        #endregion
    }
}