using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Order.Api.Infrastructure.EntityConfigurations
{
    public class GroupBookingOrderItemEntityTypeConfiguration : TenantBaseConfiguration<Model.GroupBookingOrderItem>, IEntityTypeConfiguration<Model.GroupBookingOrderItem>
    {
        public void Configure(EntityTypeBuilder<Model.GroupBookingOrderItem> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.GroupBookingOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.GroupBookingPreOrderItemId)
                .HasColumnType("bigint");

            builder.Property(s => s.BaseOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.HotelOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.SupplierId)
                .HasColumnType("bigint");

            builder.Property(s => s.SupplierApiType)
                .HasColumnType("tinyint");

            builder.Property(s => s.HotelId)
                .HasColumnType("bigint");

            builder.Property(s => s.ResourceHotelId)
               .HasColumnType("bigint");

            builder.Property(s => s.CityCode)
                .HasColumnType("int");

            builder.Property(s => s.CityName)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.HotelZHName)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.HotelENName)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.CheckInDate)
                .HasColumnType("datetime");

            builder.Property(s => s.CheckOutDate)
                .HasColumnType("datetime");

            builder.Property(s => s.HotelRoomId)
                .HasColumnType("bigint");

            builder.Property(s => s.HotelRoomName)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.HotelRoomEnName)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.BedType)
                .HasColumnType("varchar(1000)");

            builder.Property(s => s.MaximumOccupancy)
                .HasColumnType("int");

            builder.Property(s => s.PriceStrategyId)
                .HasColumnType("varchar(64)");

            builder.Property(s => s.PriceStrategyName)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.PriceStrategyEnName)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.RoomCount)
                .HasColumnType("int");

            builder.Property(s => s.NumberOfBreakfast)
                .HasColumnType("int");

            builder.Property(s => s.HotelOrderStatus)
                .HasColumnType("tinyint");

            builder.Property(s => s.GuestFilePath)
                .HasColumnType("varchar(256)");

            builder.HasIndex(s => s.GroupBookingOrderId);
        }
    }
}
