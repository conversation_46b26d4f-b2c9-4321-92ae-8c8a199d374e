using AutoMapper;
using Cit.Storage.Redis;
using Common.ServicesHttpClient;
using Contracts.Common.Order.DTOs.MessageNotify;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Scenic.Enums;
using Contracts.Common.User.Messages;
using DotNetCore.CAP;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Order.Api.ConfigModel;
using Order.Api.Infrastructure;
using Order.Api.Model;
using Order.Api.Services;
using Order.Api.Services.Interfaces;
using Order.Api.Services.MappingProfiles;
using Order.Api.Services.OrderProcessing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;
using UserType = Contracts.Common.Order.Enums.UserType;

namespace Order.UnitTest
{
    public class BaseOrderServiceTest : TestBase<CustomDbContext>
    {
        private static BaseOrderService CreateService(
            CustomDbContext dbContext = null,
            ICapPublisher capPublisher = null,
            ITicketCodeCreateService ticketCodeCreateService = null,
            Hangfire.IBackgroundJobClient backgroundJobClient = null,
            IMessageNotifyService messageNotifyService = null,
            IMediator mediator = null,
            IMapper mapper = null,
            List<IOrderProcessingService> orderProcessingServices = null,
            IOptions<SellingPlatformConfig> sellingPlatformConfig=null)
        {

            return new BaseOrderService(
                dbContext,
                capPublisher,
                messageNotifyService,
                mapper,
                orderProcessingServices,
                sellingPlatformConfig
            );
        }

        private static List<IOrderProcessingService> GetOrderProcessingServices(CustomDbContext dbContext,
            ICapPublisher capPublisher)
        {
            var mediator = new Mock<MediatR.IMediator>();
            mediator.Setup(s => s.Publish(It.IsAny<MediatR.INotification>(), default))
                .Returns(Task.CompletedTask);
            MapperConfiguration mapperConfiguration = new(x =>
            {
                x.AddMaps(typeof(BaseOrderProfile).Assembly);
            });
            var mapper = mapperConfiguration.CreateMapper();
            var messageNotifyService = new Mock<IMessageNotifyService>();
            messageNotifyService.Setup(x => x.MailOrderCreatedNotify(It.IsAny<OrderNotifyDto<MailOrderCreatedNotifyDto>>()));
            var orderShareInfoService = new Mock<IOrderShareInfoService>();
            orderShareInfoService.Setup(x => x.OrderDarenPreRelease(It.IsAny<long>(), It.IsAny<IEnumerable<SubOrder>>()));

            var ticketCodeCreateMock = new Mock<ITicketCodeCreateService>();
            List<IOrderProcessingService> orderProcessingServices = new();
            orderProcessingServices.Add(new MailOrderProcessingService(dbContext,
                capPublisher,
                messageNotifyService.Object,
                orderShareInfoService.Object,
                mapper)
            );
            orderProcessingServices.Add(new HotelOrderProcessingService(dbContext,
                null,
                mapper,
                mediator.Object,
                capPublisher,
                messageNotifyService.Object,
                orderShareInfoService.Object));
            orderProcessingServices.Add(new TicketOrderProcessingService(dbContext,
                ticketCodeCreateMock.Object,
                 messageNotifyService.Object,
                 orderShareInfoService.Object,
                 mapper,
                 mediator.Object,
                 capPublisher)
                );
            return orderProcessingServices;
        }

        #region [订阅] 订单支付成功（不含预约单）

        [Fact(DisplayName = "MailOrder 支付成功")]
        public async Task OrderStatusChangeByPaySuccess_MailOrder_Success()
        {
            //arrange
            var tenantId = 1;
            var dbContext = GetNewDbContext();
            var capPublisher = GetCapPublisher();
            MapperConfiguration mapperConfiguration = new(x =>
            {
                x.AddMaps(typeof(BaseOrderProfile).Assembly);
            });
            var mapper = mapperConfiguration.CreateMapper();
            var messageNotifyService =  new Mock<IMessageNotifyService>();
            messageNotifyService.Setup(x => x.MailOrderCreatedNotify(It.IsAny<OrderNotifyDto<MailOrderCreatedNotifyDto>>()));
            var orderShareInfoService = new Mock<IOrderShareInfoService>();
            orderShareInfoService.Setup(x => x.OrderDarenPreRelease(It.IsAny<long>(), It.IsAny<IEnumerable<SubOrder>>()));
            IOrderProcessingService orderProcessingService = new MailOrderProcessingService(dbContext,
                capPublisher,
                messageNotifyService.Object,
                orderShareInfoService.Object,
                mapper);

            var service = CreateService(
                dbContext: dbContext,
                capPublisher: capPublisher,
                mapper: mapper,
                messageNotifyService: new MessageNotifyService(capPublisher),
                orderProcessingServices: new List<IOrderProcessingService> { orderProcessingService });

            //fake data
            var baseOrder = new BaseOrder
            {
                ContactsName = "1",
                ContactsPhoneNumber = "1",
                Status = BaseOrderStatus.WaitingForPay,
                OrderType = OrderType.Mail
            };
            await dbContext.AddAsync(baseOrder);
            var mailOrders = new List<MailOrder>
            {
                new MailOrder { BaseOrderId = baseOrder.Id, Status = MailOrderStatus.WaitingForPay },
                new MailOrder { BaseOrderId = baseOrder.Id, Status = MailOrderStatus.WaitingForPay }
            };
            await dbContext.AddRangeAsync(mailOrders);
            List<OrderPrice> orderPrices = new();
            mailOrders.ForEach(x =>
            {
                orderPrices.Add(new OrderPrice
                {
                    SubOrderId = x.Id,
                    OrderType = OrderType.Mail,
                    Quantity = 1
                });
            });
            await dbContext.AddRangeAsync(orderPrices);
            await dbContext.SetTenantId(tenantId).SaveChangesAsync();

            //act
            await service.OrderStatusChangeByPaySuccess(new OrderStatusChangeByPaySuccessMessage
            {
                PaymentType = PayType.YeePay,
                OrderId = baseOrder.Id,
                PaymentMode = "wx",
                PaymentChannel = "c1",
                PaymentExternalNo = "wx10022112154"
            });
            await dbContext.SaveChangesAsync();
            //1，主单状态必须变更为UnFinished
            //2，必须有支付成功的日志
            //3，所有子订单状态必须变更为WaitingForDeliver
            Assert.True(dbContext.BaseOrders.IgnoreQueryFilters()
                .Any(s => s.Id == baseOrder.Id && s.Status == BaseOrderStatus.UnFinished));
            Assert.True(dbContext.OrderLogs.IgnoreQueryFilters()
                .Any(s => s.OrderId == baseOrder.Id && s.OperationType == OrderOperationType.Paid));
            var counts = dbContext.MailOrders.IgnoreQueryFilters()
                .Where(s => s.BaseOrderId == baseOrder.Id && s.Status == MailOrderStatus.WaitingForDeliver)
                .Count();
            Assert.True(counts == 2);
        }

        [Fact(DisplayName = "HotelOrder 支付成功")]
        public async Task OrderStatusChangeByPaySuccess_HotelOrder_Success()
        {
            //arrange
            var tenantId = 1;
            var dbContext = GetNewDbContext();
            var capPublisher = GetCapPublisher();
            var mediator = new Mock<MediatR.IMediator>();
            mediator.Setup(s => s.Publish(It.IsAny<MediatR.INotification>(), default))
                .Returns(Task.CompletedTask);
            MapperConfiguration mapperConfiguration = new(x =>
            {
                x.AddMaps(typeof(BaseOrderProfile).Assembly);
            });
            var mapper = mapperConfiguration.CreateMapper();
            var messageNotifyService = new Mock<IMessageNotifyService>();
            messageNotifyService.Setup(x => x.MailOrderCreatedNotify(It.IsAny<OrderNotifyDto<MailOrderCreatedNotifyDto>>()));
            var orderShareInfoService = new Mock<IOrderShareInfoService>();
            orderShareInfoService.Setup(x => x.OrderDarenPreRelease(It.IsAny<long>(), It.IsAny<IEnumerable<SubOrder>>()));
            IOrderProcessingService orderProcessingService = new HotelOrderProcessingService(dbContext,
                null,
                mapper,
                mediator.Object,
                capPublisher,
                messageNotifyService.Object,
                orderShareInfoService.Object);

            var service = CreateService(
                dbContext: dbContext,
                capPublisher: capPublisher,
                messageNotifyService: new MessageNotifyService(capPublisher),
                mediator: mediator.Object,
                mapper: mapper,
                orderProcessingServices: new List<IOrderProcessingService> { orderProcessingService });

            //fake data
            var baseOrder = new BaseOrder
            {
                ContactsName = "1",
                ContactsPhoneNumber = "1",
                Status = BaseOrderStatus.WaitingForPay,
                OrderType = OrderType.Hotel
            };
            await dbContext.AddAsync(baseOrder);

            await dbContext.AddAsync(new HotelOrder
            {
                BaseOrderId = baseOrder.Id,
                PriceStrategyIsAutoConfirm = false, //需确认
                Status = HotelOrderStatus.WaitingForPay,
            });
            await dbContext.SetTenantId(tenantId).SaveChangesAsync();

            //act
            await service.OrderStatusChangeByPaySuccess(new OrderStatusChangeByPaySuccessMessage
            {
                PaymentType = PayType.YeePay,
                OrderId = baseOrder.Id,
                PaymentMode = "wx",
                PaymentChannel = "c1",
                PaymentExternalNo = "wx10022112154"
            });
            await dbContext.SaveChangesAsync();
            //1，主单状态必须变更为UnFinished
            //2，必须有支付成功的日志
            //3，子订单状态必须变更为WaitingForConfirm
            Assert.True(dbContext.BaseOrders.IgnoreQueryFilters()
                .Any(s => s.Id == baseOrder.Id && s.Status == BaseOrderStatus.UnFinished));
            Assert.True(dbContext.OrderLogs.IgnoreQueryFilters()
                .Any(s => s.OrderId == baseOrder.Id && s.OperationType == OrderOperationType.Paid));
            Assert.True(dbContext.HotelOrders.IgnoreQueryFilters()
                .Any(s => s.BaseOrderId == baseOrder.Id && s.Status == HotelOrderStatus.WaitingForConfirm));
        }

        [Fact(DisplayName = "TicketOrder 支付成功")]
        public async Task OrderStatusChangeByPaySuccess_TicketOrder_Success()
        {
            //arrange
            var tenantId = 1;
            var quantity = 2;
            var dbContext = GetNewDbContext();
            var capPublisher = GetCapPublisher();
            var ticketCodeCreateMock = new Mock<ITicketCodeCreateService>();
            var codes = Enumerable.Range(10_000_000, quantity).Select(s => (long)s).ToList();
            ticketCodeCreateMock.Setup(s => s.Get(quantity))
                .ReturnsAsync(codes);
            var mediator = new Mock<MediatR.IMediator>();
            mediator.Setup(s => s.Publish(It.IsAny<MediatR.INotification>(), default))
                .Returns(Task.CompletedTask);
            MapperConfiguration mapperConfiguration = new(x =>
            {
                x.AddMaps(typeof(BaseOrderProfile).Assembly);
            });
            var mapper = mapperConfiguration.CreateMapper();
            var messageNotifyService = new Mock<IMessageNotifyService>();
            messageNotifyService.Setup(x => x.MailOrderCreatedNotify(It.IsAny<OrderNotifyDto<MailOrderCreatedNotifyDto>>()));
            var orderShareInfoService = new Mock<IOrderShareInfoService>();
            orderShareInfoService.Setup(x => x.OrderDarenPreRelease(It.IsAny<long>(), It.IsAny<IEnumerable<SubOrder>>()));
            IOrderProcessingService orderProcessingService = new TicketOrderProcessingService(dbContext,
                ticketCodeCreateMock.Object,
                messageNotifyService.Object,
                orderShareInfoService.Object,
                mapper, 
                mediator.Object,
                capPublisher);

            var service = CreateService(
                dbContext: dbContext,
                capPublisher: capPublisher,
                ticketCodeCreateService: ticketCodeCreateMock.Object,
                mapper: mapper,
                messageNotifyService: new MessageNotifyService(capPublisher),
                orderProcessingServices: new List<IOrderProcessingService> { orderProcessingService });

            //fake data
            var baseOrder = new BaseOrder
            {
                ContactsName = "1",
                ContactsPhoneNumber = "1",
                Status = BaseOrderStatus.WaitingForPay,
                OrderType = OrderType.Ticket
            };
            await dbContext.AddAsync(baseOrder);
            TicketOrder ticketOrder = new TicketOrder
            {
                BaseOrderId = baseOrder.Id,
                Quantity = quantity,
                ProductNeedReservation = true, //需要预约
            };
            await dbContext.AddAsync(ticketOrder);
            OrderPrice orderPrice = new()
            {
                SubOrderId = ticketOrder.Id,
                OrderType = OrderType.Ticket,
                Quantity = quantity
            };
            await dbContext.AddAsync(orderPrice);
            await dbContext.SetTenantId(tenantId).SaveChangesAsync();

            //act
            await service.OrderStatusChangeByPaySuccess(new OrderStatusChangeByPaySuccessMessage
            {
                PaymentType = PayType.YeePay,
                OrderId = baseOrder.Id,
                PaymentMode = "wx",
                PaymentChannel = "c1",
                PaymentExternalNo = "wx10022112154"
            });
            await dbContext.SaveChangesAsync();
            //1，主单状态必须变更为UnFinished
            //2，必须有支付成功的日志
            //3，生成 quantity 个的券码，状态为WaitingForReservation
            Assert.True(dbContext.BaseOrders.IgnoreQueryFilters()
                .Any(s => s.Id == baseOrder.Id && s.Status == BaseOrderStatus.UnFinished));
            Assert.True(dbContext.OrderLogs.IgnoreQueryFilters()
                .Any(s => s.OrderId == baseOrder.Id && s.OperationType == OrderOperationType.Paid));
            Assert.True(dbContext.TicketCodes.IgnoreQueryFilters()
                .Where(s => s.BaseOrderId == baseOrder.Id && s.Status == TicketCodeStatus.WaitingForReservation)
                .Count() == quantity);
        }
        
        #endregion

        #region [订阅] 订单退款结果

        [Theory(DisplayName = "MailOrder 退款成功")]
        [InlineData(new[] { MailOrderStatus.Refunding }, BaseOrderStatus.Closed)] //1退款中，最终状态为已关闭
        [InlineData(new[] { MailOrderStatus.Refunding, MailOrderStatus.Refunded }, BaseOrderStatus.Closed)] //1退款中，1已退款，最终状态为已关闭
        [InlineData(new[] { MailOrderStatus.Refunding, MailOrderStatus.Finished }, BaseOrderStatus.Finished)] //1退款中，1已完成，最终状态为已完成
        [InlineData(new[] { MailOrderStatus.Refunding, MailOrderStatus.WaitingForDeliver }, BaseOrderStatus.UnFinished)] //1退款中，1待发货，最终状态待完成
        public async Task RefundResult_MailOrder_Success(MailOrderStatus[] mailOrderStatus, BaseOrderStatus baseOrderFinalStatus)
        {
            //arrange
            var tenantId = 1;
            var dbContext = GetNewDbContext();
           
            #region fake data

            var baseOrder = new BaseOrder
            {
                ContactsName = "1",
                ContactsPhoneNumber = "1",
                Status = BaseOrderStatus.UnFinished,
                OrderType = OrderType.Mail
            };
            await dbContext.AddAsync(baseOrder);
            //子单
            var mailOrders = mailOrderStatus.Select(s => new MailOrder
            {
                BaseOrderId = baseOrder.Id,
                Status = s
            }).ToList();
            await dbContext.AddRangeAsync(mailOrders);

            List<OrderPrice> orderPrices = new();
            mailOrders.ForEach(x =>
            {
                orderPrices.Add(new OrderPrice { SubOrderId = x.Id, OrderType = OrderType.Mail, Quantity = 1 });
            });
            await dbContext.AddRangeAsync(orderPrices);
            //退款单
            var refundOrder = new RefundOrder
            {
                BaseOrderId = baseOrder.Id,
                SubOrdeId = mailOrders.First().Id,
                UserType = UserType.Customer,
                OrderType = RefundOrderType.Mail,
                Status = RefundOrderStatus.Refunding
            };
            await dbContext.AddAsync(refundOrder);
            await dbContext.SetTenantId(tenantId).SaveChangesAsync();

            #endregion
            var capPublisher = GetCapPublisher();
            MapperConfiguration mapperConfiguration = new(x =>
            {
                x.AddMaps(typeof(BaseOrderProfile).Assembly);
            });
            var mapper = mapperConfiguration.CreateMapper();
            var messageNotifyService = new Mock<IMessageNotifyService>();
            messageNotifyService.Setup(x => x.MailOrderCreatedNotify(It.IsAny<OrderNotifyDto<MailOrderCreatedNotifyDto>>()));
            var orderShareInfoService = new Mock<IOrderShareInfoService>();
            orderShareInfoService.Setup(x => x.OrderDarenPreRelease(It.IsAny<long>(), It.IsAny<IEnumerable<SubOrder>>()));
            IOrderProcessingService orderProcessingService = new MailOrderProcessingService(dbContext,
                capPublisher,
                messageNotifyService.Object,
                orderShareInfoService.Object,
                mapper);
            var service = CreateService(
                dbContext: dbContext,
                capPublisher: capPublisher,
                mapper: mapper,
                messageNotifyService: new MessageNotifyService(capPublisher),
                orderProcessingServices: new List<IOrderProcessingService> { orderProcessingService });


            //act
            await service.RefundResult(new RefundResultMessage
            {
                ExtRefundNo = "wx000",
                IsSuccess = true,
                RefundOrderId = refundOrder.Id
            });
            await dbContext.SaveChangesAsync();
            //1，退款单状态必须变更为Refunded
            //2，退款成功日志
            //3，当前子订单状态必须变更为Refunded
            //4，主单状态
            Assert.True(dbContext.RefundOrders.IgnoreQueryFilters()
                .Any(s => s.Id == refundOrder.Id && s.Status == RefundOrderStatus.Refunded));
            Assert.True(dbContext.OrderLogs.IgnoreQueryFilters()
                .Any(s => s.OrderId == baseOrder.Id && s.OperationType == OrderOperationType.RefundSuccessed));
            Assert.True(dbContext.MailOrders.IgnoreQueryFilters()
                .Any(s => s.Id == mailOrders.First().Id && s.Status == MailOrderStatus.Refunded));
            Assert.True(dbContext.BaseOrders.IgnoreQueryFilters()
                .Any(s => s.Id == baseOrder.Id && s.Status == baseOrderFinalStatus));
        }

        [Theory(DisplayName = "TicketOrder 退款成功")]
        [InlineData(new[] { TicketCodeStatus.Refunding }, BaseOrderStatus.Closed)] //1退款中，最终状态为已关闭
        [InlineData(new[] { TicketCodeStatus.Refunding, TicketCodeStatus.Refunded }, BaseOrderStatus.Closed)] //1退款中，1已退款，最终状态为已关闭
        [InlineData(new[] { TicketCodeStatus.Refunding, TicketCodeStatus.Finished }, BaseOrderStatus.Finished)] //1退款中，1已完成，最终状态为已完成
        [InlineData(new[] { TicketCodeStatus.Refunding, TicketCodeStatus.WaitingForUse }, BaseOrderStatus.UnFinished)] //1退款中，1待使用，最终状态待完成
        public async Task RefundResult_TicketOrder_Success(TicketCodeStatus[] ticketCodeStatus, BaseOrderStatus baseOrderFinalStatus)
        {
            //arrange
            var tenantId = 1;
            var dbContext = GetNewDbContext();
            var capPublisher = GetCapPublisher();
            MapperConfiguration mapperConfiguration = new(x =>
            {
                x.AddMaps(typeof(BaseOrderProfile).Assembly);
            });
            var mapper = mapperConfiguration.CreateMapper();
            var messageNotifyService = new Mock<IMessageNotifyService>();
            messageNotifyService.Setup(x => x.MailOrderCreatedNotify(It.IsAny<OrderNotifyDto<MailOrderCreatedNotifyDto>>()));
            var orderShareInfoService = new Mock<IOrderShareInfoService>();
            orderShareInfoService.Setup(x => x.OrderDarenPreRelease(It.IsAny<long>(), It.IsAny<IEnumerable<SubOrder>>()));
            IOrderProcessingService orderProcessingService = new TicketOrderProcessingService(dbContext,
                null,
                messageNotifyService.Object,
                orderShareInfoService.Object,
                mapper,
                null,
                capPublisher);
            var service = CreateService(dbContext: dbContext, capPublisher: capPublisher, mapper: mapper,
                messageNotifyService: new MessageNotifyService(capPublisher),
                orderProcessingServices: new List<IOrderProcessingService> { orderProcessingService });

            #region fake data

            var baseOrder = new BaseOrder
            {
                ContactsName = "1",
                ContactsPhoneNumber = "1",
                Status = BaseOrderStatus.UnFinished,
                OrderType = OrderType.Ticket
            };
            await dbContext.AddAsync(baseOrder);
            //子单
            var ticketCodes = ticketCodeStatus.Select(s => new TicketCode
            {
                BaseOrderId = baseOrder.Id,
                Status = s,
            }).ToList();
            await dbContext.AddRangeAsync(ticketCodes);
            //退款单
            var refundOrder = new RefundOrder
            {
                BaseOrderId = baseOrder.Id,
                Quantity = 1,
                UserType = UserType.Customer,
                OrderType = RefundOrderType.Ticket,
                Status = RefundOrderStatus.Refunding
            };
            await dbContext.AddAsync(refundOrder);
            await dbContext.SetTenantId(tenantId).SaveChangesAsync();

            #endregion

            //act
            await service.RefundResult(new RefundResultMessage
            {
                ExtRefundNo = "wx000",
                IsSuccess = true,
                RefundOrderId = refundOrder.Id
            });
            await dbContext.SaveChangesAsync();
            //1，退款单状态必须变更为Refunded
            //2，退款成功日志
            //3，当前TicketCode状态必须变更为Refunded
            //4，主单状态
            Assert.True(dbContext.RefundOrders.IgnoreQueryFilters()
                .Any(s => s.Id == refundOrder.Id && s.Status == RefundOrderStatus.Refunded));
            Assert.True(dbContext.OrderLogs.IgnoreQueryFilters()
                .Any(s => s.OrderId == baseOrder.Id && s.OperationType == OrderOperationType.RefundSuccessed));
            Assert.True(dbContext.TicketCodes.IgnoreQueryFilters()
                .Any(s => s.Id == ticketCodes.First().Id && s.Status == TicketCodeStatus.Refunded));
            Assert.True(dbContext.BaseOrders.IgnoreQueryFilters()
                .Any(s => s.Id == baseOrder.Id && s.Status == baseOrderFinalStatus));
        }

        [Fact(DisplayName = "HotelOrder 退款成功")]
        public async Task RefundResult_HotelOrder_Success()
        {
            //arrange
            var tenantId = 1;
            var dbContext = GetNewDbContext();
            var capPublisher = GetCapPublisher();
            var mediator = new Mock<MediatR.IMediator>();
            mediator.Setup(s => s.Publish(It.IsAny<MediatR.INotification>(), default))
                .Returns(Task.CompletedTask);
            MapperConfiguration mapperConfiguration = new(x =>
            {
                x.AddMaps(typeof(BaseOrderProfile).Assembly);
            });
            var mapper = mapperConfiguration.CreateMapper();
            var messageNotifyService = new Mock<IMessageNotifyService>();
            messageNotifyService.Setup(x => x.MailOrderCreatedNotify(It.IsAny<OrderNotifyDto<MailOrderCreatedNotifyDto>>()));
            var orderShareInfoService = new Mock<IOrderShareInfoService>();
            orderShareInfoService.Setup(x => x.OrderDarenPreRelease(It.IsAny<long>(), It.IsAny<IEnumerable<SubOrder>>()));
            IOrderProcessingService orderProcessingService = new HotelOrderProcessingService(dbContext,
                null,
                mapper,
                mediator.Object,
                capPublisher,
                messageNotifyService.Object,
                orderShareInfoService.Object);

            var service = CreateService(dbContext: dbContext,
                capPublisher: capPublisher,
                mediator: mediator.Object,
                mapper: mapper,
                messageNotifyService: new MessageNotifyService(capPublisher),
                orderProcessingServices: new List<IOrderProcessingService> { orderProcessingService });

            #region fake data

            var baseOrder = new BaseOrder
            {
                ContactsName = "1",
                ContactsPhoneNumber = "1",
                Status = BaseOrderStatus.UnFinished,
                OrderType = OrderType.Hotel
            };
            await dbContext.AddAsync(baseOrder);
            //子单
            var hotelOrder = new HotelOrder
            {
                BaseOrderId = baseOrder.Id,
                Status = HotelOrderStatus.Refunding
            };
            await dbContext.AddAsync(hotelOrder);
            //退款单
            var refundOrder = new RefundOrder
            {
                BaseOrderId = baseOrder.Id,
                SubOrdeId = hotelOrder.Id,
                UserType = UserType.Customer,
                OrderType = RefundOrderType.Hotel,
                Status = RefundOrderStatus.Refunding
            };
            await dbContext.AddAsync(refundOrder);
            await dbContext.SetTenantId(tenantId).SaveChangesAsync();

            #endregion

            //act
            await service.RefundResult(new RefundResultMessage
            {
                ExtRefundNo = "wx000",
                IsSuccess = true,
                RefundOrderId = refundOrder.Id
            });
            await dbContext.SaveChangesAsync();
            //1，退款单状态必须变更为Refunded
            //2，退款成功日志
            //3，当前子订单状态必须变更为Refunded
            //4，主单状态Closed
            Assert.True(dbContext.RefundOrders.IgnoreQueryFilters()
                .Any(s => s.Id == refundOrder.Id && s.Status == RefundOrderStatus.Refunded));
            Assert.True(dbContext.OrderLogs.IgnoreQueryFilters()
                .Any(s => s.OrderId == baseOrder.Id && s.OperationType == OrderOperationType.RefundSuccessed));
            Assert.True(dbContext.HotelOrders.IgnoreQueryFilters()
                .Any(s => s.Id == hotelOrder.Id && s.Status == HotelOrderStatus.Refunded));
            Assert.True(dbContext.BaseOrders.IgnoreQueryFilters()
                .Any(s => s.Id == baseOrder.Id && s.Status == BaseOrderStatus.Closed));
        }

        [Fact(DisplayName = "ReservationOrder 退款成功")]
        public async Task RefundResult_ReservationOrder_Success()
        {
            //arrange
            var tenantId = 1;
            var dbContext = GetNewDbContext();
            var capPublisher = GetCapPublisher();
            MapperConfiguration mapperConfiguration = new(x =>
            {
                x.AddMaps(typeof(BaseOrderProfile).Assembly);
            });
            var mapper = mapperConfiguration.CreateMapper();
            IOrderProcessingService orderProcessingService = new ReservationOrderProcessingService(dbContext);

            var service = CreateService(dbContext: dbContext, capPublisher: capPublisher, mapper: mapper,
                messageNotifyService: new MessageNotifyService(capPublisher),
                orderProcessingServices: new List<IOrderProcessingService> { orderProcessingService });

            #region fake data
            //主单
            var baseOrder = new BaseOrder
            {
                ContactsName = "1",
                ContactsPhoneNumber = "1",
                Status = BaseOrderStatus.UnFinished,
                OrderType = OrderType.Ticket
            };
            await dbContext.AddAsync(baseOrder);
            //预约单
            var reservationOrder = new ReservationOrder
            {
                BaseOrderId = baseOrder.Id,
                TicketOrderId = 1,
                Status = ReservationStatus.Refunding
            };
            await dbContext.AddAsync(reservationOrder);
            //退款单
            var refundOrder = new RefundOrder
            {
                BaseOrderId = reservationOrder.BaseOrderId,
                SubOrdeId = reservationOrder.Id,
                UserType = UserType.Customer,
                OrderType = RefundOrderType.ReservationOrder,
                Status = RefundOrderStatus.Refunding
            };
            await dbContext.AddAsync(refundOrder);
            //券
            var ticketCode = new TicketCode
            {
                SubOrderId = reservationOrder.TicketOrderId,
                Status = TicketCodeStatus.Canceling
            };
            await dbContext.AddAsync(ticketCode);
            await dbContext.AddAsync(new ReservationOrderTicketCode
            {
                ReservationOrderId = reservationOrder.Id,
                TicketCodeId = ticketCode.Id
            });

            await dbContext.SetTenantId(tenantId).SaveChangesAsync();

            #endregion

            //act
            await service.RefundResult(new RefundResultMessage
            {
                ExtRefundNo = "wx000",
                IsSuccess = true,
                RefundOrderId = refundOrder.Id
            });
            await dbContext.SaveChangesAsync();
            //1，退款单状态必须变更为Refunded
            //2，退款成功日志
            //3，预约单状态Refunded
            //4，券码状态WaitingForReservation
            Assert.True(dbContext.RefundOrders.IgnoreQueryFilters()
                .Any(s => s.Id == refundOrder.Id && s.Status == RefundOrderStatus.Refunded));
            Assert.True(dbContext.OrderLogs.IgnoreQueryFilters()
                .Any(s => s.OrderId == reservationOrder.BaseOrderId && s.OperationType == OrderOperationType.RefundSuccessed));
            Assert.True(dbContext.ReservationOrders.IgnoreQueryFilters()
                .Any(s => s.Id == reservationOrder.Id && s.Status == ReservationStatus.Refunded));
            Assert.True(dbContext.TicketCodes.IgnoreQueryFilters()
                .Any(s => s.Id == ticketCode.Id && s.Status == TicketCodeStatus.WaitingForReservation));
        }

        #endregion
    }
}
