using AutoMapper;
using Cit.Storage.Redis;
using Common.ServicesHttpClient;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.DTOs.LineProduct;
using Contracts.Common.Product.Enums;
using Contracts.Common.Tenant.DTOs.Supplier;
using Contracts.Common.Tenant.DTOs.Tenant;
using DotNetCore.CAP;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Product.Api.Infrastructure;
using Product.Api.Model;
using Product.Api.Services;
using Product.Api.Services.Interfaces;
using Product.Api.Services.OpenPlatform.Interfaces;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Product.UnitTest;

public class LineProductServiceTest : TestBase<CustomDbContext>
{
    private static readonly long TenantId = 897049819561590784;
    public LineProductService CreateService(
            CustomDbContext dbContext = null,
            IMapper mapper = null,
            ICapPublisher capPublisher = null,
            IBaseProductService baseProductService = null,
            IRedisClient redisClient = null,
            IOpenPlatformBaseService openPlatformBaseService = null,
            IOpenSupplierService openSupplierService = null)
    {
        return new LineProductService(
            dbContext,
            mapper,
            capPublisher,
            baseProductService,
            redisClient,
            openPlatformBaseService,
            openSupplierService);
    }

    [Fact(DisplayName = "创建产品")]
    public async Task Add() 
    {
        var dbContext = GetNewDbContext(TenantId); 
        var mapper = new MapperConfiguration(cfg =>
        {
            cfg.CreateMap<LineProduct, AddLineProductInput>().ReverseMap();
        }).CreateMapper();
        var httpClientFactory = GetHttpClientFactoryMock(
            new HttpResponseMessage
            {
                Content = new StringContent(JsonConvert.SerializeObject(new GetTenantSysConfigOutput
                {
                    CurrencyCode = Currency.CNY.ToString()
                }))
            },
            new HttpResponseMessage
            {
                Content = new StringContent(JsonConvert.SerializeObject(new GetSupplierOutput
                {
                    CurrencyCode = Currency.CNY.ToString()
                }))
            });
        var options = Options.Create(new ServicesAddress
        {
            Payment = "http://127.0.0.1/",
            Tenant = "http://127.0.0.1/"
        });
        var capPublisher = GetCapPublisher();
        var baseProductService = new BaseProductService(
            dbContext,
            httpClientFactory,
            options,
            capPublisher); 

        var service = CreateService(
            dbContext: dbContext,
            mapper: mapper,
            capPublisher: capPublisher,
            baseProductService: baseProductService);

        var input = new AddLineProductInput()
        {
            Title = "",
            ProductPictures = new List<string>(),
            ProductVideos = new List<string>(),
            RallyPoints = new List<RallyPointItem>() 
            {
                new RallyPointItem() 
                {
                    Time = new TimeSpan(18, 0, 0),
                    Address = "",
                    Longitude = 10,
                    Latitude = 1
                }
            }
        };
        var addResult = await service.Add(TenantId, input);

        var entity = await dbContext.LineProduct.FindAsync(addResult.ProductId);
        Assert.NotNull(entity);
    }

    [Fact(DisplayName = "修改产品")]
    public async Task Update()
    {
        var dbContext = GetNewDbContext(TenantId);
        var httpClientFactory = GetHttpClientFactoryMock(
            new HttpResponseMessage()
            {
                StatusCode = System.Net.HttpStatusCode.OK, Content = new StringContent("true", Encoding.UTF8)
            },
            new HttpResponseMessage
            {
                Content = new StringContent(JsonConvert.SerializeObject(new GetSupplierOutput
                {
                    CurrencyCode = Currency.CNY.ToString()
                }))
            });
        var options = Options.Create(new ServicesAddress() { Tenant = "http://127.0.0.1/" });
        var capPublisher = GetCapPublisher();
        var baseProductService = new BaseProductService(
            dbContext,
            httpClientFactory,
            options,
            capPublisher);

        var service = CreateService(
            dbContext: dbContext,
            capPublisher: capPublisher,
            baseProductService: baseProductService); 

        #region fake data

        var product = new LineProduct()
        {
            Title = "Old Title",
            Enabled = true
        };
        await dbContext.LineProduct.AddAsync(product);
        
        var rallyPoint1 = new LineProductRallyPoint()
        {
            LineProductId = product.Id,
            Time = new TimeSpan(18, 0, 0),
            Address = "上车点1"
        };
        rallyPoint1.SetLocation(10, 10);
        await dbContext.LineProductRallyPoint.AddAsync(rallyPoint1);

        var rallyPoint2 = new LineProductRallyPoint()
        {
            LineProductId = product.Id,
            Time = new TimeSpan(18, 0, 0),
            Address = "上车点2"
        };
        rallyPoint2.SetLocation(18, 18);
        await dbContext.LineProductRallyPoint.AddAsync(rallyPoint2);

        await dbContext.SaveChangesAsync();

        #endregion

        var input = new UpdateLineProductInput()
        {
            Id = product.Id,
            Title = "New Title",
            RallyPoints = new List<RallyPointItem>() 
            {
                new RallyPointItem(){ Id = rallyPoint1.Id, Address = "上车点1", Time = new TimeSpan(18, 0, 0), Latitude = 20, Longitude = 20  },
                new RallyPointItem(){ Address = "上车点2", Time = new TimeSpan(18, 0, 0), Latitude = 10, Longitude = 10  }
            }
        };
        await service.Update(TenantId, input);

        var entity = await dbContext.LineProduct.FindAsync(product.Id);
        Assert.Equal(input.Title, entity.Title);

        var rallyPoints = await dbContext.LineProductRallyPoint.Where(x => x.LineProductId == product.Id).ToListAsync();
        Assert.True(rallyPoints.Count == 2);
    }


    [Theory(DisplayName = "Vebk端搜索产品")]
    [ClassData(typeof(SearchLineProductInputData))]
    public async Task SearchByTenant(SearchLineProductInput input)
    {
        var dbContext = GetNewDbContext(TenantId);

        var service = CreateService(
            dbContext: dbContext);

        #region fake data

        var products = new List<LineProduct>()
        {
            new LineProduct()
            {
                Title = "产品1-售卖未开始",
                Enabled = false,
                SellingDateBegin = DateTime.Today.AddDays(3),
                SellingDateEnd = DateTime.Today.AddDays(5),
            },
            new LineProduct()
            {
                Title = "产品2-售卖已经开始",
                Enabled = true,
                SellingDateBegin = DateTime.Today.AddDays(-3),
                SellingDateEnd = DateTime.Today,
            },
            new LineProduct()
            {
                Title = "产品3-售卖已经结束",
                Enabled = true,
                SellingDateBegin = DateTime.Today.AddDays(-3),
                SellingDateEnd = DateTime.Today.AddDays(-1),
            },
            new LineProduct()
            {
                Title = "产品4-无配置售卖时间",
                Enabled = true
            }
        };
        await dbContext.LineProduct.AddRangeAsync(products);

        var redundantDatas = new List<ProductRedundantData>()
        {
            new ProductRedundantData()
            {
                ProductId = products[0].Id,
                MinPrice = 100
            },
            new ProductRedundantData()
            {
                ProductId = products[1].Id,
                MinPrice = 150
            },
            new ProductRedundantData()
            {
                ProductId = products[2].Id,
                MinPrice = 200
            },
            new ProductRedundantData()
            {
                ProductId = products[3].Id,
                MinPrice = 200
            }
        };
        await dbContext.ProductRedundantDatas.AddRangeAsync(redundantDatas);

        var groups = new List<Group>()
        {
            new Group(){ Name = "分组1" },
            new Group(){ Name = "分组2" }
        };
        await dbContext.Groups.AddRangeAsync(groups);

        var groupItems = new List<GroupItems>()
        {
            new GroupItems(){  ProductType = ProductType.Line, ProductId = products[0].Id, GroupId = groups[0].Id},
            new GroupItems(){ ProductType = ProductType.Line, ProductId = products[1].Id, GroupId = groups[0].Id },
            new GroupItems(){  ProductType = ProductType.Line, ProductId = products[2].Id, GroupId = groups[1].Id},
            new GroupItems(){  ProductType = ProductType.Line, ProductId = products[3].Id, GroupId = groups[1].Id}
        };
        await dbContext.GroupItems.AddRangeAsync(groupItems);

        await dbContext.SaveChangesAsync();

        #endregion

        input.GroupIds = groups.Select(x => x.Id).ToList();
        var result = await service.Search(input);
        switch (input.SaleStatus)
        {
            case SaleStatus.None:
            case null:
                Assert.True(result.Total == products.Count);
                break;
            case SaleStatus.NotStarted:
                Assert.True(result.Data.First().Id == products[0].Id);
                break;
            case SaleStatus.InProgress:
                var ids = new List<long>
                {
                    products[1].Id,
                    products[3].Id
                };
                Assert.True(result.Data.All(x=>ids.Contains(x.Id)));
                break;
            case SaleStatus.Finished:
                Assert.True(result.Data.First().Id == products[2].Id);
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }
    }

    [Fact(DisplayName = "C端搜索产品")]
    public async Task SearchByCustomer()
    {
        var dbContext = GetNewDbContext(TenantId);

        var service = CreateService(
            dbContext: dbContext);

        #region fake data

        var products = new List<LineProduct>()
        {
            new LineProduct()
            {
                Title = "产品1",
                Enabled = false
            },
            new LineProduct()
            {
                Title = "产品2",
                SellingDateBegin = DateTime.Today,
                SellingDateEnd = DateTime.Today.AddDays(30),
                Enabled = true
            },
            new LineProduct()
            {
                Title = "产品3",
                SellingDateBegin = DateTime.Today.AddDays(10),
                SellingDateEnd = DateTime.Today.AddDays(30),
                Enabled = true
            }
        };
        await dbContext.LineProduct.AddRangeAsync(products);

        var redundantDatas = new List<ProductRedundantData>()
        {
            new ProductRedundantData()
            {
                ProductId = products[0].Id,
                MinPrice = 100
            },
            new ProductRedundantData()
            {
                ProductId = products[1].Id,
                MinPrice = 150
            },
            new ProductRedundantData()
            {
                ProductId = products[2].Id,
                MinPrice = 200
            }
        };
        await dbContext.ProductRedundantDatas.AddRangeAsync(redundantDatas);

        await dbContext.SaveChangesAsync();

        #endregion

        var input = new SearchLineProductInput()
        {
            PageIndex = 1,
            PageSize = 10,
            Enabled = true,
            SaleStatus = SaleStatus.InProgress
        };
        var result = await service.Search(input);

        Assert.True(result.Data.Count() == 1);

    }

    [Fact(DisplayName = "上下架")]
    public async Task SetEnabled() 
    {
        var dbContext = GetNewDbContext(TenantId);
        var httpClientFactory = GetHttpClientFactoryMock(new HttpResponseMessage()
        {
            StatusCode = System.Net.HttpStatusCode.OK,
            Content = new StringContent("true", Encoding.UTF8)
        });
        var options = Options.Create(new ServicesAddress() { Tenant = "http://127.0.0.1/" });
        var baseProductService = new BaseProductService(
            dbContext,
            httpClientFactory,
            options,
            null);

        var service = CreateService(
            dbContext: dbContext,
            baseProductService: baseProductService);

        #region fake data

        var product = new LineProduct()
        {
            Title = "测试产品上下架",
            SupplierId = 1000,
            Enabled = false
        };
        await dbContext.LineProduct.AddAsync(product);
        await dbContext.SaveChangesAsync();

        #endregion

        var input = new SetLineProductEnabledInput()
        {
            ProductIds = new List<long>() { product.Id },
            IsEnabled = true
        };

        await service.SetEnabled(input);

        var entity = await dbContext.LineProduct.FindAsync(product.Id);
        Assert.True(entity.Enabled);
    }

    [Fact(DisplayName = "获取产品城市")]
    public async Task GetCities()
    {
        var dbContext = GetNewDbContext(TenantId);
        var service = CreateService(
            dbContext: dbContext);

        #region fake data

        var products = new List<LineProduct>()
        {
            new LineProduct()
            {
                Title = "测试产品1",
                DepartureCountryId = 10,
                DepartureCountryName = "中国",
                DepartureCityId = 1000,
                DepartureCityName = "深圳",
                DestinationCountryId = 10,
                DestinationCountryName = "中国",
                DestinationCityId = 1001,
                DestinationCityName = "广州"
            },
            new LineProduct()
            {
                Title = "测试产品2",
                DepartureCountryId = 10,
                DepartureCountryName = "中国",
                DepartureCityId = 1000,
                DepartureCityName = "深圳",
                DestinationCountryId = 10,
                DestinationCountryName = "中国",
                DestinationCityId = 1002,
                DestinationCityName = "北京"
            },
            new LineProduct()
            {
                Title = "测试产品3",
                DepartureCountryId = 10,
                DepartureCountryName = "中国",
                DepartureCityId = 1001,
                DepartureCityName = "广州",
                DestinationCountryId = 10,
                DestinationCountryName = "中国",
                DestinationCityId = 1003,
                DestinationCityName = "上海"
            },
            new LineProduct()
            {
                Title = "测试产品4",
                DepartureCountryId = 10,
                DepartureCountryName = "中国",
                DepartureCityId = 1000,
                DepartureCityName = "深圳",
                DestinationCountryId = 10,
                DestinationCountryName = "中国",
                DestinationCityId = 1001,
                DestinationCityName = "广州"
            }
        };
        await dbContext.LineProduct.AddRangeAsync(products);
        await dbContext.SaveChangesAsync();

        #endregion

        var result = await service.GetCities(TenantId, false, false);

        Assert.True(result.DepartureCities.Count == 2);
        Assert.True(result.DestinationCities.Count == 3);
    }

    class SearchLineProductInputData : IEnumerable<object[]>
    {
        public IEnumerator<object[]> GetEnumerator()
        {
            yield return new object[]
            {
                new SearchLineProductInput
                {
                    PageIndex = 1,
                    PageSize = 10,
                    MinPrice = 50,
                    MaxPrice = 200,
                    SaleStatus = SaleStatus.NotStarted
                }
            };
            yield return new object[]
            {
                new SearchLineProductInput
                {
                    PageIndex = 1,
                    PageSize = 10,
                    MinPrice = 50,
                    MaxPrice = 200,
                    SaleStatus = SaleStatus.InProgress
                }
            };
            yield return new object[]
            {
                new SearchLineProductInput
                {
                    PageIndex = 1,
                    PageSize = 10,
                    MinPrice = 50,
                    MaxPrice = 200,
                    SaleStatus = SaleStatus.Finished
                }
            };
            yield return new object[]
            {
                new SearchLineProductInput
                {
                    PageIndex = 1,
                    PageSize = 10
                }
            };
        }

        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
    }
}
