using Common.Jwt;
using Contracts.Common.Product.DTOs.LineProductSku;
using Contracts.Common.Product.DTOs.LineProductSkuCalendarPrice;
using Microsoft.AspNetCore.Mvc;
using Product.Api.Services.Interfaces;
using static System.Net.HttpStatusCode;

namespace Product.Api.Controllers;

[ApiController]
[Route("[controller]/[action]")]
public class LineProductSkuController : ControllerBase
{
    private readonly ILineProductSkuService _lineProductSkuService;
    private readonly ILineProductSkuCalendarPriceService _lineProductSkuCalendarPriceService;
    public LineProductSkuController(
        ILineProductSkuService lineProductSkuService,
        ILineProductSkuCalendarPriceService lineProductSkuCalendarPriceService)
    {
        _lineProductSkuService = lineProductSkuService;
        _lineProductSkuCalendarPriceService = lineProductSkuCalendarPriceService;
    }

    /// <summary>
    /// 创建线路产品套餐
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(long),(int)OK)]
    public async Task<IActionResult> Add(AddLineProductSkuInput input)
    {
        var result = await _lineProductSkuService.Add(input);
        return Ok(result);
    }

    /// <summary>
    /// 编辑线路产品套餐
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)OK)]
    public async Task<IActionResult> Edit(EditLineProductSkuInput input)
    {
        await _lineProductSkuService.Edit(input);
        return Ok();
    }

    /// <summary>
    /// 查询线路产品套餐详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(GetLineProductSkuDetailOutput), (int)OK)]
    public async Task<IActionResult> Detail(long id)
    {
        var skuDetail = await _lineProductSkuService.Detail(id);
        var result = skuDetail.FirstOrDefault();
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<GetLineProductSkuDetailOutput>), (int)OK)]
    public async Task<IActionResult> GetByIds(params long[] ids)
    {
        var result = await _lineProductSkuService.Detail(ids);
        return Ok(result);
    }

    /// <summary>
    /// 通过产品id查询sku信息
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(List<GetLineProductSkuDetailOutput>), (int)OK)]
    public async Task<IActionResult> GetByProductId(long id)
    {
        var productIds = new List<long>() { id };
        var result = await _lineProductSkuService.GetByProductIds(new GetSkuByProductIdsInput
        {
            ProductIds = productIds
        });
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<GetSkuByProductIdsOutput>), (int)OK)]
    public async Task<IActionResult> GetByProductIds(GetSkuByProductIdsInput input)
    {
        var list = await _lineProductSkuService.GetByProductIds(input);

        if (input.HasPrice is true)
        {
            var prices = await _lineProductSkuCalendarPriceService.GetRecentMinOrMaxPrice(
                new GetRecentMinOrMaxPriceInput
                {
                    ProductIds = input.ProductIds
                });
            foreach (var item in list)
            {
                item.MinSellingPrice = prices.FirstOrDefault(x => x.ProductSkuId == item.Id)?.MinPrice;
            }
        }

        var result = input.ProductIds.Select(x => new GetSkuByProductIdsOutput()
        {
            ProductId = x,
            ProductSkus = list.Where(w => w.LineProductId == x).ToList()
        });

        return Ok(result);
    }

    /// <summary>
    /// 移除线路产品套餐
    /// </summary>
    /// <param name="id"></param>
    [HttpPost]
    [ProducesResponseType((int)OK)]
    public async Task<IActionResult> Remove(RemoveProductSkuInput input)
    {
        var productId = await _lineProductSkuService.Remove(input.Id);
        await _lineProductSkuCalendarPriceService.SetProductRedundantDataBase(productId);
        return Ok();
    }

    /// <summary>
    /// 线路产品套餐上下架切换
    /// </summary>
    /// <param name="input"></param>
    [HttpPost]
    [ProducesResponseType((int)OK)]
    public async Task<IActionResult> Switch(SwitchLineProductEnableInput input)
    {
        var productId = await _lineProductSkuService.Switch(input);
        await _lineProductSkuCalendarPriceService.SetProductRedundantDataBase(productId);
        return Ok();
    }

    /// <summary>
    /// 设置B2B售卖状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)OK)]
    public async Task<IActionResult> SetB2bSellingStatus(Contracts.Common.Product.DTOs.ProductSku.SetB2bSellingStatusInput input)
    {
        await _lineProductSkuService.SetB2bSellingStatus(input);
        return Ok();
    }

    [HttpPost]
    public async Task<IActionResult> UpdateApiContent(UpdateLineSkuApiContentInput input)
    {
        await _lineProductSkuService.UpdateApiContent(input);
        return Ok();
    }
    
    #region 分销商web

    /// <summary>
    /// 规格详情
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<GetLineProductSkuDetailOutput>), (int)OK)]
    public async Task<IActionResult> AgencyGet(AgencyGetLineProductSkuInput input)
    {
        input.AgencyId ??= HttpContext.GetCurrentUser().GetAgencyId();
        var result = await _lineProductSkuService.AgencyGet(input);
        return Ok(result);
    }

    #endregion
}