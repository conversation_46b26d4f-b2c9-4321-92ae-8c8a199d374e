using AutoMapper;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Payment.DTOs.Currency;
using Contracts.Common.Product.DTOs;
using Contracts.Common.Product.DTOs.AgencyChannelPriceSettings;
using Contracts.Common.Product.DTOs.LineProduct;
using Contracts.Common.Product.DTOs.LineProductOpenChannelSetting;
using Contracts.Common.Product.DTOs.LineProductOpenSupplierSetting;
using Contracts.Common.Product.DTOs.LineProductSkuCalendarPrice;
using Contracts.Common.Product.DTOs.LineProductSkuTypeItem;
using Contracts.Common.Product.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using Product.Api.Services.Interfaces;

namespace Product.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class LineProductController : ControllerBase
{
    private readonly ILineProductService _lineProductService;
    private readonly IBaseProductService _baseProductService;
    private readonly IAgencyChannelPriceSettingsService _channelPriceSettingsService;
    private readonly IMapper _mapper;
    private readonly ILineProductSkuCalendarPriceService _lineProductSkuCalendarPriceService;
    private readonly ILineProductSkuTypeItemService _lineProductSkuTypeItemService;

    public LineProductController(
        IMapper mapper,
        ILineProductService lineProductService,
        IBaseProductService baseProductService,
        IAgencyChannelPriceSettingsService channelPriceSettingsService,
        ILineProductSkuCalendarPriceService lineProductSkuCalendarPriceService,
        ILineProductSkuTypeItemService lineProductSkuTypeItemService) 
    {
        _mapper = mapper;
        _lineProductService = lineProductService;
        _baseProductService = baseProductService;
        _channelPriceSettingsService = channelPriceSettingsService;
        _lineProductSkuCalendarPriceService = lineProductSkuCalendarPriceService;
        _lineProductSkuTypeItemService = lineProductSkuTypeItemService;
    }

    /// <summary>
    /// 创建产品
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(AddLineProductOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default,ErrorTypes.Tenant.SupplierInvalid)]
    [SwaggerResponseExt(default,ErrorTypes.Product.GroupInvalid)]
    public async Task<IActionResult> Add(AddLineProductInput input)
    {
        var result = await _lineProductService.Add(HttpContext.GetTenantId(), input);
        return Ok(result);
    }

    /// <summary>
    /// 修改产品
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(UpdateLineProductOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default,ErrorTypes.Tenant.SupplierInvalid)]
    [SwaggerResponseExt(default,ErrorTypes.Product.GroupInvalid)]
    public async Task<IActionResult> Update(UpdateLineProductInput input) 
    {
        var result = await _lineProductService.Update(HttpContext.GetTenantId(), input);
        return Ok(result);
    }

    /// <summary>
    /// 批量移除线路产品
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> Remove(IEnumerable<long> ids)
    {
        await _lineProductService.Remove(ids);
        return Ok();
    }

    /// <summary>
    /// 获取产品
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(GetLineProductOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Get(long productId)
    {
        var result = await _lineProductService.Get(productId);
        return Ok(result);
    }

    /// <summary>
    /// 搜索产品
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchLineProductOutput, SearchLineProductSummary>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchLineProductInput input)
    {
        var result = await _lineProductService.Search(input);
        return Ok(result);
    }

    /// <summary>
    /// 产品上下架
    /// </summary>
    [HttpPost]
    [SwaggerResponseExt(default,ErrorTypes.Tenant.SupplierInvalid)]
    public async Task<IActionResult> SetEnabled(SetLineProductEnabledInput input)
    {
        await _lineProductService.SetEnabled(input);
        return Ok();
    }

    /// <summary>
    /// 获取产品首图
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(List<GetLineProductImageOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetImages(List<long> productIds) 
    {
        var photos = await _baseProductService.GetFirstPhoto(productIds);

        var result = productIds.Select(x => new GetLineProductImageOutput()
        {
            ProductId = x,
            ImgPath = photos.FirstOrDefault(w=> w.ProductId == x 
                && w.MediaType == MediaTypeOfPhotos.Picture)?.Path ?? ""
        });
        return Ok(result);
    }

    /// <summary>
    /// 获取产品城市
    /// </summary>
    /// <param name="isCache">是否取缓存</param>
    /// <param name="needAvailable">是否取可用产品的</param>
    [HttpGet]
    [ProducesResponseType(typeof(GetLineProductCitiesOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetCities(bool? isCache, bool? needAvailable)
    {
        if (!isCache.HasValue) isCache = true;
        if (!needAvailable.HasValue) needAvailable = false;

        var result = await _lineProductService.GetCities(HttpContext.GetTenantId(), isCache.Value, needAvailable.Value);
        return Ok(result);
    }

    /// <summary>
    /// 根据id列表获取产品
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<MallGetLineByIdsOutput>),(int) HttpStatusCode.OK)]
    public async Task<IActionResult> MallGetByIds(List<long> ids)
    {
        var result = await _lineProductService.MallGetByIds(ids);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<GetLineProductsAndSkuOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetProductsAndSku(GetProductsAndSkuInput input)
    {
        var result = await _lineProductService.GetProductsAndSku(input);
        return Ok(result);
    }

    /// <summary>
    /// 查询价格分组下的产品规格分页数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchLinePriceGroupProductOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchByPriceGroup(SearchPriceGroupProductInput input)
    {
        var result = new PagingModel<SearchLinePriceGroupProductOutput>();
        
        #region 查询价格分组产品数据

        var response = await _channelPriceSettingsService.Query(new QueryChannelPriceInput
        {
            PriceGroupId = input.PriceGroupId,
            ProductType = new[] {ChannelProductType.Line}
        });

        if (response.Any() is false) 
            return Ok(result);
        var settings = _mapper.Map<List<AgencyChannelPriceSettings>>(response);
        var skuIds = settings.Select(x => x.SkuId).Distinct();
        input.SkuIds = skuIds;
        
        #endregion
        
        var pageData = await _lineProductService.GetProductsAndSku(input);

        #region 汇率查询

        //查询汇率
        var getCurrencyRateInput = pageData.Data
            .GroupBy(x => new {x.SaleCurrencyCode, x.CostCurrencyCode})
            .Select(x => new GetExchangeRatesInput
            {
                BaseCurrencyCode = x.Key.CostCurrencyCode,
                TargetCurrencyCode = x.Key.SaleCurrencyCode
            })
            .ToList();
        var exchangeRateList = await _baseProductService.GetCurrencyExchangeRateList(getCurrencyRateInput);

        #endregion
        
        result.Total = pageData.Total;
        result.PageIndex = pageData.PageIndex;
        result.PageSize = pageData.PageSize;
        result.Data = pageData.Data.Select(s =>
        {
            var skuSubClassPrices= new List<SkuSubClassPrice>();
            foreach (var typeValue in s.SkuSubClass)
            {
                var settingItems = settings
                    .Where(x => x.SkuId == s.SkuId && x.SkuSubClass == typeValue)
                    .ToList();
                
                if (settingItems.Any() is false) continue;
                foreach (var settingItem in settingItems)
                {
                    _ = Enum.TryParse(typeValue.ToString(), out LineSkuPriceType lineSkuPriceType);
                    var skuSubClassPricesItem = s.SkuSubClassPrices.FirstOrDefault(x => x.Type == lineSkuPriceType && x.SkuSubItemId == settingItem.SkuSubItemId);
                    var costToSaleExchangeRate = exchangeRateList
                        .First(x => x.BaseCurrencyCode == s.CostCurrencyCode
                                    && x.TargetCurrencyCode == s.SaleCurrencyCode).ExchangeRate;
                    skuSubClassPrices.Add(new SkuSubClassPrice
                    {
                        ChannelPrice = _baseProductService.CalcAgencyChannelPrice(settingItem, skuSubClassPricesItem?.SalePrice,
                            skuSubClassPricesItem?.CostPrice,costToSaleExchangeRate),
                        BasePriceType = settingItem.BasePriceType,
                        PriceSettingType = settingItem.PriceSettingType,
                        PriceSettingValue = settingItem.PriceSettingValue,
                        Type = lineSkuPriceType,
                        SkuSubItemId = settingItem.SkuSubItemId,
                        SkuSubItemName = skuSubClassPricesItem?.SkuSubItemName,
                        SalePrice = skuSubClassPricesItem?.SalePrice,
                        CostPrice = skuSubClassPricesItem?.CostPrice
                    });
                }
            }

            s.SkuSubClassPrices = skuSubClassPrices;
            return new SearchLinePriceGroupProductOutput
            {
                ProductId = s.ProductId,
                ProductName = s.ProductName,
                SkuId = s.SkuId,
                SkuName = s.SkuName,
                SupplierId = s.SupplierId,
                SkuSubClass = s.SkuSubClass,
                SkuSubClassPrices = s.SkuSubClassPrices,
                SaleCurrencyCode = s.SaleCurrencyCode,
                CostCurrencyCode = s.CostCurrencyCode
            };
        });

        return Ok(result);
    }

    /// <summary>
    /// 修改排序
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> SetSort(SetLineProductSortInput input)
    {
        await _lineProductService.SetSort(input);
        return Ok();
    }
    
    /// <summary>
    /// 获取线路产品最低价
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<GetMinPriceByProductOutput>),(int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetMinPrice(params long[] productIds)
    {
        var result = new List<GetMinPriceByProductOutput>();
        var recentPrice =
            await _lineProductSkuCalendarPriceService.GetRecentMinOrMaxPrice(
                new GetRecentMinOrMaxPriceInput
                {
                    ProductIds = productIds.ToList()
                });

        foreach (var productId in productIds)
        {
            var minPrice = recentPrice
                .Where(x => x.ProductId == productId)
                .Min(x => x.MinPrice);
            
            result.Add(new GetMinPriceByProductOutput
            {
                ProductId = productId,
                Price = minPrice
            });
        }

        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<GetLineProductSimpleInfoOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetSimpleInfo(params long[] productIds)
    {
        var lineProducts = await _lineProductService.GetLineProducts(productIds);
        var result = _mapper.Map<List<GetLineProductSimpleInfoOutput>>(lineProducts);
        return Ok(result);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateSupplierModuleSetting(UpdateLineProductSupplierModuleSettingInput input)
    {
        await _lineProductService.UpdateSupplierModuleSetting(input);
        return Ok();
    }

    [HttpGet]
    [ProducesResponseType(typeof(GetLineProductSupplierModuleSettingOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetSupplierModuleSetting(long lineProductId)
    {
        var result = await _lineProductService.GetSupplierModuleSetting(lineProductId);
        return Ok(result);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateOpenSupplierSetting(UpdateOpenSupplierSyncSettingInput input)
    {
        await _lineProductService.UpdateOpenSupplierSetting(input);
        return Ok();
    }

    [HttpGet]
    [ProducesResponseType(typeof(LineProductOpenSupplierSettingInfo), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetOpenSupplierSetting(long lineProductId)
    {
        var result = await _lineProductService.GetOpenSupplierSetting(lineProductId);
        return Ok(result);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateChannelModuleSetting(UpdateLineProductChannelModuleSettingInput input)
    {
        await _lineProductService.UpdateChannelModuleSetting(input);
        return Ok();
    }

    [HttpGet]
    [ProducesResponseType(typeof(GetLineProductChannelModuleSettingOutput),(int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetChannelModuleSetting(long lineProductId)
    {
        var result = await _lineProductService.GetChannelModuleSetting(lineProductId);
        return Ok(result);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateOpenChannelSetting(UpdateOpenChannelSyncSettingInput input)
    {
        var result =  await _lineProductService.UpdateOpenChannelSetting(input);
        if (result.PushEmptySettingInfos.Any())
        {
            //执行渠道配置推空
            await _lineProductSkuTypeItemService.RunPushEmptyToChannel(new ChannelPriceStockPushEmptyInput
            {
                LineProductId = input.LineProductId,
                PushEmptySettingInfos = result.PushEmptySettingInfos
            });
        }
        return Ok();
    }

    [HttpGet]
    [ProducesResponseType(typeof(List<LineProductOpenChannelSettingInfo>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetOpenChannelSetting(long lineProductId)
    {
        var result = await _lineProductService.GetOpenChannelSetting(lineProductId);
        return Ok(result);
    }

    [HttpPost]
    public async Task UpdateChannelTimelinessSetting(UpdateOpenChannelTimelinessSettingInput input)
    {
        await _lineProductService.UpdateChannelTimelinessSetting(input);
    }

    [HttpGet]
    [ProducesResponseType(typeof(GetOpenChannelTimelinessSettingOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetChannelTimelinessSetting(long lineProductId)
    {
        var result = await _lineProductService.GetChannelTimelinessSetting(lineProductId);
        return Ok(result);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateOperationModuleSetting(UpdateLineProductOperationModuleSettingInput input)
    {
        await _lineProductService.UpdateOperationModuleSetting(input);
        return Ok();
    }

    [HttpGet]
    [ProducesResponseType(typeof(GetLineProductOperationModuleSettingOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetOperationModuleSetting(long lineProductId)
    {
        var result = await _lineProductService.GetOperationModuleSetting(lineProductId);
        return Ok(result);
    }

    [HttpGet]
    [ProducesResponseType(typeof(GetCompensationLineOutput),200)]
    public async Task<IActionResult> GetCompensationLine()
    {
        var result = await _lineProductService.GetCompensationLine();
        return Ok(result);
    }
    
    #region 分销商Web

    /// <summary>
    /// 分销商web-线路列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<AgencySearchOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> AgencySearch(AgencySearchInput input)
    {
        input.AgencyId = HttpContext.GetCurrentUser().GetAgencyId();
        var result = await _lineProductService.AgencySearch(input);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(IEnumerable<AgencyGetMinPriceOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> AgencyGetMinPrice(AgencyGetMinPriceInput input)
    {
        var paging = await _lineProductService.AgencySearch(new AgencySearchInput
        {
            AgencyId= HttpContext.GetCurrentUser().GetAgencyId(),
            ProductIds = input.ProductIds,
            PageIndex = 1,
            PageSize = input.ProductIds.Count()
        });

        var result = paging.Data.Select(x =>
        {
            return new AgencyGetMinPriceOutput()
            {
                ProductId = x.ProductId,
                Price = x.Skus.Min(s => s.ChannelPrice)
            };
        });

        return Ok(result);
    }

    #endregion
}
