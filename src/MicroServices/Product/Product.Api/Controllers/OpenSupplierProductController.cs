using Cit.Storage.Redis;
using Common.Jwt;
using Contracts.Common.Order.Messages;
using Contracts.Common.Product.DTOs.OpenSupplier;
using Contracts.Common.Scenic.DTOs.OpenSupplier;
using DotNetCore.CAP;
using Microsoft.AspNetCore.Mvc;
using Product.Api.Services.Interfaces;

namespace Product.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class OpenSupplierProductController : ControllerBase
{
    private readonly IRedisClient _redisClient;
    private readonly IOpenSupplierProductService _openSupplierProductService;
    private readonly ILogger<OpenSupplierProductController> _logger;
    public OpenSupplierProductController(
        IRedisClient redisClient,
        IOpenSupplierProductService openSupplierProductService,
        ILogger<OpenSupplierProductController> logger)
    {
        _redisClient = redisClient;
        _openSupplierProductService = openSupplierProductService;
        _logger = logger;
    }

    [HttpPost]
    [ProducesResponseType(typeof(GetOpenSupplierProductDetailOutput),200)]
    public async Task<IActionResult> GetProductDetail(GetOpenSupplierProductDetailInput input)
    {
        var tenantId = HttpContext.GetTenantId();
        var result = await _openSupplierProductService.GetProductDetail(input, tenantId);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(GetOpenSupplierProductContentOutput),200)]
    public async Task<IActionResult> GetProductContent(GetOpenSupplierProductContentInput input)
    {
        var tenantId = HttpContext.GetTenantId();
        var result = await _openSupplierProductService.GetProductContent(input, tenantId);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<GetOpenSupplierBasicProductOutput>),200)]
    public async Task<IActionResult> GetBasicProducts(GetOpenSupplierBasicProductInput input)
    {
        var tenantId = HttpContext.GetTenantId();
        var result = await _openSupplierProductService.GetBasicProducts(input, tenantId);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<QueryOpenSupplierSkuExtraInfoOutput>),200)]
    public async Task<IActionResult> QuerySkuExtraInfo(QueryOpenSupplierSkuExtraInfoInput input)
    {
        var result = await _openSupplierProductService.QuerySkuExtraInfo(input);
        return Ok(result);
    }

    [HttpPost]
    public async Task<IActionResult> SyncExtraInfo(SyncOpenSupplierExtraInfoInput input)
    {
        var openSupplierType = input.OpenSupplierType.ToString().ToLowerInvariant();
        var lockSecret = Guid.NewGuid().ToString();
        var lockKey = $"sync:extrainfo:{openSupplierType}:{input.ProductId}";
        if (!string.IsNullOrEmpty(input.SkuId))
        {
            lockKey = $"sync:extrainfo:{openSupplierType}:{input.ProductId}:{input.SkuId}";
        }else if (!string.IsNullOrEmpty(input.OptionId))
        {
            lockKey = $"sync:extrainfo:{openSupplierType}:{input.ProductId}:{input.OptionId}";
        }

        try
        {
            await _redisClient.LockTakeWaitingAsync(lockKey, lockSecret, TimeSpan.FromSeconds(30));
            await _openSupplierProductService.SyncExtraInfo(input);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "同步供应商sku附加信息:{@Input}", input);
        }
        finally
        {
            await _redisClient.LockReleaseAsync(lockKey, lockSecret);
        }
        return Ok();
    }

    [HttpPost]
    public async Task<IActionResult> RetryMatchBasicProduct(RetryMatchBasicProductInput input)
    {
        var tenantId = HttpContext.GetTenantId();
        await _openSupplierProductService.RetryMatchBasicProduct(input, tenantId);
        return Ok();
    }
}