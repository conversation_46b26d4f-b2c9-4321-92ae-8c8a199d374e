using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Product.Api.Infrastructure.EntityConfigurations
{
    public class LineProductSkuEntityTypeConfiguration : TenantBaseConfiguration<Model.LineProductSku>, IEntityTypeConfiguration<Model.LineProductSku>
    {
        public void Configure(EntityTypeBuilder<Model.LineProductSku> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.LineProductId)
                .HasColumnType("bigint");

            builder.Property(s => s.Name)
                .HasColumnType("varchar(300)")
                .IsRequired();

            builder.Property(s => s.IncludedAccommodation)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.FeeIncludes)
                .HasColumnType("text")
                .IsRequired()
                .HasConversion(
                    mod => JsonConvert.SerializeObject(mod),
                    str => JsonConvert.DeserializeObject<List<FeeInclude>>(str))
                .Metadata
                .SetValueComparer(new ValueComparer<List<FeeInclude>>(
                    (c1, c2) => c1.SequenceEqual(c2),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()));

            builder.Property(s => s.Enabled)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.B2bSellingStatus)
                .HasColumnType("tinyint(1)");
            
            builder.Property(s => s.IsTimeSlot)
                .HasColumnType("tinyint(1)");
            
            builder.Property(s => s.TimeSlotName)
                .HasColumnType("varchar(200)");
            
            builder.Property(s => s.CostDiscountRate)
                .HasColumnType("decimal(18,2)");
            
            builder.Property(s => s.ActivityId)
                .HasColumnType("varchar(100)");
            
            builder.Property(s => s.PackageId)
                .HasColumnType("varchar(100)");
            
            builder.Property(s => s.TimeSlotId)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");
            
            builder.Property(s => s.FeeNote)
                .HasColumnType("text");

            builder.Property(s => s.FeeNotNote)
                .HasColumnType("text");
            
            builder.Property(s => s.ValidityDescription)
                .HasColumnType("text");
            
            builder.Property(s => s.Precautions)
                .HasColumnType("text");
            
            builder.Property(s => s.UsageInstructions)
                .HasColumnType("text");
            
            builder.Property(s => s.OtherInstructions)
                .HasColumnType("text");
            
            builder.Property(s => s.CancellationPolicy)
                .HasColumnType("text");
            
            builder.HasIndex(s => new { s.TenantId, s.LineProductId });
        }
    }
}
