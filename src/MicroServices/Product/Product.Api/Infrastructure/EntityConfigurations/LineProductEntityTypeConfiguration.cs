using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Product.Api.Infrastructure.EntityConfigurations
{
    public class LineProductEntityTypeConfiguration : TenantBaseConfiguration<Model.LineProduct>, IEntityTypeConfiguration<Model.LineProduct>
    {
        public void Configure(EntityTypeBuilder<Model.LineProduct> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.Title)
                .HasColumnType("varchar(50)")
                .IsRequired();

            builder.Property(s => s.EnTitle)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.SellPointDescribe)
                .HasColumnType("text");
            
            builder.Property(s => s.DepartureCountryId)
                .HasColumnType("int");
            
            builder.Property(s => s.DepartureCountryName)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.DepartureCityId)
                .HasColumnType("int");
            
            builder.Property(s => s.DepartureCityName)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.DestinationCountryId)
                .HasColumnType("int");
            
            builder.Property(s => s.DestinationCountryName)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.DestinationCityId)
                .HasColumnType("int");
            
            builder.Property(s => s.DestinationCityName)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.Days)
                .HasColumnType("int");
            
            builder.Property(s => s.Nights)
                .HasColumnType("int");
            
            builder.Property(s => s.AdultsAllowed)
                .HasColumnType("tinyint(1)");
            
            builder.Property(s => s.AdultsStandard)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.ChildrenAllowed)
                .HasColumnType("tinyint(1)");
            
            builder.Property(s => s.ChildrenStandard)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.BabyAllowed)
                .HasColumnType("tinyint(1)");
            
            builder.Property(s => s.BabyStandard)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.ElderlyAllowed)
                .HasColumnType("tinyint(1)");
            
            builder.Property(s => s.ElderlyStandard)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.Instructions)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.KindReminder)
               .HasColumnType("varchar(500)");

            builder.Property(s => s.OtherNote)
                .HasColumnType("text");
            
            builder.Property(s => s.FeeNote)
                .HasColumnType("text");

            builder.Property(s => s.FeeNotNote)
                .HasColumnType("text");

            builder.Property(s => s.Content)
                .HasColumnType("mediumtext");
            
            builder.Property(s => s.ReservationDaysInAdvance)
                .HasColumnType("int");
            
            builder.Property(s => s.ReservationTimeInAdvance)
                .HasColumnType("time");
            
            builder.Property(s => s.IsManualConfirm)
                .HasColumnType("tinyint(1)");
            
            builder.Property(s => s.IsSupportRefund)
                .HasColumnType("tinyint(1)");
            
            builder.Property(s => s.RefundRate)
                .HasColumnType("decimal(8,4)")
                .IsRequired(false);
            
            builder.Property(s => s.RefundBeforeTravelDateDay)
                .HasColumnType("int")
                .IsRequired(false);
            
            builder.Property(s => s.RefundTravelDateTime)
                .HasColumnType("time")
                .IsRequired(false);
            
            builder.Property(s => s.SellingDateBegin)
                .HasColumnType("datetime")
                .IsRequired(false);
            
            builder.Property(s => s.SellingDateEnd)
                .HasColumnType("datetime")
                .IsRequired(false);
            
            builder.Property(s => s.SupplierId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.Enabled)
                .HasColumnType("tinyint(1)");
            
            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");
            
            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime")
                .IsRequired(false);

            builder.Property(s => s.CostCurrencyCode)
                .HasColumnType("varchar(20)")
                .HasDefaultValue(Currency.CNY.ToString())
                .IsRequired();
            
            builder.Property(s => s.SaleCurrencyCode)
                .HasColumnType("varchar(20)")
                .HasDefaultValue(Currency.CNY.ToString())
                .IsRequired();

            builder.Property(s => s.Sort)
                .HasColumnType("int");

            builder.Property(s => s.IsDeleted)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.DevelopUserId)
              .HasColumnType("bigint");

            builder.Property(s => s.OperatorUserId)
            .HasColumnType("bigint");

            builder.Property(s => s.SupplierName)
                 .HasColumnType("varchar(128)");

            builder.Property(x => x.DestinationCoordinateType)
           .HasColumnType("tinyint");

            builder.Property(x => x.DestinationLocation)
               .HasColumnType("point");

            builder.Property(x => x.DestinationGooglePalceId)
               .HasColumnType("varchar(100)");

            builder.Property(x => x.AutoConfirm)
                    .HasColumnType("tinyint(1)");

            builder.Property(s => s.PurchaseSourceType)
                .HasColumnType("tinyint")
                .HasDefaultValue(LineProductPurchaseSourceType.OfflinePurchase);
            
            builder.Property(s => s.PriceInventoryType)
                .HasColumnType("tinyint")
                .HasDefaultValue(PriceInventoryType.DailyCalendar);

            builder.Property(s => s.PriceInventorySource)
                .HasColumnType("tinyint")
                .HasDefaultValue(PriceInventorySource.System);

            builder.Property(s => s.IsOpenTimeSlotInventory)
                .HasColumnType("tinyint(1)");
            
            builder.Property(s => s.B2bSellingStatus)
                .HasColumnType("tinyint(1)");
            
            builder.Property(s => s.CostDiscountRate)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.IsCompensation)
                .HasColumnType("tinyint(1)");
            
            builder.Property(s => s.IsChannelTimeliness)
                .HasColumnType("tinyint(1)");
            
            builder.Property(s => s.TimelinessChannelTypes)
                .HasColumnType("int");

            builder.Property(s => s.SupplierServiceInfoId)
               .HasColumnType("bigint");

            builder.Property(s => s.SupplierServiceInfoName)
              .HasColumnType("varchar(128)");

            builder.HasIndex(x => new { x.Sort, x.UpdateTime });
        }
    }
}
