using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Product.Api.Infrastructure.Migrations
{
    public partial class Add_LineProductSku_ApiSkuContent : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CancellationPolicy",
                table: "LineProductSku",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "FeeNotNote",
                table: "LineProductSku",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "FeeNote",
                table: "LineProductSku",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "OtherInstructions",
                table: "LineProductSku",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "Precautions",
                table: "LineProductSku",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "UsageInstructions",
                table: "LineProductSku",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ValidityDescription",
                table: "LineProductSku",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_LineProductSku_TenantId_LineProductId",
                table: "LineProductSku",
                columns: new[] { "TenantId", "LineProductId" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_LineProductSku_TenantId_LineProductId",
                table: "LineProductSku");

            migrationBuilder.DropColumn(
                name: "CancellationPolicy",
                table: "LineProductSku");

            migrationBuilder.DropColumn(
                name: "FeeNotNote",
                table: "LineProductSku");

            migrationBuilder.DropColumn(
                name: "FeeNote",
                table: "LineProductSku");

            migrationBuilder.DropColumn(
                name: "OtherInstructions",
                table: "LineProductSku");

            migrationBuilder.DropColumn(
                name: "Precautions",
                table: "LineProductSku");

            migrationBuilder.DropColumn(
                name: "UsageInstructions",
                table: "LineProductSku");

            migrationBuilder.DropColumn(
                name: "ValidityDescription",
                table: "LineProductSku");
        }
    }
}
