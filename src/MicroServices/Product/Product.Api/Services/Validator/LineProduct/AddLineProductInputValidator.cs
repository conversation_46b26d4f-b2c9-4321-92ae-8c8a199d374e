using Contracts.Common.Product.DTOs.LineProduct;
using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.Enums;
using FluentValidation;

namespace Product.Api.Services.Validator.LineProduct;

public class AddLineProductInputValidator : AbstractValidator<AddLineProductInput>
{
    public AddLineProductInputValidator()
    {
        RuleFor(x => x.Title).NotNull().Length(1, 50);
        RuleFor(x => x.EnTitle).Length(0, 100);
        RuleFor(x => x.SellPointDescribe).Length(0, 60000);
        RuleFor(x => x.ProductPictures).NotEmpty().Must(p => p.Count <= 20);
        
        RuleFor(x => x.DepartureCityId).NotEmpty();
        RuleFor(x => x.DepartureCityName).NotEmpty().Length(1, 20);
        RuleFor(x => x.DestinationCityId).NotEmpty().GreaterThan(0);
        RuleFor(x => x.DestinationCityName).NotEmpty();

        RuleFor(x => x.AdultsStandard).Length(0, 50);
        RuleFor(x => x.ChildrenStandard).Length(0, 50);
        RuleFor(x => x.BabyStandard).Length(0, 50);
        RuleFor(x => x.ElderlyStandard).Length(0, 50);

        RuleFor(x => x.RallyPoints).ForEach(x => x.SetValidator(new RallyPointValidtor()));

        RuleFor(x => x.ReservationDaysInAdvance).NotNull().GreaterThanOrEqualTo(0);
        RuleFor(x => x.ReservationTimeInAdvance).NotNull();
        RuleFor(x => x.IsSupportRefund).NotNull();
        RuleFor(x => x.RefundTravelDateTime)
            .NotNull()
            .When(x => x.IsSupportRefund);
        RuleFor(x => x.RefundBeforeTravelDateDay)
            .NotNull()
            .GreaterThanOrEqualTo(0)
            .When(x => x.IsSupportRefund);
        RuleFor(x => x.RefundRate)
            .NotNull()
            .GreaterThan(0)
            .LessThanOrEqualTo(1)
            .When(x => x.IsSupportRefund);

        RuleFor(x => x.OtherNote).Length(0, 60000);
        RuleFor(x => x.FeeNote).NotEmpty().Length(1, 60000);
        RuleFor(x => x.FeeNotNote).NotEmpty().Length(1, 60000);
    }
}
