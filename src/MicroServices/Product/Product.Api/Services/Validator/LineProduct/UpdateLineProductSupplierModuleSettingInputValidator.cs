using Contracts.Common.Product.DTOs.LineProduct;
using FluentValidation;

namespace Product.Api.Services.Validator.LineProduct;

public class UpdateLineProductSupplierModuleSettingInputValidator : AbstractValidator<UpdateLineProductSupplierModuleSettingInput>
{
    public UpdateLineProductSupplierModuleSettingInputValidator()
    {
        RuleFor(x => x.LineProductId).NotEmpty();
        RuleFor(x => x.SupplierId).NotEmpty();
        RuleFor(x => x.CostDiscountRate)
            .GreaterThanOrEqualTo(0)
            .LessThanOrEqualTo(100);
    }
}