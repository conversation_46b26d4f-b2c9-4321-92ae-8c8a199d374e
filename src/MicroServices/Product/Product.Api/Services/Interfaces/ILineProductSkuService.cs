using Contracts.Common.Product.DTOs.LineProductSku;
using Contracts.Common.Product.DTOs.ProductSku;

namespace Product.Api.Services.Interfaces;

public interface ILineProductSkuService
{
    Task<long> Add(AddLineProductSkuInput input);
    Task Edit(EditLineProductSkuInput input);
    Task<List<GetLineProductSkuDetailOutput>> Detail(params long[] ids);
    Task<long> Remove(long id);
    Task<long> Switch(SwitchLineProductEnableInput input);
    Task<List<GetLineProductSkuDetailOutput>> GetByProductIds(GetSkuByProductIdsInput input);
    Task<List<GetLineProductSkuDetailOutput>> AgencyGet(AgencyGetLineProductSkuInput input);
    /// <summary>
    /// 设置B2B售卖状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SetB2bSellingStatus(SetB2bSellingStatusInput input);

    /// <summary>
    /// 更新api内容信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateApiContent(UpdateLineSkuApiContentInput input);
}