using Contracts.Common.Product.DTOs.OpenSupplier;
using Contracts.Common.Scenic.DTOs.OpenSupplier;

namespace Product.Api.Services.Interfaces;

public interface IOpenSupplierProductService
{
    /// <summary>
    /// 查询产品详情
    /// </summary>
    /// <param name="input"></param>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task<GetOpenSupplierProductDetailOutput> GetProductDetail(GetOpenSupplierProductDetailInput input,
        long tenantId);

    /// <summary>
    /// 查询产品内容信息
    /// </summary>
    /// <param name="input"></param>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task<GetOpenSupplierProductContentOutput> GetProductContent(GetOpenSupplierProductContentInput input,
        long tenantId);

    /// <summary>
    /// 查询基础产品数据
    /// </summary>
    /// <param name="input"></param>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task<List<GetOpenSupplierBasicProductOutput>> GetBasicProducts(GetOpenSupplierBasicProductInput input,
        long tenantId);


    /// <summary>
    /// 查询供应端sku附加信息数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<QueryOpenSupplierSkuExtraInfoOutput>> QuerySkuExtraInfo(QueryOpenSupplierSkuExtraInfoInput input);
    
    /// <summary>
    /// 同步维护供应端sku附加信息数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SyncExtraInfo(SyncOpenSupplierExtraInfoInput input);

    /// <summary>
    /// 重试匹配基础产品数据
    /// </summary>
    /// <param name="input"></param>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task RetryMatchBasicProduct(RetryMatchBasicProductInput input, long tenantId);
}