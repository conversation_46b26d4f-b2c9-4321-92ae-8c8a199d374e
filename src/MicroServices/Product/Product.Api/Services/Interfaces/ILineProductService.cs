using Contracts.Common.Product.DTOs;
using Contracts.Common.Product.DTOs.LineProduct;
using Contracts.Common.Product.DTOs.LineProductOpenChannelSetting;
using Contracts.Common.Product.DTOs.LineProductOpenSupplierSetting;
using EfCoreExtensions.Abstract;

namespace Product.Api.Services.Interfaces;

public interface ILineProductService
{
    /// <exception cref="ErrorTypes.Tenant.SupplierInvalid"></exception>
    /// <exception cref="ErrorTypes.Product.GroupInvalid"></exception>
    Task<AddLineProductOutput> Add(long tenantId, AddLineProductInput input);
    
    /// <exception cref="ErrorTypes.Tenant.SupplierInvalid"></exception>
    /// <exception cref="ErrorTypes.Product.GroupInvalid"></exception>
    Task<UpdateLineProductOutput> Update(long tenantId, UpdateLineProductInput input);

    /// <summary>
    /// 更新线路产品供应商模块设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateSupplierModuleSetting(UpdateLineProductSupplierModuleSettingInput input);
    
    /// <summary>
    /// 获取线路产品供应商模块设置
    /// </summary>
    /// <param name="lineProductId"></param>
    /// <returns></returns>
    Task<GetLineProductSupplierModuleSettingOutput> GetSupplierModuleSetting(long lineProductId);
    Task<LineProductOpenSupplierSettingInfo> GetOpenSupplierSetting(long lineProductId);
    Task UpdateOpenSupplierSetting(UpdateOpenSupplierSyncSettingInput input);

    /// <summary>
    /// 更新线路产品渠道模块设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateChannelModuleSetting(UpdateLineProductChannelModuleSettingInput input);
    
    /// <summary>
    /// 获取线路产品渠道模块设置
    /// </summary>
    /// <param name="lineProductId"></param>
    /// <returns></returns>
    Task<GetLineProductChannelModuleSettingOutput> GetChannelModuleSetting(long lineProductId);
    
    Task<UpdateOpenChannelSyncSettingOutput> UpdateOpenChannelSetting(UpdateOpenChannelSyncSettingInput input);
    Task<List<LineProductOpenChannelSettingInfo>> GetOpenChannelSetting(long lineProductId);

    Task UpdateChannelTimelinessSetting(UpdateOpenChannelTimelinessSettingInput input);
    Task<GetOpenChannelTimelinessSettingOutput> GetChannelTimelinessSetting(long lineProductId);

    /// <summary>
    /// 更新线路产品运营模块设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateOperationModuleSetting(UpdateLineProductOperationModuleSettingInput input);

    /// <summary>
    /// 获取线路产品运营模块设置
    /// </summary>
    /// <param name="lineProductId"></param>
    /// <returns></returns>
    Task<GetLineProductOperationModuleSettingOutput> GetOperationModuleSetting(long lineProductId);
    
    Task<GetLineProductOutput> Get(long productId);
    Task<List<LineProduct>> GetLineProducts(params long[] productIds);
    Task<PagingModel<SearchLineProductOutput, SearchLineProductSummary>> Search(SearchLineProductInput input);
    
    /// <exception cref="ErrorTypes.Tenant.SupplierInvalid"></exception>
    Task SetEnabled(SetLineProductEnabledInput input);
    Task<GetLineProductCitiesOutput> GetCities(long tenantId, bool isCache, bool needAvailable);
    Task<List<MallGetLineByIdsOutput>> MallGetByIds(List<long> ids);
    Task<PagingModel<GetLineProductsAndSkuOutput>> GetProductsAndSku(GetProductsAndSkuInput input);
    Task<PagingModel<AgencySearchOutput>> AgencySearch(AgencySearchInput input);
    Task SetSort(SetLineProductSortInput input);

    /// <summary>
    /// 移除线路产品
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    Task Remove(IEnumerable<long> ids);

    /// <summary>
    /// 查询补差线路产品
    /// </summary>
    /// <returns></returns>
    Task<GetCompensationLineOutput> GetCompensationLine();
}
