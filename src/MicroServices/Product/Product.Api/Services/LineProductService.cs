using AutoMapper;
using Cit.Storage.Redis;
using Common.GlobalException;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Marketing.DTOs.FlashSale;
using Contracts.Common.Marketing.Enums;
using Contracts.Common.Payment.DTOs.Currency;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.DTOs;
using Contracts.Common.Product.DTOs.LineProduct;
using Contracts.Common.Product.DTOs.LineProductOpenChannelSetting;
using Contracts.Common.Product.DTOs.LineProductOpenSupplierSetting;
using Contracts.Common.Product.DTOs.ProductOperatorUser;
using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.Enums;
using Contracts.Common.Tenant.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Microsoft.EntityFrameworkCore;
using Product.Api.Services.Interfaces;
using Product.Api.Services.OpenPlatform.Contracts.Supplier;
using Product.Api.Services.OpenPlatform.Interfaces;
using System.Text.RegularExpressions;

namespace Product.Api.Services;

public class LineProductService : ILineProductService
{
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly ICapPublisher _capPublisher;
    private readonly IBaseProductService _baseProductService;
    private readonly IRedisClient _redisClient;
    private readonly IOpenPlatformBaseService _openPlatformBaseService;
    private readonly IOpenSupplierService _openSupplierService;

    public LineProductService(
        CustomDbContext dbContext,
        IMapper mapper,
        ICapPublisher capPublisher,
        IBaseProductService baseProductService,
        IRedisClient redisClient,
        IOpenPlatformBaseService openPlatformBaseService,
        IOpenSupplierService openSupplierService)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _capPublisher = capPublisher;
        _baseProductService = baseProductService;
        _redisClient = redisClient;
        _openPlatformBaseService = openPlatformBaseService;
        _openSupplierService = openSupplierService;
    }

    /// <summary>
    /// 创建
    /// </summary>
    [UnitOfWork]
    public async Task<AddLineProductOutput> Add(long tenantId, AddLineProductInput input)
    {
        var result = new AddLineProductOutput();
        await _baseProductService.GroupValidator(input.GroupIds);
        var tenantInfo = await _baseProductService.GetTenantSysConfig();
        //基础信息
        if (!string.IsNullOrWhiteSpace(input.Content))
            input.Content = Regex.Replace(input.Content, @"<script[^>]*?>.*?</script>", "", RegexOptions.IgnoreCase);
        var product = _mapper.Map<LineProduct>(input);
        product.SaleCurrencyCode = tenantInfo.CurrencyCode;
        if (input.DestinationLongitude.HasValue && input.DestinationLatitude.HasValue)
            product.SetLocation(input.DestinationLongitude.Value, input.DestinationLatitude.Value);

        product.CostCurrencyCode = Currency.CNY.ToString();
        product.SupplierName = string.Empty;
        product.PriceInventoryType = PriceInventoryType.DailyCalendar;//价库类型
        product.PriceInventorySource = PriceInventorySource.System;//价库来源
        product.Enabled = false;

        result.ProductId = product.Id;
        await _dbContext.LineProduct.AddAsync(product);

        //分组
        if (input.GroupIds != null && input.GroupIds.Any())
            await _baseProductService.Add(ProductType.Line, product.Id, input.GroupIds);

        //冗余数据
        var productRedundantData = new ProductRedundantData()
        {
            ProductId = product.Id
        };
        await _dbContext.ProductRedundantDatas.AddAsync(productRedundantData);

        //图片/视频
        var addPhotos = new List<ProductPhotos>();
        var method = new AddProductPhotosMethod(product.Id, addPhotos);
        method.Execute(MediaTypeOfPhotos.Picture, input.ProductPictures);
        method.Execute(MediaTypeOfPhotos.Video, input.ProductVideos);
        await _dbContext.ProductPhotos.AddRangeAsync(addPhotos);

        //上车点设置
        var rallyPoints = new List<LineProductRallyPoint>();
        foreach (var item in input.RallyPoints)
        {
            var rallyPoint = new LineProductRallyPoint()
            {
                LineProductId = product.Id,
                Time = item.Time,
                Address = item.Address,
                CoordinateType = item.CoordinateType
            };
            if (item.Longitude.HasValue && item.Latitude.HasValue)
                rallyPoint.SetLocation(item.Longitude.Value, item.Latitude.Value);
            rallyPoints.Add(rallyPoint);
        }
        await _dbContext.LineProductRallyPoint.AddRangeAsync(rallyPoints);

        await _dbContext.SaveChangesAsync();

        return result;
    }

    /// <summary>
    /// 修改
    /// </summary>
    [UnitOfWork]
    public async Task<UpdateLineProductOutput> Update(long tenantId, UpdateLineProductInput input)
    {
        var result = new UpdateLineProductOutput
        {
            ProductId = input.Id
        };

        await _baseProductService.GroupValidator(input.GroupIds);

        //基础信息
        if (!string.IsNullOrWhiteSpace(input.Content))
            input.Content = Regex.Replace(input.Content, @"<script[^>]*?>.*?</script>", "", RegexOptions.IgnoreCase);
        var product = await _dbContext.LineProduct.FindAsync(input.Id);
        product.Title = input.Title;
        product.EnTitle = input.EnTitle;
        product.SellPointDescribe = input.SellPointDescribe;
        product.DepartureCountryId = input.DepartureCountryId;
        product.DepartureCountryName = input.DepartureCountryName;
        product.DepartureCityId = input.DepartureCityId;
        product.DepartureCityName = input.DepartureCityName;
        product.DestinationCountryId = input.DestinationCountryId;
        product.DestinationCountryName = input.DestinationCountryName;
        product.DestinationCityId = input.DestinationCityId;
        product.DestinationCityName = input.DestinationCityName;
        product.AdultsStandard = input.AdultsStandard;
        product.ChildrenStandard = input.ChildrenStandard;
        product.BabyStandard = input.BabyStandard;
        product.ElderlyStandard = input.ElderlyStandard;
        product.ReservationDaysInAdvance = input.ReservationDaysInAdvance;
        product.ReservationTimeInAdvance = input.ReservationTimeInAdvance;
        product.IsSupportRefund = input.IsSupportRefund;
        product.RefundBeforeTravelDateDay = input.RefundBeforeTravelDateDay;
        product.RefundTravelDateTime = input.RefundTravelDateTime;
        product.RefundRate = input.RefundRate;
        product.Content = input.Content;
        product.UpdateTime = DateTime.Now;
        product.OtherNote = input.OtherNote;
        product.FeeNote = input.FeeNote;
        product.FeeNotNote = input.FeeNotNote;
        product.DestinationCoordinateType = input.DestinationCoordinateType;
        product.DestinationGooglePalceId = input.DestinationGooglePalceId;
        product.AutoConfirm = input.AutoConfirm;
        product.PurchaseSourceType = input.PurchaseSourceType;

        if (input.DestinationLongitude.HasValue && input.DestinationLatitude.HasValue)
            product.SetLocation(input.DestinationLongitude.Value, input.DestinationLatitude.Value);

        //分组
        await _baseProductService.Update(ProductType.Line, product.Id, input.GroupIds);

        //图片/视频/海报
        var oldAllPhotos = await _dbContext.ProductPhotos
            .Where(x => x.ProductId == product.Id)
            .ToListAsync();
        var addPhotos = new List<ProductPhotos>();
        var method = new UpdateProductPhotosMethod(product.Id, oldAllPhotos, addPhotos);
        method.Execute(MediaTypeOfPhotos.Picture, input.ProductPictures);
        method.Execute(MediaTypeOfPhotos.Video, input.ProductVideos);

        if (addPhotos.Any())
        {
            await _dbContext.ProductPhotos.AddRangeAsync(addPhotos);
        }

        #region  修改上车点

        var oldRallyPoints = await _dbContext.LineProductRallyPoint
            .Where(x => x.LineProductId == product.Id)
            .ToListAsync();

        var rallyPointIds = input.RallyPoints.Select(x => x.Id).ToList();
        var removeRallyPoints = oldRallyPoints.Where(x => !rallyPointIds.Contains(x.Id)).ToList();
        _dbContext.LineProductRallyPoint.RemoveRange(removeRallyPoints);

        var addRallyPoints = new List<LineProductRallyPoint>();
        foreach (var item in input.RallyPoints)
        {
            if (item.Id > 0)
            {
                var oldRallyPoint = oldRallyPoints.FirstOrDefault(x => x.Id == item.Id);
                oldRallyPoint.Time = item.Time;
                oldRallyPoint.Address = item.Address;
                oldRallyPoint.CoordinateType = item.CoordinateType;
                if (item.Longitude.HasValue && item.Latitude.HasValue)
                    oldRallyPoint.SetLocation(item.Longitude.Value, item.Latitude.Value);
            }
            else
            {
                var rallyPoint = new LineProductRallyPoint()
                {
                    LineProductId = product.Id,
                    Time = item.Time,
                    Address = item.Address,
                    CoordinateType = item.CoordinateType
                };
                if (item.Longitude.HasValue && item.Latitude.HasValue)
                    rallyPoint.SetLocation(item.Longitude.Value, item.Latitude.Value);
                addRallyPoints.Add(rallyPoint);
            }
        }
        await _dbContext.LineProductRallyPoint.AddRangeAsync(addRallyPoints);

        #endregion
        await _dbContext.SaveChangesAsync();

        return result;
    }

    #region 供应模块配置

    [UnitOfWork]
    public async Task UpdateSupplierModuleSetting(UpdateLineProductSupplierModuleSettingInput input)
    {
        var product = await _dbContext.LineProduct.FirstOrDefaultAsync(x => x.Id == input.LineProductId);
        var supplierInfo = await _baseProductService.GetSupplierInfo(input.SupplierId);
        product.SupplierId = input.SupplierId;
        product.CostCurrencyCode = supplierInfo.CurrencyCode;
        product.SupplierName = supplierInfo.FullName;
        product.SupplierServiceInfoId = input.SupplierServiceInfoId;
        product.SupplierServiceInfoName = input.SupplierServiceInfoName;
        //API对接产品
        if (product.PurchaseSourceType == LineProductPurchaseSourceType.InterfaceDock)
        {
            if (supplierInfo.SupplierApiSetting == null)
                throw new BusinessException(ErrorTypes.Tenant.IsNotApiSupplier);
            if (supplierInfo.SupplierApiSetting.SupplierApiType == null)
                throw new BusinessException(ErrorTypes.Tenant.IsNotApiSupplier);
            if (string.IsNullOrEmpty(input.ActivityId))
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

            var convertSupplierApiType =
                _openPlatformBaseService.ConvertSupplierApiType(supplierInfo.SupplierApiSetting.SupplierApiType
                    .Value);

            product.AutoConfirm = false; //API对接不自动确认
            product.PriceInventoryType = PriceInventoryType.DailyCalendar; //价库类型
            product.PriceInventorySource = convertSupplierApiType.priceInventorySource; //价库来源

            #region 供应端产品校验初始化

            var initResult = await InitOpenSupplierProduct(input.ActivityId,
                convertSupplierApiType.openSupplierTypeStr,
                product.TenantId);
            product.CostDiscountRate = initResult.CommissionRate;

            #endregion
            
            #region 处理供应端配置

            var openSupplierSetting = await _dbContext.LineProductOpenSupplierSettings
                .FirstOrDefaultAsync(x => x.LineProductId == input.LineProductId);
            if (openSupplierSetting == null)
            {
                openSupplierSetting = new LineProductOpenSupplierSetting
                {
                    LineProductId = product.Id,
                    ActivityId = input.ActivityId,
                    PriceInventorySyncType = PriceInventorySyncType.NoNeedSync,
                    SyncDateRange = 90, // 初始化默认值
                    SyncInterval = 10
                };
                await _dbContext.AddAsync(openSupplierSetting);
            }
            else
            {
                //供应端配置更换移除旧配置的套餐
                if (openSupplierSetting.ActivityId != input.ActivityId)
                {
                    var oldSkus = await _dbContext.LineProductSku
                        .Where(x => x.LineProductId == product.Id)
                        .ToListAsync();
                    if (oldSkus.Any())
                    {
                        _dbContext.RemoveRange(oldSkus);
                    }

                    var oldSkuTypeItems = await _dbContext.LineProductSkuTypeItems
                        .Where(x => x.LineProductId == product.Id)
                        .ToListAsync();
                    if (oldSkuTypeItems.Any())
                    {
                        _dbContext.RemoveRange(oldSkuTypeItems);
                    }
                }

                openSupplierSetting.ActivityId = input.ActivityId;
                openSupplierSetting.UpdateTime = DateTime.Now;
            }

            #endregion
        }

        if (product.PurchaseSourceType == LineProductPurchaseSourceType.OfflinePurchase)
        {
            product.CostDiscountRate = input.CostDiscountRate;
        }
    }
    
    public async Task<GetLineProductSupplierModuleSettingOutput> GetSupplierModuleSetting(long lineProductId)
    {
        var result = new GetLineProductSupplierModuleSettingOutput
        {
            LineProductId = lineProductId
        };
        var product = await _dbContext.LineProduct.AsNoTracking().FirstOrDefaultAsync(x => x.Id == lineProductId);
        if (product == null) return result;
        
        result.SupplierId = product.SupplierId;
        result.SupplierServiceInfoId = product.SupplierServiceInfoId;
        result.SupplierServiceInfoName = product.SupplierServiceInfoName;
        result.PurchaseSourceType = product.PurchaseSourceType;
        result.CostDiscountRate = product.CostDiscountRate;

        var openSupplierSetting = await _dbContext.LineProductOpenSupplierSettings.AsNoTracking()
            .FirstOrDefaultAsync(x => x.LineProductId == lineProductId);
        result.ActivityId = openSupplierSetting?.ActivityId;
        
        return result;
    }
    

    [UnitOfWork]
    public async Task UpdateOpenSupplierSetting(UpdateOpenSupplierSyncSettingInput input)
    {
        //供应端配置更新
        var openSupplierSetting = await _dbContext.LineProductOpenSupplierSettings
            .FirstOrDefaultAsync(x => x.LineProductId == input.LineProductId);
        if (openSupplierSetting != null)
        {
            openSupplierSetting.PriceInventorySyncType = input.PriceInventorySyncType;
            openSupplierSetting.SyncDateRange = input.SyncDateRange;
            openSupplierSetting.SyncInterval = input.SyncInterval;
            openSupplierSetting.UpdateTime = DateTime.Now;
        }
    }

    public async Task<LineProductOpenSupplierSettingInfo> GetOpenSupplierSetting(long lineProductId)
    {
        var openSupplierSetting = await _dbContext.LineProductOpenSupplierSettings.AsNoTracking()
            .FirstOrDefaultAsync(x => x.LineProductId == lineProductId);
        if (openSupplierSetting == null) return null;
        var result = new LineProductOpenSupplierSettingInfo()
        {
            LineProductId = openSupplierSetting.LineProductId,
            ActivityId = openSupplierSetting.ActivityId,
            PriceInventorySyncType = openSupplierSetting.PriceInventorySyncType,
            SyncDateRange = openSupplierSetting.SyncDateRange,
            SyncInterval = openSupplierSetting.SyncInterval
        };
        return result;
    }

    #endregion

    #region 渠道模块配置

    [UnitOfWork]
    public async Task UpdateChannelModuleSetting(UpdateLineProductChannelModuleSettingInput input)
    {
        var product = await _dbContext.LineProduct.FirstOrDefaultAsync(x => x.Id == input.LineProductId);
        if (input.Enabled)
            await _baseProductService.SupplierValidator(new List<long>() { product.SupplierId });

        product.Enabled = input.Enabled;
        product.SellingDateBegin = input.SellingDateBegin;
        product.SellingDateEnd = input.SellingDateEnd;
        product.UpdateTime = DateTime.Now;
    }

    public async Task<GetLineProductChannelModuleSettingOutput> GetChannelModuleSetting(long lineProductId)
    {
        var result = new GetLineProductChannelModuleSettingOutput
        {
            LineProductId = lineProductId
        };
        var product = await _dbContext.LineProduct.AsNoTracking().FirstOrDefaultAsync(x => x.Id == lineProductId);
        result.Enabled = product?.Enabled ?? false;
        result.SellingDateBegin = product?.SellingDateBegin;
        result.SellingDateEnd = product?.SellingDateEnd;
        return result;
    }

    [UnitOfWork]
    public async Task<UpdateOpenChannelSyncSettingOutput> UpdateOpenChannelSetting(UpdateOpenChannelSyncSettingInput input)
    {
        var result = new UpdateOpenChannelSyncSettingOutput();

        var lineProduct = await _dbContext.LineProduct.AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == input.LineProductId);
        if (lineProduct.PurchaseSourceType != LineProductPurchaseSourceType.InterfaceDock)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        var dbSettingInfos = await _dbContext.LineProductOpenChannelSettings
            .Where(x => x.LineProductId == input.LineProductId)
            .Where(x => x.SettingType == OpenChannelSettingType.PriceInventorySync)
            .ToListAsync();
        var dbSettingInfoItems = await _dbContext.LineProductOpenChannelSettingItems
            .Where(x => x.LineProductId == input.LineProductId)
            .ToListAsync();

        //移除旧配置
        if (dbSettingInfos.Any())
            _dbContext.RemoveRange(dbSettingInfos);

        if (dbSettingInfoItems.Any())
            _dbContext.RemoveRange(dbSettingInfoItems);

        //新旧配置对比
        var oldSettingInfos = (from item in dbSettingInfos
            let currentSyncSkuIds = dbSettingInfoItems.Where(x => x.LineProductOpenChannelSettingId == item.Id)
                .Select(x => x.SyncSkuId)
                .ToList()
            select new LineProductOpenChannelSettingInfo
            {
                LineProductId = item.LineProductId,
                PriceInventorySyncChannelType = item.PriceInventorySyncChannelType,
                ChannelProductId = item.ChannelProductId,
                SupplierProductId = item.SupplierProductId,
                Area = item.Area,
                PriceInventorySyncType = item.PriceInventorySyncType,
                SyncSkuIds = currentSyncSkuIds,
                ZeroStockThreshold = item.ZeroStockThreshold
            }).ToList();
        var compareResult = ChannelPushEmptyConfigComparator(oldSettingInfos, input.SettingInfos);

        //保存新配置
        await SaveOpenChannelSettings(input.LineProductId, input.SettingInfos);

        result.PushEmptySettingInfos = compareResult;
        return result;
    }

    public async Task<List<LineProductOpenChannelSettingInfo>> GetOpenChannelSetting(long lineProductId)
    {
        var result = new List<LineProductOpenChannelSettingInfo>();
        var openChannelSettings = await _dbContext.LineProductOpenChannelSettings.AsNoTracking()
            .Where(x => x.LineProductId == lineProductId)
            .Where(x => x.SettingType == OpenChannelSettingType.PriceInventorySync)
            .ToListAsync();
        var openChannelSettingItems = await _dbContext.LineProductOpenChannelSettingItems.AsNoTracking()
            .Where(x => x.LineProductId == lineProductId)
            .ToListAsync();
        foreach (var item in openChannelSettings)
        {
            var currentSyncSkuIds = openChannelSettingItems
                .Where(x => x.LineProductOpenChannelSettingId == item.Id)
                .Select(x => x.SyncSkuId).ToList();

            result.Add(
                new LineProductOpenChannelSettingInfo
                {
                    LineProductId = item.LineProductId,
                    PriceInventorySyncChannelType = item.PriceInventorySyncChannelType,
                    ChannelProductId = item.ChannelProductId,
                    SupplierProductId = item.SupplierProductId,
                    Area = item.Area,
                    PriceInventorySyncType = item.PriceInventorySyncType,
                    SyncSkuIds = currentSyncSkuIds,
                    ZeroStockThreshold = item.ZeroStockThreshold
                });
        }

        return result;
    }

    [UnitOfWork]
    public async Task UpdateChannelTimelinessSetting(UpdateOpenChannelTimelinessSettingInput input)
    {
        var lineProduct = await _dbContext.LineProduct.FirstOrDefaultAsync(x => x.Id == input.LineProductId);
        var dbSettingInfos = await _dbContext.LineProductOpenChannelSettings
            .Where(x => x.LineProductId == input.LineProductId)
            .Where(x => x.SettingType == OpenChannelSettingType.TimelinessSync)
            .ToListAsync();

        //移除旧配置
        if (dbSettingInfos.Any())
            _dbContext.RemoveRange(dbSettingInfos);

        //更新产品冗余
        lineProduct.IsChannelTimeliness = input.IsChannelTimeliness;
        lineProduct.TimelinessChannelTypes = input.TimelinessChannelSettingInfos.Select(x => x.TimelinessChannelType)
            .Aggregate(new PriceInventorySyncChannelType(),
                (current, item) => current | item);

        //更新配置
        var timelinessSettings = new List<LineProductOpenChannelSetting>();
        foreach (var item in input.TimelinessChannelSettingInfos)
        {
            timelinessSettings.Add(new LineProductOpenChannelSetting
            {
                LineProductId = lineProduct.Id,
                SettingType = OpenChannelSettingType.TimelinessSync,
                PriceInventorySyncChannelType = item.TimelinessChannelType,
                PriceInventorySyncType = PriceInventorySyncType.NoNeedSync,
                TimelinessTriggerType = item.TimelinessTriggerType
            });
        }

        await _dbContext.AddRangeAsync(timelinessSettings);
    }

    public async Task<GetOpenChannelTimelinessSettingOutput> GetChannelTimelinessSetting(long lineProductId)
    {
        var result = new GetOpenChannelTimelinessSettingOutput();
        var lineProduct = await _dbContext.LineProduct.AsNoTracking().FirstOrDefaultAsync(x => x.Id == lineProductId);
        result.IsChannelTimeliness = lineProduct.IsChannelTimeliness;
        var openChannelSettings = await _dbContext.LineProductOpenChannelSettings.AsNoTracking()
            .Where(x => x.LineProductId == lineProductId)
            .Where(x => x.SettingType == OpenChannelSettingType.TimelinessSync)
            .ToListAsync();
        result.TimelinessChannelSettingInfos =
            openChannelSettings.Select(x => new TimelinessChannelSettingInfo
            {
                TimelinessChannelType = x.PriceInventorySyncChannelType,
                TimelinessTriggerType = x.TimelinessTriggerType!.Value
            }).ToList();
        return result;
    }

    #endregion

    #region 运营信息模块

    [UnitOfWork]
    public async Task UpdateOperationModuleSetting(UpdateLineProductOperationModuleSettingInput input)
    {
        var product = await _dbContext.LineProduct.FirstOrDefaultAsync(x => x.Id == input.LineProductId);
        if (product == null) throw new BusinessException(ErrorTypes.Common.ProductInvalid);
        product.DevelopUserId = input.DevelopUserId;
        
        //同平台运营人只能一个
        if (input.ProductOperatorUser.Any() && input.ProductOperatorUser.GroupBy(x => x.SellingPlatform).Any(x => x.Count() > 1))
            throw new BusinessException(ErrorTypes.Product.OperatorUserPlatformCannotMultiple);
        
        //海报
        var oldAllPhotos = await _dbContext.ProductPhotos
            .Where(x => x.ProductId == product.Id)
            .ToListAsync();
        var addPhotos = new List<ProductPhotos>();
        var method = new UpdateProductPhotosMethod(product.Id, oldAllPhotos, addPhotos);
        method.Execute(MediaTypeOfPhotos.Poster, input.ProductPosters);
        if (addPhotos.Any())
        {
            await _dbContext.ProductPhotos.AddRangeAsync(addPhotos);
        }
        
        // 平台运营人
        var oldOperatorUsers = await _dbContext.ProductOperatorUsers
            .Where(x => x.ProductId == product.Id && x.ProductType == ProductType.Line)
            .ToListAsync();
        if(oldOperatorUsers.Any())
            _dbContext.ProductOperatorUsers.RemoveRange(oldOperatorUsers);
        var operatorUsers = input.ProductOperatorUser.Select(x => new ProductOperatorUser()
        {
            OperatorUserId = x.OperatorUserId,
            ProductId = product.Id,
            ProductType = ProductType.Line,
            SellingPlatform = x.SellingPlatform,
            OperatorAssistantUserId = x.OperatorAssistantUserId,
        });
        await _dbContext.ProductOperatorUsers.AddRangeAsync(operatorUsers);
        
        //设置售前售后客服
        var command = new SetProductSupportStaffMessage
        {
            TenantId = product.TenantId,
            ProductType = ProductType.Line,
            ProductId = product.Id,
            PreSaleStaff = input.PreSaleStaff,
            AfterSaleStaff = input.AfterSaleStaff
        };

        await _capPublisher.PublishAsync(CapTopics.Tenant.SetProductSupportStaff, command);
    }

    public async Task<GetLineProductOperationModuleSettingOutput> GetOperationModuleSetting(long lineProductId)
    {
        var result = new GetLineProductOperationModuleSettingOutput
        {
            LineProductId = lineProductId
        };
        var product = await _dbContext.LineProduct.AsNoTracking().FirstOrDefaultAsync(x => x.Id == lineProductId);
        result.DevelopUserId = product.DevelopUserId;
        
        // 海报
        var posters = await _dbContext.ProductPhotos.AsNoTracking()
            .Where(x => x.ProductId == product.Id && x.Enabled && x.MediaType == MediaTypeOfPhotos.Poster)
            .ToListAsync();
        var method = new GetProductPhotosMethod(posters);
        result.ProductPosters = method.Execute(MediaTypeOfPhotos.Poster);
        
        // 平台运营人
        var operatorUsers = await _dbContext.ProductOperatorUsers
            .Where(x => x.ProductId == product.Id && x.ProductType == ProductType.Line)
            .Select(x => new ProductOperatorUserDto()
            {
                OperatorUserId = x.OperatorUserId,
                SellingPlatform = x.SellingPlatform,
                OperatorAssistantUserId = x.OperatorAssistantUserId,
            })
            .ToListAsync();
        result.ProductOperatorUser = operatorUsers;

        return result;
    }

    #endregion

    /// <summary>
    /// 详情
    /// </summary>
    public async Task<GetLineProductOutput> Get(long id)
    {
        //初始化对象
        var entity = await _dbContext.LineProduct.AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == id);
        var product = _mapper.Map<GetLineProductOutput>(entity);

        var redundantData = await _dbContext.ProductRedundantDatas.AsNoTracking()
            .FirstOrDefaultAsync(x => x.ProductId == product.Id);
        product.MinPrice = redundantData?.MinPrice;
        product.Sales = redundantData?.Sales ?? 0;

        //图片/视频/海报
        var allPhotos = await _dbContext.ProductPhotos.AsNoTracking()
            .Where(x => x.ProductId == product.Id && x.Enabled)
            .ToListAsync();
        var method = new GetProductPhotosMethod(allPhotos);
        product.ProductPictures = method.Execute(MediaTypeOfPhotos.Picture);
        product.ProductVideos = method.Execute(MediaTypeOfPhotos.Video);
        product.ProductPosters = method.Execute(MediaTypeOfPhotos.Poster);

        //是否在售卖时间内
        product.InSellingDate = false;
        if ((product.SellingDateBegin == null || product.SellingDateBegin.Value <= DateTime.Now)
            && (product.SellingDateEnd == null || product.SellingDateEnd.Value.AddDays(1) > DateTime.Now))
        {
            product.InSellingDate = true;
        }

        //分组
        product.GroupIds = await _baseProductService.Get(ProductType.Line, product.Id);
        //上车点
        var rallyPoints = await _dbContext.LineProductRallyPoint.AsNoTracking()
            .Where(x => x.LineProductId == product.Id)
            .ToListAsync();
        product.RallyPoints = rallyPoints.Select(x => new RallyPointItem()
        {
            Id = x.Id,
            Address = x.Address,
            Time = x.Time,
            Longitude = x.Location?.X,
            Latitude = x.Location?.Y,
            CoordinateType = x.CoordinateType
        }).ToList();


        var operatorUsers = await _dbContext.ProductOperatorUsers
                             .Where(x => x.ProductId == product.Id && x.ProductType == ProductType.Line)
                             .Select(x => new ProductOperatorUserDto()
                             {
                                 OperatorUserId = x.OperatorUserId,
                                 SellingPlatform = x.SellingPlatform,
                                 OperatorAssistantUserId = x.OperatorAssistantUserId,
                             })
                             .ToListAsync();
        product.ProductOperatorUser = operatorUsers;

        //供应端- 配置
        var openSupplierSetting = await GetOpenSupplierSetting(product.Id);
        product.OpenSupplierSettingInfo = openSupplierSetting;

        //渠道时效配置
        var timelinessSetting = await GetChannelTimelinessSetting(product.Id);
        product.TimelinessChannelSettingInfos = timelinessSetting.TimelinessChannelSettingInfos;

        //渠道端同步配置
        product.OpenChannelSettingInfos = await GetOpenChannelSetting(product.Id);

        return product;
    }

    public async Task<List<LineProduct>> GetLineProducts(params long[] productIds)
    {
        var products = await _dbContext.LineProduct.AsNoTracking()
            .Where(x => productIds.Contains(x.Id))
            .ToListAsync();
        return products;
    }

    /// <summary>
    /// 搜索
    /// </summary>
    public async Task<PagingModel<SearchLineProductOutput, SearchLineProductSummary>> Search(SearchLineProductInput input)
    {
        var pagingQuery = _dbContext.LineProduct.AsNoTracking()
            .Join(_dbContext.ProductRedundantDatas.AsNoTracking(),
                line => line.Id, data => data.ProductId, (line, data) => new { line, data, b2bCount = 0, skuCount = 0 })
            .Where(x => x.line.IsDeleted == false && x.line.IsCompensation == false)
            .WhereIF(input.DepartureCountryIds.Any(),
                x => input.DepartureCountryIds.Contains(x.line.DepartureCountryId))
            .WhereIF(input.DestinationCountryIds.Any(),
                x => input.DestinationCountryIds.Contains(x.line.DestinationCountryId))
            .WhereIF(input.DepartureCityIds != null && input.DepartureCityIds.Any(),
                x => input.DepartureCityIds.Contains(x.line.DepartureCityId))
            .WhereIF(input.DestinationCityIds != null && input.DestinationCityIds.Any(),
                x => input.DestinationCityIds.Contains(x.line.DestinationCityId))
            .WhereIF(input.MinPrice.HasValue, x => x.data.MinPrice >= input.MinPrice.Value)
            .WhereIF(input.MaxPrice.HasValue, x => x.data.MinPrice <= input.MaxPrice.Value)
            .WhereIF(input.Days != null && input.Days.Any(), x => input.Days.Contains(x.line.Days))
            .WhereIF(input.SupplierId.HasValue, x => x.line.SupplierId == input.SupplierId!.Value)
            .WhereIF(input.HasCommission.HasValue, x => x.data.HasCommission == input.HasCommission.Value)
            .WhereIF(input.PurchaseSourceType.HasValue, x => x.line.PurchaseSourceType == input.PurchaseSourceType.Value)
            .WhereIF(input.IsCompensation.HasValue, x => x.line.IsCompensation == input.IsCompensation);

        //关键字搜索
        if (!string.IsNullOrWhiteSpace(input.KeyWord))
        {
            switch (input.SearchType)
            {
                case ProductSearchType.ProductId:
                    pagingQuery = pagingQuery.Where(x => x.line.Id.ToString().Contains(input.KeyWord));
                    break;
                case ProductSearchType.ProductName:
                    pagingQuery = pagingQuery.Where(x => x.line.Title.Contains(input.KeyWord) || x.line.SupplierName.Contains(input.KeyWord));
                    break;
            }
        }

        // 通过sku搜索
        if (!string.IsNullOrWhiteSpace(input.SkuName) || input.SkuId.HasValue)
        {
            var productIds = await _dbContext.LineProductSku.AsNoTracking()
                .WhereIF(!string.IsNullOrWhiteSpace(input.SkuName), x => x.Name.Contains(input.SkuName!))
                .WhereIF(input.SkuId.HasValue, x => x.Id == input.SkuId!.Value)
                .Select(x => x.LineProductId)
                .ToListAsync();
            if (productIds.Any() is false)
            {
                productIds = new List<long> { 0 };
            }
            input.ProductIds.AddRange(productIds);
        }

        // 通过票种搜索
        if (!string.IsNullOrWhiteSpace(input.SkuTypeItemName) || input.SkuTypeItemId.HasValue)
        {
            var productIds = await _dbContext.LineProductSkuTypeItems.AsNoTracking()
                .WhereIF(!string.IsNullOrWhiteSpace(input.SkuTypeItemName),
                    x => x.Name.Contains(input.SkuTypeItemName!))
                .WhereIF(input.SkuTypeItemId.HasValue, x => x.Id == input.SkuTypeItemId!.Value)
                .Select(x => x.LineProductId)
                .ToListAsync();
            if (productIds.Any() is false)
            {
                productIds = new List<long> { 0 };
            }
            input.ProductIds.AddRange(productIds);
        }

        if (input.ProductIds.Any())
        {
            var distProductIds = input.ProductIds.Distinct().ToList();
            pagingQuery = pagingQuery
                .Where(x => distProductIds.Contains(x.line.Id));
        }

        var pagingSkuQuery = _dbContext.LineProduct.AsNoTracking()
                         .Join(_dbContext.LineProductSku.AsNoTracking(), line => line.Id, sku => sku.LineProductId, (line, sku) => new
                         {
                             line,
                             sku
                         }).GroupBy(x => x.line.Id)
                         .Select(x => new
                         {
                             lineId = x.Key,
                             b2bCount = x.Count(c => c.sku.B2bSellingStatus == true),
                             skuCount = x.Count()
                         })
                         ;

        pagingQuery = pagingQuery
            .GroupJoin(pagingSkuQuery, page => page.line.Id, sku => sku.lineId, (page, sku) => new
            {
                page,
                sku
            })
            .SelectMany(x => x.sku.DefaultIfEmpty(), (x, sku) => new
            {
                x.page.line,
                x.page.data,
                b2bCount = sku.b2bCount,
                skuCount = sku.skuCount,
            })
            .WhereIF(input.B2bSellingStatus.HasValue, x => (x.b2bCount > 0) == input.B2bSellingStatus);

        var query = pagingQuery.Select(x => new SearchLineProductOutput()
        {
            Id = x.line.Id,
            Title = x.line.Title,
            DepartureCountryName = x.line.DepartureCountryName,
            DepartureCityId = x.line.DepartureCityId,
            DepartureCityName = x.line.DepartureCityName,
            DestinationCountryName = x.line.DestinationCountryName,
            DestinationCityId = x.line.DestinationCityId,
            DestinationCityName = x.line.DestinationCityName,
            Days = x.line.Days,
            Nights = x.line.Nights,
            SellingDateBegin = x.line.SellingDateBegin,
            SellingDateEnd = x.line.SellingDateEnd,
            SaleCurrencyCode = x.line.SaleCurrencyCode,
            Price = x.data.MinPrice,
            Sales = x.data.Sales,
            Enabled = x.line.Enabled,
            CreateTime = x.line.CreateTime,
            UpdateTime = x.line.UpdateTime,
            Sort = x.line.Sort,
            CostCurrencyCode = x.line.CostCurrencyCode,
            SupplierId = x.line.SupplierId,
            DevelopUserId = x.line.DevelopUserId,
            OperatorUserId = x.line.OperatorUserId,
            SupplierName = x.line.SupplierName,
            PurchaseSourceType = x.line.PurchaseSourceType,
            PriceInventoryType = x.line.PriceInventoryType,
            CostDiscountRate = x.line.CostDiscountRate,
            B2bSellingStatusType = x.b2bCount > 0 ?
                                      (x.b2bCount == x.skuCount ? B2bSellingStatusType.Published : B2bSellingStatusType.PartPublished)
                                      : B2bSellingStatusType.UnPublish

        });

        //售卖状态判断
        if (input.SaleStatus.HasValue)
        {
            query = input.SaleStatus.Value switch
            {
                SaleStatus.NotStarted => query.Where(x =>
                    x.SellingDateBegin.HasValue && x.SellingDateBegin.Value.Date > DateTime.Now),
                SaleStatus.InProgress => query.Where(x =>
                    (!x.SellingDateBegin.HasValue || x.SellingDateBegin.Value.Date <= DateTime.Now) &&
                    (!x.SellingDateEnd.HasValue || x.SellingDateEnd.Value.Date.AddDays(1) > DateTime.Now)),
                SaleStatus.Finished => query.Where(x =>
                    x.SellingDateEnd.HasValue && x.SellingDateEnd.Value.Date.AddDays(1) < DateTime.Now),
                _ => query
            };
        }

        //是否查限时抢购
        if (input.IsFlashSale)
        {
            var flashSaleRequest = new GetProductInfoInput
            {
                ProductType = new List<FlashSaleItemsType> { FlashSaleItemsType.TravelLine }
            };
            var flashSaleProductIds = await _baseProductService.GetFlashSaleProduct(flashSaleRequest);
            var productIds = flashSaleProductIds.Select(x => x.ProductId).Distinct();
            if (productIds.Any() is false)
                return new PagingModel<SearchLineProductOutput, SearchLineProductSummary>();
            query = query.Where(x => productIds.Contains(x.Id));
        }

        //分组搜索
        if (input.GroupIds != null && input.GroupIds.Any())
        {
            query = query.Join(
                _dbContext.GroupItems
                .Where(s => input.GroupIds.Contains(s.GroupId))
                .Select(x => new { x.ProductId })
                .Distinct(),
                q => q.Id,
                gi => gi.ProductId, (q, gi) => q);
        }

        //排序规则
        switch (input.SearchOrderByType)
        {
            case SearchOrderByType.Recommended://推荐
                query = query
                    .OrderByDescending(x => x.Sort)
                    .ThenByDescending(x => x.Sales)
                    .ThenByDescending(x => x.Enabled)
                    .ThenBy(x => x.Id);
                break;
            case SearchOrderByType.PriceAsc://价格升序
                query = query
                    .OrderByDescending(x => x.Sort)
                    .ThenBy(x => x.Price)
                    .ThenByDescending(x => x.Enabled)
                    .ThenBy(x => x.Id);
                break;
            case SearchOrderByType.PriceDesc://价格降序
                query = query
                    .OrderByDescending(x => x.Sort)
                    .ThenByDescending(x => x.Price)
                    .ThenByDescending(x => x.Enabled)
                    .ThenBy(x => x.Id);
                break;
            case SearchOrderByType.None:
            case SearchOrderByType.CreateTime://默认排序、创建时间降序
                query = query
                    .OrderByDescending(x => x.Sort)
                    .ThenByDescending(x => x.CreateTime)
                    .ThenBy(x => x.Id);
                break;
            case SearchOrderByType.SortAsc:
                query = query
                    .OrderBy(x => x.Sort)
                    .ThenByDescending(x => x.Enabled)
                    .ThenByDescending(x => x.UpdateTime)
                    .ThenBy(x => x.Id);
                break;
            case SearchOrderByType.SortDesc:
                query = query
                    .OrderByDescending(x => x.Sort)
                    .ThenByDescending(x => x.Enabled)
                    .ThenByDescending(x => x.UpdateTime)
                    .ThenBy(x => x.Id);
                break;
            default:
                query = query
                    .OrderByDescending(x => x.Enabled)
                    .ThenByDescending(x => x.UpdateTime)
                    .ThenBy(x => x.Id);
                break;
        }

        var pageModel = await query
            .WhereIF(input.Enabled.HasValue, x => x.Enabled == input.Enabled.Value)
            .PagingAsync(input.PageIndex, input.PageSize);

        var summaryData = new SearchLineProductSummary();
        if (input.NeedSummary)
        {
            var groupData = await query.GroupBy(x => x.Enabled)
                .Select(x => new
                {
                    Enabled = x.Key,
                    Count = x.Count()
                })
                .ToListAsync();
            summaryData.EnabledCount = groupData.FirstOrDefault(x => x.Enabled)?.Count ?? 0;
            summaryData.DisabledCount = groupData.FirstOrDefault(x => x.Enabled == false)?.Count ?? 0;
            summaryData.Total = summaryData.EnabledCount + summaryData.DisabledCount;
        }

        //补充产品图片
        var allProductIds = pageModel.Data.Select(x => x.Id).ToList();
        var productPhotos = await _dbContext.ProductPhotos.AsNoTracking()
            .Where(x => allProductIds.Contains(x.ProductId) && x.Enabled)
            .Select(x => new { x.ProductId, x.Path })
            .ToListAsync();
        //查询sku信息
        var skuList = await _dbContext.LineProductSku.AsNoTracking()
            .Where(x => allProductIds.Contains(x.LineProductId))
            .ToListAsync();
        var skuItemTypes = await _dbContext.LineProductSkuTypeItems.AsNoTracking()
            .Where(x => allProductIds.Contains(x.LineProductId))
            .ToListAsync();
        foreach (var item in pageModel.Data)
        {
            item.Picture = productPhotos
                .Where(x => x.ProductId == item.Id)
                .Select(x => x.Path)
                .FirstOrDefault();

            item.SkuItems = skuList.Where(x => x.LineProductId == item.Id)
                .Select(x => new SearchLineProductSkuItem
                {
                    SkuId = x.Id,
                    SkuName = x.Name,
                    CostDiscountRate = x.CostDiscountRate
                })
                .ToList();

            if (item.PurchaseSourceType == LineProductPurchaseSourceType.InterfaceDock)
            {
                item.SupplierIsSale = skuItemTypes.Any(x => x.LineProductId == item.Id && x.SupplierIsSale);
            }
        }

        return new PagingModel<SearchLineProductOutput, SearchLineProductSummary>()
        {
            Total = pageModel.Total,
            PageIndex = input.PageIndex,
            PageSize = input.PageSize,
            Data = pageModel.Data,
            Supplement = summaryData
        };
    }

    /// <summary>
    /// 上下架
    /// </summary>
    public async Task SetEnabled(SetLineProductEnabledInput input)
    {
        var products = await _dbContext.LineProduct
            .Where(x => input.ProductIds.Contains(x.Id))
            .ToListAsync();

        if (input.IsEnabled)
        {
            var supplierIds = products.Select(x => x.SupplierId)
                .Distinct()
                .ToList();

            //验证供应商有效性
            await _baseProductService.SupplierValidator(supplierIds);
        }

        products.ForEach(x =>
        {
            x.Enabled = input.IsEnabled;
            x.UpdateTime = DateTime.Now;
        });

        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 修改排序
    /// </summary>
    public async Task SetSort(SetLineProductSortInput input)
    {
        var product = await _dbContext.LineProduct
            .FirstOrDefaultAsync(x => x.Id == input.ProductId);
        product.Sort = input.Sort;
        product.UpdateTime = DateTime.Now;
        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 根据id列表获取产品
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    public async Task<List<MallGetLineByIdsOutput>> MallGetByIds(List<long> ids)
    {
        var products = await _dbContext.LineProduct.AsNoTracking()
            .Join(_dbContext.ProductRedundantDatas.AsNoTracking(),
                x => x.Id, y => y.ProductId, (x, y) => new { line = x, data = y })
            .Where(x => ids.Contains(x.line.Id)
                        && x.line.Enabled
                        && x.line.IsDeleted == false && x.line.IsCompensation == false
                        && (!x.line.SellingDateBegin.HasValue || x.line.SellingDateBegin.Value.Date <= DateTime.Now)
                        && (!x.line.SellingDateEnd.HasValue || x.line.SellingDateEnd.Value.Date.AddDays(1) > DateTime.Now))
            .Where(x => x.line.PurchaseSourceType != LineProductPurchaseSourceType.InterfaceDock) //临时屏蔽线路API对接产品
            .Select(x => new MallGetLineByIdsOutput
            {
                Id = x.line.Id,
                Title = x.line.Title,
                Price = x.data.MinPrice,
                DepartureCountryName = x.line.DepartureCountryName,
                DepartureCityName = x.line.DepartureCityName,
                DestinationCountryName = x.line.DestinationCountryName,
                DestinationCityName = x.line.DestinationCityName,
                Days = x.line.Days,
                Nights = x.line.Nights,
                ProductType = ProductType.Line
            }).ToListAsync();

        if (products.Any() is false) return products;

        //查询首图
        var pictures = await _baseProductService.GetFirstPhoto(ids);
        foreach (var item in products)
        {
            item.Picture = pictures
                .FirstOrDefault(x => x.ProductId == item.Id
                                     && x.MediaType == MediaTypeOfPhotos.Picture)?.Path ?? "";
        }

        return products;
    }

    /// <summary>
    /// 获取所有产品的出发地和目的地
    /// </summary>
    public async Task<GetLineProductCitiesOutput> GetCities(long tenantId, bool isCache, bool needAvailable)
    {
        if (!isCache)
            return await GetCities(needAvailable);

        var cacheKey = $"product:lineproductcities:{tenantId}:{(needAvailable ? 1 : 0)}";
        var cities = await _redisClient.StringGetAsync<GetLineProductCitiesOutput>(cacheKey);

        if (cities != null)
            return cities;

        cities = await GetCities(needAvailable);
        await _redisClient.StringSetAsync(cacheKey, cities, TimeSpan.FromMinutes(10));

        return cities;
    }

    private async Task<GetLineProductCitiesOutput> GetCities(bool needAvailable)
    {
        var products = await _dbContext.LineProduct
               .WhereIF(needAvailable, x => x.Enabled && x.IsCompensation == false
                   && (x.SellingDateBegin == null || x.SellingDateBegin.Value < DateTime.Now)
                   && (x.SellingDateEnd == null || x.SellingDateEnd.Value > DateTime.Now.AddDays(-1)))
               .Select(x => new
               {
                   x.DepartureCountryId,
                   x.DepartureCountryName,
                   x.DepartureCityId,
                   x.DepartureCityName,
                   x.DestinationCountryId,
                   x.DestinationCountryName,
                   x.DestinationCityId,
                   x.DestinationCityName
               })
               .Distinct()
               .ToListAsync();

        var result = new GetLineProductCitiesOutput()
        {
            DepartureCities = products
                .Where(x=> !string.IsNullOrEmpty(x.DepartureCityName) && !string.IsNullOrEmpty(x.DepartureCountryName))
                .GroupBy(x => new
                {
                    x.DepartureCountryId,
                    x.DepartureCountryName,
                    x.DepartureCityId,
                    x.DepartureCityName
                })
                .Select(x => new LineProduct_CityItem()
                {
                    CountryId = x.Key.DepartureCountryId,
                    CountryName = x.Key.DepartureCountryName,
                    CityId = x.Key.DepartureCityId,
                    CityName = x.Key.DepartureCityName
                })
                .OrderBy(x => x.CityId)
                .ToList(),
            DestinationCities = products
                .Where(x=> !string.IsNullOrEmpty(x.DestinationCityName) && !string.IsNullOrEmpty(x.DestinationCountryName))
                .GroupBy(x => new
                {
                    x.DestinationCountryId,
                    x.DestinationCountryName,
                    x.DestinationCityId,
                    x.DestinationCityName
                })
                .Select(x => new LineProduct_CityItem()
                {
                    CountryId = x.Key.DestinationCountryId,
                    CountryName = x.Key.DestinationCountryName,
                    CityId = x.Key.DestinationCityId,
                    CityName = x.Key.DestinationCityName
                })
                .OrderBy(x => x.CityId)
                .ToList(),
        };
        return result;
    }

    public async Task<PagingModel<GetLineProductsAndSkuOutput>> GetProductsAndSku(GetProductsAndSkuInput input)
    {
        var result = await _dbContext.LineProductSku
            .Join(_dbContext.LineProduct, sku => sku.LineProductId, product => product.Id, (sku, product) => new { sku, product })
            .Where(x => x.product.IsDeleted == false && x.product.IsCompensation == false)
            .WhereIF(input.SkuIds.Any(), x => input.SkuIds.Contains(x.sku.Id))
            .WhereIF(input.Enabled.HasValue, s => s.product.Enabled == input.Enabled && s.sku.Enabled == input.Enabled)
            .WhereIF(!string.IsNullOrEmpty(input.Name), s => s.product.Title.Contains(input.Name))
            .WhereIF(input.SupplierId is > 0, x => x.product.SupplierId == input.SupplierId)
            .WhereIF(input.OperatingModel == OperatingModel.SelfSupport, x => x.product.SupplierId == 0)
            .WhereIF(input.OperatingModel == OperatingModel.Agency, x => x.product.SupplierId > 0)
            .WhereIF(input.B2bSellingStatus.HasValue, x => x.sku.B2bSellingStatus == input.B2bSellingStatus)
            .OrderByDescending(s => s.product.Id)
            .ThenByDescending(s => s.sku.Id)
            .PagingAsync(input.PageIndex, input.PageSize, s => new
            {
                ProductId = s.product.Id,
                ProductName = s.product.Title,
                s.product.PurchaseSourceType,
                SkuId = s.sku.Id,
                SkuName = s.sku.Name,
                s.sku.IncludedAccommodation,
                s.product.SupplierId,
                s.product.CostCurrencyCode,
                s.product.SaleCurrencyCode
            });

        var skuIds = result.Data.Select(x => x.SkuId).ToList();
        var prices = await _dbContext.LineProductSkuCalendarPrice.AsNoTracking()
            .Where(x => skuIds.Contains(x.LineProductSkuId)
                        && x.Date >= DateTime.Today
                        && x.Date <= DateTime.Today.AddDays(30)
                        && x.Price != null)
            .GroupBy(x => new { x.LineProductSkuId, x.Type, x.LineProductSkuTypeItemId })
            .Select(x => new
            {
                x.Key.LineProductSkuId,
                x.Key.Type,
                SkuTypeItemId = x.Key.LineProductSkuTypeItemId,
                CostPrice = x.Min(m => m.CostPrice),
                SalePrice = x.Min(m => m.Price)
            })
            .ToListAsync();
        var skuTypeItems = await _dbContext.LineProductSkuTypeItems.AsNoTracking()
            .Where(x => skuIds.Contains(x.LineProductSkuId))
            .ToListAsync();

        return new PagingModel<GetLineProductsAndSkuOutput>
        {
            PageIndex = result.PageIndex,
            PageSize = result.PageSize,
            Total = result.Total,
            Data = result.Data.Select(s =>
            {
                var skuSubClass = Enum.GetValues(typeof(LineSkuPriceType)).Cast<int>().ToList();
                var skuPrices = prices.Where(x => x.LineProductSkuId == s.SkuId).ToList();
                var subClassPrices = new List<SkuSubClassPrice>();
                if (s.PurchaseSourceType == LineProductPurchaseSourceType.InterfaceDock)
                {
                    var subTypeItems = skuTypeItems.Where(x => x.LineProductSkuId == s.SkuId).ToList();
                    subClassPrices = subTypeItems
                        .Select(x => new SkuSubClassPrice
                        {
                            Type = x.SkuPriceType,
                            SkuSubItemId = x.Id,
                            SkuSubItemName = x.Name,
                            SalePrice = skuPrices.FirstOrDefault(y => y.SkuTypeItemId == x.Id)?.SalePrice,
                            CostPrice = skuPrices.FirstOrDefault(y => y.SkuTypeItemId == x.Id)?.CostPrice
                        })
                        .ToList();
                }
                else
                {
                    subClassPrices = skuPrices
                        .Select(x => new SkuSubClassPrice
                        {
                            Type = x.Type,
                            SalePrice = x.SalePrice,
                            CostPrice = s.SupplierId > 0 ? x.CostPrice : null
                        })
                        .ToList();
                }

                return new GetLineProductsAndSkuOutput
                {
                    ProductId = s.ProductId,
                    ProductName = s.ProductName,
                    SkuId = s.SkuId,
                    SkuName = s.SkuName,
                    SupplierId = s.SupplierId,
                    SkuSubClass = skuSubClass,
                    SkuSubClassPrices = subClassPrices,
                    CostCurrencyCode = s.CostCurrencyCode,
                    SaleCurrencyCode = s.SaleCurrencyCode
                };
            })
        };
    }

    #region 分销商web

    /// <summary>
    /// 分销商web-线路列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<PagingModel<AgencySearchOutput>> AgencySearch(AgencySearchInput input)
    {
        var result = new PagingModel<AgencySearchOutput>();
        //查询分销商分组id
        var agencyInfo = await _baseProductService.GetAgencyInfo(input.AgencyId);
        if (agencyInfo.PriceGroupId is null or <= 0) return result;
        var priceGroupId = agencyInfo.PriceGroupId;

        //判断价格分组开启状态
        var priceGroupInfo = await _baseProductService.GetPriceGroupDetail(priceGroupId.Value);
        if (!priceGroupInfo.Enable)
            return result;

        //查询分组下的产品配置
        var settings = await _dbContext.AgencyChannelPriceSettings.AsNoTracking()
            .Where(x => x.PriceGroupId == priceGroupId
                        && x.ProductType == ChannelProductType.Line).ToListAsync();
        if (settings.Any() is false) return result;

        //查询优惠券分组产品
        if (input.GroupIds.Any())
        {
            var groupProductIds = await _dbContext.GroupItems.AsNoTracking()
                .Where(x => x.ProductType == ProductType.Line)
                .Where(x => input.GroupIds.Contains(x.GroupId))
                .Select(x => x.ProductId)
                .ToListAsync();
            if (groupProductIds.Any())
                input.ProductIds = groupProductIds.Distinct().ToList();
        }

        //产品
        var productIds = settings.Select(x => x.ProductId).Distinct().ToList();
        if (input.ProductIds.Any())
            productIds = productIds.Intersect(input.ProductIds).ToList();
        result = await _dbContext.LineProduct.AsNoTracking()
            .Where(x => productIds.Contains(x.Id)
                        && x.Enabled && x.IsDeleted == false
                        && (!x.SellingDateBegin.HasValue || x.SellingDateBegin <= DateTime.Now)
                        && (!x.SellingDateEnd.HasValue || x.SellingDateEnd > DateTime.Now.AddDays(-1)))
            .WhereIF(!string.IsNullOrEmpty(input.Name), x => x.Title.Contains(input.Name))
            .WhereIF(input.Days.Any(), x => input.Days.Contains(x.Days))
            .WhereIF(input.DepartureCountryIds.Any(), x => input.DepartureCountryIds.Contains(x.DepartureCountryId))
            .WhereIF(input.DestinationCountryIds.Any(), x => input.DestinationCountryIds.Contains(x.DestinationCountryId))
            .WhereIF(input.DepartureCityIds.Any(), x => input.DepartureCityIds.Contains(x.DepartureCityId))
            .WhereIF(input.DestinationCityIds.Any(), x => input.DestinationCityIds.Contains(x.DestinationCityId))
            .OrderByDescending(x => x.Sort)
            .ThenByDescending(x => x.UpdateTime)
            .PagingAsync(input.PageIndex, input.PageSize,
                x => new AgencySearchOutput
                {
                    ProductId = x.Id,
                    ProductName = x.Title,
                    IsSupportRefund = x.IsSupportRefund,
                    SellPointDescribe = x.SellPointDescribe,
                    DepartureCountryName = x.DepartureCountryName,
                    DepartureCityName = x.DepartureCityName,
                    DestinationCountryName = x.DestinationCountryName,
                    DestinationCityName = x.DestinationCityName,
                    CostCurrencyCode = x.CostCurrencyCode,
                    SaleCurrencyCode = x.SaleCurrencyCode
                });
        if (result.Data.Any() is false) return result;

        productIds = result.Data.Select(x => x.ProductId).ToList();
        var photos = await _baseProductService.GetFirstPhoto(productIds);

        //规格
        var skuIds = settings.Where(x => productIds.Contains(x.ProductId))
            .Select(x => x.SkuId).Distinct();
        var skuList = await _dbContext.LineProductSku.AsNoTracking()
            .Where(x => skuIds.Contains(x.Id) && x.Enabled)
            .Select(x => new { x.Id, x.LineProductId, x.Name }).ToListAsync();
        skuIds = skuList.Select(x => x.Id).Distinct().ToList();

        //30天各种年龄段最低价格
        var prices = await _dbContext.LineProductSkuCalendarPrice.AsNoTracking()
            .Where(x => skuIds.Contains(x.LineProductSkuId)
                        && x.Date >= DateTime.Today
                        && x.Date <= DateTime.Today.AddDays(30)
                        && x.Price > 0)
            .GroupBy(x => new
            {
                x.LineProductSkuId,
                x.Type
            })
            .Select(x => new
            {
                SkuId = x.Key.LineProductSkuId,
                Type = x.Key.Type,
                SalePrice = x.Min(m => m.Price),
                CostPrice = x.Min(m => m.CostPrice)
            })
            .ToListAsync();

        #region 查询汇率

        var getCurrencyRateInput = new List<GetExchangeRatesInput>();
        var saleCurrencyRateList = result.Data
            .GroupBy(x => new { x.CostCurrencyCode, x.SaleCurrencyCode })
            .Select(x => new GetExchangeRatesInput
            {
                BaseCurrencyCode = x.Key.CostCurrencyCode,
                TargetCurrencyCode = x.Key.SaleCurrencyCode
            })
            .ToList();
        var agencyCurrencyRate = result.Data
            .GroupBy(x => x.SaleCurrencyCode)
            .Select(x => new GetExchangeRatesInput
            {
                BaseCurrencyCode = x.Key,
                TargetCurrencyCode = agencyInfo.CurrencyCode
            }).ToList();
        getCurrencyRateInput.AddRange(saleCurrencyRateList);
        getCurrencyRateInput.AddRange(agencyCurrencyRate);
        var exchangeRateList = await _baseProductService.GetCurrencyExchangeRateList(getCurrencyRateInput);

        #endregion

        foreach (var item in result.Data)
        {
            //查询sku日历价(有勾选成人则查询成人.否则查询其他的出团年龄最低价)
            var skuPriceTypes = Enum.GetValues<LineSkuPriceType>();
            item.FirstPhoto = photos.FirstOrDefault(x => x.ProductId == item.ProductId
                                                         && x.MediaType == MediaTypeOfPhotos.Picture)?.Path;
            var skus = skuList.Where(x => x.LineProductId == item.ProductId);
            foreach (var sku in skus)
            {
                //计算出团年龄渠道价
                decimal? b2BChannelMinPrice = null;
                foreach (var skuPriceType in skuPriceTypes)
                {
                    var skuSubClass = (int)skuPriceType;
                    var settingItems = settings
                        .Where(s => s.SkuId == sku.Id && s.SkuSubClass == skuSubClass)
                        .ToList();
                    if (settingItems.Any() is false)
                        continue;

                    foreach (var settingItem in settingItems)
                    {
                        var price = prices.FirstOrDefault(p => p.SkuId == sku.Id && p.Type == skuPriceType);
                        //计算B2B渠道价格
                        var b2BChannelPrice = _baseProductService.ConvertB2BPrice(
                            exchangeRateList,
                            settingItem,
                            price?.SalePrice,
                            price?.CostPrice,
                            item.CostCurrencyCode,
                            item.SaleCurrencyCode,
                            agencyInfo.CurrencyCode);

                        //取出最低价
                        if (b2BChannelMinPrice == null || b2BChannelPrice < b2BChannelMinPrice)
                        {
                            b2BChannelMinPrice = b2BChannelPrice;
                        }
                    }
                }

                var skuInfo = new AgencySearchSkuInfo
                {
                    SkuId = sku.Id,
                    SkuName = sku.Name,
                    ChannelPrice = b2BChannelMinPrice
                };
                item.Skus.Add(skuInfo);
            }
        }

        return result;
    }

    #endregion

    public async Task Remove(IEnumerable<long> ids)
    {
        var products = await _dbContext.LineProduct
            .Where(x => !x.IsDeleted && ids.Contains(x.Id))
            .ToListAsync();
        foreach (var item in products)
        {
            item.IsDeleted = true;
            item.UpdateTime = DateTime.Now;
        }
        
        //清空日历价库
        var calendarPriceIds = await _dbContext.LineProductSkuCalendarPrice
            .Where(x => ids.Contains(x.LineProductId))
            .ToListAsync();
        _dbContext.RemoveRange(calendarPriceIds);
        
        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 查询补差线路产品
    /// <value>查不到则创建.用于补差单创建.不做正常产品展示</value>
    /// </summary>
    [UnitOfWork]
    public async Task<GetCompensationLineOutput> GetCompensationLine()
    {
        var outPut = new GetCompensationLineOutput();
        var lineProduct =
            await _dbContext.LineProduct.AsNoTracking().FirstOrDefaultAsync(x => x.IsCompensation == true);
        if (lineProduct == null)
        {
            var compensationLineProduct = new LineProduct
            {
                Title = "补差线路-系统创建",
                PurchaseSourceType = LineProductPurchaseSourceType.OfflinePurchase,
                PriceInventoryType = PriceInventoryType.DailyCalendar,
                PriceInventorySource = PriceInventorySource.System,
                IsCompensation = true,
                Enabled = true
            };
            compensationLineProduct.SetLocation(0,0);
            await _dbContext.AddAsync(compensationLineProduct);

            var compensationLineSku = new LineProductSku
            {
                LineProductId = compensationLineProduct.Id,
                Name = "补差套餐-系统创建",
                Enabled = true
            };
            await _dbContext.AddAsync(compensationLineSku);
            
            outPut.LineProductId = compensationLineProduct.Id;
            outPut.LineProductSkuId = compensationLineSku.Id;

        }
        else
        {
            var lineProductSku = await _dbContext.LineProductSku.AsNoTracking()
                .FirstOrDefaultAsync(x => x.LineProductId == lineProduct.Id);
            outPut.LineProductId = lineProduct.Id;
            outPut.LineProductSkuId = lineProductSku.Id;
        }
        return outPut;
    }

    #region private

    record InitOpenSupplierProductResult(decimal CommissionRate);
    /// <summary>
    /// 检查API产品配置
    /// </summary>
    /// <param name="activityId"></param>
    /// <param name="openSupplierTypeStr"></param>
    /// <param name="tenantId"></param>
    /// <exception cref="BusinessException"></exception>
    private async Task<InitOpenSupplierProductResult> InitOpenSupplierProduct(string? activityId,
        string openSupplierTypeStr,
        long tenantId)
    {
        if (string.IsNullOrEmpty(activityId)) return new InitOpenSupplierProductResult(0m);
        var supplierProductRequest = new SupplierProductDetailRequest
        {
            OutProductId = activityId,
            SupplierType = openSupplierTypeStr
        };

        //查询供应端信息,判断是否存在该供应端产品
        var supplierProduct = await _openSupplierService.SupplierProductDetail(supplierProductRequest, tenantId);
        if (supplierProduct.Data is null || supplierProduct.Data?.SkuList.Any() is false)
        {
            throw new BusinessException(ErrorTypes.Common.ThirdProductConfigurationError);
        }

        //通知开放平台需要价库(从当天开始.范围:180天)
        var startDate = DateTime.Today;
        var endDate = startDate.AddDays(179);
        var supplierSkuScheduleRequest = new SupplierSkuScheduleRequest
        {
            OutProductId = activityId,
            SupplierType = openSupplierTypeStr,
            IsAsync = true,
            DateFrom = startDate,
            DateTo = endDate
        };
        await _openSupplierService.SupplierSkuSchedule(supplierSkuScheduleRequest, tenantId);

        return new InitOpenSupplierProductResult(0m);
    }

    private async Task<UpdateApiSettingProcessResultDto> UpdateApiRelatedSettingProcess(long productId,
        LineProductOpenSupplierSettingInfo openSupplierSettingInfo,
        List<LineProductOpenChannelSettingInfo> openChannelSettingInfos)
    {
        var result = new UpdateApiSettingProcessResultDto();

        //供应端配置更新
        var openSupplierSetting = await _dbContext.LineProductOpenSupplierSettings
            .FirstOrDefaultAsync(x => x.LineProductId == productId);
        if (openSupplierSetting != null)
        {
            openSupplierSetting.ActivityId = openSupplierSettingInfo.ActivityId;
            openSupplierSetting.UpdateTime = DateTime.Now;
        }

        #region 渠道配置更新

        result.PushEmptyOpenChannelSettings = await UpdateOpenChannelSettingProcess(
            productId: productId,
            openChannelSettingInfos: openChannelSettingInfos);

        #endregion

        return result;
    }

    /// <summary>
    /// 渠道配置更新处理
    /// </summary>
    /// <param name="productId"></param>
    /// <param name="openChannelSettingInfos"></param>
    /// <returns>
    ///<para>需要推空的渠道配置</para>
    /// </returns>
    private async Task<List<LineProductOpenChannelSettingInfo>> UpdateOpenChannelSettingProcess(long productId,
        List<LineProductOpenChannelSettingInfo> openChannelSettingInfos)
    {
        var dbOtaChannelSettings = await _dbContext.LineProductOpenChannelSettings
            .Where(x => x.LineProductId == productId)
            .Where(x => x.SettingType == OpenChannelSettingType.PriceInventorySync)
            .ToListAsync();
        var dbOtaChannelSettingItems = await _dbContext.LineProductOpenChannelSettingItems
            .Where(x => x.LineProductId == productId)
            .ToListAsync();

        var oldChannelTypes = dbOtaChannelSettings.Select(x => x.PriceInventorySyncChannelType).ToList();
        var inputChannelTypes = openChannelSettingInfos.Select(x => x.PriceInventorySyncChannelType).ToList();
        var exceptTypes = oldChannelTypes.Except(inputChannelTypes).ToList();//移除的
        var newTypes = inputChannelTypes.Except(oldChannelTypes).ToList();//新增的

        //判断需要推空的渠道设置
        var pushEmptyOtaChannelSettings = new List<LineProductOpenChannelSettingInfo>();
        //查询移除的渠道设置-需要做推空处理
        foreach (var item in dbOtaChannelSettings
                     .Where(x => exceptTypes.Contains(x.PriceInventorySyncChannelType)))
        {
            //是否有设置同步配置,旧数据无需同步跳过
            if (item.PriceInventorySyncType == PriceInventorySyncType.NoNeedSync)
                continue;

            var syncSkuIds = dbOtaChannelSettingItems
                .Where(x => x.LineProductOpenChannelSettingId == item.Id)
                .Select(x => x.SyncSkuId)
                .ToList();

            pushEmptyOtaChannelSettings.Add(new LineProductOpenChannelSettingInfo
            {
                LineProductId = productId,
                PriceInventorySyncChannelType = item.PriceInventorySyncChannelType,
                ChannelProductId = item.ChannelProductId,
                PriceInventorySyncType = item.PriceInventorySyncType,
                SupplierProductId = item.SupplierProductId,
                Area = item.Area,
                SyncSkuIds = syncSkuIds
            });
        }

        //查询同步配置更新变动的渠道 - 需要做推空
        //1.价库同步类型变动
        //2.飞猪渠道- 商品编码变动
        //3. 美团渠道 - 同步id变动
        foreach (var item in dbOtaChannelSettings
                     .Where(x => inputChannelTypes.Contains(x.PriceInventorySyncChannelType)))
        {
            var inputItem = openChannelSettingInfos.FirstOrDefault(x =>
                x.PriceInventorySyncChannelType == item.PriceInventorySyncChannelType);
            if (inputItem is null) continue;

            var syncSkuIds = dbOtaChannelSettingItems
                .Where(x => x.LineProductOpenChannelSettingId == item.Id)
                .Select(x => x.SyncSkuId)
                .ToList();

            //同步类型变动 `需要同步`=>'无需同步'
            if (item.PriceInventorySyncType != inputItem.PriceInventorySyncType)
            {
                if (item.PriceInventorySyncType != PriceInventorySyncType.NoNeedSync
                    && inputItem.PriceInventorySyncType == PriceInventorySyncType.NoNeedSync)
                {
                    pushEmptyOtaChannelSettings.Add(new LineProductOpenChannelSettingInfo
                    {
                        LineProductId = productId,
                        PriceInventorySyncChannelType = item.PriceInventorySyncChannelType,
                        ChannelProductId = item.ChannelProductId,
                        PriceInventorySyncType = item.PriceInventorySyncType,
                        SupplierProductId = item.SupplierProductId,
                        Area = item.Area,
                        SyncSkuIds = syncSkuIds
                    });
                }
            }
            else
            {
                //飞猪渠道只更改商品编码
                if (item.PriceInventorySyncChannelType == PriceInventorySyncChannelType.Fliggy
                    && item.ChannelProductId != inputItem.ChannelProductId)
                {
                    pushEmptyOtaChannelSettings.Add(new LineProductOpenChannelSettingInfo
                    {
                        LineProductId = productId,
                        PriceInventorySyncChannelType = item.PriceInventorySyncChannelType,
                        ChannelProductId = item.ChannelProductId,
                        PriceInventorySyncType = item.PriceInventorySyncType,
                        SupplierProductId = item.SupplierProductId,
                        Area = item.Area,
                        SyncSkuIds = syncSkuIds
                    });
                }

                //美团渠道-同步id变动
                if (item.PriceInventorySyncChannelType == PriceInventorySyncChannelType.Meituan)
                {
                    //旧配置-配置了同步id & 新配置-配置了同步id
                    if (syncSkuIds.Any() && inputItem.SyncSkuIds.Any())
                    {
                        //移除的同步id - 推空
                        var removeSyncSkuIds = syncSkuIds.Except(inputItem.SyncSkuIds).ToList();
                        if (removeSyncSkuIds.Any())
                        {
                            pushEmptyOtaChannelSettings.Add(new LineProductOpenChannelSettingInfo
                            {
                                LineProductId = productId,
                                PriceInventorySyncChannelType = item.PriceInventorySyncChannelType,
                                ChannelProductId = item.ChannelProductId,
                                PriceInventorySyncType = item.PriceInventorySyncType,
                                SupplierProductId = item.SupplierProductId,
                                Area = item.Area,
                                SyncSkuIds = removeSyncSkuIds
                            });
                        }
                    }
                }
            }
        }

        //移除旧的渠道设置
        _dbContext.RemoveRange(dbOtaChannelSettings);
        _dbContext.RemoveRange(dbOtaChannelSettingItems);
        await SaveOpenChannelSettings(productId, openChannelSettingInfos);
        return pushEmptyOtaChannelSettings;
    }

    /// <summary>
    /// 保存渠道设置
    /// </summary>
    /// <param name="productId"></param>
    /// <param name="openChannelSettingInfos"></param>
    private async Task SaveOpenChannelSettings(long productId,
        List<LineProductOpenChannelSettingInfo> openChannelSettingInfos)
    {
        if (openChannelSettingInfos.Any())
        {
            var openChannelSettingList = new List<LineProductOpenChannelSetting>();
            var openChannelSettingItemList = new List<LineProductOpenChannelSettingItem>();
            foreach (var settingItem in openChannelSettingInfos)
            {
                var openChannelSetting = new LineProductOpenChannelSetting
                {
                    LineProductId = productId,
                    SettingType = OpenChannelSettingType.PriceInventorySync,
                    PriceInventorySyncChannelType = settingItem.PriceInventorySyncChannelType,
                    ChannelProductId = settingItem.ChannelProductId,
                    SupplierProductId = settingItem.SupplierProductId,
                    Area = settingItem.Area,
                    PriceInventorySyncType = settingItem.PriceInventorySyncType,
                    ZeroStockThreshold = settingItem.ZeroStockThreshold,
                };

                openChannelSettingList.Add(openChannelSetting);

                //渠道端同步skuIds
                openChannelSettingItemList.AddRange(settingItem.SyncSkuIds.Select(syncSkuId =>
                    new LineProductOpenChannelSettingItem
                    {
                        LineProductId = productId,
                        LineProductOpenChannelSettingId = openChannelSetting.Id,
                        SyncSkuId = syncSkuId
                    }));
            }

            await _dbContext.AddRangeAsync(openChannelSettingList);
            if (openChannelSettingItemList.Any())
                await _dbContext.AddRangeAsync(openChannelSettingItemList);
        }

    }

    /// <summary>
    /// 渠道推空配置比较
    /// </summary>
    /// <param name="sourceSettingInfos">旧配置</param>
    /// <param name="targetSettingInfos">新配置</param>
    private List<LineProductOpenChannelSettingInfo> ChannelPushEmptyConfigComparator(
        List<LineProductOpenChannelSettingInfo> sourceSettingInfos,
        List<LineProductOpenChannelSettingInfo> targetSettingInfos)
    {
        var pushEmptyOtaChannelSettings = new List<LineProductOpenChannelSettingInfo>();
        foreach (var item in sourceSettingInfos)
        {
            if (item.PriceInventorySyncType == PriceInventorySyncType.NoNeedSync) continue;//旧配置无需同步.跳过

            //查出对应的新配置
            var inputItem = targetSettingInfos.FirstOrDefault(x =>
                x.PriceInventorySyncChannelType == item.PriceInventorySyncChannelType);
            if (inputItem == null)
            {
                //整个渠道移除.直接推空
                if (item.PriceInventorySyncType != PriceInventorySyncType.NoNeedSync)
                {
                    pushEmptyOtaChannelSettings.Add(item);
                }
                continue;
            }

            /*
             * 飞猪渠道:
             * 1. ChannelProductId 值变动
             * 2. PriceInventorySyncType 变成无需同步(NoNeedSync)
             */
            if (item.PriceInventorySyncChannelType == PriceInventorySyncChannelType.Fliggy)
            {
                //飞猪渠道只更改商品编码
                if (item.ChannelProductId != inputItem.ChannelProductId || inputItem.PriceInventorySyncType == PriceInventorySyncType.NoNeedSync)
                {
                    pushEmptyOtaChannelSettings.Add(new LineProductOpenChannelSettingInfo
                    {
                        LineProductId = item.LineProductId,
                        PriceInventorySyncChannelType = item.PriceInventorySyncChannelType,
                        ChannelProductId = item.ChannelProductId,
                        Area = item.Area,
                        PriceInventorySyncType = item.PriceInventorySyncType,
                    }
                    );
                }
            }

            /*
             * 美团渠道:
             * 1. SyncSkuIds id列表 存在移除的id
             * 2. PriceInventorySyncType 变成无需同步(NoNeedSync)
             */
            if (item.PriceInventorySyncChannelType == PriceInventorySyncChannelType.Meituan)
            {
                var removeSyncSkuIds = item.SyncSkuIds.Except(inputItem.SyncSkuIds).ToList();
                if (removeSyncSkuIds.Any() || inputItem.PriceInventorySyncType == PriceInventorySyncType.NoNeedSync)
                {
                    pushEmptyOtaChannelSettings.Add(new LineProductOpenChannelSettingInfo
                    {
                        LineProductId = item.LineProductId,
                        PriceInventorySyncChannelType = item.PriceInventorySyncChannelType,
                        SupplierProductId = item.SupplierProductId,
                        SyncSkuIds = removeSyncSkuIds
                    }
                    );
                }
            }

            /*
             * 携程渠道
             * 1.PriceInventorySyncType 变成无需同步(NoNeedSync)
             */
            if (item.PriceInventorySyncChannelType == PriceInventorySyncChannelType.Ctrip)
            {
                if (inputItem.PriceInventorySyncType == PriceInventorySyncType.NoNeedSync)
                {
                    pushEmptyOtaChannelSettings.Add(new LineProductOpenChannelSettingInfo
                    {
                        LineProductId = item.LineProductId,
                        PriceInventorySyncChannelType = item.PriceInventorySyncChannelType
                    }
                    );
                }
            }
        }

        return pushEmptyOtaChannelSettings;
    }

    #endregion
}
