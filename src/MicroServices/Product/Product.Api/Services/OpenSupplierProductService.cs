using Common.GlobalException;
using Contracts.Common.Order.Enums;
using Contracts.Common.Product.DTOs.OpenSupplier;
using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.DTOs.OpenSupplier;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Extensions;
using Microsoft.EntityFrameworkCore;
using Product.Api.Services.Interfaces;
using Product.Api.Services.OpenPlatform.Contracts.Supplier;
using Product.Api.Services.OpenPlatform.Interfaces;

namespace Product.Api.Services;

/// <summary>
/// 供应端产品
/// </summary>
public class OpenSupplierProductService : IOpenSupplierProductService
{
    private readonly CustomDbContext _dbContext;
    private readonly IOpenSupplierService _openSupplierService;
    private readonly IOpenPlatformBaseService _openPlatformBaseService;
    private readonly IBaseProductService _baseProductService;

    public OpenSupplierProductService(
        CustomDbContext dbContext,
        IOpenSupplierService openSupplierService,
        IOpenPlatformBaseService openPlatformBaseService,
        IBaseProductService baseProductService)
    {
        _dbContext = dbContext;
        _openSupplierService = openSupplierService;
        _openPlatformBaseService = openPlatformBaseService;
        _baseProductService = baseProductService;
    }

    public async Task<GetOpenSupplierProductDetailOutput> GetProductDetail(GetOpenSupplierProductDetailInput input,
        long tenantId)
    {
        var result = new GetOpenSupplierProductDetailOutput();
        if (string.IsNullOrEmpty(input.OutProductId) &&
            string.IsNullOrEmpty(input.OutProductOptionId) &&
            string.IsNullOrEmpty(input.OutSkuId))
            return result;

        var convertSupplierApiType = _openPlatformBaseService.ConvertSupplierApiType(input.SupplierApiType);
        var productDetail = await _openSupplierService.SupplierProductDetail(
            new SupplierProductDetailRequest
            {
                SupplierType = convertSupplierApiType.openSupplierTypeStr,
                OutProductId = input.OutProductId,
                OutProductOptionId = input.OutProductOptionId,
                OutSkuId = input.OutSkuId
            }, tenantId);
        if (productDetail.Code == 200)
        {
            result.OutProductId = input.OutProductId;
            result.Instant = productDetail.Data.Instant;
            result.HasContent = productDetail.Data.HasContent;
            foreach (var item in productDetail.Data.SkuList)
            {
                var travelerInfoType = item.TravelerInfoType ?? OpenSupplierProductTravelerInfoType.NotNeed;// 处理null值情况
                
                var skuDetailItem = new GetOpenSupplierProductSkuDetailItem
                {
                    OutProductOptionId = item.OutProductOptionId, 
                    OutProductOptionName = item.OutProductOptionName,
                    OutSkuId = item.OutSkuId,
                    OutSkuName = item.OutSkuName,
                    NeedTravelerInfo = travelerInfoType!= OpenSupplierProductTravelerInfoType.NotNeed,
                    CommissionRate = item.CommissionRate
                };

                bool hasPickUpLocation = false;
                foreach (var extraInfoItem in item.ExtraInfos)
                {
                    //转成枚举
                    var extraInfoKeyType =
                        _openPlatformBaseService.GetSaasEnumValue<ApiSkuExtraInfoDataType>(extraInfoItem.Key);
                    var extraInfoValueType =
                        _openPlatformBaseService.GetSaasEnumValue<ApiSkuExtraInfoValueType>(extraInfoItem.ValueType);

                    //由于会同时存在[上车地点]和[出发酒店] . 优先取值 [上车地点] 没有再取 [出发酒店]
                    switch (extraInfoKeyType)
                    {
                        case ApiSkuExtraInfoDataType.PickUpLocation:

                            skuDetailItem.PickUpLocationData = new GetApiSkuLocationData
                            {
                                KeyType = extraInfoKeyType,
                                ValueType = extraInfoValueType,
                                ValueOptions = extraInfoItem.ValueOptions.Select(x =>
                                    new GetApiSkuLocationValueOptions {Key = x.Key, Value = x.Value}).ToList()
                            };
                            hasPickUpLocation = true;

                            break;
                        case ApiSkuExtraInfoDataType.DepartureHotel:

                            if (!hasPickUpLocation)
                            {
                                skuDetailItem.PickUpLocationData = new GetApiSkuLocationData
                                {
                                    KeyType = extraInfoKeyType,
                                    ValueType = extraInfoValueType,
                                    ValueOptions = extraInfoItem.ValueOptions.Select(x =>
                                        new GetApiSkuLocationValueOptions {Key = x.Key, Value = x.Value}).ToList()
                                };
                            }

                            break;
                        case ApiSkuExtraInfoDataType.DeliveryLocation:

                            //送达地点
                            skuDetailItem.DeliveryLocationData = new GetApiSkuLocationData
                            {
                                KeyType = extraInfoKeyType,
                                ValueType = extraInfoValueType,
                                ValueOptions = extraInfoItem.ValueOptions.Select(x =>
                                    new GetApiSkuLocationValueOptions {Key = x.Key, Value = x.Value}).ToList()
                            };

                            break;
                    }
                }

                result.SkuList.Add(skuDetailItem);
            }
        }

        return result;
    }
    
    public async Task<GetOpenSupplierProductContentOutput> GetProductContent(GetOpenSupplierProductContentInput input,
        long tenantId)
    {
        var result = new GetOpenSupplierProductContentOutput();
        if (string.IsNullOrEmpty(input.OutProductId))
            return result;
        var convertSupplierApiType = _openPlatformBaseService.ConvertSupplierApiType(input.SupplierApiType);
        var productContent = await _openSupplierService.SupplierProductContent(
            new SupplierProductContentRequest
            {
                SupplierType = convertSupplierApiType.openSupplierTypeStr,
                OutProductId = input.OutProductId,
                Language = input.LanguageType.GetDescription()
            }, tenantId);
        result.Code = productContent.Code;
        result.Msg = productContent.Msg;
        if (productContent.Code == 200)
        {
            result.Title = productContent.Data.Title;
            result.Images = productContent.Data.Images;
            result.Description = StringEmptyToNull(productContent.Data.Description);
            result.Highlight = StringEmptyToNull(productContent.Data.Highlight);
            result.PickUpLocation = StringEmptyToNull(productContent.Data.PickUpLocation);
            result.SkuList = productContent.Data.SkuList.Select(item => new OpenSupplierProductContentSkuInfo
            {
                OutProductOptionId = item.OutProductOptionId,
                OutProductOptionName = item.OutProductOptionName,
                OutSkuId = item.OutSkuId,
                OutSkuName = item.OutSkuName,
                OpenHours = StringEmptyToNull(item.OpenHours),
                UsageValidity = StringEmptyToNull(item.UsageValidity),
                Inclusions = StringEmptyToNull(item.Inclusions),
                Exclusions = StringEmptyToNull(item.Exclusions),
                AdditionalCharges = StringEmptyToNull(item.AdditionalCharges),
                AgeLimit = StringEmptyToNull(item.AgeLimit),
                HowToUse = StringEmptyToNull(item.HowToUse),
                CancelPolicy = StringEmptyToNull(item.CancelPolicy)
            }).ToList();
        }

        return result;
    }
    
    public async Task<List<GetOpenSupplierBasicProductOutput>> GetBasicProducts(GetOpenSupplierBasicProductInput input, long tenantId)
    {
        var result = new List<GetOpenSupplierBasicProductOutput>();
        var convertSupplierApiType = _openPlatformBaseService.ConvertSupplierApiType(input.SupplierApiType);
        
        //查询供应端产品详情
        var productDetail = await _openSupplierService.SupplierProductDetail(
            new SupplierProductDetailRequest
            {
                SupplierType = convertSupplierApiType.openSupplierTypeStr,
                OutProductId = input.OutProductId
            }, tenantId);
        if (productDetail.Code != 200)
            return new List<GetOpenSupplierBasicProductOutput>();
        var productBasicProducts = productDetail.Data.SkuList.Select(sku => new GetOpenSupplierBasicProductOutput
            {
                ProductId = productDetail.Data.OutProductId,
                ProductName = productDetail.Data.ProductName,
                OptionId = sku.OutProductOptionId,
                OptionName = sku.OutProductOptionName,
                SkuId = sku.OutSkuId,
                SkuName = sku.OutSkuName,
                MatchStatus = OpenSupplierBasicProductMatchStatus.WaitingMatch,
                SkuPriceType = _openPlatformBaseService.ConvertSupplierProductSkuType(sku.SkuType)
            })
            .ToList();

        //查询saas维护的基础产品信息
        var basicProducts = await _dbContext.OpenSupplierBasicProducts.AsNoTracking()
            .Where(x => x.OpenSupplierType == convertSupplierApiType.openSupplierType
                        && x.ProductId == input.OutProductId)
            .Select(x=> new GetOpenSupplierBasicProductOutput
            {
                MatchId = x.Id,
                ProductId = x.ProductId,
                ProductName = x.ProductName,
                MatchStatus = x.MatchStatus,
                TimeSlotId = x.TimeSlotId,
                TimeSlotName = x.TimeSlotName,
                SkuId = x.SkuId,
                SkuName = x.SkuName,
                OptionId = x.OptionId,
                OptionName = x.OptionName,
                SkuPriceType = x.SkuPriceType
            })
            .ToListAsync();
        result.AddRange(basicProducts);
        
        //数据是以Sku维度异步匹配.需要对未同步的数据进行兼容处理
        foreach (var productBasicProduct in productBasicProducts)
        {
            //判断是否存在saas
            var isExist = basicProducts.Any(x => x.ProductId == productBasicProduct.ProductId
                                                 && x.OptionId == productBasicProduct.OptionId
                                                 && x.SkuId == productBasicProduct.SkuId);
            
            if(isExist) continue;
            result.Add(productBasicProduct);
        }
        
        return result;
    }

    public async Task<List<QueryOpenSupplierSkuExtraInfoOutput>> QuerySkuExtraInfo(QueryOpenSupplierSkuExtraInfoInput input)
    {
        var result = new List<QueryOpenSupplierSkuExtraInfoOutput>();
        if (input.ProductIds.Any() is false) return result;
        
        var skuExtraInfos = await _dbContext.OpenSupplierSkuExtraInfos
            .AsNoTracking()
            .Where(x => input.ProductIds.Contains(x.ProductId))
            .WhereIF(input.OpenSupplierTypes.Any(),x => input.OpenSupplierTypes.Contains(x.OpenSupplierType))
            .WhereIF(input.OptionIds.Any(),x => input.OptionIds.Contains(x.OptionId))
            .WhereIF(input.SkuIds.Any(),x => input.SkuIds.Contains(x.SkuId))
            .Where(x=>x.Status == ApiSkuExtraInfoStatus.Default)
            .ToListAsync();

        var extraInfoIds = skuExtraInfos.Select(x => x.Id).ToList();
        var skuExtraInfoValueOptions = await _dbContext.OpenSupplierSkuExtraInfoValueOptions
            .AsNoTracking()
            .Where(x => extraInfoIds.Contains(x.ExtraInfoId))
            .Where(x => x.Status == ApiSkuExtraInfoStatus.Default)
            .ToListAsync();

        result = skuExtraInfos.GroupBy(g => new { g.ProductId, g.OpenSupplierType })
            .Select(gi => new QueryOpenSupplierSkuExtraInfoOutput
            {
                ProductId = gi.Key.ProductId, OpenSupplierType = gi.Key.OpenSupplierType,
                Sub = gi.GroupBy(gs => new {gs.OptionId,gs.SkuId})
                    .Select(gs=> new QueryOpenSupplierSkuExtraInfoSub
                    {
                        OptionId = gs.Key.OptionId,
                        SkuId = gs.Key.SkuId,
                        ExtraInfos = gs.Select(ge=> new GetSkuExtraInfoItem
                        {
                            DataType = ge.DataType,
                            ValueType = ge.ValueType,
                            IsRequired = ge.IsRequired,
                            ValueOptions = skuExtraInfoValueOptions.Where(v => v.ExtraInfoId == ge.Id)
                                .Select(y=> new GetSkuExtraInfoValueOption
                                {
                                    Id = y.Id,
                                    Key = y.OptionKey,
                                    Value = y.OptionValue
                                }).ToList()
                        }).ToList()
                    })
                    .ToList()
            })
            .ToList();

        return result;
    }

    [UnitOfWork]
    public async Task SyncExtraInfo(SyncOpenSupplierExtraInfoInput input)
    {

        // 查出关联玩乐产品的租户信息
        if (input.TenantIds.Any() is false)
        {
            input.TenantIds = await QueryFunProductTenantIdList(input.OpenSupplierType,input.ProductId);
        }
        
        var skuExtraInfos = await _dbContext.OpenSupplierSkuExtraInfos.IgnoreQueryFilters()
            .Where(x => x.OpenSupplierType == input.OpenSupplierType)
            .Where(x => x.ProductId == input.ProductId)
            .WhereIF(!string.IsNullOrEmpty(input.OptionId), x => x.OptionId == input.OptionId)
            .WhereIF(!string.IsNullOrEmpty(input.SkuId), x => x.SkuId == input.SkuId)
            .ToListAsync();

        var extraInfoIds = skuExtraInfos.Select(x => x.Id).ToList();
        var skuExtraInfoValueOptions = await _dbContext.OpenSupplierSkuExtraInfoValueOptions.IgnoreQueryFilters()
            .Where(x => extraInfoIds.Contains(x.ExtraInfoId))
            .ToListAsync();

        foreach (var tenantId in input.TenantIds)
        {
            var productRequest = new SupplierProductDetailRequest
            { 
                OutProductId = input.ProductId,
                OutProductOptionId = input.OptionId,
                OutSkuId = input.SkuId,
                SupplierType = input.OpenSupplierType.ToString().ToLowerInvariant()
            };
            
            var productResponse = await _openSupplierService.SupplierProductDetail(productRequest, tenantId);
            if(productResponse.Code != 200) continue;
            
            // 获取当前租户关联的附加信息数据
            var relatedSkuExtraInfos = skuExtraInfos.Where(x => x.TenantId == tenantId).ToList();
            var relatedExtraInfoIds = relatedSkuExtraInfos.Select(x => x.Id).ToList();
            var relatedSkuExtraInfoValueOptions =
                skuExtraInfoValueOptions.Where(x => relatedExtraInfoIds.Contains(x.ExtraInfoId))
                    .ToList();
            var updateTime = DateTime.Now; //  更新时间
            
            if (productResponse?.Data is null || productResponse.Data.SkuList.Any() is false)
            {
                // 更改状态
                foreach (var extraInfo in relatedSkuExtraInfos)
                {
                    extraInfo.Status = ApiSkuExtraInfoStatus.Deleted;
                    extraInfo.UpdateTime = updateTime;
                }

                foreach (var valueOption in relatedSkuExtraInfoValueOptions)
                {
                    valueOption.Status = ApiSkuExtraInfoStatus.Deleted;
                    valueOption.UpdateTime = updateTime;
                }
            }
            else
            {
                // 供应端返回的附加信息
                var supplierSkuExtraInfos = productResponse.Data.SkuList.First().ExtraInfos;
                
                /*
                 * 与数据库中数据进行对比.
                 * 数据库不存在的数据,则创建
                 * 供应端不存在的数据,则删除(修改状态)
                 */
                var valueOptionsRemoveList = new List<OpenSupplierSkuExtraInfoValueOptions>(); // 记录值类型变更移除的db数据
                foreach (var supplierExtraInfoItem in supplierSkuExtraInfos)
                {
                    //转成枚举
                    var extraInfoKeyType =
                        _openPlatformBaseService.GetSaasEnumValue<ApiSkuExtraInfoDataType>(supplierExtraInfoItem.Key);
                    var extraInfoValueType =
                        _openPlatformBaseService.GetSaasEnumValue<ApiSkuExtraInfoValueType>(supplierExtraInfoItem.ValueType);
                    
                    var dbSkuExtraInfo = relatedSkuExtraInfos.FirstOrDefault(x => x.DataType == extraInfoKeyType);
                    if (dbSkuExtraInfo != null)
                    {
                        // 判断值类型是否变更.数据处理
                        if (dbSkuExtraInfo.ValueType != extraInfoValueType)
                        {
                            switch (extraInfoValueType)
                            {
                                case ApiSkuExtraInfoValueType.Text:
                                    
                                    // 下拉 => 文本 移除下拉数据
                                    var dbSkuExtraInfoValueOptions = relatedSkuExtraInfoValueOptions
                                        .Where(x => x.ExtraInfoId == dbSkuExtraInfo.Id).ToList();
                                    valueOptionsRemoveList.AddRange(dbSkuExtraInfoValueOptions);
                                    
                                    break;
                                case ApiSkuExtraInfoValueType.Select:
                                    
                                    // 文本 => 下拉 新增下拉数据
                                    await AddSkuExtraInfoValueOptions(valueOptions: supplierExtraInfoItem.ValueOptions,
                                        extraInfoId: dbSkuExtraInfo.Id,
                                        tenantId: tenantId);
                                    
                                    break;
                                default:
                                    throw new ArgumentOutOfRangeException();
                            }
                        }
                        else
                        {
                            // 值类型未变更,需要处理选项值数据
                            if (dbSkuExtraInfo.ValueType == ApiSkuExtraInfoValueType.Select)
                            {
                                var dbSkuExtraInfoValueOptions = relatedSkuExtraInfoValueOptions
                                    .Where(x => x.ExtraInfoId == dbSkuExtraInfo.Id).ToList();
                                
                                await UpdateSkuExtraInfoValueOptions(
                                    dbSkuExtraInfoValueOptions: dbSkuExtraInfoValueOptions,
                                    valueOptions: supplierExtraInfoItem.ValueOptions,
                                    extraInfoId: dbSkuExtraInfo.Id,
                                    tenantId: tenantId,
                                    updateTime: updateTime);
                            }
                        }
                        
                        // 匹配到.更新
                        dbSkuExtraInfo.DataName = supplierExtraInfoItem.Name;
                        dbSkuExtraInfo.DataType = extraInfoKeyType;
                        dbSkuExtraInfo.ValueType = extraInfoValueType;
                        dbSkuExtraInfo.Status = ApiSkuExtraInfoStatus.Default;
                        dbSkuExtraInfo.UpdateTime = updateTime;
                        dbSkuExtraInfo.IsRequired = supplierExtraInfoItem.Required;
                    }
                    else
                    {
                        // 无匹配,新增
                        var newSkuExtraInfo = new OpenSupplierSkuExtraInfo
                        {
                            OpenSupplierType = input.OpenSupplierType,
                            ProductId = input.ProductId,
                            OptionId = input.OptionId,
                            SkuId = input.SkuId,
                            DataName = supplierExtraInfoItem.Name,
                            DataType = extraInfoKeyType,
                            ValueType = extraInfoValueType,
                            Status = ApiSkuExtraInfoStatus.Default,
                            IsRequired = supplierExtraInfoItem.Required
                        };
                        newSkuExtraInfo.SetTenantId(tenantId);
                        await _dbContext.AddAsync(newSkuExtraInfo);

                        if (newSkuExtraInfo.ValueType == ApiSkuExtraInfoValueType.Select)
                        {
                            await AddSkuExtraInfoValueOptions(valueOptions: supplierExtraInfoItem.ValueOptions,
                                extraInfoId: newSkuExtraInfo.Id,
                                tenantId: tenantId);
                        }
                    }
                }
                
                // 处理本次未更新的db数据
                foreach (var deleteExtraInfo in relatedSkuExtraInfos
                             .Where(x => x.Status == ApiSkuExtraInfoStatus.Default)
                             .Where(x => x.UpdateTime is null || x.UpdateTime < updateTime))
                {
                    deleteExtraInfo.Status = ApiSkuExtraInfoStatus.Deleted;
                    deleteExtraInfo.UpdateTime = updateTime;
                }

                foreach (var deleteValueOption in relatedSkuExtraInfoValueOptions
                             .Where(x=>x.Status == ApiSkuExtraInfoStatus.Default)
                             .Where(x => x.UpdateTime is null || x.UpdateTime < updateTime))
                {
                    deleteValueOption.Status = ApiSkuExtraInfoStatus.Deleted;
                    deleteValueOption.UpdateTime = updateTime;
                }

                // 处理本次值类型变更需要删除的数据
                if (valueOptionsRemoveList.Any())
                    _dbContext.RemoveRange(valueOptionsRemoveList);
            }
        }
    }
    
    public async Task RetryMatchBasicProduct(RetryMatchBasicProductInput input,long tenantId)
    {
        var supplierInfo = await _baseProductService.GetSupplierInfo(input.SupplierId);
        if (supplierInfo.SupplierApiSetting == null)
            throw new BusinessException(ErrorTypes.Tenant.IsNotApiSupplier);
        if (supplierInfo.SupplierApiSetting.SupplierApiType == null)
            throw new BusinessException(ErrorTypes.Tenant.IsNotApiSupplier);
        if (string.IsNullOrEmpty(input.SupplierProductId) || !input.SupplierSkuIds.Any())
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        var convertSupplierApiType =
            _openPlatformBaseService.ConvertSupplierApiType(supplierInfo.SupplierApiSetting.SupplierApiType.Value);
        var openSupplierTypeStr = convertSupplierApiType.openSupplierTypeStr;
        
        // 查询saas维护的基础信息.
        var basicProductSkuIds = await _dbContext.OpenSupplierBasicProducts.AsNoTracking()
            .Where(x => x.OpenSupplierType == convertSupplierApiType.openSupplierType &&
                        x.ProductId == input.SupplierProductId &&
                        x.MatchStatus == OpenSupplierBasicProductMatchStatus.MatchSuccess)
            .Select(x => x.SkuId)
            .ToListAsync();
        var matchSuccessSkuIds = basicProductSkuIds.Where(x => !string.IsNullOrEmpty(x)).Select(x => x!).ToList();
        input.SupplierSkuIds = input.SupplierSkuIds.Where(x => !matchSuccessSkuIds.Contains(x)).ToList();
        if (!input.SupplierSkuIds.Any()) return;
        
        //查询供应端信息,判断是否存在该供应端产品
        var supplierProductRequest = new SupplierProductDetailRequest
        {
            OutProductId = input.SupplierProductId,
            SupplierType = openSupplierTypeStr
        };
        var supplierProduct = await _openSupplierService.SupplierProductDetail(supplierProductRequest, tenantId);
        if (supplierProduct.Data is null || supplierProduct.Data?.SkuList.Any() is false)
        {
            throw new BusinessException(ErrorTypes.Common.ThirdProductConfigurationError);
        }
        var retryMarchSkuIds = supplierProduct.Data.SkuList.Select(x => x.OutSkuId).Intersect(input.SupplierSkuIds)
            .ToList();
        if (!retryMarchSkuIds.Any()) return;
        
        // 发起异步推送
        var startDate = DateTime.Today;
        var endDate = startDate.AddDays(179);
        foreach (var retryMarchSkuId in retryMarchSkuIds)
        {
            var supplierSkuScheduleRequest = new SupplierSkuScheduleRequest
            {
                OutProductId = input.SupplierProductId,
                OutSkuId = retryMarchSkuId,
                SupplierType = openSupplierTypeStr,
                IsAsync = true,
                DateFrom = startDate,
                DateTo = endDate
            };
            _ = _openSupplierService.SupplierSkuSchedule(supplierSkuScheduleRequest, tenantId);
        }
    }
    
    #region private

    /// <summary>
    /// 新增sku附加信息选项值
    /// </summary>
    /// <param name="valueOptions"></param>
    /// <param name="extraInfoId"></param>
    /// <param name="tenantId"></param>
    private async Task AddSkuExtraInfoValueOptions(
        List<SupplierProductDetailsExtraInfoValueOptionItem> valueOptions,
        long extraInfoId,
        long tenantId)
    {
        var newDbValueOptionList = new List<OpenSupplierSkuExtraInfoValueOptions>();
        foreach (var valueOptionItem in valueOptions)
        {
            var newDbValueOptions = new OpenSupplierSkuExtraInfoValueOptions
            {
                ExtraInfoId = extraInfoId,
                OptionKey = valueOptionItem.Key,
                OptionValue = valueOptionItem.Value,
                Status = ApiSkuExtraInfoStatus.Default
            };
            newDbValueOptions.SetTenantId(tenantId);
            newDbValueOptionList.Add(newDbValueOptions);
        }
        await _dbContext.AddRangeAsync(newDbValueOptionList);
    }

    /// <summary>
    /// 更新sku附加信息选项值
    /// </summary>
    /// <param name="dbSkuExtraInfoValueOptions"></param>
    /// <param name="valueOptions"></param>
    /// <param name="updateTime"></param>
    private async Task UpdateSkuExtraInfoValueOptions(
        List<OpenSupplierSkuExtraInfoValueOptions> dbSkuExtraInfoValueOptions,
        List<SupplierProductDetailsExtraInfoValueOptionItem> valueOptions,
        long extraInfoId,
        long tenantId,
        DateTime updateTime)
    {
        foreach (var valueOptionItem in valueOptions)
        {
            var dbValueOption =
                dbSkuExtraInfoValueOptions.FirstOrDefault(x =>
                    x.OptionKey == valueOptionItem.Key);
            if (dbValueOption != null)
            {
                // 有匹配 更新
                dbValueOption.OptionValue = valueOptionItem.Value;
                dbValueOption.Status = ApiSkuExtraInfoStatus.Default;
                dbValueOption.UpdateTime = updateTime;
            }
            else
            {
                // 无匹配 新增
                var newDbValueOptions = new OpenSupplierSkuExtraInfoValueOptions
                {
                    ExtraInfoId = extraInfoId,
                    OptionKey = valueOptionItem.Key,
                    OptionValue = valueOptionItem.Value,
                    Status = ApiSkuExtraInfoStatus.Default
                };
                newDbValueOptions.SetTenantId(tenantId);
                await _dbContext.AddAsync(newDbValueOptions);
            }
        }
    }


    /// <summary>
    /// 查询产品编码关联的玩乐产品租户信息
    /// </summary>
    /// <param name="openSupplierType"></param>
    /// <param name="productId"></param>
    /// <returns></returns>
    private async Task<List<long>> QueryFunProductTenantIdList(OpenSupplierType openSupplierType,string productId)
    {
        // 门票
        var relatedScenicTicketTenantResponse= await _baseProductService.QueryTenantIdList(new QueryTenantIdListInput
        {
            OpenSupplierType = openSupplierType,
            ProductId = productId
        });
        var relatedScenicTicketTenantIds = relatedScenicTicketTenantResponse.TenantIds;
        
        // 线路
        var relateLineProductTenantIds = await _dbContext.LineProductOpenSupplierSettings.IgnoreQueryFilters()
            .AsNoTracking()
            .Where(x => x.ActivityId == productId)
            .Select(x => x.TenantId)
            .ToListAsync();

        List<long> allRelatedTenantIds = relatedScenicTicketTenantIds.Concat(relateLineProductTenantIds)
            .Distinct()
            .ToList();

        return allRelatedTenantIds;
    }

    private string StringEmptyToNull(string? value)
    {
        return string.IsNullOrEmpty(value) ? null : value;
    }
    
    #endregion
}