using AutoMapper;
using Common.GlobalException;
using Contracts.Common.Product.DTOs.LineProductSku;
using Contracts.Common.Product.DTOs.ProductSku;
using Contracts.Common.Product.Enums;
using Contracts.Common.Product.Messages;
using Contracts.Common.User.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Microsoft.EntityFrameworkCore;
using Product.Api.Services.Interfaces;

namespace Product.Api.Services;

public class LineProductSkuService : ILineProductSkuService
{
    private readonly IMapper _mapper;
    private readonly CustomDbContext _dbContext;
    private readonly IBaseProductService _baseProductService;
    private readonly ICapPublisher _capPublisher;

    public LineProductSkuService(
        IMapper mapper,
        CustomDbContext dbContext,
        IBaseProductService baseProductService,
        ICapPublisher capPublisher)
    {
        _mapper = mapper;
        _dbContext = dbContext;
        _baseProductService = baseProductService;
        _capPublisher = capPublisher;
    }

    /// <summary>
    /// 创建线路产品套餐
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<long> Add(AddLineProductSkuInput input)
    {
        //接口对接产品无法手动添加.需要同步
        var lineProduct = await _dbContext.LineProduct.AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == input.LineProductId);
        if (lineProduct.PurchaseSourceType == LineProductPurchaseSourceType.InterfaceDock)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }
        
        var entity = _mapper.Map<LineProductSku>(input);
        await _dbContext.AddAsync(entity);
        await _dbContext.SaveChangesAsync();
        await PushBytePlusItemAttributesUpdate(entity);
        return entity.Id;
    }

    /// <summary>
    /// 编辑线路产品套餐
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task Edit(EditLineProductSkuInput input)
    {
        //接口对接产品无法设置单房差
        var lineProduct = await _dbContext.LineProduct.AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == input.LineProductId);
        if (lineProduct.PurchaseSourceType == LineProductPurchaseSourceType.InterfaceDock)
        {
            input.IncludedAccommodation = false;
        }
        
        var entity = await _dbContext.LineProductSku.FirstOrDefaultAsync(x => x.Id == input.Id);
        entity.Name = input.Name;
        entity.IncludedAccommodation = input.IncludedAccommodation;
        entity.FeeIncludes = _mapper.Map<List<FeeInclude>>(input.FeeIncludes);
        entity.UpdateTime = DateTime.Now;
        entity.CostDiscountRate = input.CostDiscountRate;
        await PushBytePlusItemAttributesUpdate(entity);
        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 查询线路产品套餐详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<List<GetLineProductSkuDetailOutput>> Detail(params long[] ids)
    {
        var lineProductSku = await _dbContext.LineProductSku.AsNoTracking()
            .Where(x => ids.Contains(x.Id))
            .ToListAsync();
        var lineProductIds = lineProductSku.Select(x => x.LineProductId).Distinct().ToList();
        var lineProducts = await _dbContext.LineProduct.AsNoTracking()
            .Where(x => lineProductIds.Contains(x.Id))
            .ToListAsync();
        var result = _mapper.Map<List<GetLineProductSkuDetailOutput>>(lineProductSku);
        foreach (var item in result)
        {
            var lineProduct = lineProducts.First(x => x.Id == item.LineProductId);
            item.PurchaseSourceType = lineProduct.PurchaseSourceType;
        }
        return result;
    }

    /// <summary>
    /// 批量获取产品Sku
    /// </summary>
    public async Task<List<GetLineProductSkuDetailOutput>> GetByProductIds(GetSkuByProductIdsInput input)
    {
        var lineProductSku = await _dbContext.LineProductSku.AsNoTracking()
            .Where(x => input.ProductIds.Contains(x.LineProductId))
            .WhereIF(input.InSale is true, x => x.Enabled)
            .ToListAsync();
        var result = _mapper.Map<List<GetLineProductSkuDetailOutput>>(lineProductSku);
        return result;
    }

    /// <summary>
    /// 移除线路产品套餐
    /// </summary>
    [UnitOfWork]
    public async Task<long> Remove(long id)
    {
        var sku = await _dbContext.LineProductSku.FirstOrDefaultAsync(x => x.Id == id);

        var tenantId = sku.TenantId;
        var productId = sku.LineProductId;

        _dbContext.Remove(sku);
        var skuCalendarPrice = await _dbContext.LineProductSkuCalendarPrice
            .Where(x => x.LineProductSkuId == id)
            .ToListAsync();
        _dbContext.RemoveRange(skuCalendarPrice);

        var skuTypeItems = await _dbContext.LineProductSkuTypeItems
            .Where(x => x.LineProductId == productId && x.LineProductSkuId == id)
            .ToListAsync();
        _dbContext.RemoveRange(skuTypeItems);

        //删除已配置的佣金设置
        var command = new RemoveProductCommissionMessage
        {
            TenantId = tenantId,
            ProductId = productId,
            ProductSkuIds = new List<long> { id },
            ProductType = ProductType.Line
        };
        await _capPublisher.PublishAsync(CapTopics.User.RemoveProductCommission, command);
        return productId;
    }

    /// <summary>
    /// 线路产品套餐上下架切换
    /// </summary>
    /// <param name="input"></param>
    public async Task<long> Switch(SwitchLineProductEnableInput input)
    {
        var entity = await _dbContext.LineProductSku.FirstOrDefaultAsync(x => x.Id == input.Id);
        entity.Enabled = input.Enabled;
        entity.UpdateTime = DateTime.Now;
        await _dbContext.SaveChangesAsync();
        return entity.LineProductId;
    }

    public async Task SetB2bSellingStatus(SetB2bSellingStatusInput input)
    {
        var lineProductSkus = await _dbContext.LineProductSku
            .Where(x => input.SkuIds.Contains(x.Id))
            .ToListAsync();
        if (lineProductSkus.Any())
        {
            var lineProductIds = lineProductSkus.Select(x => x.LineProductId).Distinct().ToList();
            var lineProducts = await _dbContext.LineProduct
                .Where(x => lineProductIds.Contains(x.Id))
                .ToListAsync();

            foreach (var lineProduct in lineProducts)
            {
                var relatedSkus = lineProductSkus.Where(x => x.LineProductId == lineProduct.Id)
                    .ToList();
                
                foreach (var relatedSku in relatedSkus)
                {
                    relatedSku.B2bSellingStatus = input.B2bSellingStatus;
                }

                if (relatedSkus.Count > 1)
                {
                    lineProduct.B2bSellingStatus = input.B2bSellingStatus;
                }
            }
        }
        
        await _dbContext.SaveChangesAsync();
    }

    [UnitOfWork]
    public async Task UpdateApiContent(UpdateLineSkuApiContentInput input)
    {
        if(!input.SkuContentItems.Any()) return;
        
        var lineProductSkuIds = input.SkuContentItems.Select(x => x.LineProductSkuId).Distinct().ToList();
        var lineProductSkus = await _dbContext.LineProductSku
            .Where(x => lineProductSkuIds.Contains(x.Id))
            .ToListAsync();

        foreach (var lineProductSku in lineProductSkus)
        {
            var apiContent = input.SkuContentItems.FirstOrDefault(x => x.LineProductSkuId == lineProductSku.Id);
            if(apiContent == null) continue;
            lineProductSku.FeeNote = apiContent.FeeNote;
            lineProductSku.FeeNotNote = apiContent.FeeNotNote;
            lineProductSku.ValidityDescription = apiContent.ValidityDescription;
            lineProductSku.Precautions = apiContent.Precautions;
            lineProductSku.UsageInstructions = apiContent.UsageInstructions;
            lineProductSku.OtherInstructions = apiContent.OtherInstructions;
            lineProductSku.CancellationPolicy = apiContent.CancellationPolicy;
        }
    }

    #region 分销商web

    /// <summary>
    /// 规格详情
    /// </summary>
    /// <param name="lineProductSkuInput"></param>
    /// <returns></returns>
    public async Task<List<GetLineProductSkuDetailOutput>> AgencyGet(AgencyGetLineProductSkuInput input)
    {
        var result = new List<GetLineProductSkuDetailOutput>();

        //无价格分组id.通过分销商查询
        if (!input.PriceGroupId.HasValue)
        {
            //查询分销商分组id
            var agencyInfo = await _baseProductService.GetAgencyInfo(input.AgencyId!.Value);
            if (agencyInfo.PriceGroupId is null or <= 0) return result;
            input.PriceGroupId = agencyInfo.PriceGroupId;
        }

        //查询分组下的产品配置
        var settings = await _dbContext.AgencyChannelPriceSettings.AsNoTracking()
            .Where(x => x.PriceGroupId == input.PriceGroupId
                        && x.ProductType == ChannelProductType.Line
                        && x.ProductId == input.ProductId).ToListAsync();
        if (settings.Any() is false) return result;

        var skuIds = settings.Select(x => x.SkuId).Distinct();

        var lineProductSku = await _dbContext.LineProductSku.AsNoTracking()
            .Where(x => skuIds.Contains(x.Id)
                        && x.Enabled)
            .ToListAsync();
        result = _mapper.Map<List<GetLineProductSkuDetailOutput>>(lineProductSku);

        var settingSubItemIds = settings.Where(x => x.SkuSubItemId.HasValue)
            .Select(x => x.SkuSubItemId!.Value)
            .ToList();
        var skuTypeItems = await _dbContext.LineProductSkuTypeItems.AsNoTracking()
            .Where(x => skuIds.Contains(x.LineProductSkuId) && settingSubItemIds.Contains(x.Id))
            .ToListAsync();

        foreach (var item in result)
        {
            var skuSettings = settings.Where(x => x.SkuId == item.Id).ToList();
            var currentSkuTypeItems = skuTypeItems.Where(x => x.LineProductSkuId == item.Id).ToList();
            if (currentSkuTypeItems.Any())
            {
                item.SkuTypeItemInfos = currentSkuTypeItems.Select(x => new LineProductSkuTypeItemInfo
                    {
                        SkuPriceType = x.SkuPriceType,
                        SkuTypeItemId = x.Id,
                        SkuTypeItemName = x.Name
                    })
                    .ToList();
            }
            else
            {
                item.SkuTypeItemInfos = skuSettings.Select(x => new LineProductSkuTypeItemInfo
                    {
                        SkuPriceType = (LineSkuPriceType)x.SkuSubClass
                    })
                    .ToList();
            }
        }
        
        return result;
    }

    #endregion

    #region Private
    /// <summary>
    /// 推送到byteplus
    /// </summary>
    /// <param name="lineProductSku"></param>
    /// <returns></returns>
    private async Task PushBytePlusItemAttributesUpdate(LineProductSku lineProductSku)
    {
        var title = await _dbContext.LineProduct.AsNoTracking()
            .Where(x => x.Id.Equals(lineProductSku.LineProductId))
            .Select(x => x.Title)
            .FirstOrDefaultAsync();
        var message = new BytePlusItemAttributesUpdateMessage
        {
            TenantId = lineProductSku.TenantId,
            ProductItems = new List<BytePlusProductItemDto>
            {
                new BytePlusProductItemDto
                {
                    Id = lineProductSku.Id.ToString(),
                    ItemCategory = BytePlusItemCategory.TravelLine,
                    ItemSubCategory = BytePlusItemSubCategory.TravelLine,
                    ItemId = lineProductSku.LineProductId,
                    ItemName = title,
                    ItemVariant = lineProductSku.Name
                }
            }
        };
        await _capPublisher.PublishAsync(CapTopics.Product.BytePlusItemAttributesUpdate, message);
    }
    #endregion
}