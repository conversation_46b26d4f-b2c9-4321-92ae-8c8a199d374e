using Contracts.Common.Product.Enums;
using EfCoreExtensions.EntityBase;

namespace Product.Api.Model;

public class LineProductSku : TenantBase
{
    /// <summary>
    /// 线路产品Id
    /// </summary>
    public long LineProductId { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 含住宿
    /// </summary>
    public bool IncludedAccommodation { get; set; }

    /// <summary>
    /// 费用包含
    /// </summary>
    public List<FeeInclude> FeeIncludes { get; set; } = new();

    /// <summary>
    /// 上架/下架
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// B2b售卖状态
    /// </summary>
    public bool B2bSellingStatus { get; set; }

    /// <summary>
    /// 活动id
    /// <remarks>供应端 - productid</remarks>
    /// </summary>
    public string? ActivityId { get; set; }

    /// <summary>
    /// 套餐包id
    /// <remarks>供应端 - optionId</remarks>
    /// </summary>
    public string? PackageId { get; set; }

    /// <summary>
    /// 是否时段场次
    /// </summary>
    public bool IsTimeSlot { get; set; }
    
    /// <summary>
    /// 时段场次id
    /// </summary>
    public string? TimeSlotId { get; set; }
    
    /// <summary>
    /// 时段场次名称
    /// </summary>
    public string? TimeSlotName { get; set; }
    
    /// <summary>
    /// 采购折扣比例[0-100] 2位小数
    /// <remarks>
    /// <para>如15表示为15%的折扣</para>
    /// <para>API对接直接读取供应端数据</para>
    /// </remarks>
    /// </summary>
    public decimal CostDiscountRate { get; set; }
    
    /// <summary>
    /// 费用包含
    /// </summary>
    public string? FeeNote { get; set; }

    /// <summary>
    /// 费用不含
    /// </summary>
    public string? FeeNotNote { get; set; }
    
    /// <summary>
    /// 有效期描述
    /// </summary>
    public string? ValidityDescription { get; set; }
    
    /// <summary>
    /// 注意事项
    /// </summary>
    public string? Precautions { get; set; }

    /// <summary>
    /// 使用方式
    /// </summary>
    public string? UsageInstructions { get; set; }

    /// <summary>
    /// 其他说明
    /// </summary>
    public string? OtherInstructions { get; set; }
    
    /// <summary>
    /// 取消政策
    /// </summary>
    public string? CancellationPolicy { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; } = DateTime.Now;

}

public class FeeInclude : IEquatable<FeeInclude>
{
    /// <summary>
    /// 费用类型
    /// </summary>
    public string FeeType { get; set; }

    /// <summary>
    /// 游客类型
    /// </summary>
    public LineSkuPriceType TouristType { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; }

    public bool Equals(FeeInclude other)
    {
        if (other is null) return false;
        return this.FeeType == other.FeeType && this.TouristType == other.TouristType &&
               this.Description == other.Description;
    }

    public override bool Equals(object obj) => Equals(obj as FeeInclude);
    public override int GetHashCode() => (FeeType, TouristType, Description).GetHashCode();
}