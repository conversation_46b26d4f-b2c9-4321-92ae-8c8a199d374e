using Cit.Payment.Onerway;
using Cit.Payment.WexVirtualCard;
using Cit.Payment.Yeepay;
using Cit.Storage.Redis;
using Common;
using Common.ServicesHttpClient;
using Contracts.Common.Payment.Enums;
using DingTalk.Robot;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using EventBus.Cap;
using EventBus.Cap.Transaction;
using Extensions;
using HangfireClient;
using Microsoft.Extensions.Options;
using Payment.Api.ConfigModel;
using Payment.Api.Services.Interfaces;
using MediatR;

namespace Payment.Api.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection ConfigureApplicationServices(this IServiceCollection services,
        IConfiguration configuration,
        IWebHostEnvironment env)
    {
        services.AddBasicServices(env);

        //TODO: 请求入口全部转移至bff以后移除
        services.AddControllers(options =>
        {
            options.Filters.Add<Common.Jwt.SetTenantHeaderForServiceFilter>();
        });

        services.AddCustomConfiguration(configuration);
        services.AddServices();
        services.AddCustomDbContext<CustomDbContext>(configuration.GetConnectionString("LogicDatabase")!)
            .AddUow<CustomDbContext>();
        services.AddNetCoreCAP<CustomDbContext>(configuration, env)
            .AddPublisherDecorator(sp =>
            {
                var uowTransaction = sp.GetRequiredService<IUnitOfWorkTransaction>();
                return new EFCoreTransactionReplacer(
                    () => uowTransaction.CurrentTransaction,
                    newTransaction => uowTransaction.CurrentTransaction = newTransaction);
            })
            .AddTenantIdSetterFilter(sp => (tenantId) =>
            {
                var tenantIdentify = sp.GetRequiredService<ITenantIdentify>();
                tenantIdentify.SetTenantId(tenantId);
            });
        services.AddHangfireClient(configuration.GetConnectionString("HangfireDB"), env);
        services.AddMediatR(System.Reflection.Assembly.GetExecutingAssembly());
        services.AddCitRedis(configuration.GetConnectionString("Redis"));
        services.AddAutoMapper(System.Reflection.Assembly.GetExecutingAssembly());

        services.AddYeePay();
        services.AddOnerwayClient();
        services.RegisterPayment();
        services.AddWexVirtualCardClient();
        return services;
    }

    /// <summary>
    /// 配置文件
    /// </summary>
    /// <param name="services"></param>
    /// <param name="configuration"></param>
    /// <returns></returns>
    private static IServiceCollection AddCustomConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<ServicesAddress>(configuration.GetSection(nameof(ServicesAddress)));
        services.Configure<ConfigModel.SysSetting>(configuration.GetSection(nameof(ConfigModel.SysSetting)));
        services.Configure<ConfigModel.YeePaySetting>(configuration.GetSection(nameof(ConfigModel.YeePaySetting)));
        services.Configure<OapiRobotConfig>(configuration.GetSection(nameof(OapiRobotConfig)));
        services.Configure<YeeProductInfoConfig>(configuration.GetSection(nameof(YeeProductInfoConfig)));
        services.Configure<HuiZhiWeiXinConfig>(configuration.GetSection(nameof(HuiZhiWeiXinConfig)));
        services.Configure<CurrencyExchangerateApiConfig>(configuration.GetSection(nameof(CurrencyExchangerateApiConfig)));
        services.Configure<ConfigModel.OnerwayConfig>(configuration.GetSection(nameof(ConfigModel.OnerwayConfig)));
        services.Configure<ConfigModel.YopGptransferConfig>(configuration.GetSection(nameof(ConfigModel.YopGptransferConfig)));
        return services;
    }

    /// <summary>
    /// 批量注册Service
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    private static IServiceCollection AddServices(this IServiceCollection services)
    {
        var types = System.Reflection.Assembly
            .GetExecutingAssembly()
            .GetInterfaceAndImplement(s => s.Name.EndsWith("Service"));
        foreach (var item in types)
            services.AddScoped(item.Value, item.Key);

        return services;
    }

    /// <summary>
    /// 注册易宝
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    private static IServiceCollection AddYeePay(this IServiceCollection services)
    {
        services.AddYopService(sp =>
        {
            var yeeConfigRepository = sp.GetService<IYeeConfigService>();
            var yeeConfig = yeeConfigRepository.GetYeeConfig().Result;

            //默认应用 密钥算法：RSA2048
            return new Cit.Payment.Yeepay.Base.YopConfig
            {
                ParentMetchantNo = yeeConfig.ParentMetchantNo,
                AesSecretKey = yeeConfig.AesSecretKey ?? "",
                AppKey = yeeConfig.AppKey,
                HmacSecretKey = yeeConfig.HmacSecretKey ?? "",
                PriviteKey = yeeConfig.PriviteKey,
                YopPublishKey = yeeConfig.YopPublishKey
            };
        },
        op => op.UseRegister().UseYeepay().UseYeeAccount()
     );
        services.AddYopGptransferService(sp =>
        {
            var yopGptransferConfig = sp.GetService<IOptions<ConfigModel.YopGptransferConfig>>().Value;
            return new Cit.Payment.Yeepay.Base.YopGptransferConfig()
            {
                ServerRoot = yopGptransferConfig.ServerRoot,
                AppKey = yopGptransferConfig.AppKey,
                PriviteKey = yopGptransferConfig.PriviteKey,
                YopPublishKey = yopGptransferConfig.YopPublishKey
            };
        }, op => op.UseGptransfer());
        return services;
    }

    private static IServiceCollection RegisterPayment(this IServiceCollection services)
    {
        services.AddScoped<Func<PayType, IOrderPay>>(sp =>
         {
             IOrderPay func(PayType paytype) =>
                paytype switch
                {
                    PayType.None => null,
                    PayType.YeePay => sp.GetService<IYeePayService>(),
                    PayType.Offline => sp.GetService<IOfflinePayService>(),
                    PayType.UserStoredValueCardPay => sp.GetService<IUserStoredValueCardPayService>(),
                    PayType.ZeroPay => sp.GetService<IZeroPayService>(),
                    PayType.AgencyCreditPay => sp.GetService<IAgencyCreditPayService>(),
                    PayType.AdvancePayment => sp.GetService<IReceiptPrepaymentService>(),
                    PayType.CreditCardGuarantee => sp.GetService<ICreditCardGuaranteePayService>(),
                    PayType.Onerway => sp.GetService<IOnerwayPayService>(),
                    PayType.YeepayCollection => sp.GetService<IYeegptransferPayService>(),
                    _ => throw new NotImplementedException()
                };

             return func;
         });

        services.AddScoped<Func<PayType, IOrderRefund>>(sp =>
        {
            IOrderRefund func(PayType paytype) =>
               paytype switch
               {
                   PayType.None => null,
                   PayType.YeePay => sp.GetService<IYeeRefundService>(),
                   PayType.Offline => sp.GetService<IOfflineRefundService>(),
                   PayType.UserStoredValueCardPay => sp.GetService<IUserStoredValueCardRefundService>(),
                   PayType.ZeroPay => sp.GetService<IZeroRefundService>(),
                   PayType.AgencyCreditPay => sp.GetService<IAgencyCreditRefundService>(),
                   PayType.AdvancePayment => sp.GetService<IReceiptPrepaymentService>(),
                   PayType.CreditCardGuarantee => sp.GetService<ICreditCardGuaranteeRefundService>(),
                   PayType.Onerway => sp.GetService<IOnerwayPayService>(),
                   PayType.YeepayCollection => sp.GetService<IYeegptransferPayService>(),
                   _ => throw new NotImplementedException()
               };

            return func;
        });

        return services;
    }
}
