<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <LangVersion>10.0</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <SatelliteResourceLanguages>zh</SatelliteResourceLanguages>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591,NU1803</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\BuildingBlocks\BackgroundJob\HangfireClient\HangfireClient.csproj" />
    <ProjectReference Include="..\..\..\BuildingBlocks\CommonSupport\Common\Common.csproj" />
    <ProjectReference Include="..\..\..\BuildingBlocks\CommonSupport\DingTalk.Robot\DingTalk.Robot.csproj" />
    <ProjectReference Include="..\..\..\BuildingBlocks\CommonSupport\EfCoreExtensions\EfCoreExtensions.csproj" />
    <ProjectReference Include="..\..\..\BuildingBlocks\CommonSupport\Extensions\Extensions.csproj" />
    <ProjectReference Include="..\..\..\BuildingBlocks\EventBus\EventBus.Cap\EventBus.Cap.csproj" />
    <ProjectReference Include="..\..\..\Contracts\Contracts.Common\Contracts.Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Cit.Payment.WexVirtualCard" Version="1.1.0" />
    <PackageReference Include="Cit.Payment.Yeepay" Version="1.2.9" />
    <PackageReference Include="Cit.Storage.Redis" Version="2.0.0" />
    <PackageReference Include="Cit.Payment.Onerway" Version="1.0.1" />
    <PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="10.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>
