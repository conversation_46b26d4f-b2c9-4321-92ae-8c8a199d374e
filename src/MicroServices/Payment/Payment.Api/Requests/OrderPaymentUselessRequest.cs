using Contracts.Common.Payment.Enums;
using MediatR;

namespace Payment.Api.Requests;

public class OrderPaymentUselessRequest : IRequest
{
    /// <summary>
    /// 支付单id
    /// </summary>
    public long OrderPaymentId { get; set; }

    /// <summary>
    /// 支付单类型 1-订单 2-预约单
    /// </summary>
    public OrderPaymentType OrderPaymentType { get; set; }

    /// <summary>
    /// 关联订单id
    /// </summary>
    public long OrderId { get; set; }
}
