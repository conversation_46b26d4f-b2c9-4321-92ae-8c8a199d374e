using Common.GlobalException;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Payment.Messages;
using DotNetCore.CAP;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Payment.Api.Services.Interfaces;

namespace Payment.Api.Requests;

public class OrderPaymentUselessRequestHandler : IRequestHandler<OrderPaymentUselessRequest>
{
    private readonly CustomDbContext _dbContext;
    private readonly IOrderPayService _orderPayService;
    private readonly ICapPublisher _capPublisher;
    private readonly ILogger<OrderPaymentUselessRequestHandler> _logger;
    private readonly IOrderRefundService _orderRefundService;

    public OrderPaymentUselessRequestHandler(CustomDbContext dbContext,
        IOrderPayService orderPayService,
        ICapPublisher capPublisher,
        ILogger<OrderPaymentUselessRequestHandler> logger,
        IOrderRefundService orderRefundService)
    {
        _dbContext = dbContext;
        _orderPayService = orderPayService;
        _capPublisher = capPublisher;
        _logger = logger;
        _orderRefundService = orderRefundService;
    }

    public async Task<Unit> Handle(OrderPaymentUselessRequest request, CancellationToken cancellationToken)
    {
        //查询支付成功的支付单OrderPayment
        var orderPayments = await _dbContext.OrderPayments
            .IgnoreQueryFilters()
            .Where(o => o.OrderPaymentType == request.OrderPaymentType && o.OrderId == request.OrderId)
            .Where(o => o.PayStatus == PayStatus.Paid)
            .ToListAsync();

        var currentOrderPayment = orderPayments.FirstOrDefault(o => o.Id == request.OrderPaymentId);

        if (currentOrderPayment is null)
            throw new BusinessException($"支付单{request.OrderPaymentId}不存在");
        //查询订单信息
        var paymentInfo = await _orderPayService.GetPaymentInfo(new Contracts.Common.Payment.DTOs.GetPaymentInfoInput
        {
            OrderPaymentType = request.OrderPaymentType,
            OrderId = request.OrderId,
        });
        switch (paymentInfo?.OrderStatus)
        {
            case OrderStatus.Paid:
                //如果订单状态为已支付，且当前支付单为唯一支付单，则不允许退款
                if (orderPayments.Any(s => s.Id != request.OrderPaymentId) is not true)
                {
                    throw new BusinessException($"订单{request.OrderId}订单状态为已支付，且当前支付单{request.OrderPaymentId}为唯一支付单，不允许自动退款");
                }
                break;
            case OrderStatus.Closed:
                //如果订单状态为已关闭,允许退款
                break;
            default:
                //如果订单状态为其他状态，则不允许退款
                throw new BusinessException($"订单{request.OrderId}订单状态为{paymentInfo?.OrderStatus}，不允许自动退款");
                break;
        }
        _logger.LogInformation("支付单无效支付退款，OrderPaymentId:{orderPaymentId}.", request.OrderPaymentId);
        //如果支付单状态为重复支付或者订单已关闭 执行自动退款
        var receive = new OrderRefundMessage
        {
            Description = $"订单{request.OrderId}无效支付退款",
            OrderId = currentOrderPayment.OrderId,
            RefundOrderId = currentOrderPayment.Id,//支付单id作为退款单id
            OrderRefundType = OrderRefundType.Useless,
            PayType = currentOrderPayment.PayType,
            RefundAmount = currentOrderPayment.OrderAmount,
            TenantId = currentOrderPayment.TenantId,
            OrderPaymentId = currentOrderPayment.Id,
        };
        switch (receive.PayType)
        {
            case PayType.YeePay:
                //易宝支付退款，先生成退款单 并分账回退，避免分账回退未完成退款失败
                var orderRefund = await _orderRefundService.OrderRefundGenerated(receive);
                await _capPublisher.PublishDelayAsync(TimeSpan.FromSeconds(5), CapTopics.Payment.OrderRefund, receive);
                break;
            default:
                await _capPublisher.PublishAsync(CapTopics.Payment.OrderRefund, receive);
                break;
        }
        return Unit.Value;
    }
}
