using Cit.Payment.Yeepay.Service;
using Cit.Payment.Yeepay.Service.Response.Account;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Payment.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.UOW;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Payment.Api.ConfigModel;
using Payment.Api.Services.Interfaces;

namespace Payment.Api.Services;

public class AccountPayOrderService : IAccountPayOrderService
{
    private readonly CustomDbContext _dbContext;
    private readonly IAccountService _accountService;
    private readonly IYeeConfigService _yeeConfigService;
    private readonly ICapPublisher _capPublisher;
    private readonly YeePaySetting _yeePaySetting;

    public AccountPayOrderService(CustomDbContext dbContext,
        IAccountService accountService,
        IYeeConfigService yeeConfigService,
        IOptions<YeePaySetting> yeePaySetting,
        ICapPublisher capPublisher)
    {
        _dbContext = dbContext;
        _accountService = accountService;
        _yeeConfigService = yeeConfigService;
        _capPublisher = capPublisher;
        _yeePaySetting = yeePaySetting.Value;
    }

    [UnitOfWork]
    public async Task PayOrder(AccountPayOrderMessage receive)
    {
        //生成付款单
        var accountPayOrder = await GetAccountPayOrder(receive);
        //发起付款
        var payOrderResponse = await GetPayOrderResponse(accountPayOrder);

        var orderNo = payOrderResponse?.OrderNo ?? "";
        AccountPayOrderStatus accountPayOrderStatus;
        if (!string.IsNullOrWhiteSpace(payOrderResponse?.Status))
            _ = Enum.TryParse(payOrderResponse?.Status, out accountPayOrderStatus);
        else
            accountPayOrderStatus = AccountPayOrderStatus.FAIL;

        //处理付款结果
        await AccountPayOrderResult(new AccountPayOrderResultInput
        {
            PayOrderNo = accountPayOrder.PayOrderNo,
            OrderTime = DateTime.Now,
            Status = accountPayOrderStatus,
            OrderNo = orderNo,
            FailReason = payOrderResponse?.ReturnMsg ?? ""
        });
    }

    private async Task<AccountPayOrder> GetAccountPayOrder(AccountPayOrderMessage receive)
    {
        var accountPayOrder = await _dbContext.AccountPayOrders.IgnoreQueryFilters()
            .Where(x => x.TenantId == receive.TenantId && x.PayOrderNo == receive.PayOrderNo)
            .FirstOrDefaultAsync();
        if (accountPayOrder is not null)
            return accountPayOrder;
        var merchantNo = (await _yeeConfigService.GetYeeConfig()).ParentMetchantNo;
        accountPayOrder = new AccountPayOrder()
        {
            BusinessType = receive.BusinessType,
            PayOrderNo = receive.PayOrderNo,
            OrderAmount = receive.OrderAmount,
            MerchantNo = merchantNo,
            ReceiverAccountNo = receive.ReceiverAccountNo,
            ReceiverAccountName = receive.ReceiverAccountName,
            ReceiverBankCode = receive.ReceiverBankCode,
            BankAccountType = receive.BankAccountType,
            ReceiveType = receive.ReceiveType,
            FeeChargeSide = receive.FeeChargeSide,
            Comments = receive.Comments,
            Remark = receive.Remark,
            OrderTime = DateTime.Now,
            Status = AccountPayOrderStatus.PENDING
        };
        await _dbContext.AddAsync(accountPayOrder);
        await _dbContext.SetTenantId(receive.TenantId).SaveChangesAsync();
        return accountPayOrder;
    }

    private async Task<PayOrderResponse> GetPayOrderResponse(AccountPayOrder accountPayOrder)
    {
        var request = new Cit.Payment.Yeepay.Service.Request.Account.PayOrderRequest
        {
            MerchantNo = accountPayOrder.MerchantNo,
            RequestNo = accountPayOrder.PayOrderNo.ToString(),
            OrderAmount = decimal.Round(accountPayOrder.OrderAmount, 2),
            FeeChargeSide = accountPayOrder.FeeChargeSide?.ToString(),
            ReceiveType = accountPayOrder.ReceiveType.ToString(),
            ReceiverAccountNo = accountPayOrder.ReceiverAccountNo,
            ReceiverAccountName = accountPayOrder.ReceiverAccountName,
            ReceiverBankCode = accountPayOrder.ReceiverBankCode,
            BankAccountType = accountPayOrder.BankAccountType.ToString(),
            Comments = accountPayOrder.Comments,
            NotifyUrl = _yeePaySetting.YeePayOrderNotifyUri //通知地址
        };
        var response = await _accountService.PayOrder(request);
        var payOrderResponse = response.Result;
        return payOrderResponse;
    }

    [UnitOfWork]
    public async Task AccountPayOrderResult(AccountPayOrderResultInput input)
    {
        var accountPayOrder = await _dbContext.AccountPayOrders
            .IgnoreQueryFilters()
            .Where(x => x.PayOrderNo == input.PayOrderNo)
            .SingleOrDefaultAsync();
        var finishedStatusArr = new AccountPayOrderStatus[] { AccountPayOrderStatus.SUCCESS, AccountPayOrderStatus.FAIL };
        //是否已完结单
        var hasFinished = finishedStatusArr.Contains(accountPayOrder.Status);
        accountPayOrder.ExtOrderNo = input.OrderNo;
        accountPayOrder.Status = input.Status;
        accountPayOrder.FailReason = input.FailReason;
        accountPayOrder.OrderTime = input.OrderTime;
        if (input.FinishTime.HasValue)
            accountPayOrder.FinishTime = input.FinishTime.Value;
        accountPayOrder.DebitAmount = input.DebitAmount;
        accountPayOrder.ReceiveAmount = input.ReceiveAmount;
        accountPayOrder.Fee = input.Fee;
        accountPayOrder.IsReversed = input.IsReversed;
        if (input.ReverseTime.HasValue)
            accountPayOrder.ReverseTime = input.ReverseTime.Value;

        //通知付款结果
        switch (accountPayOrder.Status)
        {
            case AccountPayOrderStatus.SUCCESS:
            case AccountPayOrderStatus.FAIL:
                if (hasFinished is true)//已完成完结处理
                    break;
                //达人付款结果处理
                var isSuccessed = accountPayOrder.Status == AccountPayOrderStatus.SUCCESS && !accountPayOrder.IsReversed;
                var command = new AccountPayOrderResultMessage
                {
                    TenantId = accountPayOrder.TenantId,
                    PayOrderNo = accountPayOrder.PayOrderNo,
                    Fee = accountPayOrder.Fee,
                    ReceiveAmount = accountPayOrder.ReceiveAmount,
                    DebitAmount = accountPayOrder.DebitAmount,
                    FailReason = input.FailReason,
                    IsSuccess = isSuccessed
                };
                switch (accountPayOrder.BusinessType)
                {
                    case AccountPayOrderBusinessType.DarenWithdrawal:
                        await _capPublisher.PublishAsync(CapTopics.Payment.DarenWithdrawalResult, command);
                        break;
                    case AccountPayOrderBusinessType.OfflineReceiptOrderWithdrawal:
                        await _capPublisher.PublishAsync(CapTopics.Payment.OfflineReceiptOrderWithdrawalResult, command);
                        break;
                    case AccountPayOrderBusinessType.TenantWithdrawal:
                        await _capPublisher.PublishAsync(CapTopics.Payment.WithdrawOrderAccountPayOrderResult, command);
                        break;
                    case AccountPayOrderBusinessType.SupplierSettlementPayOrder:
                        await _capPublisher.PublishAsync(CapTopics.Payment.SettlementOrderAccountPayOrderResult, command);
                        break;
                }
                //流水
                await _capPublisher.PublishAsync(CapTopics.Payment.AccountPayOrderBillFlow,
                    new AccountPayOrderBillFlowMessage
                    {
                        TenantId = accountPayOrder.TenantId,
                        PayOrderNo = accountPayOrder.PayOrderNo
                    });
                break;
        }
    }
}
