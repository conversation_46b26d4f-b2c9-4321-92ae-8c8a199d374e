using Common.GlobalException;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.DTOs.OrderPayment;
using Contracts.Common.Payment.Enums;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using Payment.Api.Services.Interfaces;

namespace Payment.Api.Services
{
    public class OrderPaymentService : IOrderPaymentService
    {
        private readonly CustomDbContext _dbContext;

        public OrderPaymentService(CustomDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<OrderPayment> GetPaidOrderPayment(long tenantId, long orderId, PayType? payType = null, string uniqueOrderNo = "", long? orderPaymentId = null)
        {
            var result = await _dbContext.OrderPayments.AsNoTracking()
                .IgnoreQueryFilters()
                .Where(p => p.TenantId == tenantId && p.OrderId == orderId && p.PayStatus == PayStatus.Paid)
                .WhereIF(payType.HasValue, p => p.PayType == payType.Value)
                .WhereIF(!string.IsNullOrWhiteSpace(uniqueOrderNo), p => p.UniqueOrderNo == uniqueOrderNo)
                .WhereIF(orderPaymentId.HasValue, p => p.Id == orderPaymentId!.Value)
                .OrderByDescending(p => p.Id)
                .FirstOrDefaultAsync();
            if (result is null)
                throw new BusinessException("无订单支付成功信息");
            return result;
        }

        public async Task<GetOrderPaymentInfoOutput> GetOrderPaymentInfo(GetOrderPaymentInfoInput input)
        {
            var outTradeNo = input.out_trade_no;
            var result = await _dbContext.OrderPayments
                .IgnoreQueryFilters()
                .Where(x => x.PayType == PayType.YeePay
                && x.PayChannel == PayChannel.WECHAT
                && x.BankOrderId == outTradeNo)//外部商户订单号
                .Select(x => new GetOrderPaymentInfoOutput
                {
                    OrderId = x.OrderId,
                    OrderAmount = x.OrderAmount,
                    OrderPaymentType = x.OrderPaymentType,
                    OrderType = x.OrderType,
                    ProductName = x.ProductName,
                    TenantId = x.TenantId
                })
                .FirstOrDefaultAsync();
            switch (result?.OrderPaymentType)
            {
                case OrderPaymentType.OfflineReceiptOrderPay:
                    {
                        var offlineReceiptOrderPaymentInfo = await _dbContext.OfflineReceiptOrders
                            .IgnoreQueryFilters()
                            .Where(x => x.Id == result.OrderId)
                            .Select(x => new OfflineReceiptOrderPaymentInfo
                            {
                                Remark = x.Remark,
                                SupplierName = x.SupplierName
                            })
                            .FirstOrDefaultAsync();
                        result.OfflineReceiptOrderPaymentInfo = offlineReceiptOrderPaymentInfo;
                    }
                    break;
            }
            return result;
        }


        public async Task<OrderPaymentDataOutput> GetOrderPaymentData(long orderId)
        {
            var output = await _dbContext.OrderPayments
                .Where(x => x.OrderId == orderId && x.PayStatus == PayStatus.Paid)
                .Select(x => new OrderPaymentDataOutput
                {
                    OrderId = x.OrderId,
                    UniqueOrderNo = x.UniqueOrderNo
                })
                .FirstOrDefaultAsync();
            output ??= new OrderPaymentDataOutput();

            output.OrderpaymentRefunds = await _dbContext.OrderRefunds
                .Where(x => x.OrderId == orderId && x.RefundStatus == RefundStatus.SUCCESS)
                .Select(x => new OrderPaymentRefundDataOutput
                {
                    RefundOrderId = x.RefundOrderId,
                    UniqueRefundNo = x.UniqueRefundNo
                })
                .ToListAsync();

            var profitDivide = await _dbContext.ProfitDivides
                .Where(x => x.OrderId == orderId && x.DivideStatus == DivideStatus.SUCCESS)
                .Select(x => new { x.Id })
                .FirstOrDefaultAsync();
            if (profitDivide?.Id is > 0)
            {
                var profitDivideDetails = await _dbContext.ProfitDivideDetails
                    .Where(x => x.DivideId == profitDivide.Id)
                    .Select(x => new { x.Id, x.DivideType, x.Amount })
                    .ToListAsync();
                var profitDivideDetailIds = profitDivideDetails.Select(x => x.Id);

                var divideBackDetails = await _dbContext.ProfitDivideBackDetails
                    .Where(x => profitDivideDetailIds.Contains(x.DivideDetailId))
                    .Select(x => new { x.DivideDetailId, x.Amount })
                    .ToListAsync();
                decimal paymentFee = 0;
                decimal PlatformCommission = 0;
                foreach (var profitDivideDetail in profitDivideDetails)
                {
                    var backAmount = divideBackDetails.FirstOrDefault(x => x.DivideDetailId == profitDivideDetail.Id)?.Amount ?? 0;
                    switch (profitDivideDetail.DivideType)
                    {
                        case ProfitDivideType.PaymentFee:
                            paymentFee = paymentFee + profitDivideDetail.Amount - backAmount;
                            break;
                        case ProfitDivideType.PlatformCommission:
                            PlatformCommission = PlatformCommission + profitDivideDetail.Amount - backAmount;
                            break;
                    }
                }
                output.PaymentFee = paymentFee;
                output.PlatformCommission = PlatformCommission;
            }
            return output;
        }

        public async Task<List<SearchOrderPaymentOutput>> Search(SearchOrderPaymentInput input)
        {
            var query = _dbContext.OrderPayments
                    .WhereIF(input.PayType.HasValue, x => x.PayType == input.PayType!.Value)
                    .WhereIF(input.OrderPaymentTypes?.Length is >0, x => input.OrderPaymentTypes!.Contains(x.OrderPaymentType))
                    .WhereIF(input.PayStatus.HasValue, x => x.PayStatus == input.PayStatus!.Value)
                    .WhereIF(input.BeginDate.HasValue, x => x.CreateTime >= input.BeginDate!.Value)
                    .WhereIF(input.EndDate.HasValue, x => x.CreateTime < input.EndDate!.Value.AddDays(1))
                    .OrderBy(x => x.CreateTime)
                    .Select(x => new SearchOrderPaymentOutput
                    {
                        OrderPaymentType = x.OrderPaymentType,
                        OrderId = x.OrderId,
                        OrderType = x.OrderType,
                        PayType = x.PayType,
                        PayChannel = x.PayChannel,
                        PayWay = x.PayWay,
                        ProductName = x.ProductName,
                        OrderAmount = x.OrderAmount,
                        FundProcessType = x.FundProcessType,
                        UniqueOrderNo = x.UniqueOrderNo,
                        PayStatus = x.PayStatus,
                        CsSuccessDate = x.CsSuccessDate,
                        MerchantFee = x.MerchantFee,
                        YpSettleAmount = x.YpSettleAmount,
                        CreateTime = x.CreateTime,
                        LastUpdateTime = x.LastUpdateTime
                    });
            return await query.ToListAsync();
        }

    }
}
