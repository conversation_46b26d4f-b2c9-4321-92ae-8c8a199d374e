using AutoMapper;
using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common.Marketing.DTOs.UserCoupon;
using Contracts.Common.Marketing.Messages;
using Contracts.Common.Notify.Enums;
using Contracts.Common.Notify.Messages;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.DTOs.OfflineReceipt;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Payment.Messages;
using Contracts.Common.Tenant.DTOs.Supplier;
using Contracts.Common.WeChat.DTOs;
using DotNetCore.CAP;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Payment.Api.Extensions;
using Payment.Api.Services.Interfaces;

namespace Payment.Api.Services;

public class OfflineReceiptOrderService : IOfflineReceiptOrderService
{
    private readonly CustomDbContext _dbContext;
    private readonly IOrderPayService _orderPayService;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ICapPublisher _capPublisher;
    private readonly IMapper _mapper;
    private readonly ServicesAddress _servicesAddress;

    public OfflineReceiptOrderService(CustomDbContext dbContext,
        IOrderPayService orderPayService,
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> servicesAddress,
        ICapPublisher capPublisher,
        IMapper mapper)
    {
        _dbContext = dbContext;
        _orderPayService = orderPayService;
        _httpClientFactory = httpClientFactory;
        _capPublisher = capPublisher;
        _mapper = mapper;
        _servicesAddress = servicesAddress.Value;
    }

    [UnitOfWork]
    public async Task<OfflineReceiptOrderOutput> Create(OfflineReceiptOrderInput input,
        OfflineReceiptOrderPayerInfoInput payerInfo)
    {
        var offlineReceipt = await _dbContext.OfflineReceipts.IgnoreQueryFilters()
             .Where(x => x.Id == input.OfflineReceiptId)
             .FirstOrDefaultAsync();
        OfflineReceiptOrderPayerInfo offlineReceiptOrderPayerInfo = new()
        {
            CustomerUserId = payerInfo.CustomerUserId,
            NickName = payerInfo.Nickname,
            UserID = payerInfo.OpenId
        };

        if (!string.IsNullOrWhiteSpace(input.Code))
        {
            GetUserInfoOutput userInfoOutput = await GetUserInfo(input.Code);
            offlineReceiptOrderPayerInfo.UserID = userInfoOutput.OpenId;
            offlineReceiptOrderPayerInfo.NickName = userInfoOutput.Nickname;
        }

        decimal discountAmount = 0;
        string? discountName = null;
        if (input.UserCouponId is > 0)
        {
            var userCoupon = await GetOrderUserCoupon(new GetOfflineReceiptOrderUserCouponsInput
            {
                UserId = payerInfo.CustomerUserId!.Value,
                OfflineReceiptId = input.OfflineReceiptId,
                OrderAmount = input.OrderAmount,
                UserCouponId = input.UserCouponId.Value,
            });
            if (userCoupon?.IsEnable is not true)
            {
                throw new BusinessException(ErrorTypes.Marketing.UserCouponDisabled);//消费券不可用
            }
            discountAmount = userCoupon.Discount;
            discountName = userCoupon.CouponName;
        }

        OfflineReceiptOrder order = new()
        {
            OfflineReceiptId = offlineReceipt.Id,
            OrderAmount = input.OrderAmount,
            DiscountAmount = discountAmount,//优惠金额
            DiscountName = discountName,
            PayChannel = PayChannel.None,
            PayType = PayType.YeePay,
            PayerInfo = offlineReceiptOrderPayerInfo,
            Status = OfflineReceiptOrderStatus.WaitingForPay,
            CreateTime = DateTime.Now,
            SupplierId = offlineReceipt.SupplierId,
            SupplierName = offlineReceipt.SupplierName,
            Title = offlineReceipt.Title,
            Remark = offlineReceipt.Remark,
        };
        await _dbContext.AddAsync(order);

        //使用优惠券                
        if (input.UserCouponId is > 0)
        {
            var UserCouponUsedCommand = new UserCouponUsedMessage
            {
                TenantId = offlineReceipt.TenantId,
                UserCouponId = input.UserCouponId!.Value,
                BaseOrderId = order.Id,//线下收款单id
                OrderType = OrderType.None
            };
            await _capPublisher.PublishAsync(CapTopics.Marketing.UserCouponUsed, UserCouponUsedCommand);
        }
        _dbContext.SetTenantId(offlineReceipt.TenantId);

        return new OfflineReceiptOrderOutput
        {
            OfflineReceiptOrderId = order.Id
        };
    }

    private async Task<GetUserInfoOutput> GetUserInfo(string code)
    {
        var url = _servicesAddress.Wechat_GetUserInfo(code);
        var result = await _httpClientFactory.InternalGetAsync<GetUserInfoOutput>(url);
        return result;
    }

    private async Task<OrderUserCouponOutput> GetOrderUserCoupon(GetOfflineReceiptOrderUserCouponsInput input)
    {
        using HttpContent httpContent = new StringContent(JsonConvert.SerializeObject(input),
            Encoding.UTF8, "application/json");
        var result = await _httpClientFactory.InternalPostAsync<OrderUserCouponOutput>(_servicesAddress.Markting_GetByOfflineReceipt(),
            httpContent: httpContent);
        return result;
    }

    public async Task<GetOfflineReceiptOrderOutput> GetOfflineReceiptOrder(long offlineReceiptOrderId)
    {
        var order = await _dbContext.OfflineReceiptOrders
            .AsNoTracking()
            .Where(x => x.Id == offlineReceiptOrderId)
            .FirstOrDefaultAsync();
        return _mapper.Map<GetOfflineReceiptOrderOutput>(order);
    }

    public async Task<OrderPayOutput> Pay(OfflineReceiptOrderPayInput input)
    {
        var order = await _dbContext.OfflineReceiptOrders
            .IgnoreQueryFilters()
            .AsNoTracking()
            .Where(x => x.Id == input.OfflineReceiptOrderId)
            .FirstOrDefaultAsync();

        var userId = string.Empty;//用户ID用户标识
        switch (input.PayChannel)
        {
            case PayChannel.WECHAT:
                if (input.PayWay == PayWay.MINI_PROGRAM || input.PayWay == PayWay.WECHAT_OFFIACCOUNT)
                    userId = order.PayerInfo.UserID;
                break;
        }

        OrderPayInput orderPayInput = new()
        {
            OrderPaymentType = OrderPaymentType.OfflineReceiptOrderPay,
            OrderId = order.Id,
            PayChannel = input.PayChannel,
            PayType = input.PayType,
            PayWay = input.PayWay,
            UserId = userId,//用户ID用户标识，微信公众号/微信小程序为用户的openId，支付宝生活号/支付宝小程序/银联 JS 支付为用户的userId
            RedirectUrl = input.RedirectUrl
        };

        var orderPayOutput = await _orderPayService.Pay(orderPayInput);

        return orderPayOutput;
    }

    [UnitOfWork]
    public async Task PaySuccess(Contracts.Common.Order.Messages.OrderStatusChangeByPaySuccessMessage receive)
    {
        var offlineReceiptOrder = await _dbContext.OfflineReceiptOrders
            .IgnoreQueryFilters()
            .Where(x => x.Id == receive.OrderId)
            .FirstOrDefaultAsync();

        //非待支付状态 订单无效支付退款 (如订单关闭、重复支付等场景)
        if (offlineReceiptOrder.Status != OfflineReceiptOrderStatus.WaitingForPay)
        {
            await _capPublisher.PublishDelayAsync(TimeSpan.FromSeconds(10),//延时10秒处理，避免订单状态未更新
            CapTopics.Payment.OrderPaymentUselessRefund, new OrderPaymentUselessRefundMessage
            {
                OrderPaymentId = receive.OrderPaymentId,
                OrderPaymentType = receive.OrderPaymentType,
                OrderId = receive.OrderId,
            });
            return;
        }

        _ = Enum.TryParse(receive.PaymentChannel, out PayChannel payChannel);
        offlineReceiptOrder.PayChannel = payChannel;
        offlineReceiptOrder.PayType = receive.PaymentType;
        offlineReceiptOrder.PayTime = DateTime.Now;
        offlineReceiptOrder.Status = OfflineReceiptOrderStatus.Received;//已收款

        //支付手续费 预算
        var paymentFeeRate = (await GetTenantFeeRate(offlineReceiptOrder.TenantId)).PaymentFeeRate;
        var paymentAmount = offlineReceiptOrder.OrderAmount - offlineReceiptOrder.DiscountAmount;
        decimal paymentFee = Math.Round(paymentFeeRate * paymentAmount, 2);
        offlineReceiptOrder.PaymentFee = paymentFee;//收款手续费
        OfflineReceiptOrderFlow offlineReceiptOrderFlow = new()
        {
            OfflineReceiptOrderId = offlineReceiptOrder.Id,
            Amount = offlineReceiptOrder.OrderAmount,
            FlowType = OfflineReceiptOrderFlowType.Receive,
            PaymentFee = paymentFee,
            CreateTime = DateTime.Now,
            Status = offlineReceiptOrder.Status
        };
        await _dbContext.AddAsync(offlineReceiptOrderFlow);
        var tenantId = offlineReceiptOrder.TenantId;
        //发送消息通知供应商
        await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess,
            new NotifyMessageProcessMessage
            {
                NotifyEventSubType = NotifyEventSubType.Payment_Offline,
                TenantId = tenantId,
                NotifyMode = NotifyMode.Wechat,
                SendToTheRole = SendToTheRole.SupplierStaff,
                NotifyChannel = NotifyChannel.None,
                Variables = new
                {
                    offlineReceiptOrder.SupplierId,
                    Nickname = offlineReceiptOrder.PayerInfo.NickName,
                    PaymentAmount = offlineReceiptOrder.OrderAmount,
                    ReceiptTime = offlineReceiptOrder.CreateTime
                }
            });
        _dbContext.SetTenantId(tenantId);
    }

    private async Task<DivideFeeRate> GetTenantFeeRate(long tenantId)
    {
        var result = await _httpClientFactory.InternalGetAsync<JObject>(
            _servicesAddress.Tenant_GetTenantSysConfig(),
            new List<KeyValuePair<string, string>> { new("tenant", tenantId.ToString()) });
        var paymentFeeRate = decimal.Parse(result["paymentFeeRate"].ToString()); //支付手续费率
        var platformCommissionRate = decimal.Parse(result["platformCommissionRate"].ToString());//平台佣金费率

        return new DivideFeeRate { PaymentFeeRate = paymentFeeRate, PlatformCommissionRate = platformCommissionRate };
    }

    [UnitOfWork]
    public async Task Cancel(OfflineReceiptOrderCancelInput input, long? userId)
    {
        var order = await _dbContext.OfflineReceiptOrders
            .Where(x => x.Id == input.OfflineReceiptOrderId)
            .WhereIF(userId is > 0, x => x.PayerInfo.CustomerUserId == userId!.Value)
            .FirstOrDefaultAsync();
        if (order?.Status != OfflineReceiptOrderStatus.WaitingForPay)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        order.Status = OfflineReceiptOrderStatus.Canceled;//已取消
                                                          //返还消费券                
        if (order.DiscountAmount > 0 && order.PayerInfo.CustomerUserId is > 0)
        {
            var userCouponReturnMessage = new UserCouponReturnMessage
            {
                TenantId = order.TenantId,
                BaseOrderId = order.Id,//线下收款单id
            };
            await _capPublisher.PublishAsync(CapTopics.Marketing.UserCouponReturn, userCouponReturnMessage);
        }
    }

    #region 线下收款提现信息

    public async Task<OfflineReceiptStatOutput> GetOfflineReceiptStat(long supplierId)
    {
        var todayQuery = from flow in _dbContext.OfflineReceiptOrderFlows.AsNoTracking()
                         join order in _dbContext.OfflineReceiptOrders.AsNoTracking()
                         on flow.OfflineReceiptOrderId equals order.Id
                         where order.SupplierId == supplierId
                         && flow.CreateTime >= DateTime.Today
                         && flow.CreateTime < DateTime.Today.AddDays(1)
                         select new
                         {
                             flow.FlowType,
                             flow.Amount,
                             flow.PaymentFee,
                             order.Status
                         };
        var todayFlows = await todayQuery.ToListAsync();

        var todayReceived = todayFlows.Where(x => x.FlowType == OfflineReceiptOrderFlowType.Receive).Sum(x => x.Amount);
        var todayRefund = todayFlows.Where(x => x.FlowType == OfflineReceiptOrderFlowType.Refund).Sum(x => x.Amount);
        var todayPaymentFee = todayFlows.Where(x =>
            x.FlowType == OfflineReceiptOrderFlowType.Receive
            && x.Status == OfflineReceiptOrderStatus.Received
        ).Sum(x => x.PaymentFee);
        var todayReceivedCount = todayFlows.Where(x => x.FlowType == OfflineReceiptOrderFlowType.Receive).Count();
        OfflineReceiptStatOutput output = new()
        {
            TodayReceivedAmount = todayReceived + todayRefund - todayPaymentFee,
            TodayReceivedCount = todayReceivedCount,
        };
        var query = from flow in _dbContext.OfflineReceiptOrderFlows.AsNoTracking()
                    join order in _dbContext.OfflineReceiptOrders.AsNoTracking()
                    on flow.OfflineReceiptOrderId equals order.Id
                    where order.SupplierId == supplierId
                    && order.Status == OfflineReceiptOrderStatus.Received
                    && flow.FlowType == OfflineReceiptOrderFlowType.Receive
                    select flow;
        var flows = await query.ToListAsync();
        //可提现金额
        output.WithdrawalAmount = flows.Sum(x => x.Amount - x.PaymentFee);

        //获取供应商银行账户信息
        var supplier = await _httpClientFactory.InternalGetAsync<GetSupplierOutput>(
                 requestUri: _servicesAddress.Tenant_GetSupplier(supplierId));
        if (supplier is not null)
        {
            output.Account = new WithdrawalAccount
            {
                AccountName = supplier.AccountName,
                AccountNumber = supplier.BankAccount,
                BankCode = supplier.BankCode,
                BankName = supplier.BankName
            };
        }

        return output;
    }

    #endregion

    public async Task<List<GetOfflineReceiptOrderOutput>> SearchByIds(IEnumerable<long> ids)
    {
        if (ids?.Any() is not true)
            return new List<GetOfflineReceiptOrderOutput>();
        var offlineReceiptOrders = await _dbContext.OfflineReceiptOrders
            .Where(x => ids.Contains(x.Id))
            .ToListAsync();
        return _mapper.Map<List<GetOfflineReceiptOrderOutput>>(offlineReceiptOrders); ;
    }
}
