using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.DTOs.OfflineReceipt;
using Contracts.Common.Payment.Messages;

namespace Payment.Api.Services.Interfaces
{
    public interface IOfflineReceiptOrderService
    {
        /// <summary>
        /// 付款码付款下单
        /// </summary>
        /// <param name="input"></param>
        /// <param name="payerInfo"></param>
        /// <returns></returns>
        /// <exception cref="ErrorTypes.Marketing.UserCouponDisabled"></exception>
        Task<OfflineReceiptOrderOutput> Create(OfflineReceiptOrderInput input, OfflineReceiptOrderPayerInfoInput payerInfo);

        /// <summary>
        /// 获取线下付款单信息
        /// </summary>
        /// <param name="offlineReceiptOrderId"></param>
        /// <returns></returns>
        Task<GetOfflineReceiptOrderOutput> GetOfflineReceiptOrder(long offlineReceiptOrderId);

        /// <summary>
        /// 取消未支付的线下付款单
        /// </summary>
        /// <param name="input"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        /// <exception cref="ErrorTypes.Common.NotSupportedOperation"></exception>
        Task Cancel(OfflineReceiptOrderCancelInput input, long? userId);

        /// <summary>
        /// 支付
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="ErrorTypes.Order.OrderNotFind"></exception>
        /// <exception cref="ErrorTypes.Payment.CurrencyNonsupport"></exception>
        Task<OrderPayOutput> Pay(OfflineReceiptOrderPayInput input);

        /// <summary>
        /// 支付成功处理
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        Task PaySuccess(Contracts.Common.Order.Messages.OrderStatusChangeByPaySuccessMessage receive);

        /// <summary>
        /// 供应商线下收款统计信息
        /// </summary>
        /// <param name="supplierId">供应商id</param>
        /// <returns></returns>
        Task<OfflineReceiptStatOutput> GetOfflineReceiptStat(long supplierId);

        Task<List<GetOfflineReceiptOrderOutput>> SearchByIds(IEnumerable<long> ids);

    }
}
