using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.DTOs.OrderPayment;
using Contracts.Common.Payment.Enums;

namespace Payment.Api.Services.Interfaces
{
    public interface IOrderPaymentService
    {
        /// <summary>
        /// 获取订单支付成功的支付信息
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="orderId">订单id</param>
        /// <param name="payType">支付类型 限定可选</param>
        /// <param name="uniqueOrderNo">外部支付单号</param>
        /// <param name="orderPaymentId">支付单id</param>
        /// <returns></returns>
        Task<OrderPayment> GetPaidOrderPayment(long tenantId, long orderId, PayType? payType = null, string uniqueOrderNo = "", long? orderPaymentId = null);

        /// <summary>
        /// 微信支付信息 用于服务商用于点金计划商户小票功能
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetOrderPaymentInfoOutput> GetOrderPaymentInfo(GetOrderPaymentInfoInput input);


        /// <summary>
        /// 订单数据信息
        /// </summary>
        /// <param name="orderId"></param>
        /// <returns></returns>
        Task<OrderPaymentDataOutput> GetOrderPaymentData(long orderId);

        /// <summary>
        /// 查询支付单列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<SearchOrderPaymentOutput>> Search(SearchOrderPaymentInput input);
    }
}
