using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.DTOs.OrderRefund;
using Contracts.Common.Payment.Messages;

namespace Payment.Api.Services.Interfaces
{
    public interface IOrderRefundService
    {
        /// <summary>
        /// 订单申请退款
        /// </summary>
        /// <param name="receive"></param>
        /// <returns></returns>
        [Obsolete]
        Task<OrderRefundResultMessage> Refund(OrderRefundMessage receive);

        /// <summary>
        /// 退款单退款
        /// </summary>
        /// <param name="orderRefund"></param>
        /// <returns></returns>
        Task<OrderRefundResultMessage> Refund(OrderRefundDetailDto orderRefund);

        /// <summary>
        /// 生成退款单
        /// </summary>
        /// <param name="receive"></param>
        /// <returns></returns>
        Task<OrderRefundDetailDto> OrderRefundGenerated(OrderRefundMessage receive);


        /// <summary>
        /// 订单支付作废退款 (如订单关闭、重复支付等场景)
        /// </summary>
        /// <param name="receive"></param>
        /// <returns></returns>
        Task OrderPaymentUselessRefund(OrderPaymentUselessRefundMessage receive);

        /// <summary>
        /// 订单退款结果处理
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task RefundResult(OrderRefundResultInput input);

        /// <summary>
        /// 搜索退款单列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<SearchOrderRefundOutput>> SearchOrderRefunds(SearchOrderRefundInput input);
    }
}
