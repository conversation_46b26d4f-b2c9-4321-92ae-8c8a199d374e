using Cit.Storage.Redis;
using Common.GlobalException;
using Contracts.Common.Order.Messages;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Payment.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Microsoft.EntityFrameworkCore;
using Payment.Api.Services.Interfaces;

namespace Payment.Api.Services;

public class OrderPayResultService : IOrderPayResultService
{
    private readonly CustomDbContext _dbContext;
    private readonly ICapPublisher _capPublisher;
    private readonly Func<PayType, IOrderPay> _orderPayFunc;

    public OrderPayResultService(CustomDbContext dbContext,
        ICapPublisher capPublisher,
        Func<PayType, IOrderPay> orderPayFunc)
    {
        _dbContext = dbContext;
        _capPublisher = capPublisher;
        _orderPayFunc = orderPayFunc;
    }

    [UnitOfWork]
    public async Task<OrderPayOutDto> OrderPayProcessing(OrderPayDto dto)
    {
        /*调起支付 返回支付结果*/
        var orderPay = _orderPayFunc(dto.PayType);
        OrderPayOutDto result = await orderPay.Pay(dto);

        /*更新支付记录 返回支付订单状态等信息 */
        OrderPayResultInput orderPayResultInput = new()
        {
            OrderPaymentId = dto.OrderPaymentId,
            PayType = dto.PayType,
            OrderId = dto.OrderId,
            OrderAmount = dto.OrderAmount,
            PayStatus = result.PayStatus,
            UniqueOrderNo = result.UniqueOrderNo,
            Extend = new OrderPayResultExtend
            {
                Message = result.Message,
                PrePayTn = result.PrePayTn,
                FundProcessType = result.FundProcessType,
                BankOrderId = result.BankOrderId
            }
        };
        //支付结果处理
        await Handle(orderPayResultInput);
        return result;
    }

    public string GetLockName(PayType payType, long orderId) => $"Payment:OrderPayResult:{payType}_{orderId}";

    [UnitOfWork]
    public async Task Handle(OrderPayResultInput input)
    {
        var orderPayment = await _dbContext.OrderPayments
            .IgnoreQueryFilters()
            .Where(o => o.PayType == input.PayType)
            .WhereIF(input.OrderPaymentId.HasValue, o => o.Id == input.OrderPaymentId!.Value)
            .WhereIF(input.OrderId.HasValue, o => o.OrderId == input.OrderId)
            .WhereIF(input.PayWay.HasValue, o => o.PayWay == input.PayWay!.Value)
            .WhereIF(input.PayChannel.HasValue, o => o.PayChannel == input.PayChannel!.Value)
            .OrderByDescending(o => o.Id)
            .FirstOrDefaultAsync();
        //支付信息不存在
        if (orderPayment == null)
            throw new BusinessException("订单支付信息不存在");
        //已支付
        if (orderPayment.PayStatus == PayStatus.Paid)
            return;

        orderPayment.UniqueOrderNo ??= input.UniqueOrderNo;
        orderPayment.PayStatus = input.PayStatus;
        orderPayment.LastUpdateTime = DateTime.Now;
        if (input.Extend is not null)
        {
            orderPayment.Message += input.Extend.Message;
            orderPayment.PrePayTn = input.Extend.PrePayTn;
            orderPayment.BankOrderId = input.Extend.BankOrderId;
            if (input.Extend.FundProcessType.HasValue)
                orderPayment.FundProcessType = input.Extend.FundProcessType.Value;
        }

        var tenantId = orderPayment.TenantId;

        switch (orderPayment.PayStatus)
        {
            case PayStatus.Paid:
                //支付成功通知订单处理事件
                await OrderStatusChangeByPaySuccessPublish(orderPayment);
                break;
        }
    }

    private async Task OrderStatusChangeByPaySuccessPublish(OrderPayment orderPayment)
    {
        PaymentScene paymentScene = orderPayment.OrderPaymentType switch
        {
            OrderPaymentType.OrderPay => PaymentScene.OrderPay,//订单支付
            OrderPaymentType.ReservationOrderPay => PaymentScene.ReservationOrderPay,
            OrderPaymentType.OfflineReceiptOrderPay => PaymentScene.OfflineReceiptPay,
            _ => PaymentScene.None
        };

        //新增支付流水
        var orderPaymentDetail = new OrderPaymentDetail
        {
            OrderPaymentId = orderPayment.Id,
            PaymentScene = paymentScene,
            Amount = orderPayment.OrderAmount,
            ExtNo = orderPayment.UniqueOrderNo,
            PaymentUserType = PaymentUserType.Individual
        };
        orderPaymentDetail.SetTenantId(orderPayment.TenantId);
        await _dbContext.OrderPaymentDetails.AddAsync(orderPaymentDetail);
        //支付成功通知
        var command = new OrderStatusChangeByPaySuccessMessage
        {
            OrderPaymentId = orderPayment.Id,
            OrderPaymentType = orderPayment.OrderPaymentType,
            OrderId = orderPayment.OrderId,
            PaymentChannel = orderPayment.PayChannel.ToString(),
            PaymentType = orderPayment.PayType,
            PaymentMode = orderPayment.PayWay.ToString(),
            PaymentExternalNo = orderPayment.UniqueOrderNo,
            PayTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
        };
        switch (orderPayment.OrderPaymentType)
        {
            case OrderPaymentType.OrderPay:
                //订单
                await _capPublisher.PublishAsync(CapTopics.Order.StatusChangeByPaySuccess, command);
                break;
            case OrderPaymentType.ReservationOrderPay:
                //预约单
                await _capPublisher.PublishAsync(CapTopics.Order.ReservationOrderPaySuccess, command);
                break;
            case OrderPaymentType.OfflineReceiptOrderPay:
                //线下收款单
                await _capPublisher.PublishAsync(CapTopics.Payment.OfflineReceiptOrderPaySuccess, command);
                break;
            case OrderPaymentType.AgencyCreditCharge:
                //分销商额度充值
                await _capPublisher.PublishAsync(CapTopics.Tenant.AgencyChargeStatusChangeByPaySuccess, command);
                break;
            case OrderPaymentType.GroupBookingOrder:
                //团房单首尾款支付
                await _capPublisher.PublishAsync(CapTopics.Order.GroupBookingOrderStatusChangeByPaySuccess, command);
                break;
            default:
                break;
        }
        if (orderPayment.PayType == PayType.YeePay)
        {
            AccountBusinessType? businessType = orderPayment.OrderPaymentType switch
            {
                OrderPaymentType.OrderPay => AccountBusinessType.OrderPay,
                OrderPaymentType.ReservationOrderPay => AccountBusinessType.ReservationOrderPay,
                OrderPaymentType.OfflineReceiptOrderPay => AccountBusinessType.OfflineReceiptPay,
                OrderPaymentType.AgencyCreditCharge => AccountBusinessType.AgencyCreditCharge,
                OrderPaymentType.GroupBookingOrder => AccountBusinessType.OrderPay,
                _ => null
            };
            if (businessType is not null)
                await _capPublisher.PublishAsync(CapTopics.Payment.AccountInfoDetailRecord,
                    new AccountInfoDetailRecordMessage
                    {
                        TenantId = orderPayment.TenantId,
                        BusinessType = businessType.Value,
                        BusinessOrderId = orderPayment.OrderId,
                        OrderType = orderPayment.OrderType,
                        Income = orderPayment.OrderAmount,
                        ExtNo = orderPayment.UniqueOrderNo,
                        AccountTime = orderPayment.CreateTime
                    });
        }
    }

    public async ValueTask<decimal> CalculatePaymentFee(CalculatePaymentFeeDto dto)
    {
        if (!dto.PayType.HasValue)
            return 0m;
        var orderPay = _orderPayFunc(dto.PayType!.Value);
        decimal result = await orderPay.CalculatePaymentFee(dto);
        return result;
    }

}
