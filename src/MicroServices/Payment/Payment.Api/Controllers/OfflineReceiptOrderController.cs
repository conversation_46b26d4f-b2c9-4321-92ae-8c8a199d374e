using Common.GlobalException;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.DTOs.OfflineReceipt;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Payment.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Payment.Api.Services.Interfaces;

namespace Payment.Api.Controllers
{
    [Route("[controller]/[action]")]
    [ApiController]
    public class OfflineReceiptOrderController : ControllerBase
    {
        private readonly IOfflineReceiptOrderService _offlineReceiptOrderService;
        private readonly IOfflineReceiptOrderFlowService _offlineReceiptOrderFlowService;
        private readonly IOfflineReceiptRefundOrderService _offlineReceiptRefundOrderService;
        private readonly IOfflineReceiptWithdrawalService _offlineReceiptWithdrawalService;

        public OfflineReceiptOrderController(IOfflineReceiptOrderService offlineReceiptOrderService,
            IOfflineReceiptOrderFlowService offlineReceiptOrderFlowService,
            IOfflineReceiptRefundOrderService offlineReceiptRefundOrderService,
            IOfflineReceiptWithdrawalService offlineReceiptWithdrawalService)
        {
            _offlineReceiptOrderService = offlineReceiptOrderService;
            _offlineReceiptOrderFlowService = offlineReceiptOrderFlowService;
            _offlineReceiptRefundOrderService = offlineReceiptRefundOrderService;
            _offlineReceiptWithdrawalService = offlineReceiptWithdrawalService;
        }
        #region H5

        /// <summary>
        /// 线下收款下单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(OfflineReceiptOrderOutput), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default,ErrorTypes.Marketing.UserCouponDisabled)]
        public async Task<IActionResult> Create(OfflineReceiptOrderInput input)
        {
            var currentUser = HttpContext.GetCurrentUser();
            if (currentUser is null && string.IsNullOrWhiteSpace(input.Code))
            {
                throw new BusinessException("code 不能为空。");
            }
            var payerInfo = new OfflineReceiptOrderPayerInfoInput
            {
                CustomerUserId = currentUser?.userid,
                Nickname = currentUser?.nickname
            };
            var result = await _offlineReceiptOrderService.Create(input, payerInfo);
            return Ok(result);
        }

        /// <summary>
        /// 获取线下收款单信息
        /// </summary>
        /// <param name="orderId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(GetOfflineReceiptOrderOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> Get(long orderId)
        {
            var result = await _offlineReceiptOrderService.GetOfflineReceiptOrder(orderId);
            return Ok(result);
        }

        /// <summary>
        /// 获取线下收款码支付信息
        /// </summary>
        /// <param name="orderId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(PaymentInfoOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> PaymentInfo(long orderId)
        {
            var result = await _offlineReceiptOrderService.GetOfflineReceiptOrder(orderId);
            PaymentInfoOutput paymentInfo = new()
            {
                UserId = result.PayerInfo?.CustomerUserId ?? 0,
                Amount = result.OrderAmount - result.DiscountAmount,//支付金额=付款金额-优惠金额
                CurrencyCode = Contracts.Common.Payment.Enums.Currency.CNY.ToString(),//CNY
                ProductName = result.Title,
                OrderStatus = result.Status switch
                {
                    OfflineReceiptOrderStatus.WaitingForPay => OrderStatus.WaitingForPay,
                    OfflineReceiptOrderStatus.Received => OrderStatus.Paid,
                    OfflineReceiptOrderStatus.Refunding => OrderStatus.Paid,
                    OfflineReceiptOrderStatus.Refunded => OrderStatus.Paid,
                    OfflineReceiptOrderStatus.Withdrawing => OrderStatus.Paid,
                    OfflineReceiptOrderStatus.Withdrawed => OrderStatus.Paid,
                    OfflineReceiptOrderStatus.Canceled => OrderStatus.Closed,
                    _ => OrderStatus.Closed
                },
            };
            return Ok(paymentInfo);
        }

        /// <summary>
        /// 线下收款支付
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(OrderPayOutput), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default,ErrorTypes.Order.OrderNotFind)]
        [SwaggerResponseExt(default,ErrorTypes.Payment.CurrencyNonsupport)]
        public async Task<IActionResult> Pay(OfflineReceiptOrderPayInput input)
        {
            var result = await _offlineReceiptOrderService.Pay(input);
            return Ok(result);
        }

        /// <summary>
        /// 取消
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [SwaggerResponseExt(default,ErrorTypes.Common.NotSupportedOperation)]
        public async Task<IActionResult> Cancel(OfflineReceiptOrderCancelInput input)
        {
            await _offlineReceiptOrderService.Cancel(input, HttpContext.GetCurrentUser()?.userid);
            return Ok();
        }

        #endregion

        #region Supplier

        /// <summary>
        /// 供应商线下收款统计信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(OfflineReceiptStatOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetOfflineReceiptStat()
        {
            long supplierId = HttpContext.GetCurrentUser().GetSupplierId();
            var result = await _offlineReceiptOrderService.GetOfflineReceiptStat(supplierId);
            return Ok(result);
        }

        /// <summary>
        /// 获取收款码明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(PagingModel<OfflineReceiptOrderFlowOutput, OfflineReceiptOrderFlowStat>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetOfflineReceiptOrderFlows(GetOfflineReceiptOrderFlowsInput input)
        {
            long supplierId = HttpContext.GetCurrentUser().GetSupplierId();
            var result = await _offlineReceiptOrderFlowService.GetOfflineReceiptOrderFlows(input, supplierId);
            return Ok(result);
        }

        /// <summary>
        /// 供应商发起退款
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> RefundBySupplier(OfflineReceiptOrderRefundInput input)
        {
            CurrentUser currentUser = HttpContext.GetCurrentUser();
            long supplierId = HttpContext.GetCurrentUser().GetSupplierId();
            await _offlineReceiptRefundOrderService.Refund(input, currentUser, supplierId);
            return Ok();
        }

        /// <summary>
        /// 根据收款单id获取退款状态信息
        /// </summary>
        /// <param name="offlineReceiptOrderId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(GetRefundStatusOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetRefundStatus(long offlineReceiptOrderId)
        {
            var result = await _offlineReceiptRefundOrderService.GetRefundStatus(offlineReceiptOrderId);
            return Ok(result);
        }

        /// <summary>
        /// 供应商发起提现
        /// </summary>
        /// <returns>提现单id</returns>
        [HttpPost]
        [ProducesResponseType(typeof(long), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> Withdraw()
        {
            CurrentUser currentUser = HttpContext.GetCurrentUser();
            var result = await _offlineReceiptWithdrawalService.Withdraw(currentUser);
            return Ok(result);
        }

        /// <summary>
        /// 根据提现单id获取提现状态信息
        /// </summary>
        /// <param name="offlineReceiptWithdrawalId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(GetWithdrawStatusOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetWithdrawStatus(long offlineReceiptWithdrawalId)
        {
            long supplierId = HttpContext.GetCurrentUser().GetSupplierId();
            var result = await _offlineReceiptWithdrawalService.GetWithdrawStatus(offlineReceiptWithdrawalId, supplierId);
            return Ok(result);
        }

        /// <summary>
        /// 供应商提现记录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(PagingModel<OfflineReceiptOrderWithdrawalOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetWithdrawals(PagingInput input)
        {
            long supplierId = HttpContext.GetCurrentUser().GetSupplierId();
            var result = await _offlineReceiptWithdrawalService.GetWithdrawals(input, supplierId);
            return Ok(result);
        }

        #endregion

        #region vebk

        /// <summary>
        /// 线下收款统计-收款列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(PagingModel<OfflineReceiptOrderSearchOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> SearchOrders(OfflineReceiptOrderSearchInput input)
        {
            var result = await _offlineReceiptOrderFlowService.Search(input);
            return Ok(result);
        }

        /// <summary>
        /// 线下收款统计-退款列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(PagingModel<OfflineReceiptOrderRefundSearchOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> SearchRefundOrders(OfflineReceiptOrderRefundSearchInput input)
        {
            var result = await _offlineReceiptRefundOrderService.Search(input);
            return Ok(result);
        }

        /// <summary>
        /// 租户退款申请
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> RefundByTenant(OfflineReceiptOrderRefundInput input)
        {
            CurrentUser user = HttpContext.GetCurrentUser();
            long? supplierId = null;
            await _offlineReceiptRefundOrderService.Refund(input, user, supplierId);
            return Ok();
        }

        /// <summary>
        /// 线下收款统计-提现列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(PagingModel<OfflineReceiptOrderWithdrawalSearchOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> SearchWithdrawals(OfflineReceiptOrderWithdrawalSearchInput input)
        {
            var result = await _offlineReceiptWithdrawalService.Search(input);
            return Ok(result);
        }

        [HttpPost]
        [ProducesResponseType(typeof(List<OfflineReceiptOrderWithdrawalSearchOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> SearchWithdrawalsByIds(IEnumerable<long> ids)
        {
            var result = await _offlineReceiptWithdrawalService.SearchByIds(ids);
            return Ok(result);
        }

        #endregion

        [HttpPost]
        [ProducesResponseType(typeof(List<GetOfflineReceiptOrderOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> SearchByIds(IEnumerable<long> ids)
        {
            var result = await _offlineReceiptOrderService.SearchByIds(ids);
            return Ok(result);
        }

        #region CapSubscribe

        [NonAction]
        [CapSubscribe(CapTopics.Payment.OfflineReceiptOrderPaySuccess)]
        public async Task PaySuccess(Contracts.Common.Order.Messages.OrderStatusChangeByPaySuccessMessage receive)
        {
            await _offlineReceiptOrderService.PaySuccess(receive);
        }

        [NonAction]
        [CapSubscribe(CapTopics.Payment.OfflineReceiptOrderRefundResult)]
        public async Task RefundResult(OfflineReceiptOrderRefundResultMessage command)
        {
            await _offlineReceiptRefundOrderService.RefundResult(command);
        }

        [NonAction]
        [CapSubscribe(CapTopics.Payment.OfflineReceiptTransferOrderResult)]
        public async Task TransferOrderResult(TransferOrderResultMessage command)
        {
            await _offlineReceiptWithdrawalService.TransferOrderResult(command);
        }

        [NonAction]
        [CapSubscribe(CapTopics.Payment.OfflineReceiptOrderWithdrawalResult)]
        public async Task WithdrawalResult(AccountPayOrderResultMessage command)
        {
            await _offlineReceiptWithdrawalService.WithdrawalResult(command);
        }

        #endregion
    }
}
