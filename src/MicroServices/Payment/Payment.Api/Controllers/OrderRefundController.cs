using AutoMapper;
using Contracts.Common.Payment.DTOs.OrderProfitDivideBack;
using Contracts.Common.Payment.DTOs.OrderRefund;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Payment.Messages;
using DotNetCore.CAP;
using Microsoft.AspNetCore.Mvc;
using Payment.Api.Services.Interfaces;

namespace Payment.Api.Controllers;
[Route("[controller]/[action]")]
[ApiController]
public class OrderRefundController : ControllerBase
{
    private readonly IOrderRefundService _orderRefundService;
    private readonly IOrderProfitDivideService _orderProfitDivideService;
    private readonly IOrderProfitDivideBackService _orderProfitDivideBackService;
    private readonly IMapper _mapper;

    public OrderRefundController(IOrderRefundService orderRefundService,
        IOrderProfitDivideService orderProfitDivideService,
        IOrderProfitDivideBackService orderProfitDivideBackService,
        IMapper mapper)
    {
        _orderRefundService = orderRefundService;
        _orderProfitDivideService = orderProfitDivideService;
        _orderProfitDivideBackService = orderProfitDivideBackService;
        _mapper = mapper;
    }

    /// <summary>
    /// 搜索订单退款信息列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<SearchOrderRefundOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchOrderRefundInput input)
    {
        var result = await _orderRefundService.SearchOrderRefunds(input);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<SearchOrderRefundDetailOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchDetails(SearchOrderRefundInput input)
    {
        List<SearchOrderRefundDetailOutput> result = new();
        var orderRefunds = await _orderRefundService.SearchOrderRefunds(input);

        if (orderRefunds.Count <= 0)
            return Ok(result);
        long[] orderIds = orderRefunds.Select(x => x.OrderId).ToArray();
        long?[] refundIds = orderRefunds.Select(x => (long?)x.RefundOrderId).ToArray();
        var profitDivides = await _orderProfitDivideService.CollectOrderProfitDivides(new Contracts.Common.Payment.DTOs.OrderProfitDivide.CollectOrderProfitDivideInput
        {
            OrderIds = orderIds
        });
        var profitDivideBacks = await _orderProfitDivideBackService.CollectOrderProfitDivideBacks(new CollectOrderProfitDivideBacksInput
        {
            RefundOrderIds = refundIds
        });
        foreach (var refund in orderRefunds)
        {
            var divides = profitDivides.Where(x => x.OrderId == refund.OrderId);
            var backs = profitDivideBacks.Where(x => x.RefundOrderId == refund.RefundOrderId);
            var divideBacks = backs
                              .Join(divides, b => b.DivideDetailId, d => d.ProfitDivideDetailId,
                              (b, d) => new
                              {
                                  d.DivideType,
                                  b.Amount
                              });

            var paymentFee = divideBacks.Where(x => x.DivideType == ProfitDivideType.PaymentFee).Sum(x => x.Amount);
            var platformCommission = divideBacks.Where(x => x.DivideType == ProfitDivideType.PlatformCommission).Sum(x => x.Amount);

            SearchOrderRefundDetailOutput output = _mapper.Map<SearchOrderRefundDetailOutput>(refund);
            output.PaymentFee = paymentFee;
            output.PlatformCommission = platformCommission;
            result.Add(output);
        }

        return Ok(result);
    }

    #region CapSubscribe

    [HttpPost]
    [CapSubscribe(CapTopics.Payment.OrderRefund)]
    public async Task<OrderRefundResultMessage> Refund(OrderRefundMessage receive)
    {
        var orderRefund = await _orderRefundService.OrderRefundGenerated(receive);
        var command = await _orderRefundService.Refund(orderRefund);
        return command;
    }

    /// <summary>
    /// 订单支付作废退款 (如订单关闭、重复支付等场景)
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    [HttpPost]
    [CapSubscribe(CapTopics.Payment.OrderPaymentUselessRefund)]
    public async Task OrderPaymentUselessRefund(OrderPaymentUselessRefundMessage receive)
    {
        await _orderRefundService.OrderPaymentUselessRefund(receive);
    }

    #endregion 
}
