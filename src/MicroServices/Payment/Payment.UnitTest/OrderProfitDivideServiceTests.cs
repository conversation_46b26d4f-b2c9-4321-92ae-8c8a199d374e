using Cit.Payment.Yeepay.Service;
using Cit.Payment.Yeepay.Service.Request.Divide;
using Cit.Payment.Yeepay.Service.Response;
using Cit.Payment.Yeepay.Service.Response.Divide;
using Common.ServicesHttpClient;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Payment.Messages;
using DotNetCore.CAP;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Moq;
using Newtonsoft.Json;
using Payment.Api.Infrastructure;
using Payment.Api.Model;
using Payment.Api.Services;
using Payment.Api.Services.Interfaces;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Payment.UnitTest
{
    public class OrderProfitDivideServiceTests : TestBase<CustomDbContext>
    {
        private static OrderProfitDivideService GetOrderProfitDivideService(CustomDbContext dbContext,
            IYeeConfigService yeeConfigService,
            IYeeMerConfigService yeeMerConfigService,
            IDivideService divideService,
            IOrderPaymentService orderPaymentService,
            IOptions<ServicesAddress> servicesAddress,
            ICapPublisher capPublisher,
            IHttpClientFactory httpClientFactory)
        {
            return new OrderProfitDivideService(dbContext,
                yeeConfigService, yeeMerConfigService, divideService, orderPaymentService,
                servicesAddress, capPublisher, httpClientFactory);
        }

        [Fact(DisplayName = "申请分账_成功")]
        public async Task OrderProfitDivideApply_Success()
        {
            //arrange
            var tenantId = 1;
            var dbContext = GetNewDbContext();
            var yeeConfigService = new Mock<IYeeConfigService>();
            yeeConfigService.Setup(c => c.GetYeeConfig())
                .ReturnsAsync(new YeeConfig { ParentMetchantNo = "10086206042" });
            var yeeMerConfigService = new Mock<IYeeMerConfigService>();
            yeeMerConfigService.Setup(c => c.GetYeeMerConfig(It.IsAny<long>()))
                .ReturnsAsync(new YeeMerConfig { });

            var divideService = new Mock<IDivideService>();
            divideService.Setup(d => d.Apply(It.IsAny<ApplyRequest>()))
                .ReturnsAsync(new YopResponse<ApplyResponse>
                {
                    Result = new ApplyResponse
                    {
                        Code = "OPR00000",
                        Message = "成功",
                        OrderId = "1",
                        DivideRequestId = "1",
                        Status = "SUCCESS",
                        DivideDetail = "[{\"amount\":0.01,\"divideDetailDesc\":\"支付费\",\"divideDetailNo\":\"1010202112170000003000879948\",\"ledgerNo\":\"10086206042\",\"ledgerType\":\"MERCHANT2MERCHANT\"}]"
                    }
                });
            var orderPaymentId = 1;
            var orderPaymentService = new Mock<IOrderPaymentService>();
            var merchantFee = 0.02m;
            orderPaymentService.Setup(o => o.GetPaidOrderPayment(
                It.IsAny<long>(), It.IsAny<long>(), It.IsAny<PayType?>(), It.IsAny<string>(),It.IsAny<long?>()))
                .ReturnsAsync(new OrderPayment
                {
                    Id = orderPaymentId,
                    PayType = PayType.YeePay,
                    PayStatus = PayStatus.Paid,
                    FundProcessType = FundProcessType.DELAY_SETTLE,
                    OrderAmount = 1m,
                    OrderId = 1,
                    UniqueOrderNo = "1",
                    MerchantFee = merchantFee
                });
            var servicesAddress = new Mock<IOptions<ServicesAddress>>();
            servicesAddress.Setup(s => s.Value).Returns(new ServicesAddress { Order = "http://localhost", Tenant = "http://localhost" });

            StringContent stringContent = new(JsonConvert.SerializeObject(new
            {
                paymentFeeRate = 0.1,
                platformCommissionRate = 0.1
            }));
            var httpClientFactory = GetHttpClientFactoryMock(new HttpResponseMessage
            {
                Content = stringContent
            });
            //act
            var service = GetOrderProfitDivideService(dbContext,
                yeeConfigService.Object,
                yeeMerConfigService.Object,
                divideService.Object,
                orderPaymentService.Object,
                servicesAddress.Object, GetCapPublisher(), httpClientFactory);
            var command = new OrderProfitDivideApplyMessage
            {
                UniqueOrderNo = "1",
                OrderAmount = 1,
                OrderId = 1,
                TenantId = tenantId,
                OrderPaymentType = OrderPaymentType.OrderPay,
                MerchantFee = merchantFee
            };
            var divideApplyDto = await service.CreateProfitDivide(command);
            await service.OrderProfitDivideApply(divideApplyDto);
            await dbContext.SaveChangesAsync();
            //assert
            Assert.True(dbContext.ProfitDivides.IgnoreQueryFilters().FirstOrDefault().DivideStatus == DivideStatus.SUCCESS);
            var paymentFee = dbContext.ProfitDivideDetails
                .IgnoreQueryFilters()
                .Where(x => x.DivideType == ProfitDivideType.PaymentFee)
                .Select(x => x.Amount)
                .FirstOrDefault();
            Assert.True(paymentFee + command.MerchantFee == 0.1m);
            var platformCommission = dbContext.ProfitDivideDetails
                .IgnoreQueryFilters()
                .Where(x => x.DivideType == ProfitDivideType.PlatformCommission)
                .Select(x => x.Amount)
                .FirstOrDefault();
            Assert.True(platformCommission == 0.1m);
            var merchantFeeOrderPaymentDetail = dbContext.OrderPaymentDetails
                .IgnoreQueryFilters()
                .Where(x => x.OrderPaymentId == orderPaymentId && x.PaymentScene == PaymentScene.PaymentPlatformMerchantFee)
                .FirstOrDefault();
            Assert.True(merchantFeeOrderPaymentDetail.Amount == -command.MerchantFee);
            var hasOrderPaymentDivideDetail = dbContext.OrderPaymentDetails
                .IgnoreQueryFilters()
                .Where(x => x.OrderPaymentId == orderPaymentId && x.PaymentScene == PaymentScene.OrderDivide)
                .Any();
            Assert.True(hasOrderPaymentDivideDetail);
        }

        [Fact(DisplayName = "查询分账_成功")]
        public async Task OrderProfitDivideQuery_Success()
        {
            //arrange
            var tenantId = 1;
            var dbContext = GetNewDbContext();
            await dbContext.AddAsync(new ProfitDivide
            {
                OrderId = 1,
                Id = 1,
                Amount = 0.01m,
                DivideWay = ProfitDivideWay.FLAT_DIVIDE,
                DivideStatus = DivideStatus.PROCESSING,
                UniqueOrderNo = "1"
            });
            await dbContext.SetTenantId(tenantId).SaveChangesAsync();

            var yeeConfigService = new Mock<IYeeConfigService>();
            yeeConfigService.Setup(c => c.GetYeeConfig())
                .ReturnsAsync(new YeeConfig { ParentMetchantNo = "10086206042" });
            var yeeMerConfigService = new Mock<IYeeMerConfigService>();
            yeeMerConfigService.Setup(c => c.GetYeeMerConfig(It.IsAny<long>()))
                .ReturnsAsync(new YeeMerConfig { });

            var divideService = new Mock<IDivideService>();
            divideService.Setup(d => d.Query(It.IsAny<QueryRequest>()))
                .ReturnsAsync(new YopResponse<QueryResponse>
                {
                    Result = new QueryResponse
                    {
                        Status = "SUCCESS",
                        DivideRequestId = "1",
                        OrderId = "1"
                    }
                });

            var orderPaymentService = new Mock<IOrderPaymentService>();
            orderPaymentService.Setup(o => o.GetPaidOrderPayment(
                It.IsAny<long>(), It.IsAny<long>(), It.IsAny<PayType?>(), It.IsAny<string>(),It.IsAny<long?>()))
                .ReturnsAsync(new OrderPayment
                {
                    PayType = PayType.YeePay,
                    PayStatus = PayStatus.Paid,
                    FundProcessType = FundProcessType.DELAY_SETTLE,
                    OrderAmount = 1m,
                    OrderId = 1,
                    UniqueOrderNo = "1",
                });
            var servicesAddress = new Mock<IOptions<ServicesAddress>>();
            servicesAddress.Setup(s => s.Value).Returns(new ServicesAddress { Order = "http://localhost", Tenant = "http://localhost" });

            StringContent stringContent = new(JsonConvert.SerializeObject(new
            {
                paymentFeeRate = 0.01,
                platformCommissionRate = 0
            }));
            var httpClientFactory = GetHttpClientFactoryMock(new HttpResponseMessage
            {
                Content = stringContent
            });
            //act
            var service = GetOrderProfitDivideService(dbContext,
                yeeConfigService.Object,
                yeeMerConfigService.Object,
                divideService.Object,
                orderPaymentService.Object,
                servicesAddress.Object, GetCapPublisher(), httpClientFactory);
            var command = new OrderProfitDivideQueryMessage
            {
                DivideId = 1,
                TenantId = tenantId
            };
            await service.OrderProfitDivideQuery(command);
            //assert
            Assert.True(dbContext.ProfitDivides.IgnoreQueryFilters().FirstOrDefault().DivideStatus == DivideStatus.SUCCESS);
        }

        [Fact(DisplayName = "完结分账_成功")]
        public async Task OrderProfitDivideComplete_Success()
        {
            //arrange
            var tenantId = 1;
            var dbContext = GetNewDbContext();
            await dbContext.AddAsync(new ProfitDivide
            {
                OrderId = 1,
                Id = 1,
                Amount = 0.01m,
                DivideWay = ProfitDivideWay.FLAT_DIVIDE,
                DivideStatus = DivideStatus.PROCESSING,
                UniqueOrderNo = "1"
            });
            await dbContext.SetTenantId(tenantId).SaveChangesAsync();

            var yeeConfigService = new Mock<IYeeConfigService>();
            yeeConfigService.Setup(c => c.GetYeeConfig())
                .ReturnsAsync(new YeeConfig { ParentMetchantNo = "10086206042" });
            var yeeMerConfigService = new Mock<IYeeMerConfigService>();
            yeeMerConfigService.Setup(c => c.GetYeeMerConfig(It.IsAny<long>()))
                .ReturnsAsync(new YeeMerConfig { });

            var divideService = new Mock<IDivideService>();
            divideService.Setup(d => d.Complete(It.IsAny<CompleteRequest>()))
                .ReturnsAsync(new YopResponse<CompleteResponse>
                {
                    Result = new CompleteResponse
                    {
                        Code= "OPR00000",
                        Amount = 0.9m,
                        DivideStatus = "SUCCESS",
                    }
                });

            var orderPaymentService = new Mock<IOrderPaymentService>();
            orderPaymentService.Setup(o => o.GetPaidOrderPayment(
                It.IsAny<long>(), It.IsAny<long>(), It.IsAny<PayType?>(), It.IsAny<string>(),It.IsAny<long?>()))
                .ReturnsAsync(new OrderPayment
                {
                    PayType = PayType.YeePay,
                    PayStatus = PayStatus.Paid,
                    FundProcessType = FundProcessType.DELAY_SETTLE,
                    OrderAmount = 1m,
                    OrderId = 1,
                    UniqueOrderNo = "1",
                });
            var servicesAddress = new Mock<IOptions<ServicesAddress>>();
            servicesAddress.Setup(s => s.Value).Returns(new ServicesAddress { Order = "http://localhost", Tenant = "http://localhost" });

            StringContent stringContent = new(JsonConvert.SerializeObject(new
            {
                paymentFeeRate = 0.01,
                platformCommissionRate = 0
            }));
            var httpClientFactory = GetHttpClientFactoryMock(new HttpResponseMessage
            {
                Content = stringContent
            });
            //act
            var service = GetOrderProfitDivideService(dbContext,
                yeeConfigService.Object,
                yeeMerConfigService.Object,
                divideService.Object,
                orderPaymentService.Object,
                servicesAddress.Object, GetCapPublisher(), httpClientFactory);
            var receive = new OrderProfitDivideCompleteMessage
            {
                OrderId = 1,
                UniqueOrderNo = "1",
                TenantId = tenantId
            };
            await service.OrderProfitDivideComplete(receive);
            //assert
            Assert.True(dbContext.ProfitDivides.IgnoreQueryFilters().Any(d => d.DivideWay == ProfitDivideWay.END_DIVIDE));
            Assert.True(dbContext.ProfitDivideDetails.IgnoreQueryFilters().Any(d => d.DivideType == ProfitDivideType.None));
        }
    }
}
