using AutoMapper;
using Common.ServicesHttpClient;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Payment.Messages;
using DotNetCore.CAP;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Moq;
using Newtonsoft.Json.Linq;
using Payment.Api.Infrastructure;
using Payment.Api.Model;
using Payment.Api.Services;
using Payment.Api.Services.Interfaces;
using System;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Payment.UnitTest
{
    public class OfflineReceiptOrderServiceTests : TestBase<CustomDbContext>
    {
        private const long _tenantId = 1;
        private static OfflineReceiptOrderService GetService(CustomDbContext dbContext,
           IOrderPayService orderPayService,
           IHttpClientFactory httpClientFactory,
           IOptions<ServicesAddress> servicesAddress,
           ICapPublisher capPublisher,
           IMapper mapper)
        {
            return new OfflineReceiptOrderService(dbContext,
                orderPayService,
                httpClientFactory,
                servicesAddress,
                capPublisher,
                mapper);
        }

        [Fact(DisplayName = "支付成功处理_成功")]
        public async Task PaySuccess_Success()
        {
            //arrange
            OfflineReceiptOrder offlineReceiptOrder = new()
            {
                OfflineReceiptId = 1,
                OrderAmount = 1,
                Title = "",
                Status = OfflineReceiptOrderStatus.WaitingForPay,
                CreateTime = DateTime.Now,
                PayerInfo = new Api.Model.OfflineReceiptOrderPayerInfo
                {
                    NickName = "zhangsan"
                }
            };
            var dbContext = GetNewDbContext();
            await dbContext.AddAsync(offlineReceiptOrder);
            await dbContext.SetTenantId(_tenantId).SaveChangesAsync();
            //act
            var httpClientFactory = GetHttpClientFactoryMock(new HttpResponseMessage
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                Content = new StringContent((new JObject
                {
                    ["paymentFeeRate"] = "0.3",
                    ["platformCommissionRate"] = "0.1"
                }).ToString())
            });
            var servicesAddress = new Mock<IOptions<ServicesAddress>>();
            servicesAddress.Setup(x => x.Value)
                .Returns(new ServicesAddress { Tenant = "http://localhost" });
            var service = GetService(dbContext, null, httpClientFactory,
                servicesAddress.Object,
                GetCapPublisher(), null);
            var command = new Contracts.Common.Order.Messages.OrderStatusChangeByPaySuccessMessage
            {
                OrderId = offlineReceiptOrder.Id,
                PaymentChannel = "WECHAT",
                PaymentExternalNo = "132",
                PaymentMode = "WECHAT_OFFIACCOUNT",
                PaymentType = PayType.YeePay
            };
            await service.PaySuccess(command);
            await dbContext.SaveChangesAsync();
            //assert
            Assert.True(offlineReceiptOrder.Status == OfflineReceiptOrderStatus.Received);
            var check = dbContext.OfflineReceiptOrderFlows
                .IgnoreQueryFilters()
                .Any(x => x.OfflineReceiptOrderId == offlineReceiptOrder.Id
                    && x.FlowType == OfflineReceiptOrderFlowType.Receive);
            Assert.True(check);
        }
    }
}
