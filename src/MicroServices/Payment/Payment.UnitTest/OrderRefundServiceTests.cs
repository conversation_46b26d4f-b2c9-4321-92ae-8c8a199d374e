using AutoMapper;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.DTOs.OrderRefund;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Payment.Messages;
using DotNetCore.CAP;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Moq;
using Payment.Api.Infrastructure;
using Payment.Api.Model;
using Payment.Api.Services;
using Payment.Api.Services.Interfaces;
using System;
using System.Linq;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Payment.UnitTest
{
    public class OrderRefundServiceTests : TestBase<CustomDbContext>
    {
        private OrderRefundService GetOrderRefundService(CustomDbContext dbContext,
          IOrderPaymentService orderPaymentService,
          Func<PayType, IOrderRefund> orderRefundFunc,
          IMapper mapper,
          ICapPublisher capPublisher,
          IMediator mediator)
        {
            return new OrderRefundService(dbContext, orderPaymentService,
                orderRefundFunc, mapper, capPublisher,mediator);
        }

        [Fact(DisplayName = "退款_成功")]
        public async Task Refund_Success()
        {
            var tenantId = 1;
            //arrange
            var dbContext = GetNewDbContext();
            var mockOrderPaymentService = new Mock<IOrderPaymentService>();
            mockOrderPaymentService.Setup(s => s.GetPaidOrderPayment(It.IsAny<long>(), It.IsAny<long>(), PayType.YeePay, "",It.IsAny<long?>())).ReturnsAsync(new OrderPayment
            {
                FundProcessType = FundProcessType.DELAY_SETTLE,
                OrderId = 1,
                OrderAmount = 0.1m,
                OrderType = OrderType.Ticket,
                PayType = PayType.YeePay,
                PayChannel = PayChannel.WECHAT,
                PayWay = PayWay.JS_PAY,
                PayStatus = PayStatus.Paid
            });
            var mockOrderRefund = new Mock<IOrderRefund>();
            mockOrderRefund.Setup(s => s.Refund(It.IsAny<OrderRefundDto>()))
                .ReturnsAsync(new OrderRefundOutDto { RefundStatus = RefundStatus.SUCCESS });
            MapperConfiguration mapperConfiguration = new(
                a => a.CreateMap<OrderRefund,OrderRefundDetailDto>());
            var mapper = mapperConfiguration.CreateMapper();
            var mediator = new Mock<MediatR.IMediator>();
            mediator.Setup(s => s.Publish(It.IsAny<MediatR.INotification>(), default))
                .Returns(Task.CompletedTask);
            //act
            var service = GetOrderRefundService(dbContext,
                mockOrderPaymentService.Object, p => mockOrderRefund.Object,
                mapper, GetCapPublisher(), mediator.Object);
            var command = await service.Refund(new OrderRefundMessage
            {
                TenantId = tenantId,
                Description = "申请退款",
                OrderId = 1,
                PayType = PayType.YeePay,
                RefundAmount = 0.1m,
                RefundOrderId = 1
            });
            await dbContext.SaveChangesAsync();
            //assert
            Assert.True(dbContext.OrderRefunds.IgnoreQueryFilters().Any(r => r.RefundOrderId == 1));
            Assert.True(command.RefundStatus == RefundStatus.SUCCESS);
        }

        [Fact(DisplayName = "退款结果_成功")]
        public async Task RefundResult_Success()
        {
            var tenantId = 1;
            //arrange
            var dbContext = GetNewDbContext();
            await dbContext.AddAsync(new OrderRefund
            {
                RefundOrderId = 1,
                RefundStatus = RefundStatus.PROCESSING,
                RefundAmount = 0.1m,
                Description = "取消订单",
                PayType = PayType.YeePay,
                OrderId = 1
            });
            await dbContext.SetTenantId(tenantId).SaveChangesAsync();
            var mockOrderPaymentService = new Mock<IOrderPaymentService>();
            mockOrderPaymentService.Setup(s => s.GetPaidOrderPayment(It.IsAny<long>(), It.IsAny<long>(), PayType.YeePay, "",It.IsAny<long?>())).ReturnsAsync(new OrderPayment
            {
                FundProcessType = FundProcessType.DELAY_SETTLE,
                OrderId = 1,
                OrderAmount = 0.1m,
                OrderType = OrderType.Ticket,
                PayType = PayType.YeePay,
                PayChannel = PayChannel.WECHAT,
                PayWay = PayWay.JS_PAY,
                PayStatus = PayStatus.Paid
            });
            var mockOrderRefund = new Mock<IOrderRefund>();
            mockOrderRefund.Setup(s => s.Refund(It.IsAny<OrderRefundDto>()))
                .ReturnsAsync(new OrderRefundOutDto { RefundStatus = RefundStatus.SUCCESS });
            MapperConfiguration mapperConfiguration = new(
                a => a.AddMaps(typeof(OrderRefundResultMessage).Assembly));
            var mapper = mapperConfiguration.CreateMapper();
            var mediator = new Mock<MediatR.IMediator>();
            mediator.Setup(s => s.Publish(It.IsAny<MediatR.INotification>(), default))
                .Returns(Task.CompletedTask);
            //act
            var service = GetOrderRefundService(dbContext,
                mockOrderPaymentService.Object, p => mockOrderRefund.Object,
                mapper, GetCapPublisher(),mediator.Object);
            await service.RefundResult(new OrderRefundResultInput
            {
                RefundOrderId = "1",
                FailReason = "",
                RefundStatus = RefundStatus.SUCCESS,
                UniqueRefundNo = ""
            });
            await dbContext.SaveChangesAsync();
            //assert
            Assert.True(dbContext.OrderRefunds.IgnoreQueryFilters().Any(r => r.RefundOrderId == 1 && r.RefundStatus == RefundStatus.SUCCESS));
        }
    }
}
