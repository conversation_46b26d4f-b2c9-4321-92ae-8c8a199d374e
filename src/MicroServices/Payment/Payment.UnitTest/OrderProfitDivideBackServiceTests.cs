using Cit.Payment.Yeepay.Service;
using Cit.Payment.Yeepay.Service.Request.Divide;
using Cit.Payment.Yeepay.Service.Response;
using Cit.Payment.Yeepay.Service.Response.Divide;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Payment.Messages;
using DotNetCore.CAP;
using Microsoft.EntityFrameworkCore;
using Moq;
using Payment.Api.Infrastructure;
using Payment.Api.Model;
using Payment.Api.Services;
using Payment.Api.Services.Interfaces;
using System.Linq;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Payment.UnitTest
{
    public class OrderProfitDivideBackServiceTests : TestBase<CustomDbContext>
    {
        private static OrderProfitDivideBackService GetProfitDivideBackService(CustomDbContext dbContext,
        IYeeMerConfigService yeeMerConfigService,
        IOrderPaymentService orderPaymentService,
        IDivideService divideService,
        ICapPublisher capPublisher)
        {
            return new OrderProfitDivideBackService(dbContext,
                yeeMerConfigService,
                orderPaymentService,
                divideService,
                capPublisher);
        }

        [Fact(DisplayName = "申请分账回退_成功")]
        public async Task OrderProfitDivideBack_Success()
        {
            //arrange
            var tenantId = 1;
            var dbContext = GetNewDbContext();
            var profitDivide = new ProfitDivide
            {
                DivideStatus = DivideStatus.SUCCESS,
                UniqueOrderNo = "1",
                DivideWay = ProfitDivideWay.FLAT_DIVIDE,
                Amount = 1m,
                OrderId = 1
            };
            await dbContext.AddAsync(profitDivide);
            await dbContext.AddAsync(new ProfitDivideDetail
            {
                Amount = 1,
                DivideDetailDesc = "支付费",
                DivideDetailNo = "1",
                DivideId = profitDivide.Id,
                DivideType = ProfitDivideType.PaymentFee,
                LedgerNo = "1",
            });
            await dbContext.SetTenantId(tenantId).SaveChangesAsync();

            var yeeMerConfigService = new Mock<IYeeMerConfigService>();
            yeeMerConfigService.Setup(c => c.GetYeeMerConfig(It.IsAny<long>()))
                .ReturnsAsync(new YeeMerConfig { });

            var divideService = new Mock<IDivideService>();
            divideService.Setup(d => d.Back(It.IsAny<BackRequest>()))
                .ReturnsAsync(new YopResponse<BackResponse>
                {
                    Result = new BackResponse
                    {
                        Code = "OPR00000",
                        Message = "成功",
                        BizSystemNo = "DS",
                        OrderId = "1",
                        UniqueOrderNo = "1",
                        DivideRequestId = "1",
                        DivideBackRequestId = "1",
                        UniqueDivideBackNo = "1",
                        DivideBackDetail = "[{\"amount\":0.01,\"divideDetailNo\":\"1010202112170000003000879948\",\"divideBackReason\":\"测试分账回退\"}]",
                        Status = "SUCCESS"
                    }
                });

            var orderPaymentService = new Mock<IOrderPaymentService>();
            orderPaymentService.Setup(o => o.GetPaidOrderPayment(
                It.IsAny<long>(), It.IsAny<long>(), It.IsAny<PayType?>(), It.IsAny<string>(),It.IsAny<long?>()))
                .ReturnsAsync(new OrderPayment
                {
                    PayType = PayType.YeePay,
                    PayStatus = PayStatus.Paid,
                    FundProcessType = FundProcessType.DELAY_SETTLE,
                    OrderAmount = 1m,
                    OrderId = 1,
                    UniqueOrderNo = "1",
                });

            //act
            var service = GetProfitDivideBackService(dbContext,
                yeeMerConfigService.Object,
                orderPaymentService.Object,
                divideService.Object,
                GetCapPublisher());
            var command = new OrderProfitDivideBackMessage
            {
                DivideBackReason = "取消订单回退",
                OrderId = 1,
                Percentage = 0.01m,
                TenantId = tenantId
            };
            await service.OrderProfitDivideBack(command);
            await dbContext.SaveChangesAsync();
            //assert
            Assert.True(dbContext.ProfitDivideBacks.IgnoreQueryFilters().Any(b => b.DivideBackStatus == DivideBackStatus.PROCESSING));
            Assert.True(dbContext.ProfitDivideBackDetails.IgnoreQueryFilters().Any(d => d.Amount == command.Percentage * 1));
        }

        [Fact(DisplayName = "易宝分账回退_成功")]
        public async Task YeeDivideBack_Success()
        {
            //arrange
            var tenantId = 1;
            var dbContext = GetNewDbContext();
            ProfitDivideBack profitDivideBack = new()
            {
                OrderId = 1,
                DivideBackStatus = DivideBackStatus.PROCESSING,
                DivideId = 1,
                UniqueDivideBackNo = "1",
                UniqueOrderNo = "1"
            };
            await dbContext.AddAsync(profitDivideBack);
            await dbContext.AddAsync(new ProfitDivideBackDetail
            {
                Amount = 0.01m,
                DivideBackReason = "测试分账回退",
                DivideDetailNo = "",
                DivideBackId = profitDivideBack.Id
            });
            await dbContext.SetTenantId(tenantId).SaveChangesAsync();

            var yeeMerConfigService = new Mock<IYeeMerConfigService>();
            yeeMerConfigService.Setup(c => c.GetYeeMerConfig(It.IsAny<long>()))
                .ReturnsAsync(new YeeMerConfig { });

            var divideService = new Mock<IDivideService>();
            divideService.Setup(d => d.Back(It.IsAny<BackRequest>()))
                .ReturnsAsync(new YopResponse<BackResponse>
                {
                    Result = new BackResponse
                    {
                        Code = "OPR00000",
                        Message = "成功",
                        BizSystemNo = "DS",
                        OrderId = "1",
                        UniqueOrderNo = "1",
                        DivideRequestId = "1",
                        DivideBackRequestId = "1",
                        UniqueDivideBackNo = "1",
                        DivideBackDetail = "[{\"amount\":0.01,\"divideDetailNo\":\"1\",\"divideBackReason\":\"测试分账回退\"}]",
                        Status = "SUCCESS"
                    }
                });

            var orderPaymentService = new Mock<IOrderPaymentService>();
            orderPaymentService.Setup(o => o.GetPaidOrderPayment(
                It.IsAny<long>(), It.IsAny<long>(), It.IsAny<PayType?>(), It.IsAny<string>(),It.IsAny<long?>()))
                .ReturnsAsync(new OrderPayment
                {
                    PayType = PayType.YeePay,
                    PayStatus = PayStatus.Paid,
                    FundProcessType = FundProcessType.DELAY_SETTLE,
                    OrderAmount = 1m,
                    OrderId = 1,
                    UniqueOrderNo = "1",
                });

            //act
            var service = GetProfitDivideBackService(dbContext,
                yeeMerConfigService.Object,
                orderPaymentService.Object,
                divideService.Object,
                GetCapPublisher());
            var receive = new DivideBackMessage
            {
                DivideBackId = profitDivideBack.Id,
                TenantId = tenantId
            };
            await service.YeeDivideBack(receive);
            await dbContext.SaveChangesAsync();
            //assert
            Assert.True(dbContext.ProfitDivideBacks.IgnoreQueryFilters().Any(b => b.DivideBackStatus == DivideBackStatus.SUCCESS));
        }

        [Fact(DisplayName = "分账回退查询_成功")]
        public async Task DivideBackQuery_Success()
        {
            //arrange
            var tenantId = 1;
            var dbContext = GetNewDbContext();
            ProfitDivideBack profitDivideBack = new()
            {
                OrderId = 1,
                DivideBackStatus = DivideBackStatus.PROCESSING,
                DivideId = 1,
                UniqueDivideBackNo = "1",
                UniqueOrderNo = "1"
            };
            await dbContext.AddAsync(profitDivideBack);
            await dbContext.AddAsync(new ProfitDivideBackDetail
            {
                Amount = 0.01m,
                DivideBackReason = "测试分账回退",
                DivideDetailNo = "",
                DivideBackId = profitDivideBack.Id
            });
            await dbContext.SetTenantId(tenantId).SaveChangesAsync();

            var yeeMerConfigService = new Mock<IYeeMerConfigService>();
            yeeMerConfigService.Setup(c => c.GetYeeMerConfig(It.IsAny<long>()))
                .ReturnsAsync(new YeeMerConfig { });

            var divideService = new Mock<IDivideService>();
            divideService.Setup(d => d.BackQuery(It.IsAny<BackQueryRequest>()))
                .ReturnsAsync(new YopResponse<BackQueryResponse>
                {
                    Result = new BackQueryResponse
                    {
                        Code = "OPR00000",
                        Message = "成功",
                        BizSystemNo = "DS",
                        OrderId = "1",
                        UniqueOrderNo = "1",
                        DivideRequestId = "1",
                        DivideBackRequestId = "1",
                        UniqueDivideBackNo = "1",
                        DivideBackDetail = "[{\"amount\":0.01,\"divideDetailNo\":\"1\",\"divideBackReason\":\"测试分账回退\"}]",
                        Status = "SUCCESS"
                    }
                });

            var orderPaymentService = new Mock<IOrderPaymentService>();
            orderPaymentService.Setup(o => o.GetPaidOrderPayment(
                It.IsAny<long>(), It.IsAny<long>(), It.IsAny<PayType?>(), It.IsAny<string>(),It.IsAny<long?>()))
                .ReturnsAsync(new OrderPayment
                {
                    PayType = PayType.YeePay,
                    PayStatus = PayStatus.Paid,
                    FundProcessType = FundProcessType.DELAY_SETTLE,
                    OrderAmount = 1m,
                    OrderId = 1,
                    UniqueOrderNo = "1",
                });

            //act
            var service = GetProfitDivideBackService(dbContext,
                yeeMerConfigService.Object,
                orderPaymentService.Object,
                divideService.Object,
                GetCapPublisher());
            var receive = new OrderProfitDivideBackQueryMessage
            {
                DivideBackId = profitDivideBack.Id,
                TenantId = tenantId
            };
            await service.OrderProfitDivideBackQuery(receive);
            await dbContext.SaveChangesAsync();
            //assert
            Assert.True(dbContext.ProfitDivideBacks.IgnoreQueryFilters().Any(b => b.DivideBackStatus == DivideBackStatus.SUCCESS));
        }
    }
}
