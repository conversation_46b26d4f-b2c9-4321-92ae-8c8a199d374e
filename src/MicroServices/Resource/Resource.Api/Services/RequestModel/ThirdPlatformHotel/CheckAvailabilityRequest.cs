namespace Resource.Api.Services.RequestModel.ThirdPlatformHotel;

public class CheckAvailabilityRequest
{
    public long ResourceHotelId { get; set; }
    public string HotelId { get; set; }
    public string RoomId { get; set; }
    public string PricestrategyId { get; set; }
    public DateTime CheckIn { get; set; }
    public DateTime CheckOut { get; set; }
    public int RoomNum { get; set; }
    public int AdultNum { get; set; }

    /// <summary>
    /// 儿童年龄集合
    /// </summary>
    public IEnumerable<int>? ChildrenAges { get; set; }

    public IEnumerable<OrderCalendarPriceDto>? CalendarPrices { get; set; }

    /// <summary>
    /// 采购总价
    /// </summary>
    public decimal? TotalPrice { get; set; }

    /// <summary>
    /// 入住客人国籍，入住客人国籍，国家或地区两位字母代码，例如：CN、TH 汇智酒店V3接口需要传入
    /// </summary>
    public string? Nationality { get; set; }

}

public class OrderCalendarPriceDto
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 成本价/采购价
    /// </summary>
    public decimal CostPrice { get; set; }
}