using Contracts.Common.Hotel.Enums;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Resource.Enums;

namespace Resource.Api.Services.RequestModel.ThirdPlatformHotel;

public class ThirdHotelPriceResponse
{
    public string HotelId { get; set; }

    public List<ThirdHotelRoomDto> Rooms { get; set; } = new();
}

public class ThirdHotelRoomDto
{
    public string RoomId { get; set; }

    public List<ThirdPricestrategyDto> PriceStrategies { get; set; } = new();
}

public class ThirdPricestrategyDto
{
    public string PricestrategyId { get; set; }

    #region 价格策略信息

    /// <summary>
    /// 策略名称
    /// </summary>
    public string Name { get; set; }

    public string? EnName { get; set; }

    /// <summary>
    /// 价格策略类型
    /// </summary>
    public PriceStrategyType PriceStrategyType { get; set; } = PriceStrategyType.StandardRoom;

    ///// <summary>
    ///// 供应商Id
    ///// </summary>
    //public long SupplierId { get; set; }

    /// <summary>
    /// 提前几小时预订
    /// </summary>
    public int BookingHoursInAdvance { get; set; }

    /// <summary>
    /// 餐食类型
    /// </summary>
    public BoardCodeType BoardCodeType { get; set; }

    /// <summary>
    /// 餐食数量
    /// </summary>
    public int BoardCount { get; set; }

    /// <summary>
    /// 是否自动确认
    /// </summary>
    public bool IsAutoConfirm { get; set; }

    /// <summary>
    /// 非立即确认报价预计确认时长，单位：分钟
    /// </summary>
    public int? ConfirmByMins { get; set; }

    /// <summary>
    /// 是否上架
    /// </summary>
    public bool Enabled { get; set; }

    #region 连住策略单独部分

    /// <summary>
    /// 连住晚数及以上
    /// </summary>
    public int NumberOfNights { get; set; }

    /// <summary>
    /// 限制最大连住天数
    /// </summary>
    public int? LimitNumberOfNights { get; set; }

    #endregion

    #region 团房策略单独部分

    /// <summary>
    /// 最小预订间数
    /// </summary>
    public int NumberOfRooms { get; set; }

    #endregion

    /// <summary>
    /// 采购价币种
    /// </summary>
    public string CostCurrencyCode { get; set; } = Currency.CNY.ToString();

    /// <summary>
    /// 该价格仅适用于nationalityCodes的客人 空数组表示不限制
    /// </summary>
    public IEnumerable<string>? NationalityCodes { get; set; }

    /// <summary>
    /// 该价格不适用于nationalityCodes的客人 空数组表示不限制
    /// </summary>
    public IEnumerable<string>? NonNationalityCodes { get; set; }

    /// <summary>
    /// 国籍限制 
    /// </summary>
    [Obsolete]
    public IEnumerable<string> NationalNames { get; set; } = Enumerable.Empty<string>();

    /// <summary>
    /// 最大入住人数
    /// </summary>
    public int MaxOccupancy { get; set; }

    #endregion

    public List<ThirdPriceStrategyCancelRuleDto> CancelRules { get; set; }

    public IEnumerable<ThirdPricestrategyCalendarPriceDto> CalendarPrices { get; set; } = Enumerable.Empty<ThirdPricestrategyCalendarPriceDto>();

    /// <summary>
    /// 总价
    /// </summary>
    public decimal TotalPrice { get; set; }

    /// <summary>
    /// 是否直采
    /// </summary>
    public bool IsDirect { get; set; }

    /// <summary>
    /// 敏感度类型 
    /// </summary>
    public SellHotelTag? Tag { get; set; }

    /// <summary>
    /// 预订礼遇
    /// </summary>
    public string? BookingBenefits { get; set; }

    /// <summary>
    /// 供应商渠道类型 1-直采 2-EBK 3-CM渠道 4-直连
    /// </summary>
    public ApiHotelChannelType ChannelType { get; set; }

    /// <summary>
    /// 报价计划类型 0-日历房 1-商旅 2-高德 3-团房 4-直销
    /// </summary>
    public ApiHotelSaleType? SaleType { get; set; }
}

public class ThirdPriceStrategyCancelRuleDto
{
    /// <summary>
    /// 取消策略类型
    /// </summary>
    public CancelRulesType CancelRulesType { get; set; }

    /// <summary>
    /// 入住日前x天
    /// </summary>
    public int BeforeCheckInDays { get; set; }

    /// <summary>
    /// 入住日前x天时间 如14:00
    /// </summary>
    public TimeSpan? BeforeCheckInTime { get; set; }

    /// <summary>
    /// 入住当日 - 时间 如14:00
    /// </summary>
    public TimeSpan CheckInDateTime { get; set; }

    /// <summary>
    /// 取消收费类型
    /// </summary>
    public CancelChargeType CancelChargeType { get; set; }

    /// <summary>
    /// 收费值
    /// </summary>
    public int ChargeValue { get; set; }
}

public class ThirdPricestrategyCalendarPriceDto
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 成本价/采购价
    /// </summary>
    public decimal CostPrice { get; set; }

    /// <summary>
    /// 房量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 是否可超卖
    /// </summary>
    public bool OverSaleable { get; set; }

    /// <summary>
    /// 房态
    /// </summary>
    public bool Enabled { get; set; }
}
