using Contracts.Common.Hotel.Enums;
using Contracts.Common.Resource.Enums;

namespace Resource.Api.Services.RequestModel.ThirdPlatformHotel;

public class CheckAvailabilityResponse
{
    /// <summary>
    /// 可订码 0-成功
    /// </summary>
    public CheckPriceStrategySaleCode CheckCode { get; set; }

    /// <summary>
    /// bookingCode，试单接口返回的bookingCode 汇智酒店V3接口需要传入
    /// </summary>
    public string? BookingCode { get; set; }

    public string Msg { get; set; }

    public string PricestrategyId { get; set; }
    public string Name { get; set; }

    public string ENName { get; set; }

    /// <summary>
    /// 多段取消政策
    /// </summary>
    public List<ThirdPriceStrategyCancelRuleDto> CancelRules { get; set; }

    public int MaxOccupancy { get; set; }

    /// <summary>
    /// 餐食类型
    /// </summary>
    public BoardCodeType? BoardCodeType { get; set; }

    /// <summary>
    /// 餐食数量
    /// </summary>
    public int BoardCount { get; set; }

    /// <summary>
    /// 是否自动确认 立即确认
    /// </summary>
    public bool IsAutoConfirm { get; set; }

    /// <summary>
    /// 非立即确认报价预计确认时长，单位：分钟
    /// </summary>
    public int? ConfirmByMins { get; set; }

    /// <summary>
    /// 采购价币种
    /// </summary>
    public string CostCurrencyCode { get; set; } = Contracts.Common.Payment.Enums.Currency.CNY.ToString();

    /// <summary>
    /// 采购总价
    /// </summary>
    public decimal TotalPrice { get; set; }

    public IEnumerable<ThirdPricestrategyCalendarPriceDto> CalendarPrices { get; set; } = Enumerable.Empty<ThirdPricestrategyCalendarPriceDto>();

    /// <summary>
    /// 税费提醒
    /// </summary>
    public string? TaxDescription { get; set; }

    /// <summary>
    /// 税费说明
    /// </summary>
    public ArrivalTaxFeeDto[]? ArrivalTaxFees { get; set; }

}

public class ArrivalTaxFeeDto 
{
    /// <summary>
    /// 费用类型
    /// </summary>
    public HotelFeeType Type { get; set; }

    /// <summary>
    /// 是否强制，某些酒店可能允许客人选择是否支付度假村费
    /// </summary>
    public bool IsMandatory { get; set; }

    /// <summary>
    /// 费用的具体金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 币种
    /// </summary>
    public string Currency { get; set; }
}