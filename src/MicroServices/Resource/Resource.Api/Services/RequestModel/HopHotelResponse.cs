using System.Collections.Generic;

namespace Resource.Api.Services.RequestModel
{
    public class HopHotelResponse
    {
        public IEnumerable<HopHotel> Hotels { get; set; }
    }

    public class HopHotel
    {
        public int Hid { get; set; }
        public string CnName { get; set; } = string.Empty;
        public string EnName { get; set; } = string.Empty;
        public int CountryCode { get; set; }
        public int ProvinceCode { get; set; }
        public int CityCode { get; set; }
        public int DistrictCode { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 经度
        /// </summary>
        public string Longitude { get; set; }

        /// <summary>
        /// 纬度
        /// </summary>
        public string Latitude { get; set; }

        /// <summary>
        /// 星级
        /// </summary>
        public float Star { get; set; }
        /// <summary>
        /// 电话
        /// </summary>
        public string Tel { get; set; }
        /// <summary>
        /// 传真
        /// </summary>
        public string Fax { get; set; }
        /// <summary>
        /// 开业时间
        /// </summary>
        public string OpenTime { get; set; }
        /// <summary>
        /// 装修时间
        /// </summary>
        public string DecorateTime { get; set; }
        /// <summary>
        /// 周边
        /// </summary>
        public string Surroundings { get; set; }
        /// <summary>
        /// 商圈
        /// </summary>
        public string Business { get; set; }
        /// <summary>
        /// 楼层
        /// </summary>
        public string Floors { get; set; }
        /// <summary>
        /// 房间数
        /// </summary>
        public int RoomNum { get; set; }
        /// <summary>
        /// 缩略图
        /// </summary>
        public string Thumbnails { get; set; }
        /// <summary>
        /// 主图
        /// </summary>
        public string MainImgs { get; set; }

        public string ListImgs { get; set; }

        /// <summary>
        /// 酒店政策
        /// </summary>
        public string HotelPolicies { get; set; }
        /// <summary>
        /// 酒店服务
        /// </summary>
        public string HotelService { get; set; }
        /// <summary>
        /// 酒店设施
        /// </summary>
        public string HotelFacilities { get; set; }
        /// <summary>
        /// 房型设施
        /// </summary>
        public string RoomFacilities { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 注意事项
        /// </summary>
        public string ImportantNotices { get; set; }

        /// <summary>
        /// 酒店类型，酒店、民宿类、酒店式公寓、特色住宿类、青旅类、别墅类
        /// </summary>
        public string HotelCategory { get; set; }

        /// <summary>
        /// 发单邮箱
        /// </summary>
        public string? ReceiptEmail { get; set; }

        /// <summary>
        /// 团房数量
        /// </summary>
        public int ReunionRoom { get; set; }

        /// <summary>
        /// 0-非主推  1-主推
        /// </summary>
        public int StaffTag { get; set; }

        public IEnumerable<HopRoom> Rooms { get; set; }
    }

    public class HopRoom
    {
        public int Rid { get; set; }
        public string CnName { get; set; }
        public string EnName { get; set; }
        /// <summary>
        /// 0无窗 1有窗
        /// </summary>
        public int WindowType { get; set; }
        /// <summary>
        /// 0无宽带 1免费宽带 2收费宽带 3部分收费宽带
        /// </summary>
        public int Internet { get; set; }
        /// <summary>
        /// 最大入住人数
        /// </summary>
        public int MaxOccupancy { get; set; }
        /// <summary>
        /// 楼层
        /// </summary>
        public string Floor { get; set; }
        /// <summary>
        /// 房型描述
        /// </summary>
        public string BedJson { get; set; }
        /// <summary>
        /// 图片
        /// </summary>
        public string Pics { get; set; }

        /// <summary>
        /// 房间数
        /// </summary>
        public int RoomNum { get; set; }

        /// <summary>
        /// 房间面积
        /// </summary>
        public string Area { get; set; }

        /// <summary>
        /// 是否标准房型
        /// </summary>
        public bool IsStandardRoom { get; set; }
    }
}
