using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common.Resource.DTOs.ThirdHotel;
using Contracts.Common.Tenant.DTOs.SupplierApiFields;
using Contracts.Common.Tenant.DTOs.SupplierApiSetting;
using Contracts.Common.Tenant.Enums;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Resource.Api.ConfigModel;
using Resource.Api.Extensions;
using Resource.Api.Services.Interfaces;
using Resource.Api.Services.RequestModel.ThirdPlatformHotel;
using Resource.Api.Services.ThirdPlatformHotel;
using System.Collections.Concurrent;

namespace Resource.Api.Services;

public class ThirdHotelService : IThirdHotelService
{
    private readonly IEnumerable<IThirdPlatformHotelService> _thirdPlatformHotelServices;
    private readonly IThirdHotelBindService _thirdHotelBindService;
    private readonly IHotelService _hotelService;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IOptions<ServicesAddress> _servicesAddress;
    private readonly IOptions<OssConfig> _ossConfig;
    private readonly HotelPreBookingConfig _hotelPreBookingConfig;
    private readonly ILogger<ThirdHotelService> _logger;
    private readonly IHuizhiPlatformHotelService _huizhiPlatformHotelService;
    private readonly static TimeSpan _timeOutTimeSpan = TimeSpan.FromSeconds(10);

    public ThirdHotelService(IEnumerable<IThirdPlatformHotelService> thirdPlatformHotelServices,
        IThirdHotelBindService thirdHotelBindService,
        IHotelService hotelService,
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> servicesAddress,
        IOptions<OssConfig> ossConfig,
        IOptions<HotelPreBookingConfig> hotelPreBookingConfig,
        ILogger<ThirdHotelService> logger,
        IHuizhiPlatformHotelService huizhiPlatformHotelService)
    {
        _thirdPlatformHotelServices = thirdPlatformHotelServices;
        _thirdHotelBindService = thirdHotelBindService;
        _hotelService = hotelService;
        _httpClientFactory = httpClientFactory;
        _servicesAddress = servicesAddress;
        _ossConfig = ossConfig;
        _hotelPreBookingConfig = hotelPreBookingConfig.Value;
        _logger = logger;
        _huizhiPlatformHotelService = huizhiPlatformHotelService;
    }

    public async Task<GetThirdHotelPriceOutput> GetPrice(GetThirdHotelPriceInput input)
    {
        var thirdHotelBinds = await _thirdHotelBindService.GetHotelBinds(new GetThirdHotelBindsInput
        {
            ResourceHotelId = input.ResourceHotelId,
            SupplierApiTypes = input.SupplierApiTypes,
        });
        var thirdHotelBind = thirdHotelBinds.First(x => x.ResourceHotelId == input.ResourceHotelId);

        var hotelRooms = await _hotelService.GetRoomsByHotelIds(new List<long> { input.ResourceHotelId });
        var roomBinds = await _thirdHotelBindService.GetThirdHotelRoomBinds(new GetThirdHotelRoomBindsInput
        {
            ResourceRoomIds = hotelRooms.Select(x => x.Id).ToArray(),
            SupplierApiTypes = input.SupplierApiTypes,
        });

        var apiSettings = await GetSupplierApiSettings(new GetApiSettingInfosInput
        {
            TenantId = input.TenantId,
            SupplierEnabled = true
        });

        //异步循环查询第三方api接口数据
        ConcurrentDictionary<SupplierApiType, ThirdHotelPriceResponse> pairs = new();

        var cts = new CancellationTokenSource(_timeOutTimeSpan);
        try
        {
            await Parallel.ForEachAsync(thirdHotelBind.Binds, new ParallelOptions
            {
                MaxDegreeOfParallelism = 5,
                CancellationToken = cts.Token,
            }, async (bind, cancellationToken) =>
            {
                SupplierApiType supplierApi = bind.SupplierApiType;
                var apiSetting = apiSettings.FirstOrDefault(x => x.SupplierApiType == supplierApi);
                if (apiSetting is null) return;

                var thirdPlatformHotelService = GetThirdPlatformHotelService(supplierApi, apiSetting);
                ThirdHotelPricesRequest thirdHotelPricesRequest = new()
                {
                    ResourceHotelId = input.ResourceHotelId,
                    HotelId = bind.ThirdHotelId,
                    CheckIn = input.CheckIn,
                    CheckOut = input.CheckOut,
                    RoomNum = input.RoomNum ?? 1,
                    AdultNum = input.AdultNum,
                    ChildrenAges = input.ChildrenAges,
                    Nationality = input.Nationality,
                    IsGroupBooking = input.IsGroupBooking,
                    OnlyCachePrice = input.OnlyCachePrice,
                };
                try
                {
                    var thirdHotelPrice = await thirdPlatformHotelService.GetHotelPrices(thirdHotelPricesRequest, cancellationToken);
                    if (thirdHotelPrice is not null)
                        pairs.TryAdd(supplierApi, thirdHotelPrice);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex,
                        "第三方酒店API查价异常,SupplierApiType:{supplierApi},ThirdHotelPricesRequest:{thirdHotelPricesRequest}",
                        supplierApi, thirdHotelPricesRequest);
                }
            });
        }
        catch (TaskCanceledException) { }

        List<ThirdHotelRoomOutput> thirdHotelRooms = new();
        foreach (var pair in pairs)
        {
            var hotelBind = thirdHotelBind.Binds.FirstOrDefault(x => x.SupplierApiType == pair.Key);
            if (hotelBind is null) continue;
            var apiSetting = apiSettings.FirstOrDefault(x => x.SupplierApiType == pair.Key);
            var rooms = pair.Value.Rooms
                .Select(x =>
                {
                    var roomBind = roomBinds.FirstOrDefault(rb => rb.SupplierApiType == pair.Key && rb.ThirdRoomId == x.RoomId);
                    var thirdHotelRoom = new ThirdHotelRoomOutput
                    {
                        RoomId = x.RoomId,
                        ResourceRoomId = roomBind?.ResourceRoomId ?? 0,
                        Pricestrategies = x.PriceStrategies.Select(s => new ThirdHotelPricestrategyOutput
                        {
                            SupplierApiType = pair.Key,
                            PricestrategyId = s.PricestrategyId,
                            Name = s.Name,
                            ENName = s.EnName,
                            BookingHoursInAdvance = s.BookingHoursInAdvance,
                            Enabled = s.Enabled,
                            IsAutoConfirm = s.IsAutoConfirm,
                            ConfirmByMins = s.ConfirmByMins,
                            NumberOfBreakfast = s.BoardCodeType == Contracts.Common.Resource.Enums.BoardCodeType.BB ? s.BoardCount : 0,
                            BoardCodeType = s.BoardCodeType,
                            BoardCount = s.BoardCount,
                            NumberOfRooms = s.NumberOfRooms,
                            NumberOfNights = s.NumberOfNights,
                            LimitNumberOfNights = s.LimitNumberOfNights,
                            PriceStrategyType = s.PriceStrategyType,
                            SupplierId = apiSetting.SupplierId,
                            CostCurrencyCode = s.CostCurrencyCode,
                            NationalNames = s.NationalNames,
                            NationalityCodes = s.NationalityCodes,
                            NonNationalityCodes = s.NonNationalityCodes,
                            CancelRules = s.CancelRules?.Select(cr => new ThirdPriceStrategyCancelRuleOutput
                            {
                                BeforeCheckInDays = cr.BeforeCheckInDays,
                                CancelChargeType = cr.CancelChargeType,
                                CancelRulesType = cr.CancelRulesType,
                                ChargeValue = cr.ChargeValue,
                                CheckInDateTime = cr.CheckInDateTime,
                                BeforeCheckInTime = cr.BeforeCheckInTime,
                            }).ToList(),
                            Calendars = s.CalendarPrices.Select(c => new ThirdPriceStrategyCalendarPriceOutput
                            {
                                CostPrice = c.CostPrice,
                                Date = c.Date,
                                Enabled = c.Enabled,
                                OverSaleable = c.OverSaleable,
                                Quantity = c.Quantity,
                            }),
                            TotalCost = s.TotalPrice,
                            IsDirect = s.IsDirect,
                            MaxOccupancy = s.MaxOccupancy,
                            Tag = s.Tag,
                            BookingBenefits = s.BookingBenefits,
                            ChannelType = s.ChannelType,
                            SaleType = s.SaleType,
                            PreBookingCode = CreatePreBookingCode(new Contracts.Common.Hotel.DTOs.HotelPreBookingDto
                            {
                                BookingBenefits = s.BookingBenefits,
                                IsDirect = s.IsDirect,
                                LimitNumberOfNights = s.LimitNumberOfNights,
                                NumberOfNights = s.NumberOfNights,
                                NumberOfRooms = s.NumberOfRooms,
                                PriceStrategyType = s.PriceStrategyType,
                                Tag = s.Tag,
                                TotalPrice = s.TotalPrice,
                                DatePrices = s.CalendarPrices.Select(c => new Contracts.Common.Hotel.DTOs.HotelPreBookingDatePriceDto
                                {
                                    Date = c.Date,
                                    Price = c.CostPrice,
                                    Cost = c.CostPrice
                                }).ToArray(),
                            }),
                        })
                    };
                    return thirdHotelRoom;
                });
            thirdHotelRooms.AddRange(rooms);
        }
        thirdHotelRooms = thirdHotelRooms.Where(x => x.ResourceRoomId > 0)
            .GroupBy(x => x.ResourceRoomId)
            .Select(x =>
            {
                var thirdHotelRoom = new ThirdHotelRoomOutput
                {
                    ResourceRoomId = x.Key,
                    Pricestrategies = x.SelectMany(s => s.Pricestrategies)
                };
                var hotelRoom = hotelRooms.FirstOrDefault(hr => hr.Id == thirdHotelRoom.ResourceRoomId);
                if (hotelRoom is not null)
                {
                    thirdHotelRoom.ResourceRoomId = hotelRoom.Id;
                    thirdHotelRoom.ZHName = hotelRoom.ZHName;
                    thirdHotelRoom.ENName = hotelRoom.ENName;
                    thirdHotelRoom.AreaMin = hotelRoom.AreaMin;
                    thirdHotelRoom.AreaMax = hotelRoom.AreaMax;
                    thirdHotelRoom.FloorMax = hotelRoom.FloorMax;
                    thirdHotelRoom.FloorMin = hotelRoom.FloorMin;
                    thirdHotelRoom.RoomQuantity = hotelRoom.RoomQuantity;
                    thirdHotelRoom.MaximumOccupancy = hotelRoom.MaximumOccupancy;
                    thirdHotelRoom.BedType = hotelRoom.BedType;
                    thirdHotelRoom.WindowType = hotelRoom.WindowType;
                }
                return thirdHotelRoom;
            })
            .ToList();

        GetThirdHotelPriceOutput output = new()
        {
            ResourceHotelId = input.ResourceHotelId,
            Rooms = thirdHotelRooms
        };
        return output;
    }

    public async Task<IEnumerable<QueryDateMinPriceOutput>> QueryDateMinPrices(QueryDateMinPriceInput input)
    {
        SupplierApiType supplierApi = SupplierApiType.Hop;
        var thirdHotelBinds = await _thirdHotelBindService.GetHotelBinds(input.ResourceHotelIds.Select(x => new GetThirdHotelBindsInput
        {
            ResourceHotelId = x,
            SupplierApiTypes = new SupplierApiType[] { supplierApi },
        }).ToArray());

        var hotelRooms = await _hotelService.GetRoomsByHotelIds(input.ResourceHotelIds);
        var roomBinds = await _thirdHotelBindService.GetThirdHotelRoomBinds(new GetThirdHotelRoomBindsInput
        {
            ResourceRoomIds = hotelRooms.Select(x => x.Id).ToArray(),
            SupplierApiTypes = new SupplierApiType[] { supplierApi },
        });

        var apiSetting = (await GetSupplierApiSettings(new GetApiSettingInfosInput
        {
            TenantId = input.TenantId,
            SupplierApiType = SupplierApiType.Hop,
            SupplierEnabled = true,
        })).FirstOrDefault();
        if (apiSetting is null)
            return new List<QueryDateMinPriceOutput>();

        var thirdHotelIds = thirdHotelBinds
            .Select(x => x.Binds
                .Where(b => b.SupplierApiType == supplierApi && !string.IsNullOrWhiteSpace(b.ThirdHotelId))
                .Select(s => s.ThirdHotelId)
                .FirstOrDefault()
             ).ToArray();

        if (thirdHotelIds?.Length is not > 0)
            return new List<QueryDateMinPriceOutput>();

        var appKey = apiSetting.SupplierApiSettingFields
                  .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.HopCode.HopKey)?.FieldValue;
        var appSecret = apiSetting.SupplierApiSettingFields
                             .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.HopCode.HopSecret)?.FieldValue;

        var thirdPlatformHotelService = _thirdPlatformHotelServices
            .First(x => x.SupplierApiType == supplierApi)
            .SetSupplierApiInfo(new SupplierApiInfo
            {
                AppKey = appKey,
                AppSecret = appSecret
            });
        Dictionary<string, ThirdHotelPriceResponse> thirdHotelPricesPairs = new();

        ThirdHotelsPricesRequest thirdHotelsPricesRequest = new()
        {
            HotelIds = thirdHotelIds,
            CheckIn = input.CheckIn,
            CheckOut = input.CheckOut,
            RoomNum = input.RoomNum ?? 1,
        };
        var responnses = await (thirdPlatformHotelService as IHopOpenApiPlatformHotelService).GetHotelPrices(thirdHotelsPricesRequest);

        foreach (var response in responnses)
        {
            thirdHotelPricesPairs.Add(response.HotelId, response);
        }

        List<QueryDateMinPriceOutput> result = new();
        foreach (var keyValue in thirdHotelPricesPairs)
        {
            var hotelBind = thirdHotelBinds
                .Where(x => x.Binds.Any(x => x.ThirdHotelId == keyValue.Key))
                .FirstOrDefault();
            if (hotelBind is null) continue;

            var dateMinPrices = keyValue.Value.Rooms.Where(x => roomBinds.Any(s => s.ThirdRoomId == x.RoomId))
                 .SelectMany(r => r.PriceStrategies)
                 .SelectMany(p => p.CalendarPrices).Where(x => x.Enabled)
                 .GroupBy(d => d.Date)
                 .Select(d => new DateMinPriceOutput
                 {
                     Date = d.Key,
                     MinPrice = d.Min(x => x.CostPrice)
                 });

            QueryDateMinPriceOutput output = new()
            {
                ResourceHotelId = hotelBind.ResourceHotelId,
                DateMinPrices = dateMinPrices
            };
            result.Add(output);
        }
        return result;
    }

    #region

    private IThirdPlatformHotelService GetThirdPlatformHotelService(SupplierApiType supplierApi, SupplierApiSettingDto apiSetting)
    {
        var appKey = apiSetting.SupplierApiSettingFields
                 .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.HopCode.HopKey)?.FieldValue;
        var appSecret = apiSetting.SupplierApiSettingFields
                             .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.HopCode.HopSecret)?.FieldValue;

        var thirdPlatformHotelService = _thirdPlatformHotelServices.First(x => x.SupplierApiType == supplierApi)
                    .SetSupplierApiInfo(new SupplierApiInfo
                    {
                        AppKey = appKey,
                        AppSecret = appSecret
                    });
        return thirdPlatformHotelService;
    }

    private async Task<List<SupplierApiSettingDto>> GetSupplierApiSettings(GetApiSettingInfosInput input)
    {
        var requestUri = _servicesAddress.Value.Tenant_GetApiSettingInfos();
        using var content = new StringContent(JsonConvert.SerializeObject(input), Encoding.UTF8, "application/json");
        var result = await _httpClientFactory.InternalPostAsync<List<SupplierApiSettingDto>>(requestUri, httpContent: content);
        return result;
    }

    private string CreatePreBookingCode(Contracts.Common.Hotel.DTOs.HotelPreBookingDto dto)
    {
        var data = JsonConvert.SerializeObject(dto);
        var code = Common.Utils.SecurityUtil.AESEncrypt(data, _hotelPreBookingConfig.Key, _hotelPreBookingConfig.IV);
        return code;
    }

    #endregion

    public async Task<CheckAvailabilityOutput> CheckAvailability(CheckAvailabilityInput input)
    {
        var supplierApiSetting = (await GetSupplierApiSettings(new GetApiSettingInfosInput
        {
            TenantId = input.TenantId,
            SupplierApiType = input.SupplierApiType
        }))
       .First();
        IThirdPlatformHotelService thirdPlatformHotelService = GetThirdPlatformHotelService(input.SupplierApiType, supplierApiSetting);

        var thirdHotelBindOutput = (await _thirdHotelBindService.GetHotelBinds(new GetThirdHotelBindsInput
        {
            ResourceHotelId = input.ResourceHotelId,
            SupplierApiTypes = new SupplierApiType[] { input.SupplierApiType }
        }))
       .FirstOrDefault(x => x.ResourceHotelId == input.ResourceHotelId);
        var thirdHotelId = thirdHotelBindOutput.Binds.FirstOrDefault(x => x.SupplierApiType == input.SupplierApiType).ThirdHotelId;

        var thirdRoomId = (await _thirdHotelBindService.GetThirdHotelRoomBinds(new GetThirdHotelRoomBindsInput
        {
            ResourceRoomIds = new[] { input.ResourceRoomId },
            SupplierApiTypes = new SupplierApiType[] { input.SupplierApiType }
        }))
        .Where(x => x.ResourceRoomId == input.ResourceRoomId && x.SupplierApiType == input.SupplierApiType)
        .Select(x => x.ThirdRoomId)
        .FirstOrDefault();

        var response = await thirdPlatformHotelService.CheckAvailability(new CheckAvailabilityRequest
        {
            ResourceHotelId = input.ResourceHotelId,
            HotelId = thirdHotelId,
            RoomId = thirdRoomId,
            PricestrategyId = input.PricestrategyId,
            AdultNum = input.AdultNum,
            ChildrenAges = input.ChildrenAges,
            RoomNum = input.RoomNum,
            CheckIn = input.CheckIn,
            CheckOut = input.CheckOut,
            CalendarPrices = input.CalendarPrices?.Select(x => new OrderCalendarPriceDto { CostPrice = x.CostPrice, Date = x.Date }),
            TotalPrice = input.TotalCost,
            Nationality = input.Nationality,
        });
        var hotelRooms = await _hotelService.GetRoomsByHotelIds(new List<long> { input.ResourceHotelId });
        ThirdHotelRoomOutput? thirdHotelRoom = default;
        var hotelRoom = hotelRooms.FirstOrDefault(hr => hr.Id == input.ResourceRoomId);
        if (hotelRoom is not null)
        {
            thirdHotelRoom = new()
            {
                ResourceRoomId = hotelRoom.Id,
                ZHName = hotelRoom.ZHName,
                ENName = hotelRoom.ENName,
                AreaMin = hotelRoom.AreaMin,
                AreaMax = hotelRoom.AreaMax,
                FloorMax = hotelRoom.FloorMax,
                FloorMin = hotelRoom.FloorMin,
                RoomQuantity = hotelRoom.RoomQuantity,
                MaximumOccupancy = hotelRoom.MaximumOccupancy,
                BedType = hotelRoom.BedType,
                WindowType = hotelRoom.WindowType
            };
        }
        CheckAvailabilityOutput output = new()
        {
            HotelId = thirdHotelId,
            RoomId = thirdRoomId,
            PricestrategyId = response.PricestrategyId,
            Name = response.Name,
            ENName = response.ENName,
            Room = thirdHotelRoom,
            SupplierId = supplierApiSetting.SupplierId,
            IsAutoConfirm = response.IsAutoConfirm,
            ConfirmByMins = response.ConfirmByMins,
            BreakfastCount = response.BoardCodeType == Contracts.Common.Resource.Enums.BoardCodeType.BB ? response.BoardCount : 0,
            BoardCodeType = response.BoardCodeType,
            BoardCount = response.BoardCount,
            MaxOccupancy = response.MaxOccupancy,
            CancelRules = response.CancelRules?.Select(s => new ThirdPriceStrategyCancelRuleOutput
            {
                BeforeCheckInDays = s.BeforeCheckInDays,
                BeforeCheckInTime = s.BeforeCheckInTime,
                CancelChargeType = s.CancelChargeType,
                CancelRulesType = s.CancelRulesType,
                ChargeValue = s.ChargeValue,
                CheckInDateTime = s.CheckInDateTime,
            }).ToList(),
            CostCurrencyCode = response.CostCurrencyCode,
            TotalPrice = response.TotalPrice,
            CalendarPrices = response.CalendarPrices.Select(x => new ThirdPriceStrategyCalendarPriceOutput
            {
                CostPrice = x.CostPrice,
                Date = x.Date,
                Enabled = x.Enabled,
                OverSaleable = x.OverSaleable,
                Quantity = x.Quantity
            }),
            TaxDescription = response.TaxDescription,
            ArrivalTaxFees = response.ArrivalTaxFees?
            .Select(s => new CheckAvailabilityArrivalTaxFeeOutput
            {
                Type = s.Type,
                IsMandatory = s.IsMandatory,
                Currency = s.Currency,
                Amount = s.Amount,
            }).ToArray(),
            CheckCode = response.CheckCode,
            BookingCode = response.BookingCode,
            Msg = response.Msg,
        };
        return output;
    }

    public async Task<CreateOrderOutput> CreateOrder(CreateOrderInput input)
    {
        var supplierApiSetting = (await GetSupplierApiSettings(new GetApiSettingInfosInput
        {
            TenantId = input.TenantId,
            SupplierApiType = input.SupplierApiType
        }))
        .First();
        IThirdPlatformHotelService thirdPlatformHotelService = GetThirdPlatformHotelService(input.SupplierApiType, supplierApiSetting);
        string thirdHotelId = input.HotelId;
        //未传第三方酒店id 查询资源酒店绑定的第三方酒店id （汇智酒店）
        if (string.IsNullOrWhiteSpace(input.HotelId) && input.ResourceHotelId.HasValue)
        {
            var thirdHotelBindOutput = (await _thirdHotelBindService.GetHotelBinds(new GetThirdHotelBindsInput
            {
                ResourceHotelId = input.ResourceHotelId!.Value,
                SupplierApiTypes = new SupplierApiType[] { input.SupplierApiType }
            }))
            .FirstOrDefault(x => x.ResourceHotelId == input.ResourceHotelId);
            thirdHotelId = thirdHotelBindOutput.Binds.FirstOrDefault(x => x.SupplierApiType == input.SupplierApiType).ThirdHotelId;
        }
        CreateOrderRequest createOrderRequest = new()
        {
            HotelId = thirdHotelId,
            RoomId = input.RoomId,
            PricestrategyId = input.PricestrategyId,
            BaseOrderId = input.BaseOrderId,
            UnionOrderId = input.UnionOrderId,
            UnionOrderCount = input.UnionOrderCount,
            AdditionalItems = input.AdditionalItems?.Select(x => new AdditionalItem
            {
                Optional = x.Optional,
                Cost = x.Cost,
                Num = x.Num,
                Price = x.Price,
                Remarks = x.Remarks,
            }).ToArray(),
            ContactsName = input.ContactsName,
            ContactsPhoneNumber = input.ContactsPhoneNumber,
            ContactsEmail = input.ContactsEmail,
            CheckIn = input.CheckIn,
            CheckOut = input.CheckOut,
            RoomCount = input.RoomNum,
            Adults = input.Adults,
            Guests = input.Guests.Select(x => new OrderGuestDto { GuestName = x.GuestName, FirstName = x.FirstName, LastName = x.LastName, HotelOrderGuestType = x.HotelOrderGuestType, Age = x.Age }),
            ChildrenAges = input.ChildrenAges,
            Nationality = input.Nationality,
            CalendarPrices = input.CalendarPrices.Select(x => new OrderCalendarPriceDto
            {
                CostPrice = x.CostPrice,
                Date = x.Date
            }),
            CurrencyCode = input.CurrencyCode,
            Message = input.Message,
            SpecialRemarks = input.SpecialRemarks,
            BookingCode = input.BookingCode,
        };

        //选择抽成模式，则将该租户通过汇智酒店的订单下到HOP的时候传过去佣金+手续费
        if (supplierApiSetting.CooperationMode == CooperationModeType.CommissionModel
            && supplierApiSetting.SupplierApiType == SupplierApiType.Hop)
        {
            var config = await GetSysConfigByTenantId(input.TenantId);
            if (config is not null)
            {
                createOrderRequest.CommissionRate = config.PaymentFeeRate + config.PlatformCommissionRate;
            }
        }
        var result = await thirdPlatformHotelService.CreateOrder(createOrderRequest);
        return new CreateOrderOutput
        {
            SupplierOrderId = result.SupplierOrderId,
            Status = result.Status,
            Message = result.Msg,
        };
    }

    public async Task<QueryOrderDetailOutput> QueryOrderDetail(QueryOrderDetailInput input)
    {
        var supplierApiSetting = (await GetSupplierApiSettings(new GetApiSettingInfosInput
        {
            TenantId = input.TenantId,
            SupplierApiType = input.SupplierApiType
        }))
       .First();
        IThirdPlatformHotelService thirdPlatformHotelService = GetThirdPlatformHotelService(input.SupplierApiType, supplierApiSetting);

        var response = await thirdPlatformHotelService.QueryOrderDetail(new QueryOrderDetailRequest
        {
            BaseOrderId = input.BaseOrderId,
            SupplierOrderId = input.SupplierOrderId
        });
        if (response == null) return null;
        var output = new QueryOrderDetailOutput()
        {
            HotelId = response.HotelId,
            RoomId = response.RoomId,
            PricestrategyId = response.PricestrategyId,
            CheckIn = response.CheckIn,
            CheckOut = response.CheckOut,
            PriceStrategyNumberOfBreakfast = response.BoardCodeType == Contracts.Common.Resource.Enums.BoardCodeType.BB ? response.BoardCount : 0,
            BoardCodeType = response.BoardCodeType,
            BoardCount = response.BoardCount,
            RoomNum = response.RoomCount,
            Guests = response.Guests?.Select(x => new HotelOrderGuestDto { GuestName = x.GuestName, FirstName = x.FirstName, LastName = x.LastName }),
            CancelRules = response.CancelRules?.Select(s => new ThirdPriceStrategyCancelRuleOutput
            {
                BeforeCheckInDays = s.BeforeCheckInDays,
                BeforeCheckInTime = s.BeforeCheckInTime,
                CancelChargeType = s.CancelChargeType,
                CancelRulesType = s.CancelRulesType,
                ChargeValue = s.ChargeValue,
                CheckInDateTime = s.CheckInDateTime,
            }).ToList(),
            TotalPrice = response.TotalPrice,
            ConfirmCode = response.ConfirmCode,
            Status = response.Status,
            SupplierOrderId = response.SupplierOrderId,
        };
        return output;
    }

    public async Task<CancelOrderOutput> CancelOrder(CancelOrderInput input)
    {
        var supplierApiSetting = (await GetSupplierApiSettings(new GetApiSettingInfosInput
        {
            TenantId = input.TenantId,
            SupplierApiType = input.SupplierApiType
        }))
      .First();
        IThirdPlatformHotelService thirdPlatformHotelService = GetThirdPlatformHotelService(input.SupplierApiType, supplierApiSetting);

        var response = await thirdPlatformHotelService.CancelOrder(new CancelOrderRequest
        {
            BaseOrderId = input.BaseOrderId,
            SupplierOrderId = input.SupplierOrderId
        });
        var output = new CancelOrderOutput
        {
            IsSuccessed = response.IsSuccessed,
            Message = response.Msg,
            CancellationFees = response.CancellationFees,
            SupplierOrderId = response.SupplierOrderId,
        };
        return output;
    }

    public async Task UpdateOrderGuest(UpdateOrderGuestInput input)
    {
        var supplierApiSetting = (await GetSupplierApiSettings(new GetApiSettingInfosInput
        {
            TenantId = input.TenantId,
            SupplierApiType = SupplierApiType.Hop
        }))
         .First();
        var thirdPlatformHotelService = (IHopOpenApiPlatformHotelService)GetThirdPlatformHotelService(SupplierApiType.Hop, supplierApiSetting);

        var requests = input.OrderGuests.Select(x =>
        {
            var guests = x.Guests
            .Select(g => new OrderGuestDto { GuestName = g.GuestName, FirstName = g.FirstName, LastName = g.LastName })
            .ToList();
            var fileUrl = $"{_ossConfig.Value.DomainName}/{x.FilePath}";
            return new RequestModel.ThirdPlatformHotel.UpdateOrderGuestDto
            {
                SupplierOrderId = x.SupplierOrderId,
                Link = fileUrl,
                Guests = guests
            };
        }).ToList();
        var response = await thirdPlatformHotelService.UpdateOrderGuests(new UpdateGuestsRequest
        {
            OrderGuests = requests
        });

        if (response.IsSuccessed is not true)
            throw new BusinessException("Failed to update the order guest information.");

    }

    public async Task<List<GetHotHotelOutput>> GetHotHotel(GetHotHotelInput input)
    {
        var supplierApiType = SupplierApiType.Hop;
        var supplierApiSetting = (await GetSupplierApiSettings(new GetApiSettingInfosInput
        {
            TenantId = input.TenantId,
            SupplierApiType = SupplierApiType.Hop
        })).First();

        if (supplierApiSetting == null)
            return new List<GetHotHotelOutput>();

        _huizhiPlatformHotelService.SetSupplierApiInfo(new SupplierApiInfo {
            AppSecret = supplierApiSetting.AppSecret,
            AppKey = supplierApiSetting.AppKey,
        });
        var datas = await _huizhiPlatformHotelService.SelfsupportGetHotHotel(new ThirdPlatformHotel.HuiZhiHotel.SelfsupportGetHotHotelRequest { 
            StartDate = input.StartDate,
            EndDate = input.EndDate,
        });

        return datas.Select(x => new GetHotHotelOutput
        {
            CountryCode = x.CountryCode,
            Hotels = x.Hotels.Select(o => new GetHotHotelDetailOutput
            {
                HopId = o.HopId,
            }).ToList()
        }).ToList();
    }

    private async Task<Contracts.Common.Tenant.DTOs.Tenant.GetTenantSysConfigOutput> GetSysConfigByTenantId(long tenantId)
    {
        var requestUri = _servicesAddress.Value.Tenant_GetSysConfigByTenantId(tenantId);
        var result = await _httpClientFactory.InternalGetAsync<Contracts.Common.Tenant.DTOs.Tenant.GetTenantSysConfigOutput>(requestUri);
        return result;
    }
}


