using AutoMapper;
using Contracts.Common.Resource.DTOs.Holiday;
using Contracts.Common.Resource.DTOs.Holiday.OpenApiModel;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Resource.Api.Services.Interfaces;

namespace Resource.Api.Services;

public class HolidayService : IHolidayService
{
    private readonly ILogger _logger;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IMapper _mapper;
    private readonly CustomDbContext _dbContext;

    public HolidayService(
         CustomDbContext dbContext,
        ILoggerFactory loggerFactory,
        IHttpClientFactory httpClientFactory,
        IMapper mapper)
    {
        _logger = loggerFactory.CreateLogger<HolidayService>();
        _httpClientFactory = httpClientFactory;
        _mapper = mapper;
        _dbContext = dbContext;
    }

    /// <summary>
    /// 因为有些节假日数据是跨年的，12-30、12-31有时候会跨年，放在下一年数据中了
    /// </summary>
    /// <param name="years"></param>
    /// <returns></returns>
    public async Task<List<HolidayListOutput>> HolidayList(int[] years)
    {
        var holiday = await _dbContext.Holidays.Where(x => years.Contains(x.Year)).ToListAsync();
        var res = new List<HolidayListOutput>();
        foreach (var year in holiday)
        {
            res.Add(new HolidayListOutput
            {
                Year = year.Year,
                Holidays = JsonConvert.DeserializeObject<List<HolidayDto>>(year.HolidayDetail)
            });
        }
        return res;
    }

    public async Task<string> SyncHolidayList()
    {
        var year = DateTime.Now.Year;
        var years = new List<int>() { year, year + 1 };
        var oldHolidayCount = await _dbContext.Holidays.Where(x => years.Contains(x.Year)).CountAsync();
        if (oldHolidayCount == 2)
            return $"{string.Join(',', years)},已存在";
        if (oldHolidayCount == 1)
        {
            year = year + 1;// 获取下一年的节假日
        }
        var apiRes = await GetHoliday(year);
        if (apiRes?.Days?.Any() == true)
        {
            var list = new List<HolidayDto>();
            foreach (var item in apiRes.Days)
            {
                var info = new HolidayDto();
                info.Date = DateTime.Parse(item.Date);

                info.IsOffDay = item.IsOffDay;
                info.Name = item.Name;
                list.Add(info);
            }

            if (list.Any())
            {
                var holiday = new Holiday()
                {
                    Year = year,
                    HolidayDetail = JsonConvert.SerializeObject(list),
                    CreateTime = DateTime.Now
                };
                await _dbContext.Holidays.AddAsync(holiday);
                await _dbContext.SaveChangesAsync();
            }
        }
        return $"{year},success";
    }


    /// <summary>
    /// 
    /// </summary>
    /// <param name="year"></param>
    /// <returns></returns>
    /// <exception cref="HttpRequestException"></exception>
    private async Task<ApiHolidayResult> GetHoliday(int year)
    {
        using var client = GetHttpClient();
        var url = $"https://holiday.cyi.me/api/holidays?year={year}";
        var response = await client.GetAsync(url);
        var responseString = await response.Content.ReadAsStringAsync();
        response.EnsureSuccessStatusCode();
        var resData = JsonConvert.DeserializeObject<ApiHolidayResult>(responseString);
        return resData?.Days?.Any() == true ? resData : throw new Exception("获取节假日失败");
    }

    private HttpClient GetHttpClient()
    {
        var client = _httpClientFactory.CreateClient("Holiday");
        return client;
    }
}
