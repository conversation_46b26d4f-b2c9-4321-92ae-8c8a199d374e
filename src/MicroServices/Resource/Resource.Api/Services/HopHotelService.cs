using AutoMapper;
using Cit.Storage.Aliyun.Oss;
using Cit.Storage.Redis;
using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common.Hotel.DTOs;
using Contracts.Common.Hotel.DTOs.ApiHotel;
using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Product.Messages;
using Contracts.Common.Resource.DTOs;
using Contracts.Common.Resource.DTOs.HopHotel;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Tenant.Enums;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Resource.Api.ConfigModel;
using Resource.Api.Extensions;
using Resource.Api.Services.Interfaces;
using Resource.Api.Services.RequestModel;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text.RegularExpressions;

namespace Resource.Api.Services;

public class HopHotelService : IHopHotelService
{
    public const string RESOURCE_HOPHOTEL_BATCHHOTELINFO = "resource:hophotel:batch_hotelinfo";
    private readonly CustomDbContext _dbContext;
    private readonly ILocationService _locationService;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IRedisClient _redisClient;
    private readonly ILogger<HopHotelService> _logger;
    private readonly ICapPublisher _capPublisher;
    private readonly IMapper _mapper;
    private readonly ServicesAddress _servicesAddress;
    private readonly IAliyunOssObject _aliyunOssObject;
    private readonly IFileResourceService _fileResourceService;
    private readonly HuiZhiHotelConfig _huiZhiHotelConfig;

    public HopHotelService(CustomDbContext dbContext,
        ILocationService locationService,
        IHttpClientFactory httpClientFactory,
        IRedisClient redisClient,
        ILogger<HopHotelService> logger,
        ICapPublisher capPublisher,
        IMapper mapper,
        IOptionsMonitor<HuiZhiHotelConfig> huiZhiHotelConfig,
        IOptions<ServicesAddress> servicesAddress,
        IAliyunOssObject aliyunOssObject,
        IFileResourceService fileResourceService)
    {
        _dbContext = dbContext;
        _locationService = locationService;
        _httpClientFactory = httpClientFactory;
        _redisClient = redisClient;
        _logger = logger;
        _capPublisher = capPublisher;
        _mapper = mapper;
        _servicesAddress = servicesAddress.Value;
        _aliyunOssObject = aliyunOssObject;
        _fileResourceService = fileResourceService;
        _huiZhiHotelConfig = huiZhiHotelConfig.CurrentValue;
    }

    public async Task<DownloadHopHotelDataResult> BatchHopHotelInfo()
    {
        var result = new DownloadHopHotelDataResult();
        var batchHotelInfo = await _redisClient.StringGetAsync<BatchHotelInfo>(RESOURCE_HOPHOTEL_BATCHHOTELINFO);
        if (batchHotelInfo?.IsProcessing is true)
        {
            return new DownloadHopHotelDataResult
            {
                IsSuccessed = false,
                Message = "批量拉取正在执行中，请稍后再试..",
            };
        }

        var maxHopHid = batchHotelInfo?.MaxHopHid ?? 0;
        //获取hop酒店信息的最大hid
        var hopHid = await _dbContext.Hotels
            .OrderByDescending(h => h.HopId)
            .Select(h => h.HopId)
            .FirstOrDefaultAsync();
        if (maxHopHid > 0 && maxHopHid != hopHid)
            throw new BusinessException("上次更新的最大hophid跟数据库最大hophid不一致");
        try
        {
            //同步中
            batchHotelInfo = new BatchHotelInfo { IsProcessing = true, MaxHopHid = hopHid };
            await _redisClient.StringSetAsync(RESOURCE_HOPHOTEL_BATCHHOTELINFO, batchHotelInfo, TimeSpan.FromDays(1));

            var batchHopHotelResult = await BatchHopHotels(new BatchHopHotelInput
            {
                MinHid = hopHid,
                Count = 1000//默认一次取1000条
            });
            if (batchHopHotelResult.IsSuccessed)
                maxHopHid = batchHopHotelResult.MaxHopHid;//执行成功更新HOP MaxHid

            return batchHopHotelResult;
        }
        catch (Exception ex)
        {
            batchHotelInfo = new BatchHotelInfo { IsProcessing = false, MaxHopHid = hopHid };
            await _redisClient.StringSetAsync(RESOURCE_HOPHOTEL_BATCHHOTELINFO, batchHotelInfo, TimeSpan.FromDays(1));
            throw;
        }
        finally
        {
            //执行完成
            batchHotelInfo = new() { IsProcessing = false, MaxHopHid = maxHopHid };
            await _redisClient.StringSetAsync(RESOURCE_HOPHOTEL_BATCHHOTELINFO, batchHotelInfo, TimeSpan.FromDays(1));
        }
    }

    [UnitOfWork]
    public async Task<UpdateHotelOutput> UpdateHotel(long hotelId)
    {
        var hotel = await _dbContext.Hotels.FirstAsync(x => x.Id == hotelId);
        if (hotel.UpdateDisabled)
            return new UpdateHotelOutput { IsSuccessed = false, Message = "Hotel update disabled." };
        HopHotel? hotelInfo = null;
        if (hotel.HopId > 0)
        {
            var hopHotelResponse = await GetHopHotelResponse(hotel.HopId);
            hotelInfo = hopHotelResponse?.Hotels?.FirstOrDefault();
        }
        //_logger.LogInformation("同步HOP酒店信息HotelId:{hotelId},HopId:{hopId}", hotel.Id, hotel.HopId);
        return await UpdateHotel(hotel, hotelInfo);
    }

    private async Task<UpdateHotelOutput> UpdateHotel(Hotel hotel,
        HopHotel? hotelInfo,
        List<ApiHotelInfo>? apiHotelInfos = null)
    {
        if (hotel.UpdateDisabled)
            return new UpdateHotelOutput { IsSuccessed = false, Message = "Hotel update disabled." };
        var hotelId = hotel.Id;
        var hotelInfoModel = new HotelInfoModel()
        {
            Hotel = hotel,
            HotelRooms = new List<HotelRoom>(),
            HotelPhotos = new List<HotelPhotos>()
        };
        if (hotel.HopId > 0)
        {
            if (hotelInfo is null)
                return new UpdateHotelOutput { IsSuccessed = false, Message = "未查询到HOP酒店信息" };
            //修复字段数据长度
            FixHopHotel(hotelInfo);
            hotelInfoModel.HopHotel = hotelInfo;
            //酒店图片
            var hotelPhotos = new List<HotelPhotos>();
            var imgs = $"{hotelInfo.MainImgs},{hotelInfo.ListImgs}".Split(',').Where(i => !string.IsNullOrWhiteSpace(i));
            foreach (var i in imgs)
            {
                hotelPhotos.Add(new HotelPhotos { HotelId = hotel.Id, Path = i, Enabled = true });
            }
            if (hotelPhotos.Any())
                hotelInfoModel.HotelPhotos = hotelInfoModel.HotelPhotos.Union(hotelPhotos);

            var rooms = GetHotelRooms(hotel.Id, hotelInfo.Rooms, out List<HotelPhotos> hotelRoomPhotos);

            hotelInfoModel.HotelRooms = rooms;
            if (hotelRoomPhotos.Any())
                hotelInfoModel.HotelPhotos = hotelInfoModel.HotelPhotos.Union(hotelRoomPhotos);
        }
        else
        { //非Hop酒店房型同步
            hotelInfoModel.HotelRooms = await _dbContext.HotelRooms.AsNoTracking()
                .Where(x => x.HotelId == hotelId).ToListAsync();
            hotelInfoModel.HotelPhotos = await _dbContext.HotelPhotos.AsNoTracking()
                .Where(x => x.HotelId == hotelId && x.HotelRoomId != 0)
                .ToListAsync();
        }
        //TODO 更新酒店信息
        var isSuccessed = await UpdateHotelInfo(hotel.Id, hotel.HopId, hotelInfoModel, apiHotelInfos);

        var result = new UpdateHotelOutput
        {
            IsSuccessed = isSuccessed,
            Message = isSuccessed ? "更新成功" : "程序异常"
        };
        //_logger.LogInformation("同步HOP酒店信息HotelId:{hotelId},Result:{@result}", hotel.Id, result);
        return result;
    }

    private async Task<bool> UpdateHotelInfo(long hotelId,
        int hopId,
        HotelInfoModel hotelInfo,
        List<ApiHotelInfo>? apiHotelInfos)
    {
        SyncHotelRoomsInput syncHotelRooms = new() { HotelId = hotelId, Rooms = new(), Photos = new() };
        var hotel = hotelInfo.Hotel;
        OriginInfo originInfo = new(hotel.Intro, hotel.SurroundingFacilities, hotel.CheckinPolicy, hotel.ImportantNotices);
        if (hopId > 0)
        {//HOP酒店房型同步
            #region 房型信息

            var resRooms = await _dbContext.HotelRooms.Where(x => x.HotelId == hotelId).ToListAsync();
            var addRooms = new List<HotelRoom>();
            foreach (var hopRoom in hotelInfo.HotelRooms)
            {
                var resRoom = resRooms.FirstOrDefault(x => x.HopRoomId == hopRoom.HopRoomId);
                var roomPhotos = hotelInfo.HotelPhotos
                    .Where(x => x.HotelRoomId == hopRoom.Id)
                    .ToList();
                if (resRoom is null)
                {
                    addRooms.Add(hopRoom);
                }
                else
                {
                    hopRoom.Id = resRoom.Id;
                    foreach (var photo in roomPhotos) photo.HotelRoomId = resRoom.Id;

                    resRoom.ZHName = hopRoom.ZHName;
                    resRoom.ENName = hopRoom.ENName;
                    resRoom.AreaMax = hopRoom.AreaMax;
                    resRoom.AreaMin = hopRoom.AreaMin;
                    resRoom.FloorMax = hopRoom.FloorMax;
                    resRoom.FloorMin = hopRoom.FloorMin;
                    resRoom.MaximumOccupancy = hopRoom.MaximumOccupancy;
                    resRoom.RoomQuantity = hopRoom.RoomQuantity;
                    resRoom.WindowType = hopRoom.WindowType;
                    resRoom.BedType = hopRoom.BedType;
                    resRoom.IsStandardRoom = hopRoom.IsStandardRoom;
                    resRoom.UpdateTime = DateTime.Now;
                }
                syncHotelRooms.Rooms.Add(_mapper.Map<HotelRoomDto>(hopRoom));
                syncHotelRooms.Photos.AddRange(_mapper.Map<List<HotelPhotosDto>>(roomPhotos));
            }

            await _dbContext.HotelRooms.AddRangeAsync(addRooms);
            await _dbContext.AddRangeAsync(addRooms.Select(x => new ThirdHotelRoomBind
            {
                ResourceRoomId = x.Id,
                ThirdRoomId = x.HopRoomId.ToString(),
                SupplierApiType = SupplierApiType.Hop
            }));

            var publishRooms = new List<HotelRoom>();
            publishRooms.AddRange(addRooms);
            publishRooms.AddRange(resRooms);
            //推送byteplus
            if (publishRooms.Any() && apiHotelInfos is not null && apiHotelInfos.Any())
                await bytePlusItemAttributesUpdate(hopId, publishRooms, apiHotelInfos);
            #endregion

            #region 酒店图片

            //仅添加不存在图片的酒店图片、房型图片
            var existsHotelPhotoRoomIds = await _dbContext.HotelPhotos
                .Where(x => x.HotelId == hotelId && x.Enabled)
                .GroupBy(x => x.HotelRoomId)
                .Select(x => x.Key)
                .ToListAsync();

            var hopPhotos = hotelInfo.HotelPhotos
                .Where(x => existsHotelPhotoRoomIds.Contains(x.HotelRoomId) is false);
            var uploadResults = await UploadHotelPhotos(hopPhotos);

            var hotelPhotos = new List<HotelPhotos>();
            foreach (var hotelPhoto in hopPhotos)
            {
                var uploadResult = uploadResults.FirstOrDefault(x => x.SourceUrl == hotelPhoto.Path);
                if (uploadResult is null) continue;

                hotelPhoto.Path = uploadResult.Path;
                hotelPhotos.Add(hotelPhoto);
            }
            await _dbContext.HotelPhotos.AddRangeAsync(hotelPhotos);

            #endregion

            //更新酒店信息
            var hopHotel = hotelInfo.HopHotel;
            if (hopHotel is not null)
            {
                //1、酒店名称直接同步HOP的
                if (!string.IsNullOrWhiteSpace(hopHotel.CnName))
                    hotel.ZHName = hopHotel.CnName;
                //2、英文名称HOP侧的字段如果是中文的不同步，如果是英文的直接同步。
                if (!string.IsNullOrWhiteSpace(hopHotel.EnName)
                    && Regex.IsMatch(hopHotel.EnName, "[\u4e00-\u9fa5]{1,}") is false)
                    hotel.ENName = hopHotel.EnName;
                hotel.StarLevel = (decimal)hopHotel.Star;//星级
                hotel.Telephone = hopHotel.Tel;//电话
                hotel.Telefax = hopHotel.Fax;//传真
                //国家 省份 城市信息
                if (hopHotel.CityCode > 0)
                {
                    var apiCityCodes = new int[] { hopHotel.CityCode };
                    var cityCodes = await _dbContext.ApiCities
                        .Where(x => x.ApiCityType == ApiCityType.Hop && apiCityCodes.Contains(x.ApiCityCode))
                        .Select(x => new { x.ApiCityCode, x.CityCode })
                        .ToListAsync();
                    var cityInfos = await _locationService.GetCityInfos(new GetCityInfoInput { CityCodes = cityCodes.Select(x => x.CityCode) });
                    var districtInfos = await _locationService.GetDistrictInfos(new int[] { hopHotel.DistrictCode });
                    var city = cityInfos.FirstOrDefault(c => c.ApiCities.Select(x => x.ApiCityCode).Contains(hopHotel.CityCode));
                    if (city != null)
                    {
                        hotel.CityCode = city.CityCode;
                        hotel.CityName = city.CityName;
                        hotel.ProvinceCode = city.ProvinceCode;
                        hotel.ProvinceName = city.ProvinceName;
                        hotel.CountryCode = city.CountryCode;
                        hotel.CountryName = city.CountryName;
                        hotel.Enabled = true;//城市编码匹配 上架
                    }
                    var district = districtInfos.FirstOrDefault(d => d.DistrictCode.Equals(hopHotel.DistrictCode));
                    if (district != null)
                    {
                        hotel.DistrictCode = district.DistrictCode;
                        hotel.DistrictName = district.DistrictName;
                    }
                }
                //地址
                if (!string.IsNullOrWhiteSpace(hopHotel.Address))
                    hotel.Address = hopHotel.Address;
                //坐标
                _ = double.TryParse(hopHotel?.Longitude, out double lon);
                _ = double.TryParse(hopHotel?.Latitude, out double lat);
                if (Math.Abs(lon) <= 180 && Math.Abs(lat) <= 90)
                {
                    hotel.SetLocation(lon, lat);
                    hotel.CoordinateType = CoordinateType.WGS84;
                    //中国内地城市 坐标系类型为GCJ02
                    if (hotel.CountryCode == 10)
                        hotel.CoordinateType = CoordinateType.GCJ02;
                }

                ///发单邮箱
                hotel.ReceiptEmail = hopHotel.ReceiptEmail;

                //4、预订需知，公共库字段长度小于HOP的才同步覆盖
                if (string.IsNullOrWhiteSpace(hotel.Intro)
                    || hotel.Intro.Length < hopHotel.Description?.Length)
                    hotel.Intro = HopContentFormat(hopHotel.Description);
                if (string.IsNullOrWhiteSpace(hotel.SurroundingFacilities)
                    || hotel.SurroundingFacilities.Length < hopHotel.Surroundings?.Length)
                    hotel.SurroundingFacilities = HopContentFormat(hopHotel.Surroundings);
                if (string.IsNullOrWhiteSpace(hotel.CheckinPolicy)
                    || hotel.CheckinPolicy.Length < hopHotel.HotelPolicies?.Length)
                    hotel.CheckinPolicy = HopContentFormat(hopHotel.HotelPolicies);
                if (string.IsNullOrWhiteSpace(hotel.ImportantNotices)
                   || hotel.ImportantNotices.Length < hopHotel.ImportantNotices?.Length)
                    hotel.ImportantNotices = HopContentFormat(hopHotel.ImportantNotices);
            }
        }
        else
        {//非Hop酒店房型同步
            var roomInfos = from hopRoom in hotelInfo.HotelRooms
                            let roomPhotos = hotelInfo.HotelPhotos.Where(x => x.HotelRoomId == hopRoom.Id)
                                .ToList()
                            select new
                            {
                                HotelId = hotelId,
                                HopId = hopId,
                                Room = _mapper.Map<HotelRoomDto>(hopRoom),
                                Photos = _mapper.Map<List<HotelPhotosDto>>(roomPhotos)
                            };
            syncHotelRooms.Rooms.AddRange(roomInfos.Select(r => r.Room));
            syncHotelRooms.Photos.AddRange(roomInfos.SelectMany(r => r.Photos));
        }
        await _dbContext.SaveChangesAsync();
        await SyncHotelInfos(hotelInfo, syncHotelRooms, originInfo);
        return true;
    }

    private async Task SyncHotelInfos(HotelInfoModel hotelInfo, SyncHotelRoomsInput syncHotelRooms, OriginInfo originInfo)
    {
        var hotel = hotelInfo.Hotel;
        SyncHotelInfosInput syncHotelInfoMessage = new()
        {
            Id = hotel.Id,
            OriginInfo = originInfo,
            ZHName = hotel.ZHName,
            ENName = hotel.ENName,
            CountryCode = hotel.CountryCode,
            CountryName = hotel.CountryName,
            ProvinceCode = hotel.ProvinceCode,
            ProvinceName = hotel.ProvinceName,
            CityCode = hotel.CityCode,
            CityName = hotel.CityName,
            Address = hotel.Address,
            ENAddress = hotel.ENAddress,
            CoordinateType = hotel.CoordinateType,
            Latitude = hotel.Location.Y,
            Longitude = hotel.Location.X,
            StarLevel = hotel.StarLevel,
            ImportantNotices = hotel.ImportantNotices,
            CheckinPolicy = hotel.CheckinPolicy,
            Intro = hotel.Intro,
            SurroundingFacilities = hotel.SurroundingFacilities,
            Telephone = hotel.Telephone,
            Telefax = hotel.Telefax,
            ReceiptEmail = hotel.ReceiptEmail,
            HopId = hotel.HopId,
            StaffTag = hotelInfo?.HopHotel?.StaffTag == 1,
            ReunionRoom = hotelInfo?.HopHotel?.ReunionRoom,
        };
        //同步酒店信息
        var syncHotelInfoUrl = _servicesAddress.Hotel_SyncHotelInfos();
        using HttpContent hotelInfoHttpContent = new StringContent(JsonConvert.SerializeObject(syncHotelInfoMessage), Encoding.UTF8, "application/json");
        await _httpClientFactory.InternalPostAsync(syncHotelInfoUrl, httpContent: hotelInfoHttpContent);

        //同步商户酒店房型信息
        var url = _servicesAddress.Hotel_SyncHotelRooms();
        using HttpContent httpContent = new StringContent(JsonConvert.SerializeObject(syncHotelRooms), Encoding.UTF8, "application/json");
        await _httpClientFactory.InternalPostAsync(url, httpContent: httpContent);
    }

    private async Task bytePlusItemAttributesUpdate(long hopId, List<HotelRoom> publishRooms, List<ApiHotelInfo> apiHotelInfos)
    {
        var hopOpenHotel = await _dbContext.HopOpenHotels.AsNoTracking()
                .FirstOrDefaultAsync(x => hopId.Equals(x.HopId));
        BytePlusItemSubCategory? itemSubCategory = null;
        var apiHotelTag = CreateMappingTag(hopOpenHotel.Tags);
        switch (apiHotelTag)
        {
            case ApiHotelTag.A:
                itemSubCategory = BytePlusItemSubCategory.AApiHotel;
                break;
            case ApiHotelTag.B:
                itemSubCategory = BytePlusItemSubCategory.BApiHotel;
                break;
            case ApiHotelTag.C:
                itemSubCategory = BytePlusItemSubCategory.CApiHotel;
                break;
            case ApiHotelTag.D:
                itemSubCategory = BytePlusItemSubCategory.DApiHotel;
                break;
            case ApiHotelTag.E:
                itemSubCategory = BytePlusItemSubCategory.EApiHotel;
                break;
            case ApiHotelTag.O:
                itemSubCategory = BytePlusItemSubCategory.OApiHotel;
                break;
            default:
                break;
        }

        foreach (var apiHotelInfo in apiHotelInfos)
        {
            var productItems = new List<BytePlusProductItemDto>();
            foreach (var item in publishRooms)
            {
                productItems.Add(new BytePlusProductItemDto
                {
                    Id = item.Id.ToString(),
                    ItemCategory = BytePlusItemCategory.Hotel,
                    ItemSubCategory = itemSubCategory ?? BytePlusItemSubCategory.ApiHotel,
                    ItemId = apiHotelInfo.Id,
                    ItemName = apiHotelInfo.ZHName,
                    ItemVariant = item.ZHName
                });
            }
            var message = new BytePlusItemAttributesUpdateMessage
            {
                TenantId = apiHotelInfo.TenantId,
                ProductItems = productItems
            };
            await _capPublisher.PublishAsync(CapTopics.Product.BytePlusItemAttributesUpdate, message);
        }
    }

    private static ApiHotelTag? CreateMappingTag(string? hopTags)
    {
        if (string.IsNullOrWhiteSpace(hopTags)) return null;
        var hopTagArrs = hopTags.Split(",", StringSplitOptions.RemoveEmptyEntries);
        int tags = 0;
        foreach (var item in hopTagArrs)
        {
            var sellHotelTag = (SellHotelTag)Enum.Parse(typeof(SellHotelTag), item);
            tags |= (int)MappingTag(sellHotelTag);
        }

        static ApiHotelTag MappingTag(SellHotelTag tag) => tag switch
        {
            SellHotelTag.O => ApiHotelTag.O,
            SellHotelTag.A => ApiHotelTag.A,
            SellHotelTag.B => ApiHotelTag.B,
            SellHotelTag.C => ApiHotelTag.C,
            SellHotelTag.D => ApiHotelTag.D,
            SellHotelTag.E => ApiHotelTag.E,
            _ => throw new NotImplementedException(),
        };

        if (tags.Equals(0))
            return null;

        return (ApiHotelTag)tags;
    }

    #region 私有类 方法

    /// <summary>
    /// 批量获取同步HOP酒店信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private async Task<DownloadHopHotelDataResult> BatchHopHotels(BatchHopHotelInput input)
    {
        Stopwatch stopwatch = Stopwatch.StartNew();
        stopwatch.Start();
        var hopHotelResponse = await GetHopHotelResponse(input);
        var hotelInfos = await BatchHotelInfos(hopHotelResponse.Hotels);

        if (hotelInfos is null || hotelInfos.Count == 0)
            return new DownloadHopHotelDataResult { MaxHopHid = input.MinHid, IsSuccessed = true, Message = "未查询到新增HOP酒店信息" };
        var hotels = hotelInfos.Select(h => h.Hotel);
        var hotelRooms = hotelInfos.SelectMany(r => r.HotelRooms);
        await _dbContext.AddRangeAsync(hotels);
        await _dbContext.AddRangeAsync(hotelRooms);
        await _dbContext.AddRangeAsync(hotels.Select(h => new ThirdHotelBind
        {
            SupplierApiType = SupplierApiType.Hop,
            ResourceHotelId = h.Id,
            ThirdHotelId = h.HopId.ToString()
        }));
        await _dbContext.AddRangeAsync(hotelRooms.Select(x => new ThirdHotelRoomBind
        {
            SupplierApiType = SupplierApiType.Hop,
            ResourceRoomId = x.Id,
            ThirdRoomId = x.HopRoomId.ToString()
        }));
        var hotelInfoPhotos = hotelInfos.SelectMany(p => p.HotelPhotos);
        var uploadResults = await UploadHotelPhotos(hotelInfoPhotos);

        var hotelPhotos = new List<HotelPhotos>();
        foreach (var hotelPhoto in hotelInfoPhotos)
        {
            var uploadResult = uploadResults.FirstOrDefault(x => x.SourceUrl == hotelPhoto.Path);
            if (uploadResult is null) continue;

            hotelPhoto.Path = uploadResult.Path;
            hotelPhotos.Add(hotelPhoto);
        }
        await _dbContext.AddRangeAsync(hotelPhotos);
        await _dbContext.AddRangeAsync(hotelInfos.SelectMany(f => f.HotelFacilities));
        var result = await _dbContext.SaveChangesAsync() > 0;
        //同步酒店信息
        foreach (var hotelInfo in hotelInfos)
        {
            await SyncHotelInfos(hotelInfo, new SyncHotelRoomsInput
            {
                HotelId = hotelInfo.Hotel.Id,
                Rooms = _mapper.Map<List<HotelRoomDto>>(hotelInfo.HotelRooms),
                Photos = _mapper.Map<List<HotelPhotosDto>>(hotelInfo.HotelPhotos)
            }, new OriginInfo(hotelInfo.Hotel.Intro, hotelInfo.Hotel.SurroundingFacilities, hotelInfo.Hotel.CheckinPolicy, hotelInfo.Hotel.ImportantNotices));
        }

        var maxHopHid = hotelInfos.Select(h => h.Hotel).Max(h => h.HopId);
        return new DownloadHopHotelDataResult
        {
            IsSuccessed = result,
            MaxHopHid = maxHopHid,
            HopHotelCount = hotelInfos.Count,
            Message = $"执行耗时:{stopwatch.ElapsedMilliseconds}ms."
        };
    }
    private async Task<HopHotelResponse> GetHopHotelResponse(BatchHopHotelInput input)
    {
        var requestUri = new Uri(_huiZhiHotelConfig.BaseUrl + _huiZhiHotelConfig.BatchHopHotelUrl);

        var client = _httpClientFactory.CreateClient();
        var request = JsonConvert.SerializeObject(input);
        HttpContent content = new StringContent(request, Encoding.UTF8, "application/json");
        //请求HOP酒店信息
        var response = await client.PostAsync(requestUri: requestUri, content: content);
        response.EnsureSuccessStatusCode();
        var hopHotelResponse = JsonConvert.DeserializeObject<HopHotelResponse>(await response.Content.ReadAsStringAsync());
        return hopHotelResponse;
    }

    private async Task<HopHotelResponse> GetHopHotelResponse(int hopHid)
    {
        //配置hop请求地址
        var requestUri = new Uri(_huiZhiHotelConfig.BaseUrl + _huiZhiHotelConfig.GetHopHotelUrl);

        var client = _httpClientFactory.CreateClient();
        var request = JsonConvert.SerializeObject(hopHid);
        HttpContent content = new StringContent(request, Encoding.UTF8, "application/json");
        //请求HOP酒店信息
        var response = await client.PostAsync(requestUri: requestUri, content: content);
        response.EnsureSuccessStatusCode();

        var hopHotelResponseStr = await response.Content.ReadAsStringAsync();
        //_logger.LogInformation("同步HOP酒店信息HopHotel:{hopHotel}", hopHotelResponseStr);
        var hopHotelResponse = JsonConvert.DeserializeObject<HopHotelResponse>(hopHotelResponseStr);
        return hopHotelResponse;
    }

    //获取最后更新时间酒店
    private async Task<HopHotelResponse> GetHotelResponse(GetHotelRequest request)
    {
        var requestUri = new Uri(_huiZhiHotelConfig.BaseUrl + _huiZhiHotelConfig.GetHotelUrl);

        var client = _httpClientFactory.CreateClient();
        HttpContent content = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
        //请求HOP酒店信息
        var response = await client.PostAsync(requestUri: requestUri, content: content);
        response.EnsureSuccessStatusCode();
        var hopHotelResponse = JsonConvert.DeserializeObject<HopHotelResponse>(await response.Content.ReadAsStringAsync());
        return hopHotelResponse;
    }
    record GetHotelRequest
    {
        public int? MinHid { get; set; }
        public int Count { get; set; }
        public int[]? HidList { get; set; }
        public DateTime? LastUpdateTime { get; set; }
    }

    class BatchHopHotelInput
    {
        /// <summary>
        /// 起始hopHid
        /// </summary>
        public int MinHid { get; set; }

        /// <summary>
        /// 获取数据条数
        /// </summary>
        public int Count { get; set; }
    }

    class BatchHotelInfo
    {
        /// <summary>
        /// 上次执行最大的hop hid
        /// </summary>
        public int MaxHopHid { get; set; }

        /// <summary>
        /// 是否执行中
        /// </summary>
        public bool IsProcessing { get; set; }
    }

    class HotelInfoModel
    {
        public Hotel Hotel { get; set; }

        public HopHotel? HopHotel { get; set; }

        public IEnumerable<HotelRoom> HotelRooms { get; set; }

        public IEnumerable<HotelFacilities> HotelFacilities { get; set; }

        public IEnumerable<HotelPhotos> HotelPhotos { get; set; }
    }

    private async Task<List<HotelInfoModel>> BatchHotelInfos(IEnumerable<HopHotel> hopHotels, bool valid = true)
    {
        List<HotelInfoModel> hotelInfoModels = new List<HotelInfoModel>();
        //酒店全部设施
        var facilities = await _dbContext.Facilities.AsQueryable().ToListAsync();
        foreach (var hopHotel in hopHotels)
        {
            _ = DateTime.TryParse(hopHotel.OpenTime, out DateTime openingDate);
            _ = DateTime.TryParse(hopHotel.DecorateTime, out DateTime decorateDate);
            _ = int.TryParse(hopHotel.Floors, out int floors);
            //验证酒店有效性
            if (valid is true)
            {
                if (VaildHopHotel(hopHotel) is false)
                    continue;
            }
            else
            {
                FixHopHotel(hopHotel);
            }

            var hotel = new Hotel
            {
                HopId = hopHotel.Hid,
                HotelType = GetHotelType(hopHotel.HotelCategory),
                ZHName = !string.IsNullOrWhiteSpace(hopHotel.CnName) ? hopHotel.CnName : hopHotel.EnName,
                ENName = hopHotel.EnName,
                CountryCode = hopHotel.CountryCode,
                ProvinceCode = hopHotel.ProvinceCode,
                CityCode = hopHotel.CityCode,
                DistrictCode = hopHotel.DistrictCode,
                Address = hopHotel.Address,
                CountryName = "",
                ProvinceName = "",
                CityName = "",
                DistrictName = "",
                StarLevel = (decimal)hopHotel.Star,
                OpeningDate = openingDate,
                DecorateDate = decorateDate > DateTime.MinValue ? decorateDate : null,
                Floors = floors,
                Rooms = hopHotel.RoomNum,
                Telephone = hopHotel.Tel,
                Telefax = hopHotel.Fax,
                ReceiptEmail = hopHotel.ReceiptEmail,
                Intro = HopContentFormat(hopHotel.Description),
                SurroundingFacilities = HopContentFormat(hopHotel.Surroundings),
                CheckinPolicy = HopContentFormat(hopHotel.HotelPolicies),
                ImportantNotices = HopContentFormat(hopHotel.ImportantNotices),
                Enabled = false,//默认下架
            };
            _ = double.TryParse(hopHotel?.Longitude, out double lon);
            _ = double.TryParse(hopHotel?.Latitude, out double lat);
            if (Math.Abs(lon) <= 180 && Math.Abs(lat) <= 90)
            {
                hotel.SetLocation(lon, lat);
                hotel.CoordinateType = CoordinateType.WGS84;
                //中国内地城市 坐标系类型为GCJ02
                if (hotel.CountryCode == 10)
                    hotel.CoordinateType = CoordinateType.GCJ02;
            }
            else
                hotel.SetLocation(0, 0);//默认经纬度

            //酒店图片
            var hotelPhotos = new List<HotelPhotos>();
            var imgs = $"{hopHotel.MainImgs},{hopHotel.ListImgs}".Split(',').Where(i => !string.IsNullOrWhiteSpace(i));
            foreach (var i in imgs)
            {
                hotelPhotos.Add(new HotelPhotos { HotelId = hotel.Id, Path = i, Enabled = true });
            }

            var rooms = GetHotelRooms(hotel.Id, hopHotel.Rooms, out List<HotelPhotos> hotelRoomPhotos);

            //酒店图片+房型图片
            if (hotelRoomPhotos != null && hotelRoomPhotos.Any()) hotelPhotos.AddRange(hotelRoomPhotos);

            hotelInfoModels.Add(new HotelInfoModel
            {
                Hotel = hotel,
                HotelRooms = rooms,
                HotelPhotos = hotelPhotos,
                HotelFacilities = GetHotelFacilities(hotel.Id, hopHotel, facilities),
                HopHotel = hopHotel
            });
        }
        //设置城市信息
        if (hotelInfoModels.Any())
        {
            var apiCityCodes = hotelInfoModels.Select(h => h.Hotel.CityCode).Distinct();
            var cityCodes = await _dbContext.ApiCities
                .Where(x => x.ApiCityType == ApiCityType.Hop && apiCityCodes.Contains(x.ApiCityCode))
                .Select(x => new { x.ApiCityCode, x.CityCode })
                .ToListAsync();
            var cityInfos = await _locationService.GetCityInfos(new GetCityInfoInput { CityCodes = cityCodes.Select(x => x.CityCode) });
            var districtInfos = await _locationService.GetDistrictInfos(hotelInfoModels.Select(h => h.Hotel.DistrictCode).Distinct());
            foreach (var hotelInfoModel in hotelInfoModels)
            {
                var hotel = hotelInfoModel.Hotel;
                var city = cityInfos.FirstOrDefault(c => c.ApiCities.Select(x => x.ApiCityCode).Contains(hotel.CityCode));
                if (city != null)
                {
                    hotel.CityCode = city.CityCode;
                    hotel.CityName = city.CityName;
                    hotel.ProvinceCode = city.ProvinceCode;
                    hotel.ProvinceName = city.ProvinceName;
                    hotel.CountryCode = city.CountryCode;
                    hotel.CountryName = city.CountryName;
                    hotel.Enabled = true;//城市编码匹配 上架
                }
                else //酒店城市匹配不上的，规类到其它--其它--其它中
                {
                    hotel.CityCode = 1;
                    hotel.CityName = "其他";
                    hotel.ProvinceCode = 1;
                    hotel.ProvinceName = "其他";
                    hotel.CountryCode = 1;
                    hotel.CountryName = "其他";
                    hotel.Enabled = true;//城市编码匹配 上架
                }
                var district = districtInfos.FirstOrDefault(d => d.DistrictCode.Equals(hotelInfoModel.Hotel.DistrictCode));
                if (district != null)
                {
                    hotel.DistrictCode = district.DistrictCode;
                    hotel.DistrictName = district.DistrictName;
                }
            }
        }
        return hotelInfoModels;
    }

    private bool VaildHopHotel(HopHotel hopHotel)
    {
        if (hopHotel is null) return false;
        if (hopHotel.CnName?.Length > 100)
        {
            _logger.LogInformation("验证HOP酒店有效性:hid:{hid},CnName超长", hopHotel.Hid);
            return false;
        }
        if (hopHotel.EnName?.Length > 100)
        {
            _logger.LogInformation("验证HOP酒店有效性:hid:{hid},EnName超长", hopHotel.Hid);
            return false;
        }
        if (hopHotel.Address?.Length > 200)
        {
            _logger.LogInformation("验证HOP酒店有效性:hid:{hid},Address超长", hopHotel.Hid);
            return false;
        }
        if (hopHotel.Description?.Length > 2000)
        {
            _logger.LogInformation("验证HOP酒店有效性:hid:{hid},Description超长", hopHotel.Hid);
            return false;
        }
        if (hopHotel.Surroundings?.Length > 500)
        {
            _logger.LogInformation("验证HOP酒店有效性:hid:{hid},Surroundings超长", hopHotel.Hid);
            return false;
        }
        if (hopHotel.HotelPolicies?.Length > 500)
        {
            _logger.LogInformation("验证HOP酒店有效性:hid:{hid},HotelPolicies超长", hopHotel.Hid);
            return false;
        }
        if (hopHotel.ImportantNotices?.Length > 500)
        {
            _logger.LogInformation("验证HOP酒店有效性:hid:{hid},ImportantNotices超长", hopHotel.Hid);
            return false;
        }
        return true;
    }
    private void FixHopHotel(HopHotel hopHotel)
    {
        if (hopHotel is null)
            return;

        if (hopHotel.CnName.Length > 100)
            hopHotel.CnName = hopHotel.CnName[..100];

        if (hopHotel.EnName.Length > 100)
            hopHotel.EnName = hopHotel.EnName[..100];

        if (hopHotel.Address.Length > 200)
            hopHotel.Address = hopHotel.Address[..200];

        if (hopHotel.Description.Length > 2000)
            hopHotel.Description = hopHotel.Description[..2000];

        if (hopHotel.Surroundings.Length > 500)
            hopHotel.Surroundings = hopHotel.Surroundings[..500];

        if (hopHotel.HotelPolicies.Length > 500)
            hopHotel.HotelPolicies = hopHotel.HotelPolicies[..500];

        if (hopHotel.ImportantNotices.Length > 500)
            hopHotel.ImportantNotices = hopHotel.ImportantNotices[..500];
    }

    static HotelType GetHotelType(string hotelCategory)
    {
        HotelType hotelType = HotelType.Hotel;
        switch (hotelCategory)
        {
            case string category when category.Contains("民宿"):
                hotelType = HotelType.Homestay;
                break;
            case string category when category.Contains("酒店式公寓"):
                hotelType = HotelType.HotelApartment;
                break;
            case string category when category.Contains("特色住宿"):
                hotelType = HotelType.FeaturedAccommodation;
                break;
            case string category when category.Contains("青旅"):
                hotelType = HotelType.YouthHotel;
                break;
            case string category when category.Contains("别墅"):
                hotelType = HotelType.VillaHotel;
                break;
        }
        return hotelType;
    }

    private List<HotelRoom> GetHotelRooms(long hotelId, IEnumerable<HopRoom> hopRooms, out List<HotelPhotos> hotelRoomPhotos)
    {
        List<HotelRoom> rooms = new();
        hotelRoomPhotos = new List<HotelPhotos>();
        if (hopRooms is null) return rooms;

        foreach (var room in hopRooms)
        {
            var cnName = room.CnName?.Length > 100 ? room.CnName[..100] : room.CnName;
            var enName = room.EnName?.Length > 100 ? room.EnName[..100] : room.EnName;
            var hotelRoom = new HotelRoom
            {
                HopRoomId = room.Rid,
                HotelId = hotelId,
                ZHName = cnName,
                ENName = enName,
                WindowType = room.WindowType == 0 ? WindowType.None : WindowType.Have,
                MaximumOccupancy = room.MaxOccupancy,
                RoomQuantity = room.RoomNum,
                BedType = room.BedJson,
                IsStandardRoom = room.IsStandardRoom,
            };
            if (!string.IsNullOrWhiteSpace(room.Floor))
            {
                var collection = Regex.Matches(room.Floor, @"[\d]+");
                if (collection.Any())
                {
                    if (int.TryParse(collection[0].Value, out int floor))
                    {
                        hotelRoom.FloorMin = floor;
                        hotelRoom.FloorMax = floor;
                    }
                    if (collection.Count > 1)
                    {
                        if (int.TryParse(collection[1].Value, out int floorMax))
                            hotelRoom.FloorMax = floorMax;
                    }
                }
            }
            if (!string.IsNullOrWhiteSpace(room.Area))
            {
                var collection = Regex.Matches(room.Area, @"[\d\.]+");
                if (collection.Any())
                {
                    if (decimal.TryParse(collection[0].Value, out decimal area))
                    {
                        hotelRoom.AreaMin = area;
                        hotelRoom.AreaMax = area;
                    }
                    if (collection.Count > 1)
                    {
                        if (decimal.TryParse(collection[1].Value, out decimal areaMax))
                            hotelRoom.AreaMax = areaMax;
                    }
                }
            }
            rooms.Add(hotelRoom);
            //房型图片
            if (!string.IsNullOrWhiteSpace(room.Pics))
            {
                var imgs = room.Pics.Split(',').Where(i => !string.IsNullOrWhiteSpace(i));
                foreach (var i in imgs)
                {
                    hotelRoomPhotos.Add(new HotelPhotos
                    {
                        HotelId = hotelId,
                        HotelRoomId = hotelRoom.Id,
                        Path = i,
                        Enabled = true
                    });
                }
            }
        }

        return rooms;
    }

    private List<HotelFacilities> GetHotelFacilities(long hotelId, HopHotel hopHotel, List<Facility> facilities)
    {
        List<HotelFacilities> hotelFacilities = new();
        //酒店设施
        hotelFacilities.AddRange(GetHotelFacilities(hotelId, hopHotel.HotelFacilities, FacilityType.Hotel, facilities));
        //酒店服务
        hotelFacilities.AddRange(GetHotelFacilities(hotelId, hopHotel.HotelService, FacilityType.HotelService, facilities));
        //房间设施
        hotelFacilities.AddRange(GetHotelFacilities(hotelId, hopHotel.RoomFacilities, FacilityType.Room, facilities));
        return hotelFacilities;
    }

    private List<HotelFacilities> GetHotelFacilities(long hotelId, string facilityStr, FacilityType facilityType, List<Facility> facilities)
    {
        var hotelFacilities = new List<HotelFacilities>();

        if (string.IsNullOrWhiteSpace(facilityStr)) return hotelFacilities;

        var facilityArr = facilityStr.Split(',').Where(f => !string.IsNullOrWhiteSpace(f));
        Facility? facility = null;
        foreach (var fac in facilityArr)
        {
            switch (fac)
            {
                case "残疾人设施":
                    facility = facilities.FirstOrDefault(f => f.FacilityType == facilityType && f.Name.Contains("无障碍设施"));
                    break;
                default:
                    facility = facilities.FirstOrDefault(f => f.FacilityType == facilityType && f.Name.Contains(fac));
                    break;
            }

            if (facility is null) continue;
            hotelFacilities.Add(new HotelFacilities
            {
                FacilityId = facility.Id,
                HotelId = hotelId
            });
        }
        return hotelFacilities;
    }

    /// <summary>
    /// 内容格式化
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    private static string HopContentFormat(string str)
    {
        //换行符 1<br>2</br>3< Br>4< br >5<br/>6<br / >7< / br>
        str = Regex.Replace(str, @"<[\sbBrR\/]+?>", "\n");
        //特殊
        str = str.Replace("$#$", "\n")
                 .Replace("&nbsp;", " ");
        //移除所有html标签
        str = Regex.Replace(str, @"<\/?.+?\/?>", "");

        return str;
    }

    private async Task<IEnumerable<ImageUploadResult>> UploadHotelPhotos(IEnumerable<HotelPhotos> hotelPhotos)
    {
        var options = new ParallelOptions
        {
            MaxDegreeOfParallelism = 5,
        };
        var result = new ConcurrentBag<ImageUploadResult>();
        await Parallel.ForEachAsync(hotelPhotos, options, async (hotelPhoto, cancellationToken) =>
        {
            try
            {
                //download
                var (suffix, mimeType, bytes) = await _fileResourceService.DownloadFile(
                    url: hotelPhoto.Path,
                    timeoutSeconds: 10,
                    cancellationToken: cancellationToken);
                //updload
                var path = $"hotel/{(hotelPhoto.HotelRoomId == 0 ? "h" : "r")}/{Guid.NewGuid():N}{suffix}";
                var uploadResult = _aliyunOssObject.Upload(path, bytes);
                if (uploadResult.AliyunResult.HttpStatusCode == HttpStatusCode.OK)
                    result.Add(new ImageUploadResult { SourceUrl = hotelPhoto.Path, Path = path });
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "image upload error, {url}", hotelPhoto.Path);
            }
        });
        return result;
    }

    #endregion

    public async Task<IEnumerable<CheckQuoteOutput>> CheckQuotes(CheckQuoteInput input)
    {
        var hopHids = input.HopHids.Distinct();
        var hopHotels = await _dbContext.Hotels
            .Where(x => hopHids.Contains(x.HopId))
            .Select(x => new { x.HopId })
            .ToListAsync();
        var result = hopHids.Select(x => new CheckQuoteOutput { HopHid = x, HasQuoted = hopHotels.Any(s => s.HopId == x) });
        return result;
    }

    public async Task<long> AddHopHotel(int hopHid)
    {
        var isExist = await _dbContext.Hotels.Where(x => x.HopId == hopHid).AnyAsync();
        if (isExist)
            throw new BusinessException(ErrorTypes.Resource.HotelIsExists);

        //获取hop酒店信息的最大hid
        var maxHopHid = await _dbContext.Hotels
            .OrderByDescending(h => h.HopId)
            .Select(h => h.HopId)
            .FirstOrDefaultAsync();
        if (maxHopHid > 0 && hopHid > maxHopHid)
            throw new BusinessException(ErrorTypes.Resource.HotelWaitForSync);//新添加的HOP酒店等待系统自动同步


        var hopHotelResponse = await GetHopHotelResponse(hopHid);
        var hopHotels = hopHotelResponse.Hotels;
        var hotelInfos = await BatchHotelInfos(hopHotelResponse.Hotels, false);

        var hotelInfo = hotelInfos.Where(x => x.Hotel.HopId == hopHid)?.FirstOrDefault();
        if (hotelInfo is null)
            throw new BusinessException(ErrorTypes.Resource.HopHotelNotExists);
        await _dbContext.AddAsync(hotelInfo.Hotel);
        await _dbContext.AddAsync(new ThirdHotelBind
        {
            SupplierApiType = SupplierApiType.Hop,
            ResourceHotelId = hotelInfo.Hotel.Id,
            ThirdHotelId = hotelInfo.Hotel.HopId.ToString()
        });
        await _dbContext.AddRangeAsync(hotelInfo.HotelRooms);
        await _dbContext.AddRangeAsync(hotelInfo.HotelRooms.Select(x => new ThirdHotelRoomBind
        {
            SupplierApiType = SupplierApiType.Hop,
            ResourceRoomId = x.Id,
            ThirdRoomId = x.HopRoomId.ToString()
        }));
        var uploadResults = await UploadHotelPhotos(hotelInfo.HotelPhotos);

        var hotelPhotos = new List<HotelPhotos>();
        foreach (var hotelPhoto in hotelInfo.HotelPhotos)
        {
            var uploadResult = uploadResults.FirstOrDefault(x => x.SourceUrl == hotelPhoto.Path);
            if (uploadResult is null) continue;

            hotelPhoto.Path = uploadResult.Path;
            hotelPhotos.Add(hotelPhoto);
        }

        await _dbContext.AddRangeAsync(hotelPhotos);
        await _dbContext.AddRangeAsync(hotelInfo.HotelFacilities);
        await _dbContext.SaveChangesAsync();

        //同步酒店信息
        await SyncHotelInfos(hotelInfo, new SyncHotelRoomsInput
        {
            HotelId = hotelInfo.Hotel.Id,
            Rooms = _mapper.Map<List<HotelRoomDto>>(hotelInfo.HotelRooms),
            Photos = _mapper.Map<List<HotelPhotosDto>>(hotelInfo.HotelPhotos)
        }, new OriginInfo(hotelInfo.Hotel.Intro, hotelInfo.Hotel.SurroundingFacilities, hotelInfo.Hotel.CheckinPolicy, hotelInfo.Hotel.ImportantNotices));

        return hotelInfo.Hotel.Id;
    }

    public async Task<PagingModel<SearchHopHotelOutput>> Search(SearchHopHotelInput input)
    {
        if (input.CityCode.HasValue)
        {
            var apiCity = await _dbContext.ApiCities
                .Where(x => x.ApiCityType == ApiCityType.Hop && x.CityCode == input.CityCode!.Value)
                .FirstOrDefaultAsync();
            input.CityCode = apiCity?.ApiCityCode;//转化为hop城市编码
        }
        var requestUri = new Uri(_huiZhiHotelConfig.BaseUrl + _huiZhiHotelConfig.SearchHopHotelUrl);
        var client = _httpClientFactory.CreateClient();
        HttpContent content = new StringContent(JsonConvert.SerializeObject(input), Encoding.UTF8, "application/json");
        var responseMessage = await client.PostAsync(requestUri: requestUri, content: content);
        responseMessage.EnsureSuccessStatusCode();
        var responseContent = await responseMessage.Content.ReadAsStringAsync();
        var response = JsonConvert.DeserializeObject<SearchHopHotelResponse>(responseContent);
        IEnumerable<SearchHopHotelOutput> hotels = response.Hotels.Select(x => new SearchHopHotelOutput
        {
            Hid = x.Hid,
            Name = x.Name,
            Star = x.Star,
            City = x.City,
            Country = x.Country,
            Address = x.Address,
        });
        var total = response.TotalCount;
        PagingModel<SearchHopHotelOutput> paging = new()
        {
            Data = hotels,
            PageIndex = input.PageIndex,
            PageSize = input.PageSize,
            Total = total
        };
        return paging;
    }

    public async Task<FillUpHopHotelOutput> FillUp(FillUpHopHotelInput input, CancellationToken cancellationToken = default)
    {
        var count = input.MaxCount;
        var query = from o in _dbContext.Set<HopOpenHotel>()
                    from t in _dbContext.Hotels.Where(x => x.HopId == o.HopId).DefaultIfEmpty()
                    where t == null
                    orderby o.HopId
                    select o.HopId;
        var HopIds = await query
            .WhereIF(input.MinHopId.HasValue, x => x > input.MinHopId!.Value)
            .Take(count)
            .ToListAsync();

        //循环添加到资源库
        var addCount = 0;
        foreach (var hopId in HopIds)
        {
            if (cancellationToken.IsCancellationRequested)
                break;

            try
            {
                await AddHopHotel(hopId);
                addCount++;
            }
            catch (TaskCanceledException) { }
            catch (BusinessException ex)
            {
                _logger.LogError(ex, "[Hop在售资源酒店补缺]HopId:{hopId},message:{message}", hopId, ex.Message);
            }
        }
        return new FillUpHopHotelOutput
        {
            Count = addCount,
            MaxHopId = HopIds.Count > 0 ? HopIds.Max() : input.MinHopId,
            IsFinished = HopIds.Count == 0 || HopIds.Count < count
        };
    }

    public async Task<SyncHotelInfoOutput> SyncHotelInfo(SyncHotelInfoInput input, CancellationToken cancellationToken = default)
    {
        var response = await GetHotelResponse(new GetHotelRequest
        {
            MinHid = input.MinHid,
            Count = input.Count,
            HidList = input.HidList,
            LastUpdateTime = input.LastUpdateTime,
        });
        var hopHotels = response.Hotels.ToList();
        var count = hopHotels.Count;
        var hopHids = hopHotels.Select(x => x.Hid).ToArray();
        var hotels = await _dbContext.Hotels
            .Where(x => hopHids.Contains(x.HopId))
            .ToListAsync();

        //同步酒店信息
        var searchApiHotelUrl = _servicesAddress.ApiHotel_SearchV2();
        var apiResourceHotelIds = hotels.Select(x => x.Id).ToList();
        var searchInput = new SearchInput
        {
            PageIndex = 1,
            PageSize = apiResourceHotelIds.Count,
            ResourceHotelIds = apiResourceHotelIds
        };
        using HttpContent hotelInfoHttpContent = new StringContent(JsonConvert.SerializeObject(searchInput), Encoding.UTF8, "application/json");
        var apiHotelSearchOutput = await _httpClientFactory.InternalPostAsync<PagingModel<SearchOutput>>(searchApiHotelUrl, httpContent: hotelInfoHttpContent);
        var apiHotels = apiHotelSearchOutput.Data
            .Select(x => new ApiHotelInfo(x.Id, x.ResourceHotelId, x.TenantId, x.ZHName))
            .ToList();

        foreach (var hotel in hotels)
        {
            var hopHotel = hopHotels.FirstOrDefault(x => x.Hid == hotel.HopId);
            try
            {
                apiHotels = apiHotels.Where(x => x.ResourceHotelId.Equals(hotel.Id)).ToList();
                var output = await UpdateHotel(hotel, hopHotel, apiHotels);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[资源酒店同步]hotelId:{hotelId},message:{message}", hotel.Id, ex.Message);
            }
        }
        return new SyncHotelInfoOutput
        {
            Count = count,
            MaxId = count > 0 ? hopHotels.Max(x => x.Hid) : 0,
            IsFinished = count < input.Count
        };
    }

    record ApiHotelInfo(long Id, long ResourceHotelId, long TenantId, string ZHName);
}
