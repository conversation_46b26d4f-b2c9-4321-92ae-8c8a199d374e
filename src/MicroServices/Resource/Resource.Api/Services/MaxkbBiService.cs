using AutoMapper;
using Contracts.Common.Resource.DTOs.MaxkbBi;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Resource.Api.Services.Interfaces;

namespace Resource.Api.Services;

public class MaxkbBiService : IMaxkbBiService
{
    private readonly ILogger _logger;
    private readonly IOptions<MaxkbBiConfig> _config;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IMapper _mapper;

    public MaxkbBiService(
        ILoggerFactory loggerFactory,
        IHttpClientFactory httpClientFactory,
        IMapper mapper,
        IOptions<MaxkbBiConfig> config)
    {
        _logger = loggerFactory.CreateLogger<MaxkbBiService>();
        _httpClientFactory = httpClientFactory;
        _config = config;
        _mapper = mapper;
    }

    public async Task<string> ChatMessageByPromt(ChatMessageByPromtInput input)
    {
        var chatId = (await ChatOpen()).Data;
        var promt = $"{input.Message}";
        var url = $"{_config.Value.BaseUrl.TrimEnd('/')}/api/application/chat_message/{chatId}";
        var requestBody = new
        {
            message = promt,
        };
        using var client = GetHttpClient();
        string jsonData = JsonConvert.SerializeObject(requestBody);
        StringContent bodyContent = new StringContent(jsonData, Encoding.UTF8, "application/json");
        HttpResponseMessage response = await client.PostAsync(url, bodyContent);
        response.EnsureSuccessStatusCode();
        // 读取响应内容
        var respString = await response.Content.ReadAsStringAsync();
        var list = respString.Split(new string[] { "data:" }, StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
        StringBuilder stringBuilder = new StringBuilder();
        foreach (var item in list)
        {
            var jobj = JObject.Parse(item);
            jobj.TryGetValue("content", out var content);
            if (content != null)
            {
                stringBuilder.Append(content.ToString());
            }
        }
        return stringBuilder.ToString();
    }

    /// <summary>
    /// 获取回话ID
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="HttpRequestException"></exception>
    private async Task<ChatOpenOutput> ChatOpen()
    {
        using var client = GetHttpClient();
        var url = $"{_config.Value.BaseUrl.TrimEnd('/')}/api/application/{_config.Value.ApplicationId}/chat/open";
        var response = await client.GetAsync(url);
        var responseString = await response.Content.ReadAsStringAsync();
        response.EnsureSuccessStatusCode();
        var resData = JsonConvert.DeserializeObject<ChatOpenOutput>(responseString);
        return resData?.Code == 200 ? resData : throw new HttpRequestException("Failed to open chat session.");
    }

    private HttpClient GetHttpClient()
    {
        var client = _httpClientFactory.CreateClient("MaxkbBi");
        client.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", $"{_config.Value.ApiKey}");
        return client;
    }

}
