using Common.GlobalException;
using Common.Utils;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Tenant.Enums;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Resource.Api.ConfigModel;
using Resource.Api.Services.Interfaces;
using Resource.Api.Services.RequestModel.ThirdPlatformHotel;
using Resource.Api.Services.ThirdPlatformHotel.HopOpenApi;
using System.Diagnostics;

namespace Resource.Api.Services.ThirdPlatformHotel;

public class HopOpenApiPlatformHotelService : IHopOpenApiPlatformHotelService
{
    private readonly HuiZhiHotelConfig _huiZhiHotelConfig;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<HopOpenApiPlatformHotelService> _logger;
    private readonly IThirdHotelPricestrategyService _thirdHotelPricestrategyService;

    public HopOpenApiPlatformHotelService(IOptions<HuiZhiHotelConfig> huiZhiHotelConfig,
        IHttpClientFactory httpClientFactory,
        ILogger<HopOpenApiPlatformHotelService> logger,
        IThirdHotelPricestrategyService thirdHotelPricestrategyService)
    {
        _huiZhiHotelConfig = huiZhiHotelConfig.Value;
        _httpClientFactory = httpClientFactory;
        _logger = logger;
        _thirdHotelPricestrategyService = thirdHotelPricestrategyService;
    }
    public SupplierApiType SupplierApiType => SupplierApiType.Hop;

    public SupplierApiInfo SupplierApiInfo { get; set; }

    public async Task<ThirdHotelPriceResponse> GetHotelPrices(ThirdHotelPricesRequest request, CancellationToken cancellationToken = default)
    {
        var thirdHotelPriceResponses = await GetHotelPrices(new ThirdHotelsPricesRequest
        {
            HotelIds = new string[] { request.HotelId },
            CheckIn = request.CheckIn,
            CheckOut = request.CheckOut,
            AdultNum = request.AdultNum,
            ChildrenAges = request.ChildrenAges,
            RoomNum = request.RoomNum,
            Nationality = request.Nationality,
            IsGroupBooking = request.IsGroupBooking,
            OnlyCachePrice = request.OnlyCachePrice
        }, cancellationToken);

        var result = thirdHotelPriceResponses.FirstOrDefault(s => s.HotelId == request.HotelId) ?? new ThirdHotelPriceResponse
        {
            HotelId = request.HotelId,
            Rooms = new List<ThirdHotelRoomDto>()
        };
        var thirdHotelPricestrategies = await _thirdHotelPricestrategyService.GetListAsync(new Contracts.Common.Resource.DTOs.ThirdHotelPricestrategy.GetThirdHotelPricestrategyInput
        {
            ResourceHotelId = request.ResourceHotelId,
            SupplierApiType = SupplierApiType.Hop,
        });
        if (thirdHotelPricestrategies.Count > 0)
        {
            foreach (var room in result.Rooms)
            {
                foreach (var item in room.PriceStrategies)
                {
                    var ps = thirdHotelPricestrategies.FirstOrDefault(s => s.PriceStrategyId == item.PricestrategyId);
                    if (ps is not null)
                    {
                        if (!string.IsNullOrWhiteSpace(ps.ZHName))
                            item.Name = ps.ZHName;
                        if (ps.ENName is not null)
                            item.EnName = ps.ENName;
                        if (item.Enabled)
                            item.Enabled = ps.Enabled;
                    }
                }
            }
        }
        return result;
    }

    public async Task<List<ThirdHotelPriceResponse>> GetHotelPrices(ThirdHotelsPricesRequest request, CancellationToken cancellationToken = default)
    {
        HotelSearchRequest hotelSearchRequest = new()
        {
            HotelIds = request.HotelIds,
            CheckIn = $"{request.CheckIn:yyyy-MM-dd}",
            CheckOut = $"{request.CheckOut:yyyy-MM-dd}",
            Adults = request.AdultNum ?? 2,
            ChildrenAges = request.ChildrenAges?.ToArray(),
            Rooms = request.RoomNum,
            Nationality = request.Nationality,
            IsReunionRoom = request.IsGroupBooking,
            OnlyCachePrice = request.OnlyCachePrice,
        };
        var response = await GetResponse<HotelSearchRequest, HotelSearchResponse>(hotelSearchRequest,
            _huiZhiHotelConfig.HotelSearchV3Url,
            default,
            cancellationToken);
        List<ThirdHotelPriceResponse> responses = new();
        foreach (var hotelId in request.HotelIds)
        {
            var hotel = response.Hotels?.FirstOrDefault(x => x.HotelId == hotelId);
            ThirdHotelPriceResponse thirdHotelPriceResponse = new()
            {
                HotelId = hotel?.HotelId ?? hotelId,
            };
            if (hotel?.Rooms?.Length is > 0)
            {
                thirdHotelPriceResponse.Rooms = hotel?.Rooms?.Select(r => new ThirdHotelRoomDto
                {
                    RoomId = r.RoomId,
                    PriceStrategies = r.Rates.Select(rate =>
                    {
                        var (Name, ENName) = GenerateRateplanName(new GenerateRateplanNameInput
                        {
                            Adults = rate.Adults,
                            ChildrenAges = rate.ChildrenAges,
                            BoardCode = rate.BoardCode,
                            BoardCount = rate.BoardCount,
                        });
                        var thirdPricestrategyDto = new ThirdPricestrategyDto
                        {
                            PricestrategyId = rate.RatePlanCode,
                            Name = Name,
                            EnName = ENName,
                            Enabled = true,
                            CostCurrencyCode = response.Currency,
                            IsAutoConfirm = rate.IsInstantConfirm,
                            ConfirmByMins = rate.ConfirmByMins,
                            IsDirect = rate.SaleScenario?.ChannelType is HopOpenApi.ChannelType.DirectPurchase or HopOpenApi.ChannelType.ChannelManager,
                            PriceStrategyType = rate.SaleScenario?.SaleType == SaleType.ReunionRoom ? PriceStrategyType.GroupRoom : PriceStrategyType.StandardRoom,
                            BookingHoursInAdvance = rate.SaleScenario?.MinAdvHours ?? 0,
                            BoardCodeType = rate?.BoardCode switch
                            {
                                HopOpenApi.BoardCodeType.RO => Contracts.Common.Resource.Enums.BoardCodeType.RO,
                                HopOpenApi.BoardCodeType.BB => Contracts.Common.Resource.Enums.BoardCodeType.BB,
                                HopOpenApi.BoardCodeType.HB => Contracts.Common.Resource.Enums.BoardCodeType.HB,
                                HopOpenApi.BoardCodeType.FB => Contracts.Common.Resource.Enums.BoardCodeType.FB,
                                HopOpenApi.BoardCodeType.BL => Contracts.Common.Resource.Enums.BoardCodeType.BL,
                                HopOpenApi.BoardCodeType.BD => Contracts.Common.Resource.Enums.BoardCodeType.BD,
                                HopOpenApi.BoardCodeType.HBBL => Contracts.Common.Resource.Enums.BoardCodeType.HBBL,
                                HopOpenApi.BoardCodeType.HBLD => Contracts.Common.Resource.Enums.BoardCodeType.HBLD,
                                HopOpenApi.BoardCodeType.AI => Contracts.Common.Resource.Enums.BoardCodeType.AI,
                                _ => Contracts.Common.Resource.Enums.BoardCodeType.RO,
                            },
                            BoardCount = rate?.BoardCount ?? 0,
                            LimitNumberOfNights = rate.SaleScenario?.MaxDays,
                            MaxOccupancy = rate.Adults,
                            BookingBenefits = rate.SaleScenario.Premium,
                            ChannelType = rate.SaleScenario.ChannelType switch
                            {
                                HopOpenApi.ChannelType.None => ApiHotelChannelType.None,
                                HopOpenApi.ChannelType.DirectPurchase => ApiHotelChannelType.DirectPurchase,
                                HopOpenApi.ChannelType.EBooking => ApiHotelChannelType.EBooking,
                                HopOpenApi.ChannelType.ChannelManager => ApiHotelChannelType.ChannelManager,
                                HopOpenApi.ChannelType.DirectConnect => ApiHotelChannelType.DirectConnect,
                                _ => ApiHotelChannelType.None,
                            },
                            SaleType = rate.SaleScenario.SaleType switch
                            {
                                SaleType.CalendarRoom => ApiHotelSaleType.CalendarRoom,
                                SaleType.BusinessTravel => ApiHotelSaleType.BusinessTravel,
                                SaleType.Gaode => ApiHotelSaleType.Gaode,
                                SaleType.ReunionRoom => ApiHotelSaleType.ReunionRoom,
                                SaleType.DirectSales => ApiHotelSaleType.DirectSales,
                                _ => null,
                            },
                            Tag = rate.SaleScenario.SensitivityLevel.HasValue ? (SellHotelTag)rate.SaleScenario.SensitivityLevel.Value : null,
                            NumberOfNights = rate.SaleScenario.MinDays,
                            NumberOfRooms = rate.SaleScenario.MinReunionRooms,
                            CalendarPrices = rate.DailyPrices.Select(s => new ThirdPricestrategyCalendarPriceDto
                            {
                                Date = s.Date,
                                CostPrice = s.Price,
                                Enabled = s.Status == 1,
                                OverSaleable = s.IsOversell == 1,
                                Quantity = s.Allotment,
                            }).ToList(),
                            TotalPrice = rate.TotalPrice,
                            NationalityCodes = rate.NationalityRule == 1 ? rate.NationalityCodes : null,
                            NonNationalityCodes = rate.NationalityRule == 2 ? rate.NationalityCodes : null,
                        };
                        if (rate?.CancellationPolicies?.Length is > 0)
                        {
                            List<ThirdPriceStrategyCancelRuleDto> cancelRuleDtos = new();
                            var cancellationPolicies = rate.CancellationPolicies
                                .Where(s => s.From > DateTime.Now)
                                .OrderBy(s => s.From)
                                .ToArray();
                            foreach (var policy in cancellationPolicies)
                            {
                                ThirdPriceStrategyCancelRuleDto ruleDto = new() { CancelRulesType = CancelRulesType.CannotCancel };
                                if (policy.From > DateTime.Now)
                                {
                                    ruleDto.CancelRulesType = CancelRulesType.LimitedTimeCancel;
                                    ruleDto.BeforeCheckInDays = response.CheckIn.Subtract(policy.From.Date).Days;
                                    ruleDto.BeforeCheckInTime = policy.From.TimeOfDay;
                                    ruleDto.CancelChargeType = CancelChargeType.Order;
                                    ruleDto.ChargeValue = (int)Math.Ceiling(policy.Amount / rate.TotalPrice * 100);
                                    if (policy.From.Date == response.CheckIn.Date)
                                    {
                                        ruleDto.CheckInDateTime = new TimeSpan(23, 59, 59);//入住当日 - 时间 如23:59:59
                                    }
                                }

                                cancelRuleDtos.Add(ruleDto);
                            }
                            //如果有多条取消政策，取最早的一条作为默认取消政策 全额取消
                            if (cancelRuleDtos.Count > 0)
                            {
                                var cancelRuleDto = cancelRuleDtos.OrderByDescending(s => s.BeforeCheckInDays)
                                     .ThenByDescending(s => s.BeforeCheckInTime)
                                     .FirstOrDefault();
                                cancelRuleDto.ChargeValue = 100;
                                cancelRuleDtos = new List<ThirdPriceStrategyCancelRuleDto>() { cancelRuleDto };
                            }
                            thirdPricestrategyDto.CancelRules = cancelRuleDtos;
                        }
                        return thirdPricestrategyDto;
                    }).ToList(),
                }).ToList();
            }
            responses.Add(thirdHotelPriceResponse);
        }
        return responses;
    }

    public async Task<CheckAvailabilityResponse> CheckAvailability(CheckAvailabilityRequest request)
    {
        PreCheckRequest preCheckRequest = new()
        {
            Adults = request.AdultNum,
            ChildrenAges = request.ChildrenAges?.ToArray(),
            CheckIn = $"{request.CheckIn:yyyy-MM-dd}",
            CheckOut = $"{request.CheckOut:yyyy-MM-dd}",
            HotelId = request.HotelId,
            RatePlanCode = request.PricestrategyId,
            RoomId = request.RoomId,
            Rooms = request.RoomNum,
            TotalPrice = request.TotalPrice.HasValue ? request.TotalPrice : (request.CalendarPrices.Sum(s => s.CostPrice) * request.RoomNum),
            Nationality = request.Nationality,
        };
        var response = await GetResponse<PreCheckRequest, PreCheckResponse>(preCheckRequest,
            _huiZhiHotelConfig.PreCheckV3Url);
        if (response.IsSuccessed is not true)
        {
            return new CheckAvailabilityResponse
            {
                CheckCode = response.Code switch
                {
                    1001 => CheckPriceStrategySaleCode.CalendarNotEnable,
                    200 => CheckPriceStrategySaleCode.Success,
                    _ => CheckPriceStrategySaleCode.PriceStrategyInvalid,
                },
                Msg = response.Message,
            };
        }
        var rate = response.Hotel.Room.Rate;
        var (Name, ENName) = GenerateRateplanName(new GenerateRateplanNameInput
        {
            Adults = rate.Adults,
            ChildrenAges = rate.ChildrenAges,
            BoardCode = rate.BoardCode,
            BoardCount = rate.BoardCount,
        });
        CheckAvailabilityResponse checkAvailabilityResponse = new()
        {
            CheckCode = response.Code switch
            {
                1001 => CheckPriceStrategySaleCode.CalendarNotEnable,
                200 => CheckPriceStrategySaleCode.Success,
                _ => CheckPriceStrategySaleCode.PriceStrategyInvalid,
            },
            BookingCode = response.BookingCode,
            PricestrategyId = rate.RatePlanCode,
            Name = Name,
            ENName = ENName,
            IsAutoConfirm = rate.IsInstantConfirm,
            ConfirmByMins = rate.ConfirmByMins,
            BoardCodeType = rate?.BoardCode switch
            {
                HopOpenApi.BoardCodeType.RO => Contracts.Common.Resource.Enums.BoardCodeType.RO,
                HopOpenApi.BoardCodeType.BB => Contracts.Common.Resource.Enums.BoardCodeType.BB,
                HopOpenApi.BoardCodeType.HB => Contracts.Common.Resource.Enums.BoardCodeType.HB,
                HopOpenApi.BoardCodeType.FB => Contracts.Common.Resource.Enums.BoardCodeType.FB,
                HopOpenApi.BoardCodeType.BL => Contracts.Common.Resource.Enums.BoardCodeType.BL,
                HopOpenApi.BoardCodeType.BD => Contracts.Common.Resource.Enums.BoardCodeType.BD,
                HopOpenApi.BoardCodeType.HBBL => Contracts.Common.Resource.Enums.BoardCodeType.HBBL,
                HopOpenApi.BoardCodeType.HBLD => Contracts.Common.Resource.Enums.BoardCodeType.HBLD,
                HopOpenApi.BoardCodeType.AI => Contracts.Common.Resource.Enums.BoardCodeType.AI,
                _ => null,
            },
            BoardCount = rate?.BoardCount ?? 0,
            MaxOccupancy = rate?.Adults ?? 0,
            TotalPrice = rate.TotalPrice,
            TaxDescription = rate?.TaxDescription,
            ArrivalTaxFees = rate?.Fees?.Select(s =>
            {
                Enum.TryParse(s.Type, out HotelFeeType type);
                return new ArrivalTaxFeeDto
                {
                    Amount = s.Amount,
                    Currency = s.Currency,
                    IsMandatory = s.IsMandatory,
                    Type = type,
                };
            }).ToArray(),
            Msg = response.Message,
        };
        if (!string.IsNullOrWhiteSpace(response?.Currency))
        {
            response.Currency = response.Currency;
        }
        if (rate?.DailyPrices?.Length is > 0)
        {
            checkAvailabilityResponse.CalendarPrices = rate.DailyPrices
                .Select(s => new ThirdPricestrategyCalendarPriceDto
                {
                    Date = s.Date,
                    CostPrice = s.Price,
                    Enabled = s.Status == 1,
                    OverSaleable = s.IsOversell == 1,
                    Quantity = s.Allotment,
                }).ToList();
        }
        if (rate?.Fees?.Length is > 0)
        {
            checkAvailabilityResponse.ArrivalTaxFees = rate.Fees
                .Select(s => new ArrivalTaxFeeDto
                {
                    Amount = s.Amount,
                    Currency = s.Currency,
                    IsMandatory = s.IsMandatory,
                    Type = Enum.Parse<HotelFeeType>(s.Type),
                }).ToArray();
        }
        if (rate?.CancellationPolicies?.Length is > 0)
        {
            List<ThirdPriceStrategyCancelRuleDto> cancelRuleDtos = new();
            var cancellationPolicies = rate.CancellationPolicies
                .Where(s => s.From > DateTime.Now)
                .OrderBy(s => s.From)
                .ToArray();
            foreach (var policy in cancellationPolicies)
            {
                ThirdPriceStrategyCancelRuleDto ruleDto = new() { CancelRulesType = CancelRulesType.CannotCancel };
                if (policy.From > DateTime.Now)
                {
                    ruleDto.CancelRulesType = CancelRulesType.LimitedTimeCancel;
                    ruleDto.BeforeCheckInDays = response.CheckIn.Subtract(policy.From.Date).Days;
                    ruleDto.BeforeCheckInTime = policy.From.TimeOfDay;
                    ruleDto.CancelChargeType = CancelChargeType.Order;
                    ruleDto.ChargeValue = (int)Math.Ceiling(policy.Amount / rate.TotalPrice * 100);
                    if (policy.From.Date == response.CheckIn.Date)
                    {
                        ruleDto.CheckInDateTime = new TimeSpan(23, 59, 59);//入住当日 - 时间 如23:59:59
                    }
                }
                cancelRuleDtos.Add(ruleDto);
            }
            //如果有多条取消政策，取最早的一条作为默认取消政策 全额取消
            if (cancelRuleDtos.Count > 0)
            {
                var cancelRuleDto = cancelRuleDtos.OrderByDescending(s => s.BeforeCheckInDays)
                     .ThenByDescending(s => s.BeforeCheckInTime)
                     .FirstOrDefault();
                cancelRuleDto.ChargeValue = 100;
                cancelRuleDtos = new List<ThirdPriceStrategyCancelRuleDto>() { cancelRuleDto };
            }
            checkAvailabilityResponse.CancelRules = cancelRuleDtos;
        }
        var ps = (await _thirdHotelPricestrategyService.GetListAsync(new Contracts.Common.Resource.DTOs.ThirdHotelPricestrategy.GetThirdHotelPricestrategyInput
        {
            ResourceHotelId = request.ResourceHotelId,
            SupplierApiType = SupplierApiType.Hop,
            PricestrategyId = request.PricestrategyId,
        })).FirstOrDefault();
        if (ps is not null)
        {
            if (!string.IsNullOrWhiteSpace(ps.ZHName))
                checkAvailabilityResponse.Name = ps.ZHName;
            if (ps.ENName is not null)
                checkAvailabilityResponse.ENName = ps.ENName;
            if (ps.Enabled is not true)
                checkAvailabilityResponse.CheckCode = CheckPriceStrategySaleCode.PriceStrategyInvalid;
        }

        return checkAvailabilityResponse;
    }

    public async Task<CreateOrderResponse> CreateOrder(CreateOrderRequest request)
    {
        BookingRequest bookingRequest = new()
        {
            AgentOrderId = request.BaseOrderId.ToString(),
            TotalPrice = request.CalendarPrices.Sum(s => s.CostPrice) * request.RoomCount,
            Adults = request.Adults > 0 ? request.Adults : 1,
            ChildrenAges = request.ChildrenAges?.Select(s => s.ToString()).ToArray(),
            BookingCode = request.BookingCode,
            Guests = request.Guests?.Where(s => s.HotelOrderGuestType == Contracts.Common.Order.Enums.HotelOrderGuestType.Adult).Select(s => new BookingGuest { FirstName = s.FirstName, LastName = s.LastName }).ToArray(),
            CheckIn = $"{request.CheckIn:yyyy-MM-dd}",
            CheckOut = $"{request.CheckOut:yyyy-MM-dd}",
            HotelId = request.HotelId,
            RoomId = request.RoomId,
            RatePlanCode = request.PricestrategyId,
            ContactEmail = request.ContactsEmail,
            ContactName = request.ContactsName,
            ContactPhone = request.ContactsPhoneNumber,
            Nationality = request.Nationality,
            Rooms = request.RoomCount,
            DailyPrices = request.CalendarPrices.Select(s => new BookingDailyPrice
            {
                Date = $"{s.Date:yyyy-MM-dd}",
                Price = s.CostPrice,
            }).ToArray(),
            Currency = request.CurrencyCode,
            SpecialRemarks = request.SpecialRemarks,
            SpecialRequests = request.Message,
            CommissionRate = request.CommissionRate,
        };
        if (request.UnionOrderId.HasValue)
        {
            if (request.Guests?.Any() is not true)
            {
                //团房没有入住人信息，则默认每间房添加占位符TBA TBA 
                //Each booked room must have at least one guest(422)
                List<BookingGuest> bookingGuests = new();
                foreach (var i in Enumerable.Range(0, request.RoomCount))
                {
                    bookingGuests.Add(new BookingGuest { FirstName = "TBA", LastName = "TBA" });
                }
                bookingRequest.Guests = bookingGuests.ToArray();
            }
            bookingRequest.AgentOrderId = $"{request.UnionOrderId},{request.UnionOrderCount},{request.BaseOrderId}";
            bookingRequest.IsReunionRoom = true;//团房
            bookingRequest.AdditionalPrices = request.AdditionalItems?.Select(x => new AdditionalPrice
            {
                CheckIn = $"{request.CheckIn:yyyy-MM-dd}",
                CheckOut = $"{request.CheckOut:yyyy-MM-dd}",
                Optional = x.Optional,
                Price = x.Price,
                Cost = x.Cost,
                Num = x.Num,
                Remarks = x.Remarks
            }).ToArray();
            if (bookingRequest.AdditionalPrices?.Length is > 0)
            {
                bookingRequest.TotalPrice += bookingRequest.AdditionalPrices.Sum(s => s.Cost * s.Num);
            }
        }
        var response = await GetResponse<BookingRequest, BookingResponse>(bookingRequest,
            _huiZhiHotelConfig.BookingV3Url, timeout: TimeSpan.FromMinutes(2));
        CreateOrderResponse createOrderResponse = new()
        {
            Msg = !string.IsNullOrWhiteSpace(response.Message) ? response.Message : null,
            Status = response.OrderStatus switch
            {
                1 => SupplierApiOrderStatus.Confirmed,
                2 => SupplierApiOrderStatus.WaitForConfirm,
                3 => SupplierApiOrderStatus.WaitForConfirm,
                4 => SupplierApiOrderStatus.WaitForPay,
                6 => SupplierApiOrderStatus.Closed,
                10 => SupplierApiOrderStatus.Refunded,
                11 => SupplierApiOrderStatus.Refunded,
                13 => SupplierApiOrderStatus.Refused,
                _ => SupplierApiOrderStatus.WaitForOrder
            },
            SupplierOrderId = response.OrderId,
        };
        /*code	描述
        1001	报价已无效，请获取最新报价
        1002	存在未付款账单，账号已冻结
        1003	额度不足
        1004	订单已存在*/
        if (response.Code == 1001)
        {
            createOrderResponse.Msg ??= $"报价已无效，请获取最新报价";
            createOrderResponse.Status = SupplierApiOrderStatus.OrderError;
        }
        else if (response.Code == 1002)
        {
            createOrderResponse.Msg ??= $"存在未付款账单，账号已冻结";
            createOrderResponse.Status = SupplierApiOrderStatus.OrderError;
        }
        else if (response.Code == 1003)
        {
            createOrderResponse.Msg ??= $"创建订单失败,额度不足";
            createOrderResponse.Status = SupplierApiOrderStatus.OrderError;
        }
        else if (response.Code == 1004)
        {
            //createOrderResponse.Msg ??= $"创建订单失败,订单已存在";
            //createOrderResponse.Status = SupplierApiOrderStatus.OrderError;
            //1004 - Order already exists 查询订单详情，返回采购单号 订单状态
            var queryOrderDetail = await QueryOrderDetail(new QueryOrderDetailRequest
            {
                BaseOrderId = request.BaseOrderId,
            });
            createOrderResponse.Status = queryOrderDetail.Status;
            createOrderResponse.SupplierOrderId = queryOrderDetail.SupplierOrderId;
        }
        return createOrderResponse;
    }

    public async Task<QueryOrderDetailResponse> QueryOrderDetail(QueryOrderDetailRequest request)
    {
        SearchBookingRequest searchBookingRequest = new()
        {
            OrderId = request.SupplierOrderId,
            AgentOrderId = request.BaseOrderId.ToString(),
        };
        var response = await GetResponse<SearchBookingRequest, SearchBookingResponse>(searchBookingRequest,
            _huiZhiHotelConfig.SearchBookingV3Url);

        if (response.Code == 1005)
            throw new Exception($"[汇智酒店]订单不存在,{response.Message}({response.Code})");

        QueryOrderDetailResponse queryOrderDetailResponse = new()
        {
            HotelId = response.HotelId,
            SupplierOrderId = response.OrderId,
            Status = response.OrderStatus switch
            {
                1 => SupplierApiOrderStatus.Confirmed,
                2 => SupplierApiOrderStatus.WaitForConfirm,
                3 => SupplierApiOrderStatus.WaitForConfirm,
                4 => SupplierApiOrderStatus.WaitForPay,
                5 => SupplierApiOrderStatus.Refused,
                6 => SupplierApiOrderStatus.Closed,
                10 => SupplierApiOrderStatus.Refunded,
                11 => SupplierApiOrderStatus.Refunded,
                13 => SupplierApiOrderStatus.Refused,
                _ => throw new NotImplementedException()
            },
            PricestrategyId = response.RatePlanCode,
            RoomCount = response.Rooms,
            TotalPrice = response.TotalPrice,
            RoomId = response.RoomId,
            CheckIn = response.CheckIn,
            CheckOut = response.CheckOut,
            MaxOccupancy = response.Adults,
            BoardCodeType = response?.BoardCode switch
            {
                HopOpenApi.BoardCodeType.RO => Contracts.Common.Resource.Enums.BoardCodeType.RO,
                HopOpenApi.BoardCodeType.BB => Contracts.Common.Resource.Enums.BoardCodeType.BB,
                HopOpenApi.BoardCodeType.HB => Contracts.Common.Resource.Enums.BoardCodeType.HB,
                HopOpenApi.BoardCodeType.FB => Contracts.Common.Resource.Enums.BoardCodeType.FB,
                HopOpenApi.BoardCodeType.BL => Contracts.Common.Resource.Enums.BoardCodeType.BL,
                HopOpenApi.BoardCodeType.BD => Contracts.Common.Resource.Enums.BoardCodeType.BD,
                HopOpenApi.BoardCodeType.HBBL => Contracts.Common.Resource.Enums.BoardCodeType.HBBL,
                HopOpenApi.BoardCodeType.HBLD => Contracts.Common.Resource.Enums.BoardCodeType.HBLD,
                HopOpenApi.BoardCodeType.AI => Contracts.Common.Resource.Enums.BoardCodeType.AI,
                _ => null,
            },
            BoardCount = response?.BoardCount ?? 0,
        };
        if (response.CancellationPolicies?.Length is > 0)
        {
            List<ThirdPriceStrategyCancelRuleDto> cancelRuleDtos = new();
            var cancellationPolicies = response.CancellationPolicies
                .Where(s => s.From > DateTime.Now)
                .OrderBy(s => s.From)
                .ToArray();
            foreach (var policy in cancellationPolicies)
            {
                ThirdPriceStrategyCancelRuleDto ruleDto = new() { CancelRulesType = CancelRulesType.CannotCancel };
                if (policy.From > DateTime.Now)
                {
                    ruleDto.CancelRulesType = CancelRulesType.LimitedTimeCancel;
                    ruleDto.BeforeCheckInDays = response.CheckIn.Subtract(policy.From.Date).Days;
                    ruleDto.BeforeCheckInTime = policy.From.TimeOfDay;
                    ruleDto.CancelChargeType = CancelChargeType.Order;
                    ruleDto.ChargeValue = (int)Math.Ceiling(policy.Amount / response.TotalPrice * 100);
                    if (policy.From.Date == response.CheckIn.Date)
                    {
                        ruleDto.CheckInDateTime = new TimeSpan(23, 59, 59);//入住当日 - 时间 如23:59:59
                    }
                }

                cancelRuleDtos.Add(ruleDto);
            }
            queryOrderDetailResponse.CancelRules = cancelRuleDtos;
        }
        queryOrderDetailResponse.ConfirmCode = response.ConfirmNo;
        List<OrderGuestDto> guests = response.Guests
            .Where(x => x.Name != "TBA TBA")//排除占位符入住人
            .Select(customername =>
            {
                //格式必须为：名在前，姓氏在后，中间用空格区分，例：zhang san
                var guestName = customername.Name ?? string.Empty;
                OrderGuestDto orderGuest = new()
                {
                    GuestName = guestName,
                };
                var index = guestName.IndexOf(' ');
                if (index > -1)
                {
                    orderGuest.LastName = guestName[..index];
                    orderGuest.FirstName = guestName[(index + 1)..];
                }
                return orderGuest;
            }).ToList();
        queryOrderDetailResponse.Guests = guests;
        return queryOrderDetailResponse;
    }

    public async Task<CancelOrderResponse> CancelOrder(CancelOrderRequest request)
    {
        CancelBookingRequest cancelBookingRequest = new()
        {
            OrderId = request.SupplierOrderId,
            AgentOrderId = request.BaseOrderId.ToString(),
        };
        var response = await GetResponse<CancelBookingRequest, CancelBookingResponse>(cancelBookingRequest,
            _huiZhiHotelConfig.CancelBookingV3Url);

        if (response.Code == 1005)
            throw new Exception($"[汇智酒店]订单不存在,{response.Message}({response.Code})");

        CancelOrderResponse cancelOrderResponse = new()
        {
            SupplierOrderId = response.OrderId,
            CancellationFees = response.CancellationFees,
            IsSuccessed = response.IsSuccessed,
            Msg = response.Message
        };
        return cancelOrderResponse;
    }

    #region api接口

    private async Task<TResult> GetResponse<TData, TResult>(TData request,
        string relativeUrl,
        TimeSpan? timeout = default,
        CancellationToken cancellationToken = default) where TResult : BaseResponse
    {
        var requestUri = new Uri(_huiZhiHotelConfig.BaseUrl + relativeUrl);
        var content = JsonConvert.SerializeObject(request);
        var stringContent = new StringContent(content, Encoding.UTF8, "application/json");
        var client = _httpClientFactory.CreateClient();
        if (timeout.HasValue)
            client.Timeout = timeout.Value;
        //client.DefaultRequestHeaders.AcceptEncoding.Add(new System.Net.Http.Headers.StringWithQualityHeaderValue("gzip"));
        var timestamp = new DateTimeOffset(DateTime.Now).ToUnixTimeSeconds();
        //签名方法：md5(md5(appKey + secretKey) + timestamp)md5采用32位小写。
        var sign = SecurityUtil.MD5Encrypt(SecurityUtil.MD5Encrypt(SupplierApiInfo.AppKey + SupplierApiInfo.AppSecret, Encoding.UTF8).ToLower() + timestamp, Encoding.UTF8).ToLower();
        var appKey = SupplierApiInfo.AppKey.ToString();
        client.DefaultRequestHeaders.Add("app-key", appKey);
        client.DefaultRequestHeaders.Add("timestamp", timestamp.ToString());
        client.DefaultRequestHeaders.Add("signature", sign);

        Stopwatch stopwatch = new();
        stopwatch.Start();
        var responseMessage = await client.PostAsync(requestUri, stringContent, cancellationToken);
        var response = await responseMessage.Content.ReadAsStringAsync();
        stopwatch.Stop();
        var elapsedMilliseconds = stopwatch.ElapsedMilliseconds;
        Serilog.Context.LogContext.PushProperty("Elapsed", elapsedMilliseconds);

        var message = "[汇智酒店]调用接口" + relativeUrl + ",Request:{request};Response:{response},app-key:{appKey},timestamp:{timestamp},signature:{sign}";
        _logger.LogInformation(message, content, response, appKey, timestamp, sign);

        responseMessage.EnsureSuccessStatusCode();
        var result = JsonConvert.DeserializeObject<TResult>(response);
        if (result.IsSuccessed is not true)
        {
            /*  401	认证失败
                403	账号状态不可用
                422	请求体参数错误
                429	接口限流
                500	未知错误，若问题一直存在，请联系我们解决
                503	服务器负载过高，请稍后重试您的请求*/
            //接口响应公共异常代码 抛出异常
            if (result.Code == 401 || result.Code == 403)
            {
                throw new UnauthorizedAccessException($"[汇智酒店]调用接口请求未成功,请检查AppKey和AppSecret是否正确,{result.Message}({result.Code})");
            }
            else if (result.Code == 422)
            {
                throw new Exception($"[汇智酒店]调用接口请求未成功,请求体参数错误,{result.Message}({result.Code})");
            }
            else if (result.Code == 429)
            {
                throw new Exception($"[汇智酒店]调用接口请求未成功,接口限流,{result.Message}({result.Code})");
            }
            else if (result.Code == 500 || result.Code == 503)
            {
                throw new Exception($"[汇智酒店]调用接口请求未成功,服务器负载过高，请稍后重试您的请求,{result.Message}({result.Code})");
            }
        }
        return result;
    }

    private static Request<T> CreateRequest<T>(string appKey, string appSecret, T data)
    {
        var timestamp = new DateTimeOffset(DateTime.Now).ToUnixTimeSeconds();
        //签名方法：md5(md5(appKey + secretKey) + timestamp)md5采用32位小写。
        var sign = SecurityUtil.MD5Encrypt(SecurityUtil.MD5Encrypt(appKey + appSecret, Encoding.UTF8).ToLower() + timestamp, Encoding.UTF8).ToLower();
        Request<T> request = new()
        {
            Head = new Head { AppKey = appKey, Sign = sign, Timestamp = timestamp },
            Data = data
        };
        return request;
    }

    #endregion

    /// <summary>
    /// 生成房价计划名称和英文名称
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private static (string Name, string ENName) GenerateRateplanName(GenerateRateplanNameInput input)
    {
        // 生成房价计划名称 X成人X儿童_餐型*几, 若无儿童列表书，则不显示；英文名称生成规则X Adult ­­_X Child _ Meal Type * X
        var name = $"{input.Adults}成人{(input.ChildrenAges?.Length is > 0 ? $"{input.ChildrenAges?.Length}儿童" : "")}";

        if (input.BoardCode.HasValue)
        {
            var boardCodeDescription = input.BoardCode switch
            {
                HopOpenApi.BoardCodeType.RO => "不含餐食",
                HopOpenApi.BoardCodeType.BB => "含早",
                HopOpenApi.BoardCodeType.HB => "半餐",
                HopOpenApi.BoardCodeType.FB => "全餐",
                HopOpenApi.BoardCodeType.BL => "午餐",
                HopOpenApi.BoardCodeType.BD => "晚餐",
                HopOpenApi.BoardCodeType.HBBL => "早餐+午餐",
                HopOpenApi.BoardCodeType.HBLD => "午餐+晚餐",
                HopOpenApi.BoardCodeType.AI => "全包",
                _ => ""
            };
            name += $"_{boardCodeDescription}{(input.BoardCount > 0 ? $"*{input.BoardCount}" : "")}";
        }
        var enName = $"{input.Adults} Adult{(input.ChildrenAges?.Length is > 0 ? $"_{input.ChildrenAges?.Length} Child" : "")}";
        if (input.BoardCode.HasValue)
        {
            var boardCodeDescription = input.BoardCode switch
            {
                HopOpenApi.BoardCodeType.RO => "Room Only",
                HopOpenApi.BoardCodeType.BB => "Breakfast",
                HopOpenApi.BoardCodeType.HB => "Half Board",
                HopOpenApi.BoardCodeType.FB => "Full Board",
                HopOpenApi.BoardCodeType.BL => "Lunch",
                HopOpenApi.BoardCodeType.BD => "Dinner",
                HopOpenApi.BoardCodeType.HBBL => "Breakfast and Lunch",
                HopOpenApi.BoardCodeType.HBLD => "Lunch and Dinner",
                HopOpenApi.BoardCodeType.AI => "All Inclusive",
                _ => ""
            };
            enName += $"_{boardCodeDescription}{(input.BoardCount > 0 ? $"*{input.BoardCount}" : "")}";
        }

        return (name, enName);
    }

    record GenerateRateplanNameInput
    {
        /// <summary>
        /// 成人数，每间房入住成人数
        /// </summary>
        public int Adults { get; set; }

        /// <summary>
        /// 餐食代码，详见餐食代码
        /// </summary>
        public HopOpenApi.BoardCodeType? BoardCode { get; set; }

        /// <summary>
        /// 餐食数量，餐食数量
        /// </summary>
        public int BoardCount { get; set; }

        /// <summary>
        /// 儿童年龄列表，每间房入住儿童的年龄列表，列表数量为儿童的入住人数
        /// </summary>
        public long[] ChildrenAges { get; set; }
    }

    public async Task<UpdateGuestsResponse> UpdateOrderGuests(UpdateGuestsRequest request, CancellationToken cancellationToken = default)
    {
        UpdateOrderGuestsRequest[] updateOrderGuestsRequest = request.OrderGuests
            .Select(x => new UpdateOrderGuestsRequest
            {
                Orderid = x.SupplierOrderId,
                Link = x.Link,
                Names = x.Guests.Select(g =>
                {
                    if (!string.IsNullOrWhiteSpace(g.LastName) && !string.IsNullOrWhiteSpace(g.FirstName))
                    {
                        return $"{g.LastName} {g.FirstName}";//汇智酒店：入住人姓名，格式必须为：姓氏在前，名在后，中间用空格区分，例：zhang san
                    }
                    else
                        return g.GuestName ?? string.Empty;
                }).ToArray()
            })
            .ToArray();


        var data = CreateRequest(SupplierApiInfo.AppKey, SupplierApiInfo.AppSecret, updateOrderGuestsRequest);

        var response = await GetResponse<Request<UpdateOrderGuestsRequest[]>, UpdateOrderGuestsResponse>(data,
            _huiZhiHotelConfig.UpdateOrderGuestsUrl);

        var result = new UpdateGuestsResponse
        {
            IsSuccessed = response.Code == 0,
            SupplierOrderIds = response.Result,
        };
        return result;
    }
}
