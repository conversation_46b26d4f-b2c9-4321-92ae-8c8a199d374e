using Contracts.Common.Resource.DTOs.HopHotel;
using Resource.Api.Services.Interfaces;
using Resource.Api.Services.RequestModel.ThirdPlatformHotel;
using Resource.Api.Services.ThirdPlatformHotel.HuiZhiHotel;

namespace Resource.Api.Services.ThirdPlatformHotel;

public interface IHuizhiPlatformHotelService : IThirdPlatformHotelService
{
    /// <summary>
    /// 【仅限汇智】更新酒店入住人信息
    /// </summary>
    /// <param name="requests"></param>
    /// <returns></returns>
    [Obsolete]
    Task<UpdateOrderGuestResponse> UpdateOrderGuests(params UpdateOrderGuestRequest[] requests);

    /// <summary>
    /// 获取国家城市列表
    /// </summary>
    /// <returns></returns>
    Task<GetCountryCityResult> GetCountryCities();

    /// <summary>
    /// 获取酒店列表
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    Task<GetHotelListResult> GetHotelList(GetHotelListData request);

    /// <summary>
    /// Selfsupport 获取酒店报价计划信息
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    Task<IEnumerable<SelfsupportGetRateplanResult>> GetSelfsupportGetRateplans(SelfsupportGetRateplansRequest request);

    /// <summary>
    /// Selfsupport 报价计划推送绑定/解绑
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    Task<SelfsupportRateplanbindResponse> SelfsupportRateplanbind(SelfsupportRateplanbindRequest request);

    /// <summary>
    /// Selfsupport 获取酒店价格
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    Task<SelfsupportGethotelpricesResponse> SelfsupportGethotelprices(SelfsupportGethotelpricesRequest request);

    /// <summary>
    /// Selfsupport 获取热卖酒店
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    Task<List<SelfsupportGetHotHotelResponse>> SelfsupportGetHotHotel(SelfsupportGetHotHotelRequest request);
}