using Resource.Api.Services.Interfaces;
using Resource.Api.Services.RequestModel.ThirdPlatformHotel;

namespace Resource.Api.Services.ThirdPlatformHotel;

public interface IHopOpenApiPlatformHotelService : IThirdPlatformHotelService
{

    Task<List<ThirdHotelPriceResponse>> GetHotelPrices(ThirdHotelsPricesRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// 上传团房客人姓名
    /// </summary>
    /// <param name="request"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<UpdateGuestsResponse> UpdateOrderGuests(UpdateGuestsRequest request, CancellationToken cancellationToken = default);
}
