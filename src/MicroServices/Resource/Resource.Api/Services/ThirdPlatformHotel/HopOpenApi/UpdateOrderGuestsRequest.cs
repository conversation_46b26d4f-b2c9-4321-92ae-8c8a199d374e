using Newtonsoft.Json;

namespace Resource.Api.Services.ThirdPlatformHotel.HopOpenApi;

public class UpdateOrderGuestsRequest
{
    // <summary>
    /// 链接
    /// </summary>
    [JsonProperty("link")]
    public string Link { get; set; }

    /// <summary>
    /// 客人姓名
    /// </summary>
    [JsonProperty("names")]
    public string[] Names { get; set; }

    /// <summary>
    /// HOP订单Id
    /// </summary>
    [JsonProperty("orderid")]
    public string Orderid { get; set; }
}
