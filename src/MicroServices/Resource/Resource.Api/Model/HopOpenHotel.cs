using EfCoreExtensions.EntityBase;

namespace Resource.Api.Model;

[Obsolete("功能移植到 Hotel.ApiHotel")]
public class HopOpenHotel : KeyBase
{
    [Obsolete("HopId is obsolete. Functionality has been migrated to Hotel.ApiHotelExtend.")]
    public int HopId { get; set; }

    /// <summary>
    /// 0-非主推  1-主推
    /// </summary>
    [Obsolete]
    public int StaffTag { get; set; }

    /// <summary>
    /// 团房数量
    /// </summary>
    [Obsolete]
    public int ReunionRoom { get; set; }

    /// <summary>
    /// 敏感度标签 英文逗号分隔
    /// </summary>
    [Obsolete]
    public string? Tags { get; set; }

    /// <summary>
    /// 是否在售
    /// </summary>
    [Obsolete]
    public bool SaleFlag { get; set; }

    [Obsolete]
    public DateTime CreateTime { get; set; } = DateTime.Now;
}
