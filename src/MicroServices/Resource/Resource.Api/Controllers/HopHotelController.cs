using Common.Swagger;
using Contracts.Common.Resource.DTOs;
using Contracts.Common.Resource.DTOs.HopHotel;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using Resource.Api.Services.Interfaces;

namespace Resource.Api.Controllers
{
    [Route("[controller]/[action]")]
    [ApiController]
    public class HopHotelController : ControllerBase
    {
        private readonly IHopHotelService _hopHotelService;
        private readonly ILogger<HopHotelController> _logger;

        public HopHotelController(IHopHotelService hopHotelService,
            ILogger<HopHotelController> logger)
        {
            _hopHotelService = hopHotelService;
            _logger = logger;
        }

        /// <summary>
        /// 批量更新hop酒店信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(DownloadHopHotelDataResult), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> BatchHotelInfo()
        {
            var result = await _hopHotelService.BatchHopHotelInfo();
            return Ok(result);
        }

        /// <summary>
        /// HOP酒店资源信息同步
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(SyncHotelInfoOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> SyncHotelInfo(SyncHotelInfoInput input)
        {
            var result = await _hopHotelService.SyncHotelInfo(input, HttpContext.RequestAborted);
            return Ok(result);
        }

        /// <summary>
        /// 更新HOP酒店信息
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(UpdateHotelOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> UpdateHotel(long hotelId)
        {
            var result = await _hopHotelService.UpdateHotel(hotelId);
            return Ok(result);
        }

        /// <summary>
        /// 根据hopHids检查hop酒店引用
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(IEnumerable<CheckQuoteOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> CheckQuotes(CheckQuoteInput input)
        {
            var result = await _hopHotelService.CheckQuotes(input);
            return Ok(result);
        }

        /// <summary>
        /// 添加HOP酒店
        /// </summary>
        /// <param name="input"></param>
        /// <returns>酒店id</returns>
        [HttpPost]
        [ProducesResponseType(typeof(long), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default,
            ErrorTypes.Resource.HotelIsExists,
            ErrorTypes.Resource.HotelWaitForSync,
            ErrorTypes.Resource.HopHotelNotExists)]
        public async Task<IActionResult> Add(AddHopHotelInput input)
        {
            var result = await _hopHotelService.AddHopHotel(input.HopHid);
            return Ok(result);
        }

        [HttpPost]
        [ProducesResponseType(typeof(PagingModel<SearchHopHotelOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> Search(SearchHopHotelInput input)
        {
            var result = await _hopHotelService.Search(input);
            return Ok(result);
        }

    }
}