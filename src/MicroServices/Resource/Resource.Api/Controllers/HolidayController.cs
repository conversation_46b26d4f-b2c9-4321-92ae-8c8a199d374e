using Contracts.Common.Resource.DTOs.Holiday;
using Microsoft.AspNetCore.Mvc;
using Resource.Api.Services.Interfaces;

namespace Resource.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class HolidayController : ControllerBase
{
    private readonly IHolidayService _holidayService;

    public HolidayController(IHolidayService holidayService)
    {
        _holidayService = holidayService;
    }

    [HttpPost]
    [ProducesResponseType(typeof(HolidayListOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> HolidayList(int[] years)
    {
        var result = await _holidayService.HolidayList(years);
        return Ok(result);
    }

    /// <summary>
    /// 同步holiday
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<IActionResult> SyncHolidayList()
    {
        var res = await _holidayService.SyncHolidayList();
        return Ok(res);
    }
}
