using Contracts.Common.Resource.DTOs.MaxkbBi;
using Microsoft.AspNetCore.Mvc;
using Resource.Api.Services.Interfaces;

namespace Resource.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class MaxkbBiController : ControllerBase
{
    private readonly IMaxkbBiService _maxkbBiService;
    public MaxkbBiController(IMaxkbBiService maxkbBiService)
    {
        _maxkbBiService = maxkbBiService;
    }

    [HttpPost]
    [ProducesResponseType(typeof(string), (int)System.Net.HttpStatusCode.OK)]
    public async Task<IActionResult> ChatMessageByPromt(ChatMessageByPromtInput input)
    {
        var result = await _maxkbBiService.ChatMessageByPromt(input);
        return Ok(result);
    }

}
