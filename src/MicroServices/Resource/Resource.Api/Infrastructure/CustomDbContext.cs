using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using System.Reflection;

namespace Resource.Api.Infrastructure;

public class CustomDbContext : DbContextBase
{
    public CustomDbContext(DbContextOptions dbContextOptions, ITenantIdentify tenantIdentify) : base(dbContextOptions, tenantIdentify)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
        base.OnModelCreating(modelBuilder);
    }

    public DbSet<City> Cities { get; set; }
    public DbSet<District> Districts { get; set; }
    public DbSet<Facility> Facilities { get; set; }
    public DbSet<Hotel> Hotels { get; set; }
    public DbSet<HotelFacilities> HotelFacilities { get; set; }
    public DbSet<HotelPhotos> HotelPhotos { get; set; }
    public DbSet<HotelRoom> HotelRooms { get; set; }
    public DbSet<HotelInquiryEmail> HotelInquiryEmails { get; set; }
    public DbSet<Province> Provinces { get; set; }

    public DbSet<ScenicSpot> ScenicSpots { get; set; }
    public DbSet<ScenicSpotPhotos> ScenicSpotPhotos { get; set; }
    public DbSet<Country> Countries { get; set; }
    public DbSet<ThirdHotelBind> ThirdHotelBinds { get; set; }
    public DbSet<ThirdHotelRoomBind> ThirdHotelRoomBinds { get; set; }
    public DbSet<TradingArea> TradingAreas { get; set; }
    public DbSet<ApiCity> ApiCities { get; set; }
    public DbSet<HopOpenHotel> HopOpenHotels { get; set; }
    public DbSet<PopularCity> PopularCities { get; set; }
    public DbSet<HotelExtend> HotelExtend { get; set; }
    public DbSet<HotelExtraBedPolicy> HotelExtraBedPolicy { get; set; }
    public DbSet<HotelMealChildPolicy> HotelMealChildPolicy { get; set; }
    public DbSet<HotelMealPolicy> HotelMealPolicy { get; set; }
    public DbSet<HotelPolicyExtend> HotelPolicyExtend { get; set; }
    public DbSet<HotelRoomExtraBedPolicy> HotelRoomExtraBedPolicy { get; set; }
    public DbSet<Airport> Airports { get; set; }

    public DbSet<HotelMeeting> HotelMeetings { get; set; }

    public DbSet<HotelPhotoExtend> HotelPhotoExtend { get; set; }

    public DbSet<HotelIntroRoom> HotelIntroRoom { get; set; }

    public DbSet<HotelIntroNearby> HotelIntroNearby { get; set; }

    public DbSet<HotelIntroFeature> HotelIntroFeature { get; set; }
    public DbSet<StandardHotelRoomCode> StandardHotelRoomCode { get; set; }
    public DbSet<StandardThirdHotelRoomCode> StandardThirdHotelRoomCode { get; set; }
    public DbSet<HotelGroupRecommend> HotelGroupRecommends { get; set; }
    public DbSet<GDSTag> GDSTags { get; set; }
    public DbSet<GDSHotel> GDSHotels { get; set; }
    public DbSet<GDSHotelTag> GDSHotelTags { get; set; }
    public DbSet<GDSHotelFacilities> GDSHotelFacilities { get; set; }
    public DbSet<GDSHotelPhotos> GDSHotelPhotos { get; set; }
    public DbSet<GDSHotelRoom> GDSHotelRoom { get; set; }
    public DbSet<GDSThirdHotelBind> GDSThirdHotelBind { get; set; }
    public DbSet<GDSHotelExtend> GDSHotelExtends { get; set; }
    public DbSet<ThirdHotelPricestrategy> ThirdHotelPricestrategies { get; set; }

    public DbSet<Holiday> Holidays { get; set; }
}
