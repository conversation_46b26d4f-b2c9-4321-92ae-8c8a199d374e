using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Resource.Api.Infrastructure.EntityConfigurations
{
    public class HolidayEntityTypeConfiguration : KeyBaseConfiguration<Holiday>, IEntityTypeConfiguration<Holiday>
    {
        public void Configure(EntityTypeBuilder<Holiday> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.Year)
                .HasColumnType("int");
            builder.Property(x => x.HolidayDetail)
               .HasColumnType("mediumtext");

            builder.Property(s => s.CreateTime)
               .HasColumnType("datetime");

            builder.HasIndex(s => s.Year);
        }
    }
}
