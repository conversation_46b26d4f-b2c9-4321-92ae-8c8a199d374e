using Common.GlobalException;
using Contracts.Common.Order.DTOs.ReceiptSettlementOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.Messages;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Tenant.DTOs.AgencyCreditCharge;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using Tenant.Api.Services.Interfaces;
using Hangfire;
using EfCoreExtensions.UOW;
using DotNetCore.CAP;
using Contracts.Common.Notify.Messages;
using Contracts.Common.Payment.Messages;
using Microsoft.Extensions.Options;
using Common.ServicesHttpClient;
using Newtonsoft.Json;
using Tenant.Api.Extensions;

namespace Tenant.Api.Services;

public class AgencyCreditChargeService : IAgencyCreditChargeService
{
    private readonly CustomDbContext _dbContext;
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly ICapPublisher _capPublisher;
    private readonly IOptions<ServicesAddress> _servicesAddress;
    private readonly IHttpClientFactory _httpClientFactory;

    public AgencyCreditChargeService(CustomDbContext dbContext,
        IBackgroundJobClient backgroundJobClient,
        ICapPublisher capPublisher,
        IOptions<ServicesAddress> servicesAddress,
        IHttpClientFactory httpClientFactory)
    {
        _dbContext = dbContext;
        _backgroundJobClient = backgroundJobClient;
        _capPublisher = capPublisher;
        _servicesAddress = servicesAddress;
        _httpClientFactory = httpClientFactory;
    }

    public async Task<PagingModel<AgencyCreditChargeSearchOutput>> Search(AgencyCreditChargeSearchInput input)
    {
        var query = Query(new AgencyCreditChargeQueryInput
        {
            AgencyId = input.AgencyId,
            BeginDate = input.BeginDate,
            EndDate = input.EndDate,
            ChargeStatus = input.ChargeStatus,
            PayType = input.PayType,
            PayChannel = input.PayChannel,
            ChargeFlowId = input.ChargeFlowId,
        });
        var result = await query
            .Select(x => new AgencyCreditChargeSearchOutput
            {
                Id = x.Id,
                AgencyId = x.AgencyId,
                AgencyName = x.AgencyName,
                PayType = x.PayType,
                PayChannel = x.PayChannel,
                Amount = x.Amount,
                CurrencyCode = x.CurrencyCode,
                AccountInfo = new TenantAccountInfoDto
                {
                    TenantBankAccountId = x.AccountInfo.TenantBankAccountId,
                    AccountName = x.AccountInfo.AccountName,
                    AccountNo = x.AccountInfo.AccountNo,
                    BankCode = x.AccountInfo.BankCode,
                    BankName = x.AccountInfo.BankName,
                    BranchName = x.AccountInfo.BranchName,
                    Proof = x.AccountInfo.Proof,
                },
                ChargeStatus = x.ChargeStatus,
                CreateTime = x.CreateTime,
                Operator = x.Operator,
                OperatorId = x.OperatorId,
                ConfirmTime = x.FinishTime,
                ExtNo = x.ExtNo,
                ReceiptSettlementOrderId = x.ReceiptSettlementOrderId,
                ChargeFlowId = x.ChargeFlowId,
            })
            .PagingAsync(input.PageIndex, input.PageSize);
        return result;
    }

    public async Task<List<AgencyCreditChargeSearchOutput>> ExportData(AgencyCreditChargeQueryInput input)
    {
        var result = await Query(input)
                        .Select(x => new AgencyCreditChargeSearchOutput
                        {
                            Id = x.Id,
                            AgencyId = x.AgencyId,
                            AgencyName = x.AgencyName,
                            PayType = x.PayType,
                            PayChannel = x.PayChannel,
                            Amount = x.Amount,
                            CurrencyCode = x.CurrencyCode,
                            AccountInfo = new TenantAccountInfoDto
                            {
                                TenantBankAccountId = x.AccountInfo.TenantBankAccountId,
                                AccountName = x.AccountInfo.AccountName,
                                AccountNo = x.AccountInfo.AccountNo,
                                BankCode = x.AccountInfo.BankCode,
                                BankName = x.AccountInfo.BankName,
                                BranchName = x.AccountInfo.BranchName,
                                Proof = x.AccountInfo.Proof,
                            },
                            ChargeStatus = x.ChargeStatus,
                            CreateTime = x.CreateTime,
                            Operator = x.Operator,
                            OperatorId = x.OperatorId,
                            ConfirmTime = x.FinishTime,
                            ExtNo = x.ExtNo,
                            ReceiptSettlementOrderId = x.ReceiptSettlementOrderId,
                            ChargeFlowId = x.ChargeFlowId,
                        })
                        .ToListAsync();
        return result;
    }

    private IOrderedQueryable<AgencyCreditCharge> Query(AgencyCreditChargeQueryInput input)
    {
        var query = _dbContext.AgencyCreditCharges
               .Where(x => x.ChargeType == AgencyChargeType.Receipt)//预收款类型充值
               .WhereIF(input.BeginDate.HasValue, x => x.CreateTime >= input.BeginDate!)
               .WhereIF(input.EndDate.HasValue, x => x.CreateTime <= input.EndDate!.Value.AddDays(1))
               .WhereIF(input.ChargeStatus.HasValue, x => x.ChargeStatus == input.ChargeStatus!)
               .WhereIF(input.AgencyId.HasValue, x => x.AgencyId == input.AgencyId!)
               .WhereIF(input.PayType.HasValue, x => x.PayType == input.PayType!)
               .WhereIF(input.PayChannel.HasValue, x => x.PayChannel == input.PayChannel!)
               .WhereIF(input.ChargeFlowId.HasValue, x => x.ChargeFlowId == input.ChargeFlowId!.Value)
               .OrderByDescending(x => x.CreateTime);
        return query;
    }

    [UnitOfWork]
    public async Task<long> Charge(AgencyCreditChargeInput input)
    {
        AgencyCreditCharge agencyCreditCharge = new()
        {
            AgencyId = input.AgencyId,
            AgencyName = input.AgencyName,
            Amount = input.Amount,
            CurrencyCode = input.CurrencyCode,
            PayType = input.PayType,
            PayChannel = input.PayChannel,
            AccountInfo = new TenantAccountInfo(),
            ChargeStatus = AgencyCreditChargeStatus.Pending,
            CreateTime = DateTime.Now,
            ChargeType = AgencyChargeType.Receipt
        };
        if (input.TenantAccountInfo is not null)
        {
            agencyCreditCharge.AccountInfo = new TenantAccountInfo
            {
                TenantBankAccountId = input.TenantAccountInfo.TenantBankAccountId,
                AccountName = input.TenantAccountInfo.AccountName,
                AccountNo = input.TenantAccountInfo.AccountNo,
                BankCode = input.TenantAccountInfo.BankCode,
                BankName = input.TenantAccountInfo.BankName,
                BranchName = input.TenantAccountInfo.BranchName,
                Proof = input.TenantAccountInfo.Proof,
            };
        }
        agencyCreditCharge.SetTenantId(input.TenantId);
        await _dbContext.AddAsync(agencyCreditCharge);

        if (agencyCreditCharge.PayType == PayType.Offline && agencyCreditCharge.ChargeStatus == AgencyCreditChargeStatus.Pending)
        {
            await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess, new NotifyMessageProcessMessage
            {
                TenantId = input.TenantId,
                SendToTheRole = Contracts.Common.Notify.Enums.SendToTheRole.TenantStaff,
                NotifyEventSubType = Contracts.Common.Notify.Enums.NotifyEventSubType.Financial_Charge,
                NotifyMode = Contracts.Common.Notify.Enums.NotifyMode.Wechat,
                Variables = new
                {
                    AgencyId = agencyCreditCharge.AgencyId,
                    PaymentAmount = agencyCreditCharge.Amount,
                    ReceiptTime = agencyCreditCharge.CreateTime,
                    ReceiptType = "线下转账（待确认）",
                    AgencyName = agencyCreditCharge.AgencyName
                }
            });
            await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess, new NotifyMessageProcessMessage
            {
                TenantId = input.TenantId,
                SendToTheRole = Contracts.Common.Notify.Enums.SendToTheRole.TenantStaff,
                NotifyEventSubType = Contracts.Common.Notify.Enums.NotifyEventSubType.Financial_Charge,
                NotifyMode = Contracts.Common.Notify.Enums.NotifyMode.DingTalkRobot,
                Variables = new
                {
                    AgencyId = agencyCreditCharge.AgencyId,
                    PaymentAmount = agencyCreditCharge.Amount,
                    ReceiptTime = agencyCreditCharge.CreateTime,
                    ReceiptType = "线下转账（待确认）",
                    AgencyName = agencyCreditCharge.AgencyName
                }
            });
        }

        await _dbContext.SaveChangesAsync();
        //易宝支付 超时关闭
        if (agencyCreditCharge.PayType == PayType.YeePay)
        {
            _backgroundJobClient.Schedule<HangfireClient.Jobs.IOrderJob>(s => s.CloseAgencyCreditChargeOrder(agencyCreditCharge.Id, agencyCreditCharge.TenantId), TimeSpan.FromMinutes(30));
        }
        return agencyCreditCharge.Id;
    }

    public async Task Update(AgencyCreditChargeUpdateInput input)
    {
        var charge = await _dbContext.AgencyCreditCharges
                .Where(x => x.Id == input.Id)
                .WhereIF(input.AgencyId.HasValue, x => x.AgencyId == input.AgencyId!)
                .FirstOrDefaultAsync();
        if (charge?.ChargeStatus != AgencyCreditChargeStatus.Pending)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        charge.Amount = input.Amount;
        charge.AccountInfo = new TenantAccountInfo
        {
            TenantBankAccountId = input.TenantAccountInfo.TenantBankAccountId,
            AccountName = input.TenantAccountInfo.AccountName,
            AccountNo = input.TenantAccountInfo.AccountNo,
            BankCode = input.TenantAccountInfo.BankCode,
            BankName = input.TenantAccountInfo.BankName,
            BranchName = input.TenantAccountInfo.BranchName,
            Proof = input.TenantAccountInfo.Proof,
        };
        await _dbContext.SaveChangesAsync();
    }

    [UnitOfWork]
    public async Task Handle(AgencyCreditChargeHandleInput input)
    {
        var charge = await _dbContext.AgencyCreditCharges
            .Where(x => x.Id == input.Id)
            .WhereIF(input.AgencyId.HasValue, x => x.AgencyId == input.AgencyId!.Value)
            .FirstOrDefaultAsync();
        if (charge?.ChargeStatus != AgencyCreditChargeStatus.Pending)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        charge.ChargeStatus = input.ChargeStatus;
        charge.FinishTime = DateTime.Now;
        charge.Operator = input.Operator;
        charge.OperatorId = input.OperatorId;
        if (input.PayType.HasValue)
            charge.PayType = input.PayType.Value;
        if (input.PayChannel.HasValue)
            charge.PayChannel = input.PayChannel.Value;
        if (!string.IsNullOrWhiteSpace(input.ExtNo))
            charge.ExtNo = input.ExtNo;
        if (charge.ChargeStatus == AgencyCreditChargeStatus.Finished)
        {
            //预收款充值
            await _capPublisher.PublishAsync(CapTopics.Payment.ReceiptPrepaymentRecharge, new ReceiptPrepaymentRechargeMessage
            {
                ChargeId = charge.Id,
                AgencyId = charge.AgencyId,
                Amount = charge.Amount,
                CurrencyCode = charge.CurrencyCode,
                TenantId = charge.TenantId
            }, CapTopics.Tenant.ReceiptPrepaymentRechargeFlow);

            //收款流水
            await _capPublisher.PublishAsync(CapTopics.Payment.TenantReceiptFlow, new TenantReceiptFlowMessage
            {
                TenantId = charge.TenantId,
                AgencyId = charge.AgencyId,
                Amount = charge.Amount,
                BusinessOrderId = charge.Id,
                CreateTime = charge.FinishTime!.Value,
                PayType = charge.PayType,
                TenantReceiptFlowType = TenantReceiptFlowType.ReceiptPrepayment,
                TenantBankAccount = new Contracts.Common.Payment.DTOs.TenantReceiptFlow.TenantBankAccountDto
                {
                    Id = charge.AccountInfo?.TenantBankAccountId,
                    AccountNo = charge.AccountInfo?.AccountNo,
                    BankName = charge.AccountInfo?.BankName
                },
            });
        }
        await _dbContext.SaveChangesAsync();
    }

    public async Task ReceiptPrepaymentRechargeFlow(ReceiptPrepaymentRechargeFlowMessage message)
    {
        var charge = await _dbContext.AgencyCreditCharges
                .Where(x => x.Id == message.ChargeId)
                .FirstOrDefaultAsync();
        charge.ChargeFlowId = message.FlowId;
        if (charge.PayType == PayType.YeePay)
        {
            charge.ChargeFlowId = message.ChargeId;//易宝支付的充值订单号作为流水号 推送金蝶使用的是充值单号
        }
        await _dbContext.SaveChangesAsync();
    }

    public async Task<PaymentInfoOutput> GetPaymentInfo(long id)
    {
        var charge = await _dbContext.AgencyCreditCharges
        .Where(x => x.Id == id)
        .FirstOrDefaultAsync();
        PaymentInfoOutput output = new()
        {
            BaseOrderId = charge.Id,
            ProductName = "分销商额度充值",
            OrderStatus = charge.ChargeStatus switch
            {
                AgencyCreditChargeStatus.Pending => OrderStatus.WaitingForPay,
                AgencyCreditChargeStatus.Finished => OrderStatus.Paid,
                AgencyCreditChargeStatus.Closed => OrderStatus.Closed,
            },
            BaseOrderStatus = charge.ChargeStatus switch
            {
                AgencyCreditChargeStatus.Pending => BaseOrderStatus.WaitingForPay,
                AgencyCreditChargeStatus.Finished => BaseOrderStatus.Finished,
                AgencyCreditChargeStatus.Closed => BaseOrderStatus.Closed,
            },
            SellingChannels = SellingChannels.B2b,
            SellingPlatform = SellingPlatform.B2BWeb,
            AgencyId = charge.AgencyId,
            AgencyName = charge.AgencyName,
            CurrencyCode = charge.CurrencyCode,
            Amount = charge.Amount,
            CreateTime = charge.CreateTime,
        };
        return output;
    }

    public async Task<IEnumerable<GetCreditChargeDetailsOutput>> Details(params long[] ids)
    {
        var charges = await _dbContext.AgencyCreditCharges.AsNoTracking()
            .Where(x => ids.Contains(x.Id))
            .Select(x => new GetCreditChargeDetailsOutput
            {
                Id = x.Id,
                AgencyId = x.AgencyId,
                AgencyName = x.AgencyName,
                PayType = x.PayType,
                PayChannel = x.PayChannel,
                Amount = x.Amount,
                CurrencyCode = x.CurrencyCode,
                AccountInfo = new TenantAccountInfoDto
                {
                    TenantBankAccountId = x.AccountInfo.TenantBankAccountId,
                    AccountName = x.AccountInfo.AccountName,
                    AccountNo = x.AccountInfo.AccountNo,
                    BankCode = x.AccountInfo.BankCode,
                    BankName = x.AccountInfo.BankName,
                    BranchName = x.AccountInfo.BranchName,
                    Proof = x.AccountInfo.Proof,
                },
                ChargeStatus = x.ChargeStatus,
                CreateTime = x.CreateTime,
                Operator = x.Operator,
                OperatorId = x.OperatorId,
                ConfirmTime = x.FinishTime,
                ExtNo = x.ExtNo,
                ReceiptSettlementOrderId = x.ReceiptSettlementOrderId
            })
            .ToListAsync();
        return charges;
    }

    [UnitOfWork]
    public async Task OrderStatusChangeByPaySuccess(OrderStatusChangeByPaySuccessMessage receive)
    {
        Enum.TryParse<PayChannel>(receive.PaymentChannel, out var payChannel);
        var charge = await _dbContext.AgencyCreditCharges.AsNoTracking()
            .Where(x => x.Id == receive.OrderId)
            .Select(x => new { x.Id, x.ChargeStatus })
            .FirstOrDefaultAsync();
        //订单非待确认状态 订单无效支付退款 (如订单关闭、重复支付等场景)
        if (charge.ChargeStatus != AgencyCreditChargeStatus.Pending)
        {
            await _capPublisher.PublishDelayAsync(TimeSpan.FromSeconds(10),//延时10秒处理，避免订单状态未更新
            CapTopics.Payment.OrderPaymentUselessRefund, new Contracts.Common.Payment.Messages.OrderPaymentUselessRefundMessage
            {
                OrderPaymentId = receive.OrderPaymentId,
                OrderPaymentType = receive.OrderPaymentType,
                OrderId = receive.OrderId,
            });
            return;
        }

        await Handle(new AgencyCreditChargeHandleInput
        {
            ChargeStatus = AgencyCreditChargeStatus.Finished,
            Id = receive.OrderId,
            PayType = receive.PaymentType,
            PayChannel = payChannel,
            ExtNo = receive.PaymentExternalNo
        });
    }

    public async Task Refund(AgencyCreditChargeRefundInput input)
    {
        var charge = await _dbContext.AgencyCreditCharges
              .Where(x => x.Id == input.ChargeId)
              .FirstOrDefaultAsync();
        if (charge?.ChargeStatus != AgencyCreditChargeStatus.Finished)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        if (charge.Amount < input.RefundAmount)
            throw new BusinessException(ErrorTypes.Tenant.AgencyChargeOverChagreAmount);

        using var content = new StringContent(JsonConvert.SerializeObject(new Contracts.Common.Payment.DTOs.ReceiptPrepayment.RefundInput
        {
            AgencyId = charge.AgencyId,
            ChargeId = charge.Id,
            CurrencyCode = charge.CurrencyCode,
            PayType = charge.PayType,
            RefundAmount = input.RefundAmount,
            Remark = input.Remark,
            Description = "预收款充值退款"
        }), Encoding.UTF8, "application/json");
        var refundOutput = await _httpClientFactory.InternalPostAsync<Contracts.Common.Payment.DTOs.ReceiptPrepayment.RefundOutput>(_servicesAddress.Value.Payment_ReceiptPrepaymentRefund(),
            httpContent: content);

        switch (refundOutput.Status)
        {
            case RefundStatus.PROCESSING:
                charge.ChargeStatus = AgencyCreditChargeStatus.Refunding;
                break;
            case RefundStatus.SUCCESS:
                charge.ChargeStatus = AgencyCreditChargeStatus.Refunded;
                //收款流水
                await _capPublisher.PublishAsync(CapTopics.Payment.TenantReceiptFlow, new TenantReceiptFlowMessage
                {
                    TenantId = charge.TenantId,
                    AgencyId = charge.AgencyId,
                    Amount = -input.RefundAmount,
                    BusinessOrderId = charge.Id,
                    CreateTime = DateTime.Now,
                    PayType = charge.PayType,
                    TenantReceiptFlowType = TenantReceiptFlowType.ReceiptPrepaymentRefund,
                    TenantBankAccount = new Contracts.Common.Payment.DTOs.TenantReceiptFlow.TenantBankAccountDto
                    {
                        Id = charge.AccountInfo?.TenantBankAccountId,
                        AccountNo = charge.AccountInfo?.AccountNo,
                        BankName = charge.AccountInfo?.BankName
                    },
                });
                break;
        }
        await _dbContext.SaveChangesAsync();

        switch (refundOutput.RefundType)
        {
            case Contracts.Common.Payment.DTOs.ReceiptPrepayment.ReceiptPrepaymentRefundResultType.Success: break;
            case Contracts.Common.Payment.DTOs.ReceiptPrepayment.ReceiptPrepaymentRefundResultType.RefundIsExtsts:
                throw new BusinessException(ErrorTypes.Tenant.ReceiptPrepaymentRefundIsExists);
                break;
            case Contracts.Common.Payment.DTOs.ReceiptPrepayment.ReceiptPrepaymentRefundResultType.InsufficientBalance:
                throw new BusinessException(ErrorTypes.Tenant.ReceiptPrepaymentInsufficientBalance);
                break;
        }
    }

    [UnitOfWork]
    public async Task OfflineRefundConfirm(OfflineRefundConfirmInput input)
    {
        var charge = await _dbContext.AgencyCreditCharges.AsNoTracking()
                 .Where(x => x.Id == input.ChargeId && x.PayType == PayType.Offline)
                 .FirstOrDefaultAsync();
        if (charge?.ChargeStatus != AgencyCreditChargeStatus.Refunding)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        using var content = new StringContent(JsonConvert.SerializeObject(
            new Contracts.Common.Payment.DTOs.ReceiptPrepayment.OfflineRefundConfirmInput
            {
                ChargeId = input.ChargeId,
                PayTime = input.PayTime,
                AccountInfo = input.AccountInfo,
                Proof = input.Proof
            }), Encoding.UTF8, "application/json");
        var result = await _httpClientFactory.InternalPostAsync<Contracts.Common.Payment.DTOs.ReceiptPrepayment.OfflineRefundConfirmOutput>
            (_servicesAddress.Value.Payment_ReceiptPrepaymentOfflineRefundConfirm(),
            httpContent: content);
        await RefundResult(new OrderRefundResultHandleMessage
        {
            IsSuccess = true,
            OrderId = charge.Id,
            TenantId = charge.TenantId,
            RefundAmount = charge.Amount,
        });
    }

    [UnitOfWork]
    public async Task RefundResult(OrderRefundResultHandleMessage command)
    {
        var orderId = command.OrderId;
        var charge = await _dbContext.AgencyCreditCharges.IgnoreQueryFilters()
            .Where(x => x.Id == orderId && x.TenantId == command.TenantId)
            .FirstOrDefaultAsync();
        if (command.IsSuccess)
        {
            charge.ChargeStatus = AgencyCreditChargeStatus.Refunded;
            //收款流水
            await _capPublisher.PublishAsync(CapTopics.Payment.TenantReceiptFlow, new TenantReceiptFlowMessage
            {
                TenantId = charge.TenantId,
                AgencyId = charge.AgencyId,
                Amount = -command.RefundAmount,
                BusinessOrderId = charge.Id,
                CreateTime = DateTime.Now,
                PayType = charge.PayType,
                TenantReceiptFlowType = TenantReceiptFlowType.ReceiptPrepaymentRefund,
                TenantBankAccount = new Contracts.Common.Payment.DTOs.TenantReceiptFlow.TenantBankAccountDto
                {
                    Id = charge.AccountInfo?.TenantBankAccountId,
                    AccountNo = charge.AccountInfo?.AccountNo,
                    BankName = charge.AccountInfo?.BankName
                },
            });
        }
        else if (charge.ChargeStatus == AgencyCreditChargeStatus.Refunding)
            charge.ChargeStatus = AgencyCreditChargeStatus.Finished;
        await _dbContext.SaveChangesAsync();
    }

    #region Receivables

    public async Task<PagingModel<ReceivablesAgencyCreditRechargeOutput>> ReceivablesSearch(ReceivablesSearchInput input)
    {
        var result = await CreditReChargeQuery(input)
            .OrderByDescending(x => x.OrderId)
            .PagingAsync(input.PageIndex, input.PageSize);
        return result;
    }

    public async Task<List<ReceivablesAgencyCreditRechargeOutput>> ReceivablesExport(ReceivablesOrderExportInput input)
    {
        var result = await CreditReChargeQuery(new ReceivablesSearchInput
        {
            AgencyIds = input.AgencyIds,
            BillingCycleBegin = input.BillingCycleBegin,
            BillingCycleEnd = input.BillingCycleEnd,
            ReceiptDateTye = input.CreditReChargeOrderDateType
        })
        .ToListAsync();
        return result;
    }

    public async Task<IEnumerable<ReceiptSettlementOrderDetailInfo>> SettlementDetailSearch(PreCreateInput input)
    {
        //结算周期为空或者没指定订单Id列表
        if (!input.BillingCycleBegin.HasValue && !input.BillingCycleEnd.HasValue && input.CreditReChargeOrderIds.Any() is false)
            return Enumerable.Empty<ReceiptSettlementOrderDetailInfo>();

        var query = CreditReChargeQuery(new ReceivablesSearchInput
        {
            AgencyIds = input.AgencyIds,
            BillingCycleBegin = input.BillingCycleBegin,
            BillingCycleEnd = input.BillingCycleEnd,
            ReceiptOrderIds = input.CreditReChargeOrderIds
        });

        var result = await query.Select(x =>
            new ReceiptSettlementOrderDetailInfo
            {
                BaseOrderId = x.BaseOrderId,
                BusinessOrderId = x.OrderId,
                AgencyId = x.AgencyId,
                TotalAmount = x.TotalAmount,
                BusinessType = ReceiptSettlementBusinessType.AgencyCreditReChargeRecord,
                PaymentCurrencyCode = x.PaymentCurrencyCode,
                CreateOrderDate = x.CreateOrderDate,
                OrderSettlementDateTypeTime = x.CreateOrderDate
            }).ToListAsync();

        return result;
    }

    private IQueryable<ReceivablesAgencyCreditRechargeOutput> CreditReChargeQuery(ReceivablesSearchInput input)
    {
        var filterDate = input is { BillingCycleBegin: not null, BillingCycleEnd: not null }
                         && input.ReceiptOrderIds.Any() is false;

        //查询已完成且未生成收款结算单的充值订单.
        var query = _dbContext.AgencyCreditCharges.AsNoTracking()
            .Where(x => x.ChargeStatus == AgencyCreditChargeStatus.Finished && x.ChargeType == AgencyChargeType.Credit)
            .Where(x => x.ReceiptSettlementOrderId == null)
            .WhereIF(filterDate, x => x.CreateTime >= input.BillingCycleBegin
                                      && x.CreateTime < input.BillingCycleEnd)
            .WhereIF(input.AgencyIds.Any() is false, x => x.AgencyId != 0)
            .WhereIF(input.AgencyIds.Any(), x => input.AgencyIds.Contains(x.AgencyId))
            .WhereIF(input.ReceiptOrderIds.Any(), x => input.ReceiptOrderIds.Contains(x.Id));

        return query.Select(x => new ReceivablesAgencyCreditRechargeOutput
        {
            BaseOrderId = x.Id,
            OrderId = x.Id,
            CreateOrderDate = x.CreateTime,
            AgencyId = x.AgencyId,
            AgencyName = x.AgencyName,
            PayType = x.PayType,
            PayChannel = x.PayChannel,
            TotalAmount = x.Amount,
            PaymentCurrencyCode = x.CurrencyCode,
            ChargeStatus = x.ChargeStatus,
            FinishTime = x.FinishTime
        });
    }


    public async Task BindReceiptSettlementOrder(List<BindReceiptSettlementOrderInput> input)
    {
        var allChargeIds = input.SelectMany(x => x.ChargeRecordIds).ToList();
        var allCharges = await _dbContext.AgencyCreditCharges
            .Where(x => allChargeIds.Contains(x.Id))
            .ToListAsync();
        foreach (var charge in allCharges)
        {
            var bindItem = input.FirstOrDefault(x => x.ChargeRecordIds.Contains(charge.Id));
            if (bindItem is null) continue;
            charge.ReceiptSettlementOrderId = bindItem.ReceiptSettlementOrderId;
        }

        await _dbContext.SaveChangesAsync();
    }

    public async Task UnBindReceiptSettlementOrder(UnBindReceiptSettlementOrderInput input)
    {
        var allCharges = await _dbContext.AgencyCreditCharges
            .Where(x => input.ChargeRecordIds.Contains(x.Id))
            .ToListAsync();
        foreach (var charge in allCharges)
        {
            charge.ReceiptSettlementOrderId = null;
        }

        await _dbContext.SaveChangesAsync();
    }

    #endregion
}
