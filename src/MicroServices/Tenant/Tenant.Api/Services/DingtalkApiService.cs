using AutoMapper;
using Cit.OpenAPI.Dingtalk.Client;
using Cit.OpenAPI.Dingtalk.Models;
using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common.Order.DTOs.OffsetOrderDingtalkApply;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Tenant.DTOs.DingtalkApi;
using Contracts.Common.Tenant.Enums;
using Contracts.Common.Tenant.Messages;
using Contracts.Common.User.DTOs;
using Contracts.Common.User.DTOs.TenantUser;
using Contracts.Common.User.Messages;
using DotNetCore.CAP;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Tenant.Api.Extensions;
using Tenant.Api.Services.Interfaces;
using static AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.QuerySchemaByProcessCodeResponseBody;

namespace Tenant.Api.Services;

public class DingtalkApiService : IDingtalkApiService
{
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly IDingtalkClientFactory _dingtalkClientFactory;
    private readonly IOptions<ServicesAddress> _servicesAddress;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ICapPublisher _capPublisher;
    private readonly ILogger _logger;
    private readonly IOptions<DingtalkConfig> _dingtalkConfig;

    public DingtalkApiService(
        CustomDbContext customDbContext,
        IMapper mapper,
        IDingtalkClientFactory dingtalkClientFactory,
        IOptions<ServicesAddress> servicesAddress,
        IHttpClientFactory httpClientFactory,
        ICapPublisher capPublisher,
        ILoggerFactory loggerFactory,
           IOptions<DingtalkConfig> dingtalkConfig)
    {
        _dbContext = customDbContext;
        _mapper = mapper;
        _dingtalkClientFactory = dingtalkClientFactory;
        _servicesAddress = servicesAddress;
        _httpClientFactory = httpClientFactory;
        _capPublisher = capPublisher;
        _logger = loggerFactory.CreateLogger<DingtalkApiService>();
        _dingtalkConfig = dingtalkConfig;
    }

    public async Task DepartmentList(SyncDingtalkDepartmentMessage input)
    {
        var config = await _dbContext.DingtalkApiConfigs.FirstOrDefaultAsync(x => x.TenantId == input.TenantId);
        if (config == null)
            return;
        var configReq = new DingtalkConfig()
        {
            AppId = config.AppId,
            AppKey = config.ClientID,
            AppSecret = config.ClientSecret
        };
        var client = _dingtalkClientFactory.Create(configReq);
        var dept_id = 1;
        bool isDo = true;

        List<DingtalkDepartmentListsubResponse> listsubResponses = new();
        await Department(client, dept_id, listsubResponses);
        var oldDingtalkDepartments = await _dbContext.DingtalkDepartments.Where(x => x.TenantId == input.TenantId).ToListAsync();
        _dbContext.RemoveRange(oldDingtalkDepartments);

        var dingtalkDepartments = listsubResponses.Select(x => new DingtalkDepartment
        {
            DeptId = x.DeptId,
            Name = x.Name,
            ParentId = x.ParentId,

        }).ToList();
        await _dbContext.SetTenantId(input.TenantId).AddRangeAsync(dingtalkDepartments);
        await _dbContext.SaveChangesAsync();
    }

    private async Task Department(DingtalkClient client, long dept_id, List<DingtalkDepartmentListsubResponse> listsubResponses)
    {
        var apiRes = await client.DepartmentListsub(dept_id);
        _logger.LogInformation("Dingtalk Department: dept_id:{@dept_id}, apiRes: {@apiRes}", dept_id, apiRes);
        if (apiRes?.Result?.Any() is true)
        {
            listsubResponses.AddRange(apiRes?.Result!);
            foreach (var item in apiRes?.Result)
            {

                await Department(client, item.DeptId, listsubResponses);
            }
        }
    }

    /// <summary>
    /// 用户可见的工作流表单列表，因为是在配置的时候获取的，那时候可能还没有填写ClientID/ClientSecret或者修改了，所以需要传入
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<List<WorkflowFormsOuput>> WorkflowForms(WorkflowFormsInput input)
    {
        var res = new List<WorkflowFormsOuput>();
        var config = new DingtalkConfig()
        {
            AppId = input.AppId,
            AppKey = input.ClientID,
            AppSecret = input.ClientSecret
        };
        var client = _dingtalkClientFactory.Create(config);
        var nextToken = 0;
        var request = new AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.ListUserVisibleBpmsProcessesRequest()
        {
            UserId = input.DingtalkUserid,
            MaxResults = 100, // 每页最大数量
            NextToken = 0,//页码
        };
        bool isDo = true;
        do
        {
            var apiRes = await client.UserVisibilitiesTemplates(request);
            if (apiRes?.Body?.Result?.ProcessList?.Any() is true)
            {
                var list = apiRes?.Body?.Result?.ProcessList.Select(x => new WorkflowFormsOuput()
                {
                    Name = x.Name,
                    ProcessCode = x.ProcessCode,
                }).ToList();
                if (list?.Any() is true)
                    res.AddRange(list);
            }
            if (apiRes?.Body?.Result?.NextToken > 0)
            {
                request.NextToken = apiRes?.Body?.Result?.NextToken;
            }
            else
            {
                isDo = false;
            }
        } while (isDo);

        return res;
    }

    /// <summary>
    /// 工作流表单详情，因为是在配置的时候获取的，那时候可能还没有填写ClientID/ClientSecret或者修改了，所以需要传入
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<GetWorkflowFormInfoOuput> GetWorkflowFormInfo(GetWorkflowFormInfoInput input)
    {
        var res = new GetWorkflowFormInfoOuput();
        var config = new DingtalkConfig()
        {
            AppId = input.AppId,
            AppKey = input.ClientID,
            AppSecret = input.ClientSecret
        };
        var client = _dingtalkClientFactory.Create(config);
        var request = new AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.QuerySchemaByProcessCodeRequest()
        {
            ProcessCode = input.ProcessCode,
        };
        var apiRes = await client.SchemaByProcessCode(request);
        if (apiRes?.Body?.Result is not null)
        {
            var result = apiRes?.Body?.Result;
            var schema = await _dbContext.DingtalkFormsSchemas.FirstOrDefaultAsync(x => x.ProcessCode == result.FormCode);
            if (schema == null)
            {
                if (input.AddNew == false)
                    return res;
                schema = new DingtalkFormsSchemas()
                {
                    ProcessCode = result.FormCode,
                };
                await _dbContext.DingtalkFormsSchemas.AddAsync(schema);
            }
            schema.Result = JsonConvert.SerializeObject(result);
            schema.UpdateTime = DateTime.Now;
            await _dbContext.SaveChangesAsync();

            res = new GetWorkflowFormInfoOuput()
            {
                ProcessCode = result.FormCode,
                Name = result.Name,
            };
            res.Items = result.SchemaContent?.Items?.Where(x => !(string.IsNullOrEmpty(x.Props?.Label) && x.Props?.Required is not true))
                .Select(x => new SchemaContentitem()
                {
                    ComponentName = x.ComponentName,
                    Format = x.Props.Format,
                    Id = x.Props.Id,
                    Label = x.Props.Label,
                    Placeholder = x.Props.Placeholder,
                    Link = x.Props.Link,
                    Options = x.Props?.Options?.Any() is true ? x.Props.Options.
                                          Select(x => JsonConvert.DeserializeObject<OptionItem>(x)!)
                                          .ToList() : null,
                    Required = x.Props.Required == true ? true : false,
                    Unit = x.Props.Unit
                }).ToList();
        }
        else
        {
            throw new BusinessException(ErrorTypes.Tenant.DintalkConfigError);
        }
        return res;
    }

    public async Task<GetWorkflowFormInfoOuput> GetWorkflowFormInfoByProcessCode(string processCode)
    {
        var schema = await _dbContext.DingtalkFormsSchemas.FirstOrDefaultAsync(x => x.ProcessCode == processCode);
        var result = JsonConvert.DeserializeObject<QuerySchemaByProcessCodeResponseBodyResult>(schema.Result);
        var res = new GetWorkflowFormInfoOuput()
        {
            ProcessCode = result.FormCode,
            Name = result.Name,
        };
        res.Items = result.SchemaContent?.Items?.Where(x => !(string.IsNullOrEmpty(x.Props?.Label) && x.Props?.Required is not true))
            .Select(x => new SchemaContentitem()
            {
                ComponentName = x.ComponentName,
                Format = x.Props.Format,
                Id = x.Props.Id,
                Label = x.Props.Label,
                Placeholder = x.Props.Placeholder,
                Link = x.Props.Link,
                Options = x.Props?.Options?.Any() is true ? x.Props.Options.
                                          Select(x => JsonConvert.DeserializeObject<OptionItem>(x)!)
                                          .ToList() : null,
                Required = x.Props.Required == true ? true : false,
                Unit = x.Props.Unit
            }).ToList();
        return res;
    }

    /// <summary>
    /// 更新表单信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task WorkflowFormInfoChange(WorkflowFormInfoChangeInput input)
    {
        var config = await _dbContext.DingtalkApiConfigs.FirstOrDefaultAsync(x => x.TenantId == input.TenantId);
        if (config == null)
            return;
        await GetWorkflowFormInfo(new GetWorkflowFormInfoInput()
        {
            AppId = config.AppId,
            ClientID = config.ClientID,
            ClientSecret = config.ClientSecret,
            ProcessCode = input.ProcessCode,
            AddNew = false,// 只做更新，不做新增
        });
    }

    public async Task<List<GetUserOutput>> GetUser(GetUserInput input)
    {
        var res = new List<GetUserOutput>();
        var configInput = new DingtalkConfig();
        if (input.IsManage)
        {
            configInput = new DingtalkConfig()
            {
                AppId = _dingtalkConfig.Value.AppId,
                AppKey = _dingtalkConfig.Value.AppKey,
                AppSecret = _dingtalkConfig.Value.AppSecret
            };
        }
        else
        {
            var config = await _dbContext.DingtalkApiConfigs.FirstOrDefaultAsync(x => x.TenantId == input.TenantId);
            if (config == null)
                return res;
            configInput = new DingtalkConfig()
            {
                AppId = config.AppId,
                AppKey = config.ClientID,
                AppSecret = config.ClientSecret
            };
        }
        var client = _dingtalkClientFactory.Create(configInput);
        var request = new AlibabaCloud.SDK.Dingtalkcontact_1_0.Models.SearchUserRequest()
        {
            QueryWord = input.UserName,
            Offset = 0,
            Size = 20,
        };
        var apiRes = await client.UsersSearch(request);
        _logger.LogInformation("Dingtalk GetUser: Request:{@Request}, apiRes: {@apiRes}", request, apiRes);
        if (apiRes?.Body?.List.Any() is true)
        {
            // 可能同名
            foreach (var item in apiRes?.Body?.List)
            {
                var apiUserRes = await client.UsersInfo(new DingtalkUserGetRequest() { Userid = item });
                _logger.LogInformation("Dingtalk GetUser-》UsersInfo: item:{@item}, apiRes: {@apiRes}", item, apiUserRes);
                if (apiUserRes.Errcode == 0)
                {
                    var user = new GetUserOutput();
                    user.JobId = apiUserRes.Result.JobNumber.ToString();
                    user.DingtalkUserid = apiUserRes.Result.Userid;
                    user.Name = apiUserRes.Result.Name;
                    user.DeptIdList = apiUserRes.Result.DeptIdList;
                    user.IsDeptLeader = apiUserRes.Result.LeaderInDept.FirstOrDefault()?.Leader ?? false;
                    user.LeaderDeptId = apiUserRes.Result.LeaderInDept.FirstOrDefault()?.DeptId;
                    user.DingtalkUnionid = apiUserRes.Result.Unionid;
                    user.DingtalkTitle = apiUserRes.Result.Title;
                    res.Add(user);
                }
            }
        }

        if (res.Any())
        {
            // 钉钉用户信息更新，manage的也要同步，离职后是查不到的了，所以要同步
            await _capPublisher.PublishAsync(CapTopics.User.SyncDingtalkUser, new DingtalkUserSaveMessage()
            {
                DingtalkUsers = res,
                TenantId = input.TenantId,
            });
        }
        return res;
    }

    /// <summary>
    /// 创建审批单
    /// </summary>
    /// <returns></returns>
    public async Task CreateProcessInstances(DingtalkApplySubmitMessage input)
    {
        var res = new SyncOffsetOrderDingtalkMessage()
        {
            OffsetOrderDingtalkApplyId =
            input.OffsetOrderDingtalkApplyId,
            AuditStatus = OffsetOrderDingtalkApplyAuditStatus.Fail,
            TenantId = input.TenantId
        };
        var config = await _dbContext.DingtalkApiConfigs.FirstOrDefaultAsync(x => x.TenantId == input.TenantId);
        if (config?.Enable is true)
        {
            var user = (await GetUserAsync(new List<long>() { input.UserId })).FirstOrDefault();
            if (string.IsNullOrEmpty(user?.DingtalkUserId) || string.IsNullOrEmpty(user?.DingtalkDeptId))
            {
                res.ErrorMsg = $"[{input.UserId}]缺失钉钉用户Id或者部门Id";
            }
            else
            {
                var originatorUserId = user.DingtalkUserId;
                var dingtalkDeptIds = user?.DingtalkDeptId.Split(',').ToList();
                var deptId = long.Parse(dingtalkDeptIds?.FirstOrDefault()!);
                var configInput = new DingtalkConfig()
                {
                    AppId = config.AppId,
                    AppKey = config.ClientID,
                    AppSecret = config.ClientSecret
                };
                var client = _dingtalkClientFactory.Create(configInput);
                AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.StartProcessInstanceRequest startProcessInstanceRequest = new AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.StartProcessInstanceRequest
                {
                    OriginatorUserId = originatorUserId,
                    ProcessCode = input.ProcessCode,
                    DeptId = deptId,
                    FormComponentValues = new List<AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues>(),
                };

                foreach (var item in input.Items)
                {
                    if (string.IsNullOrEmpty(item.Value) && item.Required == false)// 非必填项，必填的做日志，方便排查log
                        continue;
                    startProcessInstanceRequest.FormComponentValues.Add(new AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues
                    {
                        ComponentType = item.ComponentName,
                        Id = item.Id,
                        Name = item.Label,
                        Value = item.Value,
                    });
                }

                try
                {
                    var apiRes = await client.AddProcessInstances(startProcessInstanceRequest);
                    _logger.LogInformation("Dingtalk CreateProcessInstances: startProcessInstanceRequest:{@Request}, apiRes: {@apiRes}", startProcessInstanceRequest, apiRes);
                    if (!string.IsNullOrEmpty(apiRes.Body?.InstanceId))
                    {
                        res.InstanceId = apiRes.Body.InstanceId;
                        res.AuditStatus = OffsetOrderDingtalkApplyAuditStatus.UnderApproval;
                    }
                    else
                    {
                        res.ErrorMsg = "申请失败";
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Dingtalk CreateProcessInstances error: startProcessInstanceRequest:{@Request}，ex：{@ex}", startProcessInstanceRequest, ex);
                    res.ErrorMsg = ex.Message;
                }
            }
        }
        else
        {
            res.ErrorMsg = "钉钉配置未启用";
        }
        await _capPublisher.PublishAsync(CapTopics.Order.SyncOffsetOrderDingtalk, res);
    }

    public async Task CallbackProcessInstances(DingtalkApplyCallBackMessage input)
    {
        var res = new SyncOffsetOrderDingtalkMessage()
        {
            OffsetOrderDingtalkApplyId = input.OffsetOrderDingtalkApplyId,
            AuditStatus = OffsetOrderDingtalkApplyAuditStatus.UnderApproval,
            InstanceId = input.InstanceId,
            TenantId = input.TenantId
        };
        var config = await _dbContext.DingtalkApiConfigs.FirstOrDefaultAsync(x => x.TenantId == input.TenantId);
        var configInput = new DingtalkConfig()
        {
            AppId = config.AppId,
            AppKey = config.ClientID,
            AppSecret = config.ClientSecret
        };
        var request = new AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.GetProcessInstanceRequest()
        {
            ProcessInstanceId = input.InstanceId,
        };
        var client = _dingtalkClientFactory.Create(configInput);
        var apiInfoRes = await client.GetProcessInstances(request);
        _logger.LogInformation("Dingtalk GetProcessInstances: GetProcessInstanceRequest:{@Request}, apiRes: {@apiRes}", request, apiInfoRes);
        if (!string.IsNullOrEmpty(apiInfoRes.Body?.Result?.BusinessId))
        {
            res.BusinessId = apiInfoRes.Body.Result.BusinessId;
            foreach (var item in apiInfoRes.Body.Result.OperationRecords)
            {
                var info = new OperationRecordsItem()
                {
                    ActivityId = item.ActivityId,
                    Date = DateTime.Parse(item.Date),
                    Remark = item.Remark,
                    Result = item.Result,
                    ShowName = item.ShowName,
                    UserId = item.UserId,
                    Type = item.Type,
                };
                info.Status = item.Result switch
                {
                    "NONE" => DingtalkOperationRecordType.None,
                    "AGREE" => DingtalkOperationRecordType.AGREE,
                    "REFUSE" => DingtalkOperationRecordType.REFUSE,
                    _ => DingtalkOperationRecordType.None,
                };
                if (info.Type.Equals("START_PROCESS_INSTANCE")) //申请人默认同意
                {
                    info.Status = DingtalkOperationRecordType.AGREE;
                }
                res.OperationRecords.Add(info);
            }
            switch (apiInfoRes.Body.Result.Status)
            {
                case "COMPLETED":
                    if (apiInfoRes.Body.Result.Result.Equals("agree", StringComparison.InvariantCultureIgnoreCase))
                    {
                        res.AuditStatus = OffsetOrderDingtalkApplyAuditStatus.Approved;
                    }
                    else
                    {
                        res.AuditStatus = OffsetOrderDingtalkApplyAuditStatus.Rejected;
                        res.ErrorMsg = "已拒绝";
                    }
                    break;
                case "TERMINATED":
                    res.AuditStatus = OffsetOrderDingtalkApplyAuditStatus.Revoked;
                    res.ErrorMsg = "已撤销";
                    break;
                case "RUNNING":
                    res.AuditStatus = OffsetOrderDingtalkApplyAuditStatus.UnderApproval;
                    break;
                default:
                    res.AuditStatus = OffsetOrderDingtalkApplyAuditStatus.UnderApproval;
                    break;
            }
            await _capPublisher.PublishAsync(CapTopics.Order.SyncOffsetOrderDingtalk, res);
        }
    }

    private async Task<IEnumerable<UserSearchOuput>> GetUserAsync(List<long> ids)
    {
        if (ids is null)
            return new List<UserSearchOuput>();

        var userSearchInput = new SearchTenantUsersInput { Ids = ids.ToArray() };
        var tenantUsers = await _httpClientFactory.InternalPostAsync<IEnumerable<UserSearchOuput>>(_servicesAddress.Value.User_SearchTenantUsers(), httpContent: new StringContent(JsonConvert.SerializeObject(userSearchInput), Encoding.UTF8, "application/json"));

        return tenantUsers;
    }
}
