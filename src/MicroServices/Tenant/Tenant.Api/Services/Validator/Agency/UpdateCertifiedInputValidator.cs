using Contracts.Common.Tenant.DTOs.Agency;
using FluentValidation;

namespace Tenant.Api.Services.Validator.Agency;

public class UpdateCertifiedInputValidator : AbstractValidator<UpdateCertifiedInput>
{
    public UpdateCertifiedInputValidator()
    {
        RuleFor(x => x.LicenceNo)
            .NotEmpty();
        RuleFor(x => x.BusinessLicensePath)
            .NotEmpty();
        RuleFor(x => x.Id)
           .NotEmpty();
        RuleFor(x => x.CityName)
           .NotEmpty();
        RuleFor(x => x.CityCode)
           .NotEmpty();
        RuleFor(x => x.CountryCode)
          .NotEmpty();
        RuleFor(x => x.CountryName)
           .NotEmpty();
        RuleFor(x => x.ProvinceCode)
          .NotEmpty();
        RuleFor(x => x.ProvinceName)
           .NotEmpty();

        RuleFor(x => x.FinancialStaff)
         .NotEmpty();

        // 因为脱敏，如果不做更改的数据，是传 null
        When(s => s.FinancialStaffNumber != null, () =>
        {
            RuleFor(s => s.FinancialStaffNumber)
            .Length(1, 32)
            .Matches(@"^\d+$")
            .SetValidator(new SensitiveDataValidator())
            .WithMessage("FinancialStaffNumber error");

            RuleFor(s => s.FinancialCountryDialCode).NotEmpty();
        });

        When(s => s.NoticeEmail != null, () =>
        {
            RuleFor(s => s.NoticeEmail).NotEmpty().Length(1, 40).SetValidator(new SensitiveDataValidator()).WithMessage("NoticeEmail error");
        });
    }

}
