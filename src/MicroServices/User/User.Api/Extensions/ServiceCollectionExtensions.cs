using Cit.Storage.Redis;
using Common;
using Common.Jwt;
using Common.ServicesHttpClient;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using EventBus.Cap;
using EventBus.Cap.Transaction;
using Extensions;
using HangfireClient;
using User.Api.ConfigModel;

namespace User.Api.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection ConfigureApplicationServices(this IServiceCollection services,
        IConfiguration configuration,
        IWebHostEnvironment env)
    {
        services.AddBasicServices(env);

        //TODO: 请求入口全部转移至bff以后移除
        services.AddControllers(options =>
        {
            options.Filters.Add<Common.Jwt.SetTenantHeaderForServiceFilter>();
        });

        services.AddCustomConfiguration(configuration);
        services.AddServices();
        services.AddCustomDbContext<CustomDbContext>(configuration.GetConnectionString("LogicDatabase")!)
            .AddUow<CustomDbContext>();

        services.AddNetCoreCAP<CustomDbContext>(configuration, env)
            .AddPublisherDecorator(sp =>
            {
                var uowTransaction = sp.GetRequiredService<IUnitOfWorkTransaction>();
                return new EFCoreTransactionReplacer(
                    () => uowTransaction.CurrentTransaction,
                    newTransaction => uowTransaction.CurrentTransaction = newTransaction);
            })
            .AddTenantIdSetterFilter(sp => (tenantId) => 
            {
                var tenantIdentify = sp.GetRequiredService<ITenantIdentify>();
                tenantIdentify.SetTenantId(tenantId);
            });
        services.AddHangfireClient(configuration.GetConnectionString("HangfireDB"), env);
        services.AddCitRedis(configuration.GetConnectionString("Redis"));
        services.AddAutoMapper(System.Reflection.Assembly.GetExecutingAssembly());
        return services;
    }

    /// <summary>
    /// 配置文件
    /// </summary>
    /// <param name="services"></param>
    /// <param name="configuration"></param>
    /// <returns></returns>
    private static IServiceCollection AddCustomConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<ServicesAddress>(configuration.GetSection(nameof(ServicesAddress)));
        services.Configure<JwtConfig>(configuration.GetSection(nameof(JwtConfig)));
        services.Configure<ShortMessageConfig>(configuration.GetSection(nameof(ShortMessageConfig)));
        services.Configure<DingtalkConfig>(configuration.GetSection(nameof(DingtalkConfig)));

        return services;
    }

    /// <summary>
    /// 批量注册Service
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    private static IServiceCollection AddServices(this IServiceCollection services)
    {
        var types = System.Reflection.Assembly
            .GetExecutingAssembly()
            .GetInterfaceAndImplement(s => s.Name.EndsWith("Service"));
        foreach (var item in types)
            services.AddScoped(item.Value, item.Key);

        return services;
    }
}
