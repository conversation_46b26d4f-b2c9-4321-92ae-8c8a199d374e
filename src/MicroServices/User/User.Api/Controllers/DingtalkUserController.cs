using Contracts.Common.User.DTOs.TenantUser;
using Contracts.Common.User.Messages;
using DotNetCore.CAP;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using User.Api.ConfigModel;
using User.Api.Services.Interfaces;

namespace User.Api.Controllers;

/// <summary>
/// 钉钉用户
/// </summary>
[Route("[controller]/[action]")]
[ApiController]
public class DingtalkUserController : ControllerBase
{
    private readonly IDingtalkUserService _dingtalkUserService;
    private readonly ITenantUserService _tenantUserService;
    private readonly IManageUserService _manageUserService;
    private readonly IOptions<DingtalkConfig> _dingtalkConfig;

    public DingtalkUserController(IDingtalkUserService dingtalkUserService,
        ITenantUserService tenantUserService,
        IManageUserService manageUserService,
          IOptions<DingtalkConfig> dingtalkConfig)
    {
        _dingtalkUserService = dingtalkUserService;
        _tenantUserService = tenantUserService;
        _manageUserService = manageUserService;
        _dingtalkConfig = dingtalkConfig;
    }

    [HttpPost]
    public async Task<IActionResult> SetEnabledByDingtalkUserLeave(SetEnabledByDingtalkUserLeaveInput input)
    {
        await _tenantUserService.SetEnabledByDingtalkUserLeave(input);
        if (_dingtalkConfig.Value.AppId == input.AppId && _dingtalkConfig.Value.AppKey == input.AppKey)
            await _manageUserService.SetEnabledByDingtalkUserLeave(input);
        return Ok();
    }


    #region CapSubscribe
    /// <summary>
    /// 订阅 - 保存钉钉用户
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    [NonAction]
    //[HttpPost]
    [CapSubscribe(CapTopics.User.SyncDingtalkUser)]
    public async Task SyncDingtalkUserId(DingtalkUserSaveMessage receive)
    {
        await _dingtalkUserService.SyncUserSave(receive);
    }
    #endregion

}
