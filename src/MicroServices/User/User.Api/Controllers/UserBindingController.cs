using Contracts.Common.User.DTOs.UserBinding;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using User.Api.Services.Interfaces;

namespace User.Api.Controllers;
[Route("[controller]/[action]")]
[ApiController]
public class UserBindingController : ControllerBase
{
    private readonly IUserBindingService _userBindingService;

    public UserBindingController(IUserBindingService userBindingService)
    {
        _userBindingService = userBindingService;
    }

    /// <summary>
    /// 新增/更新 用户绑定关系
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> AddOrUpdateBinding(AddOrUpdateBindingInput input)
    {
        await _userBindingService.AddOrUpdateBinding(input);
        return Ok();
    }

    /// <summary>
    /// 查询用户绑定关系信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<List<GetUserBindingOutput>> GetUserBindings(GetUserBindingInput input)
    {
        var result = await _userBindingService.GetUserBindings(input);
        return result;
    }

    /// <summary>
    /// 解绑平台账号
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Unbound(UnboundUserBindingInput input)
    {
        await _userBindingService.Unbound(input);
        return Ok();
    }

}
