using AutoMapper;
using Common.GlobalException;
using Common.Jwt;
using Common.Message;
using Common.ServicesHttpClient;
using Common.Utils;
using Contracts.Common.Notify.DTOs.ManagerNotify;
using Contracts.Common.Permission.DTOs.Role;
using Contracts.Common.Reflection;
using Contracts.Common.User.DTOs;
using Contracts.Common.User.DTOs.ManageUser;
using Contracts.Common.User.DTOs.OperationLog;
using Contracts.Common.User.Enums;
using Contracts.Common.User.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using User.Api.Extensions;
using User.Api.Services.Interfaces;

namespace User.Api.Services;

public class ManageUserService : IManageUserService
{
    private const string ADMIN = "admin";
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly IOptions<ServicesAddress> _servicesAddress;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ICapPublisher _capPublisher;

    public ManageUserService(CustomDbContext dbContext,
        IMapper mapper,
        IOptions<ServicesAddress> servicesAddress,
        IHttpClientFactory httpClientFactory,
        IHttpContextAccessor httpContextAccessor,
        ICapPublisher capPublisher)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _servicesAddress = servicesAddress;
        _httpClientFactory = httpClientFactory;
        _httpContextAccessor = httpContextAccessor;
        _capPublisher = capPublisher;
    }

    public async Task<ManageUserDTO?> GetByPwd(AccountAndPwdInput input)
    {
        var pwd = SecurityUtil.MD5Encrypt(input.Password, Encoding.UTF8);
        var user = await _dbContext.ManageUsers
           .AsNoTracking()
           .FirstOrDefaultAsync(s => s.Account == input.Account && s.Password == pwd);
        if (user is null)
            return null;
        return _mapper.Map<ManageUserDTO>(user);
    }

    public async Task Add(AddStaffInput input)
    {
        if (await _dbContext.ManageUsers.AnyAsync(x => x.Account == input.Account))
            throw new BusinessException(ErrorTypes.User.AccountIsExist);
        var manageUser = _mapper.Map<ManageUser>(input);

        if (string.IsNullOrEmpty(input.EmailPassword) is false)
            await CheckEmailAvailable(manageUser);

        var setRoleTask = SetManageUserRole(new SetRoleInput
        {
            UserId = manageUser.Id,
            RoleId = input.RoleId
        });
        manageUser.Password = SecurityUtil.MD5Encrypt(manageUser.Password, Encoding.UTF8);
        await _dbContext.ManageUsers.AddAsync(manageUser);
        await _dbContext.SaveChangesAsync();
        input.Password = manageUser.Password;
        var log = CreateLog(input);
        log.OperationType = OperationType.Add;
        log.TabLogType = TabLogType.ManageRoleUser;
        log.KeyId = manageUser.Id;
        log.Content = "新增员工：" + PropertyInfoHelper.PropertyInfoMsg<AddStaffInput>(input);

        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);

        try { await setRoleTask; }
        catch { }
    }

    private async Task SetManageUserRole(SetRoleInput input)
    {
        var url = _servicesAddress.Value.SetManageUserRole();
        using var httpContent = new StringContent(JsonConvert.SerializeObject(input), Encoding.UTF8, "application/json");
        await _httpClientFactory.InternalPostAsync<string>(url, null, httpContent);
    }

    [UnitOfWork]
    public async Task Update(UpdateManagerUserInput input)
    {
        var setRoleTask = SetManageUserRole(new SetRoleInput
        {
            UserId = input.Id,
            RoleId = input.RoleId
        });
        var manageUser = await _dbContext.ManageUsers.FindAsync(input.Id);
        var oldData = new UpdateManagerUserInput
        {
            Id = input.Id,
            Name = manageUser.Name,
            PhoneNumber = manageUser.PhoneNumber,
            EmailPassword = manageUser.EmailPassword,
        };
        manageUser.Name = input.Name;

        if (string.IsNullOrEmpty(input.Email) is false)
        {
            manageUser.Email = input.Email;
        }

        if (string.IsNullOrEmpty(input.PhoneNumber) is false)
            manageUser.PhoneNumber = input.PhoneNumber;

        if (string.IsNullOrEmpty(input.EmailPassword) is false)
        {
            manageUser.EmailPassword = input.EmailPassword;
        }

        await CheckEmailAvailable(manageUser);

        var log = CreateLog(input);
        log.OperationType = OperationType.Edit;
        log.TabLogType = TabLogType.ManageRoleUser;
        log.KeyId = manageUser.Id;
        log.Content = "编辑员工：" + PropertyInfoHelper.PropertyInfoMsg<UpdateManagerUserInput>(input);
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent()
        {
            OldData = oldData,
            NewData = input
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);

        await setRoleTask;
        await _dbContext.SaveChangesAsync();
    }

    private static async Task CheckEmailAvailable(ManageUser user)
    {
        if (string.IsNullOrEmpty(user.EmailPassword))
            return;

        var serverConfig = new EmailServerConfig()
        {
            Address = user.Email,
            Password = user.EmailPassword,
            Host = EmailServerConfigDefault.Host,
            Port = EmailServerConfigDefault.Port,
            UserName = user.Name
        };

        try
        {
            await MailKitHelper.CheckServerAvailable(serverConfig);
        }
        catch (MailKit.Security.AuthenticationException)
        {
            throw new BusinessException(ErrorTypes.Notify.MailBoxAuthenticationError);
        }
        catch
        {
            throw new BusinessException(ErrorTypes.Notify.MailBoxConnectError);
        }
    }

    public async Task<string> RestPassword(RestPasswordInput input)
    {
        var manageUser = await _dbContext.ManageUsers.FindAsync(input.UserId);

        input.NewPassword = string.IsNullOrWhiteSpace(input.NewPassword)
            ? AlphabetUtil.Generate(AlphabetType.Number | AlphabetType.LowerCase | AlphabetType.UpperCase, 8)
            : input.NewPassword;
        var log = CreateLog(input);
        var oldPassword = manageUser.Password;
        manageUser.Password = SecurityUtil.MD5Encrypt(input.NewPassword, Encoding.UTF8);
        log.OperationType = OperationType.Rest;
        log.TabLogType = TabLogType.ManageRoleUser;
        log.KeyId = manageUser.Id;
        log.Body = null;// 密码不保存
        log.Content = "重置密码";
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent
        {
            OldData = new { Password = oldPassword },
            NewData = new { Password = manageUser.Password },
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);
        var rows = await _dbContext.SaveChangesAsync();
        return input.NewPassword;
    }

    public async Task<string> RestTenantAdminPassword(RestTenantAdminPasswordInput input)
    {
        input.NewPassword = string.IsNullOrWhiteSpace(input.NewPassword)
            ? AlphabetUtil.Generate(AlphabetType.Number | AlphabetType.LowerCase | AlphabetType.UpperCase, 8)
            : input.NewPassword;

        var tenantAdmin = await _dbContext.TenantUsers
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(s => s.TenantId == input.TenantId && s.Account == ADMIN);

        tenantAdmin.Password = SecurityUtil.MD5Encrypt(input.NewPassword, Encoding.UTF8);

        var rows = await _dbContext.SaveChangesAsync();
        return input.NewPassword;
    }

    public async Task UpdateManageUserPwd(long manageUserId, UpdatePasswordInput input)
    {
        var manageUser = await _dbContext.ManageUsers
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(s => s.Id == manageUserId);
        var oldPwd = SecurityUtil.MD5Encrypt(input.OldPassword, Encoding.UTF8);
        var newPwd = SecurityUtil.MD5Encrypt(input.NewPassword, Encoding.UTF8);

        if (manageUser is null || manageUser.Password != oldPwd)
            throw new BusinessException(ErrorTypes.User.PasswordError);
        if (manageUser.Enabled == false)
            throw new BusinessException(ErrorTypes.User.AccountIsDisabled);

        var log = CreateLog(input);
        var oldPassword = manageUser.Password;
        log.OperationType = OperationType.Edit;
        log.TabLogType = TabLogType.ManageRoleUser;
        log.KeyId = manageUser.Id;
        log.Body = null;// 密码不保存
        log.Content = "修改密码";
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent
        {
            OldData = new { Password = oldPassword },
            NewData = new { Password = manageUser.Password },
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);

        manageUser.Password = newPwd;

        var rows = await _dbContext.SaveChangesAsync();
    }

    [UnitOfWork]
    public async Task<bool> SetEnabled(SetUserEnabledInput input)
    {
        var manageUser = await _dbContext.ManageUsers
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(s => s.Id == input.UserId);
        manageUser.Enabled = input.Enabled;

        var log = CreateLog(input);
        log.OperationType = OperationType.Edit;
        log.TabLogType = TabLogType.ManageRoleUser;
        log.KeyId = manageUser.Id;
        log.Content = input.Enabled ? "恢复已停用员工" : "停用员工";
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent
        {
            OldData = new SetUserEnabledInput { UserId = input.UserId, Enabled = !input.Enabled },
            NewData = input,
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);

        var rows = await _dbContext.SaveChangesAsync();

        if (manageUser.Enabled is false)
            await RemoveManagerNotifyUserAsync(input.UserId);

        return rows > 0;
    }

    public async Task<PagingModel<UserSearchOuput>> Search(UserSearchInput input)
    {
        var query = _dbContext.ManageUsers.AsNoTracking();
        switch (input.Status)
        {
            case 2:
                break;
            case 1:
                query = query.Where(x => x.Enabled == false);
                break;
            default:
                query = query.Where(x => x.Enabled == true);
                break;
        }
        var userRoleQuery = _dbContext.ManageUserRoles.AsNoTracking();
        var joinQuery = from user in query
                        join role in userRoleQuery on user.Id equals role.UserId into userRoles
                        from userRole in userRoles.DefaultIfEmpty()
                        select new { user, ManageRoleId = (userRole == null ? -1 : userRole.ManageRoleId) };
        var paging = await joinQuery
            .Where(s => s.user.Account != ADMIN)
            .WhereIF(input.Ids is not null && input.Ids.Any(), x => input.Ids!.Contains(x.user.Id))
            .WhereIF(string.IsNullOrWhiteSpace(input.Name) == false, s => s.user.Name.Contains(input.Name))
            .OrderByDescending(s => s.user.CreateTime)
            .WhereIF(input.RoleIds.Any(), s => input.RoleIds.Contains(s.ManageRoleId))
            .PagingAsync(input.PageIndex, input.PageSize, x => new UserSearchOuput()
            {
                Account = x.user.Account,
                CreateTime = x.user.CreateTime,
                Enabled = x.user.Enabled,
                Id = x.user.Id,
                Name = x.user.Name,
                NickName = x.user.NickName,
                PhoneNumber = x.user.PhoneNumber,
                Email = x.user.Email,
                JobId = x.user.JobId,
                DingtalkUserId = x.user.DingtalkUserId,
            });
        if (paging.Data.Any())
        {
            foreach (var item in paging.Data)
            {
                item.DingtalkBind = string.IsNullOrEmpty(item.DingtalkUserId) ? false : true;
            }
        }
        return paging;
    }

    public async Task<IEnumerable<GetAllStaffOutput>> GetAllStaff()
    {
        var result = await _dbContext.ManageUsers
            .AsNoTracking()
            .Where(s => s.Enabled == true && s.Account != ADMIN)
            .Select(s => new GetAllStaffOutput
            {
                UserId = s.Id,
                Name = s.Name,
                Email = s.Email
            })
            .ToListAsync();

        var userIds = result.Select(r => r.UserId);
        var huiZhiBindings = await _dbContext.HuiZhiBindings.IgnoreQueryFilters().AsNoTracking()
            .Where(x => userIds.Contains(x.UserId))
            .Where(x => x.SysRole == SysRole.Manager)
            .ToListAsync();

        result.ForEach(x =>
        {
            x.WechatBind = huiZhiBindings.Any(h => h.UserId.Equals(x.UserId));
        });

        return result;
    }

    public async Task<ManageUserDTO?> GetInfo(long id)
    {
        var user = await _dbContext.ManageUsers
           .AsNoTracking()
           .FirstOrDefaultAsync(s => s.Id == id);
        if (user is null)
            return null;
        var res = _mapper.Map<ManageUserDTO>(user);
        res.DingtalkBind = !string.IsNullOrEmpty(res.DingtalkUserId);
        return res;
    }

    /// <summary>
    /// 订阅 - 同步冗余用户角色数据
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    public async Task SyncManageUserRole(SyncManageUserRoleMessage receive)
    {
        if (receive.Type == 1)
        {
            var userRoles = await _dbContext.ManageUserRoles
                           .WhereIF(receive.Id > 0, x => x.Id == receive.Id)
                           .WhereIF(receive.ManageRoleId > 0, x => x.ManageRoleId == receive.ManageRoleId)
                           .WhereIF(receive.UserId > 0, x => x.UserId == receive.UserId)
                           .ToListAsync();

            _dbContext.ManageUserRoles.RemoveRange(userRoles);
        }
        else
        {
            var userRole = await _dbContext.ManageUserRoles.FirstOrDefaultAsync(x => x.Id == receive.Id);
            if (userRole == null)
            {
                userRole = _mapper.Map<ManageUserRole>(receive);
                await _dbContext.AddAsync(userRole);
            }
            else
            {
                userRole.ManageRoleId = receive.ManageRoleId;
                userRole.UserId = receive.UserId;
            }
        }
        await _dbContext.SaveChangesAsync();
    }

    private OperationLogDto CreateLog(object input)
    {
        CurrentUser currentUser = _httpContextAccessor.HttpContext.GetCurrentUser();
        var request = _httpContextAccessor.HttpContext.Request;
        var ip = _httpContextAccessor.HttpContext.GetRemotingIp();
        var log = new OperationLogDto()
        {
            OperationType = OperationType.Add,
            System = SystemType.Manage,
            Host = request.Host.ToString(),
            Url = request.Path.ToString(),
            Agent = request.Headers.UserAgent,
            Ip = ip,
            Query = JsonConvert.SerializeObject(request.Query),
            Body = JsonConvert.SerializeObject(input),
            OperationUserId = currentUser?.userid,
            OperationUserName = currentUser?.nickname,
            TenantId = currentUser?.tenant
        };
        return log;
    }

    public async Task UpdateJobId(UpdateJobIdInput input)
    {
        var manageUser = await _dbContext.ManageUsers.FindAsync(input.UserId);
        var oldData = new UpdateJobIdInput
        {
            UserId = input.UserId,
            Name = manageUser.Name,
            JobId = manageUser.JobId,
        };
        manageUser.Name = input.Name;
        manageUser.JobId = input.JobId;
        manageUser.DingtalkUserId = input.DingtalkUserId;

        var log = CreateLog(input);
        log.OperationType = OperationType.Edit;
        log.TabLogType = TabLogType.ManageRoleUser;
        log.KeyId = manageUser.Id;
        log.Content = "员工钉钉绑定：" + PropertyInfoHelper.PropertyInfoMsg<UpdateJobIdInput>(input);
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent()
        {
            OldData = oldData,
            NewData = input
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);
        await _dbContext.SaveChangesAsync();
    }

    public async Task<IEnumerable<ManageUserDTO>> GetListByIds(IEnumerable<long> userIds)
    {
        if (userIds.Any() == false)
            return default;
        var manageUsers = await _dbContext.ManageUsers
            .Where(x => userIds.Contains(x.Id))
            .ToListAsync();
        return _mapper.Map<IEnumerable<ManageUserDTO>>(manageUsers);
    }

    public async Task<IEnumerable<ManageUserDTO>> GetListByJobIds(IEnumerable<string> jobIds)
    {
        if (jobIds is null || jobIds.Any() is false)
            return default;

        var manageUsers = await _dbContext.ManageUsers
            .AsNoTracking()
            .Where(x => jobIds.Contains(x.JobId))
            .ToListAsync();

        return _mapper.Map<IEnumerable<ManageUserDTO>>(manageUsers);
    }

    /// <summary>
    /// 删除管理员工通知配置
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    private async Task RemoveManagerNotifyUserAsync(long userId)
    {
        var request = new RemoveManagerNotifyUserSettingInput
        {
            StaffIds = new List<long> { userId }
        };

        await _httpClientFactory.InternalPostAsync(
                  requestUri: _servicesAddress.Value.Notify_RemoveUserSetting(),
                  httpContent: new StringContent(JsonConvert.SerializeObject(request),
                  Encoding.UTF8, "application/json"));
    }

    /// <summary>
    /// 钉钉员工离职通知同步,停止对应钉钉工号账号
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task SetEnabledByDingtalkUserLeave(Contracts.Common.User.DTOs.TenantUser.SetEnabledByDingtalkUserLeaveInput input)
    {
        var dingtalkUserJobIds = await _dbContext.DingtalkUsers
            .Where(x => input.DingtalkUserIds.Contains(x.DingtalkUserId) && !string.IsNullOrEmpty(x.DingtalkUserId))
            .Where(x => !string.IsNullOrEmpty(x.JobId))
            .Select(x => x.JobId).ToListAsync();
        var managerUsers = await _dbContext.ManageUsers
            .Where(x => dingtalkUserJobIds.Contains(x.JobId))
            .Where(x => x.Enabled != false)
            .ToListAsync();
        if (managerUsers.Any())
        {
            foreach (var managerUser in managerUsers)
            {
                var log = CreateLog(input);
                log.OperationType = OperationType.Edit;
                log.TabLogType = TabLogType.ManageRoleUser;
                log.KeyId = managerUser.Id;
                log.Content = "停用员工-钉钉离职通知";
                log.OperationUserName = "System";
                log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent
                {
                    OldData = new SetUserEnabledInput { UserId = managerUser.Id, Enabled = managerUser.Enabled },
                    NewData = new SetUserEnabledInput { UserId = managerUser.Id, Enabled = false },
                });
                await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);

                managerUser.Enabled = false;
            }
            var rows = await _dbContext.SaveChangesAsync();

            foreach (var managerUser in managerUsers)
            {
                await RemoveManagerNotifyUserAsync(managerUser.Id);
            }

        }
    }
}
