using Contracts.Common.User.DTOs;
using Contracts.Common.User.DTOs.TenantUser;
using Contracts.Common.User.Messages;
using EfCoreExtensions.Abstract;

namespace User.Api.Services.Interfaces;

public interface ITenantUserService
{
    /// <summary>
    /// 添加
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="ErrorTypes.User.AccountIsExist"></exception>
    /// <returns></returns>
    Task Add(AddStaffInput input);

    /// <summary>
    /// 订阅 - 添加租户admin用户
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    Task AddAdmin(AddTenantUserMessage receive);

    /// <summary>
    /// 重置密码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<string> RestPassword(RestPasswordInput input);

    /// <summary>
    /// 更新密码
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="ErrorTypes.User.PasswordError"></exception>
    /// <exception cref="ErrorTypes.User.AccountIsDisabled"></exception>
    /// <returns></returns>
    Task UpdatePassword(long tenantUserId, UpdatePasswordInput input);

    /// <summary>
    /// 检索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<UserSearchOuput>> Search(UserSearchInput input);

    Task<IEnumerable<UserSearchOuput>> SearchUsers(SearchTenantUsersInput input);

    /// <summary>
    /// 启用/停用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> SetEnabled(SetUserEnabledInput input);

    /// <summary>
    /// 更新
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task Update(UpdateStaffInput input);

    Task<TenantUserRoleInfoDto?> FindTenantUserRoles(long tenantUserId);

    Task<UserSearchOuput?> FindOne(Contracts.Common.User.DTOs.SupplierUser.FindOneInput input);

    /// <summary>
    /// 获取所有员工
    /// </summary>
    /// <returns></returns>
    Task<IEnumerable<GetAllStaffOutput>> GetAllStaff(bool OnlyDingtalk = false);


    /// <summary>
    /// 订阅 - 同步冗余用户角色数据
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    Task SyncTenantUserRole(SyncTenantUserRoleMessage receive);

    /// <summary>
    /// 更新用户名字
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> UpdateName(UpdateTenantUserNameInput input);

    /// <summary>
    /// 验证手机或邮箱
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> UpdateVaildStatus(UpdateTenantUserVaildInput input);

    Task SyncDingtalkUserId(SyncDingtalkUserIdMessage receive);

    Task UpdateJobId(UpdateJobIdInput input);

    Task SetEnabledByDingtalkUserLeave(SetEnabledByDingtalkUserLeaveInput input);
}