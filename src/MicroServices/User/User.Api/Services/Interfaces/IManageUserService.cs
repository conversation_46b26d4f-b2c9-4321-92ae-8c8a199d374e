using Contracts.Common.User.DTOs;
using Contracts.Common.User.DTOs.ManageUser;
using Contracts.Common.User.Messages;
using EfCoreExtensions.Abstract;

namespace User.Api.Services.Interfaces;

public interface IManageUserService
{
    /// <summary>
    /// 根据用户密码获取用户信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ManageUserDTO?> GetByPwd(AccountAndPwdInput input);

    /// <summary>
    /// 添加用户
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="ErrorTypes.User.AccountIsExist"></exception>
    /// <returns></returns>
    Task Add(AddStaffInput input);

    /// <summary>
    /// 重置密码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<string> RestPassword(RestPasswordInput input);

    /// <summary>
    /// 重置租户管理员密码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<string> RestTenantAdminPassword(RestTenantAdminPasswordInput input);

    /// <summary>
    /// 修改密码
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="ErrorTypes.User.PasswordError"></exception>
    /// <exception cref="ErrorTypes.User.AccountIsDisabled"></exception>
    /// <returns></returns>
    Task UpdateManageUserPwd(long manageUserId, UpdatePasswordInput input);

    /// <summary>
    /// 检索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<UserSearchOuput>> Search(UserSearchInput input);

    /// <summary>
    /// 启用/停用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> SetEnabled(SetUserEnabledInput input);

    /// <summary>
    /// 更新
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task Update(UpdateManagerUserInput input);

    /// <summary>
    /// 获取所有员工
    /// </summary>
    /// <returns></returns>
    Task<IEnumerable<GetAllStaffOutput>> GetAllStaff();

    /// <summary>
    /// 获取员工信息详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<ManageUserDTO?> GetInfo(long id);

    Task SyncManageUserRole(SyncManageUserRoleMessage receive);

    Task UpdateJobId(UpdateJobIdInput input);

    /// <summary>
    /// 根据ID批量获取用户信息
    /// </summary>
    /// <param name="userIds"></param>
    /// <returns></returns>
    Task<IEnumerable<ManageUserDTO>> GetListByIds(IEnumerable<long> userIds);


    Task<IEnumerable<ManageUserDTO>> GetListByJobIds(IEnumerable<string> jobIds);

    Task SetEnabledByDingtalkUserLeave(Contracts.Common.User.DTOs.TenantUser.SetEnabledByDingtalkUserLeaveInput input);
}