using AutoMapper;
using Common.GlobalException;
using Common.Jwt;
using Common.ServicesHttpClient;
using Common.Utils;
using Contracts.Common.Notify.Messages;
using Contracts.Common.Permission.DTOs.Role;
using Contracts.Common.Reflection;
using Contracts.Common.Tenant.DTOs.DingtalkApi;
using Contracts.Common.Tenant.Enums;
using Contracts.Common.Tenant.Messages;
using Contracts.Common.User.DTOs;
using Contracts.Common.User.DTOs.OperationLog;
using Contracts.Common.User.DTOs.TenantUser;
using Contracts.Common.User.Enums;
using Contracts.Common.User.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using User.Api.Extensions;
using User.Api.Services.Interfaces;

namespace User.Api.Services;

public class TenantUserService : ITenantUserService
{
    private const string ADMIN = "admin";
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IOptions<ServicesAddress> _servicesAddress;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ICapPublisher _capPublisher;

    public TenantUserService(CustomDbContext dbContext,
        IMapper mapper,
        IHttpContextAccessor httpContextAccessor,
        ICapPublisher capPublisher,
        IOptions<ServicesAddress> servicesAddress,
        IHttpClientFactory httpClientFactory
        )
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _httpContextAccessor = httpContextAccessor;
        _servicesAddress = servicesAddress;
        _httpClientFactory = httpClientFactory;
        _capPublisher = capPublisher;
    }

    public async Task Add(AddStaffInput input)
    {
        var isExist = await _dbContext.TenantUsers.AnyAsync(x => x.Account == input.Account);
        if (isExist)
            throw new BusinessException(ErrorTypes.User.AccountIsExist);

        var tenantId = _httpContextAccessor.HttpContext.GetTenantId();
        var tenantUser = _mapper.Map<TenantUser>(input);
        var setRoleTask = SetTenantUserRole(new SetTenantUserRoleInput
        {
            TenantId = tenantId,
            UserId = tenantUser.Id,
            RoleId = input.RoleId
        });

        tenantUser.Password = SecurityUtil.MD5Encrypt(tenantUser.Password, Encoding.UTF8);
        input.Password = tenantUser.Password; // 密码不保存明文
        var log = await CreateLog(input);
        log.OperationType = OperationType.Add;
        log.TabLogType = TabLogType.TenantUser;
        log.KeyId = tenantUser.Id;
        log.Content = "新增角色用户：" + PropertyInfoHelper.PropertyInfoMsg<AddStaffInput>(input);
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);

        if (!string.IsNullOrEmpty(tenantUser.JobId))
        {
            await _capPublisher.PublishAsync(CapTopics.Tenant.SyncDingtalkSearchUserInfo, new GetUserInput()
            {
                UserName = tenantUser.Name,
                TenantId = tenantUser.TenantId,
            });
        }

        await _dbContext.AddAsync(tenantUser);
        await _dbContext.SaveChangesAsync();

        try { await setRoleTask; }
        catch { }
    }

    private async Task SetTenantUserRole(SetTenantUserRoleInput input)
    {
        var url = _servicesAddress.Value.SetTenantUserRole();
        using var httpContent = new StringContent(JsonConvert.SerializeObject(input), Encoding.UTF8, "application/json");
        await _httpClientFactory.InternalPostAsync<string>(url, null, httpContent);
    }

    #region 订阅 - 添加租户admin用户

    public async Task AddAdmin(AddTenantUserMessage receive)
    {
        var admin = new TenantUser
        {
            Account = ADMIN,
            Name = ADMIN,
            NickName = ADMIN,
            Password = SecurityUtil.MD5Encrypt(receive.PhoneNumber, Encoding.UTF8),
            PhoneNumber = receive.PhoneNumber,
        };
        admin.SetTenantId(receive.TenantId);
        await _dbContext.AddAsync(admin);
        await SetTenantUserRole(new SetTenantUserRoleInput
        {
            TenantId = receive.TenantId,
            UserId = admin.Id,
            IsAdmin = true
        });
        await _dbContext.SaveChangesAsync();
    }

    #endregion

    private async Task<List<GetUserOutput>> GetDingtalkUser(GetUserInput input)
    {
        var url = _servicesAddress.Value.Tenant_GetDingtalkUser();
        using var httpContent = new StringContent(JsonConvert.SerializeObject(input), Encoding.UTF8, "application/json");
        return await _httpClientFactory.InternalPostAsync<List<GetUserOutput>>(url, null, httpContent);
    }

    public async Task Update(UpdateStaffInput input)
    {
        var tenantId = _httpContextAccessor.HttpContext.GetTenantId();
        var setRoleTask = SetTenantUserRole(new SetTenantUserRoleInput
        {
            TenantId = tenantId,
            UserId = input.UserId,
            RoleId = input.RoleId
        });

        var tenantUser = await _dbContext.TenantUsers.FindAsync(input.UserId);
        var oldData = new UpdateStaffInput
        {
            UserId = input.UserId,
            Email = tenantUser.Email,
            JobId = tenantUser.JobId,
            Name = tenantUser.Name,
            PhoneNumber = tenantUser.PhoneNumber,
            Qrcode = tenantUser.Qrcode
        };
        tenantUser.Name = input.Name;
        if (input.PhoneNumber != null && !input.PhoneNumber.Equals(tenantUser.PhoneNumber))
        {
            tenantUser.IsVaildPhoneNumber = false;
            tenantUser.PhoneNumber = input.PhoneNumber;
        }
        if (input.Email != null && !input.Email.Equals(tenantUser.Email))
        {
            tenantUser.IsVaildEmail = false;
            tenantUser.Email = input.Email;
        }
        tenantUser.JobId = input.JobId;
        tenantUser.Qrcode = input.Qrcode;
        if (!string.IsNullOrEmpty(tenantUser.JobId))
        {
            // 获取钉钉用户信息，并触发更新dingtalkUser表
            await _capPublisher.PublishAsync(CapTopics.Tenant.SyncDingtalkSearchUserInfo, new GetUserInput()
            {
                UserName = tenantUser.Name,
                TenantId = tenantUser.TenantId,
            });
        }

        var log = await CreateLog(input);
        log.OperationType = OperationType.Edit;
        log.TabLogType = TabLogType.TenantUser;
        log.KeyId = tenantUser.Id;
        input.PhoneNumber = tenantUser.PhoneNumber;
        input.Email = tenantUser.Email;
        log.Content = "编辑角色用户：" + PropertyInfoHelper.PropertyInfoMsg<UpdateStaffInput>(input);
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent
        {
            OldData = oldData,
            NewData = input
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);

        await setRoleTask;
        await _dbContext.SaveChangesAsync();

    }

    public async Task<string> RestPassword(RestPasswordInput input)
    {
        var tenantUser = await _dbContext.TenantUsers.FindAsync(input.UserId);
        input.NewPassword = string.IsNullOrWhiteSpace(input.NewPassword)
            ? AlphabetUtil.Generate(AlphabetType.Number | AlphabetType.LowerCase | AlphabetType.UpperCase, 8)
            : input.NewPassword;

        var oldPassword = tenantUser.Password;
        tenantUser.Password = SecurityUtil.MD5Encrypt(input.NewPassword, Encoding.UTF8);
        input.NewPassword = tenantUser.Password;
        var log = await CreateLog(input);
        log.OperationType = OperationType.Rest;
        log.TabLogType = TabLogType.TenantUser;
        log.KeyId = tenantUser.Id;
        log.Body = null;// 密码不保存
        log.Content = "重置密码";
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent
        {
            OldData = new { Password = oldPassword },
            NewData = new { Password = tenantUser.Password },
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);
        var rows = await _dbContext.SaveChangesAsync();
        return input.NewPassword;
    }

    public async Task UpdatePassword(long tenantUserId, UpdatePasswordInput input)
    {
        var tenantUser = await _dbContext.TenantUsers.FindAsync(tenantUserId);
        var oldPwd = SecurityUtil.MD5Encrypt(input.OldPassword, Encoding.UTF8);
        var newPwd = SecurityUtil.MD5Encrypt(input.NewPassword, Encoding.UTF8);

        if (tenantUser is null || tenantUser.Password != oldPwd)
            throw new BusinessException(ErrorTypes.User.PasswordError);
        if (tenantUser.Enabled == false)
            throw new BusinessException(ErrorTypes.User.AccountIsDisabled);

        tenantUser.Password = newPwd;
        input.NewPassword = tenantUser.Password;
        var log = await CreateLog(input);
        log.OperationType = OperationType.Rest;
        log.TabLogType = TabLogType.TenantUser;
        log.KeyId = tenantUser.Id;
        log.Body = null;// 密码不保存
        log.Content = "修改密码";
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent
        {
            OldData = new { Password = oldPwd },
            NewData = new { Password = tenantUser.Password },
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);

        var rows = await _dbContext.SaveChangesAsync();
    }

    [UnitOfWork]
    public async Task<bool> SetEnabled(SetUserEnabledInput input)
    {
        var tenantUser = await _dbContext.TenantUsers.FindAsync(input.UserId);
        tenantUser.Enabled = input.Enabled;
        var log = await CreateLog(input);
        log.OperationType = OperationType.Edit;
        log.TabLogType = TabLogType.TenantUser;
        log.KeyId = tenantUser.Id;
        log.Content = input.Enabled ? "恢复已停用员工" : "停用员工";
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent
        {
            OldData = new SetUserEnabledInput { UserId = input.UserId, Enabled = !input.Enabled },
            NewData = input
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);
        if (tenantUser.Enabled == false)
        {
            await _capPublisher.PublishAsync(CapTopics.Notify.DisableTenantUser, new RemoveNotifySettingByDisableUserMessage()
            {
                StaffIds = new List<long>() { input.UserId }
            });

            await _capPublisher.PublishAsync(CapTopics.Tenant.DisableTenantUser, new ClearAgencyByDisableUserMessage()
            {
                UserId = input.UserId
            });
        }
        return true;
    }

    public async Task<PagingModel<UserSearchOuput>> Search(UserSearchInput input)
    {
        var query = _dbContext.TenantUsers.AsNoTracking();
        switch (input.Status)
        {
            case 2:
                break;
            case 1:
                query = query.Where(x => x.Enabled == false);
                break;
            default:
                query = query.Where(x => x.Enabled == true);
                break;
        }

        var userRoleQuery = _dbContext.TenantUserRoles.AsNoTracking();
        var joinQuery = from user in query
                        join role in userRoleQuery on user.Id equals role.UserId into userRoles
                        from userRole in userRoles.DefaultIfEmpty()
                        select new { user, TenantRoleId = (userRole == null ? -1 : userRole.TenantRoleId) };
        var linq = joinQuery
            .WhereIF(string.IsNullOrWhiteSpace(input.Name) == false, s => s.user.Name.Contains(input.Name))
            .WhereIF(input.Ids.Any(), s => input.Ids.Contains(s.user.Id))
            .WhereIF(!input.Ids.Any(), s => s.user.Account != ADMIN)
            .WhereIF(input.RoleIds.Any(), s => input.RoleIds.Contains(s.TenantRoleId))
            .OrderByDescending(s => s.user.CreateTime)
            .Select(x => new UserSearchOuput()
            {
                Account = x.user.Account,
                CreateTime = x.user.CreateTime,
                Enabled = x.user.Enabled,
                Id = x.user.Id,
                Name = x.user.Name,
                NickName = x.user.NickName,
                PhoneNumber = x.user.PhoneNumber,
                Email = x.user.Email,
                JobId = x.user.JobId,
                Qrcode = x.user.Qrcode
            });

        var count = await linq.CountAsync();
        var userSearchOuputs = await linq.Skip((input.PageIndex - 1) * input.PageSize)
            .Take(input.PageSize)
            .ToListAsync();
        var jobIds = userSearchOuputs.Where(x => !string.IsNullOrEmpty(x.JobId)).Select(s => s.JobId).ToList();
        var dingtalkUsers = await _dbContext.DingtalkUsers.Where(x => jobIds.Contains(x.JobId)).AsNoTracking().ToListAsync();
        foreach (var item in userSearchOuputs)
        {
            var dingtalkUser = dingtalkUsers.FirstOrDefault(x => x.JobId == item.JobId);
            item.DingtalkIsDeptLeader = dingtalkUser?.DingtalkIsDeptLeader ?? false;
            item.DingtalkLeaderDeptId = dingtalkUser?.DingtalkLeaderDeptId;
            item.DingtalkDeptId = dingtalkUser?.DingtalkDeptId;
            item.DingtalkUnionid = dingtalkUser?.DingtalkUnionid;
            item.DingtalkUserId = dingtalkUser?.DingtalkUserId;
            item.DingtalkTitle = dingtalkUser?.DingtalkTitle;
            item.DingtalkBind = dingtalkUser != null;
        }
        return new PagingModel<UserSearchOuput>
        {
            Total = count,
            PageIndex = input.PageIndex,
            PageSize = input.PageSize,
            Data = userSearchOuputs
        };
    }

    public async Task<IEnumerable<UserSearchOuput>> SearchUsers(SearchTenantUsersInput input)
    {
        var dingtalkUsers = await _dbContext.DingtalkUsers.AsNoTracking()
            .WhereIF(input.DingtalkUserIds.Any(), s => input.DingtalkUserIds.Contains(s.DingtalkUserId))
            .ToListAsync();
        var jobIds = dingtalkUsers.Select(s => s.JobId).ToList();

        var users = await _dbContext.TenantUsers.AsNoTracking()
            .WhereIF(input.Enabled.HasValue, s => s.Enabled == input.Enabled)
            .WhereIF(string.IsNullOrWhiteSpace(input.Name) == false, s => s.Name.Contains(input.Name))
            .WhereIF(input.Ids.Any(), s => input.Ids.Contains(s.Id))
            .WhereIF(input.DingtalkUserIds.Any(), s => jobIds.Contains(s.JobId))// 指定了DingtalkUserId，就通过JobId关联
            .WhereIF(!input.Ids.Any(), s => s.Account != ADMIN)
            .ToListAsync();

        var bindings = await _dbContext.HuiZhiBindings
            .Where(s => s.SysRole == SysRole.Tenant)
            .Select(s => s.UserId)
        .ToListAsync();
        var res = _mapper.Map<List<UserSearchOuput>>(users);
        foreach (var item in res)
        {
            item.WechatBind = bindings.Any(s => s == item.Id);
            var dingtalkUser = dingtalkUsers.FirstOrDefault(x => x.JobId == item.JobId);
            item.DingtalkIsDeptLeader = dingtalkUser?.DingtalkIsDeptLeader ?? false;
            item.DingtalkLeaderDeptId = dingtalkUser?.DingtalkLeaderDeptId;
            item.DingtalkDeptId = dingtalkUser?.DingtalkDeptId;
            item.DingtalkUnionid = dingtalkUser?.DingtalkUnionid;
            item.DingtalkUserId = dingtalkUser?.DingtalkUserId;
            item.DingtalkTitle = dingtalkUser?.DingtalkTitle;
        }
        return res;
    }

    public async Task<TenantUserRoleInfoDto?> FindTenantUserRoles(long tenantUserId)
    {
        var userRole = await _dbContext.TenantUserRoles.AsNoTracking().FirstOrDefaultAsync(x => x.UserId == tenantUserId);
        return _mapper.Map<TenantUserRoleInfoDto>(userRole);
    }

    public async Task<UserSearchOuput?> FindOne(Contracts.Common.User.DTOs.SupplierUser.FindOneInput input)
    {
        if (input.UserId == null && string.IsNullOrEmpty(input.UserName) && string.IsNullOrEmpty(input.Name))
            throw new Exception();
        var pwd = string.IsNullOrWhiteSpace(input.Password)
            ? null
            : SecurityUtil.MD5Encrypt(input.Password, Encoding.UTF8);
        var user = await _dbContext.TenantUsers.AsNoTracking()
            .WhereIF(input.UserId.HasValue, x => x.Id == input.UserId!)
            .WhereIF(!string.IsNullOrEmpty(input.UserName), x => x.Account == input.UserName!)
            .WhereIF(!string.IsNullOrEmpty(pwd), x => x.Password == pwd!)
            .WhereIF(!string.IsNullOrEmpty(input.Name), x => x.Name == input.Name!)
            .FirstOrDefaultAsync();
        if (user == null)
            return null;
        var dingtalkUser = await _dbContext.DingtalkUsers.Where(x => x.JobId == user.JobId).FirstOrDefaultAsync();
        var res = _mapper.Map<UserSearchOuput>(user);
        res.DingtalkIsDeptLeader = dingtalkUser?.DingtalkIsDeptLeader ?? false;
        res.DingtalkLeaderDeptId = dingtalkUser?.DingtalkLeaderDeptId;
        res.DingtalkDeptId = dingtalkUser?.DingtalkDeptId;
        res.DingtalkUnionid = dingtalkUser?.DingtalkUnionid;
        res.DingtalkUserId = dingtalkUser?.DingtalkUserId;
        res.DingtalkTitle = dingtalkUser?.DingtalkTitle;
        return res;
    }

    public async Task<IEnumerable<GetAllStaffOutput>> GetAllStaff(bool OnlyDingtalk = false)
    {
        var users = await _dbContext.TenantUsers
            .Where(s => s.Enabled == true && s.Account != ADMIN)
            .ToListAsync();

        var dingtalkUsers = await _dbContext.DingtalkUsers
           .ToListAsync();

        var bindings = await _dbContext.HuiZhiBindings
            .Where(s => s.SysRole == SysRole.Tenant)
            .Select(s => s.UserId)
            .ToListAsync();
        var result = new List<GetAllStaffOutput>();
        foreach (var item in users)
        {
            var user = new GetAllStaffOutput
            {
                UserId = item.Id,
                Name = item.Name,
            };
            if (OnlyDingtalk)
            {
                var dingtalkUser = dingtalkUsers.FirstOrDefault(s => s.JobId == item.JobId);
                if (dingtalkUser == null)
                    continue;
                user.DingtalkUserId = dingtalkUser.DingtalkUserId;
            }
            user.WechatBind = bindings.Any(s => s == item.Id);
            result.Add(user);
        }
        return result;
    }

    /// <summary>
    /// 订阅 - 同步冗余用户角色数据
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    public async Task SyncTenantUserRole(SyncTenantUserRoleMessage receive)
    {
        if (receive.Type == 1)
        {
            var userRoles = await _dbContext.TenantUserRoles
                           .WhereIF(receive.Id > 0, x => x.Id == receive.Id)
                           .WhereIF(receive.TenantRoleId > 0, x => x.TenantRoleId == receive.TenantRoleId)
                           .WhereIF(receive.UserId > 0, x => x.UserId == receive.UserId)
                           .Where(x => x.TenantId == receive.TenantId)
                           .ToListAsync();

            _dbContext.TenantUserRoles.RemoveRange(userRoles);
        }
        else
        {
            var userRole = await _dbContext.TenantUserRoles.FirstOrDefaultAsync(x => x.Id == receive.Id);
            if (userRole == null)
            {
                userRole = _mapper.Map<TenantUserRole>(receive);
                await _dbContext.AddAsync(userRole);
            }
            else
            {
                userRole.TenantRoleId = receive.TenantRoleId;
                userRole.UserId = receive.UserId;
            }
        }
        await _dbContext.SaveChangesAsync();
    }

    public async Task<bool> UpdateName(UpdateTenantUserNameInput input)
    {
        var tenantUser = await _dbContext.TenantUsers.FindAsync(input.Id);
        if (tenantUser == null)
            return false;
        var oldData = new UpdateTenantUserNameInput()
        {
            Id = input.Id,
            Name = tenantUser.Name,
        };
        tenantUser.Name = input.Name;
        await _dbContext.SaveChangesAsync();

        var log = await CreateLog(input);
        log.OperationType = OperationType.Edit;
        log.TabLogType = TabLogType.TenantUser;
        log.KeyId = tenantUser.Id;
        log.Content = $"修改个人资料：{input.Name}";
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent
        {
            OldData = oldData,
            NewData = input
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);

        return true;
    }

    public async Task<bool> UpdateVaildStatus(UpdateTenantUserVaildInput input)
    {
        var tenantUser = await _dbContext.TenantUsers.FindAsync(input.Id);
        if (tenantUser == null)
            return false;
        var oldData = new UpdateTenantUserVaildInput() { Id = input.Id, ContactType = input.ContactType };
        if (input.ContactType == TenantUserVaildContactType.Phone)
        {
            tenantUser.IsVaildPhoneNumber = true;
            tenantUser.PhoneNumber = input.ContactTypeValue;
            oldData.ContactTypeValue = tenantUser.PhoneNumber;
        }
        else if (input.ContactType == TenantUserVaildContactType.Email)
        {
            tenantUser.IsVaildEmail = true;
            tenantUser.Email = input.ContactTypeValue;
            oldData.ContactTypeValue = tenantUser.Email;
        }
        var tenantUserFingerPrints = await _dbContext.TenantUserSecurityVerifications
            .Where(x => x.UserId == tenantUser.Id)
            .ToListAsync();
        if (tenantUserFingerPrints.Any())
            _dbContext.TenantUserSecurityVerifications.RemoveRange(tenantUserFingerPrints);

        await _dbContext.SaveChangesAsync();

        var log = await CreateLog(input);
        log.OperationType = OperationType.Validator;
        log.TabLogType = TabLogType.TenantUser;
        log.KeyId = tenantUser.Id;
        log.Content = input.ContactType == TenantUserVaildContactType.Phone ? $"验证手机：{Common.Utils.DataSecrecyUtil.PhoneSensitive(input.ContactTypeValue)}" : $"验证邮箱：{Common.Utils.DataSecrecyUtil.EmailSensitive(input.ContactTypeValue)}";
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent
        {
            OldData = oldData,
            NewData = input
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);

        return true;
    }

    public async Task<OperationLogDto> CreateLog(object input)
    {
        CurrentUser currentUser = _httpContextAccessor.HttpContext.GetCurrentUser();
        var request = _httpContextAccessor.HttpContext.Request;
        var ip = _httpContextAccessor.HttpContext.GetRemotingIp();
        var log = new OperationLogDto()
        {
            OperationType = OperationType.Add,
            System = SystemType.Vebk,
            Host = request.Host.ToString(),
            Url = request.Path.ToString(),
            Agent = request.Headers.UserAgent,
            Ip = ip,
            Query = JsonConvert.SerializeObject(request.Query),
            Body = JsonConvert.SerializeObject(input),
            OperationUserId = currentUser?.userid,
            OperationUserName = currentUser?.nickname,
            TenantId = currentUser?.tenant,
        };
        return log;
    }

    [UnitOfWork]
    public async Task SyncDingtalkUserId(SyncDingtalkUserIdMessage receive)
    {
        var users = await _dbContext.TenantUsers
                             .Where(x => x.TenantId == receive.TenantId)
                             .Where(x => !string.IsNullOrEmpty(x.JobId))
                             .ToListAsync();
        foreach (var tenantUser in users)
        {
            if (!string.IsNullOrEmpty(tenantUser.JobId) && !string.IsNullOrEmpty(tenantUser.Name))
            {
                // 获取钉钉用户信息，并触发更新dingtalkUser表
                await _capPublisher.PublishAsync(CapTopics.Tenant.SyncDingtalkSearchUserInfo, new GetUserInput()
                {
                    UserName = tenantUser.Name,
                    TenantId = tenantUser.TenantId,
                });
            }
        }
    }

    public async Task UpdateJobId(UpdateJobIdInput input)
    {
        var user = await _dbContext.TenantUsers.FindAsync(input.UserId);

        var dingtalkUser = await _dbContext.DingtalkUsers
            .Where(x => x.JobId == input.JobId)
            .FirstOrDefaultAsync();
        if (dingtalkUser == null)
            throw new BusinessException(ErrorTypes.User.DingtalkUserIdBindError);

        var oldData = new UpdateJobIdInput
        {
            UserId = input.UserId,
            Name = user.Name,
            JobId = user.JobId,
            DingtalkUserId = dingtalkUser.DingtalkUserId,
        };
        user.Name = input.Name;
        user.JobId = input.JobId;// 查询钉钉用户信息，并触发更新dingtalkUser表了，通过 jobId 进行关联

        var log = await CreateLog(input);
        log.OperationType = OperationType.Edit;
        log.TabLogType = TabLogType.TenantUser;
        log.KeyId = user.Id;
        log.Content = "员工钉钉绑定：" + PropertyInfoHelper.PropertyInfoMsg<UpdateJobIdInput>(input);
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent()
        {
            OldData = oldData,
            NewData = input
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);
        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 钉钉员工离职通知同步,停止对应钉钉工号账号
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [UnitOfWork]
    public async Task SetEnabledByDingtalkUserLeave(SetEnabledByDingtalkUserLeaveInput input)
    {
        var dingtalkUserJobIds = await _dbContext.DingtalkUsers
            .Where(x => input.DingtalkUserIds.Contains(x.DingtalkUserId) && !string.IsNullOrEmpty(x.DingtalkUserId))
            .Where(x => !string.IsNullOrEmpty(x.JobId))
            .Where(x => x.TenantId == input.TenantId)
            .Select(x => x.JobId).ToListAsync();
        var tenantUsers = await _dbContext.TenantUsers
            .Where(x => dingtalkUserJobIds.Contains(x.JobId))
            .Where(x => x.TenantId == input.TenantId)
            .Where(x => x.Enabled != false)
            .ToListAsync();
        if (tenantUsers.Any())
        {
            foreach (var tenantUser in tenantUsers)
            {
                var log = await CreateLog(input);
                log.OperationType = OperationType.Edit;
                log.TabLogType = TabLogType.TenantUser;
                log.KeyId = tenantUser.Id;
                log.Content = "停用员工-钉钉离职通知";
                log.OperationUserName = "System";
                log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent
                {
                    OldData = new SetUserEnabledInput { UserId = tenantUser.Id, Enabled = tenantUser.Enabled },
                    NewData = new SetUserEnabledInput { UserId = tenantUser.Id, Enabled = false },
                });

                tenantUser.Enabled = false;

                await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);

                await _capPublisher.PublishAsync(CapTopics.Notify.DisableTenantUser, new RemoveNotifySettingByDisableUserMessage()
                {
                    StaffIds = new List<long>() { tenantUser.Id }
                });

                await _capPublisher.PublishAsync(CapTopics.Tenant.DisableTenantUser, new ClearAgencyByDisableUserMessage()
                {
                    UserId = tenantUser.Id
                });

            }
        }
    }
}
