using Common.Jwt;
using Contracts.Common.User.DTOs.UserBinding;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using User.Api.Services.Interfaces;

namespace User.Api.Services;

public class UserBindingService : IUserBindingService
{
    private readonly CustomDbContext _dbContext;

    public UserBindingService(CustomDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task AddOrUpdateBinding(AddOrUpdateBindingInput input)
    {
        Common.Jwt.SysRole sysRole = input.SysRole switch
        {
            Contracts.Common.User.Enums.UserBindSysRoleType.Manager => Common.Jwt.SysRole.Manager,
            Contracts.Common.User.Enums.UserBindSysRoleType.Tenant => Common.Jwt.SysRole.Tenant,
            Contracts.Common.User.Enums.UserBindSysRoleType.Customer => Common.Jwt.SysRole.Customer,
            Contracts.Common.User.Enums.UserBindSysRoleType.Supplier => Common.Jwt.SysRole.Supplier,
            Contracts.Common.User.Enums.UserBindSysRoleType.Agency => Common.Jwt.SysRole.Agency,
            Contracts.Common.User.Enums.UserBindSysRoleType.Visitor => Common.Jwt.SysRole.Visitor,
            _ => throw new NotImplementedException()
        };
        var userBinding = await _dbContext.UserBindings
            .Where(s => s.UserId == input.UserId && s.SysRole == sysRole && s.PlatformType == input.PlatformType)
            .FirstOrDefaultAsync();
        if (userBinding?.Code == input.Code)
        {
            userBinding.Enabled = input.Enabled;
            userBinding.UpdateTime = DateTime.Now;
        }
        else
        {
            if (userBinding is not null)
            {
                userBinding.Enabled = false;
                userBinding.UpdateTime = DateTime.Now;
            }
            UserBinding binding = new()
            {
                UserId = input.UserId,
                SysRole = sysRole,
                PlatformType = input.PlatformType,
                Code = input.Code,
                Enabled = input.Enabled,
                CreateTime = DateTime.Now,
            };
            binding.SetTenantId(input.TenantId);
            await _dbContext.AddAsync(binding);
        }
        await _dbContext.SaveChangesAsync();
    }

    public async Task<List<GetUserBindingOutput>> GetUserBindings(GetUserBindingInput input)
    {
        var userBindings = await _dbContext.UserBindings
            .Where(s => s.Enabled && input.UserIds.Contains(s.UserId))
            .WhereIF(input.PlatformType.HasValue, s => s.PlatformType == input.PlatformType)
            .WhereIF(input.SysRole.HasValue, s => s.SysRole == (SysRole)input.SysRole!)
            .Select(s => new GetUserBindingOutput
            {
                PlatformType = s.PlatformType,
                Code = s.Code,
                UserId = s.UserId,
            })
            .ToListAsync();
        return userBindings;
    }

    public async Task<bool> Unbound(UnboundUserBindingInput input)
    {
        var entity = await _dbContext.UserBindings
            .FirstOrDefaultAsync(s => s.UserId == input.UserId && s.SysRole == (SysRole)input.SysRole);
        if (entity is null)
            return false;
        _dbContext.UserBindings.Remove(entity);
        var rows = await _dbContext.SaveChangesAsync();
        return rows > 0;
    }
}
