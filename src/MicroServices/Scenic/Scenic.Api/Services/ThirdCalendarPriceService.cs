using AutoMapper;
using Cit.Storage.Redis;
using Common.GlobalException;
using Contracts.Common.Inventory.DTOs;
using Contracts.Common.Inventory.Messages;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.DTOs.Currency;
using Contracts.Common.Product.DTOs.OpenPlatformPricingSyncLog;
using Contracts.Common.Product.DTOs.ProductPriceAdjustment;
using Contracts.Common.Product.Enums;
using Contracts.Common.Product.Messages;
using Contracts.Common.Scenic.DTOs.OpenChannel;
using Contracts.Common.Scenic.DTOs.Ticket;
using Contracts.Common.Scenic.DTOs.TicketChannelSetting;
using Contracts.Common.Scenic.DTOs.TicketsCalendarPrice;
using Contracts.Common.Scenic.Enums;
using Contracts.Common.Scenic.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Microsoft.EntityFrameworkCore;
using Scenic.Api.Services.Interfaces;
using Scenic.Api.Services.OpenPlatform.Contracts.Channel;
using Scenic.Api.Services.OpenPlatform.Contracts.Supplier;
using Scenic.Api.Services.OpenPlatform.Interfaces;

namespace Scenic.Api.Services;

/// <summary>
/// 第三方日历价
/// </summary>
public class ThirdCalendarPriceService : IThirdCalendarPriceService
{
    private readonly CustomDbContext _dbContext;
    private readonly IOpenSupplierService _openSupplierService;
    private readonly IOpenChannelService _openChannelService;
    private readonly IOpenPlatformBaseService _openPlatformBaseService;
    private readonly IBaseService _baseService;
    private readonly ICapPublisher _capPublisher;
    private readonly ILogger<ThirdCalendarPriceService> _logger;
    private readonly IMapper _mapper;
    private readonly IRedisClient _redisClient;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private static readonly SemaphoreSlim _semaphore = new(10,10);

    private readonly string _timeSlotFormat = @"hh\:mm";
    private const int _semaphoreTimeoutSeconds = 15;//超时时间
    private const int _successCode = 200;

    public ThirdCalendarPriceService(
        CustomDbContext dbContext,
        IOpenSupplierService openSupplierService,
        IOpenChannelService openChannelService,
        IOpenPlatformBaseService openPlatformBaseService,
        IBaseService baseService,
        ICapPublisher capPublisher,
        ILogger<ThirdCalendarPriceService> logger,
        IMapper mapper,
        IRedisClient redisClient,
        IServiceScopeFactory serviceScopeFactory)
    {
        _dbContext = dbContext;
        _openSupplierService = openSupplierService;
        _openChannelService = openChannelService;
        _openPlatformBaseService = openPlatformBaseService;
        _baseService = baseService;
        _capPublisher = capPublisher;
        _logger = logger;
        _mapper = mapper;
        _redisClient = redisClient;
        
        _serviceScopeFactory = serviceScopeFactory;
    }

    [UnitOfWork]
    public async Task Update(UpdateThirdCalendarPriceInput input)
    {
        var previews = new List<TicketsCalendarPricePreview>();
        var newCalendarPrices = new List<TicketsCalendarPrice>();
        var syncCalendarPrices = new List<TicketsCalendarPrice>();
        var syncSaasTimeSlotCalendarInventories = new List<BatchSyncThirdTimeSlotInventoryItem>(); //saas时段库存同步
        var syncSaasCalendarInventories = new List<BatchSyncThirdCalendarInventoryItem>(); //saas日历库存同步

        var ticket = await _dbContext.Tickets.AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == input.TicketsId);
        if (ticket.SupplierId <= 0)
            throw new BusinessException(ErrorTypes.Tenant.SupplierInvalid); //未配置供应商id不可配置价格
        
        var minDate = input.CalendarPriceItems.MinBy(x => x.BeginDate).BeginDate;
        var maxDate = input.CalendarPriceItems.MaxBy(x => x.EndDate).EndDate;

        //查询采购价转售价币种汇率
        var saasCostPriceExchangeRate = await _baseService.GetExchangeRate(ticket.CostCurrencyCode, ticket.SaleCurrencyCode);

        //第三方价库的票种
        if (ticket.PriceInventorySource == PriceInventorySource.System)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        //校验是否交叉
        var isOverLap = CheckForDateOverLap(input.CalendarPriceItems);
        if (isOverLap)
            throw new BusinessException(ErrorTypes.Scenic.PriceDuplicationOfDate);

        //查询门票供应端- 同步配置信息
        var openSupplierSetting = await _dbContext.TicketOpenSupplierSettings.AsNoTracking()
            .FirstOrDefaultAsync(x => x.TicketId == input.TicketsId);
        var openSupplierPriceInventorySyncType = openSupplierSetting?.PriceInventorySyncType ?? PriceInventorySyncType.NoNeedSync;

        //查询开放平台供应商类型
        var openSupplierTypeInfo = await GetTicketOpenSupplierType(ticket.SupplierId);

        decimal exchangeRate = 1;//计算汇率.api供应商币种换算到门票配置的采购币种
        var thirdStocks = new List<SyncThirdInventorySchedulesDto>();
        if (openSupplierPriceInventorySyncType != PriceInventorySyncType.NoNeedSync)
        {
            //查询产品信息和价库.供应商不返回币种.默认门票配置的供应商币种
            var aggregateQuery = await AggregateQueryProductDetailAndStocks(
                new()
                {
                    ActivityId = ticket.ActivityId,
                    PackageId = ticket.PackageId,
                    SkuId = ticket.SkuId,
                    OpenSupplierType = openSupplierTypeInfo.openSupplierType!.Value,
                    ScheduleStartDate = minDate,
                    ScheduleEndDate = maxDate.AddDays(1) //兼容时段场次的日历价库.范围多查一天
                }, ticket.TenantId);
            
            //记录同步日志
            var addSyncLogs = new AddSyncLogInput();
            addSyncLogs.Logs.Add(new AddSyncLogItem()
                {
                    TenantId = ticket.TenantId,
                    ProductType = ProductType.Scenic,
                    PlatformType = OpenPlatformType.OpenSupplier,
                    SyncType = OpenPlatformPricingSyncType.ActivePush,
                    SyncResult = (aggregateQuery.SkuDetail != null && aggregateQuery.Schedules.Any())
                        ? OpenPlatformPricingSyncResult.Success
                        : OpenPlatformPricingSyncResult.Failed,
                    ProductId = ticket.Id,
                    ProductName = ticket.Name,
                    SkuId = ticket.Id,
                    SkuName = ticket.Name,
                    SyncStartDate = minDate,
                    SyncEndDate = maxDate,
                    SupplierIsSale = aggregateQuery.SkuDetail != null
                }
            );
            _ = _baseService.AddOpenPlatformPricingSyncLogs(addSyncLogs);
            
            var thirdSkuDetail = aggregateQuery.SkuDetail;
            if (thirdSkuDetail is null)
                throw new BusinessException(ErrorTypes.Common.ThirdProductConfigurationError);
            
            thirdStocks = _mapper.Map<List<SyncThirdInventorySchedulesDto>>(aggregateQuery.Schedules);

            //api供应商币种
            var thirdCostCurrency = thirdSkuDetail.Currency ?? ticket.CostCurrencyCode;
            //计算汇率.api供应商币种换算到门票配置的采购币种
            exchangeRate = await _baseService.GetExchangeRate(thirdCostCurrency, ticket.CostCurrencyCode);
            
            //特殊处理价库
            if (thirdSkuDetail.IsTimeSlot == false && thirdSkuDetail.TimeSlot.Any() is false)
            {
                thirdStocks = SpecialScheduleProcess(thirdStocks);
            }
        }

        //已存在的日历价
        var dbCalendarPrices = await _dbContext.TicketsCalendarPrices
            .Where(x => x.TicketsId == input.TicketsId && x.Date.Date >= minDate.Date
                                                       && x.Date.Date <= maxDate.Date)
            .Where(x => input.PriceChannelTypes.Contains(x.PriceChannelType))
            .ToListAsync();

        //时段信息
        var syncTimeSlots = await _dbContext.TicketsTimeSlots
            .Where(x => x.TicketsId == input.TicketsId)
            .Where(x => x.Enabled)
            .OrderBy(x => x.Time)
            .ToListAsync();
        var timeSlots = syncTimeSlots.Where(x => input.TimeSlotIds.Contains(x.Id)).ToList();


        //循环插入最新日历价格数据
        foreach (var priceChannelType in input.PriceChannelTypes)
        {
            var channelDbCalendarPrices = dbCalendarPrices.Where(x => x.PriceChannelType == priceChannelType)
                .ToList();
            foreach (var priceItem in input.CalendarPriceItems)
            {
                //价格调整类型为指定，且调整值为null时，赋值为售价,兼容旧数据
                if (priceItem is
                    {
                        PriceBasisType: PriceBasisType.Designated, PriceAdjustmentType: PriceAdjustmentType.Designated,
                        PriceAdjustmentValue: null
                    })
                {
                    priceItem.PriceAdjustmentValue = priceItem.SellingPrice;
                }

                #region 日历价区间预览保存

                if (input.TimeSlotIds.Any())
                {
                    previews.AddRange(input.TimeSlotIds.Select(item => new TicketsCalendarPricePreview
                    {
                        TicketsId = ticket.Id,
                        TimeSlotId = item,
                        BeginDate = priceItem.BeginDate,
                        EndDate = priceItem.EndDate,
                        Week = string.Join(",", priceItem.Weeks.OrderBy(x => x).Select(x => (byte)x)),
                        SellingPrice = priceItem.SellingPrice,
                        PriceChannelType = priceChannelType,
                        PriceBasisType = priceItem.PriceBasisType,
                        PriceAdjustmentType = priceItem.PriceAdjustmentType,
                        PriceAdjustmentValue = priceItem.PriceAdjustmentValue
                    }));
                }
                else
                {
                    var preview = new TicketsCalendarPricePreview
                    {
                        TicketsId = ticket.Id,
                        BeginDate = priceItem.BeginDate,
                        EndDate = priceItem.EndDate,
                        Week = string.Join(",", priceItem.Weeks.OrderBy(x => x).Select(x => (byte)x)),
                        SellingPrice = priceItem.SellingPrice,
                        PriceChannelType = priceChannelType,
                        PriceBasisType = priceItem.PriceBasisType,
                        PriceAdjustmentType = priceItem.PriceAdjustmentType,
                        PriceAdjustmentValue = priceItem.PriceAdjustmentValue
                    };
                    previews.Add(preview);
                }

                #endregion

                #region 日历价数据保存

                var days = priceItem.EndDate.Subtract(priceItem.BeginDate).TotalDays;
                for (int i = 0; i <= days; i++)
                {
                    var d = priceItem.BeginDate.AddDays(i);
                    if (!priceItem.Weeks.Contains(d.DayOfWeek)) continue;

                    if (timeSlots.Any())
                    {
                        //时段的日历价
                        foreach (var timeSlot in timeSlots)
                        {
                            //查询时段日历价信息
                            var currentTimeSlotCalendarPrice = channelDbCalendarPrices
                                .Where(x => x.Date.Date == d && x.TimeSlotId == timeSlot.Id)
                                .MaxBy(x => x.CreateTime);

                            decimal? currentScheduleCostPrice = currentTimeSlotCalendarPrice?.CostPrice;
                            //如果开启了供应端的价格同步,则采购价获取来源是供应端的值.
                            if (openSupplierPriceInventorySyncType is PriceInventorySyncType.SyncPrice
                                or PriceInventorySyncType.SyncAll)
                            {
                                //查询供货端采购价
                                var currentThirdSchedule = thirdStocks
                                    .FirstOrDefault(x => x.Date == d.Add(timeSlot.Time));
                                if (currentThirdSchedule is { Price: not null })
                                {
                                    currentScheduleCostPrice = Math.Round(currentThirdSchedule.Price!.Value * exchangeRate, 2);
                                }
                            }
                            else
                            {
                                //未开启价格同步,采购价获取来源是门票配置的采购价
                                if (priceItem.CostPrice != null)
                                {
                                    //不配置不更新
                                    currentScheduleCostPrice = priceItem.CostPrice;
                                }
                            }
                            
                            //计算价格调整后的售价,前端不填则按现有的配置值计算
                            var sellingPriceBasisType = priceItem.PriceBasisType;
                            var sellingPriceAdjustmentType = priceItem.PriceAdjustmentType;
                            var sellingPriceAdjustmentValue = priceItem.PriceAdjustmentValue;
                            if (!priceItem.PriceAdjustmentValue.HasValue && currentTimeSlotCalendarPrice != null)
                            {
                                //默认按现有配置计算
                                sellingPriceBasisType = currentTimeSlotCalendarPrice.PriceBasisType;
                                sellingPriceAdjustmentType= currentTimeSlotCalendarPrice.PriceAdjustmentType;
                                sellingPriceAdjustmentValue = currentTimeSlotCalendarPrice.PriceAdjustmentValue;
                            }
                            var adjustmentSellingPrice = _baseService.CalcAdjustmentSellingPrice(
                                new CalcPriceAdjustmentInput
                                {
                                    PriceBasisType = sellingPriceBasisType,
                                    PriceAdjustmentType = sellingPriceAdjustmentType,
                                    PriceAdjustmentValue = sellingPriceAdjustmentValue,
                                    CostPrice = currentScheduleCostPrice,
                                    CostPriceExchangeRate = saasCostPriceExchangeRate
                                });

                            if (currentTimeSlotCalendarPrice != null)
                            {
                                //时段日历价不为空
                                currentTimeSlotCalendarPrice.CostPrice = currentScheduleCostPrice;
                                currentTimeSlotCalendarPrice.TimeSlotId = timeSlot.Id;
                                currentTimeSlotCalendarPrice.SellingPrice = adjustmentSellingPrice;
                                currentTimeSlotCalendarPrice.PriceBasisType = sellingPriceBasisType;
                                currentTimeSlotCalendarPrice.PriceAdjustmentType = sellingPriceAdjustmentType;
                                currentTimeSlotCalendarPrice.PriceAdjustmentValue = sellingPriceAdjustmentValue;
                            }
                            else
                            {
                                //时段日历价为空
                                newCalendarPrices.Add(new TicketsCalendarPrice
                                {
                                    ScenicSpotId = ticket.ScenicSpotId,
                                    TicketsId = input.TicketsId,
                                    TimeSlotId = timeSlot.Id,
                                    Date = d.Add(timeSlot.Time),
                                    SellingPrice = adjustmentSellingPrice,
                                    CostPrice = currentScheduleCostPrice,
                                    PriceChannelType = priceChannelType,
                                    PriceBasisType = sellingPriceBasisType,
                                    PriceAdjustmentType = sellingPriceAdjustmentType,
                                    PriceAdjustmentValue = sellingPriceAdjustmentValue
                                });
                            }

                            //saas时段配置库存.未开启全部同步和指定库存同步时,支持手动更新库存
                            if (openSupplierPriceInventorySyncType != PriceInventorySyncType.SyncAll
                                && openSupplierPriceInventorySyncType != PriceInventorySyncType.SyncInventory)
                            {
                                if (priceItem.TotalQuantity.HasValue)
                                {
                                    //更新saas时段日历库存
                                    syncSaasTimeSlotCalendarInventories.Add(new BatchSyncThirdTimeSlotInventoryItem
                                    {
                                        ProductId = ticket.ScenicSpotId,
                                        SkuId = ticket.Id,
                                        TimeSlotId = timeSlot.Id,
                                        Date = d.Date,
                                        Time = timeSlot.Time,
                                        TotalQuantity = priceItem.TotalQuantity!.Value,
                                        AvailableQuantity = priceItem.TotalQuantity!.Value
                                    });
                                }
                            }
                        }
                    }
                    else
                    {
                        //普通日历价
                        //判断该日历是否存在
                        var currentDateCalendarPrice = channelDbCalendarPrices
                            .Where(x => x.Date == d)
                            .MaxBy(x => x.CreateTime);

                        //如果开启了供应端的价格同步,则采购价获取来源是供应端的值.
                        decimal? currentScheduleCostPrice = currentDateCalendarPrice?.CostPrice;
                        if (openSupplierPriceInventorySyncType is PriceInventorySyncType.SyncPrice
                            or PriceInventorySyncType.SyncAll)
                        {
                            var currentSchedule = thirdStocks
                                .FirstOrDefault(x => x.Date == d);
                            if (currentSchedule is { Price: not null })
                            {
                                currentScheduleCostPrice = Math.Round(currentSchedule.Price!.Value * exchangeRate, 2);
                            }
                        }
                        else
                        {
                            //未开启价格同步,采购价获取来源是门票配置的采购价
                            if (priceItem.CostPrice != null)
                            {
                                //不配置不更新
                                currentScheduleCostPrice = priceItem.CostPrice;
                            }
                        }

                        //计算价格调整后的售价,前端不填则按现有的配置值计算
                        var sellingPriceBasisType = priceItem.PriceBasisType;
                        var sellingPriceAdjustmentType = priceItem.PriceAdjustmentType;
                        var sellingPriceAdjustmentValue = priceItem.PriceAdjustmentValue;
                        if (!priceItem.PriceAdjustmentValue.HasValue && currentDateCalendarPrice != null)
                        {
                            //默认按现有配置计算
                            sellingPriceBasisType = currentDateCalendarPrice.PriceBasisType;
                            sellingPriceAdjustmentType= currentDateCalendarPrice.PriceAdjustmentType;
                            sellingPriceAdjustmentValue = currentDateCalendarPrice.PriceAdjustmentValue;
                        }
                        var adjustmentSellingPrice = _baseService.CalcAdjustmentSellingPrice(
                            new CalcPriceAdjustmentInput
                            {
                                PriceBasisType = sellingPriceBasisType,
                                PriceAdjustmentType = sellingPriceAdjustmentType,
                                PriceAdjustmentValue = sellingPriceAdjustmentValue,
                                CostPrice = currentScheduleCostPrice,
                                CostPriceExchangeRate = saasCostPriceExchangeRate
                            });

                        if (currentDateCalendarPrice != null)
                        {
                            //日历价不为空
                            currentDateCalendarPrice.CostPrice = currentScheduleCostPrice;
                            currentDateCalendarPrice.TimeSlotId = null;
                            currentDateCalendarPrice.SellingPrice = adjustmentSellingPrice;
                            currentDateCalendarPrice.PriceBasisType = sellingPriceBasisType;
                            currentDateCalendarPrice.PriceAdjustmentType = sellingPriceAdjustmentType;
                            currentDateCalendarPrice.PriceAdjustmentValue = sellingPriceAdjustmentValue;
                        }
                        else
                        {
                            //日历价为空
                            newCalendarPrices.Add(new TicketsCalendarPrice
                            {
                                ScenicSpotId = ticket.ScenicSpotId,
                                TicketsId = input.TicketsId,
                                Date = d,
                                SellingPrice = adjustmentSellingPrice,
                                CostPrice = currentScheduleCostPrice,
                                PriceChannelType = priceChannelType,
                                PriceBasisType = sellingPriceBasisType,
                                PriceAdjustmentType = sellingPriceAdjustmentType,
                                PriceAdjustmentValue = sellingPriceAdjustmentValue
                            });
                        }

                        //saas配置库存.未开启全部同步和指定库存同步时,支持手动更新库存
                        if (openSupplierPriceInventorySyncType != PriceInventorySyncType.SyncAll
                            && openSupplierPriceInventorySyncType != PriceInventorySyncType.SyncInventory)
                        {
                            if (priceItem.TotalQuantity.HasValue)
                            {
                                syncSaasCalendarInventories.Add(new BatchSyncThirdCalendarInventoryItem
                                {
                                    ProductId = ticket.ScenicSpotId,
                                    SkuId = ticket.Id,
                                    Date = d.Date,
                                    TotalQuantity = priceItem.TotalQuantity!.Value,
                                    AvailableQuantity = priceItem.TotalQuantity!.Value
                                });
                            }
                        }
                    }
                }

                #endregion
            }
        }

        await _dbContext.TicketsCalendarPricePreviews.AddRangeAsync(previews);
        await _dbContext.TicketsCalendarPrices.AddRangeAsync(newCalendarPrices);

        syncCalendarPrices.AddRange(dbCalendarPrices);
        syncCalendarPrices.AddRange(newCalendarPrices);

        await ChannelDataSyncByPriceProcess(timeSlots, syncCalendarPrices, false, ticket);

        //更新saas库存
        if (syncSaasTimeSlotCalendarInventories.Any())
        {
            var distinctItems = syncSaasTimeSlotCalendarInventories
                .DistinctBy(x => (x.ProductId, x.SkuId, x.TimeSlotId, x.Date, x.Time))
                .ToList();
            distinctItems.ForEach(item => item.Enabled = true);
            
            var byteItem = MessagePack.MessagePackSerializer.Typeless.Serialize(distinctItems);
            //时段配置库存
            var syncTimeSlotInvRequest = new BatchSyncThirdTimeSlotInventoryInput
            {
                OtherDatesPushEmpty = false,
                ByteItem = byteItem
            };
            await _baseService.BatchSyncThirdTimeSlotInventory(syncTimeSlotInvRequest);
        }

        if (syncSaasCalendarInventories.Any())
        {
            var distinctItems = syncSaasCalendarInventories
                .DistinctBy(x => (x.ProductId, x.SkuId, x.Date))
                .ToList();
            distinctItems.ForEach(x => x.Enabled = true);
            
            var byteItem = MessagePack.MessagePackSerializer.Typeless.Serialize(distinctItems);
            //日历配置库存
            var syncCalendarInvRequest = new BatchSyncThirdCalendarInventoryInput
            {
                OtherDatesPushEmpty = false,
                ByteItem = byteItem
            };
            await _baseService.BatchSyncThirdCalendarInventory(syncCalendarInvRequest);
        }

        if (!input.TimeSlotIds.Any())
        {
            var message = new BytePlusItemAttributesUpdateMessage
            {
                TenantId = ticket.TenantId,
                ProductItems = new List<BytePlusProductItemDto>
                {
                    new BytePlusProductItemDto
                    {
                        Id = ticket.Id.ToString(),
                        ItemCategory = BytePlusItemCategory.ScenicTicket,
                        ItemSubCategory = ticket.TicketsType is ScenicTicketsType.PromissoryNote ? BytePlusItemSubCategory.PromissoryNoteScenicTicket : BytePlusItemSubCategory.ReservationScenicTicket,
                        ItemId = ticket.Id,
                        ItemName = ticket.Name,
                        ItemVariant = string.Empty
                    }
                }
            };
            await _capPublisher.PublishAsync(CapTopics.Product.BytePlusItemAttributesUpdate, message);
        }
        else
        {
            foreach (var item in timeSlots)
            {
                var message = new BytePlusItemAttributesUpdateMessage
                {
                    TenantId = ticket.TenantId,
                    ProductItems = new List<BytePlusProductItemDto>
                    {
                        new() {
                            Id = item.Id.ToString(),
                            ItemCategory = BytePlusItemCategory.ScenicTicket,
                            ItemSubCategory = ticket.TicketsType is ScenicTicketsType.PromissoryNote ? BytePlusItemSubCategory.PromissoryNoteScenicTicket : BytePlusItemSubCategory.ReservationScenicTicket,
                            ItemId = ticket.Id,
                            ItemName = ticket.Name,
                            ItemVariant = item.Time.ToString()
                        }
                    }
                };
                await _capPublisher.PublishAsync(CapTopics.Product.BytePlusItemAttributesUpdate, message);
            }
        }
    }
    
    public async Task SyncThirdPriceInventory(SyncThirdPriceInventoryInput input)
    {
        try
        {
            if (input.OpenSupplierType == OpenSupplierType.System)
            {
                return;
            }
            
            //新增保存都是 从今天开始 & 默认180天
            var defaultStartDate = DateTime.Today;
            var defaultEndDate = defaultStartDate.AddDays(179);
            await PullSyncProcess(new PullSyncOpenSupplierDataDto
            {
                TicketId = input.TicketId,
                SkuId = input.SkuId,
                OpenSupplierType = input.OpenSupplierType,
                EditTicketSyncInfo = input.EditTicketSyncInfo,
                IsExperienceOzEdit = input.OpenSupplierType == OpenSupplierType.Experienceoz, //暂时处理 - 澳新价库门票编辑不做推空
                ChannelFullPush = true, //渠道全量推送,不关注价库是否有变动,
                ScheduleStartDate = defaultStartDate, 
                ScheduleEndDate = defaultEndDate
            });
        }
        catch (Exception e)
        {
            //ignore
        }
    }

    [UnitOfWork]
    public async Task<NotifySyncThirdPriceOutput> NotifySyncPrice(NotifySyncThirdPriceInput input)
    {
        var output= new NotifySyncThirdPriceOutput();
        //查询关联门票信息
        var tickets = await _dbContext.Tickets.AsNoTracking()
            .WhereIF(!string.IsNullOrEmpty(input.ActivityId), x => x.ActivityId == input.ActivityId)
            .WhereIF(!string.IsNullOrEmpty(input.PackageId), x => x.PackageId == input.PackageId)
            .Where(x => x.SkuId == input.SkuId && x.PriceInventorySource != PriceInventorySource.System)
            .ToListAsync();

        if (tickets.Any() is false) return output;
        var ticketIds = tickets.Select(x => x.Id).ToList();

        //查询关联时段信息
        var syncTimeSlots = await _dbContext.TicketsTimeSlots.AsNoTracking()
            .Where(x => ticketIds.Contains(x.TicketsId))
            .Where(x => x.Enabled)
            .ToListAsync();
        var timeSlotIds = syncTimeSlots.Select(x => x.Id).ToList();

        //查询关联的日历价格
        var syncTicketCalendarPrices = await _dbContext.TicketsCalendarPrices
            .Where(x => ticketIds.Contains(x.TicketsId) && x.Date >= DateTime.Today)
            .ToListAsync();

        var ticketCalendarPrices = syncTicketCalendarPrices;
        if (timeSlotIds.Any())
        {
            ticketCalendarPrices = ticketCalendarPrices
                .Where(x => x.TimeSlotId.HasValue && timeSlotIds.Contains(x.TimeSlotId.Value))
                .ToList();
        }

        //查询门票-供应端同步配置
        var openSupplierSettings = await _dbContext.TicketOpenSupplierSettings.AsNoTracking()
            .Where(x => ticketIds.Contains(x.TicketId))
            .ToListAsync();

        //多产品汇率计算
        var targetCurrencies = tickets.Select(x => x.CostCurrencyCode).Distinct().ToList();
        var costCurrencyList = targetCurrencies.Select(x => new GetExchangeRatesInput
        {
            BaseCurrencyCode = input.Currency,
            TargetCurrencyCode = x
        })
        .ToList();

        //saas采购价转售价币种汇率
        var costToSaleCurrencyList = tickets
            .Select(x => new
            {
                BaseCurrencyCode = x.CostCurrencyCode,
                TargetCurrencyCode = x.SaleCurrencyCode
            })
            .Distinct()
            .Select(x => new GetExchangeRatesInput
            {
                BaseCurrencyCode = x.BaseCurrencyCode,
                TargetCurrencyCode = x.TargetCurrencyCode
            })
            .ToList();
        var exchangeRateRequest = costCurrencyList.Concat(costToSaleCurrencyList).ToList();
        var exchangeRateInfo = await _baseService.GetCurrencyRate(exchangeRateRequest);

        foreach (var ticket in tickets)
        {
            var currentOpenSupplierSetting = openSupplierSettings.FirstOrDefault(x => x.TicketId == ticket.Id);
            if (currentOpenSupplierSetting is null) continue;
            if (currentOpenSupplierSetting.PriceInventorySyncType != PriceInventorySyncType.SyncPrice
                && currentOpenSupplierSetting.PriceInventorySyncType != PriceInventorySyncType.SyncAll)
            {
                //全部同步或者指定价格同步才继续处理
                continue;
            }

            //查询门票的采购币种
            var targetCostCurrency = tickets.FirstOrDefault(x => x.Id == ticket.Id)?.CostCurrencyCode;
            if (string.IsNullOrEmpty(targetCostCurrency)) continue;
            var costExchangeRate = exchangeRateInfo
                .FirstOrDefault(x => x.BaseCurrencyCode == input.Currency && x.TargetCurrencyCode == targetCostCurrency)
                ?.ExchangeRate ?? 1;
            //计算最新采购价
            decimal? newCostPrice = input.NewPrice.HasValue
                ? Math.Round(input.NewPrice.Value * costExchangeRate, 2)
                : null;

            //saas采购价转售价币种汇率
            var saasCostPriceExchangeRate = exchangeRateInfo.FirstOrDefault(x =>
                    x.BaseCurrencyCode == ticket.CostCurrencyCode && x.TargetCurrencyCode == ticket.SaleCurrencyCode)
                ?.ExchangeRate ?? 1;

            foreach (var item in ticketCalendarPrices.Where(x => x.TicketsId == ticket.Id))
            {
                item.CostPrice = newCostPrice;

                //计算价格调整后的售价
                //价格调整类型为指定，且调整值为null时，赋值为售价,兼容旧数据
                if (item is
                    {
                        PriceBasisType: PriceBasisType.Designated,
                        PriceAdjustmentType: PriceAdjustmentType.Designated,
                        PriceAdjustmentValue: null
                    })
                {
                    item.PriceAdjustmentValue = item.SellingPrice;
                }

                item.SellingPrice = _baseService.CalcAdjustmentSellingPrice(new CalcPriceAdjustmentInput
                {
                    PriceBasisType = item.PriceBasisType,
                    PriceAdjustmentType = item.PriceAdjustmentType,
                    PriceAdjustmentValue = item.PriceAdjustmentValue,
                    CostPrice = newCostPrice,
                    CostPriceExchangeRate = saasCostPriceExchangeRate
                });
            }
        }

        await ChannelDataSyncByPriceProcess(syncTimeSlots, ticketCalendarPrices, false, tickets.ToArray());

        output.TicketIds = ticketIds;
        return output;
    }
    
    public async Task<NotifySyncThirdInventoryOutput> NotifySyncThirdInventory(NotifySyncThirdInventoryInput input)
    {
        var output = new NotifySyncThirdInventoryOutput();
        await PullSyncProcess(new PullSyncOpenSupplierDataDto
        {
            ActivityId = input.ActivityId,
            PackageId = input.PackageId,
            SkuId = input.SkuId,
            Schedules = input.Schedules,
            OpenSupplierType = input.OpenSupplierType,
            SupplierIsSale = input.IsSale,
            IsFromThirdInventorySync = true,
            ScheduleStartDate = input.StartDate,
            ScheduleEndDate = input.EndDate,
            DataType = input.DataType
        });

        return output;
    }

    public async Task UpdateThirdTicketMinPrice(SetThirdTicketMinPriceInput input)
    {
        if (!await _semaphore.WaitAsync(TimeSpan.FromSeconds(_semaphoreTimeoutSeconds)))
        {
            _logger.LogWarning("UpdateThirdTicketMinPrice semaphore wait timeout");
            return;
        }
        
        try
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var scopedService = scope.ServiceProvider.GetRequiredService<ICalendarPriceService>();
            if(scopedService == null) return;
            await scopedService.UpdateThirdTicketMinPrice(input);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "UpdateThirdTicketMinPrice failed: {Message}", ex.Message);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task SyncChannelPriceStock(SyncChannelPriceStockUploadMessage receive)
    {
        if (receive.ByteSchedules is null)
            return;
        
        var receiveSchedules =
            MessagePack.MessagePackSerializer.Typeless.Deserialize(receive.ByteSchedules) as List<SyncChannelPriceStockUploadMessageItem>; //反序列化元数据
        if (receiveSchedules == null || receiveSchedules.Any() is false)
            return;
        
        var request = _mapper.Map<ChannelPriceStockUploadRequest>(receive);
        request.Schedules = _mapper.Map<List<ChannelPriceStockUploadSchedulesItem>>(receiveSchedules);
        await _openChannelService.ChannelPriceStockUpload(request, receive.TenantId);
    }

    public async Task TriggerOpenChannelSync(TriggerOpenChannelSyncMessage receive)
    {
        //查询openSupplier价库数据
        var syncChannelBeginDate = receive.TravelDateTime.Date;
        var syncChannelEndDate = receive.TravelDateTime.Date.AddDays(2); //出行日期 + 2天
        if (receive.Time.HasValue)
        {
            syncChannelBeginDate = syncChannelBeginDate.Add(receive.Time.Value);
            syncChannelEndDate = syncChannelEndDate.Add(receive.Time.Value);
        }

        //门票信息
        var ticket = await _dbContext.Tickets.AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == receive.TicketId);
        if(ticket == null) return;

        //供应端 - 同步配置
        var openSupplierSetting = await _dbContext.TicketOpenSupplierSettings.AsNoTracking()
            .FirstOrDefaultAsync(x => x.TicketId == ticket.Id);
        var openSupplierPriceInventorySyncType = openSupplierSetting?.PriceInventorySyncType ?? PriceInventorySyncType.NoNeedSync;

        //供应端 - 未开启同步
        if (openSupplierPriceInventorySyncType == PriceInventorySyncType.NoNeedSync)
            return;

        if (string.IsNullOrEmpty(receive.OutProductId))
            receive.OutProductId = ticket.ActivityId;
        if (string.IsNullOrEmpty(receive.OutProductOptionId))
            receive.OutProductOptionId = ticket.PackageId;
        if (string.IsNullOrEmpty(receive.OutSkuId))
            receive.OutSkuId = ticket.SkuId;
        if (string.IsNullOrEmpty(receive.SupplierType))
        {
            var supplierApiType =
                _openPlatformBaseService.MapPriceInventorySourceToSupplierApiType(ticket.PriceInventorySource!.Value);
            receive.SupplierType = _openPlatformBaseService.ConvertSupplierType(supplierApiType)
                .openSupplierTypeStr;
        }

        //供应端 - 查询价库数据
        var querySkuSchedule = await _openSupplierService.SupplierSkuSchedule(new SupplierSkuScheduleRequest
        {
            SupplierType = receive.SupplierType,
            OutProductId = receive.OutProductId,
            OutProductOptionId = receive.OutProductOptionId,
            OutSkuId = receive.OutSkuId,
            DateFrom = syncChannelBeginDate,
            DateTo = syncChannelEndDate,
            Timeslot = receive.Time?.ToString(_timeSlotFormat)
        }, ticket.TenantId);
        var stocks = querySkuSchedule.Data?.Stocks ?? new List<SupplierProductScheduleList>();
        
        //记录同步日志
        var addSyncLogs = new AddSyncLogInput();
        addSyncLogs.Logs.Add(new AddSyncLogItem()
            {
                TenantId = ticket.TenantId,
                ProductType = ProductType.Scenic,
                PlatformType = OpenPlatformType.OpenSupplier,
                SyncType = OpenPlatformPricingSyncType.ActivePush,
                SyncResult = stocks.Any()
                ? OpenPlatformPricingSyncResult.Success
                : OpenPlatformPricingSyncResult.Failed,
                FailedMessage = querySkuSchedule.Msg,
                ProductId = ticket.Id,
                ProductName = ticket.Name,
                SkuId = ticket.Id,
                SkuName = ticket.Name,
                SyncStartDate = syncChannelBeginDate,
                SyncEndDate = syncChannelEndDate,
                SupplierIsSale = true
            }
        );
        _ = _baseService.AddOpenPlatformPricingSyncLogs(addSyncLogs);
        
        
        if (stocks.Any() is false || querySkuSchedule.Code != _successCode)
        {
            //查无价库或者查询异常.维持原样不更新价库
            _logger.LogWarning("下单后触发ota渠道同步失败:{@Message}", querySkuSchedule);
            return;
        }

        if (stocks.Any())
        {
            //系统价库不支持同步
            if (ticket.PriceInventorySource == PriceInventorySource.System) return;

            //ota同步渠道
            var otaSyncChannels = Enum.GetValues<PriceInventorySyncChannelType>()
                .Where(x => (x & ticket.PriceInventorySyncChannelTypes) == x)
                .ToList();

            //渠道同步配置
            var syncOpenChannelSettings = await _dbContext.TicketOtaChannelSettings.AsNoTracking()
                .Where(x => x.TicketId == receive.TicketId)
                .Where(x=>x.SettingType == OpenChannelSettingType.PriceInventorySync)
                .ToListAsync();
            var syncChannelSettingItems = await _dbContext.TicketOtaChannelSettingItems.AsNoTracking()
                .Where(x => x.TicketId == receive.TicketId)
                .ToListAsync();

            //日历价
            var travelBeginDate = syncChannelBeginDate.Date;
            var travelEndDate = syncChannelEndDate.Date.AddDays(1);
            var calendarPrices = await _dbContext.TicketsCalendarPrices.AsNoTracking()
                .Where(x => x.TicketsId == receive.TicketId)
                .Where(x => x.Date >= travelBeginDate && x.Date < travelEndDate)
                .WhereIF(receive.TimeSlotId.HasValue, x => x.TimeSlotId == receive.TimeSlotId!.Value)
                .ToListAsync();
            
            var syncChannelUploadDto = new List<SyncChannelPriceStockUploadDto>(); //ota渠道同步
            var syncTimeSlotCalendarInvData = new List<BatchSyncThirdTimeSlotInventoryItem>(); //时间段库存
            var syncCalendarInvData = new List<BatchSyncThirdCalendarInventoryItem>(); //日历库存
            var syncOtaSkuIdList = new List<(OtaChannelType OtaType, List<long> syncSkuIds)>();
            
            //处理同步渠道信息
            var otaZeroStockThreshold = new List<(OtaChannelType OtaTyp, int ZeroStockThreshold)>();
            foreach (var otaSyncChannel in otaSyncChannels)
            {
                var otaChannelSetting = syncOpenChannelSettings.FirstOrDefault(x =>
                    x.TicketId == ticket.Id && x.PriceInventorySyncChannelType == otaSyncChannel);

                if (otaChannelSetting == null)
                    continue; //分销渠道-未开启同步

                if (otaChannelSetting.PriceInventorySyncType == PriceInventorySyncType.NoNeedSync)
                    continue; //分销渠道-未开启同步
                
                var currentOtaChannelSettingItems =
                    syncChannelSettingItems.Where(x => x.TicketOtaChannelSettingId == otaChannelSetting.Id).ToList();
                var syncSkuIds = currentOtaChannelSettingItems.Select(x => x.SyncSkuId).ToList();

                var otaType = _baseService.MappingOtaChannelType(otaSyncChannel);
                var outProductId = !string.IsNullOrEmpty(otaChannelSetting.SupplierProductId)
                    ? otaChannelSetting.SupplierProductId
                    : ticket.Id.ToString();
                syncChannelUploadDto.Add(new SyncChannelPriceStockUploadDto
                {
                    TenantId = ticket.TenantId,
                    TicketId = ticket.Id,
                    TicketName = ticket.Name,
                    ScenicSpotId = ticket.ScenicSpotId,
                    OtaType = otaType,
                    IsCreUpdate = true,
                    ProductType = OtaChannelProductType.Ticket,
                    OutProductId = outProductId,
                    OtaProductId = otaChannelSetting?.ChannelProductId ?? string.Empty,
                    OtaSkuId = otaChannelSetting.TicketArea ?? string.Empty,
                    ScheduleType = otaChannelSetting.PriceInventorySyncType switch
                    {
                        PriceInventorySyncType.SyncPrice => OpenChannelPriceStockUploadScheduleType.SyncPrice,
                        PriceInventorySyncType.SyncInventory => OpenChannelPriceStockUploadScheduleType.SyncInventory,
                        _ => OpenChannelPriceStockUploadScheduleType.SyncAll
                    },
                    Schedules = new List<SyncChannelPriceStockUploadMessageItem>()
                });
                
                syncOtaSkuIdList.Add(new ValueTuple<OtaChannelType, List<long>>(otaType,syncSkuIds));//后续过滤
                otaZeroStockThreshold.Add(new ValueTuple<OtaChannelType, int>(otaType, otaChannelSetting.ZeroStockThreshold));
            }
            
            //查询3天数据
            var days = syncChannelEndDate.Subtract(syncChannelBeginDate).Days;
            for (int i = 0; i <= days; i++)
            {
                var syncDate = syncChannelBeginDate.AddDays(i);
                var syncDateStock = stocks.FirstOrDefault(x => x.Time == syncDate);
                var avaStockQty = syncDateStock?.Available ?? 0;
                var totalStockQty = syncDateStock?.Total ?? 0;
                var totalQty = avaStockQty > totalStockQty
                    ? avaStockQty
                    : totalStockQty;
                
                //渠道价库同步信息填充
                foreach (var channelUploadMessage in syncChannelUploadDto)
                {
                    var syncSkuIds = syncOtaSkuIdList.Where(x => x.OtaType == channelUploadMessage.OtaType)
                        .SelectMany(x => x.syncSkuIds)
                        .ToList();//同步的skuIds
                    var outSkuId = receive.TimeSlotId ?? receive.TicketId;
                    if (syncSkuIds.Any() && !syncSkuIds.Contains(outSkuId)) //存在同步的skuIds并且不包含当前skuId 则跳过
                        continue;
                    
                    var priceChannelType = _openPlatformBaseService.MappingOtaChannelTypeToCalendarPriceChannelType(channelUploadMessage.OtaType);
                    var calendarPrice = calendarPrices.FirstOrDefault(x => x.PriceChannelType == priceChannelType && x.Date == syncDate);
                    var channelPrice = Convert.ToInt32((calendarPrice?.SellingPrice ?? 0) * 100);
                    var channelStock = channelPrice == 0 ? 0 : avaStockQty;
                    //0库存阈值
                    var zeroStockThreshold =
                        otaZeroStockThreshold.FirstOrDefault(x => x.OtaTyp == channelUploadMessage.OtaType);
                    if (channelStock <= zeroStockThreshold.ZeroStockThreshold)
                        channelStock = 0;
                    
                    channelUploadMessage.Schedules.Add(
                        new SyncChannelPriceStockUploadMessageItem
                        {
                            OutSkuId = outSkuId,
                            Date = syncDate,
                            Price = channelPrice,
                            Stock = channelStock
                        });
                }
                
                
                //saas库存更新
                if (openSupplierSetting.PriceInventorySyncType is PriceInventorySyncType.SyncAll
                    or PriceInventorySyncType.SyncInventory)
                {
                    if (receive.TimeSlotId.HasValue)
                    {
                        //时段库存
                        var syncItem = new BatchSyncThirdTimeSlotInventoryItem
                        {
                            ProductId = ticket.ScenicSpotId,
                            SkuId = receive.TicketId,
                            TimeSlotId = receive.TimeSlotId!.Value,
                            Date = syncDate.Date,
                            Time = receive.Time!.Value,
                            TotalQuantity = totalQty,
                            AvailableQuantity = avaStockQty
                        };
                        syncTimeSlotCalendarInvData.Add(syncItem);
                    }
                    else
                    {
                        //日历库存
                        var syncItem = new BatchSyncThirdCalendarInventoryItem
                        {
                            ProductId = ticket.ScenicSpotId,
                            SkuId = receive.TicketId,
                            Date = syncDate.Date,
                            TotalQuantity = totalQty,
                            AvailableQuantity = avaStockQty
                        };
                        syncCalendarInvData.Add(syncItem);
                    }
                }
                
            }
            
            await SyncChannelUploadDataSerializer(syncChannelUploadDto);
            await SyncSaasTimeSlotInventory(new SyncSaasTimeSlotInventoryData(ticket.TenantId, false, syncTimeSlotCalendarInvData));
            await SyncSaasCalendarInventory(new SyncSaasCalendarInventoryData(ticket.TenantId, false, syncCalendarInvData));
        }
    }
    
    public async Task NewOrderNotifyTriggerSyncStocks(OpenChannelNotifySyncStocksInput input)
    {
        var syncData = new List<(long TicketId, long? TimeSlotId, TimeSpan? Time)>();
        syncData = await _dbContext.TicketsCombinationSettings
            .AsNoTracking()
            .Where(x => x.TicketsCombinationId == input.ChannelProductId)
            .Select(x => new ValueTuple<long, long?, TimeSpan?>(x.TicketsId, null, null))
            .ToListAsync();

        if (syncData.Any() is false)
        {
            var timeSlot = await _dbContext.TicketsTimeSlots
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == input.ChannelProductId);
            syncData.Add(new ValueTuple<long, long?, TimeSpan?>(timeSlot?.TicketsId ?? input.ChannelProductId,
                timeSlot?.Id, timeSlot?.Time));
        }

        foreach (var item in syncData)
        {
            await _capPublisher.PublishAsync(CapTopics.Scenic.TriggerOpenChannelSync,
                new TriggerOpenChannelSyncMessage
                {
                    TicketId = item.TicketId,
                    TimeSlotId = item.TimeSlotId,
                    Time = item.Time,
                    TravelDateTime = input.TravelDate
                });
        }
    }

    /// <summary>
    /// 以日历价数据为基础进行渠道同步
    /// </summary>
    [UnitOfWork]
    public async Task SyncByCalendarPriceData(SyncChannelByCalendarPriceMessage message)
    {
        var ticket = await _dbContext.Tickets
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == message.TicketId && x.TenantId == message.TenantId);

        if (ticket.PriceInventorySource == PriceInventorySource.System)
            return;

        //当前门票同步的渠道信息
        var syncChannels = Enum.GetValues<PriceInventorySyncChannelType>()
            .Where(x => (x & ticket.PriceInventorySyncChannelTypes) == x)
            .ToList();

        if (!syncChannels.Any())
            return;

        var timeSlots = await _dbContext.TicketsTimeSlots
            .AsNoTracking()
            .Where(x => message.TimeSlotIds.Contains(x.Id) && x.TenantId == message.TenantId)
            .ToListAsync();

        //查询本地时段日历数据
        var tCalendarPrices = await _dbContext.TicketsCalendarPrices
            .Where(x => x.TicketsId == ticket.Id && x.TenantId == message.TenantId)
            .WhereIF(message.TimeSlotIds.Any(),
                x => x.TimeSlotId.HasValue && message.TimeSlotIds.Contains(x.TimeSlotId.Value))
            .ToListAsync();

        if (message.IsPutEmpty)
        {
            if (timeSlots.Any())
            {
                //移除时段日历库存
                await _capPublisher.PublishAsync(CapTopics.Inventory.BatchDeleteTimeSlotInventory,
                    new BatchDeleteTimeSlotInventoryMessage
                    {
                        TenantId = ticket.TenantId,
                        Items = timeSlots.Select(x => new BatchDeleteTimeSlotInventoryItem
                        {
                            ProductId = ticket.ScenicSpotId, SkuId = ticket.Id, TimeSlotId = x.Id
                        }).ToList()
                    });
            }

            _dbContext.RemoveRange(tCalendarPrices);
        }

        if (message.IsSyncOpenChannel)
        {
            await ChannelDataSyncByPriceProcess(timeSlots, tCalendarPrices, message.IsPutEmpty, ticket);
        }
    }

    public async Task SetChannelSettingSyncPriceStocks(SetChannelSettingSyncPriceStocksInput input)
    {
        var ticket = await _dbContext.Tickets.AsNoTracking().FirstOrDefaultAsync(x => x.Id == input.TicketId);
        if(ticket == null) return;
        if(ticket.CredentialSourceType != CredentialSourceType.InterfaceDock) return;
        if(ticket.SupplierId <=0) return;
        var beginTime = DateTime.Today;
        var endTime = beginTime.AddDays(90);
        
        // 时段
        var dbTimeSlots = await _dbContext.TicketsTimeSlots
            .Where(x => x.TicketsId == input.TicketId)
            .OrderBy(x => x.Time)
            .ToListAsync();
        var dbEnableTimeSlots = dbTimeSlots.Where(x => x.Enabled).ToList();
        var timeSlotIds = dbEnableTimeSlots.Select(x => x.Id).ToList();

        // 渠道同步配置
        //渠道端-配置
        var otaChannelSettings = new List<TicketOtaChannelSettingInfo>();
        var ticketOtaChannelSettings = await _dbContext.TicketOtaChannelSettings.AsNoTracking()
            .Where(x => x.TicketId == ticket.Id)
            .Where(x => x.SettingType == OpenChannelSettingType.PriceInventorySync)
            .Where(x=>x.PriceInventorySyncType != PriceInventorySyncType.NoNeedSync)
            .ToListAsync();
        if (ticketOtaChannelSettings.Any())
        {
            //渠道配置子项
            var otaChannelSettingItems = await _dbContext.TicketOtaChannelSettingItems
                .AsNoTracking()
                .Where(x => x.TicketId == ticket.Id)
                .ToListAsync();
            foreach (var item in ticketOtaChannelSettings)
            {
                var otaChannelSettingItem = otaChannelSettingItems.Where(x => x.TicketOtaChannelSettingId == item.Id);
                otaChannelSettings.Add(new TicketOtaChannelSettingInfo
                {
                    TicketId = item.TicketId,
                    PriceInventorySyncChannelType = item.PriceInventorySyncChannelType,
                    ChannelProductId = item.ChannelProductId,
                    PriceInventorySyncType = item.PriceInventorySyncType,
                    SupplierProductId = item.SupplierProductId,
                    TicketArea = item.TicketArea,
                    ZeroStockThreshold = item.ZeroStockThreshold,
                    SyncSkuIds = otaChannelSettingItem
                        .Select(x => x.SyncSkuId)
                        .ToList()
                });
            }
        }
        
        // 日历
        var calendarPriceList = await _dbContext.TicketsCalendarPrices.AsNoTracking()
            .Where(x => x.TicketsId == ticket.Id)
            .Where(x => x.Date >= beginTime)
            .ToListAsync();
        if(calendarPriceList.Any() == false) return;
        endTime = calendarPriceList.Max(x=>x.Date.Date);
        
        // 库存
        var dbCalendarInventories = new List<NotifySyncStockDto>();
        if (timeSlotIds.Any())
        {
            //时段日历库存
            var tDbCalendarInventories = await GetTimeSlotInventories(new GetTimeSlotInventoryInput
            {
                StartDate = beginTime,
                EndDate = endTime,
                SkuIds = new List<long> { ticket.Id },
                TimeSlotIds = timeSlotIds
            });
            dbCalendarInventories.AddRange(tDbCalendarInventories);
        }
        else
        {
            //查询非时段产品的日历库存
            var calendarInventories = await GetCalendarInventories(new GetCalendarInventoryInput
            {
                CalendarProducts = new List<CalendarProduct>
                {
                    new CalendarProduct
                    {
                        ProductId = ticket.ScenicSpotId,
                        ItemIds = new List<long> { ticket.Id }
                    }
                },
                StartDate = beginTime,
                EndDate = endTime
            });
            dbCalendarInventories.AddRange(calendarInventories);
        }

        var pushChannelData = new List<PushOpenChannelPriceStockChannelDataDto>();
        foreach (var channelSetting in ticketOtaChannelSettings)
        {
            var priceChannelType = _baseService.MappingCalendarPriceChannelType(channelSetting.PriceInventorySyncChannelType);
            var channelPrices = calendarPriceList.Where(x => x.PriceChannelType == priceChannelType)
                .Select(x=> new PushOpenChannelPriceStockChannelDataDto
                {
                    TicketId = x.TicketsId,
                    TimeSlotId = x.TimeSlotId,
                    Date = x.Date.Date,
                    Price = Convert.ToInt32((x.SellingPrice ?? 0) * 100),
                    SyncChannelType = channelSetting.PriceInventorySyncChannelType
                })
                .ToList();
            foreach (var item in channelPrices)
            {
                var dateInve = dbCalendarInventories
                    .Where(x => x.SkuId == item.TicketId && x.TimeSlotId == item.TimeSlotId)
                    .SelectMany(x => x.Schedules)
                    .FirstOrDefault(x => x.Date.Date == item.Date);
                if (dateInve != null)
                {
                    item.Stock = dateInve.AvailableQuantity;
                }

                if (item.Price == 0)
                    item.Stock = 0;
            }
            
            pushChannelData.AddRange(channelPrices);
        }
        
        var syncDataList = new List<SyncOpenChannelData>
        {
            new SyncOpenChannelData(ticket: ticket,
                pushChannelData: pushChannelData,
                otaChannelSettings : otaChannelSettings,
                editTicketSyncInfo: new EditTicketSyncInfo
                {
                    NewSyncOtaChannels = input.NewSyncOtaChannels,
                    PushEmptyOtaChannelSettings = input.PushEmptyOtaChannelSettings
                })
        };
        await SyncChannelPriceStockProcess(syncDataList);
    }

    #region 同步

    private async Task<NotifySyncThirdInventoryOutput> PullSyncProcess(PullSyncOpenSupplierDataDto input)
    {
        var output = new NotifySyncThirdInventoryOutput();
        var priceInventorySource =
            _openPlatformBaseService.MapOpenSupplierTypeToPriceInventorySource(input.OpenSupplierType);
        //查询产品数据
        var tickets = await _dbContext.Tickets
            .WhereIF(input.TicketId.HasValue, x => x.Id == input.TicketId)
            .WhereIF(!string.IsNullOrEmpty(input.ActivityId), x => x.ActivityId == input.ActivityId)
            .WhereIF(!string.IsNullOrEmpty(input.PackageId), x => x.PackageId == input.PackageId)
            .WhereIF(!string.IsNullOrEmpty(input.SkuId), x => x.SkuId == input.SkuId)
            .Where(x => x.PriceInventorySource == priceInventorySource)
            .ToListAsync();

        if (tickets.Any() is false) return output;

        await PullSyncBaseProcess(input, tickets);


        output.TicketIds = tickets.Select(x => x.Id).ToList();
        return output;
    }

    /// <summary>
    /// 全量拉取+增量同步
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="BusinessException"></exception>
    private async Task PullSyncBaseProcess(PullSyncOpenSupplierDataDto input, List<Tickets> tickets)
    {

        #region definition

        var tenantIds = tickets.Select(x => x.TenantId).Distinct().ToList();
        var ticketIds = tickets.Select(x => x.Id).ToList();
        var activityId = tickets.FirstOrDefault().ActivityId!;
        var packageId = tickets.FirstOrDefault().PackageId!;
        var skuId = tickets.FirstOrDefault().SkuId!;
        var beginTime = input.ScheduleStartDate;
        var endTime = input.ScheduleEndDate;
        var otherDatesPushEmpty = false; //价库其他日期推空
        var syncOpenChannelDataList = new List<SyncOpenChannelData>();
        var syncSaasCalendarInventoryDataList = new List<SyncSaasCalendarInventoryData>();
        var syncSaasTimeSlotInventoryDataList = new List<SyncSaasTimeSlotInventoryData>();
        var syncLogs = new List<AddSyncLogItem>();

        #endregion

        #region 获取本地日历价格/时段/库存

        var dbCalendarInventories = new List<NotifySyncStockDto>(); //库存数据
        var dbCalendarPrices = await _dbContext.TicketsCalendarPrices
            .Where(x => ticketIds.Contains(x.TicketsId))
            .Where(x => x.Date >= beginTime && x.Date < endTime.AddDays(1))
            .ToListAsync();
        var dbTimeSlots = await _dbContext.TicketsTimeSlots
            .Where(x => ticketIds.Contains(x.TicketsId))
            .OrderBy(x => x.Time)
            .ToListAsync();
        var dbEnableTimeSlots = dbTimeSlots.Where(x => x.Enabled).ToList();
        var timeSlotTicketIds = dbEnableTimeSlots.Select(x => x.TicketsId).Distinct().ToList();
        var nonTimeSlotTicketIds = ticketIds.Except(timeSlotTicketIds).ToList();
        if (timeSlotTicketIds.Any())
        {
            //查询时段产品的日历库存
            var enableTimeSlotIds = dbEnableTimeSlots.Select(x => x.Id).ToList();
            //时段日历库存
            var tDbCalendarInventories = await GetTimeSlotInventories(new GetTimeSlotInventoryInput
            {
                StartDate = beginTime,
                EndDate = endTime,
                SkuIds = timeSlotTicketIds,
                TimeSlotIds = enableTimeSlotIds
            });
            dbCalendarInventories.AddRange(tDbCalendarInventories);
        }
        if (nonTimeSlotTicketIds.Any())
        {
            //查询非时段产品的日历库存
            var calendarInventories = await GetCalendarInventories(new GetCalendarInventoryInput
            {
                CalendarProducts = tickets
                    .Where(x => nonTimeSlotTicketIds.Contains(x.Id))
                    .GroupBy(x => x.ScenicSpotId)
                    .Select(x => new CalendarProduct
                    {
                        ProductId = x.Key,
                        ItemIds = x.Select(t => t.Id).ToList()
                    }),
                StartDate = beginTime,
                EndDate = endTime
            });
            dbCalendarInventories.AddRange(calendarInventories);
        }

        //供应端-配置
        var openSupplierSettings = await _dbContext.TicketOpenSupplierSettings.AsNoTracking()
            .Where(x => ticketIds.Contains(x.TicketId))
            .ToListAsync();

        //渠道端-配置
        var otaChannelSettings = new List<TicketOtaChannelSettingInfo>();
        var ticketOtaChannelSettings = await _dbContext.TicketOtaChannelSettings.AsNoTracking()
            .Where(x => ticketIds.Contains(x.TicketId))
            .Where(x => x.SettingType == OpenChannelSettingType.PriceInventorySync)
            .ToListAsync();
        if (ticketOtaChannelSettings.Any())
        {
            //渠道配置子项
            var otaChannelSettingItems = await _dbContext.TicketOtaChannelSettingItems
                .AsNoTracking()
                .Where(x => ticketIds.Contains(x.TicketId))
                .ToListAsync();
            foreach (var item in ticketOtaChannelSettings)
            {
                var otaChannelSettingItem = otaChannelSettingItems.Where(x => x.TicketOtaChannelSettingId == item.Id);
                otaChannelSettings.Add(new TicketOtaChannelSettingInfo
                {
                    TicketId = item.TicketId,
                    PriceInventorySyncChannelType = item.PriceInventorySyncChannelType,
                    ChannelProductId = item.ChannelProductId,
                    PriceInventorySyncType = item.PriceInventorySyncType,
                    SupplierProductId = item.SupplierProductId,
                    TicketArea = item.TicketArea,
                    ZeroStockThreshold = item.ZeroStockThreshold,
                    SyncSkuIds = otaChannelSettingItem
                        .Select(x => x.SyncSkuId)
                        .ToList()
                });
            }
            
        }

        #endregion

        #region 查询第三方产品信息和价库

        var tenantAggregateQueryList =
            new List<(long tenatId, 
                (decimal CommissionRate,SupplierProductDetailSkuItem? SkuDetail, List<SupplierProductScheduleList>Schedules) aggregateQuerItem)>();
        if (input.SupplierIsSale) //价库通知->产品已下架.不去查询产品详情和价库
        {
            foreach (var tenantId in tenantIds)
            {
                var aggregateQueryItem = await AggregateQueryProductDetailAndStocks(
                    new()
                    {
                        ActivityId = activityId,
                        PackageId = packageId,
                        SkuId = skuId,
                        OpenSupplierType = input.OpenSupplierType,
                        ScheduleStartDate = beginTime,
                        ScheduleEndDate = endTime,
                        Schedules = input.Schedules,
                        IsFromThirdInventorySync = input.IsFromThirdInventorySync
                    }, tenantId);
                tenantAggregateQueryList.Add(new (tenantId,aggregateQueryItem));
            }
        }

        #endregion

        var calendarPriceChannelTypes = Enum.GetValues<CalendarPriceChannelType>()
            .Where(x => x != CalendarPriceChannelType.System); //日历价格渠道枚举数据

        /*
         * 本地有时段，且有查询到对应时段价格库存，本地所有渠道价库更新；
         * 本地有时段，且没查询到对应时段价格库存，本地所有渠道价库清空；
         * 本地无时段，且查询到的价格库存无时段区分，本地所有渠道价库更新；
         * 本地无时段，且查询到的价格库存有时段区分或未查询到结果，本地所有渠道价库清空；
         * 
         * 价库清空 = 删除日历价格记录
         * 价库更新 = 日历价格更新：对应日期有返回的，更新底价库存；对应日期无返回的，底价空库存0
         */
        foreach (var ticket in tickets)
        {
            //查询门票的供应端配置
            var currentOpenSupplierSetting = openSupplierSettings.FirstOrDefault(x => x.TicketId == ticket.Id);
            if (currentOpenSupplierSetting == null)
                continue; //供应端-无需同步
            if (currentOpenSupplierSetting.PriceInventorySyncType == PriceInventorySyncType.NoNeedSync)
                continue; //供应端-无需同步

            //当前门票的有效的时段数据
            var ticketTimeSlots = dbEnableTimeSlots.Where(x => x.TicketsId == ticket.Id).ToList();
            var isTimeSlot = ticketTimeSlots.Any();

            var syncSaasTimeSlotCalendarInventories = new List<BatchSyncThirdTimeSlotInventoryItem>(); //saas时段库存同步
            var syncSaasCalendarInventories = new List<BatchSyncThirdCalendarInventoryItem>(); //saas日历库存同步
            var newCalendarPrices = new List<TicketsCalendarPrice>(); //新增日历

            #region 查询第三方产品信息和价库

            SupplierProductDetailSkuItem? thirdSkuDetail = new();
            var thirdSchedules = new List<SyncThirdInventorySchedulesDto>();
            var thirdIsTimeSlot = false;
            var thirdTimeSlots = new List<TimeSpan>();

            //门票未开启供应端的价库同步
            if (currentOpenSupplierSetting.PriceInventorySyncType != PriceInventorySyncType.NoNeedSync)
            {
                //查询第三方产品信息和价库,api对接供应商如果不返回币种. 默认门票配置的供应商币种
                var aggregateQuery = tenantAggregateQueryList
                    .FirstOrDefault(x => x.tenatId == ticket.TenantId)
                    .aggregateQuerItem;

                thirdSkuDetail = aggregateQuery.SkuDetail; //存在为null的场景
                thirdSchedules = _mapper.Map<List<SyncThirdInventorySchedulesDto>>(aggregateQuery.Schedules);//存在空列表的场景

                //experienceOz供应商获取同步传入的数据.需要处理币种信息
                if (input.OpenSupplierType == OpenSupplierType.Experienceoz)
                {
                    //价库同步的时候
                    if (input.IsFromThirdInventorySync)
                    {
                        if (thirdSkuDetail != null && string.IsNullOrEmpty(thirdSkuDetail.Currency))
                        {
                            //目前澳新sku详情没返回币种,需要以价库的币种为准
                            thirdSkuDetail.Currency = input.Schedules.FirstOrDefault()?.Currency;
                        }
                    }
                    else
                    {
                        //门票新增/编辑的时候 特殊处理experienceOz结束日期
                        otherDatesPushEmpty = false;
                    }
                }

                thirdIsTimeSlot = thirdSkuDetail?.IsTimeSlot ?? isTimeSlot;//空详情默认saas自己的时段数据
                if (thirdIsTimeSlot)
                {
                    //查询第三方时段数据
                    thirdTimeSlots = thirdSchedules
                        .GroupBy(x => x.Date.TimeOfDay)
                        .Select(x => x.Key)
                        .ToList();
                }
                
                //特殊处理价库
                if (thirdSkuDetail != null)
                {
                    if (thirdSkuDetail.IsTimeSlot == false && thirdSkuDetail.TimeSlot.Any() is false)
                    {
                        thirdSchedules = SpecialScheduleProcess(thirdSchedules);
                    }
                }

                var syncType = input.IsFromThirdInventorySync
                    ? OpenPlatformPricingSyncType.AsyncNotification
                    : OpenPlatformPricingSyncType.ActivePush;
                var syncResult = syncType == OpenPlatformPricingSyncType.ActivePush
                    ? thirdSchedules.Any()
                        ? OpenPlatformPricingSyncResult.Success
                        : OpenPlatformPricingSyncResult.Failed
                    : OpenPlatformPricingSyncResult.Success;
                var syncIsSupplierSale = input.IsFromThirdInventorySync
                    ? input.SupplierIsSale
                    : thirdSkuDetail != null;
                
                syncLogs.Add(new AddSyncLogItem
                {
                    TenantId = ticket.TenantId,
                    ProductType = ProductType.Scenic,
                    PlatformType = OpenPlatformType.OpenSupplier,
                    SyncType = syncType,
                    SyncResult = syncResult,
                    ProductId = ticket.Id,
                    ProductName = ticket.Name,
                    SkuId = ticket.Id,
                    SkuName = ticket.Name,
                    SyncStartDate = input.ScheduleStartDate,
                    SyncEndDate = input.ScheduleEndDate,
                    SupplierIsSale = syncIsSupplierSale
                });
            }

            #endregion
            
            // 更新门票信息
            ticket.CostDiscountRate = thirdSkuDetail?.CommissionRate ?? 0;
            ticket.TouristInfoType = thirdSkuDetail?.TravelerInfoType switch
            {
                OpenSupplierProductTravelerInfoType.NotNeed => TouristInfoType.None,
                OpenSupplierProductTravelerInfoType.JustOne => TouristInfoType.One,
                OpenSupplierProductTravelerInfoType.EveryOne => TouristInfoType.Every,
                _ => TouristInfoType.None
            };

            //api供应商采购币种
            var thirdCostCurrencyCode = thirdSkuDetail?.Currency ?? ticket.CostCurrencyCode;
            //api采购币种换算门票采购币种汇率
            var costExchangeRate = await _baseService.GetExchangeRate(thirdCostCurrencyCode, ticket.CostCurrencyCode);
            //saas采购价转售价币种汇率
            var saasCostPriceExchangeRate = await _baseService.GetExchangeRate(ticket.CostCurrencyCode, ticket.SaleCurrencyCode);

            //如果存在默认渠道价格的日历价.则移除
            var defaultSystemCalendarPrices = dbCalendarPrices
                .Where(x => x.TicketsId == ticket.Id
                            && x.PriceChannelType == CalendarPriceChannelType.System)
                .ToList();
            if (defaultSystemCalendarPrices.Any())
                _dbContext.RemoveRange(defaultSystemCalendarPrices);

            var pushChannelData = new List<PushOpenChannelPriceStockChannelDataDto>(); //渠道同步数据

            var priceInventorySyncChannels = Enum.GetValues<PriceInventorySyncChannelType>()
                .Where(x => (x & ticket.PriceInventorySyncChannelTypes) == x).ToList();

            //当前saas日历库存
            var ticketDbCalendarInventories = dbCalendarInventories
                .Where(x => x.SkuId == ticket.Id)
                .ToList();

            //当前门票下需要推空的时段数据
            var pushEmptyTimeSlots = input.EditTicketSyncInfo.PushEmptyTimeSlots
                .Where(x => x.TicketId == ticket.Id)
                .ToList();
            
            var editTicketSyncInfo = input.EditTicketSyncInfo;
            editTicketSyncInfo.PushEmptyTimeSlots = new List<TicketTimeSlotInfo>();

            if (input.IsFromThirdInventorySync)
            {
                //[常规性&运维性]根据是否可售更新票种[供应商可售]状态.清空价库
                ticket.SupplierIsSale = input.SupplierIsSale;
                ticket.UpdateTime = DateTime.Now;
            }
            else
            {
                //非第三方价库同步.产品为[供应商可售]且[上架]
                if (thirdSkuDetail != null && thirdSchedules.Any())
                {
                    if (ticket.SupplierIsSale == false)
                    {
                        ticket.SupplierIsSale = true;
                        ticket.UpdateTime = DateTime.Now;
                    }
                }
            }

            //日历渠道价格
            foreach (var calendarPriceChannelType in calendarPriceChannelTypes)
            {
                //同步渠道类型
                var syncChannelTypeItem = _baseService.MappingSyncChannelType(calendarPriceChannelType);

                //查询门票的渠道端配置
                var currentOatChannelSetting = otaChannelSettings.FirstOrDefault(x => x.TicketId == ticket.Id
                    && x.PriceInventorySyncChannelType == syncChannelTypeItem);


                //当前渠道的日历价数据
                var ticketDbChannelPrices = dbCalendarPrices
                    .Where(x => x.PriceChannelType == calendarPriceChannelType && x.TicketsId == ticket.Id).ToList();

                if (isTimeSlot)
                {
                    //本地有时段，且有查询到对应时段价格库存，更新本地时段价格库存，无价格的缺口日期清空；
                    //本地有时段，且没查询到对应时段价格库存，本地时段价格库存清空；
                    foreach (var timeSlot in ticketTimeSlots)
                    {
                        //是否和供货方数据匹配
                        var isMatching = thirdTimeSlots.Contains(timeSlot.Time);
                        
                        //当前渠道日历价汇总
                        var currentChannelPrices = ticketDbChannelPrices.Where(x => x.TimeSlotId == timeSlot.Id).ToList();
                        //供应端日历价库汇总
                        var currentThirdSchedules =
                            isMatching
                                ? thirdSchedules.Where(x => x.Date.TimeOfDay == timeSlot.Time).ToList()
                                : new List<SyncThirdInventorySchedulesDto>();

                        //saas时段库存
                        var timeSlotInvs = ticketDbCalendarInventories.Where(x => x.TimeSlotId == timeSlot.Id).ToList();
                        var matchingResult = await MatchingProcess(currentChannelPrices,
                            new()
                            {
                                TicketId = ticket.Id,
                                ScenicSpotId = ticket.ScenicSpotId,
                                TimeSlotId = timeSlot.Id,
                                TenantId = ticket.TenantId,
                                ExchangeRate = costExchangeRate,
                                SaasCostPriceExchangeRate = saasCostPriceExchangeRate,
                                PriceChannelType = calendarPriceChannelType,
                                SyncChannelTypeItem = syncChannelTypeItem,
                                PriceInventorySyncChannels = priceInventorySyncChannels,
                                ThirdSchedules = currentThirdSchedules,
                                SaasInventories = timeSlotInvs,
                                EditTicketSyncInfo = editTicketSyncInfo,
                                IsExperienceOzEdit = input.IsExperienceOzEdit,
                                SupplierPriceInventorySyncType = currentOpenSupplierSetting.PriceInventorySyncType,
                                ChannelPriceInventorySyncType = currentOatChannelSetting?.PriceInventorySyncType,
                                ChannelFullPush = input.ChannelFullPush
                            });

                        pushChannelData.AddRange(matchingResult.pushChannelData);
                        syncSaasCalendarInventories.AddRange(matchingResult.syncCalendarInvData);
                        syncSaasTimeSlotCalendarInventories.AddRange(matchingResult.syncTimeSlotCalendarInvData);
                        newCalendarPrices.AddRange(matchingResult.newCalendarPrices);
                    }

                    //非时段=>时段
                    if (input.EditTicketSyncInfo.PriceInventoryTypeChange)
                    {
                        //当前渠道日历价汇总
                        var notTimeSlotCp = ticketDbChannelPrices.Where(x => !x.TimeSlotId.HasValue)
                            .ToList();
                        var matchingResult = await PushEmptyNormalCalendarProcess(
                            notTimeSlotCp,
                            new()
                            {
                                TicketId = ticket.Id,
                                ScenicSpotId = ticket.ScenicSpotId,
                                TenantId = ticket.TenantId,
                                ExchangeRate = costExchangeRate,
                                PriceChannelType = calendarPriceChannelType,
                                SyncChannelTypeItem = syncChannelTypeItem,
                                PriceInventorySyncChannels = priceInventorySyncChannels,
                                EditTicketSyncInfo = editTicketSyncInfo,
                                SupplierPriceInventorySyncType = currentOpenSupplierSetting.PriceInventorySyncType,
                                ChannelPriceInventorySyncType = currentOatChannelSetting?.PriceInventorySyncType,
                                ChannelFullPush = input.ChannelFullPush
                            });
                        pushChannelData.AddRange(matchingResult.pushChannelData);
                        syncSaasCalendarInventories.AddRange(matchingResult.syncCalendarInvData);
                    }
                }
                else
                {
                    //是否和供货方数据匹配
                    var isMatching = !thirdIsTimeSlot;

                    //当前渠道日历价汇总
                    var currentChannelPrices = ticketDbChannelPrices
                        .Where(x => !x.TimeSlotId.HasValue)
                        .ToList();
                    //供应端日历价库汇总
                    var currentThirdSchedules = isMatching
                        ? thirdSchedules
                        : new List<SyncThirdInventorySchedulesDto>();

                    var matchingResult = await MatchingProcess(currentChannelPrices,
                        new()
                        {
                            TicketId = ticket.Id,
                            ScenicSpotId = ticket.ScenicSpotId,
                            TenantId = ticket.TenantId,
                            ExchangeRate = costExchangeRate,
                            SaasCostPriceExchangeRate = saasCostPriceExchangeRate,
                            PriceChannelType = calendarPriceChannelType,
                            SyncChannelTypeItem = syncChannelTypeItem,
                            PriceInventorySyncChannels = priceInventorySyncChannels,
                            ThirdSchedules = currentThirdSchedules,
                            SaasInventories = ticketDbCalendarInventories,
                            EditTicketSyncInfo = editTicketSyncInfo,
                            IsExperienceOzEdit = input.IsExperienceOzEdit,
                            SupplierPriceInventorySyncType = currentOpenSupplierSetting.PriceInventorySyncType,
                            ChannelPriceInventorySyncType = currentOatChannelSetting?.PriceInventorySyncType,
                            ChannelFullPush = input.ChannelFullPush
                        });

                    pushChannelData.AddRange(matchingResult.pushChannelData);
                    syncSaasCalendarInventories.AddRange(matchingResult.syncCalendarInvData);
                    syncSaasTimeSlotCalendarInventories.AddRange(matchingResult.syncTimeSlotCalendarInvData);
                    newCalendarPrices.AddRange(matchingResult.newCalendarPrices);

                    //判断是否存在冗余的错误时段数据
                    var deleteTimeSlots = ticketDbChannelPrices
                        .Where(x => x.TimeSlotId.HasValue)
                        .Select(x => new TicketTimeSlotInfo
                        {
                            TimeSlotId = x.TimeSlotId!.Value,
                            TicketId = x.TicketsId,
                            Time = x.Date.TimeOfDay,
                        })
                        .ToList();
                    if (deleteTimeSlots.Any())
                    {
                        pushEmptyTimeSlots.AddRange(deleteTimeSlots);
                    }

                }

                //时段推空
                if (pushEmptyTimeSlots.Any())
                {
                    List<PushOpenChannelPriceStockChannelDataDto> pushEmptyTimeSlotChannelData = new();
                    List<BatchSyncThirdTimeSlotInventoryItem> syncEmptyTimeSlotCalendarInvData = new();
                    var pushEmptyTimeSlotDays = endTime.Subtract(beginTime).Days;
                    foreach (var pushEmptyItem in pushEmptyTimeSlots)
                    {
                        var time = pushEmptyItem.Time;
                        for (int i = 0; i <= pushEmptyTimeSlotDays; i++)
                        {
                            var date = beginTime.AddDays(i);
                            //同步分销渠道数据
                            if (syncChannelTypeItem.HasValue && currentOatChannelSetting?.PriceInventorySyncType != PriceInventorySyncType.NoNeedSync)
                            {
                                //处理变更同步的数据
                                pushEmptyTimeSlotChannelData.Add(new()
                                {
                                    TicketId = pushEmptyItem.TicketId,
                                    TimeSlotId = pushEmptyItem.TimeSlotId,
                                    Date = date,
                                    Price = 0,
                                    Stock = 0,
                                    SyncChannelType = syncChannelTypeItem!.Value
                                });
                            }

                            //更新saas时段日历库存 此处不关注-供应端的同步配置
                            syncEmptyTimeSlotCalendarInvData.Add(new BatchSyncThirdTimeSlotInventoryItem
                            {
                                ProductId = ticket.ScenicSpotId,
                                SkuId = pushEmptyItem.TicketId,
                                TimeSlotId = pushEmptyItem.TimeSlotId,
                                Date = date,
                                Time = time,
                                TotalQuantity = 0,
                                AvailableQuantity = 0
                            });
                        }
                    }
                    
                    pushChannelData.AddRange(pushEmptyTimeSlotChannelData);
                    syncSaasTimeSlotCalendarInventories.AddRange(syncEmptyTimeSlotCalendarInvData);
                }
            }

            await _dbContext.AddRangeAsync(newCalendarPrices);
            
            //分销端数据同步
            syncOpenChannelDataList.Add(new(ticket, pushChannelData, otaChannelSettings, input.EditTicketSyncInfo));
            
            syncSaasCalendarInventoryDataList.Add(new (ticket.TenantId, otherDatesPushEmpty, syncSaasCalendarInventories));
            syncSaasTimeSlotInventoryDataList.Add(new (ticket.TenantId, otherDatesPushEmpty, syncSaasTimeSlotCalendarInventories));
        }
        
        await _dbContext.SaveChangesAsync();
        
        //记录操作日志
        if (syncLogs.Any())
        {
            _ = _baseService.AddOpenPlatformPricingSyncLogs(new AddSyncLogInput {Logs = syncLogs});
        }
        
        //分销端数据同步
        await SyncChannelPriceStockProcess(syncOpenChannelDataList);

        //Saas同步库存
        await SyncSaasCalendarInventory(syncSaasCalendarInventoryDataList.ToArray());
        await SyncSaasTimeSlotInventory(syncSaasTimeSlotInventoryDataList.ToArray());
    }

    private async Task<(List<PushOpenChannelPriceStockChannelDataDto> pushChannelData,
        List<BatchSyncThirdCalendarInventoryItem> syncCalendarInvData,
        List<BatchSyncThirdTimeSlotInventoryItem> syncTimeSlotCalendarInvData,
        List<TicketsCalendarPrice> newCalendarPrices)> MatchingProcess(
        List<TicketsCalendarPrice> currentChannelPriceList,
        SyncMatchingProcessDtoInput input)
    {
        List<PushOpenChannelPriceStockChannelDataDto> pushChannelData = new();
        List<BatchSyncThirdCalendarInventoryItem> syncCalendarInvData = new();
        List<BatchSyncThirdTimeSlotInventoryItem> syncTimeSlotCalendarInvData = new();
        List<TicketsCalendarPrice> newCalendarPrices = new();

        #region 临时处理重复日期数据.后续移除

        var repeatedChannelPrices = new List<TicketsCalendarPrice>();
        var currentChannelPrices = new List<TicketsCalendarPrice>();
        foreach (var groupItem in currentChannelPriceList.GroupBy(x=> new {x.Date,x.PriceChannelType}))
        {
            var groupPriceList = groupItem.OrderByDescending(x => x.Id).ToList();
            //取出每个渠道最新的一条数据,剩下的移除
            if (groupPriceList.Count > 1)
            {
                repeatedChannelPrices.AddRange(groupPriceList.Skip(1));
                groupPriceList = groupPriceList.Take(1).ToList();
            }

            currentChannelPrices.AddRange(groupPriceList);
        }
        if(repeatedChannelPrices.Any())
            _dbContext.RemoveRange(repeatedChannelPrices);
        #endregion
        
        
        //saas日历价日期
        var currentChannelDates = currentChannelPrices.Select(x => x.Date).Distinct().ToList();
        //供应端日期汇总
        var thirdDateList = input.ThirdSchedules.Select(x => x.Date).Distinct().ToList();
        //计算相同日期数据
        var intersectDateList = thirdDateList.Intersect(currentChannelDates).ToList();
        //计算供应端新增日期数据
        var addDateList = thirdDateList.Except(currentChannelDates).ToList();
        //计算供应端缺失日期数据
        var removeDateList = currentChannelDates.Except(thirdDateList).ToList();

        //移除`同步`的渠道或者`同步`渠道变更为`无需同步`
        var isMissingChannel = input.SyncChannelTypeItem.HasValue &&
                               input.EditTicketSyncInfo.PushEmptyOtaChannelSettings
                                   .Any(x => x.PriceInventorySyncChannelType == input.SyncChannelTypeItem.Value);
        if (isMissingChannel)
        {
            //修改当前渠道的同步类型
            input.ChannelPriceInventorySyncType =
                input.EditTicketSyncInfo.PushEmptyOtaChannelSettings
                    .FirstOrDefault(x => x.PriceInventorySyncChannelType == input.SyncChannelTypeItem!.Value)
                    ?.PriceInventorySyncType ?? PriceInventorySyncType.NoNeedSync;
        }

        //是否新增渠道
        var isNewSyncOtaChannel = input.SyncChannelTypeItem.HasValue &&
                                  input.EditTicketSyncInfo.NewSyncOtaChannels.Contains(input.SyncChannelTypeItem
                                      .Value);

        if (intersectDateList.Any())
        {
            //价库更新
            foreach (var stockItem in input.ThirdSchedules.Where(x => intersectDateList.Contains(x.Date)))
            {
                var currentDate = stockItem.Date;
                var currentTime = stockItem.Date.TimeOfDay;
                var currentDatePrice = currentChannelPrices
                    .Where(x => x.Date == currentDate)
                    .MaxBy(x => x.CreateTime);
                var dateStock = input.SaasInventories
                    .SelectMany(x => x.Schedules)
                    .FirstOrDefault(x => x.Date.Add(x.Time) == stockItem.Date);

                //供应端有效库存
                var avaQty = stockItem.AvailableQuantity;
                var totalQty = stockItem.AvailableQuantity > stockItem.TotalQuantity
                    ? stockItem.AvailableQuantity
                    : stockItem.TotalQuantity;

                //供应端采购价
                decimal? newPrice = stockItem.Price.HasValue
                    ? Math.Round(stockItem.Price.Value * input.ExchangeRate, 2)
                    : null;

                //渠道同步(渠道类型无变动或者全量推送或者价库有变动)
                var canChannelSync = isMissingChannel || isNewSyncOtaChannel || input.ChannelFullPush;

                //供应端-同步价格
                int channelPrice = isMissingChannel
                    ? 0
                    : (currentDatePrice.SellingPrice.HasValue
                        ? Convert.ToInt32(currentDatePrice.SellingPrice * 100)
                        : 0); //分销端同步渠道价格,默认当前配置的价格 单位:分
                
                if (input.SupplierPriceInventorySyncType is PriceInventorySyncType.SyncPrice or PriceInventorySyncType.SyncAll)
                {
                    //计算价格调整后的售价
                    //价格调整类型为指定，且调整值为null时，赋值为售价,兼容旧数据
                    if (currentDatePrice is
                        {
                            PriceBasisType: PriceBasisType.Designated,
                            PriceAdjustmentType: PriceAdjustmentType.Designated,
                            PriceAdjustmentValue: null
                        })
                    {
                        currentDatePrice.PriceAdjustmentValue = currentDatePrice.SellingPrice;
                    }

                    //saas采购价
                    var costPrice = newPrice;
                    if (!input.ChannelFullPush)//不需要全量推送才判断价格是否有变动
                    {
                        canChannelSync = canChannelSync || (newPrice != currentDatePrice.CostPrice); //价格有变动需要同步渠道
                    }
                    //saas售价
                    var sellingPrice = _baseService.CalcAdjustmentSellingPrice(new CalcPriceAdjustmentInput
                    {
                        PriceBasisType = currentDatePrice.PriceBasisType,
                        PriceAdjustmentType = currentDatePrice.PriceAdjustmentType,
                        PriceAdjustmentValue = currentDatePrice.PriceAdjustmentValue,
                        CostPrice = costPrice,
                        CostPriceExchangeRate = input.SaasCostPriceExchangeRate
                    });

                    channelPrice = isMissingChannel
                        ? 0
                        : (sellingPrice.HasValue
                            ? Convert.ToInt32(sellingPrice * 100)
                            : 0); //分销端同步渠道价格

                    //更新日历价
                    currentDatePrice.CostPrice = costPrice;
                    currentDatePrice.SellingPrice = isMissingChannel ? null : sellingPrice;
                }

                //供应商 - 同步库存
                int channelAvaQty = dateStock?.AvailableQuantity ?? 0;//分销端同步库存,默认当前配置的库存
                if (input.SupplierPriceInventorySyncType is PriceInventorySyncType.SyncInventory or PriceInventorySyncType.SyncAll)
                {
                    if (input.TimeSlotId != null)
                    {
                        //更新saas时段日历库存
                        syncTimeSlotCalendarInvData.Add(new BatchSyncThirdTimeSlotInventoryItem
                        {
                            ProductId = input.ScenicSpotId,
                            SkuId = input.TicketId,
                            TimeSlotId = input.TimeSlotId!.Value,
                            Date = currentDate.Date,
                            Time = currentTime,
                            TotalQuantity = totalQty,
                            AvailableQuantity = avaQty
                        });
                    }
                    else
                    {
                        //更新saas日历库存
                        syncCalendarInvData.Add(new BatchSyncThirdCalendarInventoryItem
                        {
                            ProductId = input.ScenicSpotId,
                            SkuId = input.TicketId,
                            Date = currentDate.Date,
                            TotalQuantity = totalQty,
                            AvailableQuantity = avaQty
                        });
                    }

                    channelAvaQty = isMissingChannel ? 0 : avaQty; //分销端同步库存
                    if (!input.ChannelFullPush)//不需要全量推送才判断库存是否有变动
                    {
                        canChannelSync = canChannelSync || (channelAvaQty != (dateStock?.AvailableQuantity ?? 0)); //库存有变动需要同步渠道
                    }
                }


                //渠道同步数据.判断是否能同步渠道
                if (canChannelSync && input.ChannelPriceInventorySyncType != PriceInventorySyncType.NoNeedSync)
                {
                    if (input.SyncChannelTypeItem.HasValue)
                    {
                        //处理变更同步的数据
                        pushChannelData.Add(new()
                        {
                            TicketId = input.TicketId,
                            TimeSlotId = input.TimeSlotId,
                            Date = currentDate,
                            Price = channelPrice,
                            Stock = channelPrice == 0 ? 0 : channelAvaQty,
                            SyncChannelType = input.SyncChannelTypeItem!.Value
                        });
                    }
                }
            }
        }

        if (addDateList.Any())
        {
            //新增价库
            foreach (var stockItem in input.ThirdSchedules.Where(x => addDateList.Contains(x.Date)))
            {
                var currentDate = stockItem.Date;
                var currentTime = stockItem.Date.TimeOfDay;

                //供应端有效库存
                var avaQty = stockItem.AvailableQuantity;
                var totalQty = stockItem.AvailableQuantity > stockItem.TotalQuantity
                    ? stockItem.AvailableQuantity
                    : stockItem.TotalQuantity;

                //供应端采购价
                decimal? newPrice = stockItem.Price.HasValue
                    ? Math.Round(stockItem.Price.Value * input.ExchangeRate, 2)
                    : null;

                //供应端-同步价格
                if (input.SupplierPriceInventorySyncType is PriceInventorySyncType.SyncPrice or PriceInventorySyncType.SyncAll)
                {
                    var defaultChannelPrice = input.EditTicketSyncInfo.ChannelDefaultSellingPrices
                        .FirstOrDefault(x => x.ChannelType == input.PriceChannelType
                                             && x.Date.Date == currentDate.Date);

                    decimal? sellingPrice = null;
                    if (defaultChannelPrice != null)
                    {
                        //计算价格调整后的售价
                        //价格调整类型为指定，且调整值为null时，赋值为售价,兼容旧数据
                        if (defaultChannelPrice is
                            {
                                PriceBasisType: PriceBasisType.Designated,
                                PriceAdjustmentType: PriceAdjustmentType.Designated,
                                PriceAdjustmentValue: null
                            })
                        {
                            defaultChannelPrice.PriceAdjustmentValue = defaultChannelPrice.SellingPrice;
                        }

                        sellingPrice = _baseService.CalcAdjustmentSellingPrice(new CalcPriceAdjustmentInput
                        {
                            PriceBasisType = defaultChannelPrice.PriceBasisType,
                            PriceAdjustmentType = defaultChannelPrice.PriceAdjustmentType,
                            PriceAdjustmentValue = defaultChannelPrice.PriceAdjustmentValue,
                            CostPrice = newPrice,
                            CostPriceExchangeRate = input.SaasCostPriceExchangeRate
                        });
                    }

                    //插入渠道日历价
                    var newCalendarPrice = new TicketsCalendarPrice
                    {
                        ScenicSpotId = input.ScenicSpotId,
                        TicketsId = input.TicketId,
                        TimeSlotId = input.TimeSlotId,
                        Date = currentDate,
                        CostPrice = newPrice,
                        SellingPrice = sellingPrice,
                        PriceChannelType = input.PriceChannelType,
                        PriceBasisType = defaultChannelPrice?.PriceBasisType ?? PriceBasisType.Designated,
                        PriceAdjustmentType = defaultChannelPrice?.PriceAdjustmentType ?? PriceAdjustmentType.Designated,
                        PriceAdjustmentValue = defaultChannelPrice?.PriceAdjustmentValue ?? sellingPrice,
                    };
                    newCalendarPrice.SetTenantId(input.TenantId);
                    newCalendarPrices.Add(newCalendarPrice);
                }

                //供应商 - 同步库存
                if (input.SupplierPriceInventorySyncType is PriceInventorySyncType.SyncInventory or PriceInventorySyncType.SyncAll)
                {
                    //saas库存数据
                    if (input.TimeSlotId != null)
                    {
                        //更新saas时段日历库存
                        syncTimeSlotCalendarInvData.Add(new BatchSyncThirdTimeSlotInventoryItem
                        {
                            ProductId = input.ScenicSpotId,
                            SkuId = input.TicketId,
                            TimeSlotId = input.TimeSlotId!.Value,
                            Date = currentDate.Date,
                            Time = currentTime,
                            TotalQuantity = totalQty,
                            AvailableQuantity = avaQty
                        });
                    }
                    else
                    {
                        //更新saas日历库存
                        syncCalendarInvData.Add(new BatchSyncThirdCalendarInventoryItem
                        {
                            ProductId = input.ScenicSpotId,
                            SkuId = input.TicketId,
                            Date = currentDate.Date,
                            TotalQuantity = totalQty,
                            AvailableQuantity = avaQty
                        });
                    }
                }

                //渠道同步数据
                if (input.SyncChannelTypeItem.HasValue &&
                    input.ChannelPriceInventorySyncType != PriceInventorySyncType.NoNeedSync)
                {
                    //处理变更同步的数据(无售价所以库存同步0)
                    pushChannelData.Add(new()
                    {
                        TicketId = input.TicketId,
                        TimeSlotId = input.TimeSlotId,
                        Date = currentDate,
                        Price = 0,
                        Stock = 0,
                        SyncChannelType = input.SyncChannelTypeItem!.Value
                    });
                }
            }
        }

        if (removeDateList.Any())
        {
            //特殊处理澳新-在非同步的情况下.澳新不做推空处理,因为澳新产品编辑的时候是查不到价库数据的
            if (!input.IsExperienceOzEdit)
            {
                //推空价库
                foreach (var calendarPriceItem in currentChannelPrices.Where(x => removeDateList.Contains(x.Date)))
                {
                    var isTimeSlot = calendarPriceItem.TimeSlotId.HasValue;
                    var currentDate = calendarPriceItem.Date;
                    var currentTimeSlot = calendarPriceItem.Date.TimeOfDay;
                    var timeSlotId = calendarPriceItem.TimeSlotId;
                    var avaQty = 0;
                    var totalQty = 0;

                    //供应端-同步价格
                    if (input.SupplierPriceInventorySyncType is PriceInventorySyncType.SyncPrice
                        or PriceInventorySyncType.SyncAll)
                    {
                        calendarPriceItem.CostPrice = null;

                        //价格调整类型为指定，且调整值为null时，赋值为售价,兼容旧数据
                        if (calendarPriceItem is
                            {
                                PriceBasisType: PriceBasisType.Designated,
                                PriceAdjustmentType: PriceAdjustmentType.Designated,
                                PriceAdjustmentValue: null
                            })
                        {
                            calendarPriceItem.PriceAdjustmentValue = calendarPriceItem.SellingPrice;
                        }

                        calendarPriceItem.SellingPrice = _baseService.CalcAdjustmentSellingPrice(
                            new CalcPriceAdjustmentInput
                            {
                                PriceBasisType = calendarPriceItem.PriceBasisType,
                                PriceAdjustmentType = calendarPriceItem.PriceAdjustmentType,
                                PriceAdjustmentValue = calendarPriceItem.PriceAdjustmentValue,
                                CostPrice = calendarPriceItem.CostPrice,
                                CostPriceExchangeRate = input.SaasCostPriceExchangeRate
                            });
                    }

                    //供应商-同步库存
                    if (input.SupplierPriceInventorySyncType is PriceInventorySyncType.SyncInventory
                        or PriceInventorySyncType.SyncAll)
                    {
                        if (isTimeSlot)
                        {
                            //更新saas时段日历库存
                            syncTimeSlotCalendarInvData.Add(new BatchSyncThirdTimeSlotInventoryItem
                            {
                                ProductId = input.ScenicSpotId,
                                SkuId = input.TicketId,
                                TimeSlotId = calendarPriceItem.TimeSlotId!.Value,
                                Date = currentDate.Date,
                                Time = currentTimeSlot,
                                TotalQuantity = totalQty,
                                AvailableQuantity = avaQty
                            });
                        }
                        else
                        {
                            //更新saas日历库存
                            syncCalendarInvData.Add(new BatchSyncThirdCalendarInventoryItem
                            {
                                ProductId = input.ScenicSpotId,
                                SkuId = input.TicketId,
                                Date = currentDate.Date,
                                TotalQuantity = totalQty,
                                AvailableQuantity = avaQty
                            });
                        }
                    }

                    //同步分销渠道数据
                    if (input.SyncChannelTypeItem.HasValue && input.ChannelPriceInventorySyncType != PriceInventorySyncType.NoNeedSync)
                    {
                        //处理变更同步的数据
                        pushChannelData.Add(new()
                        {
                            TicketId = input.TicketId,
                            TimeSlotId = timeSlotId,
                            Date = currentDate,
                            Price = 0,
                            Stock = 0,
                            SyncChannelType = input.SyncChannelTypeItem!.Value
                        });
                    }
                }
            }
        }
        
        //特殊处理澳新-移除的渠道的数据推空,因为澳新产品编辑的时候是查不到价库数据的
        
        if (input.IsExperienceOzEdit && isMissingChannel)
        {
            //移除渠道价库推空
            foreach (var calendarPriceItem in currentChannelPrices)
            {
                var currentDate = calendarPriceItem.Date;
                var timeSlotId = calendarPriceItem.TimeSlotId;
                var avaQty = 0;
                var totalQty = 0;
                
                //处理变更同步的数据
                pushChannelData.Add(new()
                {
                    TicketId = input.TicketId,
                    TimeSlotId = timeSlotId,
                    Date = currentDate,
                    Price = 0,
                    Stock = 0,
                    SyncChannelType = input.SyncChannelTypeItem!.Value
                });
            }
        }

        return (pushChannelData, syncCalendarInvData, syncTimeSlotCalendarInvData, newCalendarPrices);
    }

    /// <summary>
    /// 清空移除普通日历价
    /// </summary>
    private async Task<(List<PushOpenChannelPriceStockChannelDataDto> pushChannelData,
        List<BatchSyncThirdCalendarInventoryItem> syncCalendarInvData)> PushEmptyNormalCalendarProcess(
        List<TicketsCalendarPrice> currentChannelPrices,
        SyncMatchingProcessDtoInput input)
    {
        List<PushOpenChannelPriceStockChannelDataDto> pushChannelData = new();
        List<BatchSyncThirdCalendarInventoryItem> syncCalendarInvData = new();

        if (input.EditTicketSyncInfo.PriceInventoryTypeChange)
        {
            foreach (var item in currentChannelPrices)
            {
                //同步分销渠道数据
                if (input.SyncChannelTypeItem.HasValue && input.ChannelPriceInventorySyncType != PriceInventorySyncType.NoNeedSync)
                {
                    //处理变更同步的数据
                    pushChannelData.Add(new()
                    {
                        TicketId = input.TicketId,
                        Date = item.Date.Date,
                        Price = 0,
                        Stock = 0,
                        SyncChannelType = input.SyncChannelTypeItem!.Value
                    });
                }

                //更新saas日历库存 此处不关注-供应端的同步配置
                syncCalendarInvData.Add(new BatchSyncThirdCalendarInventoryItem
                {
                    ProductId = input.ScenicSpotId,
                    SkuId = input.TicketId,
                    Date = item.Date.Date,
                    TotalQuantity = 0,
                    AvailableQuantity = 0
                });
            }
            //移除 此处不关注-供应端的同步配置
            _dbContext.RemoveRange(currentChannelPrices);
        }

        return (pushChannelData, syncCalendarInvData);
    }

    /// <summary>
    /// 以日历价数据为基础进行渠道同步
    /// </summary>
    private async Task ChannelDataSyncByPriceProcess(
        List<TicketsTimeSlot> timeSlots,
        List<TicketsCalendarPrice> calendarPrices,
        bool isPutEmpty,
        params Tickets[] tickets)
    {
        var channelSyncRequest = new List<SyncChannelPriceStockUploadDto>(); //分销渠道同步
        var calendarInventories = new List<NotifySyncStockDto>(); //库存数据
        var syncChannelTypes = Enum.GetValues<PriceInventorySyncChannelType>(); //同步渠道枚举数据

        #region 查出日期范围的库存数据

        if (calendarPrices.Any() is false) return;

        //需要同步的日期范围
        var syncBeginDate = calendarPrices.Min(x => x.Date.Date);
        var syncEndDate = calendarPrices.Max(x => x.Date.Date);
        var ticketIds = tickets.Select(x => x.Id).ToList();
        var timeSlotIds = timeSlots.Select(x => x.Id).ToList();

        if (!isPutEmpty)
        {
            if (timeSlots.Any())
            {
                //时段日历库存
                calendarInventories = await GetTimeSlotInventories(new GetTimeSlotInventoryInput
                {
                    StartDate = syncBeginDate,
                    EndDate = syncEndDate,
                    SkuIds = ticketIds,
                    TimeSlotIds = timeSlotIds
                });
            }
            else
            {
                //日历库存
                calendarInventories = await GetCalendarInventories(new GetCalendarInventoryInput
                {
                    CalendarProducts = tickets.GroupBy(x => x.ScenicSpotId)
                        .Select(x => new CalendarProduct { ProductId = x.Key, ItemIds = x.Select(t => t.Id).ToList() }),
                    StartDate = syncBeginDate,
                    EndDate = syncEndDate
                });
            }
        }

        #endregion

        //ota渠道同步配置
        var otaChannelSettings = await _dbContext.TicketOtaChannelSettings
            .AsNoTracking()
            .Where(x => ticketIds.Contains(x.TicketId))
            .Where(x=>x.SettingType == OpenChannelSettingType.PriceInventorySync)
            .ToListAsync();
        var otaChanelSettingItems = await _dbContext.TicketOtaChannelSettingItems.AsNoTracking()
            .Where(x => ticketIds.Contains(x.TicketId))
            .ToListAsync();

        //填充同步的数据
        foreach (var ticket in tickets)
        {
            if (ticket.PriceInventorySource == PriceInventorySource.System) continue;

            //当前门票同步的渠道信息
            var syncChannels = Enum.GetValues<PriceInventorySyncChannelType>()
                .Where(x => (x & ticket.PriceInventorySyncChannelTypes) == x)
                .ToList();

            //补充同步渠道数据
            foreach (var syncChannelTypeItem in syncChannels)
            {
                var syncSchedules = new List<SyncChannelPriceStockUploadMessageItem>(); //分销渠道同步子项
                var currentOtaChannelSetting = otaChannelSettings.FirstOrDefault(x => x.TicketId == ticket.Id
                    && x.PriceInventorySyncChannelType == syncChannelTypeItem);

                if (currentOtaChannelSetting == null)
                    continue; //无需同步
                if (currentOtaChannelSetting.PriceInventorySyncType == PriceInventorySyncType.NoNeedSync)
                    continue; //无需同步
                
                var currentOtaChannelSettingItems = otaChanelSettingItems.Where(x => x.TicketOtaChannelSettingId == currentOtaChannelSetting.Id)
                    .ToList();
                var syncSkuIds = currentOtaChannelSettingItems.Select(x => x.SyncSkuId).ToList();

                //当前门票同步的库存
                var syncCalendarInventories = calendarInventories
                    .Where(x => x.SkuId == ticket.Id)
                    .ToList();

                //当前同步的时段
                var syncTimeSlots = timeSlots.Where(x => x.TicketsId == ticket.Id)
                    .ToList();

                //门票对应渠道类型
                var calendarPriceChannelType = _baseService.MappingCalendarPriceChannelType(syncChannelTypeItem);
                //当前门票同步的日历价
                var syncCalendarPrices = calendarPrices
                    .Where(x => x.TicketsId == ticket.Id)
                    .Where(x => x.PriceChannelType == calendarPriceChannelType)
                    .ToList();

                //循环日历价
                foreach (var syncPriceItem in syncCalendarPrices)
                {
                    var sellingPrice = isPutEmpty
                        ? 0
                        : syncPriceItem.SellingPrice.HasValue
                            ? Convert.ToInt32(syncPriceItem.SellingPrice * 100)
                            : 0;
                    var avaQty = 0;

                    if (syncPriceItem.TimeSlotId.HasValue)
                    {
                        //查询对应时段信息
                        var syncTimeSlot = syncTimeSlots.FirstOrDefault(x => x.Id == syncPriceItem.TimeSlotId);
                        if (syncTimeSlot != null)
                        {
                            var syncInvItem = syncCalendarInventories
                                .Where(x => x.TimeSlotId == syncTimeSlot.Id)
                                .SelectMany(x => x.Schedules)
                                .FirstOrDefault(x => x.Date.Add(x.Time) == syncPriceItem.Date);
                            avaQty = syncInvItem?.AvailableQuantity ?? 0;
                            //0库存阈值
                            if(avaQty <= currentOtaChannelSetting.ZeroStockThreshold)
                                avaQty = 0;

                            syncSchedules.Add(new SyncChannelPriceStockUploadMessageItem
                            {
                                OutSkuId = syncTimeSlot.Id,
                                Date = syncPriceItem.Date,
                                Price = sellingPrice,
                                Stock = sellingPrice == 0
                                    ? 0
                                    : avaQty
                            });
                        }
                    }
                    else
                    {
                        var syncInvItem = syncCalendarInventories
                            .SelectMany(x => x.Schedules)
                            .FirstOrDefault(x => x.Date == syncPriceItem.Date);
                        avaQty = syncInvItem?.AvailableQuantity ?? 0;
                        //0库存阈值
                        if(avaQty <= currentOtaChannelSetting.ZeroStockThreshold)
                            avaQty = 0;
                        syncSchedules.Add(new SyncChannelPriceStockUploadMessageItem
                        {
                            OutSkuId = ticket.Id,
                            Date = syncPriceItem.Date,
                            Price = sellingPrice,
                            Stock = sellingPrice == 0
                                ? 0
                                : avaQty
                        });
                    }
                }
                
                
                //处理指定同步的skuIds数据
                if (syncSkuIds.Any())//存在指定同步的skuIds数据,过滤
                {
                    syncSchedules = syncSchedules.Where(x => syncSkuIds.Contains(x.OutSkuId))
                        .ToList();
                }

                if (syncSchedules.Any())
                {
                    var outProductId = !string.IsNullOrEmpty(currentOtaChannelSetting.SupplierProductId)
                        ? currentOtaChannelSetting.SupplierProductId
                        : ticket.Id.ToString();
                    channelSyncRequest.Add(new SyncChannelPriceStockUploadDto
                    {
                        TenantId = ticket.TenantId,
                        TicketId = ticket.Id,
                        TicketName = ticket.Name,
                        ScenicSpotId = ticket.ScenicSpotId,
                        OtaType = _baseService.MappingOtaChannelType(syncChannelTypeItem),
                        IsCreUpdate = true,
                        ProductType = OtaChannelProductType.Ticket,
                        OutProductId = outProductId,
                        OtaProductId = currentOtaChannelSetting?.ChannelProductId ?? string.Empty,
                        OtaSkuId = currentOtaChannelSetting.TicketArea ?? string.Empty,
                        Schedules = syncSchedules,
                        ScheduleType = currentOtaChannelSetting.PriceInventorySyncType switch
                        {
                            PriceInventorySyncType.SyncPrice => OpenChannelPriceStockUploadScheduleType.SyncPrice,
                            PriceInventorySyncType.SyncInventory => OpenChannelPriceStockUploadScheduleType.SyncInventory,
                            _ => OpenChannelPriceStockUploadScheduleType.SyncAll,
                        }
                    });
                }
            }
        }

        //同步
        await SyncChannelUploadDataSerializer(channelSyncRequest);
    }


    record SyncSaasCalendarInventoryData(long tenantId, bool otherDatesPushEmpty, IEnumerable<BatchSyncThirdCalendarInventoryItem> syncItem);
    /// <summary>
    /// 同步saas日历库存
    /// </summary>
    private async Task SyncSaasCalendarInventory(params SyncSaasCalendarInventoryData[] syncDataList)
    {
        if (syncDataList.Any() is false) return;
        foreach (var syncData in syncDataList)
        {
            var distinctItems = syncData.syncItem
                .DistinctBy(x => (x.ProductId, x.SkuId, x.Date))
                .ToList();
            distinctItems.ForEach(x => x.Enabled = true);
            
            var byteItem = MessagePack.MessagePackSerializer.Typeless.Serialize(distinctItems);//压缩
            BatchSyncThirdCalendarInventoryInput syncInput = new()
            {
                OtherDatesPushEmpty = syncData.otherDatesPushEmpty,
                ByteItem = byteItem
            };
            var headers = new List<KeyValuePair<string, string>>
            {
                new("tenant", syncData.tenantId.ToString())
            };
            _ =  _baseService.BatchSyncThirdCalendarInventory(syncInput,headers);
        }
    }

    record SyncSaasTimeSlotInventoryData(long tenantId, bool otherDatesPushEmpty, IEnumerable<BatchSyncThirdTimeSlotInventoryItem> syncItem);
    /// <summary>
    /// 同步saas时段库存
    /// </summary>
    private async Task SyncSaasTimeSlotInventory(params SyncSaasTimeSlotInventoryData[] syncDataList)
    {
        if (syncDataList.Any() is false) return;

        foreach (var syncData in syncDataList)
        {
            var distinctItems = syncData.syncItem
                .DistinctBy(x => (x.ProductId, x.SkuId, x.TimeSlotId, x.Date, x.Time))
                .ToList();
            distinctItems.ForEach(item => item.Enabled = true);
            
            var byteItem = MessagePack.MessagePackSerializer.Typeless.Serialize(distinctItems);//压缩
            BatchSyncThirdTimeSlotInventoryInput syncInput = new()
            {
                OtherDatesPushEmpty = syncData.otherDatesPushEmpty,
                ByteItem = byteItem
            };
            var headers = new List<KeyValuePair<string, string>>
            {
                new("tenant", syncData.tenantId.ToString())
            };
            _ =  _baseService.BatchSyncThirdTimeSlotInventory(syncInput,headers);
        }
    }

    record SyncOpenChannelData(Tickets ticket,
        IEnumerable<PushOpenChannelPriceStockChannelDataDto> pushChannelData,
        IEnumerable<TicketOtaChannelSettingInfo> otaChannelSettings,
        EditTicketSyncInfo editTicketSyncInfo);
    /// <summary>
    /// 分销端同步数据处理
    /// </summary>
    /// <param name="ticket">门票数据</param>
    /// <param name="pushChannelData">同步数据</param>
    /// <param name="otaChannelSettings">正常渠道配置</param>
    /// <param name="pushEmptyOtaChannelSettings">推空渠道配置</param>
    private async Task SyncChannelPriceStockProcess(List<SyncOpenChannelData> syncDataList)
    {
        foreach (var item in syncDataList)
        {
            //门票配置的同步渠道信息
            var dbSyncChannels = Enum.GetValues<PriceInventorySyncChannelType>()
                .Where(x => (x & item.ticket.PriceInventorySyncChannelTypes) == x)
                .ToList();
            var pushEmptySyncChannels = item.editTicketSyncInfo.PushEmptyOtaChannelSettings
                .Select(x => x.PriceInventorySyncChannelType).Distinct().ToList();
            var syncChannels = dbSyncChannels.Concat(pushEmptySyncChannels).Distinct().ToList();

            foreach (var syncChannel in syncChannels)
            {
                var syncChannelSchedules = item.pushChannelData
                    .Where(x => x.TicketId == item.ticket.Id && x.SyncChannelType == syncChannel)
                    .Select(x => new SyncChannelPriceStockUploadMessageItem
                    {
                        OutSkuId = x.TimeSlotId ?? x.TicketId, Date = x.Date, Price = x.Price, Stock = x.Stock
                    })
                    .ToList();

                if (syncChannelSchedules.Any())
                {
                    var requestMessages = new List<SyncChannelPriceStockUploadDto>();
                    var otaChannelSetting = item.otaChannelSettings.FirstOrDefault(x =>
                        x.TicketId == item.ticket.Id && x.PriceInventorySyncChannelType == syncChannel);
                    var pushEmptyOtaChannelSetting = item.editTicketSyncInfo.PushEmptyOtaChannelSettings.FirstOrDefault(x =>
                        x.TicketId == item.ticket.Id && x.PriceInventorySyncChannelType == syncChannel);

                    if (otaChannelSetting != null)
                    {
                        if (otaChannelSetting.PriceInventorySyncType != PriceInventorySyncType.NoNeedSync)
                        {
                            var outProductId = !string.IsNullOrEmpty(otaChannelSetting.SupplierProductId)
                                ? otaChannelSetting.SupplierProductId
                                : item.ticket.Id.ToString();

                            var otaSchedules = syncChannelSchedules;
                            if (otaChannelSetting.SyncSkuIds.Any()) //存在同步skuId,过滤
                            {
                                otaSchedules = otaSchedules
                                    .Where(x => otaChannelSetting.SyncSkuIds.Contains(x.OutSkuId))
                                    .ToList();
                            }
                            
                            //0库存阈值
                            foreach (var otaSchedulesItem in otaSchedules)
                            {
                                if (otaSchedulesItem.Stock <= otaChannelSetting.ZeroStockThreshold)
                                    otaSchedulesItem.Stock = 0;
                            }

                            var requestMessagesItem = new SyncChannelPriceStockUploadDto
                            {
                                TenantId = item.ticket.TenantId,
                                TicketId = item.ticket.Id,
                                TicketName = item.ticket.Name,
                                ScenicSpotId = item.ticket.ScenicSpotId,
                                OtaType = _baseService.MappingOtaChannelType(syncChannel),
                                IsCreUpdate = true,
                                ProductType = OtaChannelProductType.Ticket,
                                OutProductId = outProductId,
                                OtaProductId = otaChannelSetting.ChannelProductId ?? string.Empty,
                                OtaSkuId = otaChannelSetting.TicketArea ?? string.Empty,
                                ScheduleType = otaChannelSetting.PriceInventorySyncType switch
                                {
                                    PriceInventorySyncType.SyncPrice => OpenChannelPriceStockUploadScheduleType
                                        .SyncPrice,
                                    PriceInventorySyncType.SyncInventory => OpenChannelPriceStockUploadScheduleType
                                        .SyncInventory,
                                    _ => OpenChannelPriceStockUploadScheduleType.SyncAll
                                },
                                Schedules = otaSchedules
                            };
                            requestMessages.Add(requestMessagesItem);
                        }
                    }

                    //门票编辑的时候->推空旧配置
                    //[美团] - 暂时不做推空
                    if (pushEmptyOtaChannelSetting != null && syncChannel != PriceInventorySyncChannelType.Meituan)
                    {
                        var pushEmptyChannelSchedules = syncChannelSchedules
                            .Select(x => new SyncChannelPriceStockUploadMessageItem
                            {
                                OutSkuId = x.OutSkuId, Date = x.Date, Price = 0, Stock = 0
                            })
                            .ToList();

                        var outProductId = !string.IsNullOrEmpty(pushEmptyOtaChannelSetting.SupplierProductId)
                            ? pushEmptyOtaChannelSetting.SupplierProductId
                            : item.ticket.Id.ToString();
                        if (pushEmptyOtaChannelSetting.SyncSkuIds.Any()) //存在同步skuId,过滤
                        {
                            pushEmptyChannelSchedules = pushEmptyChannelSchedules.Where(x =>
                                    pushEmptyOtaChannelSetting.SyncSkuIds.Contains(x.OutSkuId))
                                .ToList();
                        }

                        requestMessages.Add(new SyncChannelPriceStockUploadDto
                        {
                            TenantId = item.ticket.TenantId,
                            TicketId = item.ticket.Id,
                            TicketName = item.ticket.Name,
                            ScenicSpotId = item.ticket.ScenicSpotId,
                            OtaType = _baseService.MappingOtaChannelType(syncChannel),
                            IsCreUpdate = true,
                            ProductType = OtaChannelProductType.Ticket,
                            OutProductId = outProductId,
                            OtaProductId = pushEmptyOtaChannelSetting.ChannelProductId ?? string.Empty,
                            OtaSkuId = pushEmptyOtaChannelSetting.TicketArea ?? string.Empty,
                            ScheduleType = OpenChannelPriceStockUploadScheduleType.SyncAll,
                            Schedules = pushEmptyChannelSchedules
                        });
                    }
                    
                    await SyncChannelUploadDataSerializer(requestMessages);
                }
            }
        }
    }

    /// <summary>
    /// 序列化渠道价库同步数据
    /// </summary>
    private async Task SyncChannelUploadDataSerializer(List<SyncChannelPriceStockUploadDto> syncChannelUploadDto)
    {
        var syncLogs = new List<AddSyncLogItem>();
        foreach (var item in syncChannelUploadDto.Where(x => x.Schedules.Any()))
        {
            var request = _mapper.Map<ChannelPriceStockUploadRequest>(item);
            request.Schedules = _mapper.Map<List<ChannelPriceStockUploadSchedulesItem>>(item.Schedules);
            var result = await _openChannelService.ChannelPriceStockUpload(request, item.TenantId);
            syncLogs.Add(new AddSyncLogItem
            {
                TenantId = item.TenantId,
                ProductType = ProductType.Scenic,
                PlatformType = OpenPlatformType.OpenChannel,
                ChannelType = item.OtaType,
                SyncType = OpenPlatformPricingSyncType.ActivePush,
                SyncResult = result.Code == _successCode
                    ? OpenPlatformPricingSyncResult.Success
                    : OpenPlatformPricingSyncResult.Failed,
                FailedMessage = result.Msg,
                ProductId = item.TicketId,
                ProductName = item.TicketName,
                SkuId = item.TicketId,
                SkuName = item.TicketName,
                SyncStartDate = item.Schedules.Min(x => x.Date),
                SyncEndDate = item.Schedules.Max(x => x.Date),
            });
        }

        if (syncLogs.Any())
        {
            _ = _baseService.AddOpenPlatformPricingSyncLogs(new AddSyncLogInput {Logs = syncLogs});
        }
    }
    
    #endregion

    #region 判断日期范围是否交叉

    private bool CheckForDateOverLap(List<UpdateThirdCalendarPriceItem> items)
    {
        var sortedItems = items.OrderBy(x => x.BeginDate).ToList();
        for (int i = 0; i < sortedItems.Count - 1; i++)
        {
            for (int j = i + 1; j < sortedItems.Count; j++)
            {
                if (IsDateRangeAndWeeksOverlap(sortedItems[i], sortedItems[j]))
                {
                    //存在交叉,返回
                    return true;
                }
            }
        }

        return false;
    }

    private bool IsDateRangeAndWeeksOverlap(UpdateThirdCalendarPriceItem range1, UpdateThirdCalendarPriceItem range2)
    {
        //判断日期是否有交叉
        if (range1.EndDate < range2.BeginDate || range1.BeginDate > range2.EndDate)
        {
            //日期没有交叉
            return false;
        }

        //存在日期交叉.判断是否存在weeks的交叉
        var intersectWeeks = range1.Weeks.Intersect(range2.Weeks).ToList();
        return intersectWeeks.Any();
    }

    #endregion

    #region 日历库存

    /// <summary>
    /// 查询时段日历库存
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private async Task<List<NotifySyncStockDto>> GetTimeSlotInventories(
        GetTimeSlotInventoryInput request)
    {
        var response = await _baseService.GetTimeSlotInventories(request);
        var result = response.Select(x =>
                new NotifySyncStockDto
                {
                    ProductId = x.ProductId,
                    SkuId = x.SkuId,
                    TimeSlotId = x.TimeSlotId,
                    Schedules = x.Inventories.Select(i => new NotifySyncStockSchedule
                    {
                        Date = i.Date,
                        Time = i.Time,
                        AvailableQuantity = i.AvailableQuantity
                    })
                })
            .ToList();
        return result;
    }

    /// <summary>
    /// 查询预订票日历价格库存
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private async Task<List<NotifySyncStockDto>> GetCalendarInventories(
        GetCalendarInventoryInput request)
    {
        request.LimitDateRange = false;
        var response = await _baseService.GetCalendarInventories(request);
        var result = response.Select(x =>
                new NotifySyncStockDto
                {
                    ProductId = x.ProductId,
                    SkuId = x.ItemId,
                    Schedules = x.Inventories.Select(i => new NotifySyncStockSchedule
                    {
                        Date = i.Date,
                        AvailableQuantity = i.AvailableQuantity
                    })
                })
            .ToList();
        return result;
    }

    #endregion

    #region sku信息和价库聚合查询

    /// <summary>
    /// api对接供应商如果不返回币种.默认门票配置的供应商币种
    /// </summary>
    /// <param name="input"></param>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    private async Task<(decimal CommissionRate,SupplierProductDetailSkuItem? SkuDetail, List<SupplierProductScheduleList> Schedules)>
        AggregateQueryProductDetailAndStocks(AggregateQueryProductDetailAndStocksInput input, long tenantId)
    {
        //供应商接口错误码
        var errorCode = 500;
        decimal exchangeRate = 1;
        var supplierTypeStr = input.OpenSupplierType.ToString().ToLowerInvariant();
        //查询产品信息
        var thirdProductDetail = await _openSupplierService.SupplierProductDetail(
            new SupplierProductDetailRequest
            {
                OutProductId = input.ActivityId,
                OutProductOptionId = input.PackageId,
                OutSkuId = input.SkuId,
                SupplierType = supplierTypeStr
            }, tenantId);
        if (thirdProductDetail.Code != _successCode)
        {
            throw new BusinessException(ErrorTypes.Scenic.QueryThirdProductDetailError);
        }
        
        var thirdSkuDetail = thirdProductDetail.Data.SkuList.FirstOrDefault();
        var commissionRate = thirdSkuDetail?.CommissionRate ?? 0;

        //查询第三方价库信息(根据同步配置来)
        var schedules = _mapper.Map<List<SupplierProductScheduleList>>(input.Schedules);
        if (schedules.Any() is false && !input.IsFromThirdInventorySync)//非价库同步通知.需要主动拉去价库
        {
            /*
             * 供应商拉取日历价库
             * 澳新比较特殊,只会返回3天的价格库存.需要异步通知
             * 其他供应商拉取日历价库,默认90天,循环拉取
             */
            if (input.OpenSupplierType != OpenSupplierType.Experienceoz)
            {
                //一般供应商拉取日历库存价格日期范围是90天.所以需要循环拉取
                var currentDateFrom = input.ScheduleStartDate;
                var maxDateTo = input.ScheduleEndDate;
                while (currentDateFrom < maxDateTo)
                {
                    var currentDateTo = currentDateFrom.AddDays(89);
                    if (currentDateTo > maxDateTo)
                        currentDateTo = maxDateTo;

                    var thirdSkuSchedule = await _openSupplierService.SupplierSkuSchedule(
                        new SupplierSkuScheduleRequest
                        {
                            OutProductId = input.ActivityId,
                            OutProductOptionId = input.PackageId,
                            OutSkuId = input.SkuId,
                            SupplierType = supplierTypeStr,
                            DateFrom = currentDateFrom,
                            DateTo = currentDateTo
                        }, tenantId);
                    if (thirdSkuSchedule.Code == errorCode)
                    {
                        //供应商接口返回错误码500(服务器错误)抛异常,其他业务异常继续执行
                        throw new BusinessException(ErrorTypes.Scenic.QueryThirdPriceInventoryError);
                    }

                    var currentSchedules = thirdSkuSchedule.Data?.Stocks ?? new List<SupplierProductScheduleList>();
                    schedules.AddRange(currentSchedules);

                    //设置下一个开始日期
                    currentDateFrom = currentDateTo.AddDays(1);
                }
            }
            else
            {
                //澳新(查询价库只会返回3天.需要异步通知)
                var thirdSkuSchedule = await _openSupplierService.SupplierSkuSchedule(
                    new SupplierSkuScheduleRequest
                    {
                        OutProductId = input.ActivityId,
                        OutProductOptionId = input.PackageId,
                        OutSkuId = input.SkuId,
                        SupplierType = supplierTypeStr,
                        DateFrom = input.ScheduleStartDate,
                        DateTo = input.ScheduleEndDate
                    }, tenantId);
                if (thirdSkuSchedule.Code == errorCode)
                {
                    //供应商接口返回错误码500(服务器错误)抛异常,其他业务异常继续执行
                    throw new BusinessException(ErrorTypes.Scenic.QueryThirdPriceInventoryError);
                }

                schedules = thirdSkuSchedule.Data?.Stocks ?? new List<SupplierProductScheduleList>();
            }
        }

        if (thirdSkuDetail != null)
        {
            if (string.IsNullOrEmpty(thirdSkuDetail.Currency))
            {
                thirdSkuDetail.Currency = schedules.FirstOrDefault()?.Currency;
            }

            thirdSkuDetail.TimeSlot = thirdSkuDetail.TimeSlot.Distinct().ToList();

            if (input.OpenSupplierType == OpenSupplierType.GlobalTix)
            {
                //目前globaltix没有日历价.需要手动将底价赋值到日历数据
                thirdSkuDetail.IsTimeSlot = thirdSkuDetail.TimeSlot.Any();
                foreach (var item in schedules)
                {
                    item.Price = thirdSkuDetail.Price;
                    item.Currency = thirdSkuDetail.Currency;
                }
            }
            else if (input.OpenSupplierType == OpenSupplierType.Klook)
            {
                if (thirdSkuDetail.IsTimeSlot)
                {
                    thirdSkuDetail.TimeSlot = schedules
                        .GroupBy(x => x.Time.TimeOfDay)
                        .Select(x => x.Key.ToString())
                        .ToList();
                }
            }
        }

        return (commissionRate,thirdSkuDetail, schedules);
    }

    private async Task<(OpenSupplierType? openSupplierType, string openSupplierTypeStr)> GetTicketOpenSupplierType(long supplierId)
    {
        var openSupplierTypeStr = string.Empty;
        OpenSupplierType? openSupplierType = null;
        var supplierInfo = await _baseService.GetSupplierInfo(supplierId);
        if (supplierInfo.SupplierApiSetting != null)
        {
            //转换saas供应商类型
            var supplierApiType = supplierInfo.SupplierApiSetting.SupplierApiType!.Value;
            var convertSupplierType = _openPlatformBaseService.ConvertSupplierType(supplierApiType);
            openSupplierType = convertSupplierType.openSupplierType;
            openSupplierTypeStr = convertSupplierType.openSupplierTypeStr;
        }

        return (openSupplierType, openSupplierTypeStr);
    }

    /// <summary>
    /// 特殊日历价库处理
    /// <value>isTimeSlot != true 并且 timeSlot 也无值时，非时段产品，忽略价库里的时段</value>
    /// </summary>
    /// <returns></returns>
    private List<SyncThirdInventorySchedulesDto> SpecialScheduleProcess(List<SyncThirdInventorySchedulesDto> schedules)
    {
        if (schedules.Any() is false) return schedules;

        //判断日期是否存在带时间点的数据,不包括'00:00:00'
        var hasTimeSlot = schedules.Any(x => x.Date.TimeOfDay != TimeSpan.Zero);
        if (!hasTimeSlot) return schedules;

        foreach (var item in schedules)
        {
            //更改日期格式为'00:00:00'
            item.Date = item.Date.Date;
        }

        return schedules;
    }
    
    #endregion
}