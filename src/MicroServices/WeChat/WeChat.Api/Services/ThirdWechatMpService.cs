using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common.Tenant.DTOs.Tenant;
using Contracts.Common.User.DTOs.UserBinding;
using Contracts.Common.User.Messages;
using Contracts.Common.WeChat.DTOs;
using Contracts.Common.WeChat.DTOs.WechatAppletVersion;
using Contracts.Common.WeChat.Enums;
using DotNetCore.CAP;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using SKIT.FlurlHttpClient.Wechat.Api;
using SKIT.FlurlHttpClient.Wechat.Api.Events;
using SKIT.FlurlHttpClient.Wechat.Api.Models;
using WeChat.Api.ConfigModel;
using WeChat.Api.Extensions;
using WeChat.Api.Services.Interfaces;

namespace WeChat.Api.Services
{
    public class ThirdWechatMpService : IThirdWechatMpService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILogger<ThirdWechatMpService> _logger;
        private readonly IAccessTokenService _accessTokenService;
        private readonly IThirdWechatAppletService _thirdWechatAppletService;
        private readonly IWechatMpService _wechatMpService;
        private readonly IWechatManagedService _wechatManagedService;
        private readonly ICapPublisher _capPublisher;
        private readonly CustomDbContext _dbContext;
        private readonly IOptions<WechatOptions> _wechatOptions;
        private readonly IOptionsMonitor<WechatMpReplySettings> _wechatMpReplySettings;
        private readonly ServicesAddress _servicesAddress;
        private readonly IWechatAppletPrivacyAuditService _wechatAppletPrivacyAuditService;
        private readonly IWechatAppletVersionService _wechatAppletVersionService;

        public ThirdWechatMpService(
            CustomDbContext dbContext,
            IOptions<WechatOptions> wechatOptions,
            IOptions<ServicesAddress> servicesAddress,
            IOptionsMonitor<WechatMpReplySettings> wechatMpReplySettings,
            IHttpContextAccessor httpContextAccessor,
            IHttpClientFactory httpClientFactory,
            ILogger<ThirdWechatMpService> logger,
            IAccessTokenService accessTokenService,
            IThirdWechatAppletService thirdWechatAppletService,
            IWechatMpService wechatMpService,
            IWechatManagedService wechatManagedService,
            ICapPublisher capPublisher,
            IWechatAppletPrivacyAuditService wechatAppletPrivacyAuditService,
            IWechatAppletVersionService wechatAppletVersionService
            )
        {
            _dbContext = dbContext;
            _wechatOptions = wechatOptions;
            _wechatMpReplySettings = wechatMpReplySettings;
            _servicesAddress = servicesAddress.Value;
            _httpContextAccessor = httpContextAccessor;
            _httpClientFactory = httpClientFactory;
            _logger = logger;
            _accessTokenService = accessTokenService;
            _thirdWechatAppletService = thirdWechatAppletService;
            _wechatMpService = wechatMpService;
            _wechatManagedService = wechatManagedService;
            _capPublisher = capPublisher;
            _wechatAppletPrivacyAuditService = wechatAppletPrivacyAuditService;
            _wechatAppletVersionService = wechatAppletVersionService;
        }

        public async Task<GetAccessTokenOutput> GetAccessToken(string code)
        {
            var wechatOauth2Access = await _wechatMpService.GetAccessToken(code);
            return new GetAccessTokenOutput()
            {
                AccessToken = wechatOauth2Access.AccessToken,
                OpenId = wechatOauth2Access.OpenId,
                ExpiresIn = wechatOauth2Access.ExpiresIn,
                RefreshToken = wechatOauth2Access.RefreshToken,
                Scope = wechatOauth2Access.Scope,
            };
        }

        public async Task<GetUserInfoOutput> GetUserInfo(string code)
        {
            var wechatOauth2Access = await _wechatMpService.GetAccessToken(code);
            var result = await _wechatMpService.GetUserInfo(wechatOauth2Access.OpenId);
            return result;
        }

        public async Task<GetJsSdkPackageOutput> GetJsSdkPackage(string pageurl, long tenantId)
        {
            var accesssTokenInfo = await _wechatManagedService.GetAccessTokenInfo(AuthType.WechatMp, tenantId);
            var accessToken = accesssTokenInfo.AccessToken;

            var client = new WechatApiClient(accesssTokenInfo.AppId, accesssTokenInfo.Secret);
            var request = new CgibinTicketGetTicketRequest() { AccessToken = accessToken };
            var response = await client.ExecuteCgibinTicketGetTicketAsync(request);
            _logger.LogInformation("获取公众号Ticket信息入参：{@parameter} ,结果：{@result}", request, response);
            if (!response.IsSuccessful())
                throw new BusinessException(response.ErrorMessage);
            var paramMap = client.GenerateParametersForJSSDKConfig(response.Ticket, pageurl);
            return new GetJsSdkPackageOutput()
            {
                appId = paramMap["appId"],
                nonceStr = paramMap["nonceStr"],
                signature = paramMap["signature"],
                timestamp = paramMap["timestamp"]
            };
        }

        public async Task<bool> VerifyMessage(string appId, string timestamp, string nonce, string signature)
        {
            var config = await _dbContext.WechatConfigurations.AsNoTracking()
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync(x => x.AuthType == AuthType.WechatMp
                && x.AppId == appId);
            if (config == null)
                return false;

            var client = new WechatApiClient(new WechatApiClientOptions
            {
                AppId = config.AppId,
                AppSecret = config.Secret
            });
            bool valid = client.VerifyEventSignatureForEcho(callbackTimestamp: timestamp, callbackNonce: nonce, callbackSignature: signature);
            return valid;
        }

        public async Task<string> ReceiveMessage(string appId, string content)
        {
            var config = await _dbContext.WechatConfigurations.AsNoTracking()
                .IgnoreQueryFilters()
                .Where(x => x.AppId == appId)
                .FirstOrDefaultAsync();

            var client = new WechatApiClient(new WechatApiClientOptions
            {
                AppId = _wechatOptions.Value.Account.AppId,
                AppSecret = _wechatOptions.Value.Account.AppSecret,
                PushEncodingAESKey = _wechatOptions.Value.MsgKey,
                PushToken = _wechatOptions.Value.MsgToken
            });
            var clientDes = client.DeserializeEventFromXml(content);
            _logger.LogInformation("微信公众号推送入参：{parameter} ,结果：{@result}", content, clientDes);

            switch (clientDes.MessageType.ToLower())
            {
                case "event":
                    {
                        switch (clientDes.Event)
                        {
                            case "subscribe"://关注
                                {
                                    if (config.AuthType is AuthType.WechatMp or AuthType.WechatApplet)
                                    {
                                        await UpdateFollowStatusAsync(clientDes.FromUserName, config.TenantId, FollowStatus.Follow);
                                    }
                                    var eventModel = client.DeserializeEventFromXml<ScanPushEvent>(content);
                                    var reply = await ScanPushEventMessageReply(client, eventModel, config.TenantId);
                                    if (!string.IsNullOrWhiteSpace(reply))
                                        return reply;
                                }
                                break;
                            case "unsubscribe"://取消关注
                                {
                                    await UpdateFollowStatusAsync(clientDes.FromUserName, config.TenantId, FollowStatus.CancelFollow);
                                }
                                break;

                            case "weapp_audit_success":
                                {
                                    var eventModel = client.DeserializeEventFromXml<WeappAuditSuccessEvent>(content);
                                    await _wechatAppletVersionService.UpdateWechatAppletAudit(new UpdateWechatAppletAuditInput
                                    {
                                        TenantId = config.TenantId,
                                        AuditStatus = WechatAppletAuditStatus.Success,//成功
                                    });
                                }
                                break;
                            case "weapp_audit_fail":
                                {
                                    var eventModel = client.DeserializeEventFromXml<WeappAuditFailEvent>(content);
                                    await _wechatAppletVersionService.UpdateWechatAppletAudit(new UpdateWechatAppletAuditInput
                                    {
                                        TenantId = config.TenantId,
                                        AuditStatus = WechatAppletAuditStatus.Failure,//失败
                                        Reason = eventModel.FailReason,
                                        ScreenshotMediaIds = eventModel.ScreenshotMediaIds
                                    });
                                }
                                break;
                            case "weapp_audit_delay":
                                {
                                    var eventModel = client.DeserializeEventFromXml<WeappAuditDelayEvent>(content);
                                    await _wechatAppletVersionService.UpdateWechatAppletAudit(new UpdateWechatAppletAuditInput
                                    {
                                        TenantId = config.TenantId,
                                        AuditStatus = WechatAppletAuditStatus.Delay,//延后
                                        Reason = eventModel.RejectReason
                                    });
                                }
                                break;
                            case "wxa_privacy_apply"://地理位置接口审核结果
                                {
                                    var evenModel = client.DeserializeEventFromXml<WxaPrivacyApplyEvent>(content);
                                    //更新审核结果
                                    await _wechatAppletPrivacyAuditService.UpdateLocationSettingResult(appId,
                                        config.TenantId, evenModel.Result.AuditId, evenModel.Result.RejectReason,
                                        evenModel.Result.Status);
                                    //更新checkpoint
                                    await _wechatAppletPrivacyAuditService.UpdateCheckPoint(appId, config.TenantId);
                                }
                                break;
                            case "SCAN": //用户已关注时的事件推送
                                {
                                    var scanModel = client.DeserializeEventFromXml<ScanPushEvent>(content);
                                    var scanReply = await ScanPushEventMessageReply(client, scanModel, config.TenantId);
                                    if (!string.IsNullOrWhiteSpace(scanReply))
                                        return scanReply;
                                }
                                break;
                        }
                    }
                    break;
                case "text":

                    break;
                default:

                    break;
            }
            return "success";
        }

        private async Task<bool> UpdateFollowStatusAsync(string openId, long tenantId, FollowStatus followStatus)
        {
            var wechatMpFollowStatus = await _dbContext.WechatMpFollowStatuses
                 .IgnoreQueryFilters()
                 .Where(x => x.TenantId == tenantId && x.OpenId == openId)
                 .FirstOrDefaultAsync();
            if (wechatMpFollowStatus is not null)
            {
                if (wechatMpFollowStatus.CustomerUserId == 0)
                    wechatMpFollowStatus.CustomerUserId = await GetCustomerUserIdByOpenId(openId, tenantId);
                wechatMpFollowStatus.FollowStatus = followStatus;
                wechatMpFollowStatus.UpdateTime = DateTime.Now;
            }
            else
            {
                //根据openId请求获取用户id
                long customerUserId = await GetCustomerUserIdByOpenId(openId, tenantId);
                wechatMpFollowStatus = new()
                {
                    CustomerUserId = customerUserId,
                    OpenId = openId,
                    FollowStatus = followStatus,
                };
                await _dbContext.WechatMpFollowStatuses.AddAsync(wechatMpFollowStatus);
            }
            return await _dbContext.SetTenantId(tenantId).SaveChangesAsync() > 0;
        }

        private async Task<long> GetCustomerUserIdByOpenId(string openId, long tenantId)
        {
            var url = _servicesAddress.User_GetUserIdByOpenId();
            using var httpContent = new StringContent(JsonConvert.SerializeObject(new
            {
                UserBindPlatformType = 1,//1-公众号
                OpenId = openId,
                TenantId = tenantId
            }), encoding: Encoding.UTF8, "application/json-patch+json");
            long customerUserId = await _httpClientFactory.InternalPostAsync<long>(url, httpContent: httpContent);
            return customerUserId;
        }

        private async Task<string> ScanPushEventMessageReply(WechatApiClient client,
            ScanPushEvent scanPushEvent,
            long tenantId)
        {

            _logger.LogInformation("微信公众号推送入参：ScanPushEventMessageReply：{@scanModel}", scanPushEvent);

            var eventKey = (scanPushEvent.EventKey ?? string.Empty).Replace("qrscene_", "");
            //解析场景参数 Type=1&SharedId=18&CustomerId=18
            var qrSence = GetWechatMpQrSence(eventKey);
            var reply = string.Empty;
            var senceReply = _wechatMpReplySettings.CurrentValue
                           .Replies
                           .FirstOrDefault(x => x.SenceType == qrSence.Type);
            //if (string.IsNullOrWhiteSpace(senceReply?.Content))
            //    return reply;
            switch (qrSence.Type)
            {
                case WechatSenceType.Default:
                    {
                        var tenant = await GetTenant(tenantId);
                        var content = senceReply?.Content?.Replace("{subdomain}", tenant.Subdomain);
                        TextMessageReply textMessageReply = new()
                        {
                            FromUserName = scanPushEvent.ToUserName,
                            ToUserName = scanPushEvent.FromUserName,
                            Content = content,
                            CreateTimestamp = scanPushEvent.CreateTimestamp
                        };
                        reply = client.SerializeEventToXml(textMessageReply, true);
                    }
                    break;
                case WechatSenceType.InstallDaren:
                    {
                        var tenant = await GetTenant(tenantId);
                        var content = senceReply?.Content
                            ?.Replace("{shortName}", tenant.ShortName)
                            ?.Replace("{subdomain}", tenant.Subdomain);
                        TextMessageReply textMessageReply = new()
                        {
                            FromUserName = scanPushEvent.ToUserName,
                            ToUserName = scanPushEvent.FromUserName,
                            Content = content,
                            CreateTimestamp = scanPushEvent.CreateTimestamp
                        };
                        reply = client.SerializeEventToXml(textMessageReply, true);
                    }
                    break;
                case WechatSenceType.MpDarenBind:
                    {
                        var tenant = await GetTenant(tenantId);
                        var content = senceReply?.Content?.Replace("{subdomain}", tenant.Subdomain);
                        TextMessageReply textMessageReply = new()
                        {
                            FromUserName = scanPushEvent.ToUserName,
                            ToUserName = scanPushEvent.FromUserName,
                            Content = content,
                            CreateTimestamp = scanPushEvent.CreateTimestamp
                        };
                        reply = client.SerializeEventToXml(textMessageReply, true);
                        //绑定下级事件
                        if (qrSence.SharedId > 0)
                        {
                            await _capPublisher.PublishAsync(CapTopics.User.BindUnderling,
                                new BindUnderlingMessage
                                {
                                    BindRule = Contracts.Common.User.Enums.DarenBindRule.FollowWechat,
                                    OpenId = scanPushEvent.FromUserName,
                                    SharerId = qrSence.SharedId,
                                    TenantId = tenantId
                                });
                        }
                    }
                    break;
                case WechatSenceType.AppletDarenBind:
                    {
                        var url = await GenerateURLLink(tenantId);
                        var content = senceReply?.Content?.Replace("{indexLink}", url);
                        TextMessageReply textMessageReply = new()
                        {
                            FromUserName = scanPushEvent.ToUserName,
                            ToUserName = scanPushEvent.FromUserName,
                            Content = content,
                            CreateTimestamp = scanPushEvent.CreateTimestamp
                        };
                        reply = client.SerializeEventToXml(textMessageReply, true);
                        //绑定下级事件
                        if (qrSence.SharedId > 0)
                        {
                            await _capPublisher.PublishAsync(CapTopics.User.BindUnderling,
                                new BindUnderlingMessage
                                {
                                    BindRule = Contracts.Common.User.Enums.DarenBindRule.FollowWechat,
                                    OpenId = scanPushEvent.FromUserName,
                                    SharerId = qrSence.SharedId,
                                    TenantId = tenantId
                                });
                        }
                    }
                    break;
                case WechatSenceType.AgencyUserSubscribe:
                    {
                        await AddOrUpdateUserBinding(new AddOrUpdateBindingInput
                        {
                            Code = scanPushEvent.FromUserName,
                            UserId = qrSence.AgencyUserId,
                            SysRole = Contracts.Common.User.Enums.UserBindSysRoleType.Agency,
                            PlatformType = Contracts.Common.User.Enums.UserBindPlatformType.B2BWechat,
                            TenantId = tenantId,
                            Enabled = true,
                        });
                    }
                    break;
            }
            return reply;
        }

        private static WechatMpQrSence GetWechatMpQrSence(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
                return new WechatMpQrSence { Type = WechatSenceType.Default };

            var arr = key.Split('&');
            JObject obj = new();
            foreach (var item in arr)
            {
                if (!item.Contains('=')) continue;
                var a = item.Split('=');
                obj.Add(a[0], a[1]);
            }
            WechatMpQrSence wechatMpQrSence = obj.ToObject<WechatMpQrSence>(
                new JsonSerializer
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });
            return wechatMpQrSence;
        }

        private async Task<string> GenerateURLLink(long tenantId)
        {
            var url = await _thirdWechatAppletService
                .GenerateURLLink(new GenerateURLLinkInput
                {
                    IsExpire = true,
                    ExpireInterval = 30
                }, tenantId);
            return url;
        }

        private async Task<GetTenantOutput> GetTenant(long tenantId)
        {
            var result = await _httpClientFactory.InternalGetAsync<GetTenantOutput>(
                _servicesAddress.Tenant_GetTenant(tenantId));
            return result;
        }

        /// <summary>
        /// 新增/更新 用户绑定信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task AddOrUpdateUserBinding(AddOrUpdateBindingInput input)
        {
            var url = _servicesAddress.User_AddOrUpdateUserBinding();
            using var httpContent = new StringContent(JsonConvert.SerializeObject(input), encoding: Encoding.UTF8, "application/json-patch+json");
            await _httpClientFactory.InternalPostAsync(url, httpContent: httpContent);
        }

    }
}
